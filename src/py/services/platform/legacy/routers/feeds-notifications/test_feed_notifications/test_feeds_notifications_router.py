import botocore.exceptions
import httpx
import pytest
from aries_se_api_client.base import ParsedResponse
from aries_utils.lifecycle_handler import LifecycleHandler
from feeds_notifications.feeds_notifications_router import FeedsNotificationRouterService
from unittest.mock import ANY, DEFAULT, MagicMock, PropertyMock, patch


def generate_stack_api_fake_response(stack_level_pause: bool, tenant_level_pause: bool):
    return ParsedResponse(
        content=dict(
            paused=stack_level_pause,
            tenants=[] if tenant_level_pause else [dict(paused=tenant_level_pause, name="foo")],
        ),
        raw_response=httpx.Response(status_code=200),
    )


@patch.multiple(
    "feeds_notifications.feeds_notifications_router",
    SERVICE_CONFIG=DEFAULT,
    StackAPI=DEFAULT,
    AriesApiClient=DEFAULT,
    get_sqs_client=DEFAULT,
    get_s3_client=DEFAULT,
    get_lambda_client=DEFAULT,
)
@patch.object(LifecycleHandler, "running", new_callable=PropertyMock)
class TestFeedsNotificationsRouter:
    """Test cases to test the Feeds Notifications Router service End to End."""

    @patch("time.sleep")
    def test_service_run_with_stack_level_pause_enabled(
        self,
        time_mock: MagicMock,
        lifecyle_mock: PropertyMock,
        **kwargs,
    ) -> None:
        # Stack level pause is enabled, service should sleep for 900 seconds
        # before it requests for the queues again.
        kwargs["SERVICE_CONFIG"].stack = "prod-stack"
        kwargs["SERVICE_CONFIG"].queues_refresh_interval_s = "900"
        kwargs["StackAPI"].return_value.get_by_name.return_value = generate_stack_api_fake_response(
            stack_level_pause=True, tenant_level_pause=True
        )
        time_mock.return_value = None
        lifecyle_mock.side_effect = [True, False]

        FeedsNotificationRouterService().run()

        time_mock.assert_called_once_with(900)
        kwargs["StackAPI"].return_value.get_by_name.assert_called_once_with(
            stack_name="prod-stack", tenant_paused=False
        )

    @patch("time.sleep")
    def test_service_run_with_no_sqs_queue_found(
        self,
        time_mock: MagicMock,
        lifecyle_mock: PropertyMock,
        **kwargs,
    ) -> None:
        # No queue found with the name `foo` and since it's only one name
        # to search the queues with, the service should sleep
        kwargs["SERVICE_CONFIG"].env = "prod"
        kwargs["SERVICE_CONFIG"].stack = "prod-stack"
        kwargs["StackAPI"].return_value.get_by_name.return_value = generate_stack_api_fake_response(
            stack_level_pause=False, tenant_level_pause=True
        )
        time_mock.return_value = None
        kwargs["get_sqs_client"].return_value.get_queue_url.side_effect = [
            botocore.exceptions.ClientError(
                error_response={"Error": {"Code": "AWS.SimpleQueueService.NonExistentQueue"}},
                operation_name="",
            ),
        ]
        lifecyle_mock.side_effect = [True, True, False]

        FeedsNotificationRouterService().run()

        time_mock.assert_called_once()
        kwargs["StackAPI"].return_value.get_by_name.assert_called_once_with(
            stack_name="prod-stack", tenant_paused=False
        )
        kwargs["get_sqs_client"].return_value.get_queue_url.assert_called_once_with(
            QueueName="prod-stack-feeds"
        )

    @patch("time.sleep")
    def test_service_run_with_no_message_in_queue(
        self,
        time_mock: MagicMock,
        lifecyle_mock: PropertyMock,
        **kwargs,
    ) -> None:
        # No messages in the Queue and since it's only having one name, should sleep
        # and then search for the queues again after that
        kwargs["SERVICE_CONFIG"].env = "prod"
        kwargs["SERVICE_CONFIG"].stack = "prod-stack"
        kwargs["StackAPI"].return_value.get_by_name.return_value = generate_stack_api_fake_response(
            stack_level_pause=False, tenant_level_pause=True
        )
        kwargs["get_sqs_client"].return_value.get_queue_url.return_value = dict(
            QueueUrl="https://prod-stack-feeds"
        )
        kwargs["get_sqs_client"].return_value.receive_message.return_value = dict(Messages=None)
        lifecyle_mock.side_effect = [True, True, False]

        FeedsNotificationRouterService().run()

        time_mock.assert_called_once()
        kwargs["StackAPI"].return_value.get_by_name.assert_called_once_with(
            stack_name="prod-stack", tenant_paused=False
        )
        kwargs["get_sqs_client"].return_value.get_queue_url.assert_called_once_with(
            QueueName="prod-stack-feeds"
        )
        kwargs["get_sqs_client"].return_value.receive_message.assert_called_once_with(
            QueueUrl="https://prod-stack-feeds",
            WaitTimeSeconds=5,
            MaxNumberOfMessages=10,
            AttributeNames=["All"],
        )

    def test_service_run_with_test_event_message(
        self, lifecyle_mock: PropertyMock, **kwargs
    ) -> None:
        kwargs["SERVICE_CONFIG"].env = "srp"
        kwargs["SERVICE_CONFIG"].stack = "srp-stack"
        kwargs["get_sqs_client"].return_value.get_queue_url.return_value = dict(
            QueueUrl="https://srp-stack-feeds"
        )
        kwargs["get_sqs_client"].return_value.receive_message.return_value = dict(
            Messages=[{"Body": '{"Event": "s3:TestEvent"}', "ReceiptHandle": "Test"}]
        )
        lifecyle_mock.side_effect = [True, True, False, False]

        FeedsNotificationRouterService().run()

        kwargs["get_sqs_client"].return_value.get_queue_url.assert_called_once_with(
            QueueName="srp-stack-feeds"
        )
        kwargs["get_sqs_client"].return_value.receive_message.assert_called_once_with(
            QueueUrl="https://srp-stack-feeds",
            WaitTimeSeconds=5,
            MaxNumberOfMessages=10,
            AttributeNames=["All"],
        )
        # Test event messages are deleted
        kwargs["get_sqs_client"].return_value.delete_message.assert_called_once_with(
            QueueUrl="https://srp-stack-feeds", ReceiptHandle="Test"
        )

    def test_service_run_with_message_having_no_bucket_key(
        self, lifecyle_mock: PropertyMock, **kwargs
    ) -> None:
        kwargs["SERVICE_CONFIG"].env = "prod"
        kwargs["SERVICE_CONFIG"].stack = "prod-stack"
        kwargs["StackAPI"].return_value.get_by_name.return_value = generate_stack_api_fake_response(
            stack_level_pause=False, tenant_level_pause=True
        )
        kwargs["get_sqs_client"].return_value.get_queue_url.return_value = dict(
            QueueUrl="https://prod-stack-feeds"
        )
        kwargs["get_sqs_client"].return_value.receive_message.return_value = dict(
            Messages=[
                {
                    "Body": '{"Records": null, "realm": null, "keyActions": []}',
                    "ReceiptHandle": "Test",
                }
            ]
        )
        lifecyle_mock.side_effect = [True, True, False, False]

        FeedsNotificationRouterService().run()

        kwargs["StackAPI"].return_value.get_by_name.assert_called_once_with(
            stack_name="prod-stack", tenant_paused=False
        )
        kwargs["get_sqs_client"].return_value.get_queue_url.assert_called_once_with(
            QueueName="prod-stack-feeds"
        )
        kwargs["get_sqs_client"].return_value.receive_message.assert_called_once_with(
            QueueUrl="https://prod-stack-feeds",
            WaitTimeSeconds=5,
            MaxNumberOfMessages=10,
            AttributeNames=["All"],
        )
        # Message has no bucket/key so delete...
        kwargs["get_sqs_client"].return_value.delete_message.assert_called_once_with(
            QueueUrl="https://prod-stack-feeds", ReceiptHandle="Test"
        )

    def test_service_run_with_vendor_event_message(
        self, lifecyle_mock: PropertyMock, **kwargs
    ) -> None:
        kwargs["SERVICE_CONFIG"].env = "uat"
        kwargs["SERVICE_CONFIG"].stack = "uat-stack"
        kwargs["StackAPI"].return_value.get_by_name.return_value = generate_stack_api_fake_response(
            stack_level_pause=False, tenant_level_pause=True
        )
        kwargs["get_sqs_client"].return_value.get_queue_url.return_value = dict(
            QueueUrl="https://uat-stack-feeds"
        )
        kwargs["get_sqs_client"].return_value.receive_message.return_value = dict(
            Messages=[
                {
                    "Body": '{"Records": [{"s3": {"bucket": {"name": "client.verba"} ,"object": {"key": "feeds/comms/verba-voice/test/testing"}}}]}',  # noqa: E501
                    "ReceiptHandle": "Test",
                }
            ]
        )
        lifecyle_mock.side_effect = [True, True, False, False]

        FeedsNotificationRouterService().run()

        kwargs["StackAPI"].return_value.get_by_name.assert_called_once_with(
            stack_name="uat-stack", tenant_paused=False
        )
        kwargs["get_sqs_client"].return_value.get_queue_url.assert_called_once_with(
            QueueName="uat-stack-feeds"
        )
        kwargs["get_sqs_client"].return_value.receive_message.assert_called_once_with(
            QueueUrl="https://uat-stack-feeds",
            WaitTimeSeconds=5,
            MaxNumberOfMessages=10,
            AttributeNames=["All"],
        )

        # When it's vendor file the file is copied over to s3 bucket and the message is deleted
        kwargs["get_sqs_client"].return_value.delete_message.assert_called_once_with(
            QueueUrl="https://uat-stack-feeds", ReceiptHandle="Test"
        )
        kwargs["get_s3_client"].return_value.copy_object.assert_any_call(
            Bucket="client",
            CopySource={"Bucket": "client.verba", "Key": "feeds/comms/verba-voice/test/testing"},
            Key="feeds/comms/verba-voice/test/testing",
        )

    def test_service_run_with_ecs_task_event_message(
        self, lifecyle_mock: PropertyMock, **kwargs
    ) -> None:
        kwargs["SERVICE_CONFIG"].env = "sit"
        kwargs["SERVICE_CONFIG"].stack = "sit-stack"
        kwargs["SERVICE_CONFIG"].task.name = "test"
        kwargs["SERVICE_CONFIG"].task_cluster = "test-dataserver"
        kwargs["SERVICE_CONFIG"].task_queue_url = "http://test-stack-tasks.fifo"
        kwargs["StackAPI"].return_value.get_by_name.return_value = generate_stack_api_fake_response(
            stack_level_pause=False, tenant_level_pause=True
        )
        kwargs["get_sqs_client"].return_value.get_queue_url.return_value = dict(
            QueueUrl="https://sit-stack-feeds"
        )
        kwargs["get_sqs_client"].return_value.receive_message.return_value = dict(
            Messages=[
                {
                    "Body": '{"Records": [{"s3": {"bucket": {"name": "client.steeleye.co"} ,"object": {"key": "feeds/task/bloomberg-shredder/test.xml"}}}]}',  # noqa: E501
                    "ReceiptHandle": "Test",
                    "MessageId": "Test",
                    "Attributes": {"SentTimestamp": 1692558183},
                }
            ]
        )
        lifecyle_mock.side_effect = [True, True, False, False]

        FeedsNotificationRouterService().run()

        kwargs["StackAPI"].return_value.get_by_name.assert_called_once_with(
            stack_name="sit-stack", tenant_paused=False
        )
        kwargs["get_sqs_client"].return_value.get_queue_url.assert_called_once_with(
            QueueName="sit-stack-feeds"
        )
        kwargs["get_sqs_client"].return_value.receive_message.assert_called_once_with(
            QueueUrl="https://sit-stack-feeds",
            WaitTimeSeconds=5,
            MaxNumberOfMessages=10,
            AttributeNames=["All"],
        )
        # Task is bloomberg, the message visibility increase must be called
        kwargs["get_sqs_client"].return_value.change_message_visibility.assert_called_once_with(
            QueueUrl="https://sit-stack-feeds", ReceiptHandle="Test", VisibilityTimeout=39600
        )
        # We don't invoke the task directly instead we send the message in tasks queue
        # and then tasks router invokes it, so assert on message sent.
        kwargs["get_sqs_client"].return_value.send_message.assert_called_once_with(
            QueueUrl="http://test-stack-tasks.fifo",
            MessageBody=ANY,  # payload is big to compare
            MessageGroupId="client.steeleye.co",
        )

    def test_service_run_with_lambda_event_message(
        self, lifecyle_mock: PropertyMock, **kwargs
    ) -> None:
        kwargs["SERVICE_CONFIG"].env = "dev"
        kwargs["SERVICE_CONFIG"].stack = "dev-stack"
        kwargs["SERVICE_CONFIG"].task.name = "feeds-notifications-router"
        kwargs["StackAPI"].return_value.get_by_name.return_value = generate_stack_api_fake_response(
            stack_level_pause=False, tenant_level_pause=True
        )
        kwargs["get_sqs_client"].return_value.get_queue_url.return_value = dict(
            QueueUrl="https://dev-stack-feeds"
        )
        kwargs["get_sqs_client"].return_value.receive_message.return_value = dict(
            Messages=[
                {
                    "Body": '{"Records": [{"s3": {"bucket": {"name": "client.steeleye.co"} ,"object": {"key": "feeds/comms/bloomberg-shredder/test.xml"}}}]}',  # noqa: E501
                    "ReceiptHandle": "Test",
                    "MessageId": "Test",
                    "Attributes": {"SentTimestamp": 1692555877},
                }
            ]
        )
        lifecyle_mock.side_effect = [True, True, False, False]
        kwargs["get_lambda_client"].return_value.invoke.return_value = {"StatusCode": 200}

        FeedsNotificationRouterService().run()

        kwargs["StackAPI"].return_value.get_by_name.assert_called_once_with(
            stack_name="dev-stack", tenant_paused=False
        )
        kwargs["get_sqs_client"].return_value.get_queue_url.assert_called_once_with(
            QueueName="dev-stack-feeds"
        )
        kwargs["get_sqs_client"].return_value.receive_message.assert_called_once_with(
            QueueUrl="https://dev-stack-feeds",
            WaitTimeSeconds=5,
            MaxNumberOfMessages=10,
            AttributeNames=["All"],
        )
        kwargs["get_lambda_client"].return_value.invoke.assert_called_once_with(
            FunctionName="dev-stack-feeds-bloomberg-shredder-handler",
            InvocationType="Event",
            Payload='{"source": "feeds-notifications-router", "bucket_name": "client.steeleye.co", "key_name": "feeds/comms/bloomberg-shredder/test.xml", "receipt_handle": "Test", "queued_at": "1970-01-20 14:09:15.877000", "retry_msg": {"MSG_BODY": "{\\"Body\\": \\"{\\\\\\"Records\\\\\\": [{\\\\\\"s3\\\\\\": {\\\\\\"bucket\\\\\\": {\\\\\\"name\\\\\\": \\\\\\"client.steeleye.co\\\\\\"} ,\\\\\\"object\\\\\\": {\\\\\\"key\\\\\\": \\\\\\"feeds/comms/bloomberg-shredder/test.xml\\\\\\"}}}]}\\", \\"ReceiptHandle\\": \\"Test\\", \\"MessageId\\": \\"Test\\", \\"Attributes\\": {\\"SentTimestamp\\": 1692555877}}", "SQS_QUEUE_URL": "https://dev-stack-feeds"}, "source_queue_url": "https://dev-stack-feeds"}',  # noqa: E501
        )

    def test_service_run_with_queue_has_more_than_one_message(
        self, lifecyle_mock: PropertyMock, **kwargs
    ) -> None:
        kwargs["SERVICE_CONFIG"].env = "dev"
        kwargs["SERVICE_CONFIG"].stack = "dev-stack"
        kwargs["SERVICE_CONFIG"].task.name = "feeds-notifications-router"
        kwargs["StackAPI"].return_value.get_by_name.return_value = generate_stack_api_fake_response(
            stack_level_pause=False, tenant_level_pause=True
        )
        kwargs["get_sqs_client"].return_value.get_queue_url.return_value = dict(
            QueueUrl="https://dev-stack-feeds"
        )
        kwargs["get_sqs_client"].return_value.receive_message.return_value = dict(
            Messages=[
                {"Body": '{"Event": "s3:TestEvent"}', "ReceiptHandle": "Test"},
                {
                    "Body": '{"Records": [{"s3": {"bucket": {"name": "client.steeleye.co"} ,"object": {"key": "feeds/comms/bloomberg-shredder/test.xml"}}}]}',  # noqa: E501
                    # noqa: E501
                    "ReceiptHandle": "Test",
                    "MessageId": "Test",
                    "Attributes": {"SentTimestamp": 1692555877},
                },
            ]
        )
        lifecyle_mock.side_effect = [True, True, False, False]
        kwargs["get_lambda_client"].return_value.invoke.return_value = {"StatusCode": 200}

        FeedsNotificationRouterService().run()

        kwargs["StackAPI"].return_value.get_by_name.assert_called_once_with(
            stack_name="dev-stack", tenant_paused=False
        )
        kwargs["get_sqs_client"].return_value.get_queue_url.assert_called_once_with(
            QueueName="dev-stack-feeds"
        )
        kwargs["get_sqs_client"].return_value.receive_message.assert_called_once_with(
            QueueUrl="https://dev-stack-feeds",
            WaitTimeSeconds=5,
            MaxNumberOfMessages=10,
            AttributeNames=["All"],
        )
        kwargs["get_lambda_client"].return_value.invoke.assert_called_once_with(
            FunctionName="dev-stack-feeds-bloomberg-shredder-handler",
            InvocationType="Event",
            Payload='{"source": "feeds-notifications-router", "bucket_name": "client.steeleye.co", "key_name": "feeds/comms/bloomberg-shredder/test.xml", "receipt_handle": "Test", "queued_at": "1970-01-20 14:09:15.877000", "retry_msg": {"MSG_BODY": "{\\"Body\\": \\"{\\\\\\"Records\\\\\\": [{\\\\\\"s3\\\\\\": {\\\\\\"bucket\\\\\\": {\\\\\\"name\\\\\\": \\\\\\"client.steeleye.co\\\\\\"} ,\\\\\\"object\\\\\\": {\\\\\\"key\\\\\\": \\\\\\"feeds/comms/bloomberg-shredder/test.xml\\\\\\"}}}]}\\", \\"ReceiptHandle\\": \\"Test\\", \\"MessageId\\": \\"Test\\", \\"Attributes\\": {\\"SentTimestamp\\": 1692555877}}", "SQS_QUEUE_URL": "https://dev-stack-feeds"}, "source_queue_url": "https://dev-stack-feeds"}',  # noqa: E501
            # noqa: E501
        )
        kwargs["get_sqs_client"].return_value.delete_message.assert_called_once_with(
            QueueUrl="https://dev-stack-feeds", ReceiptHandle="Test"
        )

    def test_service_fatal_run_time_error(self, lifecyle_mock: PropertyMock, **kwargs):
        # No match case setup for the local-stack - fatal error for the service
        kwargs["SERVICE_CONFIG"].env = "local"
        kwargs["SERVICE_CONFIG"].stack = "local-stack"
        kwargs["StackAPI"].return_value.get_by_name.return_value = generate_stack_api_fake_response(
            stack_level_pause=False, tenant_level_pause=False
        )
        lifecyle_mock.side_effect = [True, False]

        with pytest.raises(RuntimeError, match="Unexecpected environement: local"):
            FeedsNotificationRouterService().run()

        kwargs["StackAPI"].return_value.get_by_name.assert_called_once_with(
            stack_name="local-stack", tenant_paused=False
        )
