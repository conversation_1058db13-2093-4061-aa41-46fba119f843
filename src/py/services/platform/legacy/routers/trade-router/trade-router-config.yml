data_platform_config_api_url: ${oc.env:DATA_PLATFORM_CONFIG_API_URL,https://aries-platform-config.dev-enterprise.steeleye.co/}
debug: ${oc.env:DEBUG,0}
queues_refresh_interval_s: ${oc.env:QUEUES_REFRESH_INTERVAL_S,900}
elastic:
  host: ${oc.env:ELASTIC_HOST,localhost}
  port: ${oc.env:ELASTIC_PORT,9200}
  scheme: ${oc.env:ELASTIC_SCHEME,http}
env: ${oc.env:ENV,dev}
slack_webhook_url: ${oc.env:SLACK_WEBHOOK_URL}
stack: ${oc.env:STACK,local}
task:
  name: ${oc.env:TASK_NAME,trade-router}
  version: ${oc.env:SE_VERSION,local}
task_cluster: ${oc.env:TASK_CLUSTER}
task_queue_url: ${oc.env:TASK_QUEUE_URL}
