# Aries Scheduler Service
## Background
The scheduler service is used to trigger conductor workflows on a schedule, based on `interval` and `cron` based schedules. It is implemented using the [APScheduler](https://apscheduler.readthedocs.io/en/stable/) library, using SQLAlchmey, Postgresql backend storage.

## Key points
- The scheduler services uses stack level DB called `aries_scheduler` which is specified using env var `ARIES_SCHEDULER_DB_URL`
- It periodically pings the aries-config API, which manages the source of truth for all the schedules. The sync cadence is controlled by env var `SCHEDULE_SYNC_INTERVAL_S`, defaulting to 300 seconds. Each sync receives a list of schedules with any updates since last sync, and updates the scheduler accordingly.
- The scheduler is restarted every `RESTART_INTERVAL_S` seconds, defaulting to 6hours. This is to ensure that scheduler service is always in sync with the source of truth, and any orphan jobs in scheduler are removed.
- If scheduler misses one or more job executions due to any reason, it will immediately send a single job execution to compensate for all missed executions in the past, and then continue normally as per the schedule.
- Scheduler sends events on OMA on following incidents
  - A new job was added to scheduler
  - Job updated
  - Job paused
  - Job resumed
  - Job deleted
  - Job submitted for execution
  - Submitted job successfully executed
  - Submitted job failed to executed
- Scheduler does not need the conductor workflow as a managed TenantWorkflow in config DB. It defaults to executing a workflow with name `workflow.name` in the schedule config received from config-api
- Scheduler handles `taskToDomain` of conductor workflow by reading the `io_param.params.aries_task_to_domain` value