stack: ${oc.env:STACK,dev-oma}
debug: ${oc.env:DEBUG,0}
task:
  name: aries-oma-elastic-events-synchronizer
  version: ${oc.env:SE_VERSION,local}
kafka:
  consumer_group_id: ${oc.env:CONSUMER_GROUP_ID,aries-oma-elastic-events-synchronizer-${stack}}
  consumer_auto_offset_reset: ${oc.env:CONSUMER_AUTO_OFFSET_RESET,earliest}
  bootstrap_servers: "${oc.env:KAFKA_BOOTSTRAP_SERVERS,localhost:9092}"
  rest_proxy_url: "${oc.env:KAFKA_REST_PROXY_URL,http://localhost:8082}"
  message_max_poll_timeout_s: ${oc.env:KAFKA_MESSAGE_POLL_TIMEOUT_S,5}
  consumer_max_poll_interval_ms: ${oc.env:KAFKA_CONSUMER_MAX_POLL_INTERVAL_MS,1200000} # 20 mins timeout per message consumption
  consumer_close_timeout_s: ${oc.env:KAFKA_CONSUMER_CLOSE_TIMEOUT_S,10}
topic: "${oc.env:TOPIC,aries.${stack}.oma.events}"
batch_size: ${oc.env:BATCH_SIZE,500}
oma_index_alias: ${oc.env:OMA_INDEX_ALIAS,dev_io_event_schema}
elastic_url: ${oc.env:ELASTIC_URL}
elastic_verify_certs: ${oc.env:ELASTIC_VERIFY_CERTS,1}
elastic_timeout_s: ${oc.env:ELASTIC_TIMEOUT_S,600} # 10 mins timeout per bulk request
sentry:
  enabled: ${oc.env:SENTRY_ENABLED,0}
  dsn: "${oc.env:SENTRY_DSN,https://<EMAIL>/4508334862761984}"
