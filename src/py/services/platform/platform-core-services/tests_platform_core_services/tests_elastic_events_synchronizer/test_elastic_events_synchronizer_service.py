import json
import mock
import pytest
import typing
from aries_io_event_schema.io_event.model import IOEventSchema
from aries_utils.lifecycle_handler import <PERSON>cycle<PERSON>and<PERSON>
from mock.mock import MagicMock, PropertyMock
from platform_core_services.elastic_events_synchronizer.elastic_events_synchronizer_service import (
    ElasticEventsSynchronizer,
)


@pytest.fixture
def mock_value_dict():
    return {
        "workflow": {
            "trace_id": "trace1",
            "name": "test-workflow",
            "stack": "test",
            "tenant": "test-tenant",
            "start_timestamp": "2021-01-01T00:00:00.000000+00:00",
        },
        "task": {
            "id": "task1",
            "name": "test-task",
            "success": True,
            "version": "0.0.1",
        },
        "io_param": {
            "params": {
                "file_uri": "s3://test.dev.steeleye.co/lake/ingress/bloomberg/test.json",
                "cloud_provider": "aws",
            }
        },
    }


def mock_kafka_msg(mock_value_dict: dict[str, typing.Any], kafka_return_error: bool = False):
    mock_value = MagicMock(
        return_value=bytes(
            json.dumps(mock_value_dict),
            "utf-8",
        )
    )
    return MagicMock(
        value=mock_value,
        error=MagicMock(return_value=kafka_return_error),
    )


@mock.patch.multiple(
    "platform_core_services.elastic_events_synchronizer.elastic_events_synchronizer_service",
    _config=mock.DEFAULT,
    Consumer=mock.DEFAULT,
    SlimRecordHandler=mock.DEFAULT,
    Elasticsearch=mock.DEFAULT,
)
@pytest.mark.skip(reason="Skipping this test as it is not working")
def test_success(mock_value_dict, **kwargs):
    kwargs["_config"].oma_index_alias = "aries_io_event"
    kwargs["Elasticsearch"]().info.return_value = MagicMock()
    events_synchronizer = ElasticEventsSynchronizer()
    mock_action: dict[str, typing.Any] = {
        "_op_type": "create",
        "_index": "aries_io_event",
        "_source": {
            "_meta": {
                "hash": mock.ANY,
                "id": mock.ANY,
                "key": mock.ANY,
                "model": "IOEventSchema",
                "user": "system",
                "version": 1,
                "timestamp": mock.ANY,
            },
        },
        "_id": mock.ANY,
    }
    mock_action["_source"].update(mock_value_dict)
    # mock_action['_source']['task']['timestamp'] = mock.ANY
    # mock_action['_source']['workflow']['start_timestamp'] = mock.ANY
    expected_actions = [mock_action]
    mock_msg = mock_kafka_msg(mock_value_dict)

    kwargs["Consumer"].return_value.consume.return_value = [mock_msg]
    # ES bulk helper returns no of success records count and errors if any
    kwargs["SlimRecordHandler"].return_value._get_sync_bulk_helper.return_value = MagicMock(
        return_value=(1, [])
    )
    # kwargs["SlimRecordHandler"].return_value.bulk_create_packets.return_value = mock_actions

    with mock.patch.object(LifecycleHandler, "running", new_callable=PropertyMock) as mock_handler:
        # Stopping the loop to exit after the first iteration
        mock_handler.side_effect = [True, False]
        events_synchronizer.run()

    kwargs["Consumer"].return_value.commit.assert_called_once_with(
        asynchronous=False, message=mock_msg
    )
    kwargs[
        "SlimRecordHandler"
    ].return_value._get_sync_bulk_helper.return_value.assert_called_once_with(
        mock.ANY,
        expected_actions,
        raise_on_exception=True,
        raise_on_error=False,
        stats_only=False,
        max_retries=3,
    )


@mock.patch.multiple(
    "platform_core_services.elastic_events_synchronizer.elastic_events_synchronizer_service",
    _config=mock.DEFAULT,
    Consumer=mock.DEFAULT,
    Elasticsearch=mock.DEFAULT,
)
@mock.patch("elasticsearch8.helpers.bulk")
def test_kafka_errors(bulk_patch, mock_value_dict, **kwargs):
    kwargs["_config"].elastic_url = "https://localhost:9200"
    kwargs["Elasticsearch"]().info.return_value = MagicMock()
    events_synchronizer = ElasticEventsSynchronizer()

    mock_msg = mock_kafka_msg(mock_value_dict, kafka_return_error=True)
    kwargs["Consumer"].return_value.consume.return_value = [mock_msg]
    # ES bulk helper returns no of success records count and errors if any
    bulk_patch.return_value = MagicMock(return_value=(1, []))

    with mock.patch.object(LifecycleHandler, "running", new_callable=PropertyMock) as mock_handler:
        # Stopping the loop to exit after the first iteration
        mock_handler.side_effect = [True, False]
        events_synchronizer.run()

    # no action should be done when kafka message has errors
    kwargs["Consumer"].return_value.commit.assert_not_called()
    bulk_patch.return_value.assert_not_called()


@mock.patch.multiple(
    "platform_core_services.elastic_events_synchronizer.elastic_events_synchronizer_service",
    _config=mock.DEFAULT,
    Consumer=mock.DEFAULT,
    Elasticsearch=mock.DEFAULT,
)
@mock.patch.object(ElasticEventsSynchronizer, "_bulk_call_with_retry", new_callable=PropertyMock)
def test_bulk_call_errors(bulk_patch, mock_value_dict, **kwargs):
    kwargs["_config"].elastic_url = "https://localhost:9200"
    kwargs["Elasticsearch"]().info.return_value = MagicMock()
    events_synchronizer = ElasticEventsSynchronizer()
    mock_msg = mock_kafka_msg(mock_value_dict)
    kwargs["Consumer"].return_value.consume.return_value = [mock_msg]
    kwargs["_config"].oma_index_alias = "aries_io_event"
    # ES bulk helper returns no of success records count and errors if any
    bulk_patch.side_effect = [MagicMock(return_value=(0, [{"create": {"_id": "1"}}]))]
    with mock.patch.object(IOEventSchema, "get_steeleye_meta_id") as mock_mets_id:
        mock_mets_id.return_value = "1"
        with mock.patch.object(
            LifecycleHandler, "running", new_callable=PropertyMock
        ) as mock_handler:
            # Stopping the loop to exit after the first iteration
            mock_handler.side_effect = [True, False]
            events_synchronizer.run()

    # Message should be committed even if ES bulk call fails
    kwargs["Consumer"].return_value.commit.assert_called_once_with(
        asynchronous=False, message=mock_msg
    )


@pytest.mark.parametrize(
    "reason",
    ["cluster_block_exception", "es_rejected_execution_exception", "circuit_breaking_exception"],
)
@mock.patch.multiple(
    "platform_core_services.elastic_events_synchronizer.elastic_events_synchronizer_service",
    _config=mock.DEFAULT,
    Consumer=mock.DEFAULT,
    Elasticsearch=mock.DEFAULT,
)
@mock.patch.object(ElasticEventsSynchronizer, "_bulk_call_with_retry", new_callable=PropertyMock)
def test_es_fatal(bulk_patch, mock_value_dict, reason, **kwargs):
    kwargs["_config"].elastic_url = "https://localhost:9200"
    kwargs["Elasticsearch"]().info.return_value = MagicMock()
    events_synchronizer = ElasticEventsSynchronizer()
    mock_msg = mock_kafka_msg(mock_value_dict)
    kwargs["Consumer"].return_value.consume.return_value = [mock_msg]
    kwargs["_config"].oma_index_alias = "aries_io_event"
    # ES bulk helper returns no of success records count and errors if any
    bulk_patch.side_effect = [
        MagicMock(
            return_value=(
                0,
                [
                    {
                        "create": {
                            "_id": "1",
                            "status": 429,
                            "error": {"type": reason},
                        }
                    }
                ],
            )
        )
    ]

    with mock.patch.object(IOEventSchema, "get_steeleye_meta_id") as mock_mets_id:
        mock_mets_id.return_value = "1"
        with mock.patch.object(
            LifecycleHandler, "running", new_callable=PropertyMock
        ) as mock_handler:
            # Stopping the loop to exit after the first iteration
            mock_handler.side_effect = [True, False]
            with pytest.raises(ValueError, match=reason):
                events_synchronizer.run()

    # Message is neiter consumed, nor DLQ
    kwargs["Consumer"].return_value.commit.assert_not_called()
