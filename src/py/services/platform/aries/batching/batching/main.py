import sentry_sdk
from batching.application import faust_app
from batching.config import configuration
from batching.topics import (
    stream_by_tenant_fix_bundle_topic,
    stream_by_tenant_workflow_topic,
    stream_topic,
)
from se_enums.cloud import CloudProviderEnum

DEBUG = bool(int(configuration.debug))
if bool(int(configuration.sentry.enabled)):
    sentry_sdk.init(
        dsn=configuration.sentry.dsn,
        release=configuration.task.version,
        environment=configuration.stack,
        debug=DEBUG,
    )
if DEBUG:  # pragma: no cover
    # in case of debug mode for local testing, manage the stream topic internally,
    # so its partitions are matched with ktable. This is a Faust requirement.
    # On the cloud, we need to ensure that ktable and stream topic are configured
    # with same no. of partitions
    stream_topic.internal = True
    stream_topic.partitions = configuration.app.stream_topic.partitions

    stream_by_tenant_workflow_topic.internal = True
    stream_by_tenant_workflow_topic.partitions = (
        configuration.app.tw_batching.stream_by_tenant_workflow_topic.partitions
    )

    stream_by_tenant_fix_bundle_topic.internal = True
    stream_by_tenant_fix_bundle_topic.partitions = (
        configuration.app.fix_batching.stream_by_tenant_fix_bundle_topic.partitions
    )

# The storage account name must be set for Azure cloud
if configuration.cloud == CloudProviderEnum.AZURE:
    if configuration.azure_storage_account_name is None:
        raise Exception("Cloud is Azure and Azure storage account name is not set")


def main() -> None:  # pragma: no cover
    # this import ensures that all agents are registered with faust
    from batching.agents import fix_agent, tw_agent, stream_agent  # noqa

    faust_app.main()


if __name__ == "__main__":  # pragma: no cover
    main()
