stack: ${oc.env:STACK,dev-shared-1}
app:
  consumer_group_id: ${oc.env:CONSUMER_GROUP_ID,aries-batching-${stack}}
  consumer_auto_offset_reset: ${oc.env:CONSUMER_AUTO_OFFSET_RESET,earliest}
  fix_batching:
    batch_size: ${oc.env:FIX_BATCHING_SIZE,100}
    batch_timeout_s: ${oc.env:FIX_BATCHING_TIMEOUT_S,300}
    ping_duration_s: 61
    stream_by_tenant_fix_bundle_topic:
      name: ${oc.env:STREAM_BY_TENANT_FIX_BUNDLE_TOPIC,aries.${stack}.stream.tenant-fix-bundle.events}
      partitions: 30
    table:
      name: ${oc.env:FIX_BATCHING_KTABLE,aries.${stack}.stream.tenant-fix-bundle.ktable}
      partitions: 30
      changelog_topic: ${oc.env:FIX_BATCHING_KTABLE_CHANGELOG_TOPIC,aries.${stack}.stream.tenant-fix-bundle.ktable.changelog}
  stream_topic:
    name: ${oc.env:STREAM_TOPIC,aries.${stack}.stream.events}
    partitions: 30
  tw_batching:
    default_batch_timeout_s: 300
    default_max_batch_size: 100
    ping_duration_s: 61
    stream_by_tenant_workflow_topic:
      name: ${oc.env:STREAM_BY_TENANT_WORKFLOW_TOPIC,aries.${stack}.stream.tenant-workflow.events}
      partitions: 30
    table:
      name: ${oc.env:TW_BATCHING_KTABLE,aries.${stack}.stream.tenant-workflow.ktable}
      partitions: 30
      changelog_topic: ${oc.env:TW_BATCHING_KTABLE_CHANGELOG_TOPIC,aries.${stack}.stream.tenant-workflow.ktable.changelog}

# This value is must when the cloud is Azure, the null value is handled inside the code
azure_storage_account_name: ${oc.env:AZURE_STORAGE_ACCOUNT_NAME,null}
cloud: ${oc.env:CLOUD,aws}
data_platform_config:
  api:
    url: ${oc.env:DATA_PLATFORM_CONFIG_API_URL,https://data-platform-config.dev-enterprise.steeleye.co}
debug: ${oc.env:DEBUG,0}
local_testing: ${oc.env:LOCAL_TESTING,0}
kafka:
  bootstrap_servers: ${oc.env:KAFKA_BOOTSTRAP_SERVERS,kafka://localhost:9092}
oma:
  aries_topic: ${oc.env:OMA_ARIES_TOPIC,aries.oma.events}
  rest_proxy_url: ${oc.env:OMA_REST_PROXY_URL,http://localhost:8082}
task:
  name: aries-batching
  version: ${oc.env:SE_VERSION,local}
sentry:
  enabled: ${oc.env:SENTRY_ENABLED,0}
  dsn: ${oc.env:SENTRY_DSN,https://<EMAIL>/****************}
