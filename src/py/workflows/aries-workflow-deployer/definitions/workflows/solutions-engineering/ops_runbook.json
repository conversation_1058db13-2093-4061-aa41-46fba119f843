{"accessPolicy": {}, "failureWorkflow": "aries_workflow_failure_oma_event", "inputParameters": [], "inputTemplate": {"workflow": {"name": "ops_runbook"}}, "name": "ops_runbook", "outputParameters": {}, "ownerEmail": "<EMAIL>", "restartable": true, "schemaVersion": 2, "tasks": [{"asyncComplete": false, "inputParameters": {"io_param": "${workflow.input.io_param}", "workflow": "${workflow.input.workflow}"}, "name": "ops_runbook", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "ops_runbook", "type": "SIMPLE"}], "timeoutPolicy": "TIME_OUT_WF", "timeoutSeconds": 432000, "variables": {}, "workflowStatusListenerEnabled": false}