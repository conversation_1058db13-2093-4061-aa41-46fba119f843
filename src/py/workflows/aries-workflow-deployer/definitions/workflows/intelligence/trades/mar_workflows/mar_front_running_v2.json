{"failureWorkflow": "surveillance_workflow_failure", "name": "mar_front_running_v2", "ownerEmail": "<EMAIL>", "schemaVersion": 2, "tasks": [{"asyncComplete": false, "decisionCases": {}, "defaultCase": [], "defaultExclusiveJoinTask": [], "forkTasks": [], "inputParameters": {"io_param": "${workflow.input.io_param}", "workflow": "${workflow.input.workflow}"}, "joinOn": [], "loopOver": [], "name": "mar_create_group", "onStateChange": {}, "optional": false, "permissive": false, "retryCount": 0, "startDelay": 0, "taskReferenceName": "mar_front_running_v2_create_group", "type": "SIMPLE"}, {"asyncComplete": false, "decisionCases": {}, "defaultCase": [], "defaultExclusiveJoinTask": [], "forkTasks": [], "inputParameters": {"batch_size": 1000, "evaluatorType": "javascript", "expression": "(function () {\n  var dynamicTasksInput = {};\n  var dynamicTasks = [];\n  var totalGroups = $.total_groups;\n  var group_file_uri = $.group_file_uri;\n  var watch_metadata_file_uri = $.watch_metadata_file_uri;\n  var numBatches = Math.ceil(totalGroups / $.batch_size);\n\n\n  for (var index = 0; index < numBatches; index++) {\n  var startIndex = index * $.batch_size;\n    var endIndex = Math.min(startIndex + $.batch_size - 1, totalGroups - 1);\n    var sub_workflow_ref = $.sub_workflow_name + \"_\" +\"_\"+ index;\n    var subworkflow_param = {\n      subWorkflowParam: {\n        name: $.sub_workflow_name,\n      },\n      type: \"SUB_WORKFLOW\",\n      taskReferenceName: sub_workflow_ref,\n    };\n    dynamicTasks.push(subworkflow_param);\n    dynamicTasksInput[sub_workflow_ref] = {\n    \"io_param\": {\n    \"params\": {\n    \"group_file_uri\": group_file_uri,\n    \"watch_metadata_file_uri\": watch_metadata_file_uri,\n    \"start_index\": startIndex,\n    \"end_index\": endIndex,\n    }\n    }\n    }\n    dynamicTasksInput[sub_workflow_ref][\"workflow\"] = $.workflow_input;\n  }\n  var output = {};\n  output[\"dynamicTasks\"] = Java.to(dynamicTasks, \"java.util.Map[]\");\n  output[\"dynamicTasksInput\"] = dynamicTasksInput;\n  return output;\n})();", "group_file_uri": "${mar_front_running_v2_create_group.output.io_param.params.group_file_uri}", "sub_workflow_name": "mar_batch_apply_strategy_subworkflow", "total_groups": "${mar_front_running_v2_create_group.output.io_param.params.total_groups}", "watch_metadata_file_uri": "${mar_front_running_v2_create_group.output.io_param.params.watch_metadata_file_uri}", "workflow_input": "${workflow.input.workflow}"}, "joinOn": [], "loopOver": [], "name": "initiate_batch_apply_strategy_sub_workflow", "onStateChange": {}, "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "initiate_batch_apply_strategy_sub_workflow", "type": "INLINE"}, {"asyncComplete": false, "decisionCases": {}, "defaultCase": [], "defaultExclusiveJoinTask": [], "dynamicForkTasksInputParamName": "dynamicTasksInput", "dynamicForkTasksParam": "dynamicTasks", "forkTasks": [], "inputParameters": {"dynamicTasks": "${initiate_batch_apply_strategy_sub_workflow.output.result.dynamicTasks}", "dynamicTasksInput": "${initiate_batch_apply_strategy_sub_workflow.output.result.dynamicTasksInput}"}, "joinOn": [], "loopOver": [], "name": "mar_batch_apply_strategy_dfj", "onStateChange": {}, "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "mar_batch_apply_strategy_dfj", "type": "FORK_JOIN_DYNAMIC"}, {"asyncComplete": false, "decisionCases": {}, "defaultCase": [], "defaultExclusiveJoinTask": [], "forkTasks": [], "inputParameters": {}, "joinOn": [], "loopOver": [], "name": "wait_for_alert_ingestion", "onStateChange": {}, "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "wait_for_alert_ingestion", "type": "JOIN"}, {"asyncComplete": false, "decisionCases": {}, "defaultCase": [], "defaultExclusiveJoinTask": [], "forkTasks": [], "inputParameters": {"alert_ingestion_outputs": "${wait_for_alert_ingestion.output}", "evaluatorType": "javascript", "expression": "(function() {\n  // Initialize the accumulator to store the sum\n  var sum = 0;\n  \n  // Get the keys of the dynamic join output\n  var results = $.alert_ingestion_outputs;\n  \n  // Iterate over each key in the dynamic join task output\n  for (var taskName in results) {\n    // Check if the key is actually part of the object\n    if (results[taskName] && results[taskName].total_scenarios !== undefined) {\n      // Add the \"result\" value of the current task to the sum\n      sum += results[taskName].total_scenarios;\n    }\n  }\n  \n  // Return the final sum\n  return sum;\n})()"}, "joinOn": [], "loopOver": [], "name": "count_total_scenarios", "onStateChange": {}, "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "count_total_scenarios", "type": "INLINE"}, {"asyncComplete": false, "decisionCases": {}, "defaultCase": [], "defaultExclusiveJoinTask": [], "forkTasks": [], "inputParameters": {"io_param": {"params": {"exclude_doc_id": "${mar_front_running_v2_create_group.output.io_param.params.exclude_doc_id}", "exclude_doc_parent_id": "${mar_front_running_v2_create_group.output.io_param.params.exclude_doc_parent_id}", "merge_output": {"expected_count": "${count_total_scenarios.output.result}"}, "upper_bound_date": "${mar_front_running_v2_create_group.output.io_param.params.upper_bound_date}", "watch_execution_id": "${mar_front_running_v2_create_group.output.io_param.params.watch_execution_id}", "watch_id": "${workflow.input.io_param.params.watch_id}"}}, "task": "${mar_front_running_v2_create_group.output.task}", "workflow": "${workflow.input.workflow}"}, "joinOn": [], "loopOver": [], "name": "surveillance_watch_finalize", "onStateChange": {}, "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "surveillance_watch_finalize", "type": "SIMPLE"}, {"asyncComplete": false, "caseExpression": null, "caseValueParam": null, "description": null, "dynamicForkJoinTasksParam": null, "dynamicForkTasksInputParamName": null, "dynamicForkTasksParam": null, "dynamicTaskNameParam": null, "evaluatorType": null, "expression": null, "inputParameters": {"io_param": {"params": {"total_scenarios": "${count_total_scenarios.output.result}", "upper_bound": "${mar_front_running_v2_create_group.output.io_param.params.upper_bound_date}", "watch_metadata_file_uri": "${mar_front_running_v2_create_group.output.io_param.params.watch_metadata_file_uri}"}}, "task": "${mar_front_running_v2_create_group.output.task}", "workflow": "${workflow.input.workflow}"}, "loopCondition": null, "name": "mar_auditor", "optional": true, "rateLimited": null, "retryCount": null, "scriptExpression": null, "sink": null, "startDelay": 0, "subWorkflowParam": null, "taskDefinition": null, "taskReferenceName": "mar_auditor", "type": "SIMPLE"}], "timeoutPolicy": "ALERT_ONLY", "timeoutSeconds": 432000, "variables": {}}