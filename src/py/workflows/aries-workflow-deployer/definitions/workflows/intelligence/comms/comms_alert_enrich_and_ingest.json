{"failureWorkflow": "surveillance_workflow_failure", "name": "comms_alert_enrich_and_ingest", "ownerEmail": "<EMAIL>", "schemaVersion": 2, "tasks": [{"asyncComplete": false, "inputParameters": {"io_param": "${workflow.input.input_param}", "workflow": "${workflow.input.workflow}"}, "name": "comms_watch_enrich", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "comms_watch_enrich", "type": "SIMPLE"}, {"asyncComplete": false, "inputParameters": {"io_param": "${comms_watch_enrich.output.io_param}", "task": "${comms_watch_enrich.output.task}", "workflow": "${workflow.input.workflow}"}, "name": "apply_meta", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "apply_meta", "type": "SIMPLE"}, {"asyncComplete": false, "inputParameters": {}, "name": "wait_es__apply_meta", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "wait_es__apply_meta", "type": "WAIT"}, {"asyncComplete": false, "inputParameters": {"io_param": {"params": {"merge_output": {"elastic_connector": {"io_param": "${wait_es__apply_meta.output.io_param}", "task": {"name": "elastic_connector"}}}, "upper_bound_date": "${workflow.input.input_param.params.upper_bound_date}", "watch_execution_id": "${workflow.input.input_param.params.watch_execution_id}", "watch_id": "${workflow.input.input_param.params.watch_id}"}}, "task": "${comms_watch_enrich.output.task}", "workflow": "${comms_watch_enrich.input.workflow}"}, "name": "surveillance_watch_finalize", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "surveillance_watch_finalize", "type": "SIMPLE"}], "timeoutPolicy": "ALERT_ONLY", "timeoutSeconds": 432000, "variables": {}}