{"name": "aries_workflow_failure_sink_file_audit", "ownerEmail": "<EMAIL>", "restartable": true, "schemaVersion": 2, "tasks": [{"asyncComplete": false, "inputParameters": {"http_request": {"connectionTimeOut": 60000, "method": "GET", "readTimeOut": 60000, "uri": "http://conductor-server:8080/api/workflow/${workflow.input.workflowId}"}}, "name": "get_workflow_details", "optional": false, "permissive": false, "retryCount": 3, "startDelay": 0, "taskReferenceName": "get_workflow_details", "type": "HTTP"}, {"inputParameters": {"io_param": {"params": {"failed_workflow_trace_id": "${get_workflow_details.output.response.body.input.workflow.trace_id}", "reason_for_incompletion": "${workflow.input.reason}"}}, "workflow": "${get_workflow_details.output.response.body.input.workflow}"}, "name": "sink_file_audit_failure", "taskReferenceName": "sink_file_audit_failure", "type": "SIMPLE"}, {"asyncComplete": false, "inputParameters": {}, "name": "wait_es__sink_file_audit_failure", "optional": false, "startDelay": 0, "taskReferenceName": "wait_es__sink_file_audit_failure", "type": "WAIT"}], "timeoutPolicy": "ALERT_ONLY", "timeoutSeconds": 432000, "workflowStatusListenerEnabled": false}