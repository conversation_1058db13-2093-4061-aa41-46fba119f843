{"accessPolicy": {}, "failureWorkflow": "aries_auditable_workflow_failure", "inputParameters": [], "inputTemplate": {"workflow": {"name": "steeleye_universal_voice"}}, "name": "steeleye_universal_voice", "outputParameters": {}, "ownerEmail": "<EMAIL>", "restartable": true, "schemaVersion": 2, "tasks": [{"asyncComplete": false, "inputParameters": {"io_param": "${workflow.input.io_param}", "workflow": "${workflow.input.workflow}"}, "name": "sink_file_audit_init", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "sink_file_audit_init", "type": "SIMPLE"}, {"asyncComplete": false, "inputParameters": {}, "name": "wait_es__sink_file_audit_init", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "wait_es__sink_file_audit_init", "type": "WAIT"}, {"asyncComplete": false, "inputParameters": {"io_param": "${workflow.input.io_param}", "workflow": "${workflow.input.workflow}"}, "name": "analytics_controller", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "analytics_controller", "type": "SIMPLE"}, {"asyncComplete": false, "inputParameters": {"base": {"params": {"dynamic_tasks": {"calls_to_transcribe": {"input_parameters": "${analytics_controller.output.io_param.params}", "name": "comms_voice_sub_workflow", "task_reference_name": "comms_voice_sub_workflow_calls_to_be_transcribed", "type": "SUB_WORKFLOW"}, "calls_with_transcript": {"input_parameters": "${analytics_controller.output.io_param.params}", "name": "comms_voice_with_transcript_subworkflow", "task_reference_name": "elastic_ingestion_calls_with_transcript", "type": "SUB_WORKFLOW"}, "calls_without_transcript": {"name": "elastic_ingestion", "task_reference_name": "elastic_ingestion_calls_without_transcript", "type": "SUB_WORKFLOW"}, "waveform": {"name": "waveform_transform", "task_params": {"optional": true}, "task_reference_name": "waveform_transform_ref", "type": "SIMPLE"}}}}, "io_param": "${workflow.input.io_param}", "queryExpression": "{ io_param: (.io_param * .base) }"}, "name": "io_param_params_concatenator", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "io_param_params_concatenator", "type": "JSON_JQ_TRANSFORM"}, {"asyncComplete": false, "inputParameters": {"io_param": "${io_param_params_concatenator.output.result.io_param}", "task": "${wait_es__sink_file_audit_init.output.task}", "workflow": "${workflow.input.workflow}"}, "name": "steeleye_universal_voice_tf", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "steeleye_universal_voice_tf", "type": "SIMPLE"}, {"asyncComplete": false, "forkTasks": [[{"asyncComplete": false, "dynamicForkTasksInputParamName": "dynamicTasksInput", "dynamicForkTasksParam": "dynamicTasks", "inputParameters": {"dynamicTasks": "${steeleye_universal_voice_tf.output.io_param.params.calls_to_transcribe.dynamicTasks}", "dynamicTasksInput": "${steeleye_universal_voice_tf.output.io_param.params.calls_to_transcribe.dynamicTaskInputs}"}, "name": "comms_voice_sub_workflow", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "comms_voice_sub_workflow_calls_to_be_transcribed", "type": "FORK_JOIN_DYNAMIC"}, {"asyncComplete": false, "inputParameters": {}, "name": "wait_for_comms_voice_sub_workflow", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "wait_for_comms_voice_sub_workflow_ref", "type": "JOIN"}], [{"asyncComplete": false, "dynamicForkTasksInputParamName": "dynamicTasksInput", "dynamicForkTasksParam": "dynamicTasks", "inputParameters": {"dynamicTasks": "${steeleye_universal_voice_tf.output.io_param.params.calls_with_transcript.dynamicTasks}", "dynamicTasksInput": "${steeleye_universal_voice_tf.output.io_param.params.calls_with_transcript.dynamicTaskInputs}"}, "name": "comms_voice_with_transcript_subworkflow", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "elastic_ingestion_calls_with_transcript", "type": "FORK_JOIN_DYNAMIC"}, {"asyncComplete": false, "inputParameters": {}, "name": "wait_for_elastic_ingestion_calls_with_transcript", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "wait_for_elastic_ingestion_calls_with_transcript_ref", "type": "JOIN"}], [{"asyncComplete": false, "dynamicForkTasksInputParamName": "dynamicTasksInput", "dynamicForkTasksParam": "dynamicTasks", "inputParameters": {"dynamicTasks": "${steeleye_universal_voice_tf.output.io_param.params.calls_without_transcript.dynamicTasks}", "dynamicTasksInput": "${steeleye_universal_voice_tf.output.io_param.params.calls_without_transcript.dynamicTaskInputs}"}, "name": "elastic_ingestion", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "elastic_ingestion_calls_without_transcript", "type": "FORK_JOIN_DYNAMIC"}, {"asyncComplete": false, "inputParameters": {}, "name": "wait_for_elastic_ingestion_calls_without_transcript", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "wait_for_elastic_ingestion_calls_without_transcript_ref", "type": "JOIN"}], [{"asyncComplete": false, "dynamicForkTasksInputParamName": "dynamicTasksInput", "dynamicForkTasksParam": "dynamicTasks", "inputParameters": {"dynamicTasks": "${steeleye_universal_voice_tf.output.io_param.params.Waveform.dynamicTasks}", "dynamicTasksInput": "${steeleye_universal_voice_tf.output.io_param.params.Waveform.dynamicTaskInputs}"}, "name": "waveform_transform", "optional": true, "permissive": false, "startDelay": 0, "taskReferenceName": "waveform_transform_ref", "type": "FORK_JOIN_DYNAMIC"}, {"asyncComplete": false, "inputParameters": {}, "name": "wait_for_ingestion_waveform", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "wait_for_ingestion_waveform_ref", "type": "JOIN"}]], "inputParameters": {}, "name": "fork", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "fork_ref", "type": "FORK_JOIN"}, {"asyncComplete": false, "inputParameters": {}, "joinOn": ["wait_for_comms_voice_sub_workflow_ref", "wait_for_elastic_ingestion_calls_with_transcript_ref", "wait_for_elastic_ingestion_calls_without_transcript_ref", "wait_for_ingestion_waveform_ref"], "name": "join", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "join_ref", "type": "JOIN"}, {"asyncComplete": false, "inputParameters": {"io_param": "${sink_file_audit_init.output.io_param}", "task": "${sink_file_audit_init.output.task}", "workflow": "${workflow.input.workflow}"}, "name": "sink_file_audit_finalize", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "sink_file_audit_finalize", "type": "SIMPLE"}, {"asyncComplete": false, "inputParameters": {}, "name": "wait_es__sink_file_audit_finalize", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "wait_es__sink_file_audit_finalize", "type": "WAIT"}], "timeoutPolicy": "TIME_OUT_WF", "timeoutSeconds": 432000, "variables": {}, "workflowStatusListenerEnabled": false}