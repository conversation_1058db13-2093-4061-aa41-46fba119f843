{"accessPolicy": {}, "inputParameters": [], "inputTemplate": {"workflow": {"name": "iv_transcription_sub_workflow"}}, "name": "iv_transcription_sub_workflow", "outputParameters": {}, "ownerEmail": "<EMAIL>", "restartable": true, "schemaVersion": 2, "tasks": [{"asyncComplete": false, "inputParameters": {"io_param": "${workflow.input.io_param}", "task": "${workflow.input.task}", "workflow": "${workflow.input.workflow}"}, "name": "aries_iv_transform_transcription", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "aries_iv_transform_transcription", "type": "SIMPLE"}, {"asyncComplete": false, "decisionCases": {"iv-analytics": [{"asyncComplete": false, "forkTasks": [[{"asyncComplete": false, "inputParameters": {"io_param": "${aries_iv_transform_transcription.output.io_param}", "task": "${aries_iv_transform_transcription.output.task}", "workflow": "${workflow.input.workflow}"}, "name": "language_detection", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "language_detection", "type": "SIMPLE"}], [{"asyncComplete": false, "inputParameters": {"io_param": "${aries_iv_transform_transcription.output.io_param}", "task": "${aries_iv_transform_transcription.output.task}", "workflow": "${workflow.input.workflow}"}, "name": "aries_transcription_copilot", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "aries_transcription_copilot_analytics", "type": "SIMPLE"}], [{"asyncComplete": false, "inputParameters": {"io_param": "${aries_iv_transform_transcription.output.io_param.params.Call}", "task": "${aries_iv_transform_transcription.output.task}", "workflow": "${workflow.input.workflow}"}, "name": "lexica_processor", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "lexica_processor", "type": "SIMPLE"}]], "inputParameters": {}, "name": "comms_analytics_fork", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "comms_analytics_fork", "type": "FORK_JOIN"}, {"asyncComplete": false, "inputParameters": {}, "joinOn": ["aries_transcription_copilot_analytics", "language_detection", "lexica_processor"], "name": "comms_analytics_join", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "comms_analytics_join", "type": "JOIN"}, {"asyncComplete": false, "inputParameters": {"io_param": {"params": {"aries_transcription_copilot": "${comms_analytics_join.output.aries_transcription_copilot_analytics.io_param.params.Call.params}", "language_detection": "${comms_analytics_join.output.language_detection.io_param.params}", "lexica_processor": "${comms_analytics_join.output.lexica_processor.io_param.params}"}}, "workflow": "${workflow.input.workflow}"}, "name": "merge_comms_analytics", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "merge_comms_analytics", "type": "SIMPLE"}, {"asyncComplete": false, "inputParameters": {"io_param": "${comms_analytics_join.output.aries_transcription_copilot_analytics.io_param.params.Transcript}", "task": "${merge_comms_analytics.output.task}", "workflow": "${workflow.input.workflow}"}, "name": "apply_meta", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "apply_meta_transcript_with_analytics", "type": "SIMPLE"}, {"asyncComplete": false, "inputParameters": {}, "name": "wait_es__apply_meta_transcript_with_analytics", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "wait_es__apply_meta_transcript_with_analytics", "type": "WAIT"}, {"asyncComplete": false, "inputParameters": {"io_param": "${merge_comms_analytics.output.io_param}", "task": "${wait_es__apply_meta_transcript_with_analytics.output.task}", "workflow": "${workflow.input.workflow}"}, "name": "apply_meta", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "apply_meta_call_update_with_analytics", "type": "SIMPLE"}, {"asyncComplete": false, "inputParameters": {}, "name": "wait_es__apply_meta_call_update_with_analytics", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "wait_es__apply_meta_call_update_with_analytics", "type": "WAIT"}], "iv-no-analytics": [{"asyncComplete": false, "inputParameters": {"io_param": "${aries_iv_transform_transcription.output.io_param}", "task": "${aries_iv_transform_transcription.output.task}", "workflow": "${workflow.input.workflow}"}, "name": "aries_transcription_copilot", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "aries_transcription_copilot", "type": "SIMPLE"}, {"asyncComplete": false, "inputParameters": {"io_param": "${aries_transcription_copilot.output.io_param.params.Transcript}", "task": "${aries_transcription_copilot.output.task}", "workflow": "${workflow.input.workflow}"}, "name": "apply_meta", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "apply_meta_transcript_without_analytics", "type": "SIMPLE"}, {"asyncComplete": false, "inputParameters": {}, "name": "wait_es__apply_meta_transcript_without_analytics", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "wait_es__apply_meta_transcript_without_analytics", "type": "WAIT"}, {"asyncComplete": false, "inputParameters": {"io_param": "${aries_transcription_copilot.output.io_param.params.Call}", "task": "${wait_es__apply_meta_transcript_without_analytics.output.task}", "workflow": "${workflow.input.workflow}"}, "name": "apply_meta", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "apply_meta_call_update_without_analytics", "type": "SIMPLE"}, {"asyncComplete": false, "inputParameters": {}, "name": "wait_es__apply_meta_call_update_without_analytics", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "wait_es__apply_meta_call_update_without_analytics", "type": "WAIT"}]}, "description": "Decide whether to run analytics based on the analytics_controller", "evaluatorType": "javascript", "expression": "(function () { if ($.is_lexica_enabled === true) { return 'iv-analytics'; } else { return 'iv-no-analytics'; } })();", "inputParameters": {"is_lexica_enabled": "${workflow.input.io_param.params.is_lexica_enabled}"}, "name": "switch_iv_analytics", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "switch_iv_analytics", "type": "SWITCH"}], "timeoutPolicy": "TIME_OUT_WF", "timeoutSeconds": 432000, "variables": {}, "workflowStatusListenerEnabled": false}