{"inputParameters": [], "inputTemplate": {}, "name": "asc_chat_transform", "outputParameters": {}, "ownerEmail": "<EMAIL>", "restartable": true, "schemaVersion": 2, "tasks": [{"asyncComplete": false, "caseExpression": null, "caseValueParam": null, "description": null, "dynamicForkJoinTasksParam": null, "dynamicForkTasksInputParamName": null, "dynamicForkTasksParam": null, "dynamicTaskNameParam": null, "evaluatorType": null, "expression": null, "inputParameters": {"io_param": "${workflow.input.io_param}", "task": "${workflow.input.task}", "workflow": "${workflow.input.workflow}"}, "loopCondition": null, "name": "asc_chat_transform", "optional": false, "rateLimited": null, "retryCount": null, "scriptExpression": null, "sink": null, "startDelay": 0, "subWorkflowParam": null, "taskDefinition": null, "taskReferenceName": "asc_chat_transform", "type": "SIMPLE"}, {"asyncComplete": false, "caseExpression": null, "caseValueParam": null, "description": null, "dynamicForkJoinTasksParam": null, "dynamicForkTasksInputParamName": null, "dynamicForkTasksParam": null, "dynamicTaskNameParam": null, "evaluatorType": null, "expression": null, "forkTasks": [[{"asyncComplete": false, "caseExpression": null, "caseValueParam": null, "decisionCases": {"analytics": [{"asyncComplete": false, "caseExpression": null, "caseValueParam": null, "description": null, "dynamicForkJoinTasksParam": null, "dynamicForkTasksInputParamName": null, "dynamicForkTasksParam": null, "dynamicTaskNameParam": null, "evaluatorType": null, "expression": null, "inputParameters": {"io_param": "${asc_chat_transform.output.io_param.params.Message}", "task": "${asc_chat_transform.output.task}", "workflow": "${workflow.input.workflow}"}, "loopCondition": null, "name": "lexica_processor", "optional": false, "rateLimited": null, "retryCount": null, "scriptExpression": null, "sink": null, "startDelay": 0, "subWorkflowParam": null, "taskDefinition": null, "taskReferenceName": "lexica_processor_message", "type": "SIMPLE"}, {"asyncComplete": false, "caseExpression": null, "caseValueParam": null, "description": null, "dynamicForkJoinTasksParam": null, "dynamicForkTasksInputParamName": null, "dynamicForkTasksParam": null, "dynamicTaskNameParam": null, "evaluatorType": null, "expression": null, "inputParameters": {"io_param": "${lexica_processor_message.output.io_param}", "task": "${lexica_processor_message.output.task}", "workflow": "${workflow.input.workflow}"}, "loopCondition": null, "name": "apply_meta", "optional": false, "rateLimited": null, "retryCount": null, "scriptExpression": null, "sink": null, "startDelay": 0, "subWorkflowParam": null, "taskDefinition": null, "taskReferenceName": "apply_meta_message_with_lexica", "type": "SIMPLE"}, {"asyncComplete": false, "inputParameters": {}, "name": "wait_es__apply_meta_message_with_lexica", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "wait_es__apply_meta_message_with_lexica", "type": "WAIT"}], "non-analytics": [{"asyncComplete": false, "caseExpression": null, "caseValueParam": null, "description": null, "dynamicForkJoinTasksParam": null, "dynamicForkTasksInputParamName": null, "dynamicForkTasksParam": null, "dynamicTaskNameParam": null, "evaluatorType": null, "expression": null, "inputParameters": {"io_param": "${asc_chat_transform.output.io_param.params.Message}", "task": "${asc_chat_transform.output.task}", "workflow": "${workflow.input.workflow}"}, "loopCondition": null, "name": "apply_meta", "optional": false, "rateLimited": null, "retryCount": null, "scriptExpression": null, "sink": null, "startDelay": 0, "subWorkflowParam": null, "taskDefinition": null, "taskReferenceName": "apply_meta_message_non_lexica", "type": "SIMPLE"}, {"asyncComplete": false, "inputParameters": {}, "name": "wait_es__apply_meta_message_non_lexica", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "wait_es__apply_meta_message_non_lexica", "type": "WAIT"}]}, "defaultCase": [{"asyncComplete": false, "caseExpression": null, "caseValueParam": null, "description": null, "dynamicForkJoinTasksParam": null, "dynamicForkTasksInputParamName": null, "dynamicForkTasksParam": null, "dynamicTaskNameParam": null, "evaluatorType": null, "expression": null, "inputParameters": {"duration": "0 seconds"}, "loopCondition": null, "name": "skip_empty", "optional": false, "rateLimited": null, "retryCount": null, "scriptExpression": null, "sink": null, "startDelay": 0, "subWorkflowParam": null, "taskDefinition": null, "taskReferenceName": "skip_empty_message", "type": "WAIT"}], "description": "Decide whether to apply Comms Analytics", "dynamicForkJoinTasksParam": null, "dynamicForkTasksInputParamName": null, "dynamicForkTasksParam": null, "dynamicTaskNameParam": null, "evaluatorType": "javascript", "expression": "(function () {\n  if ($.file_uri != null) {\n    return $.is_lexica_enabled ? \"analytics\" : \"non-analytics\";\n  } else {\n    return \"empty_file_uri\";\n  }\n})();", "inputParameters": {"file_uri": "${asc_chat_transform.output.io_param.params.Message.params.file_uri}", "is_lexica_enabled": "${workflow.input.is_lexica_enabled}"}, "loopCondition": null, "name": "switch_analytics", "optional": false, "rateLimited": null, "retryCount": null, "scriptExpression": null, "sink": null, "startDelay": 0, "subWorkflowParam": null, "taskDefinition": null, "taskReferenceName": "switch_analytics", "type": "SWITCH"}, {"asyncComplete": false, "caseExpression": null, "caseValueParam": null, "description": null, "dynamicForkJoinTasksParam": null, "dynamicForkTasksInputParamName": null, "dynamicForkTasksParam": null, "dynamicTaskNameParam": null, "evaluatorType": null, "expression": null, "inputParameters": {"duration": "0 seconds"}, "loopCondition": null, "name": "lazy_merge", "optional": false, "rateLimited": null, "retryCount": null, "scriptExpression": null, "sink": null, "startDelay": 0, "subWorkflowParam": null, "taskDefinition": null, "taskReferenceName": "lazy_merge_message", "type": "WAIT"}], [{"asyncComplete": false, "caseExpression": null, "caseValueParam": null, "decisionCases": {"chat_event": [{"asyncComplete": false, "caseExpression": null, "caseValueParam": null, "description": null, "dynamicForkJoinTasksParam": null, "dynamicForkTasksInputParamName": null, "dynamicForkTasksParam": null, "dynamicTaskNameParam": null, "evaluatorType": null, "expression": null, "inputParameters": {"io_param": "${asc_chat_transform.output.io_param.params.ChatEvent}", "task": "${asc_chat_transform.output.task}", "workflow": "${workflow.input.workflow}"}, "loopCondition": null, "name": "apply_meta", "optional": false, "rateLimited": null, "retryCount": null, "scriptExpression": null, "sink": null, "startDelay": 0, "subWorkflowParam": null, "taskDefinition": null, "taskReferenceName": "apply_meta_chat_event", "type": "SIMPLE"}, {"asyncComplete": false, "inputParameters": {}, "name": "wait_es__apply_meta_chat_event", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "wait_es__apply_meta_chat_event", "type": "WAIT"}]}, "defaultCase": [{"asyncComplete": false, "caseExpression": null, "caseValueParam": null, "description": null, "dynamicForkJoinTasksParam": null, "dynamicForkTasksInputParamName": null, "dynamicForkTasksParam": null, "dynamicTaskNameParam": null, "evaluatorType": null, "expression": null, "inputParameters": {"duration": "0 seconds"}, "loopCondition": null, "name": "skip_empty", "optional": false, "rateLimited": null, "retryCount": null, "scriptExpression": null, "sink": null, "startDelay": 0, "subWorkflowParam": null, "taskDefinition": null, "taskReferenceName": "skip_empty_chat_event", "type": "WAIT"}], "description": null, "dynamicForkJoinTasksParam": null, "dynamicForkTasksInputParamName": null, "dynamicForkTasksParam": null, "dynamicTaskNameParam": null, "evaluatorType": "javascript", "expression": "$.file_uri != null ? 'chat_event' : 'noop_chatevent'", "inputParameters": {"file_uri": "${asc_chat_transform.output.io_param.params.ChatEvent.params.file_uri}"}, "loopCondition": null, "name": "process_chat_event", "optional": false, "rateLimited": null, "retryCount": null, "scriptExpression": null, "sink": null, "startDelay": 0, "subWorkflowParam": null, "taskDefinition": null, "taskReferenceName": "process_chat_event", "type": "SWITCH"}, {"asyncComplete": false, "caseExpression": null, "caseValueParam": null, "description": null, "dynamicForkJoinTasksParam": null, "dynamicForkTasksInputParamName": null, "dynamicForkTasksParam": null, "dynamicTaskNameParam": null, "evaluatorType": null, "expression": null, "inputParameters": {"duration": "0 seconds"}, "loopCondition": null, "name": "lazy_merge", "optional": false, "rateLimited": null, "retryCount": null, "scriptExpression": null, "sink": null, "startDelay": 0, "subWorkflowParam": null, "taskDefinition": null, "taskReferenceName": "lazy_merge_chat_event", "type": "WAIT"}], [{"asyncComplete": false, "caseExpression": null, "caseValueParam": null, "decisionCases": {"message_update": [{"asyncComplete": false, "caseExpression": null, "caseValueParam": null, "description": null, "dynamicForkJoinTasksParam": null, "dynamicForkTasksInputParamName": null, "dynamicForkTasksParam": null, "dynamicTaskNameParam": null, "evaluatorType": null, "expression": null, "inputParameters": {"io_param": "${asc_chat_transform.output.io_param.params.MessageUpdate}", "task": "${asc_chat_transform.output.task}", "workflow": "${workflow.input.workflow}"}, "loopCondition": null, "name": "apply_meta", "optional": false, "rateLimited": null, "retryCount": null, "scriptExpression": null, "sink": null, "startDelay": 0, "subWorkflowParam": null, "taskDefinition": null, "taskReferenceName": "apply_meta_message_update", "type": "SIMPLE"}, {"asyncComplete": false, "inputParameters": {}, "name": "wait_es__apply_meta_message_update", "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "wait_es__apply_meta_message_update", "type": "WAIT"}]}, "defaultCase": [{"asyncComplete": false, "caseExpression": null, "caseValueParam": null, "description": null, "dynamicForkJoinTasksParam": null, "dynamicForkTasksInputParamName": null, "dynamicForkTasksParam": null, "dynamicTaskNameParam": null, "evaluatorType": null, "expression": null, "inputParameters": {"duration": "0 seconds"}, "loopCondition": null, "name": "skip_empty", "optional": false, "rateLimited": null, "retryCount": null, "scriptExpression": null, "sink": null, "startDelay": 0, "subWorkflowParam": null, "taskDefinition": null, "taskReferenceName": "skip_empty_message_update", "type": "WAIT"}], "description": null, "dynamicForkJoinTasksParam": null, "dynamicForkTasksInputParamName": null, "dynamicForkTasksParam": null, "dynamicTaskNameParam": null, "evaluatorType": "javascript", "expression": "$.file_uri != null ? 'message_update' : 'noop_messageupdate'", "inputParameters": {"file_uri": "${asc_chat_transform.output.io_param.params.MessageUpdate.params.file_uri}"}, "loopCondition": null, "name": "process_message_update", "optional": false, "rateLimited": null, "retryCount": null, "scriptExpression": null, "sink": null, "startDelay": 0, "subWorkflowParam": null, "taskDefinition": null, "taskReferenceName": "process_message_update", "type": "SWITCH"}, {"asyncComplete": false, "caseExpression": null, "caseValueParam": null, "description": null, "dynamicForkJoinTasksParam": null, "dynamicForkTasksInputParamName": null, "dynamicForkTasksParam": null, "dynamicTaskNameParam": null, "evaluatorType": null, "expression": null, "inputParameters": {"duration": "0 seconds"}, "loopCondition": null, "name": "lazy_merge", "optional": false, "rateLimited": null, "retryCount": null, "scriptExpression": null, "sink": null, "startDelay": 0, "subWorkflowParam": null, "taskDefinition": null, "taskReferenceName": "lazy_merge_message_update", "type": "WAIT"}]], "inputParameters": {}, "loopCondition": null, "name": "asc_chat_fork", "optional": false, "rateLimited": null, "retryCount": null, "scriptExpression": null, "sink": null, "startDelay": 0, "subWorkflowParam": null, "taskDefinition": null, "taskReferenceName": "asc_chat_fork", "type": "FORK_JOIN"}, {"asyncComplete": false, "caseExpression": null, "caseValueParam": null, "description": null, "dynamicForkJoinTasksParam": null, "dynamicForkTasksInputParamName": null, "dynamicForkTasksParam": null, "dynamicTaskNameParam": null, "evaluatorType": null, "expression": null, "inputParameters": {}, "joinOn": ["lazy_merge_chat_event", "lazy_merge_message"], "loopCondition": null, "name": "asc_chat_join", "optional": false, "rateLimited": null, "retryCount": null, "scriptExpression": null, "sink": null, "startDelay": 0, "subWorkflowParam": null, "taskDefinition": null, "taskReferenceName": "asc_chat_join", "type": "JOIN"}], "timeoutPolicy": "ALERT_ONLY", "timeoutSeconds": 432000, "variables": {}, "workflowStatusListenerEnabled": false}