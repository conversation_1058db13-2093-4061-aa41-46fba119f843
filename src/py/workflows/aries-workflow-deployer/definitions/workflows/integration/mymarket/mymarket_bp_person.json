{"failureWorkflow": "aries_auditable_workflow_failure", "inputParameters": [], "inputTemplate": {"workflow": {"name": "mymarket_bp_person"}}, "name": "mymarket_bp_person", "outputParameters": {}, "ownerEmail": "<EMAIL>", "restartable": true, "schemaVersion": 2, "tasks": [{"asyncComplete": false, "decisionCases": {}, "defaultCase": [], "defaultExclusiveJoinTask": [], "forkTasks": [], "inputParameters": {"io_param": "${workflow.input.io_param}", "workflow": "${workflow.input.workflow}"}, "joinOn": [], "loopOver": [], "name": "sink_file_audit_init", "onStateChange": {}, "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "sink_file_audit_init", "type": "SIMPLE"}, {"asyncComplete": false, "decisionCases": {}, "defaultCase": [], "defaultExclusiveJoinTask": [], "forkTasks": [], "inputParameters": {}, "joinOn": [], "loopOver": [], "name": "wait_es__sink_file_audit_init", "onStateChange": {}, "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "wait_es__sink_file_audit_init", "type": "WAIT"}, {"asyncComplete": false, "decisionCases": {}, "defaultCase": [], "defaultExclusiveJoinTask": [], "forkTasks": [], "inputParameters": {"base": {"params": {"dynamic_tasks": {"AccountPersonCreate": {"name": "elastic_ingestion", "task_reference_name": "elastic_ingestion_create_ref", "type": "SUB_WORKFLOW"}, "AccountPersonUpdate": {"name": "elastic_ingestion", "task_reference_name": "elastic_ingestion_update_ref", "type": "SUB_WORKFLOW"}}}}, "io_param": "${workflow.input.io_param}", "queryExpression": "{ io_param: (.io_param * .base) }"}, "joinOn": [], "loopOver": [], "name": "io_param_params_concatenator", "onStateChange": {}, "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "io_param_params_concatenator", "type": "JSON_JQ_TRANSFORM"}, {"asyncComplete": false, "decisionCases": {}, "defaultCase": [], "defaultExclusiveJoinTask": [], "forkTasks": [], "inputParameters": {"io_param": "${io_param_params_concatenator.output.result.io_param}", "task": "${wait_es__sink_file_audit_init.output.task}", "workflow": "${workflow.input.workflow}"}, "joinOn": [], "loopOver": [], "name": "mymarket_bp_person_transform", "onStateChange": {}, "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "mymarket_bp_person_transform", "type": "SIMPLE"}, {"asyncComplete": false, "decisionCases": {}, "defaultCase": [], "defaultExclusiveJoinTask": [], "forkTasks": [[{"asyncComplete": false, "decisionCases": {}, "defaultCase": [], "defaultExclusiveJoinTask": [], "dynamicForkTasksInputParamName": "dynamicTasksInput", "dynamicForkTasksParam": "dynamicTasks", "forkTasks": [], "inputParameters": {"dynamicTasks": "${mymarket_bp_person_transform.output.io_param.params.AccountPersonCreate.dynamicTasks}", "dynamicTasksInput": "${mymarket_bp_person_transform.output.io_param.params.AccountPersonCreate.dynamicTaskInputs}"}, "joinOn": [], "loopOver": [], "name": "person_create_elastic_ingestion", "onStateChange": {}, "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "person_create_elastic_ingestion_ref", "type": "FORK_JOIN_DYNAMIC"}, {"asyncComplete": false, "decisionCases": {}, "defaultCase": [], "defaultExclusiveJoinTask": [], "forkTasks": [], "inputParameters": {}, "joinOn": [], "loopOver": [], "name": "wait_for_ingestion_person_create", "onStateChange": {}, "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "wait_for_ingestion_person_create_ref", "type": "JOIN"}], [{"asyncComplete": false, "decisionCases": {}, "defaultCase": [], "defaultExclusiveJoinTask": [], "dynamicForkTasksInputParamName": "dynamicTasksInput", "dynamicForkTasksParam": "dynamicTasks", "forkTasks": [], "inputParameters": {"dynamicTasks": "${mymarket_bp_person_transform.output.io_param.params.AccountPersonUpdate.dynamicTasks}", "dynamicTasksInput": "${mymarket_bp_person_transform.output.io_param.params.AccountPersonUpdate.dynamicTaskInputs}"}, "joinOn": [], "loopOver": [], "name": "person_update_elastic_ingestion", "onStateChange": {}, "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "person_update_elastic_ingestion_ref", "type": "FORK_JOIN_DYNAMIC"}, {"asyncComplete": false, "decisionCases": {}, "defaultCase": [], "defaultExclusiveJoinTask": [], "forkTasks": [], "inputParameters": {}, "joinOn": [], "loopOver": [], "name": "wait_for_ingestion_person_update", "onStateChange": {}, "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "wait_for_ingestion_person_update_ref", "type": "JOIN"}]], "inputParameters": {}, "joinOn": [], "loopOver": [], "name": "mymarket_bp_person_fork", "onStateChange": {}, "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "mymarket_bp_person_fork_ref", "type": "FORK_JOIN"}, {"asyncComplete": false, "decisionCases": {}, "defaultCase": [], "defaultExclusiveJoinTask": [], "forkTasks": [], "inputParameters": {}, "joinOn": ["wait_for_ingestion_person_create_ref", "wait_for_ingestion_person_update_ref"], "loopOver": [], "name": "mymarket_bp_person_join", "onStateChange": {}, "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "mymarket_bp_person_join_ref", "type": "JOIN"}, {"asyncComplete": false, "decisionCases": {}, "defaultCase": [], "defaultExclusiveJoinTask": [], "forkTasks": [], "inputParameters": {"io_param": "${sink_file_audit_init.output.io_param}", "task": "${sink_file_audit_init.output.task}", "workflow": "${workflow.input.workflow}"}, "joinOn": [], "loopOver": [], "name": "sink_file_audit_finalize", "onStateChange": {}, "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "sink_file_audit_finalize", "type": "SIMPLE"}, {"asyncComplete": false, "decisionCases": {}, "defaultCase": [], "defaultExclusiveJoinTask": [], "forkTasks": [], "inputParameters": {}, "joinOn": [], "loopOver": [], "name": "wait_es__sink_file_audit_finalize", "onStateChange": {}, "optional": false, "permissive": false, "startDelay": 0, "taskReferenceName": "wait_es__sink_file_audit_finalize", "type": "WAIT"}], "timeoutPolicy": "TIME_OUT_WF", "timeoutSeconds": 432000, "variables": {}, "workflowStatusListenerEnabled": false}