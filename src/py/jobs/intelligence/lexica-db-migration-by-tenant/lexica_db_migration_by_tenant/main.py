import json
import logging
import os
from lexica_db_migration_by_tenant.migration_validator import validation
from lexica_db_migration_by_tenant.postgres_migration import migration

logger = logging.getLogger(__name__)
logging.basicConfig(level=logging.INFO)


class RunType:
    VALIDATION = "VALIDATION"
    MIGRATION = "MIGRATION"
    BOTH = "BOTH"


def main():
    run = os.getenv("RUN_AS")
    tenant_list: str | list = os.getenv("TENANT_LIST", [])

    if isinstance(tenant_list, str):
        tenant_list: list = json.loads(tenant_list)  # type: ignore[no-redef]

    logging.info("Running for tenants {tenants}".format(tenants=tenant_list))

    if run == RunType.MIGRATION:
        logging.info("Running only migration")
        migration(tenant_list)  # type: ignore[arg-type]
    elif run == RunType.VALIDATION:
        logging.info("Running only validation")
        validation(tenant_list)  # type: ignore[arg-type]
    elif run == RunType.BOTH:
        logging.info("Running migration and validation")
        migration(tenant_list)  # type: ignore[arg-type]
        validation(tenant_list)  # type: ignore[arg-type]


if __name__ == "__main__":
    main()
