# mypy: disable-error-code="attr-defined"
import json
import logging
import os
from mar_trials.mocks.utils import LOCAL_MARKET_DATA, DictResponseParser
from market_abuse_algorithms.data_source.repository.news.refinitiv_news import RefinitivNewsClient
from se_api_client.client import <PERSON>pi<PERSON><PERSON>
from typing import Any


def fake_get_sources(self):
    with open(LOCAL_MARKET_DATA / "news/sources.json", "r") as file:
        resp = json.load(file)

    return DictResponseParser(content=resp)


def fake_get_groupings(self):
    resp = [
        "Agriculture",
        "Analyst Ratings",
        "Author",
        "Business",
        "Business Sectors",
        "Central Banks",
        "Commodities",
        "Corporate Actions",
        "Corporate Earnings",
        "country",
        "Country",
        "Crypto",
        "Debt/Credit",
        "Earnings",
        "Energy",
        "Equity - Buybacks",
        "Europe",
        "Financial News",
        "Financial NEws",
        "Forex",
        "Journalists columns",
        "Macroeconomics",
        "Merger Acquisition",
        "News Language",
        "News Source",
        "Other",
        "Regulation",
    ]
    return DictResponseParser(content=resp)


def fake_bulk_get_stories(self, json_body: dict[str, Any]):
    # Determine file name
    relevance = json_body["min_relevance"]
    ric = json_body["ric"]
    start = json_body["date_range"]["start"].replace(" ", "_")
    end = json_body["date_range"]["end"].replace(" ", "_")
    file_name = f"stories_{ric}___{relevance}___{start}___{end}.json"
    file_path = LOCAL_MARKET_DATA / f"news/{file_name}"

    # Returns valid file path, if exists
    def get_file_path():
        if os.path.exists(file_path):
            return file_path

        # If no timezone info in start and end, add UTC timezone info
        if "+" not in start and "+" not in end:
            file_name_tz = (
                f"stories_{ric}___{relevance}___{start + '+00:00'}___{end + '+00:00'}.json"
            )
            file_path_tz = LOCAL_MARKET_DATA / f"news/{file_name_tz}"
            if os.path.exists(file_path_tz):
                return file_path_tz

        return file_path

    # Choose file path based on existence
    file_path = get_file_path()

    # If file does not exist, fetch from API
    if not os.path.exists(file_path):
        try:
            resp = self._client.call_api(api=self, endpoint=self._stories_post, json_body=json_body)
            string = json.dumps(resp.content)
            with open(file_path, "w") as file:
                file.write(string)
        except Exception:
            logging.warning("Failed to save stories! USING EMPTY RESPONSE")
            return DictResponseParser(content=[])

    # Load file
    with open(file_path, "r") as file:
        resp = json.load(file)

    return DictResponseParser(content=resp)


from master_data_api_client.api.news import News  # noqa: E402

setattr(News, "get_sources", fake_get_sources)
setattr(News, "get_groupings", fake_get_groupings)
setattr(News, "bulk_get_stories", fake_bulk_get_stories)


news_host = "http://localhost"
setattr(RefinitivNewsClient, "_get_api_client", lambda *args, **kwargs: ApiClient(host=news_host))
############################

MOCK_NEWS = True
