{"&hash": "e33791216c404c0489d03be26ac383b3d89a7d7cd2ec3d8998bd18da36dc2c38", "&id": "76d656d1-a7f2-9666-3124-b9846f2a8ea5", "&key": "SurveillanceWatch:76d656d1-a7f2-9666-3124-b9846f2a8ea5:1713274177983", "&model": "SurveillanceWatch", "&timestamp": "1704067200000", "&user": "<EMAIL>", "&version": 1, "name": "marking_open_close_intraday_v2___test_case_9_4", "queryType": "MARKET_ABUSE", "query": {"thresholds": "{\"runType\": \"Executions\", \"marketComparison\": 0, \"behaviourType\": \"Marking the Close\", \"directionality\": \"Only Buys, or Only Sells\", \"evaluationType\": \"Trader\", \"priceImprovement\": 0, \"timeWindow\": {\"unit\": \"seconds\", \"value\": 600}}", "marketAbuseReportType": "MARKING_OPEN_CLOSE_INTRADAY_V2", "name": "test_case_9_4", "filters": "{\"bool\":{\"must\":{\"terms\":{\"sourceKey\":[\"steeleyeblotter.mar.mtcmto.xlonauction.2.csv\"]}}}}"}, "createdOn": "2024-01-01T00:00:00.000000+00:00", "frequencyType": "DAILY", "status": "ACTIVE", "backtest": "FULL", "createdBy": "mar.trials", "createdByAdmin": true, "scheduleDetails": {"timeOfDay": "13:00", "timeZone": "Europe/Lisbon", "recurrence": "DAILY"}}