{"&hash": "e33791216c404c0489d03be26ac383b3d89a7d7cd2ec3d8998bd18da36dc2c38", "&id": "859f833b-a139-947a-c24e-e0a65d059e30", "&key": "SurveillanceWatch:859f833b-a139-947a-c24e-e0a65d059e30:1713274177983", "&model": "SurveillanceWatch", "&timestamp": "1704067200000", "&user": "<EMAIL>", "&version": 1, "name": "insider_trading_v2___test_case_1_7", "queryType": "MARKET_ABUSE", "query": {"thresholds": "{\"minimumNotionalValueCurrency\": \"USD\", \"priceChecks\": [{\"daysFrom\": 0, \"daysTo\": 10, \"index\": 0, \"natureOfCheck\": \"Percentage Price Movement\", \"strategy\": \"Forward Looking\", \"threshold\": 0.01}], \"minimumNotionalValue\": 0}", "marketAbuseReportType": "INSIDER_TRADING_V2", "name": "test_case_1_7", "filters": "{\"bool\":{\"must\":{\"terms\":{\"sourceKey\":[\"steeleyeblotter.mar.insidertrading.1.csv\"]}}}}"}, "createdOn": "2024-01-01T00:00:00.000000+00:00", "frequencyType": "DAILY", "status": "ACTIVE", "backtest": "FULL", "createdBy": "mar.trials", "createdByAdmin": true, "scheduleDetails": {"timeOfDay": "13:00", "timeZone": "Europe/Lisbon", "recurrence": "DAILY"}}