{"&hash": "e33791216c404c0489d03be26ac383b3d89a7d7cd2ec3d8998bd18da36dc2c38", "&id": "61c73f79-0f6b-1a8a-4171-4df4e23dd25f", "&key": "SurveillanceWatch:61c73f79-0f6b-1a8a-4171-4df4e23dd25f:1713274177983", "&model": "SurveillanceWatch", "&timestamp": "1704067200000", "&user": "<EMAIL>", "&version": 1, "name": "marking_the_open___test_case_2_2_cas", "queryType": "MARKET_ABUSE", "query": {"thresholds": "{\"client20DayAdv\": 0, \"lookBackPeriod\": 5400, \"market20DayAdv\": 0.01, \"markingType\": \"Marking the open\", \"minimumNotionalCurrency\": \"GBP\", \"priceSpike\": 0.01, \"minimumNotional\": 0}", "marketAbuseReportType": "MARKING_THE_OPEN", "name": "test_case_2_2_cas", "filters": "{\"bool\":{\"must\":{\"terms\":{\"sourceKey\":[\"steeleyeblotter.mar.markingtheopen.2.2.cas.csv\"]}}}}"}, "createdOn": "2024-01-01T00:00:00.000000+00:00", "frequencyType": "DAILY", "status": "ACTIVE", "backtest": "FULL", "createdBy": "mar.trials", "createdByAdmin": true, "scheduleDetails": {"timeOfDay": "13:00", "timeZone": "Europe/Lisbon", "recurrence": "DAILY"}}