{"&hash": "e33791216c404c0489d03be26ac383b3d89a7d7cd2ec3d8998bd18da36dc2c38", "&id": "9f634fc6-31ae-213a-74f8-c7769d583e1e", "&key": "SurveillanceWatch:9f634fc6-31ae-213a-74f8-c7769d583e1e:1713274177983", "&model": "SurveillanceWatch", "&timestamp": "1704067200000", "&user": "<EMAIL>", "&version": 1, "name": "marking_the_open___test_case_5_2", "queryType": "MARKET_ABUSE", "query": {"thresholds": "{\"client20DayAdv\": 0.5, \"lookBackPeriod\": 600, \"market20DayAdv\": 0, \"markingType\": \"Marking the open\", \"minimumNotionalCurrency\": \"EUR\", \"priceSpike\": 0, \"minimumNotional\": 10000}", "marketAbuseReportType": "MARKING_THE_OPEN", "name": "test_case_5_2", "filters": "{\"bool\":{\"must\":{\"terms\":{\"sourceKey\":[\"steeleyeblotter.mar.mtc.eng-6409.3.csv\"]}}}}"}, "createdOn": "2024-01-01T00:00:00.000000+00:00", "frequencyType": "DAILY", "status": "ACTIVE", "backtest": "FULL", "createdBy": "mar.trials", "createdByAdmin": true, "scheduleDetails": {"timeOfDay": "13:00", "timeZone": "Europe/Lisbon", "recurrence": "DAILY"}}