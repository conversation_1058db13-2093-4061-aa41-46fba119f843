{"&hash": "e33791216c404c0489d03be26ac383b3d89a7d7cd2ec3d8998bd18da36dc2c38", "&id": "e4687f29-bee1-91b4-9867-3959c382f48c", "&key": "SurveillanceWatch:e4687f29-bee1-91b4-9867-3959c382f48c:1713274177983", "&model": "SurveillanceWatch", "&timestamp": "1704067200000", "&user": "<EMAIL>", "&version": 1, "name": "potam___test_case_7_4", "queryType": "MARKET_ABUSE", "query": {"thresholds": "{\"extendedWindowDaysBefore\": 30, \"extendedWindowDaysAfter\": 30, \"runType\": \"inside\"}", "marketAbuseReportType": "POTAM", "name": "test_case_7_4", "filters": "{\"bool\":{\"must\":{\"terms\":{\"sourceKey\":[\"steeleyeblotter.mar.potam.7.csv\"]}}}}"}, "createdOn": "2024-01-01T00:00:00.000000+00:00", "frequencyType": "DAILY", "status": "ACTIVE", "backtest": "FULL", "createdBy": "mar.trials", "createdByAdmin": true, "scheduleDetails": {"timeOfDay": "13:00", "timeZone": "Europe/Lisbon", "recurrence": "DAILY"}}