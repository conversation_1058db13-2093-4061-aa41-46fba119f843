{"&hash": "e33791216c404c0489d03be26ac383b3d89a7d7cd2ec3d8998bd18da36dc2c38", "&id": "908a89e9-54e2-7a7c-eebf-0aa1bef0c76b", "&key": "SurveillanceWatch:908a89e9-54e2-7a7c-eebf-0aa1bef0c76b:1713274177983", "&model": "SurveillanceWatch", "&timestamp": "1704067200000", "&user": "<EMAIL>", "&version": 1, "name": "potam___test_case_7_8", "queryType": "MARKET_ABUSE", "query": {"thresholds": "{\"extendedWindowDaysBefore\": 0, \"extendedWindowDaysAfter\": 60}", "marketAbuseReportType": "POTAM", "name": "test_case_7_8", "filters": "{\"bool\":{\"must\":{\"terms\":{\"sourceKey\":[\"steeleyeblotter.mar.potam.7.csv\"]}}}}"}, "createdOn": "2024-01-01T00:00:00.000000+00:00", "frequencyType": "DAILY", "status": "ACTIVE", "backtest": "FULL", "createdBy": "mar.trials", "createdByAdmin": true, "scheduleDetails": {"timeOfDay": "13:00", "timeZone": "Europe/Lisbon", "recurrence": "DAILY"}}