{"&hash": "e33791216c404c0489d03be26ac383b3d89a7d7cd2ec3d8998bd18da36dc2c38", "&id": "3b3a8b2a-5ea4-b7ac-762b-9bb48ae422dd", "&key": "SurveillanceWatch:3b3a8b2a-5ea4-b7ac-762b-9bb48ae422dd:1713274177983", "&model": "SurveillanceWatch", "&timestamp": "1704067200000", "&user": "<EMAIL>", "&version": 1, "name": "potam___test_case_3_3", "queryType": "MARKET_ABUSE", "query": {"thresholds": "{\"extendedWindowDaysBefore\": 30, \"extendedWindowDaysAfter\": 30, \"runType\": \"inside\"}", "marketAbuseReportType": "POTAM", "name": "test_case_3_3", "filters": "{\"bool\":{\"must\":{\"terms\":{\"sourceKey\":[\"steeleyeblotter.mar.potam.3.csv\"]}}}}"}, "createdOn": "2024-01-01T00:00:00.000000+00:00", "frequencyType": "DAILY", "status": "ACTIVE", "backtest": "FULL", "createdBy": "mar.trials", "createdByAdmin": true, "scheduleDetails": {"timeOfDay": "13:00", "timeZone": "Europe/Lisbon", "recurrence": "DAILY"}}