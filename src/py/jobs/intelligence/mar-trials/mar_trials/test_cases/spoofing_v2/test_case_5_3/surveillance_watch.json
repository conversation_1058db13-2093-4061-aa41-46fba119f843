{"&hash": "e33791216c404c0489d03be26ac383b3d89a7d7cd2ec3d8998bd18da36dc2c38", "&id": "1e49c944-8f19-14a9-38f8-9a0a55d1a67b", "&key": "SurveillanceWatch:1e49c944-8f19-14a9-38f8-9a0a55d1a67b:1713274177983", "&model": "SurveillanceWatch", "&timestamp": "1704067200000", "&user": "<EMAIL>", "&version": 1, "name": "spoofing_v2___test_case_5_3", "queryType": "MARKET_ABUSE", "query": {"thresholds": "{\"evaluationType\": \"executingEntity\", \"includePartCancellations\": true, \"realOrderPercentageFilled\": 0, \"spoofOrderPercentageLevel\": 0.1, \"spoofOrderTimeToCancel\": 5, \"spoofingTimeWindow\": 10, \"priceImprovement\": false}", "marketAbuseReportType": "SPOOFING_V2", "name": "test_case_5_3", "filters": "{\"bool\":{\"must\":{\"terms\":{\"sourceKey\":[\"steeleyeblotter.mar.spoofingv2.5.csv\"]}}}}"}, "createdOn": "2024-01-01T00:00:00.000000+00:00", "frequencyType": "DAILY", "status": "ACTIVE", "backtest": "FULL", "createdBy": "mar.trials", "createdByAdmin": true, "scheduleDetails": {"timeOfDay": "13:00", "timeZone": "Europe/Lisbon", "recurrence": "DAILY"}}