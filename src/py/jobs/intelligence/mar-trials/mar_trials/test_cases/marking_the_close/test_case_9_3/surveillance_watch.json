{"&hash": "e33791216c404c0489d03be26ac383b3d89a7d7cd2ec3d8998bd18da36dc2c38", "&id": "b1fbe5ec-162b-1cc2-77d9-54b40d17a215", "&key": "SurveillanceWatch:b1fbe5ec-162b-1cc2-77d9-54b40d17a215:1713274177983", "&model": "SurveillanceWatch", "&timestamp": "1704067200000", "&user": "<EMAIL>", "&version": 1, "name": "marking_the_close___test_case_9_3", "queryType": "MARKET_ABUSE", "query": {"thresholds": "{\"client20DayAdv\": 0.82, \"lookBackPeriod\": 300, \"market20DayAdv\": 0, \"markingType\": \"Marking the close\", \"minimumNotionalCurrency\": \"USD\", \"priceSpike\": 0, \"minimumNotional\": 1000}", "marketAbuseReportType": "MARKING_THE_CLOSE", "name": "test_case_9_3", "filters": "sourceKey in ['steeleyeBlotter.mar.mtc.ENG-6408.1.csv', 'steeleyeBlotter.mar.mtc.ENG-6408.client.trades.1.csv']"}, "createdOn": "2024-01-01T00:00:00.000000+00:00", "frequencyType": "DAILY", "status": "ACTIVE", "backtest": "FULL", "createdBy": "mar.trials", "createdByAdmin": true, "scheduleDetails": {"timeOfDay": "13:00", "timeZone": "Europe/Lisbon", "recurrence": "DAILY"}}