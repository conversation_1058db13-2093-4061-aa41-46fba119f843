{"&hash": "e33791216c404c0489d03be26ac383b3d89a7d7cd2ec3d8998bd18da36dc2c38", "&id": "8bf3eccb-91aa-f4d2-f789-b03ddd3ca72a", "&key": "SurveillanceWatch:8bf3eccb-91aa-f4d2-f789-b03ddd3ca72a:1713274177983", "&model": "SurveillanceWatch", "&timestamp": "1704067200000", "&user": "<EMAIL>", "&version": 1, "name": "marking_the_close___test_case_6_1", "queryType": "MARKET_ABUSE", "query": {"thresholds": "{\"client20DayAdv\": 0, \"lookBackPeriod\": 7200, \"market20DayAdv\": 0, \"markingType\": \"Marking the close\", \"minimumNotionalCurrency\": \"GBP\", \"priceSpike\": 0, \"minimumNotional\": 0}", "marketAbuseReportType": "MARKING_THE_CLOSE", "name": "test_case_6_1", "filters": "{\"bool\":{\"must\":{\"terms\":{\"sourceKey\":[\"steeleyeblotter.mar.markingtheclose.6.csv\"]}}}}"}, "createdOn": "2024-01-01T00:00:00.000000+00:00", "frequencyType": "DAILY", "status": "ACTIVE", "backtest": "FULL", "createdBy": "mar.trials", "createdByAdmin": true, "scheduleDetails": {"timeOfDay": "13:00", "timeZone": "Europe/Lisbon", "recurrence": "DAILY"}}