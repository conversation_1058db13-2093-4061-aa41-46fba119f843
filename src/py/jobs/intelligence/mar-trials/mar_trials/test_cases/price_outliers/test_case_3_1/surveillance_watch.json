{"&hash": "e33791216c404c0489d03be26ac383b3d89a7d7cd2ec3d8998bd18da36dc2c38", "&id": "2b77435f-359c-1edf-cff8-48d869e65a2f", "&key": "SurveillanceWatch:2b77435f-359c-1edf-cff8-48d869e65a2f:1713274177983", "&model": "SurveillanceWatch", "&timestamp": "1704067200000", "&user": "<EMAIL>", "&version": 1, "name": "price_outliers___test_case_3_1", "queryType": "MARKET_ABUSE", "query": {"thresholds": "{\"benchmarkOne\": \"Market Touch Price\", \"benchmarkOnePercentage\": 0.02, \"level\": \"Execution\", \"minimumNotional\": 50000, \"minimumNotionalCurrency\": \"GBP\", \"natureOfOutlier\": \"Both\"}", "marketAbuseReportType": "PRICE_OUTLIERS", "name": "test_case_3_1", "filters": "sourceKey in ['steeleyeblotter.mar.priceoutliers.3.3.csv']"}, "createdOn": "2024-01-01T00:00:00.000000+00:00", "frequencyType": "DAILY", "status": "ACTIVE", "backtest": "FULL", "createdBy": "mar.trials", "createdByAdmin": true, "scheduleDetails": {"timeOfDay": "13:00", "timeZone": "Europe/Lisbon", "recurrence": "DAILY"}}