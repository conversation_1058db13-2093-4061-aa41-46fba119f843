{"&hash": "e33791216c404c0489d03be26ac383b3d89a7d7cd2ec3d8998bd18da36dc2c38", "&id": "016bc8e7-27e0-08f9-76af-a05f28f011bb", "&key": "SurveillanceWatch:016bc8e7-27e0-08f9-76af-a05f28f011bb:1713274177983", "&model": "SurveillanceWatch", "&timestamp": "1704067200000", "&user": "<EMAIL>", "&version": 1, "name": "price_outliers___test_case_2_2", "queryType": "MARKET_ABUSE", "query": {"thresholds": "{\"benchmarkOne\": \"Market Open Price\", \"benchmarkOnePercentage\": 0.01, \"level\": \"Execution\", \"minimumNotional\": 10000, \"minimumNotionalCurrency\": \"GBP\", \"natureOfOutlier\": \"Loss\"}", "marketAbuseReportType": "PRICE_OUTLIERS", "name": "test_case_2_2", "filters": "sourceKey in ['steeleyeblotter.mar.priceoutliers.2.3.csv']"}, "createdOn": "2024-01-01T00:00:00.000000+00:00", "frequencyType": "DAILY", "status": "ACTIVE", "backtest": "FULL", "createdBy": "mar.trials", "createdByAdmin": true, "scheduleDetails": {"timeOfDay": "13:00", "timeZone": "Europe/Lisbon", "recurrence": "DAILY"}}