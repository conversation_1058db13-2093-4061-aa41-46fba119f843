{"&hash": "e33791216c404c0489d03be26ac383b3d89a7d7cd2ec3d8998bd18da36dc2c38", "&id": "9911d336-743b-239f-4259-708961aa9111", "&key": "SurveillanceWatch:9911d336-743b-239f-4259-708961aa9111:1713274177983", "&model": "SurveillanceWatch", "&timestamp": "1704067200000", "&user": "<EMAIL>", "&version": 1, "name": "phishing_v2___test_case_1_1", "queryType": "MARKET_ABUSE", "query": {"thresholds": "{\"timeWindow\": {\"unit\": \"minutes\", \"value\": 20}, \"percentageQuantityOfLargeOrder\": 0.2, \"minNumberOfSmallOrders\": 6}", "marketAbuseReportType": "PHISHING_V2", "name": "test_case_1_1", "filters": "{\"bool\":{\"must\":{\"terms\":{\"sourceKey\":[\"steeleyeblotter.mar.phishingv2.1.csv\"]}}}}"}, "createdOn": "2024-01-01T00:00:00.000000+00:00", "frequencyType": "DAILY", "status": "ACTIVE", "backtest": "FULL", "createdBy": "mar.trials", "createdByAdmin": true, "scheduleDetails": {"timeOfDay": "13:00", "timeZone": "Europe/Lisbon", "recurrence": "DAILY"}}