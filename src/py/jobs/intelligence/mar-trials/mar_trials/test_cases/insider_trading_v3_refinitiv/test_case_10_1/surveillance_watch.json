{"&hash": "e33791216c404c0489d03be26ac383b3d89a7d7cd2ec3d8998bd18da36dc2c38", "&id": "e1a26d80-dab0-e0ef-b9cb-6699fbad65dc", "&key": "SurveillanceWatch:e1a26d80-dab0-e0ef-b9cb-6699fbad65dc:1713274177983", "&model": "SurveillanceWatch", "&timestamp": "1704067200000", "&user": "<EMAIL>", "&version": 1, "name": "insider_trading_v3_refinitiv___test_case_10_1", "queryType": "MARKET_ABUSE", "query": {"thresholds": "{\"evaluationType\": \"Trader\", \"eventCreation\": \"News Feed\", \"newsFeedEventNewsRelevance\": \"high\", \"sentiment\": 0.5, \"activityObservationPeriod\": 2, \"activityBehaviourPeriod\": 3, \"activityAllowedRangeFromProfile\": 0.95, \"activityMinimumTradeAmount\": 100, \"currencyFilter\": \"USD\"}", "marketAbuseReportType": "INSIDER_TRADING_V3_REFINITIV", "name": "test_case_10_1", "filters": "sourceKey in ['steeleyeblotter.mar.insidertradingv3.refinitiv.csv'] and ((executionDetails.orderStatus in ['NEWO'] and timestamps.orderSubmitted inrange [1698278400000,1698364799999]) or (executionDetails.orderStatus notin ['NEWO'] and timestamps.tradingDateTime inrange [1698278400000,1698364799999]))"}, "createdOn": "2024-01-01T00:00:00.000000+00:00", "frequencyType": "DAILY", "status": "ACTIVE", "backtest": "FULL", "createdBy": "mar.trials", "createdByAdmin": true, "scheduleDetails": {"timeOfDay": "13:00", "timeZone": "Europe/Lisbon", "recurrence": "DAILY"}}