{"&hash": "e33791216c404c0489d03be26ac383b3d89a7d7cd2ec3d8998bd18da36dc2c38", "&id": "37fa6f68-1a08-c073-7814-99f474267989", "&key": "SurveillanceWatch:37fa6f68-1a08-c073-7814-99f474267989:1713274177983", "&model": "SurveillanceWatch", "&timestamp": "1704067200000", "&user": "<EMAIL>", "&version": 1, "name": "insider_trading_v3_refinitiv___test_case_10_5", "queryType": "MARKET_ABUSE", "query": {"thresholds": "{\"evaluationType\": \"Trader\", \"eventCreation\": \"Any\", \"newsFeedEventNewsRelevance\": \"high\", \"sentiment\": 0.55, \"activityObservationPeriod\": 2, \"activityBehaviourPeriod\": 3, \"activityAllowedRangeFromProfile\": 0.95, \"marketDataEventPriceVariation\": 0.95, \"activityMinimumTradeAmount\": 100000, \"activityMinimumPNL\": 50000, \"currencyFilter\": \"USD\"}", "marketAbuseReportType": "INSIDER_TRADING_V3_REFINITIV", "name": "test_case_10_5", "filters": "sourceKey in ['steeleyeblotter.mar.insidertradingv3.refinitiv.csv'] and ((executionDetails.orderStatus in ['NEWO'] and timestamps.orderSubmitted inrange [1698278400000,1698364799999]) or (executionDetails.orderStatus notin ['NEWO'] and timestamps.tradingDateTime inrange [1698278400000,1698364799999]))"}, "createdOn": "2024-01-01T00:00:00.000000+00:00", "frequencyType": "DAILY", "status": "ACTIVE", "backtest": "FULL", "createdBy": "mar.trials", "createdByAdmin": true, "scheduleDetails": {"timeOfDay": "13:00", "timeZone": "Europe/Lisbon", "recurrence": "DAILY"}}