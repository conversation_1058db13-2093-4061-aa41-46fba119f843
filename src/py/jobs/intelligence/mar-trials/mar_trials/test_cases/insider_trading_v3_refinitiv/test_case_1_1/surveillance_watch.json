{"&hash": "e33791216c404c0489d03be26ac383b3d89a7d7cd2ec3d8998bd18da36dc2c38", "&id": "f943cd85-27ef-d10d-85e3-75f22e6e4529", "&key": "SurveillanceWatch:f943cd85-27ef-d10d-85e3-75f22e6e4529:1713274177983", "&model": "SurveillanceWatch", "&timestamp": "1704067200000", "&user": "<EMAIL>", "&version": 1, "name": "insider_trading_v3_refinitiv___test_case_1_1", "queryType": "MARKET_ABUSE", "query": {"thresholds": "{\"evaluationType\": \"Trader\", \"eventCreation\": \"Market Data\", \"marketDataEventPriceVariation\": 0.95, \"activityObservationPeriod\": 5, \"activityBehaviourPeriod\": 2, \"activityAllowedRangeFromProfile\": 0.95, \"activityMinimumTradeAmount\": 1000, \"currencyFilter\": \"USD\", \"relativeActivity\": 0.25}", "marketAbuseReportType": "INSIDER_TRADING_V3_REFINITIV", "name": "test_case_1_1", "filters": "sourceKey in ['steeleyeblotter.mar.insidertradingv3.1.csv'] and ((executionDetails.orderStatus in ['NEWO'] and timestamps.orderSubmitted inrange [1655251200000,1656201599999]) or (executionDetails.orderStatus notin ['NEWO'] and timestamps.tradingDateTime inrange [1655251200000,1656201599999]))"}, "createdOn": "2024-01-01T00:00:00.000000+00:00", "frequencyType": "DAILY", "status": "ACTIVE", "backtest": "FULL", "createdBy": "mar.trials", "createdByAdmin": true, "scheduleDetails": {"timeOfDay": "13:00", "timeZone": "Europe/Lisbon", "recurrence": "DAILY"}}