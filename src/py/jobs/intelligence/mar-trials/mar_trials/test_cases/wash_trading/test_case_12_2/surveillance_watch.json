{"&hash": "e33791216c404c0489d03be26ac383b3d89a7d7cd2ec3d8998bd18da36dc2c38", "&id": "d68c4b31-44e5-b6e8-5327-e79673838a67", "&key": "SurveillanceWatch:d68c4b31-44e5-b6e8-5327-e79673838a67:1713274177983", "&model": "SurveillanceWatch", "&timestamp": "1704067200000", "&user": "<EMAIL>", "&version": 1, "name": "wash_trading___test_case_12_2", "queryType": "MARKET_ABUSE", "query": {"thresholds": "{\"maxPriceDifference\": 100, \"maxTimeWindow\": 7200, \"maxVolumeDifference\": 0.1, \"excludeMatchingTimestamps\": false, \"numberOfCounterparties\": \"single\", \"minimumTradedQuantity\": 20}", "marketAbuseReportType": "WASH_TRADING", "name": "test_case_12_2", "filters": "{\"bool\":{\"must\":{\"terms\":{\"sourceKey\":[\"seblotter.mar.wash.12.csv\"]}}}}"}, "createdOn": "2024-01-01T00:00:00.000000+00:00", "frequencyType": "DAILY", "status": "ACTIVE", "backtest": "FULL", "createdBy": "mar.trials", "createdByAdmin": true, "scheduleDetails": {"timeOfDay": "13:00", "timeZone": "Europe/Lisbon", "recurrence": "DAILY"}}