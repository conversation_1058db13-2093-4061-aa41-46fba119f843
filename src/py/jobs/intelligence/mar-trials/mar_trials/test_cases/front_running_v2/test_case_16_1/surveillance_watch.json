{"&hash": "e33791216c404c0489d03be26ac383b3d89a7d7cd2ec3d8998bd18da36dc2c38", "&id": "51b92bcc-1cd2-a101-2737-12d70b2cb681", "&key": "SurveillanceWatch:51b92bcc-1cd2-a101-2737-12d70b2cb681:1713274177983", "&model": "SurveillanceWatch", "&timestamp": "1704067200000", "&user": "<EMAIL>", "&version": 1, "name": "front_running_v2___test_case_16_1", "queryType": "MARKET_ABUSE", "query": {"thresholds": "{\"timeWindow\": {\"unit\": \"minutes\", \"value\": 10}, \"volumeDifference\": 0.18, \"flow\": \"Prop vs. Client\", \"priceImprovement\": false, \"evaluationType\": \"Trader\"}", "marketAbuseReportType": "FRONT_RUNNING_V2", "name": "test_case_16_1", "filters": "{\"bool\": {\"must\": [{\"terms\": {\"sourceKey\": [\"steeleyeblotter.mar.frontrunning2.16.csv\"]}}]}}"}, "createdOn": "2024-01-01T00:00:00.000000+00:00", "frequencyType": "DAILY", "status": "ACTIVE", "backtest": "FULL", "createdBy": "mar.trials", "createdByAdmin": true, "scheduleDetails": {"timeOfDay": "13:00", "timeZone": "Europe/Lisbon", "recurrence": "DAILY"}}