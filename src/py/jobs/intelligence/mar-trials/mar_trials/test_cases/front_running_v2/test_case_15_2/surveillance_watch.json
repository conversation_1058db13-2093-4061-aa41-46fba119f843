{"&hash": "e33791216c404c0489d03be26ac383b3d89a7d7cd2ec3d8998bd18da36dc2c38", "&id": "1e9918ad-6708-a81e-9842-37fc601c6df9", "&key": "SurveillanceWatch:1e9918ad-6708-a81e-9842-37fc601c6df9:1713274177983", "&model": "SurveillanceWatch", "&timestamp": "1704067200000", "&user": "<EMAIL>", "&version": 1, "name": "front_running_v2___test_case_15_2", "queryType": "MARKET_ABUSE", "query": {"thresholds": "{\"frontrunOrderVolume\": 10000000, \"timeWindow\": {\"unit\": \"minutes\", \"value\": 60}, \"volumeDifference\": 0.06, \"flow\": \"PAD vs. Non Pad\", \"priceImprovement\": false}", "marketAbuseReportType": "FRONT_RUNNING_V2", "name": "test_case_15_2", "filters": "{\"bool\": {\"must\": [{\"terms\": {\"sourceKey\": [\"steeleyeblotter.mar.frontrunning2.15.csv\"]}}]}}"}, "createdOn": "2024-01-01T00:00:00.000000+00:00", "frequencyType": "DAILY", "status": "ACTIVE", "backtest": "FULL", "createdBy": "mar.trials", "createdByAdmin": true, "scheduleDetails": {"timeOfDay": "13:00", "timeZone": "Europe/Lisbon", "recurrence": "DAILY"}}