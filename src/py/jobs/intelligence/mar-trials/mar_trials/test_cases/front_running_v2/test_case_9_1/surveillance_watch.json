{"&hash": "e33791216c404c0489d03be26ac383b3d89a7d7cd2ec3d8998bd18da36dc2c38", "&id": "73d6d745-1618-ed0a-ad4e-74044e1b9c70", "&key": "SurveillanceWatch:73d6d745-1618-ed0a-ad4e-74044e1b9c70:1713274177983", "&model": "SurveillanceWatch", "&timestamp": "1704067200000", "&user": "<EMAIL>", "&version": 1, "name": "front_running_v2___test_case_9_1", "queryType": "MARKET_ABUSE", "query": {"thresholds": "{\"adv\": null, \"frontrunOrderVolume\": 1000, \"evaluationType\": \"Trader\", \"flow\": \"Client vs. Client\", \"priceImprovement\": false, \"timeWindow\": {\"unit\": \"minutes\", \"value\": 10}, \"volumeDifference\": 0.1}", "marketAbuseReportType": "FRONT_RUNNING_V2", "name": "test_case_9_1", "filters": "{\"bool\":{\"must\":{\"terms\":{\"sourceKey\":[\"steeleyeblotter.mar.frontrunnng2.9.csv\"]}}}}"}, "createdOn": "2024-01-01T00:00:00.000000+00:00", "frequencyType": "DAILY", "status": "ACTIVE", "backtest": "FULL", "createdBy": "mar.trials", "createdByAdmin": true, "scheduleDetails": {"timeOfDay": "13:00", "timeZone": "Europe/Lisbon", "recurrence": "DAILY"}}