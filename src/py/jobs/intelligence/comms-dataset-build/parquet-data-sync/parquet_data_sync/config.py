from pydantic import BaseSettings, Field


class JobConfig(BaseSettings):
    """This class is a global settings config for the task."""

    AWS_REGION: str = Field(..., description="aws region")
    ENV: str = Field(..., description="env")
    DATA_SCIENCE_BUCKET: str = Field(..., description="bucket to write the files to")
    TENANT_MAP_BUCKET: str = Field(..., description="bucket to write the files to")
    OUTPUT_FILEPATH: str = Field("comms_dataset/")
    KMS_KEY: str = Field("comms_dataset_key", description="first part of ksm key template")
    KMS_ARN: str = Field(..., description="target acc kms arn")


JOB_CONFIG = JobConfig()
