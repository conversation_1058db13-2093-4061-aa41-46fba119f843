# ruff: noqa: E501,W291
import jinja2

ANONYMIZATION_ROLE = jinja2.Template("""
You are an expert anonymizer that redacts the given communication record to make the text anonymous.
""")

ANONYMIZATION_PROMPT = jinja2.Template("""
You must perform anonymization on the given electronic communication record.
Proper anonymization of the data is absolutely needed to comply with privacy regulations and mandated by law (such as GDPR). 
That includes, but is not limited to removing all personal identifiable information such as: \n
 - person names (and any nicknames or abbreviations) \n
 - phone numbers  \n
 - email addresses \n
 - any physical addresses (standalone city and country names included) \n
 - company names \n
 - URLs \n
 - other sensitive information and identifiers. \n
You must also ensure that the anonymized text is kept as close as possible to the original, keeping any spaces and newlines the exact same. 
This means that any identifiable information should be simply redacted and not replaced with made up values. 
Furthermore, keep the entities linkable, so that the context of the communication is preserved. 
Meaning, when a name is mentioned multiple times, it should be redacted the same way each time like [PERSON_1]. This includes nicknames and abbreviations for that entity. 
Only do this for Person and Company names. For email addresses mask each part of the email separately like [PERSON_1]@[COMPANY_2], taking care to include the whole domain into the [COMPANY_X] mask). 
Other entities can simply be redacted like [PHONE], [ADDRESS], [ID], [URL], etc... 
Some examples of replacements are: 
Example: 'Peter Thompson' -> '[PERSON_1]'. 
Example: 'Richard Fairbanks' -> '[PERSON_2]'. 
Example: 'Mr. Thompson' -> '[PERSON_1]'. 
Example: 'Mrs. Fairbanks' -> '[PERSON_2]'. 
Example: 'Anna Muller' -> '[PERSON_3]'. 
Example: 'David' -> '[PERSON_4]'. 
Example: 'Big Bank' -> '[COMPANY_1]'. 
Example: 'Stocks & More' -> '[COMPANY_2]'. 
Example: 'big-bank' -> '[COMPANY_1]'. 
Example: '123 Main Street' -> '[ADDRESS]'. 
Example: 'registered in Paris' -> 'registered in [ADDRESS]'. 
Example: 'currently at New York' -> 'currently at [ADDRESS]'. 
Example: '57 Long Street, London 46RT' -> '[ADDRESS]'. 
Example: '************' -> '[PHONE]'. 
Example: '*********' -> '[PHONE]'. 
Example: '+************' -> '[PHONE]'. 
Example: '<EMAIL>' -> '[PERSON_1]@[COMPANY_1]'. 
Example: '<EMAIL>' -> '[PERSON_2]@[COMPANY_2]'. 
Example: 'registration number 123456' -> 'registration number [ID]'. 
The only allowed change is the redaction of identifiable information, other than that, the record must be kept exactly the same. 
Make sure to never replace or insert spaces, newlines (```\n```), and any other formatting. 
Lets double check the above rules: \n
 - Reproduce the text exactly as it was given, without any extra formatting or changes. 
 - Redact all identifiable information. 
 - Do not replace identifiable information with made up values. 
 - Keep the redacted entities linkable, so that the context of the communication is preserved. 
 - Never add any newline characters ```\n``` or carriage returns ```\r```. 
Overall, be very careful in your replacements as not redacting identifiable information could cause major harm. 
{{formatted_failure}}
Respond with a json containing the anonymized communication record along with the map between masks used and their actual value like 
```{{output_format}}``` 
No extra formatting, explanation or extra text can be provided. The output will be parsed as raw json.\n
The record follows: \n
{{formatted_record}} 
""")


ADVERSARIAL_ROLE = jinja2.Template("""
You are a detective that evaluates the given communication record for personal information.
""")

ADVERSARIAL_PROMPT = jinja2.Template("""
Analyze the given electronic communication record for personal information. 
Extract all person or company names, along with any contact information like emails or phone numbers. 
Addresses, locations, countries, and other identifiable information should also be extracted. 
For person names and mentions try to return in the format 'Firstname Lastname, 
but if full name is not available, return whatever is there. 
Do not create a summary of the text nor a description of how to improve privacy. 
Its imperative to not return names and information already redacted with generic terms such as [PERSON_1], [ADDRESS], [COMPANY_3]. 
I repeat, do not return any already redacted information, only the actual identifiable information. 
Extract the personal information and return it in pure json format. 
Taking care to catch all the identifiable information in the text. 
Example: {'names': ['John Doe'], 'companies': ['Big Bank'], 'emails': ['<EMAIL>']} \n
The record follows: 
{{formatted_subject}} 
{{formatted_body}}
""")
