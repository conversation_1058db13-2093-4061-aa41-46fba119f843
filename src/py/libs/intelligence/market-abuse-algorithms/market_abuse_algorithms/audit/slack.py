# type: ignore
import logging
import os
import requests
from market_abuse_algorithms.audit.models import SlackMessage
from typing import Any

logger = logging.getLogger(__name__)


class SlackReport:
    token = os.environ.get(
        "MAR_WATCHER_SLACK_TOKEN",
        "********************************************************",
    )

    def __init__(self, message: SlackMessage, username: str, icon: str):
        self.icon = icon
        self.message = message
        self.env = os.environ.get("ENVIRONMENT", os.environ.get("ENV", ""))
        self.se_realm = os.environ.get("SE_REALM", os.environ.get("REALM"))
        self.username = username

    def publish(self) -> Any:
        """Publish a message in a slack channel."""

        channel = "#mar-dev-error-watcher" if "prod" not in self.env else "#mar-error-watcher"

        if self.se_realm and "uat" in self.se_realm:
            channel = "#mar-dev-error-watcher"

        text_to_send = (
            self.message.task_message()
            if self.username == "Run Mar Watch"
            else self.message.convert_to_string()
        )

        send_message = dict(
            token=self.token,
            channel=channel,
            text=text_to_send,
            icon_emoji=self.icon,
            username=self.username,
        )

        self.send_raw_message(message=send_message)

    @staticmethod
    def send_raw_message(message: dict) -> dict:
        """Send slack message.

        :param message: str
        :return:
        """

        url = "https://slack.com/api/chat.postMessage"

        response = requests.post(url, data=message)

        result: dict = response.json()

        if not result.get("ok"):
            logger.error(
                msg=f"Request to slack returned an "
                f"error {result.get('error')}, "
                f"the response is:\n{response.text}"
            )

        return result
