from enum import Enum
from market_abuse_algorithms.data_source.static.sdp.order import OrderField
from market_abuse_algorithms.data_source.static.utility import OrderVolume


class ThresholdsNames(str, Enum):
    MINIMUM_NOTIONAL_VALUE = "minimumNotionalValue"
    MINIMUM_NOTIONAL_VALUE_CURRENCY = "minimumNotionalValueCurrency"
    PRICE_CHECKS = "priceChecks"
    ORDER_EVENTS = "orderEvents"


class PriceCheck:
    INDEX = "index"
    STRATEGY = "strategy"
    DAYS_FROM = "daysFrom"
    DAYS_TO = "daysTo"
    NATURE_OF_CHECK = "natureOfCheck"
    THRESHOLD = "threshold"


class PriceCheckStrategy(str, Enum):
    BACKWARD_LOOKING = "Backward Looking"
    FORWARD_LOOKING = "Forward Looking"


class PriceCheckNatureOfCheck(str, Enum):
    PERCENTAGE_PRICE_MOVEMENT = "Percentage Price Movement"
    VOLATILITY_FROM_TRADE_DATE = "Volatility from Trade Date"
    VOLATILITY_FROM_CHECK_DATE = "Volatility from Check Date"


class OrderEvents(str, Enum):
    EXECUTION_LEVEL = "Execution Level"
    ORDER_LEVEL = "Order Level"


class EventData(str, Enum):
    EVENT_DATE = "Event Date"
    EVENT_QUANTITY = "Event Quantity"
    EVENT_PRICE = "Event Price"
    EVENT_NOTIONAL = "Event Notional"


class DFColumns:
    CLOSE_PRICE = "closePrice"
    CLOSE_PRICE_VENUE = "close_price_venue"
    CLOSE_PRICE_CCY = "close_price_ccy"
    CLOSE_TIMESTAMP = "closeTimestamp"
    COMMS_COUNT = "commsCount"
    INVOLVED_PARTIES = "involvedParties"
    LOWER_BOUND = "lowerBound"
    ORDER_KEY = "orderKey"
    MPU = "MPU"
    PRICE_MOVEMENT = "priceMovement"
    RIC = "RIC"
    RULE_INDEX = "ruleIndex"
    STD = "std"
    STRATEGY = "strategy"
    TRADER_ORDERS = "traderOrders"
    TRADE_DATE = "tradeDate"
    TRIGGER_DATE = "triggerDate"
    UPPER_BOUND = "upperBound"
    VOLATILITY = "volatility"
    VOLATILITY_CCY = "volatility_ccy"
    VOLATILITY_VENUE = "volatility_venue"


EVENTS_DATA_MAP = {
    OrderEvents.EXECUTION_LEVEL: {
        EventData.EVENT_DATE: OrderField.TS_TRADING_DATE_TIME,
        EventData.EVENT_PRICE: OrderField.PC_FD_PRICE,
        EventData.EVENT_NOTIONAL: OrderField.get_best_exc_trx_ecb_ref_rate_ccy,
        EventData.EVENT_QUANTITY: OrderField.PC_FD_TRD_QTY,
    },
    OrderEvents.ORDER_LEVEL: {
        EventData.EVENT_DATE: OrderField.TS_ORD_SUBMITTED,
        EventData.EVENT_PRICE: OrderVolume.WEIGHTED_EXECUTION_PRICE,
        EventData.EVENT_NOTIONAL: OrderField.get_best_exc_ord_ecb_ref_rate_ccy,
        EventData.EVENT_QUANTITY: OrderField.PC_FD_INIT_QTY,
    },
}
