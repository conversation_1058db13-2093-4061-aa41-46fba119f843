import addict
import elasticsearch_dsl
import numpy as np
import pandas as pd
from functools import reduce
from market_abuse_algorithms.audit.audit import Audit
from market_abuse_algorithms.data_source.query.sdp.order import OrderAggs, OrderQuery
from market_abuse_algorithms.data_source.repository.market_data.client import (
    get_market_client,
)
from market_abuse_algorithms.data_source.static.sdp.order import NewColumns, OrderField
from market_abuse_algorithms.strategy.base.models import StrategyContext
from market_abuse_algorithms.strategy.base.query import BaseQuery
from market_abuse_algorithms.strategy.suspicious_large_order_volume.static import (
    DFColumns,
)
from operator import add
from pandas.tseries.offsets import BDay
from typing import Dict, List, Optional


class Queries(BaseQuery):
    BACK_DAYS_FOR_ADV = 5
    ORDERS_PREFIX = "orders."
    HISTOGRAM_PREFIX = "histogram."

    def __init__(
        self,
        context: StrategyContext,
        audit: Audit,
        evaluation_type: str,
        lbp: int,
        sec_adv_lbp_day_ema: float,
        by_firm: bool = False,
        by_counterparty: bool = False,
        by_instrument: bool = False,
        by_client: bool = False,
        by_trader: bool = False,
        set_date_range: bool = True,
    ):
        super().__init__(context=context, audit=audit)

        self._market_data_client = get_market_client(tenant=context.tenant)

        self._th_lbp = lbp
        self._th_sec_adv_lbp_day_ema = sec_adv_lbp_day_ema

        # Evaluation type
        self._evaluation_type = evaluation_type
        self._by_firm = by_firm
        self._by_instrument = by_instrument
        self._by_counterparty = by_counterparty
        self._by_client = by_client
        self._by_trader = by_trader

        self._set_date_range = set_date_range

        self._required_fields = self._get_required_fields()

        self._start_date_for_adv = None

        self._evaluate_thresholds()

    def _evaluate_thresholds(
        self,
    ):
        # set date range
        if self._set_date_range:
            query = self._get_base_query()

            self._set_date_range_thresholds(query, remove_data_range_from_filter=False)

            if self._start_date is not None:
                self._start_date_for_adv = self._start_date - BDay(
                    self._th_lbp + self.BACK_DAYS_FOR_ADV
                )

    def _get_required_fields(self) -> List[str]:
        # Set required fields
        required_fields = [
            OrderField.META_KEY,
            OrderField.PC_FD_INIT_QTY,
            OrderField.TS_ORD_SUBMITTED,
        ]

        if self._by_client:
            # todo: should be changed to clientFileIdentifier?
            required_fields.append(OrderField.CLIENT_IDENT_CLIENT_ID)

        if self._by_counterparty:
            required_fields.append(OrderField.COUNTERPARTY_ID)

        if self._by_instrument:
            required_fields.append(OrderField.get_instrument_fields())

        if self._by_trader:
            # todo: should be changed to traderFileIdentifier
            required_fields.append(NewColumns.TRADER_ID)

        return required_fields

    def _get_base_query(self):
        query = OrderQuery()

        self.add_default_conditions_to_query(query)

        query = self.get_query_with_required_fields(query, fields=self._required_fields)

        return query

    # -------------------------------------------------------------------------
    #                   Tenant Activity
    # -------------------------------------------------------------------------

    def tenant_activity(self) -> pd.DataFrame:
        query = self._get_tenant_activity_query()

        result = self._sdp_repository.search_query(query)

        if result.get("hits", {}).get("total", {}).get("value", 0) == 0:
            # No data
            return pd.DataFrame()

        result = result.get("aggregations")

        dfs_list = []
        # By firms
        if OrderAggs.DATE_HISTOGRAM in result.keys():
            df = self._get_tenant_histogram_result(result)
            dfs_list.append(df)

        elif OrderAggs.BY_INSTRUMENT in result:
            result = result.get(OrderAggs.BY_INSTRUMENT).get("buckets")

            if len(result) == 0:
                return pd.DataFrame()

            if OrderAggs.DATE_HISTOGRAM in result[0]:
                dfs = [self._get_tenant_histogram_result(x, instrument=x["key"]) for x in result]
            elif OrderAggs.BY_COUNTERPARTY in result[0]:
                dfs = [
                    self._get_tenant_histogram_result(y, instrument=x["key"], counterparty=y["key"])  # noqa: E501
                    for x in result
                    for y in x[OrderAggs.BY_COUNTERPARTY].get("buckets")
                ]
            elif OrderAggs.BY_CLIENT in result[0]:
                dfs = [
                    self._get_tenant_histogram_result(
                        y.get(OrderAggs.REVERSE_NESTED),
                        instrument=x["key"],
                        client=y["key"],
                    )
                    for x in result
                    for y in x.get(OrderAggs.BY_CLIENT).get(OrderAggs.BY_CLIENT_ID).get("buckets")
                ]
            elif OrderAggs.BY_TRADER in result[0]:
                # todo: should be changed to traderFileIdentifier?
                dfs = [
                    self._get_tenant_histogram_result(
                        y.get(OrderAggs.REVERSE_NESTED),
                        instrument=x["key"],
                        trader=y["key"],
                    )
                    for x in result
                    for y in x.get(OrderAggs.BY_TRADER).get(OrderAggs.BY_TRADER_ID).get("buckets")
                ]
            else:
                dfs = []
            if dfs:
                dfs_list.extend(dfs)

        elif OrderAggs.BY_COUNTERPARTY in result:
            dfs = [
                self._get_tenant_histogram_result(x, counterparty=x["key"])
                for x in result.get(OrderAggs.BY_COUNTERPARTY).get("buckets")
            ]
            dfs_list.extend(dfs)

        elif OrderAggs.BY_CLIENT in result:
            dfs = [
                self._get_tenant_histogram_result(x.get(OrderAggs.REVERSE_NESTED), client=x["key"])
                for x in result.get(OrderAggs.BY_CLIENT).get(OrderAggs.BY_CLIENT_ID).get("buckets")
            ]
            dfs_list.extend(dfs)

        elif OrderAggs.BY_TRADER in result:
            # todo: should be changed to traderFileIdentifier?
            dfs = [
                self._get_tenant_histogram_result(x.get(OrderAggs.REVERSE_NESTED), trader=x["key"])
                for x in result.get(OrderAggs.BY_TRADER).get(OrderAggs.BY_TRADER_ID).get("buckets")
            ]
            dfs_list.extend(dfs)

        if not dfs_list:
            return pd.DataFrame()

        df = pd.concat(dfs_list, ignore_index=True)

        if df.empty:
            return pd.DataFrame()

        df = self._process_tenant_result(df)

        return df

    def _get_tenant_activity_query(self) -> OrderQuery:
        query = self._get_base_query()

        query.size(0)

        query.add_start_date(self._start_date_for_adv, field=OrderField.TS_ORD_SUBMITTED)
        query.add_end_date(self._end_date, field=OrderField.TS_ORD_SUBMITTED)

        q_aggs = query.q.aggs

        # Instrument Aggregation
        q_aggs = OrderAggs.aggs_instrument(q_aggs)

        # Counterparty aggregation
        if self._by_counterparty:
            q_aggs = OrderAggs.aggs_counterparty(q_aggs)

        # Client aggregation
        elif self._by_client:
            q_aggs = OrderAggs.aggs_client(q_aggs)

        # Trader aggregation
        elif self._by_trader:
            q_aggs = OrderAggs.aggs_trader(q_aggs)

        # NOTE: hard-coded this to do not discard any bucket
        check_adv_exists = False

        self._add_tenant_metrics(
            q_aggs,
            date_field=OrderField.TS_ORD_SUBMITTED,
            sum_field=OrderField.PC_FD_INIT_QTY,
            window=self._th_lbp,
            adv=True,
            check_adv_exists=check_adv_exists,
        )

        return query

    @staticmethod
    def _add_tenant_metrics(
        q_aggs: elasticsearch_dsl.aggs.Agg,
        date_field: str,
        sum_field: str,
        window: int,
        date_interval: str = "1d",
        adv: bool = False,
        check_adv_exists: bool = False,
    ) -> None:
        assert all([len(x) > 0 for x in [date_field, date_interval, sum_field]])
        assert window > 0

        q_aggs = OrderAggs.aggs_date_histogram(q_aggs, field=date_field, interval=date_interval)

        q_aggs.metric(OrderAggs.TOTAL_VOLUME, "sum", field=sum_field).metric(
            OrderAggs.MIN_TS, "min", field=OrderField.TS_ORD_SUBMITTED
        ).metric(OrderAggs.MAX_TS, "max", field=OrderField.TS_ORD_SUBMITTED).metric(
            DFColumns.TIME_DIFFERENCE,
            "bucket_script",
            buckets_path={"min_ts": OrderAggs.MIN_TS, "max_ts": OrderAggs.MAX_TS},
            script="params.max_ts - params.min_ts",
        ).bucket(
            DFColumns.ORDERS_KEYS,
            "terms",
            field=OrderField.META_KEY,
            size=OrderQuery.MAX_AGGS_SIZE,
        )

        q_aggs[DFColumns.ORDERS_KEYS].bucket(
            OrderField.PC_FD_INIT_QTY,
            "terms",
            field=OrderField.PC_FD_INIT_QTY,
            size=OrderQuery.MAX_AGGS_SIZE,
        )

        q_aggs[DFColumns.ORDERS_KEYS].bucket(
            OrderField.TS_ORD_SUBMITTED,
            "terms",
            field=OrderField.TS_ORD_SUBMITTED,
            size=OrderQuery.MAX_AGGS_SIZE,
        )

        if adv:
            OrderAggs.aggs_adv(q_aggs, window=window, buckets_path=OrderAggs.TOTAL_VOLUME)

        if check_adv_exists:
            OrderAggs.metric_adv_exists(q_aggs)

    def _get_tenant_histogram_result(
        self,
        result: addict.Dict,
        instrument: Optional[str] = None,
        counterparty: Optional[str] = None,
        client: Optional[str] = None,
        trader: Optional[str] = None,
    ) -> pd.DataFrame:
        df = pd.json_normalize(
            result.to_dict()[OrderAggs.DATE_HISTOGRAM]["buckets"],
            record_path=[[DFColumns.ORDERS_KEYS, "buckets"]],
            meta=[
                OrderAggs.ADV,
                "key_as_string",
                DFColumns.TIME_DIFFERENCE,
                OrderAggs.TOTAL_VOLUME,
            ],
            meta_prefix=self.HISTOGRAM_PREFIX,
            record_prefix=self.ORDERS_PREFIX,
            errors="ignore",
        )

        if df.empty:
            return df

        df[DFColumns.EVALUATION_TYPE] = self._evaluation_type

        if instrument:
            df[NewColumns.INSTRUMENT_CODE] = instrument
        if counterparty:
            df[OrderField.COUNTERPARTY_ID] = counterparty
        if client:
            df[OrderField.CLIENT_IDENT_CLIENT_ID] = (
                client  # todo: should be changed to clientFileIdentifier?
            )
        if trader:
            df[NewColumns.TRADER_ID] = trader  # todo: should be changed to traderFileIdentifier?

        return df

    def _process_tenant_result(self, df: pd.DataFrame) -> pd.DataFrame:
        # NOTE: attention to prefixes used in _get_tenant_histogram_result,
        #   they should be consistent

        # Order Initial Quantity
        df[OrderField.PC_FD_INIT_QTY] = df[
            f"{self.ORDERS_PREFIX}{OrderField.PC_FD_INIT_QTY}.buckets"
        ].apply(lambda x: x[0]["key"])

        # Order Submitted TS
        df[OrderField.TS_ORD_SUBMITTED] = df[
            f"{self.ORDERS_PREFIX}{OrderField.TS_ORD_SUBMITTED}.buckets"
        ].apply(lambda x: x[0]["key_as_string"])

        df[OrderField.TS_ORD_SUBMITTED] = pd.to_datetime(
            df[OrderField.TS_ORD_SUBMITTED],
            infer_datetime_format=False,
            format="%Y-%m-%dT%H:%M:%S.%fZ",
        )

        # Filter df by start date
        if self._start_date is not None:
            ts_order_submitted_mask = df[OrderField.TS_ORD_SUBMITTED] >= self._start_date
            df = df.loc[ts_order_submitted_mask]

        # Histogram
        for col in [
            f"{self.HISTOGRAM_PREFIX}{OrderAggs.ADV}",
            f"{self.HISTOGRAM_PREFIX}{DFColumns.TIME_DIFFERENCE}",
            f"{self.HISTOGRAM_PREFIX}{OrderAggs.TOTAL_VOLUME}",
        ]:
            df[col] = df[col].dropna().apply(lambda x: x["value"])

        df = df.rename(
            columns={
                f"{self.HISTOGRAM_PREFIX}key_as_string": DFColumns.DATE,
                f"{self.HISTOGRAM_PREFIX}{OrderAggs.ADV}": DFColumns.TENANT_ADV,
                f"{self.HISTOGRAM_PREFIX}{DFColumns.TIME_DIFFERENCE}": DFColumns.TIME_DIFFERENCE,
                f"{self.HISTOGRAM_PREFIX}{OrderAggs.TOTAL_VOLUME}": DFColumns.TENANT_TOTAL_VOLUME,
                f"{self.ORDERS_PREFIX}key": OrderField.META_KEY,
            }
        )

        columns_to_drop_mask = df.columns.isin(
            [
                "instrument",
                "trader",
                f"{self.ORDERS_PREFIX}{OrderField.PC_FD_INIT_QTY}",
                f"{self.ORDERS_PREFIX}{OrderField.TRX_DTL_ULTIMATE_VENUE}",
                f"{self.ORDERS_PREFIX}{OrderField.TS_ORD_SUBMITTED}",
                f"{self.ORDERS_PREFIX}{OrderField.INST_EXT_PC_REFS}",
            ]
        ) | (
            df.columns.str.endswith("bound")
            | (df.columns.str.endswith("count"))
            | (df.columns.str.endswith("buckets"))
        )

        df = df.loc[:, ~columns_to_drop_mask]

        # Dtypes
        df[DFColumns.DATE] = pd.to_datetime(
            df[DFColumns.DATE],
            infer_datetime_format=False,
            format="%Y-%m-%dT%H:%M:%S.%fZ",
        )

        for col in [
            DFColumns.TENANT_ADV,
            DFColumns.TENANT_TOTAL_VOLUME,
            OrderField.PC_FD_INIT_QTY,
        ]:
            df[col] = df[col].dropna().astype(np.float32)

        df = self._process_tenant_result_shift_adv_to_previous_bday(df)

        # Make sure numeric columns are numeric
        for field in [
            OrderField.PC_FD_INIT_QTY,
            DFColumns.TENANT_ADV,
            DFColumns.TENANT_TOTAL_VOLUME,
        ]:
            if field in df.columns:
                df[field] = pd.to_numeric(df[field])

        # Add OrderField.INST_EXT_UNIQUE_IDENT to link market data
        if self._by_instrument:
            meta_key_inst_unique_ident_map = self._get_meta_key_unique_instrument_identifiers_map(
                data=df
            )
            df[OrderField.INST_EXT_UNIQUE_IDENT] = df[OrderField.META_KEY].map(
                meta_key_inst_unique_ident_map
            )

        return df

    def _process_tenant_result_shift_adv_to_previous_bday(self, df: pd.DataFrame) -> pd.DataFrame:
        common_merge_cols = [NewColumns.INSTRUMENT_CODE]

        # Counterparty aggregation
        if self._by_counterparty:
            common_merge_cols.append(OrderField.COUNTERPARTY_ID)

        # Client aggregation
        elif self._by_client:
            # todo: should be changed to clientFileIdentifier
            common_merge_cols.append(OrderField.CLIENT_IDENT_CLIENT_ID)

        # Trader aggregation
        elif self._by_trader:
            # todo: should be changed to traderFileIdentifier
            common_merge_cols.append(NewColumns.TRADER_ID)

        # Note: Cannot be business day because we are working on that normalized to UTC.
        # For example, found a case where two trades were submitted in a Friday and Sunday,
        # which will end up in duplicated adv for Monday.
        df["next_day"] = df[DFColumns.DATE] + pd.Timedelta(days=1)

        df["tenant_side_col"] = reduce(
            add, [df[col].astype(str) for col in [*common_merge_cols, OrderField.DATE]]
        )
        df["adv_side_col"] = reduce(
            add, [df[col].astype(str) for col in [*common_merge_cols, "next_day"]]
        )

        adv_cols_mask = df.columns.isin(
            [
                DFColumns.TENANT_ADV,
                DFColumns.TIME_DIFFERENCE,
                DFColumns.TENANT_TOTAL_VOLUME,
                "adv_side_col",
            ]
        )

        df = pd.merge(
            df.loc[:, ~adv_cols_mask].drop_duplicates(),
            df.loc[:, adv_cols_mask].drop_duplicates(),
            how="left",
            left_on="tenant_side_col",
            right_on="adv_side_col",
        ).drop(columns=["adv_side_col", "tenant_side_col", "next_day"])

        tenant_adv_not_null_mask = df[DFColumns.TENANT_ADV].notnull()

        df = df.loc[tenant_adv_not_null_mask]

        return df

    def _get_meta_key_unique_instrument_identifiers_map(
        self, data: pd.DataFrame
    ) -> Optional[Dict[str, str]]:
        """Return the map {&key: instrument unique identifier} for the data
        passed in.

        :param data:
        :return:
        """
        meta_keys = data[OrderField.META_KEY].dropna().unique().tolist()

        query = OrderQuery()

        query.size(self.MINI_BATCH_SIZE)
        query.key(meta_keys)

        query.includes([OrderField.META_KEY, OrderField.INST_EXT_UNIQUE_IDENT])

        response = self._sdp_repository.search_after_query(query)

        if response.empty:
            return

        map_ = (
            response[[OrderField.META_KEY, OrderField.INST_EXT_UNIQUE_IDENT]]
            .set_index(OrderField.META_KEY)[OrderField.INST_EXT_UNIQUE_IDENT]
            .to_dict()
        )

        return map_

    # -------------------------------------------------------------------------
    #                   Market Activity
    # -------------------------------------------------------------------------

    def get_market_data(
        self,
        instrument_unique_identifiers: List[str],
    ) -> pd.DataFrame:
        """Based on the instrument unique identifiers retrieved, fetches the
        market data with the adv calculated for the window set in the
        threshold.

        :param instrument_unique_identifiers: List of instrument unique identifiers.
        :return:
        """

        results = []

        for inst_id in instrument_unique_identifiers:
            data = self._market_data_client.get_market_data_adv(
                instrument_unique_identifier=inst_id,
                adv_col_name=DFColumns.MARKET_ADV,
                window=self._th_lbp,
                start_date=self.look_back_period_ts,
                end_date=self.market_data_end_date,
            )

            if data.empty:
                continue

            data[OrderField.INST_EXT_UNIQUE_IDENT] = inst_id

            results.append(data)

        if not results:
            return pd.DataFrame()

        df = pd.concat(results).drop_duplicates()

        return df

    # -------------------------------------------------------------------------
    #                   Additional queries
    # -------------------------------------------------------------------------

    def get_additional_fields_result(self, orders_keys: List[str]) -> pd.DataFrame:
        query = OrderQuery()
        query.size(self.MINI_BATCH_SIZE)

        query.key(orders_keys)

        includes = [
            OrderField.META_KEY,
            OrderField.CLIENT_IDENT_CLIENT_NAME,
            OrderField.COUNTERPARTY_NAME,
            OrderField.PARTICIPANTS,
            OrderField.TRADER,
            OrderField.INST_FULL_NAME,
            *OrderField.get_instrument_fields(),
        ]

        query.includes(includes)

        result = self._sdp_repository.search_after_query(query)

        cols = [
            OrderField.META_KEY,
            OrderField.CLIENT_IDENT_CLIENT_NAME,
            OrderField.COUNTERPARTY_NAME,
            NewColumns.TRADER_NAME,
            OrderField.INST_FULL_NAME,
            NewColumns.INSTRUMENT_CODE,
        ]

        result = result.loc[:, result.columns.isin(cols)]

        result[DFColumns.EVALUATION_TYPE] = self._evaluation_type

        return result
