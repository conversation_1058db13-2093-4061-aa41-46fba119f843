# ruff: noqa: E501, E712
import addict
import datetime
import pandas as pd
from market_abuse_algorithms.cross_product.cross_product import (
    CrossProductActivityDisplay,
)
from market_abuse_algorithms.cross_product.static import CrossProductColumns
from market_abuse_algorithms.cross_product.utils import (
    convert_timestamp_to_epoch,
    generate_final_result_dict,
)
from market_abuse_algorithms.data_source.query.sdp.order import OrderStatus
from market_abuse_algorithms.data_source.repository.market_data.utils import (
    get_level_mid_price,
)
from market_abuse_algorithms.data_source.static.sdp.order import BuySell, NewColumns, OrderField
from market_abuse_algorithms.mar_audit.mar_audit import (
    DATETIME_FORMAT,
    AggregatedStepAudit,
    StepAudit,
)
from market_abuse_algorithms.strategy.abstract_spoofing_v2.alerts import (
    SpoofingV2AbstractAlert,
)
from market_abuse_algorithms.strategy.abstract_spoofing_v2.query import Queries
from market_abuse_algorithms.strategy.abstract_spoofing_v2.static import (
    ORDER_TYPE_GROUP_MAP,
    AlertColumnsEnum,
    AlgoColumnsEnum,
    ThresholdsNames,
)
from market_abuse_algorithms.strategy.base.errors import StrategyError
from market_abuse_algorithms.strategy.base.models import StrategyContext
from market_abuse_algorithms.strategy.base.static import ScenarioFields, StrategyName
from market_abuse_algorithms.strategy.base.strategy import (
    AbstractStrategy,
    market_abuse_audit_object,
    singleton_audit_object,
)
from market_abuse_algorithms.utils.data import (
    calculate_percentage,
    check_number_of_orders,
    create_time_window_groups,
    get_client_name,
    get_instrument_id_code,
    get_order_status_for_ids,
    get_unique_value_for_alert,
    get_venue_name,
    sort_by_date,
)
from se_elastic_schema.components.mar.strategy.spoofing_v2.thresholds import (
    EvaluationTypeEnum,
    SpoofingV2Thresholds,
)
from se_elastic_schema.components.mar.strategy.spoofing_v2_level1.thresholds import (
    SpoofingV2Level1Thresholds,
)
from se_elastic_schema.static.mar_audit import DropReason, StepType
from typing import Dict, List, NoReturn, Optional, Tuple, Type, Union


class SpoofingV2AbstractStrategy(AbstractStrategy):
    """Spoofing V2 with order book depth level 2.

    Jira tickets for the logic:
    https://steeleye.atlassian.net/browse/EP-159
    """

    def __init__(
        self,
        context: StrategyContext,
        strategy_name: str,
        queries: Type[Queries],
        thresholds: Union[SpoofingV2Thresholds, SpoofingV2Level1Thresholds],
    ):
        super().__init__(
            context=context,
            strategy_name=strategy_name,
            thresholds_class=thresholds,
            queries_class=queries,
        )
        self.evaluation_type: EvaluationTypeEnum = self.context.thresholds.dict().get(
            ThresholdsNames.EVALUATION_TYPE
        )
        self._th_evaluation_type: str = ORDER_TYPE_GROUP_MAP.get(self.evaluation_type)
        self._th_include_partial_cancellation: bool = self.context.thresholds.dict().get(
            ThresholdsNames.INCLUDE_PARTIALLY_CANCELLATIONS
        )
        self._th_price_improvement: float = self.context.thresholds.dict().get(
            ThresholdsNames.PRICE_IMPROVEMENT
        )
        self._th_real_order_percentage_fill: float = self.context.thresholds.dict().get(
            ThresholdsNames.REAL_ORDER_PERCENTAGE_FILLED
        )

        self._th_spoof_order_time_to_cancellation: int = self.context.thresholds.dict().get(
            ThresholdsNames.SPOOF_ORDER_TIME_TO_CANCEL
        )

        self._th_spoofing_time_window: int = self.context.thresholds.dict().get(
            ThresholdsNames.SPOOFING_TIME_WINDOW
        )

        self.START_TIME = datetime.datetime.now(datetime.timezone.utc).strftime(DATETIME_FORMAT)

        self._market_data_client = self.queries.market_data_client

        self.cross_product = CrossProductActivityDisplay(
            es_client=self.queries.sdp_repository,
            market_data_client=self._market_data_client,
        )

    def _apply_strategy(self):
        """Fetches data from sdp in batches and applies the algo strategy."""
        self._logger.debug(f"Start getting data to analyse for {self.strategy_name}")

        for data in self.queries.cases_to_analyse(strategy_name=self.strategy_name):
            end, method_id = self.get_start_time_and_unique_id()

            singleton_audit_object.write_audit_data_to_local_files(
                AggregatedStepAudit(
                    aggregated_step_id=method_id,
                    start=self.START_TIME,
                    end=end,
                    number_of_dropped_orders=0,
                    number_of_input_orders=len(data),
                    number_of_resulting_orders=len(data),
                    step_name="Initial Data Retrieval",
                    step_type=StepType.FILTERS,
                )
            )
            self.START_TIME = end
            with StrategyError.handle_algo_records_error(audit=self._audit, data=data):
                market_abuse_audit_object.records_analysed += len(data)
                self._run_algo(data=data)

    def cross_product_logic(self, data: pd.DataFrame) -> pd.DataFrame:
        """Method that searches and fetches cross product activity orders from
        the given orders.

        :param data: dataframe with orders to search for cross product activity
        :return: dataframe with cross product activity found
        """
        # orders to search for related instruments with cross-product
        original_orders = data[OrderField.META_KEY].tolist()
        earliest_timestamp = data[OrderField.TS_ORD_UPDATED].dropna().min()
        start = convert_timestamp_to_epoch(earliest_timestamp)
        end = convert_timestamp_to_epoch(
            earliest_timestamp + pd.to_timedelta(self._th_spoofing_time_window, unit="s")
        )

        self.cross_product.set_orders(orders=original_orders)

        related_records_df = self.cross_product.run_checks_alert_detection(
            start=start, end=end, filters=self.context.filters
        )

        if related_records_df.empty:
            self._logger.debug("No related records retrieved from all checks")
            return pd.DataFrame()

        related_meta_keys = related_records_df[OrderField.META_KEY].dropna().unique().tolist()

        # fetch related data with the query that the algo uses to fetch the orders
        related_query = self.queries._real_orders_query(iris_filters=False)
        related_query.add_terms_query_with_chunks(
            value=related_meta_keys, field=OrderField.META_KEY, mode="filter"
        )

        related_results = self.queries.sdp_repository.search_after_query(query=related_query)

        if self._th_real_order_percentage_fill > 0:
            related_results: pd.DataFrame = self.queries.filter_newos_by_child_executions_fields(
                orders_df=related_results,
                mode="filter",
                fields={
                    OrderField.EXC_DTL_ORD_STATUS: [
                        OrderStatus.FILL,
                        OrderStatus.PARF,
                    ]
                },
            )

            if related_results.empty:
                self._logger.warning("Order without any child FILL/PARF.")
                return pd.DataFrame()

        del related_query

        if related_results.empty:
            self._logger.debug("No related orders to be used as real orders were retrieved")
            return pd.DataFrame()

        # get the missing columns and add the meta_key to be able to merge
        cols_to_merge = list(set(related_records_df.columns) - set(related_results.columns)) + [
            OrderField.META_KEY
        ]

        related_results = related_results.merge(
            related_records_df[cols_to_merge],
            on=[OrderField.META_KEY],
        )

        del related_records_df

        if related_results.empty:
            self._logger.debug(
                "No related orders after merging the fetched related orders with the records obtained "
                "from the cross-product checks"
            )
            return pd.DataFrame()

        related_results[CrossProductColumns.RELATED_INSTRUMENT] = True

        return related_results

    def _run_algo(self, data: pd.DataFrame) -> None:
        """Groups orders and apply the algo logic. In the end creates alerts if
        needed.

        :param data: order data grouped by instrument
        """
        end, method_id = self.get_start_time_and_unique_id()

        self._logger.debug(f"{self.strategy_name} is starting to run.")

        data: pd.DataFrame = data.reset_index().drop(columns=["index"])
        time_cancel_filtered_data: pd.DataFrame = self.apply_time_to_cancel_filter(
            data=data, method_id=method_id
        )

        end = datetime.datetime.now(datetime.timezone.utc).strftime(DATETIME_FORMAT)
        singleton_audit_object.write_audit_data_to_local_files(
            AggregatedStepAudit(
                aggregated_step_id=method_id,
                start=self.START_TIME,
                end=end,
                number_of_dropped_orders=abs(len(data) - len(time_cancel_filtered_data)),
                number_of_input_orders=len(data),
                number_of_resulting_orders=len(time_cancel_filtered_data),
                step_name="Application of 'Spoof Order Time to Cancel' Threshold",
                step_type=StepType.FILTERS,
            )
        )
        self.START_TIME = end

        if time_cancel_filtered_data.empty:
            self._logger.debug(
                "No data after applying timestamp (time to cancel) threshold. "
                "Terminating this batch run."
            )
            return

        groupings_no = len(
            time_cancel_filtered_data.groupby(
                by=[
                    NewColumns.INSTRUMENT_CODE,
                    self._th_evaluation_type,
                    OrderField.EXC_DTL_BUY_SELL_IND,
                ]
            )
        )
        groupings_dropped = 0
        for idx, group in time_cancel_filtered_data.groupby(
            by=[
                NewColumns.INSTRUMENT_CODE,
                self._th_evaluation_type,
                OrderField.EXC_DTL_BUY_SELL_IND,
            ]
        ):
            end, method_id = self.get_start_time_and_unique_id()
            singleton_audit_object.write_audit_data_to_local_files(
                StepAudit(
                    step_id=method_id,
                    reason=(
                        f"Grouping by instrument {idx[0]}, "
                        f"{self.context.thresholds.dict().get(ThresholdsNames.EVALUATION_TYPE)} {idx[1]}, and "
                        f"direction {idx[2]}."
                    ),
                )
            )

            singleton_audit_object.write_audit_data_to_local_files(
                AggregatedStepAudit(
                    aggregated_step_id=method_id,
                    start=self.START_TIME,
                    end=end,
                    number_of_dropped_orders=0,
                    number_of_input_orders=len(group),
                    number_of_resulting_orders=len(group),
                    step_name="Creation of groupings per 'Evaluation Type' threshold",
                    step_type=StepType.GROUPING,
                    groupings=groupings_no,
                    groupings_dropped=groupings_dropped,
                )
            )
            self.START_TIME = end

            alerts_data: List[pd.DataFrame] = self.run_spoofing_algo(spoofing_data=group)

            end, method_id = self.get_start_time_and_unique_id()

            if len(alerts_data) == 0:
                groupings_dropped += 1
                singleton_audit_object.write_audit_data_to_local_files(
                    AggregatedStepAudit(
                        aggregated_step_id=method_id,
                        start=self.START_TIME,
                        end=end,
                        number_of_dropped_orders=len(group),
                        number_of_input_orders=len(group),
                        number_of_resulting_orders=0,
                        step_name="Creation of Alerts",
                        step_type=StepType.ALERT_CREATION,
                        drop_reason=DropReason.RECORDS_DROPPED_CREATE_ALERTS,
                        groupings=groupings_no,
                        groupings_dropped=groupings_dropped,
                    )
                )
                self.START_TIME = end
                self._logger.debug(
                    f"No alerts were generated for {self.strategy_name} in this execution. "
                    f" Terminating this batch run."
                )
            else:
                singleton_audit_object.write_audit_data_to_local_files(
                    AggregatedStepAudit(
                        aggregated_step_id=method_id,
                        start=self.START_TIME,
                        end=end,
                        number_of_dropped_orders=0,
                        number_of_input_orders=len(group),
                        number_of_resulting_orders=len(group),
                        step_name="Creation of Alerts",
                        step_type=StepType.ALERT_CREATION,
                        groupings=groupings_no,
                        groupings_dropped=groupings_dropped,
                    )
                )
                self.START_TIME = end

            for alert in alerts_data:
                self._logger.debug("Start creating alert.")
                self._create_alert(alert_data=alert)

        self.START_TIME = end

    # flake8: noqa: C901
    def run_spoofing_algo(self, spoofing_data: pd.DataFrame) -> List[pd.DataFrame]:
        """Apply core logic of the algo.

        :param spoofing_data: dataframe with orders grouped by instrument/buy sell/ evaluation type
        :return: dataframe with alerts
        """

        results: List[pd.DataFrame] = []

        sorted_data: pd.DataFrame = sort_by_date(
            data=spoofing_data, date_column=OrderField.TS_ORD_UPDATED
        )

        self._logger.debug("Creating time window groups according with step 5.")

        resulting_orders = []

        time_window_groups: List[pd.DataFrame] = create_time_window_groups(
            data=sorted_data,
            time_column=OrderField.TS_ORD_UPDATED,
            time_threshold=self._th_spoofing_time_window,
            time_delta_column=AlgoColumnsEnum.TIMEDELTA,
            only_one_order=True,
        )

        time_window = {
            "Start": str(sorted_data[OrderField.TS_ORD_UPDATED].dropna().unique()[0]),
            "End": str(
                sorted_data[OrderField.TS_ORD_UPDATED].dropna().unique()[0]
                + pd.to_timedelta(self._th_spoofing_time_window, unit="s")
            ),
        }

        for time_group in time_window_groups:
            resulting_orders.extend(time_group.get(OrderField.META_KEY, pd.Series()).tolist())

        end, method_id = self.get_start_time_and_unique_id()

        if len(resulting_orders) < len(sorted_data):
            singleton_audit_object.write_audit_data_to_local_files(
                StepAudit(
                    step_id=method_id,
                    list_of_order_ids=list(
                        set(spoofing_data.get(OrderField.META_KEY, pd.Series()).tolist())
                        - set(resulting_orders)
                    ),
                    reason=f"Orders dropped for not being inside the time window {time_window}.",
                )
            )

        singleton_audit_object.write_audit_data_to_local_files(
            AggregatedStepAudit(
                aggregated_step_id=method_id,
                start=self.START_TIME,
                end=end,
                number_of_dropped_orders=len(spoofing_data) - len(resulting_orders),
                number_of_input_orders=len(spoofing_data),
                number_of_resulting_orders=len(resulting_orders),
                step_name="Creation of groupings per 'Time Window' threshold",
                step_type=StepType.FILTERS,
                drop_reason=DropReason.RECORDS_DROPPED_TIME_WINDOW_GROUPS,
            )
        )
        self.START_TIME = end

        if (not time_window_groups) or (len(time_window_groups) < 1):
            self._logger.debug(
                "Algorithm will terminate, since weren't find any time groups to evaluate."
            )
            end, method_id = self.get_start_time_and_unique_id()

            singleton_audit_object.write_audit_data_to_local_files(
                StepAudit(
                    step_id=method_id,
                    list_of_order_ids=list(
                        set(spoofing_data.get(OrderField.META_KEY, pd.Series()).tolist())
                    ),
                    reason="Algorithm will terminate, since weren't find any time groups to evaluate.",
                )
            )

            singleton_audit_object.write_audit_data_to_local_files(
                AggregatedStepAudit(
                    aggregated_step_id=method_id,
                    start=self.START_TIME,
                    end=end,
                    number_of_dropped_orders=len(spoofing_data),
                    number_of_input_orders=len(spoofing_data),
                    number_of_resulting_orders=0,
                    step_name="Creation of groupings per 'Time Window' threshold",
                    step_type=StepType.FILTERS,
                    drop_reason=DropReason.RECORDS_DROPPED_TIME_WINDOW_GROUPS,
                )
            )

            return results

        groupings_no = len(time_window_groups)
        groupings_dropped = 0

        for time_group in time_window_groups:
            time_group[OrderField.TS_ORD_SUBMITTED] = pd.to_datetime(
                time_group[OrderField.TS_ORD_SUBMITTED], format="mixed"
            )

            if not check_number_of_orders(data=time_group, only_one_order=True):
                groupings_dropped += 1
                end, method_id = self.get_start_time_and_unique_id()
                order_key: str = time_group.loc[:, OrderField.META_KEY][0]
                singleton_audit_object.write_audit_data_to_local_files(
                    StepAudit(
                        step_id=method_id,
                        list_of_order_ids=time_group.get(OrderField.META_KEY, pd.Series()).tolist(),
                        reason=f"This time group only has one order ID {order_key}.",
                    )
                )
                singleton_audit_object.write_audit_data_to_local_files(
                    AggregatedStepAudit(
                        aggregated_step_id=method_id,
                        start=self.START_TIME,
                        end=end,
                        number_of_dropped_orders=len(time_group),
                        number_of_input_orders=len(time_group),
                        number_of_resulting_orders=0,
                        step_name="Check number of orders.",
                        step_type=StepType.FILTERS,
                        drop_reason=DropReason.RECORDS_DROPPED_FILTER_DATA,
                        groupings=groupings_no,
                        groupings_dropped=groupings_dropped,
                    )
                )
                self.START_TIME = end
                self._logger.debug(f"This time group only has one order ID {order_key}.")
                continue

            rics = spoofing_data[AlgoColumnsEnum.RIC].dropna().unique().tolist()

            end, method_id = self.get_start_time_and_unique_id()

            if not rics:
                groupings_dropped += 1
                singleton_audit_object.write_audit_data_to_local_files(
                    StepAudit(
                        step_id=method_id,
                        list_of_order_ids=time_group.get(OrderField.META_KEY, pd.Series()).tolist(),
                        reason="Didn't find any RICs for the orders.",
                    )
                )
                singleton_audit_object.write_audit_data_to_local_files(
                    AggregatedStepAudit(
                        aggregated_step_id=method_id,
                        start=self.START_TIME,
                        end=end,
                        number_of_dropped_orders=len(time_group),
                        number_of_input_orders=len(time_group),
                        number_of_resulting_orders=0,
                        step_name="Fetching 'RIC' for Instrument",
                        step_type=StepType.FILTERS,
                        drop_reason=DropReason.RECORDS_DROPPED_FILTER_MANDATORY_FIELDS,
                        groupings=groupings_no,
                        groupings_dropped=groupings_dropped,
                    )
                )
                self.START_TIME = end
                self._logger.warning("Didn't find any RICs for this group")
                continue

            singleton_audit_object.write_audit_data_to_local_files(
                AggregatedStepAudit(
                    aggregated_step_id=method_id,
                    start=self.START_TIME,
                    end=end,
                    number_of_dropped_orders=0,
                    number_of_input_orders=len(time_group),
                    number_of_resulting_orders=len(time_group),
                    step_name="Fetching 'RIC' for Instrument",
                    step_type=StepType.FILTERS,
                )
            )
            self.START_TIME = end

            start_date: pd.Timestamp = pd.Timestamp(
                time_group.loc[:, OrderField.TS_ORD_SUBMITTED].min()
            )

            end_date: pd.Timestamp = pd.Timestamp(
                time_group.loc[:, OrderField.TS_ORD_SUBMITTED].max()
            )

            timestamps = time_group.loc[:, OrderField.TS_ORD_SUBMITTED]

            # to search for OBD data
            # from the start of the day until the TS_ORD_SUBMITTED of the newest order
            # to assure that we have the nearest timestamp of the order
            dates = {"start": start_date, "end": end_date, "timestamps": timestamps}

            end, method_id = self.get_start_time_and_unique_id()

            singleton_audit_object.write_audit_data_to_local_files(
                AggregatedStepAudit(
                    aggregated_step_id=method_id,
                    start=self.START_TIME,
                    end=end,
                    number_of_dropped_orders=0,
                    number_of_input_orders=len(time_group),
                    number_of_resulting_orders=len(time_group),
                    step_name="Get start/end dates to fetch market data",
                    step_type=StepType.FILTERS,
                    groupings=groupings_no,
                    groupings_dropped=groupings_dropped,
                )
            )

            eligible_spoof_groups: List = []
            market_data_list: List = []

            for ric in rics:
                try:
                    market_data = self.step_6_get_market_data(ric=ric, dates=dates)

                except Exception as e:
                    self._logger.warning(f"Could not fetch OBD data for RIC {ric} due to '{e}'")
                    market_data = pd.DataFrame()

                market_data_list.append(market_data)

                end, method_id = self.get_start_time_and_unique_id()

                if market_data.empty:
                    groupings_dropped += 1
                    singleton_audit_object.write_audit_data_to_local_files(
                        StepAudit(
                            step_id=method_id,
                            list_of_order_ids=time_group.get(
                                OrderField.META_KEY, pd.Series()
                            ).tolist(),
                            reason=f"No Market Data available for RIC {ric} between {start_date} and {end_date}.",
                        )
                    )
                    singleton_audit_object.write_audit_data_to_local_files(
                        AggregatedStepAudit(
                            aggregated_step_id=method_id,
                            start=self.START_TIME,
                            end=end,
                            number_of_dropped_orders=len(time_group),
                            number_of_input_orders=len(time_group),
                            number_of_resulting_orders=0,
                            step_name="Fetching Market Data for Instrument",
                            step_type=StepType.MARKET_DATA_OBD
                            if self.strategy_name == StrategyName.LAYERING_V2
                            else StepType.MARKET_DATA_TICKS,
                            drop_reason=DropReason.RECORDS_DROPPED_OBD_DATA
                            if self.strategy_name == StrategyName.LAYERING_V2
                            else DropReason.RECORDS_DROPPED_TICK_DATA,
                            groupings=groupings_no,
                            groupings_dropped=groupings_dropped,
                        )
                    )
                    self.START_TIME = end
                    self._logger.debug(f"No Order Book Depth data available for ric {ric}.")
                    continue

                singleton_audit_object.write_audit_data_to_local_files(
                    AggregatedStepAudit(
                        aggregated_step_id=method_id,
                        start=self.START_TIME,
                        end=end,
                        number_of_dropped_orders=0,
                        number_of_input_orders=len(time_group),
                        number_of_resulting_orders=len(time_group),
                        step_name="Fetching Market Data for Instrument",
                        step_type=StepType.MARKET_DATA_OBD
                        if self.strategy_name == StrategyName.LAYERING_V2
                        else StepType.MARKET_DATA_TICKS,
                    )
                )
                self.START_TIME = end

                (
                    valid_spoof_groups,
                    groupings_dropped,
                ) = self.step_6_7_get_params_at_level_apply_threshold(
                    market_data=market_data,
                    tenant_data=time_group,
                    start_time_of_method=self.START_TIME,
                    number_of_dropped_records=groupings_dropped,
                    groupings_no=groupings_no,
                    ric=ric,
                )

                if not valid_spoof_groups.empty:
                    eligible_spoof_groups.append(valid_spoof_groups)

            market_data_merged: pd.DataFrame = (
                pd.concat(market_data_list) if len(market_data_list) > 0 else pd.DataFrame()
            )

            end, method_id = self.get_start_time_and_unique_id()
            if market_data_merged.empty:
                groupings_dropped += 1
                singleton_audit_object.write_audit_data_to_local_files(
                    StepAudit(
                        step_id=method_id,
                        list_of_order_ids=time_group.get(OrderField.META_KEY, pd.Series()).tolist(),
                        reason=f"No market data for all RICs {rics}.",
                    )
                )
                singleton_audit_object.write_audit_data_to_local_files(
                    AggregatedStepAudit(
                        aggregated_step_id=method_id,
                        start=self.START_TIME,
                        end=end,
                        number_of_dropped_orders=len(time_group),
                        number_of_input_orders=len(time_group),
                        number_of_resulting_orders=0,
                        step_name=f"Join market data for all RICs {rics}.",
                        step_type=StepType.MARKET_DATA,
                        drop_reason=DropReason.RECORDS_DROPPED_FILTER_MARKET_DATA,
                        groupings=groupings_no,
                        groupings_dropped=groupings_dropped,
                    )
                )
                self.START_TIME = end

                self._logger.debug("No Market Data data available.")
                continue

            eligible_market_data: pd.DataFrame = (
                pd.concat(eligible_spoof_groups)
                if len(eligible_spoof_groups) > 0
                else pd.DataFrame()
            )  # tenant data with order book depth data to be used

            end, method_id = self.get_start_time_and_unique_id()

            if eligible_market_data.empty:
                groupings_dropped += 1
                singleton_audit_object.write_audit_data_to_local_files(
                    StepAudit(
                        step_id=method_id,
                        list_of_order_ids=time_group.get(OrderField.META_KEY, pd.Series()).tolist(),
                        reason="No data after validating against order book depth data.",
                    )
                )
                singleton_audit_object.write_audit_data_to_local_files(
                    AggregatedStepAudit(
                        aggregated_step_id=method_id,
                        start=self.START_TIME,
                        end=end,
                        number_of_dropped_orders=len(time_group),
                        number_of_input_orders=len(time_group),
                        number_of_resulting_orders=0,
                        step_name="Application of 'Spoof Order % of Level' Threshold",
                        step_type=StepType.THRESHOLD_CALCULATION,
                        drop_reason=DropReason.RECORDS_DROPPED_FILTER_DATA,
                        groupings=groupings_no,
                        groupings_dropped=groupings_dropped,
                    )
                )
                self.START_TIME = end
                self._logger.debug("No data after validating against order book depth data.")
                continue

            singleton_audit_object.write_audit_data_to_local_files(
                AggregatedStepAudit(
                    aggregated_step_id=method_id,
                    start=self.START_TIME,
                    end=end,
                    number_of_dropped_orders=0,
                    number_of_input_orders=len(time_group),
                    number_of_resulting_orders=len(time_group),
                    step_name="Application of 'Spoof Order % of Level' Threshold",
                    step_type=StepType.THRESHOLD_CALCULATION,
                )
            )
            self.START_TIME = end

            end, method_id = self.get_start_time_and_unique_id()

            # dict to have the different OBD data for the possible RIC hits on related
            obd_data: Dict[str, pd.DataFrame] = {rics[0]: market_data_merged}

            # Fetch Cross-Product
            related_instruments = self.cross_product_logic(data=eligible_market_data)

            eligible_market_data[
                AlgoColumnsEnum.REAL_ORDERS
            ]: pd.DataFrame = eligible_market_data.apply(
                lambda x: self.queries.fetch_real_orders(
                    instrument=x[OrderField.INST_ID_CODE],
                    buy_sell_ind=x[OrderField.EXC_DTL_BUY_SELL_IND],
                    spoof_evaluation=x[self._th_evaluation_type],
                    order_id=x.get(OrderField.META_KEY, ""),
                    related_instrument=related_instruments,
                    method_id=method_id,
                ),
                axis=1,
            )

            alert_candidate: pd.DataFrame = eligible_market_data.dropna(
                subset=[AlgoColumnsEnum.REAL_ORDERS]
            )

            end = datetime.datetime.now(datetime.timezone.utc).strftime(DATETIME_FORMAT)

            singleton_audit_object.write_audit_data_to_local_files(
                AggregatedStepAudit(
                    aggregated_step_id=method_id,
                    start=self.START_TIME,
                    end=end,
                    number_of_dropped_orders=len(eligible_market_data) - len(alert_candidate),
                    number_of_input_orders=len(eligible_market_data),
                    number_of_resulting_orders=len(alert_candidate),
                    step_name="Querying for eligible 'Real Orders' (to pair with 'Spoof Orders')",
                    step_type=StepType.FILTERS,
                    drop_reason=DropReason.RECORDS_DROPPED_FETCH_DATA,
                )
            )
            self.START_TIME = end

            if alert_candidate.empty:
                self._logger.debug("No data after removing NaN from alert candidate.")
                continue

            alert_candidate_sorted_date: pd.DataFrame = sort_by_date(
                data=alert_candidate, date_column=OrderField.TS_ORD_UPDATED
            )
            alert_candidate_sorted_date[OrderField.TS_ORD_SUBMITTED] = pd.to_datetime(
                alert_candidate_sorted_date[OrderField.TS_ORD_SUBMITTED], format="mixed"
            )

            alert_candidate_sorted_date[
                AlgoColumnsEnum.REAL_ORDERS
            ]: pd.DataFrame = alert_candidate_sorted_date.apply(
                lambda x: self._apply_spoofing_time_window(
                    real_orders_df=x[AlgoColumnsEnum.REAL_ORDERS],
                    spoof_orders_sub_ts=x[OrderField.TS_ORD_SUBMITTED],
                ),
                axis=1,
            )

            spoofing_timedelta = datetime.timedelta(seconds=self._th_spoofing_time_window)
            order_time = pd.to_datetime(
                alert_candidate_sorted_date[OrderField.TS_ORD_SUBMITTED].dropna().unique()[0],
                format="mixed",
            )
            alert_candidate_time_window = {
                "Start": str(order_time - spoofing_timedelta),
                "End": str(order_time + spoofing_timedelta),
            }

            alert_candidate_sorted_date: pd.DataFrame = alert_candidate_sorted_date.dropna(
                subset=[AlgoColumnsEnum.REAL_ORDERS]
            )

            end, method_id = self.get_start_time_and_unique_id()

            if len(alert_candidate_sorted_date) < len(alert_candidate):
                singleton_audit_object.write_audit_data_to_local_files(
                    StepAudit(
                        step_id=method_id,
                        list_of_order_ids=list(
                            set(alert_candidate.get(OrderField.META_KEY, pd.Series()).tolist())
                            - set(
                                alert_candidate_sorted_date.get(
                                    OrderField.META_KEY, pd.Series()
                                ).tolist()
                            )
                        ),
                        reason=f"Alert candidates dropped for not being inside the spoofing time window {alert_candidate_time_window}.",
                    )
                )
            singleton_audit_object.write_audit_data_to_local_files(
                AggregatedStepAudit(
                    aggregated_step_id=method_id,
                    start=self.START_TIME,
                    end=end,
                    number_of_dropped_orders=len(alert_candidate)
                    - len(alert_candidate_sorted_date),
                    number_of_input_orders=len(alert_candidate),
                    number_of_resulting_orders=len(alert_candidate_sorted_date),
                    step_name="Application of 'Spoofing Time Window' Threshold",
                    step_type=StepType.FILTERS,
                    drop_reason=DropReason.RECORDS_DROPPED_TIME_WINDOW_GROUPS,
                )
            )
            self.START_TIME = end

            if alert_candidate_sorted_date.empty:
                self._logger.debug(
                    "No data after removing NaN from alert candidate sorted by date."
                )
                continue

            start, method_id = self.get_start_time_and_unique_id()

            list_valid_alerts: List[pd.DataFrame] = self._step_16_price_improvement(
                spoofing_data=alert_candidate_sorted_date, valid_market_data=obd_data
            )

            valid_alert_spoofing_part: pd.DataFrame = (
                pd.concat(list_valid_alerts, axis=1)
                if len(list_valid_alerts) > 0
                else pd.DataFrame()
            )

            end = datetime.datetime.now(datetime.timezone.utc).strftime(DATETIME_FORMAT)

            if len(valid_alert_spoofing_part) < len(alert_candidate_sorted_date):
                singleton_audit_object.write_audit_data_to_local_files(
                    StepAudit(
                        step_id=method_id,
                        list_of_order_ids=list(
                            set(
                                alert_candidate_sorted_date.get(
                                    OrderField.META_KEY, pd.Series()
                                ).tolist()
                            )
                            - set(
                                valid_alert_spoofing_part.get(
                                    OrderField.META_KEY, pd.Series()
                                ).tolist()
                            )
                        ),
                        reason="Removing invalid data after calculating price improvement.",
                    )
                )

            singleton_audit_object.write_audit_data_to_local_files(
                AggregatedStepAudit(
                    aggregated_step_id=method_id,
                    start=self.START_TIME,
                    end=end,
                    number_of_dropped_orders=len(alert_candidate_sorted_date)
                    - len(valid_alert_spoofing_part),
                    number_of_input_orders=len(alert_candidate_sorted_date),
                    number_of_resulting_orders=len(valid_alert_spoofing_part),
                    step_name="Calculation and Application of 'Price Improvement'",
                    step_type=StepType.FILTERS,
                    drop_reason=DropReason.RECORDS_DROPPED_FILTER_DATA,
                )
            )
            self.START_TIME = end

            if valid_alert_spoofing_part.empty:
                self._logger.debug(
                    "No valid spoofing data after calculating the price improvement."
                )
                continue

            alert_calculations: addict.Dict = self._calculations_spoof_real_orders(
                valid_alert_data=valid_alert_spoofing_part
            )

            enriched_valid_spoofing_alert: pd.DataFrame = self.enrich_valid_alert(
                data=valid_alert_spoofing_part, alert_cals=alert_calculations
            )

            results.append(enriched_valid_spoofing_alert)

        return results

    def _step_16_price_improvement(
        self,
        spoofing_data: pd.DataFrame,
        valid_market_data: Dict[str, pd.DataFrame],
    ) -> List[pd.DataFrame]:
        """Calculation as to whether not price improvement has taken place.
        Measured by inspecting the L1 price at the time of the first spoof
        order against the L1 price at the time of the first execution against
        the real order. If the real order is a buy order, we would expect the
        L1 price to move down, if the real order isa  sell order, we would
        expect the L1 price to move up.

        Calculate whether a Price Improvement has taken place

        For each grouping

        If all the “Real Orders” are ‘Related Instruments’, then skip this step
        (if a scenario candidate has 2 Real Orders, 1 Related, and 1 Same Instrument,
        this check should proceed on the basis of the Same Instrument. )

        For the “Spoof Orders” :
            vTimestampPriceImprovementStart = take the earliest [Order Time Submitted]
            vLevel1MidPriceOfSpoofOrder = Level-1-Mid-Price at vTimestampPriceImprovementStart

        For the “Real Orders”:
            vTimestampPriceImprovementEnd = take the earliest [Transaction Date Time],
            if there is no [Transaction Date Time] (i.e., there are no executions on any of the “Real Orders”
            then take the last [Order Time Submitted]).
            If this timestamp is < vTimestampPriceImprovementStart
                then: take the vOrderCancelTime of the “Spoof Orders”

            vLevel1MidPriceOfRealOrder = Level-1-Mid-Price  at vTimestampPriceImprovementEnd

        if (“Real Orders” are BUY AND vLevel1MidPriceOfSpoofOrder > vLevel1MidPriceOfRealOrder)
                OR (“Real Orders” are SELL AND vLevel1MidPriceOfSpoofOrder < vLevel1MidPriceOfRealOrder)
            vPriceImprovement = yes

        else
            vPriceImprovement = no

        calculate:
            vPriceImprovementPercentage

        :param spoofing_data: pd.Dataframe with both spoof orders and real orders
        :param valid_market_data: pd.Dataframe with valid order book depth data

        :return:
        """

        price_improvement_groups: List[pd.DataFrame] = []

        for spoof_buy_sell_ind, group in spoofing_data.groupby(by=OrderField.EXC_DTL_BUY_SELL_IND):
            real_orders: pd.DataFrame = group.loc[:, AlgoColumnsEnum.REAL_ORDERS].values[0]

            # get only the real orders that were obtained normally
            same_instrument_real_orders = real_orders[
                real_orders[CrossProductColumns.RELATED_INSTRUMENT] == False
            ]
            # get the related real orders
            related_real_orders = real_orders[
                real_orders[CrossProductColumns.RELATED_INSTRUMENT] == True
            ]

            # if we only have related as real orders
            if same_instrument_real_orders.empty and not related_real_orders.empty:
                # skip the price improvement calculation
                price_improvement_groups.append(group)
                continue

            spoof_orders_sorted_submitted: pd.DataFrame = group.sort_values(
                by=[OrderField.TS_ORD_SUBMITTED], ascending=True
            )

            real_orders_buy_sell_ind = (
                same_instrument_real_orders.loc[:, OrderField.EXC_DTL_BUY_SELL_IND]
                .unique()
                .tolist()[0]
            )
            if spoof_buy_sell_ind == real_orders_buy_sell_ind:
                self._logger.warning(
                    "Real Orders and Spoof Orders should have opposite buy/sell indicators"
                )
                continue

            timestamp_price_improvement_start: pd.Timestamp = spoof_orders_sorted_submitted.iloc[0][
                OrderField.TS_ORD_SUBMITTED
            ]

            ric = group[AlgoColumnsEnum.RIC].unique()[0]
            market_data = valid_market_data.get(ric, pd.DataFrame())

            if market_data.empty:
                self._logger.warning(f"No OBD data to fetch the Mid Prices for RIC: {ric}")
                continue

            spoof_orders_sorted_submitted.loc[:, AlertColumnsEnum.SPOOF_ORDERS_MID_PRICE] = (
                spoof_orders_sorted_submitted.apply(
                    lambda row: get_level_mid_price(
                        market_data=market_data,
                        order_time_submitted=timestamp_price_improvement_start,
                    ),
                    axis=1,
                )
            )

            if (
                OrderField.TS_TRADING_DATE_TIME in same_instrument_real_orders
                and not same_instrument_real_orders[OrderField.TS_TRADING_DATE_TIME].isnull().all()
            ):
                read_orders_sorted: pd.DataFrame = same_instrument_real_orders.sort_values(
                    by=[OrderField.TS_TRADING_DATE_TIME], ascending=True
                )

                timestamp_price_improvement_end: pd.Timestamp = read_orders_sorted.iloc[0][
                    OrderField.TS_TRADING_DATE_TIME
                ]
            else:
                read_orders_sorted: pd.DataFrame = same_instrument_real_orders.sort_values(
                    by=[OrderField.TS_ORD_SUBMITTED], ascending=False
                )

                timestamp_price_improvement_end: pd.Timestamp = read_orders_sorted.iloc[0][
                    OrderField.TS_ORD_SUBMITTED
                ]

            if timestamp_price_improvement_end < timestamp_price_improvement_start:
                timestamp_price_improvement_end = spoof_orders_sorted_submitted.iloc[-1][
                    AlgoColumnsEnum.ORDER_CANCEL_TIME
                ]

            spoof_orders_sorted_submitted.loc[:, AlertColumnsEnum.REAL_ORDERS_MID_PRICE] = (
                spoof_orders_sorted_submitted.apply(
                    lambda row: get_level_mid_price(
                        market_data=market_data,
                        order_time_submitted=timestamp_price_improvement_end,
                    ),
                    axis=1,
                )
            )

            spoof_orders_sorted_submitted: pd.DataFrame = spoof_orders_sorted_submitted.dropna(
                subset=[
                    AlertColumnsEnum.SPOOF_ORDERS_MID_PRICE,
                    AlertColumnsEnum.REAL_ORDERS_MID_PRICE,
                ]
            )
            buy_mask = (
                spoof_orders_sorted_submitted.loc[:, AlertColumnsEnum.SPOOF_ORDERS_MID_PRICE]
                > spoof_orders_sorted_submitted.loc[:, AlertColumnsEnum.REAL_ORDERS_MID_PRICE]
            )
            sell_mask = (
                spoof_orders_sorted_submitted.loc[:, AlertColumnsEnum.SPOOF_ORDERS_MID_PRICE]
                < spoof_orders_sorted_submitted.loc[:, AlertColumnsEnum.REAL_ORDERS_MID_PRICE]
            )

            price_improvement = False

            if (
                real_orders_buy_sell_ind == BuySell.BUY
                and not spoof_orders_sorted_submitted.loc[buy_mask].empty
            ) or (
                real_orders_buy_sell_ind == BuySell.SELL
                and not spoof_orders_sorted_submitted.loc[sell_mask].empty
            ):
                price_improvement = True

            if self._th_price_improvement and not price_improvement:
                continue

            spoof_orders_sorted_submitted.loc[:, AlertColumnsEnum.PERCENTAGE_PRICE_IMPROVEMENT] = (
                spoof_orders_sorted_submitted.apply(
                    lambda x: self._calculate_percentage_price_improvement(
                        spoof_mid_price=x[AlertColumnsEnum.SPOOF_ORDERS_MID_PRICE],
                        real_mid_price=x[AlertColumnsEnum.REAL_ORDERS_MID_PRICE],
                    ),
                    axis=1,
                )
            )

            price_improvement_groups.append(spoof_orders_sorted_submitted)

        return price_improvement_groups

    def apply_time_to_cancel_filter(
        self, data: pd.DataFrame, method_id: Optional[str] = None
    ) -> pd.DataFrame:
        """Check if spoofing data is aligned with the Time To Cancel threshold.

        :param data: pd.Dataframe. dataframe with spoofing orders
        :param method_id: uuid to identify the method on the audit
        :return: pd.Dataframe: spoofing data filtered with time to cancel threshold
        """

        parent_ids: List[str] = data.loc[:, OrderField.META_ID].unique().tolist()

        # Fetch orders child (order state) from potential spoof orders
        spoofing_child: pd.DataFrame = self.queries.get_spoof_execs(parent_ids=parent_ids)

        if spoofing_child.empty:
            singleton_audit_object.write_audit_data_to_local_files(
                StepAudit(
                    step_id=method_id,
                    list_of_order_ids=data.get(OrderField.META_KEY, pd.Series()).tolist(),
                    reason="No order's executions retrieved for spoofing data.",
                )
            )
            self._logger.warning("No execution child for spoofing data retrieved earlier.")
            return pd.DataFrame()

        if OrderField.PC_FD_TRD_QTY not in spoofing_child.columns:
            spoofing_child[OrderField.PC_FD_TRD_QTY] = 0

        data[AlgoColumnsEnum.SPOOF_ORDER_STATES]: pd.DataFrame = data.apply(
            lambda x: spoofing_child.loc[
                x[OrderField.META_ID] == spoofing_child[OrderField.META_PARENT], :
            ],
            axis=1,
        )

        spoofing_data: pd.DataFrame = data.dropna(subset=[AlgoColumnsEnum.SPOOF_ORDER_STATES])

        if len(spoofing_data) < len(data):
            singleton_audit_object.write_audit_data_to_local_files(
                StepAudit(
                    step_id=method_id,
                    list_of_order_ids=list(
                        set(data.get(OrderField.META_KEY, pd.Series()).tolist())
                        - set(spoofing_data.get(OrderField.META_KEY, pd.Series()).tolist())
                    ),
                    reason="Dropped orders that do not have child executions.",
                )
            )
        if spoofing_data.empty:
            self._logger.warning("No alignment between spoofing child & spoofing orders")
            return pd.DataFrame()

        spoofing_data_timestamp: pd.DataFrame = self._filter_relevant_timestamps(
            data=spoofing_data, method_id=method_id
        )

        return spoofing_data_timestamp

    def _filter_relevant_timestamps(
        self, data: pd.DataFrame, method_id: Optional[str] = None
    ) -> pd.DataFrame:
        """
        DEFINE:
            vOrderCancelTime = Take the [Order Time Updated] where the Order Event is a Cancellation
            vOrderEntryTime = Retrieve the [Order Time Submitted] where the Order Event is a New’s-(New-Orders)
            vOrderTimeToCancel =  vOrderCancelTime  - vOrderEntryTime

        Filter spoofing data by relevant timestamps and return data that aligns with this threshold:
            vOrderTimeToCancel < t.[Spoof Order Time To Cancel] then: drop order
        :param data: pd.DataFrame, spoofing data
        :param method_id: uuid to identify the method on the audit
        :return: pd.DataFrame
        """
        spoofing_data: pd.DataFrame = data.copy()

        if OrderField.TS_ORD_SUBMITTED not in spoofing_data.columns:
            singleton_audit_object.write_audit_data_to_local_files(
                StepAudit(
                    step_id=method_id,
                    list_of_order_ids=data.get(OrderField.META_KEY, pd.Series()).tolist(),
                    reason=f"The required column {OrderField.TS_ORD_SUBMITTED} to calculated "
                    f"{AlgoColumnsEnum.ORDER_ENTRY_TIME} is missing from the spoofing data.",
                )
            )
            self._logger.warning(
                f"Spoofing data doesn't have col {OrderField.TS_ORD_SUBMITTED} which is necessary to calculate {AlgoColumnsEnum.ORDER_ENTRY_TIME}",
                exc_info=True,
            )
            return pd.DataFrame()

        spoofing_data.loc[:, AlgoColumnsEnum.ORDER_ENTRY_TIME] = data.loc[
            :, OrderField.TS_ORD_SUBMITTED
        ]

        spoofing_data[AlgoColumnsEnum.ORDER_CANCEL_TIME]: pd.Timestamp = spoofing_data.apply(
            lambda x: self._select_came_ts(x[AlgoColumnsEnum.SPOOF_ORDER_STATES]),
            axis=1,
        )

        spoofing_data[AlertColumnsEnum.SPOOF_ORDERS_EXECUTION_QUANTITY]: int = spoofing_data.apply(
            lambda x: x[AlgoColumnsEnum.SPOOF_ORDER_STATES]
            .loc[
                x[OrderField.META_ID]
                == x[AlgoColumnsEnum.SPOOF_ORDER_STATES][OrderField.META_PARENT],
                OrderField.PC_FD_TRD_QTY,
            ]
            .sum(),
            axis=1,
        )

        dropped = spoofing_data.get(OrderField.META_KEY, pd.Series()).tolist()

        spoofing_data: pd.DataFrame = spoofing_data.dropna(
            subset=[AlgoColumnsEnum.ORDER_CANCEL_TIME, AlgoColumnsEnum.ORDER_ENTRY_TIME]
        )

        dropped = set(dropped) - set(spoofing_data.get(OrderField.META_KEY, pd.Series()).tolist())

        if dropped:
            singleton_audit_object.write_audit_data_to_local_files(
                StepAudit(
                    step_id=method_id,
                    list_of_order_ids=list(dropped),
                    reason=f"Removed orders with empty values for fields {AlgoColumnsEnum.ORDER_CANCEL_TIME} and "
                    f"{AlgoColumnsEnum.ORDER_ENTRY_TIME}.",
                )
            )

        if spoofing_data.empty:
            self._logger.warning(
                "Spoofing data is empty after remove NaN from cols ORDER_CANCEL_TIME and ORDER_ENTRY_TIME",
                exc_info=True,
            )
            return pd.DataFrame()

        spoofing_data[AlgoColumnsEnum.ORDER_TIME_TO_CANCEL] = pd.to_datetime(
            spoofing_data.loc[:, AlgoColumnsEnum.ORDER_CANCEL_TIME], format="mixed"
        ) - pd.to_datetime(spoofing_data.loc[:, AlgoColumnsEnum.ORDER_ENTRY_TIME], format="mixed")

        order_time_to_cancel_mask: pd.Series = spoofing_data[
            AlgoColumnsEnum.ORDER_TIME_TO_CANCEL
        ] <= pd.Timedelta(self._th_spoof_order_time_to_cancellation, unit="seconds")

        dropped = spoofing_data.get(OrderField.META_KEY, pd.Series()).tolist()

        spoofing_data: pd.DataFrame = spoofing_data.loc[order_time_to_cancel_mask]

        dropped = set(dropped) - set(spoofing_data.get(OrderField.META_KEY, pd.Series()).tolist())

        if dropped:
            singleton_audit_object.write_audit_data_to_local_files(
                StepAudit(
                    step_id=method_id,
                    list_of_order_ids=list(dropped),
                    reason=f"Orders dropped after applying threshold {ThresholdsNames.SPOOF_ORDER_TIME_TO_CANCEL} of "
                    f"{self._th_spoof_order_time_to_cancellation} seconds. This is step 3.",
                )
            )
        return spoofing_data

    def _apply_spoofing_time_window(
        self, real_orders_df: pd.DataFrame, spoof_orders_sub_ts: pd.Timestamp
    ) -> pd.DataFrame:
        """Checks if the real order was submitted inside the selected spoofing
        threshold.

        :param real_orders_df: dataframe with real orders
        :param spoof_orders_sub_ts: timedelta of the spoofing order
        :return: dataframe with filtered real orders
        """

        timedelta_threshold: datetime.timedelta = datetime.timedelta(
            seconds=self._th_spoofing_time_window
        )
        real_orders_df[OrderField.TS_ORD_SUBMITTED] = pd.to_datetime(
            real_orders_df[OrderField.TS_ORD_SUBMITTED], format="mixed"
        )
        mask: pd.Series = (
            real_orders_df[OrderField.TS_ORD_SUBMITTED]
            <= (spoof_orders_sub_ts + timedelta_threshold)
        ) & (
            real_orders_df[OrderField.TS_ORD_SUBMITTED]
            >= (spoof_orders_sub_ts - timedelta_threshold)
        )

        real_orders_df: pd.DataFrame = real_orders_df.loc[mask]

        if real_orders_df.empty:
            return pd.NaT

        return real_orders_df

    def step_6_get_market_data(self, ric: str, dates: dict) -> pd.DataFrame:
        pass

    def step_6_7_get_params_at_level_apply_threshold(
        self,
        market_data: pd.DataFrame,
        tenant_data: pd.DataFrame,
        start_time_of_method: str,
        number_of_dropped_records: int,
        groupings_no: int,
        ric: str,
    ) -> Tuple[pd.DataFrame, int]:
        pass

    def step_7_apply_percentage_level_threshold(
        self,
        tenant_data_with_level: pd.DataFrame,
        percentage_threshold: int,
        groupings_no: int,
        number_of_dropped_records: int,
    ) -> Tuple[pd.DataFrame, int]:
        """Apply the percentage level threshold.

        :param number_of_dropped_records:
        :param groupings_no:
        :param tenant_data_with_level:
        :param percentage_threshold:
        :return:
        """
        start_time_of_method = datetime.datetime.now(datetime.timezone.utc).strftime(
            DATETIME_FORMAT
        )

        if any(
            tenant_data_with_level.loc[:, AlertColumnsEnum.PERCENTAGE_LEVEL] > percentage_threshold
        ):
            end, method_id = self.get_start_time_and_unique_id()
            singleton_audit_object.write_audit_data_to_local_files(
                StepAudit(
                    step_id=method_id,
                    list_of_order_ids=[],
                    reason="Valid records with spoofing data and level parameters",
                )
            )
            singleton_audit_object.write_audit_data_to_local_files(
                AggregatedStepAudit(
                    aggregated_step_id=method_id,
                    start=start_time_of_method,
                    end=end,
                    number_of_dropped_orders=0,
                    number_of_input_orders=len(tenant_data_with_level),
                    number_of_resulting_orders=len(tenant_data_with_level),
                    step_name="Apply percentage level threshold",
                    step_type=StepType.THRESHOLD_CALCULATION,
                    groupings=groupings_no,
                )
            )
            self.START_TIME = end
            return tenant_data_with_level, number_of_dropped_records

        end, method_id = self.get_start_time_and_unique_id()

        number_of_dropped_records += 1
        singleton_audit_object.write_audit_data_to_local_files(
            StepAudit(
                step_id=method_id,
                list_of_order_ids=tenant_data_with_level.get(
                    OrderField.META_KEY, pd.Series()
                ).tolist(),
                reason=f"Didn't passed the check for the percentage level threshold."
                f" Threshold value: {percentage_threshold}.",
            )
        )
        singleton_audit_object.write_audit_data_to_local_files(
            AggregatedStepAudit(
                aggregated_step_id=method_id,
                start=start_time_of_method,
                end=end,
                number_of_dropped_orders=len(tenant_data_with_level),
                number_of_input_orders=len(tenant_data_with_level),
                number_of_resulting_orders=0,
                step_name="Apply percentage level threshold.",
                step_type=StepType.THRESHOLD_CALCULATION,
                drop_reason=DropReason.RECORDS_DROPPED_THRESHOLD_FILTERING,
                groupings=groupings_no,
                groupings_dropped=number_of_dropped_records,
            )
        )
        self.START_TIME = end
        return pd.DataFrame(), number_of_dropped_records

    def step_16_price_improvement(self):
        pass

    def enrich_valid_alert(self, data: pd.DataFrame, alert_cals: addict.Dict) -> pd.DataFrame:
        """Enrich alerts with spoofing data.

        :param data: dataframe w alerts
        :param alert_cals: dict w alert calcs
        :return:
        """

        enriched_data = data.copy()

        enriched_data[AlertColumnsEnum.NUMBER_SPOOF_ORDERS] = alert_cals.COUNT_OF_SPOOF_ORDERS
        enriched_data[AlertColumnsEnum.NUMBER_REAL_ORDERS] = alert_cals.COUNT_OF_REAL_ORDERS
        enriched_data[AlertColumnsEnum.SPOOF_ORDER_SIDE] = alert_cals.SPOOF_ORDER_SIDE
        enriched_data[AlertColumnsEnum.SPOOF_ORDERS_QUANTITY] = (
            alert_cals.SUM_OF_SPOOF_ORDER_QUANTITY
        )
        enriched_data[AlertColumnsEnum.SPOOF_ORDERS_EXECUTION_QUANTITY] = (
            alert_cals.SUM_OF_SPOOF_EXECUTED_QUANTITY
        )
        enriched_data[AlertColumnsEnum.REAL_ORDERS_QUANTITY] = alert_cals.SUM_OF_REAL_ORDER_QUANTITY
        enriched_data[AlertColumnsEnum.REAL_ORDERS_EXECUTION_QUANTITY] = (
            alert_cals.SUM_OF_REAL_EXECUTED_QUANTITY
        )

        enriched_data.loc[:, AlertColumnsEnum.ORDERS_RATIO] = self._calculate_order_ratio(
            data=enriched_data
        )

        return enriched_data

    def _create_alert(self, alert_data: pd.DataFrame) -> NoReturn:
        """Create an alert to be showed in the front end.

        :param alert_data: pd.Dataframe, valid alert
        :return:
        """
        alert: dict = alert_data.to_dict(orient="list")
        earliest_order = alert_data.iloc[0]

        alert[AlertColumnsEnum.INSTRUMENT_NAME] = get_unique_value_for_alert(
            data=alert_data, column_name=OrderField.INST_FULL_NAME
        )

        alert[AlertColumnsEnum.ISIN] = get_instrument_id_code(data=alert_data)

        alert[AlertColumnsEnum.VENUE_LIST] = get_venue_name(data=alert_data)

        alert[AlertColumnsEnum.EARLIEST_TIMESTAMP] = (
            earliest_order[OrderField.TS_ORD_SUBMITTED]
            if OrderField.TS_ORD_SUBMITTED in alert_data.columns
            else pd.NA
        )

        alert[AlertColumnsEnum.SPOOF_ORDER_SIDE] = get_unique_value_for_alert(
            data=alert_data, column_name=AlertColumnsEnum.SPOOF_ORDER_SIDE
        )

        alert[AlertColumnsEnum.NUMBER_SPOOF_ORDERS] = get_unique_value_for_alert(
            data=alert_data, column_name=AlertColumnsEnum.NUMBER_SPOOF_ORDERS
        )

        alert[AlertColumnsEnum.SPOOF_ORDERS_QUANTITY] = get_unique_value_for_alert(
            data=alert_data, column_name=AlertColumnsEnum.SPOOF_ORDERS_QUANTITY
        )

        alert[AlertColumnsEnum.SPOOF_ORDERS_EXECUTION_QUANTITY] = get_unique_value_for_alert(
            data=alert_data,
            column_name=AlertColumnsEnum.SPOOF_ORDERS_EXECUTION_QUANTITY,
        )

        alert[AlertColumnsEnum.SPOOF_ORDER_TIME_CANCEL] = self._get_average_of_spoof_time_cancel(
            data=alert_data,
        )

        alert[AlertColumnsEnum.PERCENTAGE_PRICE_IMPROVEMENT] = get_unique_value_for_alert(
            data=alert_data, column_name=AlertColumnsEnum.PERCENTAGE_PRICE_IMPROVEMENT
        )

        alert[AlertColumnsEnum.NUMBER_REAL_ORDERS] = get_unique_value_for_alert(
            data=alert_data, column_name=AlertColumnsEnum.NUMBER_REAL_ORDERS
        )

        alert[AlertColumnsEnum.REAL_ORDERS_QUANTITY] = get_unique_value_for_alert(
            data=alert_data, column_name=AlertColumnsEnum.REAL_ORDERS_QUANTITY
        )

        alert[AlertColumnsEnum.REAL_ORDERS_EXECUTION_QUANTITY] = get_unique_value_for_alert(
            data=alert_data, column_name=AlertColumnsEnum.REAL_ORDERS_EXECUTION_QUANTITY
        )

        alert[AlertColumnsEnum.ORDERS_RATIO] = get_unique_value_for_alert(
            data=alert_data, column_name=AlertColumnsEnum.ORDERS_RATIO
        )

        alert[AlertColumnsEnum.INVOLVED_CLIENTS] = get_client_name(data=alert_data)

        alert[AlertColumnsEnum.EVALUATION_TYPE] = self.evaluation_type.value

        if self.evaluation_type == EvaluationTypeEnum.CLIENT:
            alert[AlertColumnsEnum.EVALUATION_ID] = alert[AlertColumnsEnum.INVOLVED_CLIENTS]
        else:
            alert[AlertColumnsEnum.EVALUATION_ID] = get_unique_value_for_alert(
                data=alert_data, column_name=self._th_evaluation_type
            )

        alert[AlertColumnsEnum.INVOLVED_DESKS] = get_unique_value_for_alert(
            data=alert_data, column_name=OrderField.TRD_ALGO_FIRM_DESKS_NAME
        )

        alert[AlertColumnsEnum.INVOLVED_PORTFOLIO_MANAGERS] = get_unique_value_for_alert(
            data=alert_data, column_name=OrderField.TRD_ALGO_FILE_IDENTIFIER
        )

        alert[AlertColumnsEnum.INVOLVED_TRADERS] = get_unique_value_for_alert(
            data=alert_data, column_name=OrderField.TRADER_FILE_IDENTIFIER
        )

        alert[AlertColumnsEnum.ORDER_ID_LIST] = (
            alert_data.loc[:, OrderField.ORD_IDENT_ID_CODE].unique().tolist()
            if OrderField.ORD_IDENT_ID_CODE in alert_data.columns
            else pd.NA
        )

        alert[AlertColumnsEnum.ORDER_STATUS_LIST]: List = get_order_status_for_ids(
            data=alert_data,
            order_ids=alert_data.loc[:, OrderField.META_KEY].unique().tolist(),
        )

        alert[AlertColumnsEnum.PRICE_LEVEL] = alert_data.loc[
            :, AlgoColumnsEnum.LEVEL_OF_ORDER_BOOK
        ].tolist()

        alert[AlertColumnsEnum.PRICE_LIST] = (
            alert_data.loc[:, OrderField.PC_FD_PRICE].tolist()
            if OrderField.PC_FD_PRICE in alert_data.columns
            else pd.NA
        )

        alert[AlertColumnsEnum.TRADED_QUANTITY_LIST] = (
            alert_data.loc[:, OrderField.PC_FD_TRD_QTY].tolist()
            if OrderField.PC_FD_TRD_QTY in alert_data.columns
            else pd.NA
        )
        alert[AlertColumnsEnum.TRANSACTION_NATIVE_VOLUME_LIST] = (
            alert_data.loc[:, OrderField.BEST_EXC_DATA_TRX_VOL_NATIVE].tolist()
            if OrderField.BEST_EXC_DATA_TRX_VOL_NATIVE in alert_data.columns
            else pd.NA
        )

        parent_id_list = []
        if AlgoColumnsEnum.REAL_ORDERS in alert.keys():
            for real_orders_df in alert.get(AlgoColumnsEnum.REAL_ORDERS):
                if OrderField.META_ID not in real_orders_df.columns:
                    continue
                ids = real_orders_df.loc[:, OrderField.META_ID].dropna().unique().tolist()
                parent_id_list.extend(ids)
            parent_id_list = list(set(parent_id_list))

        if OrderField.META_ID in alert_data.columns:
            fake_meta_order_ids_list = alert_data.loc[:, OrderField.META_ID].unique().tolist()
            parent_id_list.extend(fake_meta_order_ids_list)

        if len(parent_id_list) > 0:
            alert[AlertColumnsEnum.ORDERS_STATE_KEYS] = self.queries.get_newo_child(
                parent_ids=parent_id_list,
                required_fields=[OrderField.META_KEY],
            )

        alert[AlertColumnsEnum.FAKE_ORDERS] = get_unique_value_for_alert(
            data=alert_data, column_name=OrderField.META_KEY
        )

        list_related_real_orders = []
        if AlgoColumnsEnum.REAL_ORDERS in alert.keys():
            real_orders = alert.get(AlgoColumnsEnum.REAL_ORDERS)

            alert[AlgoColumnsEnum.REAL_ORDERS] = self._get_real_orders_keys(
                alert.get(AlgoColumnsEnum.REAL_ORDERS)
            )

            for real_orders_df in real_orders:
                related_real_orders = real_orders_df[
                    real_orders_df[CrossProductColumns.RELATED_INSTRUMENT] == True
                ]

                if not related_real_orders.empty:
                    related_real_orders = related_real_orders[
                        related_real_orders[CrossProductColumns.ORIGINAL_RECORD_METAKEY].isin(
                            alert_data[OrderField.META_KEY].unique().tolist()
                        )
                    ]

                list_related_real_orders.append(related_real_orders)

        alert[AlgoColumnsEnum.RIC.lower()] = None
        if AlgoColumnsEnum.RIC in alert_data.columns:
            alert[AlgoColumnsEnum.RIC.lower()] = get_unique_value_for_alert(
                data=alert_data, column_name=AlgoColumnsEnum.RIC
            )

        result: dict = dict(
            (key, alert[key]) for key in AlertColumnsEnum.get_cols_to_alert() if key in alert
        )

        result[ScenarioFields.RELATED_ACTIVITY_ALERTED] = False
        result[ScenarioFields.RELATED_ACTIVITY_DETECTED] = False

        scenario = SpoofingV2AbstractAlert(result=result, context=self.context)

        all_related_real_orders = pd.concat(list_related_real_orders)
        if not all_related_real_orders.empty:
            # transform df into the dict result from run_checks
            cross_product_dict = generate_final_result_dict(
                records=all_related_real_orders, explode_records=False
            )

            self.cross_product.set_scenario(
                scenario=scenario, list_of_orders=list(cross_product_dict.keys())
            )

            related_activity: pd.DataFrame = self.cross_product.related_activity(
                related_records_dict=cross_product_dict
            )

            if related_activity.empty:
                self.cross_product.add_related_records_to_scenario(
                    all_related_orders_base=all_related_real_orders
                )

            enriched_scenario = self.cross_product.enrich_scenario(
                related_activity=related_activity
            )

            enriched_scenario.loc[ScenarioFields.ADDITIONAL_FIELDS][ScenarioFields.TOP_LEVEL][
                ScenarioFields.RELATED_ACTIVITY_ALERTED
            ] = True

            scenario._scenario = enriched_scenario

        self.scenarios.append(scenario)

    def _calculate_order_ratio(self, data: pd.DataFrame) -> pd.Series:
        """
        Calculate the % of price improvement
        :return: pd.Series with price improvement percentage calculated
        """
        data.loc[:, AlertColumnsEnum.ORDERS_RATIO] = data.apply(
            lambda row: calculate_percentage(
                first_variable=row[AlertColumnsEnum.SPOOF_ORDERS_QUANTITY],
                second_variable=row[AlertColumnsEnum.REAL_ORDERS_QUANTITY],
                number_of_orders=self._get_number_of_orders_dict(row=row),
            ),
            axis=1,
        )

        return data.loc[:, AlertColumnsEnum.ORDERS_RATIO]

    @staticmethod
    def _get_real_orders_keys(real_orders: List) -> List[List[str]]:
        """Get a list with dataframe and returns a list with string, that are
        the real orders keys.

        :param real_orders: List, list of dataframe rows with orders keys
        :return:  List of str with real orders keys
        """
        real_orders_keys: List[List[str]] = []

        for real_orders_df in real_orders:
            if OrderField.META_KEY not in real_orders_df.columns:
                continue
            keys = list(set(real_orders_df.loc[:, OrderField.META_KEY]))
            real_orders_keys.extend(keys)

        return list(set(real_orders_keys))

    @staticmethod
    def _select_came_ts(order_state_data: pd.DataFrame) -> pd.Timestamp:
        """Selects the timestamp of the cancellation.

        :param order_state_data: dataframe of spoof order states
        :return: timedelta of the came
        """
        mask: pd.Series = order_state_data[OrderField.EXC_DTL_ORD_STATUS].isin(
            [OrderStatus.CAME, OrderStatus.CAMO]
        )

        order_state_data: pd.DataFrame = order_state_data.loc[mask]

        if order_state_data.empty:
            return pd.NA

        return order_state_data.loc[:, OrderField.TS_ORD_UPDATED].tolist()[0]

    @staticmethod
    def _calculate_percentage_price_improvement(
        spoof_mid_price: float, real_mid_price: float
    ) -> float:
        """Calculate the % of price improvement.

        V1 = starting point = vLevel1MidPriceOfSpoofOrder
        V2 = end point = vLevel1MidPriceOfRealOrder

        if V1 = V2 then:
            vPriceImprovementPercentage  = 0

        if V1 < V2 then:
            vPriceImprovementPercentage  = (V1 - V2) / V1 * -1
            note: (-1 because it’s a decrease)

        if V1 > V2 then:
            vPriceImprovementPercentage  = (V2- V1) / V1

        :param spoof_mid_price: float, vLevelMidPriceOfSpoofOrder
        :param real_mid_price: float, vLevelMidPriceOfRealOrder
        :return:
        """
        price_movement: Union[float, None] = None

        if spoof_mid_price == real_mid_price:
            price_movement = 0.0

        if spoof_mid_price < real_mid_price:
            price_movement = abs((spoof_mid_price - real_mid_price) / spoof_mid_price)

        if spoof_mid_price > real_mid_price:
            price_movement = (real_mid_price - spoof_mid_price) / spoof_mid_price

        return price_movement

    @staticmethod
    def _calculations_spoof_real_orders(valid_alert_data: pd.DataFrame) -> addict.Dict:
        """List of calculations which will be used by the front-end to display
        the alert.

        for “Spoof Orders”, calculate
            countOfSpoofOrders = count the distinct number of [Order ID]'s
            sumOfSpoofOrderQuantity = sum the [Order Quantity] for each New’s-(New-Orders)
            sumOfSpoofExecutedQuantity = sum the [Fill Quantity] for each  Fill-/-Partial-FIlls-(Executions)
            spoofOrderSide = retrieve the [BuySell]

        for “RealOrders”, calculate
            countOfRealOrders = count the distinct number of [Order ID]'s
            sumOfRealOrderQuantity = sum the [Order Quantity] for each New’s-(New-Orders)
            sumOfRealExecutedQuantity = sum the [Fill Quantity] for each Fill-/-Partial-FIlls-(Executions)
        :return:
        """
        real_orders: pd.DataFrame = valid_alert_data.loc[:, AlgoColumnsEnum.REAL_ORDERS].values[0]

        count_of_spoof_orders: int = len(valid_alert_data.loc[:, OrderField.META_KEY].unique())
        count_of_real_orders: int = len(real_orders.loc[:, OrderField.META_KEY].unique())

        spoof_order_side: str = (
            valid_alert_data.loc[:, OrderField.EXC_DTL_BUY_SELL_IND].unique().tolist()[0]
        )

        sum_of_spoof_order_quantity: int = valid_alert_data.loc[
            valid_alert_data.loc[:, OrderField.EXC_DTL_ORD_STATUS] == OrderStatus.NEWO
        ][OrderField.PC_FD_INIT_QTY].sum()

        sum_of_spoof_executed_quantity: int = valid_alert_data.loc[
            valid_alert_data.loc[:, OrderField.EXC_DTL_ORD_STATUS].isin(
                [OrderStatus.FILL, OrderStatus.PARF]
            )
        ][OrderField.PC_FD_INIT_QTY].sum()

        sum_of_real_order_quantity: int = real_orders.loc[
            real_orders.loc[:, OrderField.EXC_DTL_ORD_STATUS] == OrderStatus.NEWO
        ][OrderField.PC_FD_INIT_QTY].sum()

        sum_of_real_executed_quantity: int = real_orders.loc[
            real_orders.loc[:, OrderField.EXC_DTL_ORD_STATUS].isin(
                [OrderStatus.FILL, OrderStatus.PARF]
            )
        ][OrderField.PC_FD_INIT_QTY].sum()

        return addict.Dict(
            COUNT_OF_SPOOF_ORDERS=count_of_spoof_orders,
            COUNT_OF_REAL_ORDERS=count_of_real_orders,
            SPOOF_ORDER_SIDE=spoof_order_side,
            SUM_OF_SPOOF_ORDER_QUANTITY=sum_of_spoof_order_quantity,
            SUM_OF_SPOOF_EXECUTED_QUANTITY=sum_of_spoof_executed_quantity,
            SUM_OF_REAL_ORDER_QUANTITY=sum_of_real_order_quantity,
            SUM_OF_REAL_EXECUTED_QUANTITY=sum_of_real_executed_quantity,
        )

    @staticmethod
    def _get_number_of_orders_dict(row: pd.Series) -> dict:
        """Get the number of orders by each type buy/sell orders.

        :param row: pd.Series with order data
        :return dict , with number of orders
        """
        number_of_orders_dict: dict = addict.Dict(
            SPOOF=row[AlertColumnsEnum.NUMBER_SPOOF_ORDERS],
            REAL=row[AlertColumnsEnum.NUMBER_REAL_ORDERS],
        )
        return number_of_orders_dict

    @staticmethod
    def _get_average_of_spoof_time_cancel(data: pd.DataFrame) -> List:
        """Get the number of orders by each type buy/sell orders.

        :param row: pd.Series with order data
        :return dict , with number of orders
        """
        if AlgoColumnsEnum.ORDER_TIME_TO_CANCEL in data.columns:
            data[AlertColumnsEnum.SPOOF_ORDER_TIME_CANCEL] = pd.to_timedelta(
                data.loc[:, AlgoColumnsEnum.ORDER_TIME_TO_CANCEL]
            ).mean()
        else:
            data[AlertColumnsEnum.SPOOF_ORDER_TIME_CANCEL] = pd.NaT

        data[AlertColumnsEnum.SPOOF_ORDER_TIME_CANCEL] = data[
            AlertColumnsEnum.SPOOF_ORDER_TIME_CANCEL
        ].apply(lambda x: str(x))

        return data[AlertColumnsEnum.SPOOF_ORDER_TIME_CANCEL].unique().tolist()
