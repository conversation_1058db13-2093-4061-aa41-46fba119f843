from enum import Enum
from market_abuse_algorithms.data_source.static.sdp.order import OrderField
from se_elastic_schema.components.mar.strategy.spoofing_v2.thresholds import (
    EvaluationTypeEnum,
)
from typing import List


class ThresholdsNames(str, Enum):
    EVALUATION_TYPE = "evaluationType"
    INCLUDE_PARTIALLY_CANCELLATIONS = "includePartCancellations"
    PRICE_IMPROVEMENT = "priceImprovement"
    REAL_ORDER_PERCENTAGE_FILLED = "realOrderPercentageFilled"
    SPOOF_ORDER_TIME_TO_CANCEL = "spoofOrderTimeToCancel"
    SPOOF_ORDER_PERCENTAGE_OF_LEVEL = "spoofOrderPercentageLevel"
    SPOOF_ORDER_PERCENTAGE_OF_LEVEL1 = "spoofOrderPercentageLevelL1"
    SPOOFING_TIME_WINDOW = "spoofingTimeWindow"


class AlertColumnsEnum:
    EARLIEST_TIMESTAMP = "earliestTimestamp"
    EVALUATION_ID = "evaluationID"
    EVALUATION_TYPE = "evaluationType"
    FAKE_ORDERS = "fakeOrders"
    INSTRUMENT_NAME = "instrumentName"
    INVOLVED_CLIENTS = "partiesClientList"
    INVOLVED_DESKS = "partiesDeskList"
    INVOLVED_PORTFOLIO_MANAGERS = "partiesInvestmentDecisionMakerList"
    INVOLVED_TRADERS = "partiesTraderList"
    ISIN = "isin"
    NUMBER_REAL_ORDERS = "numberOfRealOrders"
    NUMBER_SPOOF_ORDERS = "numberSpoofOrders"
    ORDERS = "orders"
    ORDER_ID_LIST = "orderIdList"
    ORDERS_RATIO = "ordersRatio"
    ORDERS_STATE_KEYS = "orderStateKeys"
    ORDER_STATUS_LIST = "orderStatusList"
    PERCENTAGE_LEVEL = "percentageLevel"
    PERCENTAGE_PRICE_IMPROVEMENT = "percentagePriceImprovement"
    PRICE_LEVEL = "priceLevel"
    PRICE_LIST = "priceList"
    REAL_ORDERS = "realOrders"
    REAL_ORDERS_EXECUTION_QUANTITY = "realOrdersExecutedQuantity"
    REAL_ORDERS_MID_PRICE = "realOrdersMidPrice"
    REAL_ORDERS_QUANTITY = "realOrdersQuantity"
    SPOOF_ORDERS_MID_PRICE = "spoofOrdersMidPrice"
    SPOOF_ORDER_SIDE = "spoofOrderSide"
    SPOOF_ORDER_TIME_CANCEL = "spoofOrderTimeToCancel"
    SPOOF_ORDERS_EXECUTION_QUANTITY = "spoofOrderExecutedQuantity"
    SPOOF_ORDERS_QUANTITY = "spoofOrderQuantity"
    VENUE_LIST = "venueList"
    VOLUME_LEVEL = "volumeLevel"
    TRADED_QUANTITY_LIST = "tradedQuantityList"
    TRANSACTION_NATIVE_VOLUME_LIST = "transactionVolumeList"

    @classmethod
    def get_cols_to_alert(cls) -> List[str]:
        return [
            cls.EARLIEST_TIMESTAMP,
            cls.EVALUATION_ID,
            cls.EVALUATION_TYPE,
            cls.FAKE_ORDERS,
            cls.INSTRUMENT_NAME,
            cls.INVOLVED_CLIENTS,
            cls.INVOLVED_DESKS,
            cls.INVOLVED_PORTFOLIO_MANAGERS,
            cls.INVOLVED_TRADERS,
            cls.ISIN,
            cls.NUMBER_REAL_ORDERS,
            cls.NUMBER_SPOOF_ORDERS,
            cls.ORDERS,
            cls.ORDER_ID_LIST,
            cls.ORDERS_RATIO,
            cls.ORDERS_STATE_KEYS,
            cls.ORDER_STATUS_LIST,
            cls.PERCENTAGE_LEVEL,
            cls.PERCENTAGE_PRICE_IMPROVEMENT,
            cls.PRICE_LEVEL,
            cls.PRICE_LIST,
            cls.REAL_ORDERS,
            cls.REAL_ORDERS_EXECUTION_QUANTITY,
            cls.REAL_ORDERS_MID_PRICE,
            cls.REAL_ORDERS_QUANTITY,
            cls.SPOOF_ORDERS_MID_PRICE,
            cls.SPOOF_ORDER_SIDE,
            cls.SPOOF_ORDER_TIME_CANCEL,
            cls.SPOOF_ORDERS_EXECUTION_QUANTITY,
            cls.SPOOF_ORDERS_QUANTITY,
            cls.VENUE_LIST,
            cls.VOLUME_LEVEL,
            cls.TRADED_QUANTITY_LIST,
            cls.TRANSACTION_NATIVE_VOLUME_LIST,
        ]


class AlgoColumnsEnum:
    LEVEL_OF_ORDER_BOOK = "levelOfOrderBook"
    ORDER_CANCEL_TIME = "orderCancelTime"
    ORDER_ENTRY_TIME = "orderEntryTime"
    ORDER_TIME_TO_CANCEL = "orderTimeToCancel"
    REAL_ORDERS = "realOrders"
    REAL_ORDERS_FILLED_PERCENTAGE = "realOrdersFilledPercentage"
    SPOOF_ORDER_STATES = "spoofOrderStates"
    VENUE = "venue"
    TIMEDELTA = "timedelta"
    RIC = "RIC"


ORDER_TYPE_GROUP_MAP = {
    EvaluationTypeEnum.CLIENT: OrderField.CLIENT_FILE_IDENTIFIER,
    EvaluationTypeEnum.COUNTERPARTY: OrderField.COUNTERPARTY_ID,
    EvaluationTypeEnum.DESK: OrderField.TRD_ALGO_FIRM_DESKS_NAME,
    EvaluationTypeEnum.EXECUTING_ENTITY: OrderField.RPT_DTL_EXC_ENTITY_FILE_IDENTIFIER,
    EvaluationTypeEnum.PORTFOLIO_MANAGER: OrderField.TRD_ALGO_FILE_IDENTIFIER,
    EvaluationTypeEnum.TRADER: OrderField.TRADER_FILE_IDENTIFIER,
}
