from market_abuse_algorithms.strategy.base.currency import NotionalCurrency
from market_abuse_algorithms.strategy.base.models import CommonThresholds
from market_abuse_algorithms.strategy.marking_the_close.static import CloseType
from market_abuse_algorithms.strategy.marking_the_open.static import OpenType
from pydantic import Field
from typing import Union


class Thresholds(CommonThresholds):
    markingType: Union[CloseType, OpenType]
    lookBackPeriod: int = Field(..., ge=0, le=7200, description="In seconds")
    minimumNotional: int = Field(
        ...,
        ge=0,
        le=1000000,
    )
    minimumNotionalCurrency: NotionalCurrency = Field(...)
    client20DayAdv: float = Field(..., ge=0, le=1)
    market20DayAdv: float = Field(..., ge=0, le=1)
    priceSpike: float = Field(..., ge=0, le=1)
