import pandas as pd
from market_abuse_algorithms.data_source.static.sdp.order import NewColumns, OrderField
from market_abuse_algorithms.strategy.base.errors import StrategyError
from market_abuse_algorithms.strategy.base.models import StrategyContext
from market_abuse_algorithms.strategy.base.static import StrategyLog, StrategyName
from market_abuse_algorithms.strategy.base.strategy import AbstractStrategy
from market_abuse_algorithms.strategy.front_running.models import Thresholds
from market_abuse_algorithms.strategy.front_running.query import Queries
from market_abuse_algorithms.strategy.front_running.scenario import Scenario
from market_abuse_algorithms.strategy.front_running.static import DFColumns, ThresholdsNames
from typing import List


class Strategy(AbstractStrategy):
    """Front Running.

    Jira tickets for logic:
        - https://steeleye.atlassian.net/browse/PR-120
        - https://steeleye.atlassian.net/browse/PR-263

    Jira tickets for the front-end:
        - https://steeleye.atlassian.net/browse/PR-520
        - https://steeleye.atlassian.net/browse/PR-601
    """

    def __init__(self, context: StrategyContext):
        super().__init__(
            context=context,
            strategy_name=StrategyName.FRONT_RUNNING,
            thresholds_class=Thresholds,
            queries_class=Queries,
        )
        self._th_time_window = self.context.thresholds.dict().get(ThresholdsNames.TIME_WINDOW)

    def _apply_strategy(self):
        for data in self.queries.cases_to_analyse():
            self._apply_strategy_mini_batch(data=data)

    def _apply_strategy_mini_batch(self, data: pd.DataFrame):
        for instrument_id, group in data.groupby(NewColumns.INSTRUMENT_CODE):
            with StrategyError.handle_algo_records_error(audit=self._audit, data=group):
                self._logger.debug(
                    StrategyLog.TOP_LEVEL.format(info=f"Instrument: {instrument_id}")
                )

                self._run_algo(data=group)

    def _run_algo(self, data: pd.DataFrame):
        results = self._algo(data=data)

        if results.empty:
            return

        self._create_scenarios(df=results)

    def _algo(self, data: pd.DataFrame) -> pd.DataFrame:
        df = data.drop_duplicates()

        # TS last execution
        df[DFColumns.TS_LAST_EXECUTION] = df[OrderField.META_PARENT].apply(
            lambda x: data.loc[
                data[OrderField.META_PARENT] == x, OrderField.TS_TRADING_DATE_TIME
            ].max()
        )

        # Entry executions
        df[DFColumns.EXECUTIONS_ENTRY] = df.apply(
            lambda x: data.loc[
                (data[OrderField.META_PARENT] != x[OrderField.META_PARENT])
                & (data[OrderField.TS_ORD_SUBMITTED] > x[OrderField.TS_ORD_SUBMITTED])
                & (data[OrderField.TS_TRADING_DATE_TIME] < x[DFColumns.TS_LAST_EXECUTION])
                & (data[OrderField.CLIENT_FILE_IDENTIFIER] != x[OrderField.CLIENT_FILE_IDENTIFIER])
                & (data[OrderField.EXC_DTL_BUY_SELL_IND] == x[OrderField.EXC_DTL_BUY_SELL_IND]),
                OrderField.META_KEY,
            ]
            .unique()
            .tolist(),
            axis=1,
        )

        with_entry_executions_mask = df[DFColumns.EXECUTIONS_ENTRY].apply(len) > 0

        df = df.loc[with_entry_executions_mask]

        if df.empty:
            return pd.DataFrame()

        df[DFColumns.TS_LAST_EXECUTION_PLUS_TW] = df[DFColumns.TS_LAST_EXECUTION] + pd.Timedelta(
            seconds=self._th_time_window
        )

        # Exit Executions
        df[DFColumns.EXECUTIONS_EXIT] = df.apply(
            lambda row: self.get_exit_executions(row, data), axis=1
        )

        with_exit_executions_mask = df[DFColumns.EXECUTIONS_EXIT].apply(len) > 0

        df = df.loc[with_exit_executions_mask]

        if df.empty:
            return pd.DataFrame()

        # New orders ids
        df[DFColumns.NEW_ORDERS_IDS] = df[
            [DFColumns.EXECUTIONS_ENTRY, OrderField.META_PARENT]
        ].apply(
            lambda x: data.loc[
                data[OrderField.META_KEY].isin(x[DFColumns.EXECUTIONS_ENTRY]),
                OrderField.META_PARENT,
            ]
            .unique()
            .tolist()
            + [x[OrderField.META_PARENT]],
            axis=1,
        )

        # Time Difference
        df[DFColumns.TIME_DIFFERENCE] = df[
            [DFColumns.EXECUTIONS_ENTRY, DFColumns.TS_LAST_EXECUTION]
        ].apply(
            lambda x: (
                x[DFColumns.TS_LAST_EXECUTION]
                - data.loc[
                    data[OrderField.META_KEY].isin(x[DFColumns.EXECUTIONS_ENTRY]),
                    OrderField.TS_TRADING_DATE_TIME,
                ].min()
            ).total_seconds(),
            axis=1,
        )

        df[DFColumns.EXECUTIONS_ENTRY] = df[
            [DFColumns.EXECUTIONS_ENTRY, OrderField.META_KEY]
        ].apply(lambda row: row[DFColumns.EXECUTIONS_ENTRY] + [row[OrderField.META_KEY]], axis=1)

        return df

    def _create_scenarios(self, df: pd.DataFrame):
        parent_ids = df[OrderField.META_PARENT].unique().tolist()

        additional_fields = self.queries.get_additional_fields_for_scenarios(ids=parent_ids)

        additional_fields = additional_fields.reset_index()

        df = pd.merge(
            df,
            additional_fields,
            left_on=OrderField.META_PARENT,
            right_on=OrderField.META_ID,
        )
        df = df.rename(columns={"&key_x": OrderField.META_KEY, "&key_y": OrderField.META_KEY})

        new_orders_ids = df[DFColumns.NEW_ORDERS_IDS].explode().drop_duplicates().unique().tolist()

        # hack for new orders keys
        id_keys_map = self.queries.get_orders_keys_map(new_orders_ids)

        df[DFColumns.NEW_ORDERS] = df[DFColumns.NEW_ORDERS_IDS].apply(
            lambda x: list(map(lambda y: id_keys_map.get(y), x))
        )

        # TODO: involved parties
        df[DFColumns.INVOLVED_PARTIES] = None

        if NewColumns.TRADER_NAME in df.columns:
            df[DFColumns.INVOLVED_TRADERS] = df[NewColumns.TRADER_NAME]

        if OrderField.CLIENT_FILE_IDENTIFIER in df.columns:
            df[DFColumns.INVOLVED_CLIENTS] = df[OrderField.CLIENT_FILE_IDENTIFIER]

        if OrderField.TRX_DTL_TR_CAPACITY in df.columns:
            df[DFColumns.TRADING_CAPACITIES] = df[OrderField.TRX_DTL_TR_CAPACITY]

        fields_to_scenario_map = {
            DFColumns.NEW_ORDERS: DFColumns.NEW_ORDERS,
            DFColumns.EXECUTIONS_ENTRY: DFColumns.EXECUTIONS_ENTRY,
            DFColumns.EXECUTIONS_EXIT: DFColumns.EXECUTIONS_EXIT,
            OrderField.META_KEY: DFColumns.ORDER,
            OrderField.INST_FULL_NAME: "instrumentFullName",
            DFColumns.TIME_DIFFERENCE: DFColumns.TIME_DIFFERENCE,
            DFColumns.INVOLVED_PARTIES: DFColumns.INVOLVED_PARTIES,
            DFColumns.INVOLVED_TRADERS: DFColumns.INVOLVED_TRADERS,
            DFColumns.INVOLVED_CLIENTS: DFColumns.INVOLVED_CLIENTS,
            DFColumns.TRADING_CAPACITIES: DFColumns.TRADING_CAPACITIES,
        }

        df = df.loc[:, df.columns.isin(fields_to_scenario_map.keys())]

        df.columns = df.columns.map(fields_to_scenario_map)

        results = df.to_dict(orient="records")

        for result in results:
            scenario = Scenario(result=result, context=self.context)
            self.scenarios.append(scenario)

    def get_exit_executions(self, row: pd.Series, data: pd.DataFrame) -> List[str]:
        """
        Fetched the exit executions
        :param row: each row of front run orders
        :param data: all record being analyzed
        :return: list of ex
        """

        entry_executions = data[data[OrderField.META_KEY].isin(row[DFColumns.EXECUTIONS_ENTRY])]

        entry_executions_client_file_identifiers = list(
            entry_executions.loc[:, OrderField.CLIENT_FILE_IDENTIFIER].unique()
        )

        exit_executions_metakeys: List[str] = (
            data.loc[
                (data[OrderField.EXC_DTL_BUY_SELL_IND] != row[OrderField.EXC_DTL_BUY_SELL_IND])
                & (
                    data[OrderField.CLIENT_FILE_IDENTIFIER].isin(
                        entry_executions_client_file_identifiers
                    )
                )
                & (
                    data[OrderField.TS_ORD_SUBMITTED]
                    > row[
                        DFColumns.TS_LAST_EXECUTION
                    ]  # in here we go directly to the "main" order to get the timestamps
                )
                & (
                    data[OrderField.TS_TRADING_DATE_TIME]
                    > row[
                        DFColumns.TS_LAST_EXECUTION
                    ]  # in here we go directly to the "main" order to get the timestamps
                )
                & (
                    data[OrderField.TS_TRADING_DATE_TIME]
                    < row[
                        DFColumns.TS_LAST_EXECUTION_PLUS_TW
                    ]  # in here we go directly to the "main" order to get the timestamps
                ),
                OrderField.META_KEY,
            ]
            .unique()
            .tolist()
        )

        return exit_executions_metakeys
