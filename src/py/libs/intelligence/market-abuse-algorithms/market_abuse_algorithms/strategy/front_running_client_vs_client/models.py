from market_abuse_algorithms.strategy.base.models import CommonThresholds, TimeUnit
from market_abuse_algorithms.strategy.front_running_client_vs_client.static import (
    EvaluationType,
)
from pydantic import Field, validator
from pydantic.dataclasses import dataclass
from typing import Dict, Optional, Union


@dataclass
class TimeWindow:
    """Class with validations regarding the TimeWindow threshold."""

    unit: TimeUnit
    value: int = Field(..., ge=0, le=36000)

    @validator("unit")
    def check_unit(cls, v: TimeUnit) -> TimeUnit:
        """Validation for the unit of the time window.

        :param v: unit parameter
        :return:
        """
        valid_units = [TimeUnit.SECONDS, TimeUnit.MINUTES]
        if v not in valid_units:
            raise ValueError(f"`{v}` not valid unit for this strategy: {valid_units}")

        return v

    @validator("value")
    def check_value(cls, v: int, values: Dict[str, Union[int, TimeUnit]]) -> int:
        """Validates that the timedelta is between the lower and upper bounds.

        :param v: amount of time units
        :param values: dictionary with timewindow
        :return:
        """
        unit = values.get("unit")

        upper_bound_seconds = 36000

        upper_bounds = {
            TimeUnit.SECONDS: upper_bound_seconds,
            TimeUnit.MINUTES: upper_bound_seconds / 60,
        }

        upper_bound = upper_bounds.get(unit)

        if v < 0 or v > upper_bound:
            raise ValueError(f"Failed check 0 < {v} (value) < {upper_bound}. Unit: {unit}")

        return v


class Thresholds(CommonThresholds):
    evaluationType: EvaluationType
    frontRunningVolumeDifference: float = Field(..., ge=0.01, le=1)
    frontRunOrderVolume: Optional[float] = Field(None, ge=0, le=10000000)
    timeWindow: TimeWindow
