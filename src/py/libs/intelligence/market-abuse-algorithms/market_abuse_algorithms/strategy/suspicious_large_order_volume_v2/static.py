from enum import Enum
from market_abuse_algorithms.data_source.static.sdp.order import OrderField
from se_market_data_utils.schema.parquet import QuoteTickColumns, TradeTickColumns
from se_market_data_utils.schema.refinitiv import RefinitivEventType, RefinitivExtractColumns
from typing import List


class BaseStrEnum(str, Enum):
    @classmethod
    def get_values(cls, remove_type_all: bool = False) -> List[str]:
        values = [v for k, v in vars(cls).items() if k.isupper()]

        if remove_type_all:
            values.remove(cls.ALL)

        return values


class CategoryEvaluationType(BaseStrEnum):
    INTERNAL_FLOW = "internalFlow"
    MARKET = "market"


class DayAndOrderEvaluationType(BaseStrEnum):
    ORDER = "order"
    PARENT_ORDER = "parentOrder"
    DAY = "day"


class DFColumns:
    ADV = "adv"
    ADV_DAY = "adtv_day"
    ADV_PERCENTAGE = "advPercentage"
    AVERAGE_DAILY_TRADING_VOLUME = "averageDailyTradingVolume"
    AVERAGE_DAILY_TRADING_VOLUME_TEMP = "averageDailyTradingVolumeTemp"
    CLIENT_NAME = "clientList"
    COUNTERPARTY_NAME_LIST = "counterpartyList"
    DATE = "Date"
    DESK_NAME_LIST = "deskList"
    EARLIEST_TIMESTAMP = "earliestOrderTimestamp"
    EXECUTIONS_DETECTED = "executionsDetected"
    EVALUATION_TYPE = "evaluationType"
    EVALUATION_ID = "evaluationID"
    INITIAL_QUANTITY = "totalOrderQuantity"
    INSTRUMENT_NAME_LIST = "instrumentNameList"
    INSTRUMENT_DETECTED = "instrumentDetected"
    MARKET_AVG_DAILY_VOLUME = "marketAverageDailyVolume"
    MARKET_INTRA_DAY_VOLUME = "marketIntraDayVolume"
    MARKET_IMPACT_PERCENTAGE_PRICE = "marketImpactPercentagePrice"
    MARKET_IMPACT_PERCENTAGE_PRICE_ABS = "marketImpactPercentagePriceAbs"
    MARKET_PRICE_IMPROVEMENT = "marketPriceImprovement"
    NEAREST_MID_BEFORE = "nearestMidBefore"
    NEAREST_MID_AFTER = "nearestMidAfter"
    META_PARENT_META_KEY = "parentMetaKey"
    ORDERS_KEYS = "ordersKeys"
    ORDER_STATES_KEYS = "orderStatesKeys"
    ORDER_DETECTED = "ordersDetected"
    RIC = "RIC"
    TIMEDELTA = "timedelta"
    TRADER_NAME_LIST = "traderList"
    TRADED_QUANTITY = "tradedOrderQuantity"

    @classmethod
    def get_scenario_fields(cls):
        return [
            cls.ADV,
            cls.ADV_PERCENTAGE,
            cls.CLIENT_NAME,
            cls.COUNTERPARTY_NAME_LIST,
            cls.DATE,
            cls.DESK_NAME_LIST,
            cls.EARLIEST_TIMESTAMP,
            cls.EVALUATION_TYPE,
            cls.EVALUATION_ID,
            cls.INITIAL_QUANTITY,
            cls.INSTRUMENT_NAME_LIST,
            cls.INSTRUMENT_DETECTED,
            cls.ORDERS_KEYS,
            cls.ORDER_DETECTED,
            cls.MARKET_PRICE_IMPROVEMENT,
            cls.MARKET_IMPACT_PERCENTAGE_PRICE_ABS,
            cls.MARKET_IMPACT_PERCENTAGE_PRICE,
            cls.TRADER_NAME_LIST,
            cls.TRADED_QUANTITY,
        ]


class EventType(BaseStrEnum):
    NEWOS = "NEWOS"
    EXECUTIONS = "EXECUTIONS"


class GeneralEvaluationType(BaseStrEnum):
    CLIENT = "client"
    COUNTERPARTY = "counterparty"
    DESK = "desk"
    EXECUTING_ENTITY = "executingEntity"
    PORTFOLIO_MANAGER = "portfolioManager"
    TRADER = "trader"


class MarketDataEvaluationType(BaseStrEnum):
    AVERAGE_DAILY_TRADED_VOLUME = "averageDailyTradedVolume"
    DAY_TRADED_VOLUME = "dayTradedVolume"
    INTRA_DAY_TRADED_VOLUME = "intraDayTradedVolume"


class NormaliseBehaviour(BaseStrEnum):
    ASSET_CLASS = "assetClass"
    INSTRUMENT = "instrument"


class QuantityEvaluationType(BaseStrEnum):
    ORDER_QUANTITY = "orderQuantity"
    EXECUTED_QUANTITY = "executedQuantity"
    EXECUTED_NOTIONAL = "executedNotional"


class ThresholdsNames(str, Enum):
    CATEGORY_EVALUATION_TYPE = "categoryEvaluationType"
    DAY_AND_ORDER_EVALUATION_TYPE = "dayAndOrderEvaluationType"
    EXECUTION_NOTIONAL_VALUE_CURRENCY = "executionNotionalValueCurrency"
    GENERAL_EVALUATION_TYPE = "generalEvaluationType"
    LOOK_BACK_PERIOD = "lookBackPeriod"
    MARKET_DATA_EVALUATION_TYPE = "marketDataEvaluationType"
    MARKET_PRICE_IMPACT = "marketPriceImpact"
    MINIMUM_QUANTITY = "minimumQuantity"
    MIN_NUMBER_DAYS_ORDER_FLOW = "minNumberOfDaysOrderFlow"
    NORMALISE_BEHAVIOUR = "normaliseBehaviour"
    PERCENTAGE_ADV = "percentageAdv"
    QUANTITY_EVALUATION_TYPE = "quantityEvaluationType"


GENERAL_GROUPING_MAP = {
    GeneralEvaluationType.CLIENT: OrderField.CLIENT_FILE_IDENTIFIER,
    GeneralEvaluationType.COUNTERPARTY: OrderField.COUNTERPARTY_ID,
    GeneralEvaluationType.DESK: OrderField.TRD_ALGO_FIRM_DESKS_ID,
    GeneralEvaluationType.EXECUTING_ENTITY: OrderField.RPT_DTL_EXC_ENTITY_FILE_IDENTIFIER,
    GeneralEvaluationType.PORTFOLIO_MANAGER: OrderField.TRD_ALGO_FILE_IDENTIFIER,
    GeneralEvaluationType.TRADER: OrderField.TRADER_FILE_IDENTIFIER,
}

REFINITIV_MAP = {
    RefinitivEventType.QUOTE: {
        "DATE": RefinitivExtractColumns.DATE_TIME,
        "PRICE": QuoteTickColumns.MID_PRICE,
    },
    RefinitivEventType.TRADE: {
        "DATE": RefinitivExtractColumns.DATE_TIME,
        "PRICE": TradeTickColumns.PRICE,
    },
}
