from market_abuse_algorithms.strategy.base.models import CommonThresholds, TimeUnit
from market_abuse_algorithms.strategy.message_volume.static import GroupByEnum
from pydantic import Field, validator
from pydantic.dataclasses import dataclass
from se_elastic_schema.static.mifid2 import OrderStatus
from typing import List, Optional


@dataclass
class TimeWindow:
    unit: TimeUnit
    value: int = Field(..., ge=0, le=2592000)

    @validator("unit")
    def check_unit(cls, v):
        valid_units = [
            TimeUnit.MILLISECONDS,
            TimeUnit.SECONDS,
            TimeUnit.MINUTES,
            TimeUnit.HOURS,
            TimeUnit.DAYS,
        ]
        if v not in valid_units:
            raise ValueError(f"`{v}` not valid unit for this strategy: {valid_units}")

        return v

    @validator("value")
    def check_value(cls, v, values):
        unit = values.get("unit")

        upper_bound_seconds = 2592000

        upper_bounds = {
            TimeUnit.MILLISECONDS: upper_bound_seconds * 1e3,
            TimeUnit.SECONDS: upper_bound_seconds,
            TimeUnit.MINUTES: upper_bound_seconds / 60,
            TimeUnit.HOURS: upper_bound_seconds / 3600,
            TimeUnit.DAYS: upper_bound_seconds / (3600 * 24),
        }

        upper_bound = upper_bounds.get(unit)

        if v < 0 or v > upper_bound:
            raise ValueError(f"Failed check 0 < {v} (value) < {upper_bound}. Unit: {unit}")

        return v


class Thresholds(CommonThresholds):
    """Thresholds to be applied to the algorithm."""

    minimumMessageCount: int = Field(..., ge=0, le=100000)
    orderEvents: List[OrderStatus]
    messageCountGrouping: List[GroupByEnum]
    timeWindow: TimeWindow
    excValidityPeriods: Optional[bool] = False
    restrictAlerts: Optional[bool] = False
