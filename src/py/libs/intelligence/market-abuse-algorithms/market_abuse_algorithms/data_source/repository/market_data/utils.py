# ruff: noqa: E501
# type: ignore
import datetime
import logging
import pandas as pd
import pendulum
import pyarrow.compute as pc
import re
from market_abuse_algorithms.data_source.repository.market_data.static import (
    DATE_TIME,
    AuctionColumns,
    EODStatsDataColumns,
    QuoteColumns,
    TradeColumns,
)
from market_abuse_algorithms.data_source.static.market_data.refinitiv import (
    RefinitivColumnsEnum,
)
from market_abuse_algorithms.data_source.static.sdp.order import BuySell, OrderField
from market_abuse_algorithms.strategy.abstract_spoofing_v2.static import AlgoColumnsEnum
from market_abuse_algorithms.utils.data import calculate_percentage_level
from se_market_data_utils.client import MarketDataAPI
from se_market_data_utils.parquet_handlers import get_eod_stats, get_tick_parquet
from se_market_data_utils.schema.api import APIResponseStatus
from se_market_data_utils.schema.refinitiv import (
    RefinitivEventType,
    RefinitivExtractColumns,
    RefinitivOrderBookDepthColumns,
)
from typing import List, Optional, Union

logger = logging.getLogger(__name__)


ORIGIN = pd.Timestamp("1970-01-01", tzinfo=datetime.timezone.utc)


def get_bid_or_ask_cols(
    market_data: pd.Series, refinitiv_col_bid_ask: str
) -> Union[pd.Series, int, float]:
    """Get the BID size fields.

    :param refinitiv_col_bid_ask:
    :param market_data: pd.Dataframe
    :return: pd.Dataframe with only the bid size
    """
    size_cols: List = [col for col in market_data.keys() if refinitiv_col_bid_ask in col]

    return market_data.loc[size_cols]


def level_order_book(prices: pd.Series, buy_sell: str) -> int:
    """
    Get the level of order book, based on the max/min col
    Assuming that columns follow this template:
        L1_BID_PRICE = "L1-BidPrice"
        L1_ASK_PRICE = "L1-AskPrice"

    :param prices: pd.Series with only the prices of interest
    :param buy_sell: str, if is BUY/SELL
    :return: int, the level of order book
    """
    level_column: str = ""
    if buy_sell == BuySell.BUY:
        level_column = pd.to_numeric(prices).idxmax()

    if buy_sell == BuySell.SELL:
        level_column = pd.to_numeric(prices).idxmin()

    return int(level_column.split("-")[0][1])


def get_nearest_tick_data_from_submitted(
    market_data: pd.DataFrame,
    order_time_submitted: datetime.datetime,
    after_flag: bool = False,
) -> pd.Series:
    """Get the more recent tick in the Order Book Depth Market Tick Data <=
    the.

    [Order Time Submitted]

    :param after_flag: after flag for when the nearest tick data wanted is after the provided timestamp
    :param market_data: pd.DataFrame, data with market data
    :param order_time_submitted: datetime.datetime, date time
    :return: pd.Series, more recent tick
    """
    market_data[RefinitivOrderBookDepthColumns.DATE_TIME] = pd.to_datetime(
        market_data.loc[:, RefinitivOrderBookDepthColumns.DATE_TIME], format="mixed"
    )

    if after_flag:
        data: pd.DataFrame = market_data.loc[
            market_data.loc[:, RefinitivOrderBookDepthColumns.DATE_TIME]
            >= pd.to_datetime(order_time_submitted, format="mixed")
        ]

    else:
        data: pd.DataFrame = market_data.loc[
            market_data.loc[:, RefinitivOrderBookDepthColumns.DATE_TIME]
            <= pd.to_datetime(order_time_submitted, format="mixed")
        ]

    data: pd.DataFrame = data.sort_values(by=[RefinitivOrderBookDepthColumns.DATE_TIME])

    if after_flag:
        return pd.Series() if data.empty else data.iloc[0]

    return pd.Series() if data.empty else data.iloc[-1]


def get_closest_tick(tick_data: pd.DataFrame, order_time_submitted: datetime.datetime):
    """Method which select closest tick to a given timestamp (after or before)

    :param tick_data: dataframe with tick data to be filtered
    :param order_time_submitted: timestamp to fetch closest timestamp to
    :return:
    """

    before_tick = get_nearest_tick_data_from_submitted(
        market_data=tick_data, order_time_submitted=order_time_submitted
    )

    after_tick = get_nearest_tick_data_from_submitted(
        market_data=tick_data,
        order_time_submitted=order_time_submitted,
        after_flag=True,
    )

    if after_tick.empty and not before_tick.empty:
        return before_tick

    elif not after_tick.empty and before_tick.empty:
        return after_tick

    before_timedelta: pd.Timedelta = (
        order_time_submitted - before_tick[RefinitivOrderBookDepthColumns.DATE_TIME]
    )

    after_timedelta: pd.Timedelta = (
        after_tick[RefinitivOrderBookDepthColumns.DATE_TIME] - order_time_submitted
    )

    closest_timestamp = min(before_timedelta, after_timedelta)

    if closest_timestamp == before_timedelta:
        return before_tick

    return after_tick


def get_volume_at_level(
    market_data: pd.DataFrame,
    order_time_submitted: datetime.datetime,
    buy_sell: str,
    level_of_order_book: Optional[int],
) -> Optional[int]:
    """
    Specs: https://steeleye.atlassian.net/wiki/spaces/PRODUCT/pages/2082996227/7.1+Order+Book+Depth+-+Supporting+assets+-+Level+of+Order+Book+for+an+Order+and+Volume+at+Level#Volume-at-Level-for-an-Order
    get the volume for a specific order in a given level.
    for example: if vLevelOfOrderBook = 2 and the Order is a Buy-Order, the column to retrieve is L2-BidSize
    :param market_data: pd.Dataframe, order book depth data
    :param order_time_submitted: datetime, timestamp when the order was submitted
    :param buy_sell: str, BuySell order field
    :param level_of_order_book: int, level of an order
    :return:
    """
    nearest_tick_data: pd.Series = get_nearest_tick_data_from_submitted(
        market_data=market_data, order_time_submitted=order_time_submitted
    )

    if nearest_tick_data.empty:
        logger.warning("No nearest tick data to get the level of order book.")
        return None

    bid_col: str = (
        RefinitivExtractColumns.BID_SIZE
        if RefinitivExtractColumns.BID_PRICE in nearest_tick_data.keys()
        else RefinitivColumnsEnum.BID_SIZE
    )
    ask_col: str = (
        RefinitivExtractColumns.ASK_SIZE
        if RefinitivExtractColumns.ASK_SIZE in nearest_tick_data.keys()
        else RefinitivColumnsEnum.ASK_SIZE
    )

    if buy_sell == BuySell.BUY:
        order_sizes: pd.Series = get_bid_or_ask_cols(
            market_data=nearest_tick_data,
            refinitiv_col_bid_ask=bid_col,
        )

    elif buy_sell == BuySell.SELL:
        order_sizes: pd.Series = get_bid_or_ask_cols(
            market_data=nearest_tick_data,
            refinitiv_col_bid_ask=ask_col,
        )

    else:
        logger.error("The buy_sell variable doesn't have a valid value. Please check")
        raise ValueError("The buy_sell variable doesn't have a valid value. Please check")

    size_col = None

    if level_of_order_book is not None:
        size_col: str = [col for col in order_sizes.keys() if str(int(level_of_order_book)) in col][
            0
        ]

    volume_level: pd.Series = (
        nearest_tick_data.loc[size_col] if size_col is not None else order_sizes
    )

    return volume_level.values[0] if isinstance(volume_level, pd.Series) else volume_level


def add_vol_and_percentage_level_to_market_data(
    data: pd.DataFrame,
    market_data: pd.DataFrame,
    volume_level_col: str,
    percentage_level_col: str,
    level_of_order_book_col: Optional[str] = AlgoColumnsEnum.LEVEL_OF_ORDER_BOOK,
    level_of_order_book_flag: bool = True,
) -> pd.DataFrame:
    """Get volume at specific level & Check if order book depth data is valid.

    :param level_of_order_book: bool, it is set to false when is not supposed to be used (level1 mrkt data)
    :param data: pd.DataFrame, tenant data
    :param market_data: pd.Dataframe, order book depth data
    :param level_of_order_book_col: str, level of order book column to be created
    :param volume_level_col: str, column name for volume level of order book
    :param percentage_level_col: str, column name for percentage level of order book

    :return: List[pd.DataFrame], list of dataframes with valid data
    """
    tenant_data_with_level = data.copy()

    tenant_data_with_level.loc[:, volume_level_col] = tenant_data_with_level.apply(
        lambda row: get_volume_at_level(
            market_data=market_data,
            order_time_submitted=row.loc[OrderField.TS_ORD_SUBMITTED],
            buy_sell=row.loc[OrderField.EXC_DTL_BUY_SELL_IND],
            level_of_order_book=row.loc[level_of_order_book_col]
            if level_of_order_book_flag
            else None,
        ),
        axis=1,
    )

    tenant_data_with_level.loc[:, percentage_level_col] = tenant_data_with_level.apply(
        lambda row: calculate_percentage_level(
            volume_at_level_col=volume_level_col, single_order_data=row
        ),
        axis=1,
    )

    return tenant_data_with_level


def get_level_mid_price(
    market_data: pd.DataFrame, order_time_submitted: datetime.datetime
) -> Union[float, None]:
    """Take an input timestamp Inspect the nearest row in the Order Book Depth
    Market Tick Data <= the input timestamp Retrieve the columns L1-BidPrice,
    L1-AskPrice.

    Calculate
        Level 1 Mid Price = (L1-BidPrice + L1-AskPrice) / 2

    :param market_data: pd.Dataframe, order book depth data
    :param order_time_submitted: datetime, timestamp when the order was submitted
    :return:
    """
    nearest_tick_data: pd.Series = get_nearest_tick_data_from_submitted(
        market_data=market_data, order_time_submitted=order_time_submitted
    )
    if nearest_tick_data.empty:
        logger.warning("No nearest tick data to get the mid price.")
        return None

    if (
        RefinitivExtractColumns.BID_PRICE in nearest_tick_data.keys()
        and RefinitivExtractColumns.ASK_PRICE in nearest_tick_data.keys()
    ):
        bid_price_level1: pd.Series = get_bid_or_ask_cols(
            market_data=nearest_tick_data,
            refinitiv_col_bid_ask=RefinitivExtractColumns.BID_PRICE,
        ).values[0]

        ask_price_level1: pd.Series = get_bid_or_ask_cols(
            market_data=nearest_tick_data,
            refinitiv_col_bid_ask=RefinitivExtractColumns.ASK_PRICE,
        ).values[0]
    else:
        bid_price_level1: pd.Series = get_bid_or_ask_cols(
            market_data=nearest_tick_data,
            refinitiv_col_bid_ask=RefinitivColumnsEnum.BID_PRICE,
        ).loc[RefinitivOrderBookDepthColumns.L1_BID_PRICE]

        ask_price_level1: pd.Series = get_bid_or_ask_cols(
            market_data=nearest_tick_data,
            refinitiv_col_bid_ask=RefinitivColumnsEnum.ASK_PRICE,
        ).loc[RefinitivOrderBookDepthColumns.L1_ASK_PRICE]

    mid_price_level1: float = (bid_price_level1 + ask_price_level1) / 2

    return mid_price_level1


def fetch_tick_data_with_increasing_time_windows(
    market_data_api: MarketDataAPI,
    start_date: pd.Timestamp,
    end_date: pd.Timestamp,
    ric: str,
    event_type: Union[
        RefinitivEventType.QUOTE,
        RefinitivEventType.TRADE,
        RefinitivEventType.AUCTION,
    ],
) -> pd.DataFrame:
    """Fetches tick data for a given RIC between the provided dates. This
    function applies a timedelta as a margin to the provided dates. It starts
    with 1 hour margin and increases up to a maximum of 16 hours. While
    applying the margin, this function keeps the dates with margin in the same
    day as the dates without margin. The reasoning for this is that for tick
    data, we have a file for each day, and we don't want to fetch an extra file
    for a different day just because of the margin. E.g, if the start_date is
    '2024-04-03 12:00:00' and we apply a margin of 16 hours we would get
    '2024-04-02 20:00:00'. With the limit, it would be '2024-04-03 00:00:00'.

    :param market_data_api: se-market-data-utils client
    :param start_date: date to be used as lower limit of the time window
    :param end_date: date to be used as upper limit of the time window
    :param ric: ric to fetch data for
    :param event_type: type of data to fetch. Either Quote, Trade, or Auction
    :return:
    """
    # get all unique dates
    unique_dates = list({start_date.date(), end_date.date()})

    columns = None
    if event_type == RefinitivEventType.QUOTE:
        columns = QuoteColumns.get_columns()
    elif event_type == RefinitivEventType.TRADE:
        columns = TradeColumns.get_columns()
    elif event_type == RefinitivEventType.AUCTION:
        columns = AuctionColumns.get_columns()

    # iterate over 1, 2, 4, 8, and 16 hours of margin so that we have all the chances to have tick data
    for margin in [1, 2, 4, 8, 16]:
        # remove margin hours from the start date so that we have a margin to get the tick data
        start_date_from_margin = start_date - pd.Timedelta(hours=margin)
        # only update the margin to be the same whole day if we only have one day to get tick data,
        # otherwise leave the margin as it was calculated
        if start_date_from_margin.day != start_date.day:
            start_date_from_margin = start_date.replace(hour=0, minute=0, second=0, microsecond=0)

        # add margin hours to the end date so that we have a margin to get the tick data
        end_date_from_margin = end_date + pd.Timedelta(hours=margin)
        if end_date_from_margin.day != end_date.day:
            end_date_from_margin = end_date.replace(
                hour=23, minute=59, second=59, microsecond=999999
            )

        # the epoch in tick data has 19 digits
        start_date_from_margin = int(start_date_from_margin.timestamp() * 1e9)
        end_date_from_margin = int(end_date_from_margin.timestamp() * 1e9)

        # pyarrow expression to filter the data on the dates
        pa_expression = (pc.field(DATE_TIME) >= start_date_from_margin) & (
            pc.field(DATE_TIME) <= end_date_from_margin
        )

        tick_data = [pd.DataFrame()]
        for batch in get_tick_parquet(
            market_client=market_data_api,
            event_type=event_type,
            dates=unique_dates,
            ric=ric,
            columns=columns,  # if None is passed in the columns, it will return every column
            pa_expression=pa_expression,
        ):
            tick_data.append(batch.to_pandas())

        tick_data = pd.concat(tick_data)

        # if we have tick data in the time window, break the loop and return the data
        if not tick_data.empty:
            logger.info(f"Found tick data with a time margin of {margin} hours")

            tick_data = tick_data.sort_values(by=[DATE_TIME]).reset_index(drop=True)

            return tick_data

        logger.info(f"No tick data found with a time margin of {margin} hours")

    logger.info(f"No {event_type} tick data found for RIC: {ric} for {unique_dates}")
    return pd.DataFrame()


def fetch_eod_stats_with_time_margin(
    market_data_api: MarketDataAPI,
    start_date: datetime.date,
    end_date: datetime.date,
    ric: str,
) -> pd.DataFrame:
    """Fetches eod data for a given RIC between the provided dates. This
    function applies a timedelta as a margin to the provided dates. It fetches
    with 40 and 365 days margin.

    :param market_data_api: se-market-data-utils client
    :param start_date: date to be used as lower limit of the time window
    :param end_date: date to be used as upper limit of the time window
    :param ric: ric to fetch data for
    :return:
    """
    date_to = str(end_date)
    max_margin = 365
    # trying to get the 30 days before the minimum date otherwise won't have any previous dates
    for margin in [40, max_margin]:
        date_from = str(start_date - datetime.timedelta(days=margin))

        eod_data = get_eod_stats(
            market_client=market_data_api,
            date_from=date_from,
            date_to=date_to,
            ric=ric,
            columns=EODStatsDataColumns.get_columns(),
        ).to_pandas()

        if len(eod_data) >= 30 or margin == max_margin:
            logger.info(
                f"Found eod data with at least 30 days of data/reached the maximum time margin. "
                f"Margin: {margin}"
            )
            return eod_data
        else:
            logger.info(
                f"No eod data found with at least 30 days of data for a time margin of {margin} days"
            )
            continue


def dt_to_timestamp(tyme: Union[datetime.datetime, pendulum.datetime]) -> pd.Timestamp:
    this_time = tyme

    # pendulum.datetime.DateTime isn't accessible for type check
    if "pendulum" in str(type(tyme)):
        this_time = datetime.datetime.fromtimestamp(tyme.timestamp(), tz=datetime.timezone.utc)

    if tyme.tzinfo is None:
        this_time = this_time.replace(tzinfo=datetime.timezone.utc)
    elif tyme.tzinfo != datetime.timezone.utc:
        this_time = this_time.astimezone(datetime.timezone.utc)

    return pd.Timestamp((this_time - ORIGIN) // pd.Timedelta("1n"))


def get_integer_from_time_window(window: Union[int, str]) -> int:
    """Retrieve integer part from string window.

    :param window: str. Time window
    :return: Integer
    """
    if isinstance(window, int):
        return window
    else:
        return int(re.match(r"\d+", window).group())


class MarketDataStoreError(Exception):
    def __init__(self, status: APIResponseStatus, exception: Optional[Exception] = None):
        self.status = status
        self.exception = exception
