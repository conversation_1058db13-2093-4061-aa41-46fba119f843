# type: ignore
import datetime
from elasticsearch_dsl.query import Term
from market_abuse_algorithms.data_source.query.base import BaseQuery
from market_abuse_algorithms.data_source.static.sdp.order import OrderField
from market_abuse_algorithms.data_source.static.srp.positions import PositionsField
from se_elastic_schema.models.tenant.mifid2.position import Position


class PositionsQuery(BaseQuery):
    MODEL = "Position"
    MODEL_INDEX = Position.get_elastic_index_alias()

    def __init__(self):
        super().__init__()
        self.add_condition(
            mode="filter",
            conditions=[Term(**{PositionsField.META_MODEL: self.MODEL})],
        )

    def isin(self, instrument: str):
        conditions = [Term(**{field: instrument}) for field in OrderField.get_instrument_fields()]

        self.add_condition(mode="should", conditions=conditions)

    def currency(self, currency: str):
        self.add_condition(
            mode="filter",
            conditions=[Term(**{PositionsField.AMOUNT_NATIVE_CURRENCY: currency})],
        )

    def evaluation_type(self, evaluation_type: str):
        self.add_condition(
            mode="filter",
            conditions=[Term(**{PositionsField.LEVEL: evaluation_type})],
        )

    def evaluation_type_value(self, evaluation_type_value: str):
        self.add_condition(
            mode="filter",
            conditions=[Term(**{PositionsField.PARTIES_FILE_IDENTIFIER: evaluation_type_value})],
        )

    def observation_day(self, date: datetime.date):
        self.add_condition(
            mode="filter",
            conditions=[Term(**{PositionsField.DATE: date.isoformat()})],
        )
