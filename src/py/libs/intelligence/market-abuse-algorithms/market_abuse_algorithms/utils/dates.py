# type: ignore
import addict
import datetime
import logging
import pandas as pd
from market_abuse_algorithms.data_source.query.static import DateRangeParameters
from market_abuse_algorithms.data_source.static.sdp.order import OrderField
from typing import Dict, List, Optional

logger = logging.getLogger(__name__)

BACKTEST_DATE_RANGE_RESTRICTION = pd.Timedelta(30, unit="day")


def determine_observation_period(
    event_day: pd.Timestamp, observation_period: int, intraday_surveillance: Optional[bool] = False
) -> addict.Dict:
    """
    generates de starting and ending timestamps for the observation period
    :return: tuple with starting and ending timestamp for observation period
    """
    observation_period_dict = addict.Dict()
    if intraday_surveillance:
        observation_period_dict[DateRangeParameters.START] = (
            event_day - pd.tseries.offsets.BDay(observation_period) + pd.tseries.offsets.BDay(1)
        )

        observation_period_dict[DateRangeParameters.END] = pd.Timestamp(
            datetime.datetime.combine(event_day, datetime.time.max).replace(microsecond=0)
        )
    else:
        observation_period_dict[DateRangeParameters.START] = event_day - pd.tseries.offsets.BDay(
            observation_period
        )

        observation_period_dict[DateRangeParameters.END] = pd.Timestamp(
            datetime.datetime.combine(
                event_day - pd.tseries.offsets.BDay(1), datetime.time.max
            ).replace(microsecond=0)
        )

    return observation_period_dict


def get_order_state_date_range(
    event_day: pd.Timestamp,
    observation_period: int,
    behaviour_period: Optional[int] = None,
    intraday_surveillance: Optional[bool] = False,
):
    """Get the date range to search for executions, based on both behavior
    period and minimum observation period.

    :param behaviour_period: threshold defined by the user
    :param observation_period: threshold defined by the user
    :param event_day:
    :return:
    """
    if intraday_surveillance:
        if behaviour_period:
            timestamp_order_states_from: pd.Timestamp = event_day - pd.tseries.offsets.BDay(
                ((behaviour_period + 1) * observation_period) - 1
            )
        else:
            timestamp_order_states_from: pd.Timestamp = event_day - pd.tseries.offsets.BDay(
                observation_period - 1
            )

        timestamp_order_states_to: pd.Timestamp = pd.Timestamp(
            datetime.datetime.combine(event_day, datetime.time.max).replace(microsecond=0)
        )
    else:
        if behaviour_period:
            timestamp_order_states_from: pd.Timestamp = event_day - pd.tseries.offsets.BDay(
                ((behaviour_period + 1) * observation_period)
            )
        else:
            timestamp_order_states_from: pd.Timestamp = event_day - pd.tseries.offsets.BDay(
                observation_period
            )

        timestamp_order_states_to: pd.Timestamp = pd.Timestamp(
            datetime.datetime.combine(
                event_day - pd.tseries.offsets.BDay(1), datetime.time.max
            ).replace(microsecond=0)
        )

    orders_state_date_range = addict.Dict(
        {
            DateRangeParameters.START: timestamp_order_states_from,
            DateRangeParameters.END: timestamp_order_states_to,
        }
    )
    return orders_state_date_range


def determine_behavior_periods(
    date_range: Dict, observation_period: int, behaviour_period: int
) -> List[Dict]:
    """calculates the tuples with starting and ending timestamps for each
    behaviour periods.

    :param behaviour_period: threshold defined by the user
    :param observation_period: threshold defined by the user
    :param date_range: dict with observation period date range
    :return: list of tuples with period timestamps
    """
    from_timestamp = date_range.get(DateRangeParameters.START)
    to_timestamp = date_range.get(DateRangeParameters.END)

    behavior_periods_list = []
    for i in range(1, behaviour_period + 1):
        behaviour_time_from = from_timestamp - pd.tseries.offsets.BDay((observation_period * i))
        behaviour_time_to = to_timestamp - pd.tseries.offsets.BDay((observation_period * i))
        behaviour_period_range = {
            DateRangeParameters.START: behaviour_time_from,
            DateRangeParameters.END: behaviour_time_to,
        }
        behavior_periods_list.append(behaviour_period_range)

    return behavior_periods_list


def get_full_behaviour_period(list_of_behaviour_periods: List[Dict]) -> Dict:
    """Get the start/end days of behaviour_period.

    :param list_of_behaviour_periods: list oof behaviour periods
    :return:
    """
    end_day = max([segment.get(DateRangeParameters.END) for segment in list_of_behaviour_periods])
    start_day = min(
        [segment.get(DateRangeParameters.START) for segment in list_of_behaviour_periods]
    )
    return {DateRangeParameters.START: start_day, DateRangeParameters.END: end_day}


def determine_event_time_range(
    event_day: pd.Timestamp,
) -> addict.Dict:
    """Defines event day start and event day end.

    :param event_day: timestamp of the event day
    :return: tuple with start and end of event day
    """
    event_day_range = addict.Dict()

    event_day_range[DateRangeParameters.START] = event_day

    event_day_range[DateRangeParameters.END] = event_day + get_day_timedelta()

    return event_day_range


def get_day_timedelta():
    """Get the timedelta for a whole day."""
    return pd.Timedelta(value=1, unit="d") - pd.Timedelta(value=1, unit="s")


def get_timedelta_between_date_end_start(date_range_dict: dict) -> float:
    """Get the timedelta in seconds between the end and start date.

    :param date_range_dict:
    :return:
    """
    ts_delta = date_range_dict.get(DateRangeParameters.END, 0) - date_range_dict.get(
        DateRangeParameters.START, 0
    )
    if ts_delta == 0:
        return 0
    return ts_delta.total_seconds()


def get_latest_positions_record(positions_data: pd.DataFrame) -> pd.DataFrame:
    """Get latest position if there are more than 1 position record.

    :param positions_data: dataframe with all position records
    :return: dataframe with only 1 position record, the latest one
    """
    filtered_positions_data: pd.DataFrame = positions_data

    if len(positions_data) > 0 and OrderField.META_TIMESTAMP in positions_data.columns:
        filtered_positions_data = positions_data[
            positions_data[OrderField.META_TIMESTAMP]
            == positions_data[OrderField.META_TIMESTAMP].max()
        ]

    return filtered_positions_data


def add_date_range_filter(date_day_range: addict.Dict) -> Dict:
    return {
        "bool": {
            "must": [
                {
                    "range": {
                        OrderField.TS_TRADING_DATE_TIME: {
                            "gte": f"{date_day_range.get(DateRangeParameters.START).isoformat()}",
                            "lte": f"{date_day_range.get(DateRangeParameters.END).isoformat()}",
                        }
                    }
                }
            ]
        }
    }
