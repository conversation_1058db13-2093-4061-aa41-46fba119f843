{"&id": {"0": "ORD_ULVR.L_025662296:1:NEWO", "1": "layeringAndBookBalance_10_1:1:NEWO", "2": "layeringAndBookBalance_10_2:1:NEWO", "3": "layeringAndBookBalance_11_1:1:NEWO", "4": "layeringAndBookBalance_11_2:1:NEWO", "5": "layeringAndBookBalance_11_3:1:NEWO", "6": "layeringAndBookBalance_11_4:1:NEWO", "7": "layeringAndBookBalance_11_5:1:NEWO", "8": "layeringAndBookBalance_14_1:1:NEWO", "9": "layeringAndBookBalance_14_2:1:NEWO", "10": "layeringAndBookBalance_14_3:2:NEWO", "11": "layeringAndBookBalance_14_4:2:NEWO", "12": "layeringAndBookBalance_15A_1:1:NEWO", "13": "layeringAndBookBalance_15A_2:1:NEWO", "14": "layeringAndBookBalance_15A_3:2:NEWO", "15": "layeringAndBookBalance_15_1:1:NEWO", "16": "layeringAndBookBalance_15_2:1:NEWO", "17": "layeringAndBookBalance_15_3:2:NEWO", "18": "layeringAndBookBalance_17_1:1:NEWO", "19": "layeringAndBookBalance_17_2:1:NEWO", "20": "layeringAndBookBalance_6_1:1:NEWO", "21": "layeringAndBookBalance_6_2:1:NEWO", "22": "layeringAndBookBalance_9_1:1:NEWO", "23": "layeringAndBookBalance_9_2:1:NEWO", "24": "spoofingV2_10.1_1:2:NEWO", "25": "spoofingV2_10_1:2:NEWO", "26": "spoofingV2_10_2:1:NEWO", "27": "spoofingV2_14_1:2:NEWO", "28": "spoofingV2_14_2:1:NEWO", "29": "spoofingV2_18_10:2:NEWO", "30": "spoofingV2_18_2:2:NEWO", "31": "spoofingV2_18_3:2:NEWO", "32": "spoofingV2_18_4:2:NEWO", "33": "spoofingV2_18_5:2:NEWO", "34": "spoofingV2_18_6:2:NEWO", "35": "spoofingV2_18_9:2:NEWO", "36": "spoofingV2_5_1:1:NEWO", "37": "spoofingV2_5_2:2:NEWO", "38": "spoofingV2_6_1:2:NEWO", "39": "spoofingV2_6_2:1:NEWO", "40": "spoofingV2_7_1:2:NEWO", "41": "spoofingV2_7_2:1:NEWO", "42": "spoofingV2_8_1:2:NEWO", "43": "spoofingV2_8_2:1:NEWO", "44": "spoofingV2_9_1:2:NEWO", "45": "spoofingV2_9_2:1:NEWO"}, "clientFileIdentifier": {"0": "account:client1", "1": "account:client1", "2": "account:client1", "3": "account:client1", "4": "account:client1", "5": "account:client1", "6": "account:client1", "7": "account:client1", "8": "account:client1", "9": "account:client1", "10": "account:client1", "11": "account:client1", "12": "account:client1", "13": "account:client1", "14": "account:client1", "15": "account:client1", "16": "account:client1", "17": "account:client1", "18": "account:client1", "19": "account:client1", "20": "account:client1", "21": "account:client1", "22": "account:client1", "23": "account:client1", "24": "account:client1", "25": "account:client1", "26": "account:client1", "27": "account:client1", "28": "account:client1", "29": "account:client1", "30": "account:client1", "31": "account:client1", "32": "account:client1", "33": "account:client1", "34": "account:client1", "35": "account:client1", "36": "account:client1", "37": "account:client1", "38": "account:client1", "39": "account:client1", "40": "account:client1", "41": "account:client1", "42": "account:client1", "43": "account:client1", "44": "account:client1", "45": "account:client1"}, "&key": {"0": "Order:ORD_ULVR.L_025662296:1:NEWO:*************", "1": "Order:layeringAndBookBalance_10_1:1:NEWO:*************", "2": "Order:layeringAndBookBalance_10_2:1:NEWO:*************", "3": "Order:layeringAndBookBalance_11_1:1:NEWO:*************", "4": "Order:layeringAndBookBalance_11_2:1:NEWO:*************", "5": "Order:layeringAndBookBalance_11_3:1:NEWO:*************", "6": "Order:layeringAndBookBalance_11_4:1:NEWO:*************", "7": "Order:layeringAndBookBalance_11_5:1:NEWO:*************", "8": "Order:layeringAndBookBalance_14_1:1:NEWO:*************", "9": "Order:layeringAndBookBalance_14_2:1:NEWO:*************", "10": "Order:layeringAndBookBalance_14_3:2:NEWO:*************", "11": "Order:layeringAndBookBalance_14_4:2:NEWO:*************", "12": "Order:layeringAndBookBalance_15A_1:1:NEWO:*************", "13": "Order:layeringAndBookBalance_15A_2:1:NEWO:*************", "14": "Order:layeringAndBookBalance_15A_3:2:NEWO:*************", "15": "Order:layeringAndBookBalance_15_1:1:NEWO:1689759909778", "16": "Order:layeringAndBookBalance_15_2:1:NEWO:1689759909778", "17": "Order:layeringAndBookBalance_15_3:2:NEWO:1689759909778", "18": "Order:layeringAndBookBalance_17_1:1:NEWO:1689758534817", "19": "Order:layeringAndBookBalance_17_2:1:NEWO:1689758534817", "20": "Order:layeringAndBookBalance_6_1:1:NEWO:1689759404376", "21": "Order:layeringAndBookBalance_6_2:1:NEWO:1689759404376", "22": "Order:layeringAndBookBalance_9_1:1:NEWO:1689765354415", "23": "Order:layeringAndBookBalance_9_2:1:NEWO:1689765354415", "24": "Order:spoofingV2_10.1_1:2:NEWO:1701073983695", "25": "Order:spoofingV2_10_1:2:NEWO:1689759951048", "26": "Order:spoofingV2_10_2:1:NEWO:1689759951048", "27": "Order:spoofingV2_14_1:2:NEWO:1689762007243", "28": "Order:spoofingV2_14_2:1:NEWO:1689762007243", "29": "Order:spoofingV2_18_10:2:NEWO:1705925118102", "30": "Order:spoofingV2_18_2:2:NEWO:1705925118102", "31": "Order:spoofingV2_18_3:2:NEWO:1705925118102", "32": "Order:spoofingV2_18_4:2:NEWO:1705925118102", "33": "Order:spoofingV2_18_5:2:NEWO:1705925118102", "34": "Order:spoofingV2_18_6:2:NEWO:1705925118102", "35": "Order:spoofingV2_18_9:2:NEWO:1705925118102", "36": "Order:spoofingV2_5_1:1:NEWO:*************", "37": "Order:spoofingV2_5_2:2:NEWO:*************", "38": "Order:spoofingV2_6_1:2:NEWO:*************", "39": "Order:spoofingV2_6_2:1:NEWO:*************", "40": "Order:spoofingV2_7_1:2:NEWO:*************", "41": "Order:spoofingV2_7_2:1:NEWO:*************", "42": "Order:spoofingV2_8_1:2:NEWO:*************", "43": "Order:spoofingV2_8_2:1:NEWO:*************", "44": "Order:spoofingV2_9_1:2:NEWO:*************", "45": "Order:spoofingV2_9_2:1:NEWO:*************"}, "traderFileIdentifier": {"0": "account:trader1", "1": "account:trader1", "2": "account:trader1", "3": "account:trader1", "4": "account:trader1", "5": "account:trader1", "6": "account:trader1", "7": "account:trader1", "8": "account:trader1", "9": "account:trader1", "10": "account:trader1", "11": "account:trader1", "12": "account:trader1", "13": "account:trader1", "14": "account:trader1", "15": "account:trader1", "16": "account:trader1", "17": "account:trader1", "18": "account:trader1", "19": "account:trader1", "20": "account:trader1", "21": "account:trader1", "22": "account:trader1", "23": "account:trader1", "24": "account:trader1", "25": "account:trader1", "26": "account:trader1", "27": "account:trader1", "28": "account:trader1", "29": "account:trader1", "30": "account:trader1", "31": "account:trader1", "32": "account:trader1", "33": "account:trader1", "34": "account:trader1", "35": "account:trader1", "36": "account:trader1", "37": "account:trader1", "38": "account:trader1", "39": "account:trader1", "40": "account:trader1", "41": "account:trader1", "42": "account:trader1", "43": "account:trader1", "44": "account:trader1", "45": "account:trader1"}, "counterpartyFileIdentifier": {"0": "account:counterparty1", "1": "account:counterparty1", "2": "account:counterparty1", "3": "account:counterparty1", "4": "account:counterparty1", "5": "account:counterparty1", "6": "account:counterparty1", "7": "account:counterparty1", "8": "account:counterparty1", "9": "account:counterparty1", "10": "account:counterparty1", "11": "account:counterparty1", "12": "account:counterparty1", "13": "account:counterparty1", "14": "account:counterparty1", "15": "account:counterparty1", "16": "account:counterparty1", "17": "account:counterparty1", "18": "account:counterparty1", "19": "account:counterparty1", "20": "account:counterparty1", "21": "account:counterparty1", "22": "account:counterparty1", "23": "account:counterparty1", "24": "account:counterparty1", "25": "account:counterparty1", "26": "account:counterparty1", "27": "account:counterparty1", "28": "account:counterparty1", "29": "account:counterparty1", "30": "account:counterparty1", "31": "account:counterparty1", "32": "account:counterparty1", "33": "account:counterparty1", "34": "account:counterparty1", "35": "account:counterparty1", "36": "account:counterparty1", "37": "account:counterparty1", "38": "account:counterparty1", "39": "account:counterparty1", "40": "account:counterparty1", "41": "account:counterparty1", "42": "account:counterparty1", "43": "account:counterparty1", "44": "account:counterparty1", "45": "account:counterparty1"}, "transactionDetails.venue": {"0": "XLON", "1": "XLON", "2": "XLON", "3": "XLON", "4": "XLON", "5": "XLON", "6": "XLON", "7": "XLON", "8": "XLON", "9": "XLON", "10": "XLON", "11": "XLON", "12": "XLON", "13": "XLON", "14": "XLON", "15": "XLON", "16": "XLON", "17": "XLON", "18": "XLON", "19": "XLON", "20": "XLON", "21": "XLON", "22": "XLON", "23": "XLON", "24": "XLON", "25": "XLON", "26": "XLON", "27": "XLON", "28": "XLON", "29": "IFLL", "30": "XLON", "31": "XXXX", "32": "XEUR", "33": "XEUE", "34": "XEUE", "35": "IFLL", "36": "XLON", "37": "XLON", "38": "XLON", "39": "XLON", "40": "XLON", "41": "XLON", "42": "XLON", "43": "XLON", "44": "XLON", "45": "XLON"}, "transactionDetails.ultimateVenue": {"0": "XLON", "1": "XLON", "2": "XLON", "3": "XLON", "4": "XLON", "5": "XLON", "6": "XLON", "7": "XLON", "8": "XLON", "9": "XLON", "10": "XLON", "11": "XLON", "12": "XLON", "13": "XLON", "14": "XLON", "15": "XLON", "16": "XLON", "17": "XLON", "18": "XLON", "19": "XLON", "20": "XLON", "21": "XLON", "22": "XLON", "23": "XLON", "24": "XLON", "25": "XLON", "26": "XLON", "27": "XLON", "28": "XLON", "29": "IFLL", "30": "XLON", "31": null, "32": "XEUR", "33": "XEUE", "34": "XEUE", "35": "IFLL", "36": "XLON", "37": "XLON", "38": "XLON", "39": "XLON", "40": "XLON", "41": "XLON", "42": "XLON", "43": "XLON", "44": "XLON", "45": "XLON"}, "reportDetails.executingEntity.fileIdentifier": {"0": "lei:894500wota5040khgx73", "1": "lei:894500wota5040khgx73", "2": "lei:894500wota5040khgx73", "3": "lei:894500wota5040khgx73", "4": "lei:894500wota5040khgx73", "5": "lei:894500wota5040khgx73", "6": "lei:894500wota5040khgx73", "7": "lei:894500wota5040khgx73", "8": "lei:894500wota5040khgx73", "9": "lei:894500wota5040khgx73", "10": "lei:894500wota5040khgx73", "11": "lei:894500wota5040khgx73", "12": "lei:894500wota5040khgx73", "13": "lei:894500wota5040khgx73", "14": "lei:894500wota5040khgx73", "15": "lei:894500wota5040khgx73", "16": "lei:894500wota5040khgx73", "17": "lei:894500wota5040khgx73", "18": "lei:894500wota5040khgx73", "19": "lei:894500wota5040khgx73", "20": "lei:894500wota5040khgx73", "21": "lei:894500wota5040khgx73", "22": "lei:894500wota5040khgx73", "23": "lei:894500wota5040khgx73", "24": "lei:894500wota5040khgx73", "25": "lei:894500wota5040khgx73", "26": "lei:894500wota5040khgx73", "27": "lei:894500wota5040khgx73", "28": "lei:894500wota5040khgx73", "29": "lei:894500wota5040khgx73", "30": "lei:894500wota5040khgx73", "31": "lei:894500wota5040khgx73", "32": "lei:894500wota5040khgx73", "33": "lei:894500wota5040khgx73", "34": "lei:894500wota5040khgx73", "35": "lei:894500wota5040khgx73", "36": "lei:894500wota5040khgx73", "37": "lei:894500wota5040khgx73", "38": "lei:894500wota5040khgx73", "39": "lei:894500wota5040khgx73", "40": "lei:894500wota5040khgx73", "41": "lei:894500wota5040khgx73", "42": "lei:894500wota5040khgx73", "43": "lei:894500wota5040khgx73", "44": "lei:894500wota5040khgx73", "45": "lei:894500wota5040khgx73"}, "reportDetails.executingEntity.name": {"0": "SteelEye", "1": "SteelEye", "2": "SteelEye", "3": "SteelEye", "4": "SteelEye", "5": "SteelEye", "6": "SteelEye", "7": "SteelEye", "8": "SteelEye", "9": "SteelEye", "10": "SteelEye", "11": "SteelEye", "12": "SteelEye", "13": "SteelEye", "14": "SteelEye", "15": "SteelEye", "16": "SteelEye", "17": "SteelEye", "18": "SteelEye", "19": "SteelEye", "20": "SteelEye", "21": "SteelEye", "22": "SteelEye", "23": "SteelEye", "24": "SteelEye", "25": "SteelEye", "26": "SteelEye", "27": "SteelEye", "28": "SteelEye", "29": "SteelEye", "30": "SteelEye", "31": "SteelEye", "32": "SteelEye", "33": "SteelEye", "34": "SteelEye", "35": "SteelEye", "36": "SteelEye", "37": "SteelEye", "38": "SteelEye", "39": "SteelEye", "40": "SteelEye", "41": "SteelEye", "42": "SteelEye", "43": "SteelEye", "44": "SteelEye", "45": "SteelEye"}, "timestamps.orderStatusUpdated": {"0": 1629810000053, "1": 1629810000053, "2": 1629810000696, "3": 1629810000053, "4": 1629810000053, "5": 1629810000053, "6": 1629810000053, "7": 1629810000053, "8": 1629810000053, "9": 1629810000696, "10": 1629810000053, "11": 1629810000696, "12": 1629810000053, "13": 1629810000696, "14": 1629810000696, "15": 1629810000053, "16": 1629810000696, "17": 1629810000696, "18": 1629810000053, "19": 1629810000696, "20": 1629810000053, "21": 1629810000696, "22": 1629810000053, "23": 1629810000696, "24": 1629810000053, "25": 1629810000053, "26": 1629810000696, "27": 1629810000053, "28": 1629810000696, "29": 1629810000696, "30": 1629810000696, "31": 1629810000696, "32": 1629810000696, "33": 1629810000696, "34": 1629810000696, "35": 1629810000696, "36": 1629810000053, "37": 1629810000696, "38": 1629810000053, "39": 1629810000696, "40": 1629810000053, "41": 1629810000696, "42": 1629810000053, "43": 1629810000696, "44": 1629810000053, "45": 1629810000696}, "timestamps.orderSubmitted": {"0": 1629810000053, "1": 1629810000053, "2": 1629810000696, "3": 1629810000053, "4": 1629810000053, "5": 1629810000053, "6": 1629810000053, "7": 1629810000053, "8": 1629810000053, "9": 1629810000696, "10": 1629810000053, "11": 1629810000696, "12": 1629810000053, "13": 1629810000696, "14": 1629810000696, "15": 1629810000053, "16": 1629810000696, "17": 1629810000696, "18": 1629810000053, "19": 1629810000696, "20": 1629810000053, "21": 1629810000696, "22": 1629810000053, "23": 1629810000696, "24": 1629810000053, "25": 1629810000053, "26": 1629810000696, "27": 1629810000053, "28": 1629810000696, "29": 1629810000696, "30": 1629810000696, "31": 1629810000696, "32": 1629810000696, "33": 1629810000696, "34": 1629810000696, "35": 1629810000696, "36": 1629810000053, "37": 1629810000696, "38": 1629810000053, "39": 1629810000696, "40": 1629810000053, "41": 1629810000696, "42": 1629810000053, "43": 1629810000696, "44": 1629810000053, "45": 1629810000696}, "orderIdentifiers.orderIdCode": {"0": "ORD_ULVR.L_025662296", "1": "layeringAndBookBalance_10_1", "2": "layeringAndBookBalance_10_2", "3": "layeringAndBookBalance_11_1", "4": "layeringAndBookBalance_11_2", "5": "layeringAndBookBalance_11_3", "6": "layeringAndBookBalance_11_4", "7": "layeringAndBookBalance_11_5", "8": "layeringAndBookBalance_14_1", "9": "layeringAndBookBalance_14_2", "10": "layeringAndBookBalance_14_3", "11": "layeringAndBookBalance_14_4", "12": "layeringAndBookBalance_15A_1", "13": "layeringAndBookBalance_15A_2", "14": "layeringAndBookBalance_15A_3", "15": "layeringAndBookBalance_15_1", "16": "layeringAndBookBalance_15_2", "17": "layeringAndBookBalance_15_3", "18": "layeringAndBookBalance_17_1", "19": "layeringAndBookBalance_17_2", "20": "layeringAndBookBalance_6_1", "21": "layeringAndBookBalance_6_2", "22": "layeringAndBookBalance_9_1", "23": "layeringAndBookBalance_9_2", "24": "spoofingV2_10.1_1", "25": "spoofingV2_10_1", "26": "spoofingV2_10_2", "27": "spoofingV2_14_1", "28": "spoofingV2_14_2", "29": "spoofingV2_18_10", "30": "spoofingV2_18_2", "31": "spoofingV2_18_3", "32": "spoofingV2_18_4", "33": "spoofingV2_18_5", "34": "spoofingV2_18_6", "35": "spoofingV2_18_9", "36": "spoofingV2_5_1", "37": "spoofingV2_5_2", "38": "spoofingV2_6_1", "39": "spoofingV2_6_2", "40": "spoofingV2_7_1", "41": "spoofingV2_7_2", "42": "spoofingV2_8_1", "43": "spoofingV2_8_2", "44": "spoofingV2_9_1", "45": "spoofingV2_9_2"}, "executionDetails.orderType": {"0": "Market", "1": "Limit", "2": "Limit", "3": "Limit", "4": "Limit", "5": "Limit", "6": "Limit", "7": "Limit", "8": "Limit", "9": "Limit", "10": "Limit", "11": "Limit", "12": "Limit", "13": "Limit", "14": "Market", "15": "Limit", "16": "Limit", "17": "Market", "18": "Limit", "19": "Limit", "20": "Limit", "21": "Limit", "22": "Limit", "23": "Limit", "24": "Market", "25": "Market", "26": "Market", "27": "Market", "28": "Market", "29": "Market", "30": "Market", "31": "Market", "32": "Market", "33": "Market", "34": "Market", "35": "Market", "36": "Market", "37": "Market", "38": "Market", "39": "Market", "40": "Market", "41": "Market", "42": "Market", "43": "Market", "44": "Market", "45": "Market"}, "executionDetails.orderStatus": {"0": "NEWO", "1": "NEWO", "2": "NEWO", "3": "NEWO", "4": "NEWO", "5": "NEWO", "6": "NEWO", "7": "NEWO", "8": "NEWO", "9": "NEWO", "10": "NEWO", "11": "NEWO", "12": "NEWO", "13": "NEWO", "14": "NEWO", "15": "NEWO", "16": "NEWO", "17": "NEWO", "18": "NEWO", "19": "NEWO", "20": "NEWO", "21": "NEWO", "22": "NEWO", "23": "NEWO", "24": "NEWO", "25": "NEWO", "26": "NEWO", "27": "NEWO", "28": "NEWO", "29": "NEWO", "30": "NEWO", "31": "NEWO", "32": "NEWO", "33": "NEWO", "34": "NEWO", "35": "NEWO", "36": "NEWO", "37": "NEWO", "38": "NEWO", "39": "NEWO", "40": "NEWO", "41": "NEWO", "42": "NEWO", "43": "NEWO", "44": "NEWO", "45": "NEWO"}, "executionDetails.buySellIndicator": {"0": "BUYI", "1": "BUYI", "2": "BUYI", "3": "BUYI", "4": "BUYI", "5": "BUYI", "6": "BUYI", "7": "BUYI", "8": "BUYI", "9": "BUYI", "10": "SELL", "11": "SELL", "12": "BUYI", "13": "BUYI", "14": "SELL", "15": "BUYI", "16": "BUYI", "17": "SELL", "18": "BUYI", "19": "BUYI", "20": "BUYI", "21": "BUYI", "22": "BUYI", "23": "BUYI", "24": "SELL", "25": "SELL", "26": "BUYI", "27": "SELL", "28": "BUYI", "29": "SELL", "30": "SELL", "31": "SELL", "32": "SELL", "33": "SELL", "34": "SELL", "35": "SELL", "36": "BUYI", "37": "SELL", "38": "SELL", "39": "BUYI", "40": "SELL", "41": "BUYI", "42": "SELL", "43": "BUYI", "44": "SELL", "45": "BUYI"}, "tradersAlgosWaiversIndicators.investmentDecisionWithinFirm.name": {"0": "Trader 12", "1": "Trader 1", "2": "Trader 2", "3": "Trader 1", "4": "Trader 1", "5": "Trader 1", "6": "Trader 1", "7": "Trader 1", "8": "Trader 1", "9": "Trader 2", "10": "Trader 2", "11": "Trader 2", "12": "Trader 1", "13": "Trader 1", "14": "Trader 1", "15": "Trader 1", "16": "Trader 1", "17": "Trader 1", "18": "Trader 1", "19": "Trader 2", "20": "Trader 1", "21": "Trader 2", "22": "Trader 1", "23": "Trader 1", "24": "Trader 12", "25": "Trader 1", "26": "Trader 2", "27": "Trader 1", "28": "Trader 2", "29": "Trader 1", "30": "Trader 1", "31": "Trader 1", "32": "Trader 1", "33": "Trader 1", "34": "Trader 1", "35": "Trader 1", "36": "Trader 1", "37": "Trader 2", "38": "Trader 1", "39": "Trader 2", "40": "Trader 1", "41": "Trader 2", "42": "Trader 1", "43": "Trader 2", "44": "Trader 1", "45": "Trader 2"}, "tradersAlgosWaiversIndicators.investmentDecisionWithinFirmFileIdentifier": {"0": "account:trader1", "1": "account:trader1", "2": "account:trader2", "3": "account:trader1", "4": "account:trader1", "5": "account:trader1", "6": "account:trader1", "7": "account:trader1", "8": "account:trader1", "9": "account:trader2", "10": "account:trader2", "11": "account:trader2", "12": "account:trader1", "13": "account:trader1", "14": "account:trader1", "15": "account:trader1", "16": "account:trader1", "17": "account:trader1", "18": "account:trader1", "19": "account:trader2", "20": "account:trader1", "21": "account:trader2", "22": "account:trader1", "23": "account:trader1", "24": "account:trader1", "25": "account:trader1", "26": "account:trader2", "27": "account:trader1", "28": "account:trader2", "29": "account:trader1", "30": "account:trader1", "31": "account:trader1", "32": "account:trader1", "33": "account:trader1", "34": "account:trader1", "35": "account:trader1", "36": "account:trader1", "37": "account:trader2", "38": "account:trader1", "39": "account:trader2", "40": "account:trader1", "41": "account:trader2", "42": "account:trader1", "43": "account:trader2", "44": "account:trader1", "45": "account:trader2"}, "instrumentDetails.instrument.ext.alternativeInstrumentIdentifier": {"0": "XXXXGBPO", "1": "XXXXGBPO", "2": "XXXXGBPO", "3": "XXXXGBPO", "4": "XXXXGBPO", "5": "XXXXGBPO", "6": "XXXXGBPO", "7": "XXXXGBPO", "8": "XXXXGBPO", "9": "XXXXGBPO", "10": "XXXXGBPO", "11": "XXXXGBPO", "12": "XXXXGBPO", "13": "XXXXGBPO", "14": "XXXXGBPO", "15": "XXXXGBPO", "16": "XXXXGBPO", "17": "XXXXGBPO", "18": "XXXXGBPO", "19": "XXXXGBPO", "20": "XXXXGBPO", "21": "XXXXGBPO", "22": "XXXXGBPO", "23": "XXXXGBPO", "24": "XXXXGBPO", "25": "XXXXGBPO", "26": "XXXXGBPO", "27": "XXXXGBPO", "28": "XXXXGBPO", "29": "IFLLZFF2024-03 00:00:00", "30": "XXXXGBPO", "31": "XXXXGB00B10RZP78EQSWAP2024-03-08", "32": "XEURUNIPFF2024-01 00:00:00", "33": "XEUEUNOP2024-01 00:00:0048.********", "34": "XEUEUNOC2024-01 00:00:0048.********", "35": "IFLLZFF2024-06 00:00:00", "36": "XXXXGBPO", "37": "XXXXGBPO", "38": "XXXXGBPO", "39": "XXXXGBPO", "40": "XXXXGBPO", "41": "XXXXGBPO", "42": "XXXXGBPO", "43": "XXXXGBPO", "44": "XXXXGBPO", "45": "XXXXGBPO"}, "instrumentDetails.instrument.ext.instrumentUniqueIdentifier": {"0": "GB00B10RZP78GBPXLON", "1": "GB00B10RZP78GBPXLON", "2": "GB00B10RZP78GBPXLON", "3": "GB00B10RZP78GBPXLON", "4": "GB00B10RZP78GBPXLON", "5": "GB00B10RZP78GBPXLON", "6": "GB00B10RZP78GBPXLON", "7": "GB00B10RZP78GBPXLON", "8": "GB00B10RZP78GBPXLON", "9": "GB00B10RZP78GBPXLON", "10": "GB00B10RZP78GBPXLON", "11": "GB00B10RZP78GBPXLON", "12": "GB00B10RZP78GBPXLON", "13": "GB00B10RZP78GBPXLON", "14": "GB00B10RZP78GBPXLON", "15": "GB00B10RZP78GBPXLON", "16": "GB00B10RZP78GBPXLON", "17": "GB00B10RZP78GBPXLON", "18": "GB00B10RZP78GBPXLON", "19": "GB00B10RZP78GBPXLON", "20": "GB00B10RZP78GBPXLON", "21": "GB00B10RZP78GBPXLON", "22": "GB00B10RZP78GBPXLON", "23": "GB00B10RZP78GBPXLON", "24": "GB00B10RZP78GBPXLON", "25": "GB00B10RZP78GBPXLON", "26": "GB00B10RZP78GBPXLON", "27": "GB00B10RZP78GBPXLON", "28": "GB00B10RZP78GBPXLON", "29": "GB00KHK64W72GBPIFLL", "30": "GB00B10RZP78GBPXLON", "31": "EZH0QV8H92H2GBPXXXX", "32": "DE000F0JT1N3EURXEUR", "33": "NLEN13073841EURXEUE", "34": "NLEN13073775EURXEUE", "35": "GB00KHLXXF30GBPIFLL", "36": "GB00B10RZP78GBPXLON", "37": "GB00B10RZP78GBPXLON", "38": "GB00B10RZP78GBPXLON", "39": "GB00B10RZP78GBPXLON", "40": "GB00B10RZP78GBPXLON", "41": "GB00B10RZP78GBPXLON", "42": "GB00B10RZP78GBPXLON", "43": "GB00B10RZP78GBPXLON", "44": "GB00B10RZP78GBPXLON", "45": "GB00B10RZP78GBPXLON"}, "instrumentDetails.instrument.venue.tradingVenue": {"0": "XLON", "1": "XLON", "2": "XLON", "3": "XLON", "4": "XLON", "5": "XLON", "6": "XLON", "7": "XLON", "8": "XLON", "9": "XLON", "10": "XLON", "11": "XLON", "12": "XLON", "13": "XLON", "14": "XLON", "15": "XLON", "16": "XLON", "17": "XLON", "18": "XLON", "19": "XLON", "20": "XLON", "21": "XLON", "22": "XLON", "23": "XLON", "24": "XLON", "25": "XLON", "26": "XLON", "27": "XLON", "28": "XLON", "29": "IFLL", "30": "XLON", "31": "XXXX", "32": "XEUR", "33": "XEUE", "34": "XEUE", "35": "IFLL", "36": "XLON", "37": "XLON", "38": "XLON", "39": "XLON", "40": "XLON", "41": "XLON", "42": "XLON", "43": "XLON", "44": "XLON", "45": "XLON"}, "instrumentDetails.instrument.instrumentIdCode": {"0": "GB00B10RZP78", "1": "GB00B10RZP78", "2": "GB00B10RZP78", "3": "GB00B10RZP78", "4": "GB00B10RZP78", "5": "GB00B10RZP78", "6": "GB00B10RZP78", "7": "GB00B10RZP78", "8": "GB00B10RZP78", "9": "GB00B10RZP78", "10": "GB00B10RZP78", "11": "GB00B10RZP78", "12": "GB00B10RZP78", "13": "GB00B10RZP78", "14": "GB00B10RZP78", "15": "GB00B10RZP78", "16": "GB00B10RZP78", "17": "GB00B10RZP78", "18": "GB00B10RZP78", "19": "GB00B10RZP78", "20": "GB00B10RZP78", "21": "GB00B10RZP78", "22": "GB00B10RZP78", "23": "GB00B10RZP78", "24": "GB00B10RZP78", "25": "GB00B10RZP78", "26": "GB00B10RZP78", "27": "GB00B10RZP78", "28": "GB00B10RZP78", "29": "GB00KHK64W72", "30": "GB00B10RZP78", "31": "EZH0QV8H92H2", "32": "DE000F0JT1N3", "33": "NLEN13073841", "34": "NLEN13073775", "35": "GB00KHLXXF30", "36": "GB00B10RZP78", "37": "GB00B10RZP78", "38": "GB00B10RZP78", "39": "GB00B10RZP78", "40": "GB00B10RZP78", "41": "GB00B10RZP78", "42": "GB00B10RZP78", "43": "GB00B10RZP78", "44": "GB00B10RZP78", "45": "GB00B10RZP78"}, "instrumentDetails.instrument.instrumentFullName": {"0": "UNILEVER PLC ORD 3 1/9P", "1": "UNILEVER PLC ORD 3 1/9P", "2": "UNILEVER PLC ORD 3 1/9P", "3": "UNILEVER PLC ORD 3 1/9P", "4": "UNILEVER PLC ORD 3 1/9P", "5": "UNILEVER PLC ORD 3 1/9P", "6": "UNILEVER PLC ORD 3 1/9P", "7": "UNILEVER PLC ORD 3 1/9P", "8": "UNILEVER PLC ORD 3 1/9P", "9": "UNILEVER PLC ORD 3 1/9P", "10": "UNILEVER PLC ORD 3 1/9P", "11": "UNILEVER PLC ORD 3 1/9P", "12": "UNILEVER PLC ORD 3 1/9P", "13": "UNILEVER PLC ORD 3 1/9P", "14": "UNILEVER PLC ORD 3 1/9P", "15": "UNILEVER PLC ORD 3 1/9P", "16": "UNILEVER PLC ORD 3 1/9P", "17": "UNILEVER PLC ORD 3 1/9P", "18": "UNILEVER PLC ORD 3 1/9P", "19": "UNILEVER PLC ORD 3 1/9P", "20": "UNILEVER PLC ORD 3 1/9P", "21": "UNILEVER PLC ORD 3 1/9P", "22": "UNILEVER PLC ORD 3 1/9P", "23": "UNILEVER PLC ORD 3 1/9P", "24": "UNILEVER PLC ORD 3 1/9P", "25": "UNILEVER PLC ORD 3 1/9P", "26": "UNILEVER PLC ORD 3 1/9P", "27": "UNILEVER PLC ORD 3 1/9P", "28": "UNILEVER PLC ORD 3 1/9P", "29": "Z-FTSE 100 (New Style) Index Future", "30": "UNILEVER PLC ORD 3 1/9P", "31": "Equity Swap Portfolio_Swap_Single_Name GB00B10RZP78 GBP 20240308", "32": "UNIP SI 20240102 PS", "33": "AO1UN.2401.04800.P", "34": "AO1UN.2401.04800.C", "35": "Z-FTSE 100 (New Style) Index Future", "36": "UNILEVER PLC ORD 3 1/9P", "37": "UNILEVER PLC ORD 3 1/9P", "38": "UNILEVER PLC ORD 3 1/9P", "39": "UNILEVER PLC ORD 3 1/9P", "40": "UNILEVER PLC ORD 3 1/9P", "41": "UNILEVER PLC ORD 3 1/9P", "42": "UNILEVER PLC ORD 3 1/9P", "43": "UNILEVER PLC ORD 3 1/9P", "44": "UNILEVER PLC ORD 3 1/9P", "45": "UNILEVER PLC ORD 3 1/9P"}, "priceFormingData.initialQuantity": {"0": 500.0, "1": 500.0, "2": 500.0, "3": 500.0, "4": 500.0, "5": 500.0, "6": 500.0, "7": 500.0, "8": 500.0, "9": 500.0, "10": 50.0, "11": 50.0, "12": 500.0, "13": 500.0, "14": 50.0, "15": 500.0, "16": 500.0, "17": 50.0, "18": 500.0, "19": 500.0, "20": 500.0, "21": 500.0, "22": 500.0, "23": 500.0, "24": 500.0, "25": 500.0, "26": 500.0, "27": 500.0, "28": 500.0, "29": 500.0, "30": 500.0, "31": 500.0, "32": 500.0, "33": 500.0, "34": 500.0, "35": 500.0, "36": 500.0, "37": 500.0, "38": 500.0, "39": 500.0, "40": 500.0, "41": 500.0, "42": 500.0, "43": 500.0, "44": 500.0, "45": 500.0}, "counterparty.name": {"0": "Counterparty 1", "1": "Counterparty 1", "2": "Counterparty 1", "3": "Counterparty 1", "4": "Counterparty 1", "5": "Counterparty 1", "6": "Counterparty 1", "7": "Counterparty 1", "8": "Counterparty 1", "9": "Counterparty 1", "10": "Counterparty 1", "11": "Counterparty 1", "12": "Counterparty 1", "13": "Counterparty 1", "14": "Counterparty 1", "15": "Counterparty 1", "16": "Counterparty 1", "17": "Counterparty 1", "18": "Counterparty 1", "19": "Counterparty 1", "20": "Counterparty 1", "21": "Counterparty 1", "22": "Counterparty 1", "23": "Counterparty 1", "24": "Counterparty 1", "25": "Counterparty 1", "26": "Counterparty 1", "27": "Counterparty 1", "28": "Counterparty 1", "29": "Counterparty 1", "30": "Counterparty 1", "31": "Counterparty 1", "32": "Counterparty 1", "33": "Counterparty 1", "34": "Counterparty 1", "35": "Counterparty 1", "36": "Counterparty 1", "37": "Counterparty 1", "38": "Counterparty 1", "39": "Counterparty 1", "40": "Counterparty 1", "41": "Counterparty 1", "42": "Counterparty 1", "43": "Counterparty 1", "44": "Counterparty 1", "45": "Counterparty 1"}, "executionDetails.limitPrice": {"0": null, "1": 40.62, "2": 40.615, "3": 40.62, "4": 40.62, "5": 40.62, "6": 40.62, "7": 40.62, "8": 40.62, "9": 40.615, "10": 40.645, "11": 40.65, "12": 40.62, "13": 40.615, "14": null, "15": 40.62, "16": 40.615, "17": null, "18": 40.62, "19": 40.615, "20": 40.62, "21": 40.615, "22": 40.62, "23": 40.615, "24": null, "25": null, "26": null, "27": null, "28": null, "29": null, "30": null, "31": null, "32": null, "33": null, "34": null, "35": null, "36": null, "37": null, "38": null, "39": null, "40": null, "41": null, "42": null, "43": null, "44": null, "45": null}, "buyer.name": {"0": "Client 1", "1": "Client 1", "2": "Client 1", "3": "Client 1", "4": "Client 1", "5": "Client 1", "6": "Client 1", "7": "Client 1", "8": "Client 1", "9": "Client 1", "10": "Counterparty 1", "11": "Counterparty 1", "12": "Client 1", "13": "Client 1", "14": "Counterparty 1", "15": "Client 1", "16": "Client 1", "17": "Counterparty 1", "18": "Client 1", "19": "Client 1", "20": "Client 1", "21": "Client 1", "22": "Client 1", "23": "Client 1", "24": "Counterparty 1", "25": "Counterparty 1", "26": "Client 1", "27": "Counterparty 1", "28": "Client 1", "29": "Counterparty 1", "30": "Counterparty 1", "31": "Counterparty 1", "32": "Counterparty 1", "33": "Counterparty 1", "34": "Counterparty 1", "35": "Counterparty 1", "36": "Client 1", "37": "Counterparty 1", "38": "Counterparty 1", "39": "Client 1", "40": "Counterparty 1", "41": "Client 1", "42": "Counterparty 1", "43": "Client 1", "44": "Counterparty 1", "45": "Client 1"}, "seller.name": {"0": "Counterparty 1", "1": "Counterparty 1", "2": "Counterparty 1", "3": "Counterparty 1", "4": "Counterparty 1", "5": "Counterparty 1", "6": "Counterparty 1", "7": "Counterparty 1", "8": "Counterparty 1", "9": "Counterparty 1", "10": "Client 1", "11": "Client 1", "12": "Counterparty 1", "13": "Counterparty 1", "14": "Client 1", "15": "Counterparty 1", "16": "Counterparty 1", "17": "Client 1", "18": "Counterparty 1", "19": "Counterparty 1", "20": "Counterparty 1", "21": "Counterparty 1", "22": "Counterparty 1", "23": "Counterparty 1", "24": "Client 1", "25": "Client 1", "26": "Counterparty 1", "27": "Client 1", "28": "Counterparty 1", "29": "Client 1", "30": "Client 1", "31": "Client 1", "32": "Client 1", "33": "Client 1", "34": "Client 1", "35": "Client 1", "36": "Counterparty 1", "37": "Client 1", "38": "Client 1", "39": "Counterparty 1", "40": "Client 1", "41": "Counterparty 1", "42": "Client 1", "43": "Counterparty 1", "44": "Client 1", "45": "Counterparty 1"}, "clientIdentifiers.client.&id": {"0": "25971d62-a43d-5ae5-aaa1-b0b66c90c912", "1": "25971d62-a43d-5ae5-aaa1-b0b66c90c912", "2": "25971d62-a43d-5ae5-aaa1-b0b66c90c912", "3": "25971d62-a43d-5ae5-aaa1-b0b66c90c912", "4": "25971d62-a43d-5ae5-aaa1-b0b66c90c912", "5": "25971d62-a43d-5ae5-aaa1-b0b66c90c912", "6": "25971d62-a43d-5ae5-aaa1-b0b66c90c912", "7": "25971d62-a43d-5ae5-aaa1-b0b66c90c912", "8": "25971d62-a43d-5ae5-aaa1-b0b66c90c912", "9": "25971d62-a43d-5ae5-aaa1-b0b66c90c912", "10": "25971d62-a43d-5ae5-aaa1-b0b66c90c912", "11": "25971d62-a43d-5ae5-aaa1-b0b66c90c912", "12": "25971d62-a43d-5ae5-aaa1-b0b66c90c912", "13": "25971d62-a43d-5ae5-aaa1-b0b66c90c912", "14": "25971d62-a43d-5ae5-aaa1-b0b66c90c912", "15": "25971d62-a43d-5ae5-aaa1-b0b66c90c912", "16": "25971d62-a43d-5ae5-aaa1-b0b66c90c912", "17": "25971d62-a43d-5ae5-aaa1-b0b66c90c912", "18": "25971d62-a43d-5ae5-aaa1-b0b66c90c912", "19": "25971d62-a43d-5ae5-aaa1-b0b66c90c912", "20": "25971d62-a43d-5ae5-aaa1-b0b66c90c912", "21": "25971d62-a43d-5ae5-aaa1-b0b66c90c912", "22": "25971d62-a43d-5ae5-aaa1-b0b66c90c912", "23": "25971d62-a43d-5ae5-aaa1-b0b66c90c912", "24": "25971d62-a43d-5ae5-aaa1-b0b66c90c912", "25": "25971d62-a43d-5ae5-aaa1-b0b66c90c912", "26": "25971d62-a43d-5ae5-aaa1-b0b66c90c912", "27": "25971d62-a43d-5ae5-aaa1-b0b66c90c912", "28": "25971d62-a43d-5ae5-aaa1-b0b66c90c912", "29": "25971d62-a43d-5ae5-aaa1-b0b66c90c912", "30": "25971d62-a43d-5ae5-aaa1-b0b66c90c912", "31": "25971d62-a43d-5ae5-aaa1-b0b66c90c912", "32": "25971d62-a43d-5ae5-aaa1-b0b66c90c912", "33": "25971d62-a43d-5ae5-aaa1-b0b66c90c912", "34": "25971d62-a43d-5ae5-aaa1-b0b66c90c912", "35": "25971d62-a43d-5ae5-aaa1-b0b66c90c912", "36": "25971d62-a43d-5ae5-aaa1-b0b66c90c912", "37": "25971d62-a43d-5ae5-aaa1-b0b66c90c912", "38": "25971d62-a43d-5ae5-aaa1-b0b66c90c912", "39": "25971d62-a43d-5ae5-aaa1-b0b66c90c912", "40": "25971d62-a43d-5ae5-aaa1-b0b66c90c912", "41": "25971d62-a43d-5ae5-aaa1-b0b66c90c912", "42": "25971d62-a43d-5ae5-aaa1-b0b66c90c912", "43": "25971d62-a43d-5ae5-aaa1-b0b66c90c912", "44": "25971d62-a43d-5ae5-aaa1-b0b66c90c912", "45": "25971d62-a43d-5ae5-aaa1-b0b66c90c912"}, "clientIdentifiers.client.name": {"0": "Client 1", "1": "Client 1", "2": "Client 1", "3": "Client 1", "4": "Client 1", "5": "Client 1", "6": "Client 1", "7": "Client 1", "8": "Client 1", "9": "Client 1", "10": "Client 1", "11": "Client 1", "12": "Client 1", "13": "Client 1", "14": "Client 1", "15": "Client 1", "16": "Client 1", "17": "Client 1", "18": "Client 1", "19": "Client 1", "20": "Client 1", "21": "Client 1", "22": "Client 1", "23": "Client 1", "24": "Client 1", "25": "Client 1", "26": "Client 1", "27": "Client 1", "28": "Client 1", "29": "Client 1", "30": "Client 1", "31": "Client 1", "32": "Client 1", "33": "Client 1", "34": "Client 1", "35": "Client 1", "36": "Client 1", "37": "Client 1", "38": "Client 1", "39": "Client 1", "40": "Client 1", "41": "Client 1", "42": "Client 1", "43": "Client 1", "44": "Client 1", "45": "Client 1"}, "__client__": {"0": "Client 1", "1": "Client 1", "2": "Client 1", "3": "Client 1", "4": "Client 1", "5": "Client 1", "6": "Client 1", "7": "Client 1", "8": "Client 1", "9": "Client 1", "10": "Client 1", "11": "Client 1", "12": "Client 1", "13": "Client 1", "14": "Client 1", "15": "Client 1", "16": "Client 1", "17": "Client 1", "18": "Client 1", "19": "Client 1", "20": "Client 1", "21": "Client 1", "22": "Client 1", "23": "Client 1", "24": "Client 1", "25": "Client 1", "26": "Client 1", "27": "Client 1", "28": "Client 1", "29": "Client 1", "30": "Client 1", "31": "Client 1", "32": "Client 1", "33": "Client 1", "34": "Client 1", "35": "Client 1", "36": "Client 1", "37": "Client 1", "38": "Client 1", "39": "Client 1", "40": "Client 1", "41": "Client 1", "42": "Client 1", "43": "Client 1", "44": "Client 1", "45": "Client 1"}, "trader.&id": {"0": null, "1": null, "2": null, "3": null, "4": null, "5": null, "6": null, "7": null, "8": null, "9": null, "10": null, "11": null, "12": null, "13": null, "14": null, "15": null, "16": null, "17": null, "18": null, "19": null, "20": null, "21": null, "22": null, "23": null, "24": null, "25": null, "26": null, "27": null, "28": null, "29": null, "30": null, "31": null, "32": null, "33": null, "34": null, "35": null, "36": null, "37": null, "38": null, "39": null, "40": null, "41": null, "42": null, "43": null, "44": null, "45": null}, "trader.name": {"0": "Trader 12", "1": "Trader 1", "2": "Trader 1", "3": "Trader 1", "4": "Trader 1", "5": "Trader 1", "6": "Trader 1", "7": "Trader 1", "8": "Trader 1", "9": "Trader 1", "10": "Trader 1", "11": "Trader 1", "12": "Trader 1", "13": "Trader 1", "14": "Trader 1", "15": "Trader 1", "16": "Trader 1", "17": "Trader 1", "18": "Trader 1", "19": "Trader 1", "20": "Trader 1", "21": "Trader 1", "22": "Trader 1", "23": "Trader 1", "24": "Trader 12", "25": "Trader 1", "26": "Trader 1", "27": "Trader 1", "28": "Trader 1", "29": "Trader 1", "30": "Trader 1", "31": "Trader 1", "32": "Trader 1", "33": "Trader 1", "34": "Trader 1", "35": "Trader 1", "36": "Trader 1", "37": "Trader 1", "38": "Trader 1", "39": "Trader 1", "40": "Trader 1", "41": "Trader 1", "42": "Trader 1", "43": "Trader 1", "44": "Trader 1", "45": "Trader 1"}, "__trader_id__": {"0": null, "1": null, "2": null, "3": null, "4": null, "5": null, "6": null, "7": null, "8": null, "9": null, "10": null, "11": null, "12": null, "13": null, "14": null, "15": null, "16": null, "17": null, "18": null, "19": null, "20": null, "21": null, "22": null, "23": null, "24": null, "25": null, "26": null, "27": null, "28": null, "29": null, "30": null, "31": null, "32": null, "33": null, "34": null, "35": null, "36": null, "37": null, "38": null, "39": null, "40": null, "41": null, "42": null, "43": null, "44": null, "45": null}, "__trader_name__": {"0": "Trader 12", "1": "Trader 1", "2": "Trader 1", "3": "Trader 1", "4": "Trader 1", "5": "Trader 1", "6": "Trader 1", "7": "Trader 1", "8": "Trader 1", "9": "Trader 1", "10": "Trader 1", "11": "Trader 1", "12": "Trader 1", "13": "Trader 1", "14": "Trader 1", "15": "Trader 1", "16": "Trader 1", "17": "Trader 1", "18": "Trader 1", "19": "Trader 1", "20": "Trader 1", "21": "Trader 1", "22": "Trader 1", "23": "Trader 1", "24": "Trader 12", "25": "Trader 1", "26": "Trader 1", "27": "Trader 1", "28": "Trader 1", "29": "Trader 1", "30": "Trader 1", "31": "Trader 1", "32": "Trader 1", "33": "Trader 1", "34": "Trader 1", "35": "Trader 1", "36": "Trader 1", "37": "Trader 1", "38": "Trader 1", "39": "Trader 1", "40": "Trader 1", "41": "Trader 1", "42": "Trader 1", "43": "Trader 1", "44": "Trader 1", "45": "Trader 1"}, "__instrument_code__": {"0": "GB00B10RZP78", "1": "GB00B10RZP78", "2": "GB00B10RZP78", "3": "GB00B10RZP78", "4": "GB00B10RZP78", "5": "GB00B10RZP78", "6": "GB00B10RZP78", "7": "GB00B10RZP78", "8": "GB00B10RZP78", "9": "GB00B10RZP78", "10": "GB00B10RZP78", "11": "GB00B10RZP78", "12": "GB00B10RZP78", "13": "GB00B10RZP78", "14": "GB00B10RZP78", "15": "GB00B10RZP78", "16": "GB00B10RZP78", "17": "GB00B10RZP78", "18": "GB00B10RZP78", "19": "GB00B10RZP78", "20": "GB00B10RZP78", "21": "GB00B10RZP78", "22": "GB00B10RZP78", "23": "GB00B10RZP78", "24": "GB00B10RZP78", "25": "GB00B10RZP78", "26": "GB00B10RZP78", "27": "GB00B10RZP78", "28": "GB00B10RZP78", "29": "GB00KHK64W72", "30": "GB00B10RZP78", "31": "EZH0QV8H92H2", "32": "DE000F0JT1N3", "33": "NLEN13073841", "34": "NLEN13073775", "35": "GB00KHLXXF30", "36": "GB00B10RZP78", "37": "GB00B10RZP78", "38": "GB00B10RZP78", "39": "GB00B10RZP78", "40": "GB00B10RZP78", "41": "GB00B10RZP78", "42": "GB00B10RZP78", "43": "GB00B10RZP78", "44": "GB00B10RZP78", "45": "GB00B10RZP78"}, "__venue__": {"0": "XLON", "1": "XLON", "2": "XLON", "3": "XLON", "4": "XLON", "5": "XLON", "6": "XLON", "7": "XLON", "8": "XLON", "9": "XLON", "10": "XLON", "11": "XLON", "12": "XLON", "13": "XLON", "14": "XLON", "15": "XLON", "16": "XLON", "17": "XLON", "18": "XLON", "19": "XLON", "20": "XLON", "21": "XLON", "22": "XLON", "23": "XLON", "24": "XLON", "25": "XLON", "26": "XLON", "27": "XLON", "28": "XLON", "29": "IFLL", "30": "XLON", "31": "XXXX", "32": "XEUR", "33": "XEUE", "34": "XEUE", "35": "IFLL", "36": "XLON", "37": "XLON", "38": "XLON", "39": "XLON", "40": "XLON", "41": "XLON", "42": "XLON", "43": "XLON", "44": "XLON", "45": "XLON"}, "tradersAlgosWaiversIndicators.investmentDecisionWithinFirm.structure.desks.id": {"0": null, "1": null, "2": null, "3": null, "4": null, "5": null, "6": null, "7": null, "8": null, "9": null, "10": null, "11": null, "12": null, "13": null, "14": null, "15": null, "16": null, "17": null, "18": null, "19": null, "20": null, "21": null, "22": null, "23": null, "24": null, "25": null, "26": null, "27": null, "28": null, "29": null, "30": null, "31": null, "32": null, "33": null, "34": null, "35": null, "36": null, "37": null, "38": null, "39": null, "40": null, "41": null, "42": null, "43": null, "44": null, "45": null}, "tradersAlgosWaiversIndicators.investmentDecisionWithinFirm.structure.desks.name": {"0": "desk1", "1": "desk1", "2": "desk2", "3": "desk1", "4": "desk1", "5": "desk1", "6": "desk1", "7": "desk1", "8": "desk1", "9": "desk2", "10": "desk2", "11": "desk2", "12": "desk1", "13": "desk1", "14": "desk1", "15": "desk1", "16": "desk1", "17": "desk1", "18": "desk1", "19": "desk2", "20": "desk1", "21": "desk2", "22": "desk1", "23": "desk1", "24": "desk1", "25": "desk1", "26": "desk2", "27": "desk1", "28": "desk2", "29": "desk1", "30": "desk1", "31": "desk1", "32": "desk1", "33": "desk1", "34": "desk1", "35": "desk1", "36": "desk1", "37": "desk2", "38": "desk1", "39": "desk2", "40": "desk1", "41": "desk2", "42": "desk1", "43": "desk2", "44": "desk1", "45": "desk2"}, "Index ISIN": {"0": null, "1": null, "2": null, "3": null, "4": null, "5": null, "6": null, "7": null, "8": null, "9": null, "10": null, "11": null, "12": null, "13": null, "14": null, "15": null, "16": null, "17": null, "18": null, "19": null, "20": null, "21": null, "22": null, "23": null, "24": null, "25": null, "26": null, "27": null, "28": null, "29": "GB0001383545", "30": null, "31": null, "32": null, "33": null, "34": null, "35": "GB0001383545", "36": null, "37": null, "38": null, "39": null, "40": null, "41": null, "42": null, "43": null, "44": null, "45": null}, "relatedType": {"0": "explicitlyRelated", "1": "explicitlyRelated", "2": "explicitlyRelated", "3": "explicitlyRelated", "4": "explicitlyRelated", "5": "explicitlyRelated", "6": "explicitlyRelated", "7": "explicitlyRelated", "8": "explicitlyRelated", "9": "explicitlyRelated", "10": "explicitlyRelated", "11": "explicitlyRelated", "12": "explicitlyRelated", "13": "explicitlyRelated", "14": "explicitlyRelated", "15": "explicitlyRelated", "16": "explicitlyRelated", "17": "explicitlyRelated", "18": "explicitlyRelated", "19": "explicitlyRelated", "20": "explicitlyRelated", "21": "explicitlyRelated", "22": "explicitlyRelated", "23": "explicitlyRelated", "24": "explicitlyRelated", "25": "explicitlyRelated", "26": "explicitlyRelated", "27": "explicitlyRelated", "28": "explicitlyRelated", "29": "explicitlyCorrelated", "30": "explicitlyRelated", "31": "explicitlyRelated", "32": "explicitlyRelated", "33": "explicitlyRelated", "34": "explicitlyRelated", "35": "explicitlyCorrelated", "36": "explicitlyRelated", "37": "explicitlyRelated", "38": "explicitlyRelated", "39": "explicitlyRelated", "40": "explicitlyRelated", "41": "explicitlyRelated", "42": "explicitlyRelated", "43": "explicitlyRelated", "44": "explicitlyRelated", "45": "explicitlyRelated"}, "instrumentDetails.instrument.ext.underlyingInstruments.instrumentIdCode": {"0": null, "1": null, "2": null, "3": null, "4": null, "5": null, "6": null, "7": null, "8": null, "9": null, "10": null, "11": null, "12": null, "13": null, "14": null, "15": null, "16": null, "17": null, "18": null, "19": null, "20": null, "21": null, "22": null, "23": null, "24": null, "25": null, "26": null, "27": null, "28": null, "29": null, "30": null, "31": null, "32": null, "33": null, "34": null, "35": null, "36": null, "37": null, "38": null, "39": null, "40": null, "41": null, "42": null, "43": null, "44": null, "45": null}, "originalRecordMetaKey": {"0": "Order:spoofingV2_18_1:1:NEWO:1705925118102", "1": "Order:spoofingV2_18_1:1:NEWO:1705925118102", "2": "Order:spoofingV2_18_1:1:NEWO:1705925118102", "3": "Order:spoofingV2_18_1:1:NEWO:1705925118102", "4": "Order:spoofingV2_18_1:1:NEWO:1705925118102", "5": "Order:spoofingV2_18_1:1:NEWO:1705925118102", "6": "Order:spoofingV2_18_1:1:NEWO:1705925118102", "7": "Order:spoofingV2_18_1:1:NEWO:1705925118102", "8": "Order:spoofingV2_18_1:1:NEWO:1705925118102", "9": "Order:spoofingV2_18_1:1:NEWO:1705925118102", "10": "Order:spoofingV2_18_1:1:NEWO:1705925118102", "11": "Order:spoofingV2_18_1:1:NEWO:1705925118102", "12": "Order:spoofingV2_18_1:1:NEWO:1705925118102", "13": "Order:spoofingV2_18_1:1:NEWO:1705925118102", "14": "Order:spoofingV2_18_1:1:NEWO:1705925118102", "15": "Order:spoofingV2_18_1:1:NEWO:1705925118102", "16": "Order:spoofingV2_18_1:1:NEWO:1705925118102", "17": "Order:spoofingV2_18_1:1:NEWO:1705925118102", "18": "Order:spoofingV2_18_1:1:NEWO:1705925118102", "19": "Order:spoofingV2_18_1:1:NEWO:1705925118102", "20": "Order:spoofingV2_18_1:1:NEWO:1705925118102", "21": "Order:spoofingV2_18_1:1:NEWO:1705925118102", "22": "Order:spoofingV2_18_1:1:NEWO:1705925118102", "23": "Order:spoofingV2_18_1:1:NEWO:1705925118102", "24": "Order:spoofingV2_18_1:1:NEWO:1705925118102", "25": "Order:spoofingV2_18_1:1:NEWO:1705925118102", "26": "Order:spoofingV2_18_1:1:NEWO:1705925118102", "27": "Order:spoofingV2_18_1:1:NEWO:1705925118102", "28": "Order:spoofingV2_18_1:1:NEWO:1705925118102", "29": "Order:spoofingV2_18_1:1:NEWO:1705925118102", "30": "Order:spoofingV2_18_1:1:NEWO:1705925118102", "31": "Order:spoofingV2_18_1:1:NEWO:1705925118102", "32": "Order:spoofingV2_18_1:1:NEWO:1705925118102", "33": "Order:spoofingV2_18_1:1:NEWO:1705925118102", "34": "Order:spoofingV2_18_1:1:NEWO:1705925118102", "35": "Order:spoofingV2_18_1:1:NEWO:1705925118102", "36": "Order:spoofingV2_18_1:1:NEWO:1705925118102", "37": "Order:spoofingV2_18_1:1:NEWO:1705925118102", "38": "Order:spoofingV2_18_1:1:NEWO:1705925118102", "39": "Order:spoofingV2_18_1:1:NEWO:1705925118102", "40": "Order:spoofingV2_18_1:1:NEWO:1705925118102", "41": "Order:spoofingV2_18_1:1:NEWO:1705925118102", "42": "Order:spoofingV2_18_1:1:NEWO:1705925118102", "43": "Order:spoofingV2_18_1:1:NEWO:1705925118102", "44": "Order:spoofingV2_18_1:1:NEWO:1705925118102", "45": "Order:spoofingV2_18_1:1:NEWO:1705925118102"}, "instrumentDetails.instrument.issuerOrOperatorOfTradingVenueId": {"0": "549300MKFYEKVRWML317", "1": "549300MKFYEKVRWML317", "2": "549300MKFYEKVRWML317", "3": "549300MKFYEKVRWML317", "4": "549300MKFYEKVRWML317", "5": "549300MKFYEKVRWML317", "6": "549300MKFYEKVRWML317", "7": "549300MKFYEKVRWML317", "8": "549300MKFYEKVRWML317", "9": "549300MKFYEKVRWML317", "10": "549300MKFYEKVRWML317", "11": "549300MKFYEKVRWML317", "12": "549300MKFYEKVRWML317", "13": "549300MKFYEKVRWML317", "14": "549300MKFYEKVRWML317", "15": "549300MKFYEKVRWML317", "16": "549300MKFYEKVRWML317", "17": "549300MKFYEKVRWML317", "18": "549300MKFYEKVRWML317", "19": "549300MKFYEKVRWML317", "20": "549300MKFYEKVRWML317", "21": "549300MKFYEKVRWML317", "22": "549300MKFYEKVRWML317", "23": "549300MKFYEKVRWML317", "24": "549300MKFYEKVRWML317", "25": "549300MKFYEKVRWML317", "26": "549300MKFYEKVRWML317", "27": "549300MKFYEKVRWML317", "28": "549300MKFYEKVRWML317", "29": null, "30": "549300MKFYEKVRWML317", "31": null, "32": "529900UT4DG0LG5R9O07", "33": "724500V6UOK62XEZ2L78", "34": "724500V6UOK62XEZ2L78", "35": null, "36": "549300MKFYEKVRWML317", "37": "549300MKFYEKVRWML317", "38": "549300MKFYEKVRWML317", "39": "549300MKFYEKVRWML317", "40": "549300MKFYEKVRWML317", "41": "549300MKFYEKVRWML317", "42": "549300MKFYEKVRWML317", "43": "549300MKFYEKVRWML317", "44": "549300MKFYEKVRWML317", "45": "549300MKFYEKVRWML317"}, "underlyingColumn": {"0": null, "1": null, "2": null, "3": null, "4": null, "5": null, "6": null, "7": null, "8": null, "9": null, "10": null, "11": null, "12": null, "13": null, "14": null, "15": null, "16": null, "17": null, "18": null, "19": null, "20": null, "21": null, "22": null, "23": null, "24": null, "25": null, "26": null, "27": null, "28": null, "29": "GB0001383545", "30": null, "31": null, "32": null, "33": null, "34": null, "35": "GB0001383545", "36": null, "37": null, "38": null, "39": null, "40": null, "41": null, "42": null, "43": null, "44": null, "45": null}, "instrumentDetails.instrument.ext.exchangeSymbolRoot": {"0": null, "1": null, "2": null, "3": null, "4": null, "5": null, "6": null, "7": null, "8": null, "9": null, "10": null, "11": null, "12": null, "13": null, "14": null, "15": null, "16": null, "17": null, "18": null, "19": null, "20": null, "21": null, "22": null, "23": null, "24": null, "25": null, "26": null, "27": null, "28": null, "29": null, "30": null, "31": null, "32": null, "33": null, "34": null, "35": null, "36": null, "37": null, "38": null, "39": null, "40": null, "41": null, "42": null, "43": null, "44": null, "45": null}, "originalISIN": {"0": null, "1": null, "2": null, "3": null, "4": null, "5": null, "6": null, "7": null, "8": null, "9": null, "10": null, "11": null, "12": null, "13": null, "14": null, "15": null, "16": null, "17": null, "18": null, "19": null, "20": null, "21": null, "22": null, "23": null, "24": null, "25": null, "26": null, "27": null, "28": null, "29": "GB00B10RZP78", "30": null, "31": null, "32": null, "33": null, "34": null, "35": "GB00B10RZP78", "36": null, "37": null, "38": null, "39": null, "40": null, "41": null, "42": null, "43": null, "44": null, "45": null}, "relatedSubType": {"0": "issuer", "1": "issuer", "2": "issuer", "3": "issuer", "4": "issuer", "5": "issuer", "6": "issuer", "7": "issuer", "8": "issuer", "9": "issuer", "10": "issuer", "11": "issuer", "12": "issuer", "13": "issuer", "14": "issuer", "15": "issuer", "16": "issuer", "17": "issuer", "18": "issuer", "19": "issuer", "20": "issuer", "21": "issuer", "22": "issuer", "23": "issuer", "24": "issuer", "25": "issuer", "26": "issuer", "27": "issuer", "28": "issuer", "29": "sameUnderlying", "30": "issuer", "31": "isinToUnderlying", "32": "isinToUnderlying", "33": "isinToUnderlying", "34": "isinToUnderlying", "35": "sameUnderlying", "36": "issuer", "37": "issuer", "38": "issuer", "39": "issuer", "40": "issuer", "41": "issuer", "42": "issuer", "43": "issuer", "44": "issuer", "45": "issuer"}, "instrumentDetails.instrument.derivative.underlyingInstruments.underlyingInstrumentCode": {"0": null, "1": null, "2": null, "3": null, "4": null, "5": null, "6": null, "7": null, "8": null, "9": null, "10": null, "11": null, "12": null, "13": null, "14": null, "15": null, "16": null, "17": null, "18": null, "19": null, "20": null, "21": null, "22": null, "23": null, "24": null, "25": null, "26": null, "27": null, "28": null, "29": null, "30": null, "31": ["GB00B10RZP78"], "32": ["GB00B10RZP78"], "33": ["GB00B10RZP78"], "34": ["GB00B10RZP78"], "35": null, "36": null, "37": null, "38": null, "39": null, "40": null, "41": null, "42": null, "43": null, "44": null, "45": null}, "relatedInstrument": {"0": true, "1": true, "2": true, "3": true, "4": true, "5": true, "6": true, "7": true, "8": true, "9": true, "10": true, "11": true, "12": true, "13": true, "14": true, "15": true, "16": true, "17": true, "18": true, "19": true, "20": true, "21": true, "22": true, "23": true, "24": true, "25": true, "26": true, "27": true, "28": true, "29": true, "30": true, "31": true, "32": true, "33": true, "34": true, "35": true, "36": true, "37": true, "38": true, "39": true, "40": true, "41": true, "42": true, "43": true, "44": true, "45": true}}