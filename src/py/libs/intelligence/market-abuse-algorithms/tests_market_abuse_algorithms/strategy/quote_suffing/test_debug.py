import pytest
from market_abuse_algorithms.strategy.quote_stuffing.strategy import Strategy
from pathlib import Path

TEST_DATA = Path(__file__).parent.joinpath("test_data")


@pytest.mark.usefixtures("skip_test_in_ci")
class TestDebuggeQuoteStuffing:
    def test_case_debug(self, helpers):
        thresholds = {
            "arrivalCancelTime": 60,
            "arrivalTimeWindow": 60,
            "minimumCancelledOrders": 5,
            "assetClass": "",
        }

        filters = {}

        context = helpers.get_context(thresholds=thresholds, filters=filters)

        strategy = Strategy(context=context)

        strategy.run()

        scenarios = strategy.scenarios

        scenarios
