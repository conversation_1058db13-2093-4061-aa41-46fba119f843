from enum import Enum


class DictUseCase(Enum):
    GROUPING_FIELDS = "groupFields"
    THRESHOLDS = "thresholds"
    FILTERS = "filters"
    SPECIFIC_THRESHOLDS = "specificThresholds"
    MARKET_DATA_FILE = "marketDataFile"
    TENANT_DATA_FILE = "tenantDataFile"
    TICK_DATA_FILE = "tickDataFile"
    ADV_PERCENTAGE = "advPercentage"
    MARKET_COMPARISON_VALUE = "marketComparisonValue"
    MARKET_COMPARISON_PARAMETER = "marketComparisonParameter"
    MARKET_PRICE_PERCENTAGE = "marketPricePercentage"
    MARKET_PRICE_IMPROVEMENT = "marketPriceImprovement"
    MARKET_DATA_FAILURE = "marketDatFailure"
    RESULT_DATAFRAME_SHAPE = "resultDataFrameShape"


class MarketDataFailureReason(Enum):
    ADV = "adv"
    MARKET_PRICE_IMPACT = "marketPriceImpact"
