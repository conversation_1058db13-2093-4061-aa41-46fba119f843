# ruff: noqa: E501
# type: ignore
import market_abuse_algorithms.strategy.suspicious_large_order_volume_v2.query
import market_abuse_algorithms.strategy.suspicious_large_order_volume_v2.strategy
import pandas as pd
import pyarrow as pa
from market_abuse_algorithms.data_source.repository.market_data.client import (
    MarketDataClient,
)
from market_abuse_algorithms.strategy.suspicious_large_order_volume_v2.strategy import (
    Strategy,
)
from pathlib import Path
from se_market_data_utils.schema.refinitiv import RefinitivEventType
from tests_market_abuse_algorithms.strategy.suspicious_large_order_volume_v2.mock_data import (
    custom_date_parser,
)
from typing import Optional
from unittest.mock import MagicMock

TEST_DATA_END_TO_END = Path(__file__).parent.joinpath("test_data_end_to_end")


def fake_get_eod_stats(
    date_from=None,
    date_to=None,
    ric="",
    columns=None,
    market_client=None,
    pa_expression=None,
):
    eod_file = None
    if ric == "GOOG.OQ":
        eod_file = "test_case_4_1_get_market_data_stats.csv"
    elif ric == "BABA.N":
        eod_file = "test_case_5_5_get_market_data_stats.csv"
    elif ric == "AMC.N":
        eod_file = "test_case_intraday_threshold_get_market_data_stats.csv"
    elif ric == "278865BJ8=RRPS":
        eod_file = "test_zero_adtv_that_shouldnt_generate_alerts_get_market_data_stats.csv"

    if eod_file is not None:
        return pa.dataset.dataset(TEST_DATA_END_TO_END.joinpath(eod_file), format="csv").to_table()

    return pa.Table.from_pandas(df=pd.DataFrame())


def fake_get_eod_stats_with_margin(
    market_data_api=None,
    start_date=None,
    end_date=None,
    ric="",
):
    eod_file = None
    if ric == "278865BJ8=RRPS":
        eod_file = "test_zero_adtv_that_shouldnt_generate_alerts_get_market_data_stats.csv"

    if eod_file is not None:
        return (
            pa.dataset.dataset(TEST_DATA_END_TO_END.joinpath(eod_file), format="csv")
            .to_table()
            .to_pandas()
        )

    return pa.Table.from_pandas(df=pd.DataFrame())


class FakeMarketClient(MarketDataClient):
    def get_tick_data(
        self,
        dates,
        event_type,
        instrument_unique_identifier: Optional[str] = None,
        instrument_ric: Optional[str] = None,
        result_between_dates: bool = False,
        nearest_timestamp: bool = False,
    ):
        tick_file = None
        if instrument_ric == "GOOG.OQ":
            tick_file = "test_case5_5_market_data_client.get_ticks.csv"
        elif instrument_ric == "BABA.N":
            tick_file = "test_case5_5_market_data_client.get_ticks.csv"
        elif instrument_ric == "AMC.N":
            if event_type == RefinitivEventType.QUOTE:
                tick_file = "test_case_intraday_threshold_market_data_client.get_ticks_quote.csv"
            elif event_type == RefinitivEventType.TRADE:
                tick_file = "test_case_intraday_threshold_market_data_client.get_ticks_trade.csv"
        elif instrument_ric == "NKLA.OQ":
            if event_type == RefinitivEventType.QUOTE:
                tick_file = "test_intraday_threshold_check_volume_one_alert_quote_tick_data.csv"
            elif event_type == RefinitivEventType.TRADE:
                tick_file = "test_intraday_threshold_check_volume_one_alert_trade_tick_data.csv"

        elif instrument_ric == "IE217804353=RRPS":
            if event_type == RefinitivEventType.QUOTE:
                tick_file = "test_intraday_threshold_empty_volume_zero_alerts_quote_tick_data.csv"
            elif event_type == RefinitivEventType.TRADE:
                tick_file = None

        if tick_file is not None:
            if instrument_ric == "IE217804353=RRPS" or instrument_ric == "NKLA.OQ":
                df = pd.read_csv(TEST_DATA_END_TO_END.joinpath(tick_file), index_col=0)
                df["Date-Time"] = df["Date-Time"].apply(lambda x: pd.Timestamp(x))
                return df

            return pd.read_csv(
                TEST_DATA_END_TO_END.joinpath(tick_file),
                index_col=0,
                parse_dates=["Date-Time"],
                date_parser=custom_date_parser(),
            )
        return pd.DataFrame()


class TestSLOV2EndToEnd:
    def test_case_4_1(self, helpers, monkeypatch):
        thresholds = {
            "percentageAdv": 0.03,
            "quantityEvaluationType": "orderQuantity",
            "dayAndOrderEvaluationType": "order",
            "minimumQuantity": 10,
            "categoryEvaluationType": "market",
            "marketDataEvaluationType": "dayTradedVolume",
        }

        filters = {
            "bool": {
                "must": {
                    "terms": {
                        "sourceKey": [
                            "s3://mar.dev.steeleye.co/flows/order-universal-steeleye-trade-blotter/steeleyeBlotter.mar.sloev.4.csv"
                        ]
                    }
                }
            }
        }

        def mock_get_cases_to_analyse():
            return pd.read_csv(
                TEST_DATA_END_TO_END.joinpath("test_case_4_1_get_cases_to_analyse.csv"),
                index_col=0,
                parse_dates=[
                    "timestamps.orderStatusUpdated",
                    "timestamps.tradingDateTime",
                    "timestamps.orderSubmitted",
                ],
                date_parser=custom_date_parser(),
            )

        def mock_check_parent_and_child_orders():
            return {
                "orderStatesKeys": [
                    "OrderState:sloev_4_1:1:SLOEV41SLOEV4120220824BUYI:PARF:2022-08-24T12:00:00Z:0.0:*************"
                ],
                "executionsDetected": 1,
                "ordersKeys": ["Order:sloev_4_1:1:NEWO:*************"],
                "ordersDetected": 1,
            }

        import market_abuse_algorithms.data_source.repository.market_data.utils

        monkeypatch.setattr(
            market_abuse_algorithms.data_source.repository.market_data.utils,
            "get_eod_stats",
            fake_get_eod_stats,
        )

        market_abuse_algorithms.strategy.suspicious_large_order_volume_v2.query.get_market_client = MagicMock()
        market_abuse_algorithms.strategy.suspicious_large_order_volume_v2.query.get_market_client.return_value = FakeMarketClient()

        context = helpers.get_context(thresholds=thresholds, filters=filters)

        strategy = Strategy(context=context)

        strategy.queries.cases_to_analyse = MagicMock()
        strategy.queries.cases_to_analyse.return_value = [mock_get_cases_to_analyse()]

        strategy.check_parent_and_child_orders = MagicMock()
        strategy.check_parent_and_child_orders.side_effect = [
            mock_check_parent_and_child_orders(),
            mock_check_parent_and_child_orders(),
        ]

        strategy.context.commit_audit = False

        strategy.run()

        scenarios = strategy.scenarios

        assert len(scenarios) == 2

        assert scenarios[0].json.get("records") == {
            "orders": ["Order:sloev_4_1:1:NEWO:*************"],
            "executions": [
                "OrderState:sloev_4_1:1:SLOEV41SLOEV4120220824BUYI:PARF:2022-08-24T12:00:00Z:0.0:*************"
            ],
        }

        assert scenarios[1].json.get("records") == {
            "orders": ["Order:sloev_4_1:1:NEWO:*************"],
            "executions": [
                "OrderState:sloev_4_1:1:SLOEV41SLOEV4120220824BUYI:PARF:2022-08-24T12:00:00Z:0.0:*************"
            ],
        }

        assert scenarios[0].json.get("additionalFields") == {
            "topLevel": {
                "ric": "GOOG.OQ",
                "executionsDetected": 1,
                "ordersDetected": 1,
                "earliestOrderTimestamp": "2022-08-24T12:00:00.000",
                "instrumentNameList": ["ALPHABET CL C ORD"],
                "instrumentDetected": 1,
                "totalOrderQuantity": 200000.0,
                "clientList": ["account:client1"],
                "counterpartyList": ["account:counterparty1"],
                "traderList": ["account:trader1"],
                "deskList": ["desk1"],
                "advPercentage": [0.*********],
                "adv": [5163752.0],
                "marketPriceImprovement": [False],
                "marketImpactPercentagePrice": [0.0],
                "marketImpactPercentagePriceAbs": [0.0],
                "nearestMidBefore": [89.635],
                "nearestMidAfter": [89.635],
            }
        }

        assert scenarios[1].json.get("additionalFields") == {
            "topLevel": {
                "ric": "GOOG.OQ",
                "executionsDetected": 1,
                "ordersDetected": 1,
                "earliestOrderTimestamp": "2022-08-24T12:00:00.000",
                "instrumentNameList": ["ALPHABET CL C ORD"],
                "instrumentDetected": 1,
                "totalOrderQuantity": 200000.0,
                "clientList": ["account:client1"],
                "counterpartyList": ["account:counterparty1"],
                "traderList": ["account:trader1"],
                "deskList": ["desk1"],
                "advPercentage": [0.*********],
                "adv": [5163752.0],
                "marketPriceImprovement": [False],
                "marketImpactPercentagePrice": [0.0],
                "marketImpactPercentagePriceAbs": [0.0],
                "nearestMidBefore": [89.635],
                "nearestMidAfter": [89.635],
            }
        }

    def test_case_5_5(self, helpers, monkeypatch):
        thresholds = {
            "percentageAdv": 0.38,
            "quantityEvaluationType": "orderQuantity",
            "dayAndOrderEvaluationType": "day",
            "minimumQuantity": 400001,
            "categoryEvaluationType": "market",
            "marketDataEvaluationType": "dayTradedVolume",
            "generalEvaluationType": "executingEntity",
        }

        filters = {
            "bool": {
                "must": {
                    "terms": {
                        "sourceKey": [
                            "s3://mar.dev.steeleye.co/flows/order-universal-steeleye-trade-blotter/steeleyeBlotter.mar.sloev.5.csv"
                        ]
                    }
                }
            }
        }

        def mock_get_cases_to_analyse():
            return pd.read_csv(
                TEST_DATA_END_TO_END.joinpath("test_case_5_5_get_cases_to_analyse.csv"),
                index_col=0,
                parse_dates=[
                    "timestamps.orderStatusUpdated",
                    "timestamps.tradingDateTime",
                    "timestamps.orderSubmitted",
                ],
                date_parser=custom_date_parser(),
            )

        def mock_check_parent_and_child_orders():
            return {
                "orderStatesKeys": [
                    "OrderState:sloev_5_1:1:SLOEV51SLOEV5120220819BUYI:PARF:2022-08-19T12:00:00Z:200000.0:1689763673513",
                    "OrderState:sloev_5_2:1:SLOEV52SLOEV5220220819BUYI:PARF:2022-08-19T12:00:00Z:200000.0:1689763673513",
                ],
                "executionsDetected": 2,
                "ordersKeys": [
                    "Order:sloev_5_1:1:NEWO:1689763673513",
                    "Order:sloev_5_2:1:NEWO:1689763673513",
                ],
                "ordersDetected": 2,
            }

        import market_abuse_algorithms.data_source.repository.market_data.utils

        monkeypatch.setattr(
            market_abuse_algorithms.data_source.repository.market_data.utils,
            "get_eod_stats",
            fake_get_eod_stats,
        )

        market_abuse_algorithms.strategy.suspicious_large_order_volume_v2.query.get_market_client = MagicMock()
        market_abuse_algorithms.strategy.suspicious_large_order_volume_v2.query.get_market_client.return_value = FakeMarketClient()

        monkeypatch.setattr(
            market_abuse_algorithms.data_source.repository.market_data.client,
            "get_eod_stats",
            fake_get_eod_stats,
        )

        context = helpers.get_context(thresholds=thresholds, filters=filters)

        strategy = Strategy(context=context)

        strategy.queries.cases_to_analyse = MagicMock()
        strategy.queries.cases_to_analyse.return_value = [mock_get_cases_to_analyse()]

        strategy.check_parent_and_child_orders = MagicMock()
        strategy.check_parent_and_child_orders.side_effect = [
            mock_check_parent_and_child_orders(),
            mock_check_parent_and_child_orders(),
        ]

        strategy.run()

        scenarios = strategy.scenarios

        assert len(scenarios) == 1

        assert scenarios[0].json.get("records") == {
            "orders": [
                "Order:sloev_5_1:1:NEWO:1689763673513",
                "Order:sloev_5_2:1:NEWO:1689763673513",
            ],
            "executions": [
                "OrderState:sloev_5_1:1:SLOEV51SLOEV5120220819BUYI:PARF:2022-08-19T12:00:00Z:200000.0:1689763673513",
                "OrderState:sloev_5_2:1:SLOEV52SLOEV5220220819BUYI:PARF:2022-08-19T12:00:00Z:200000.0:1689763673513",
            ],
        }
        assert scenarios[0].json.get("additionalFields") == {
            "topLevel": {
                "ric": "BABA.N",
                "executionsDetected": 2,
                "ordersDetected": 2,
                "earliestOrderTimestamp": "2022-08-19T12:00:00.000",
                "instrumentNameList": ["ALIBABA GROUP HOLDING LTD SPON ADS EACH REP 8 ORD SHS CFD"],
                "instrumentDetected": 1,
                "totalOrderQuantity": 800000.0,
                "clientList": ["account:client1"],
                "counterpartyList": ["account:counterparty1"],
                "traderList": ["account:trader1"],
                "deskList": ["desk1"],
                "advPercentage": [0.**********],
                "adv": [2065005.0],
                "evaluationType": "executingEntity",
                "evaluationID": ["lei:894500wota5040khgx73"],
                "marketPriceImprovement": [False],
                "marketImpactPercentagePrice": [0.0],
                "marketImpactPercentagePriceAbs": [0.0],
                "nearestMidBefore": [90.4],
                "nearestMidAfter": [90.4],
            }
        }

    def test_case_intraday_threshold(self, helpers, monkeypatch):
        thresholds = {
            "percentageAdv": 0.53,
            "quantityEvaluationType": "orderQuantity",
            "dayAndOrderEvaluationType": "order",
            "minimumQuantity": 10,
            "categoryEvaluationType": "market",
            "marketDataEvaluationType": "intraDayTradedVolume",
        }

        filters = {
            "bool": {
                "must": {
                    "terms": {
                        "sourceKey": [
                            "s3://mar.dev.steeleye.co/flows/order-universal-steeleye-trade-blotter/steeleyeBlotter.mar.sloev.10.csv"
                        ]
                    }
                }
            }
        }

        def mock_get_cases_to_analyse():
            return pd.read_csv(
                TEST_DATA_END_TO_END.joinpath(
                    "test_case_intraday_threshold_get_cases_to_analyse.csv"
                ),
                index_col=0,
                parse_dates=[
                    "timestamps.orderStatusUpdated",
                    "timestamps.tradingDateTime",
                    "timestamps.orderSubmitted",
                ],
                date_parser=custom_date_parser(),
            )

        def mock_check_parent_and_child_orders():
            return {
                "orderStatesKeys": [
                    "OrderState:sloev_10_1:1:SLOEV101SLOEV10120220822BUYI:FILL:2022-08-22T15:00:00Z:50000.0:*************"
                ],
                "executionsDetected": 1,
                "ordersKeys": ["Order:sloev_10_1:1:NEWO:*************"],
                "ordersDetected": 1,
            }

        import market_abuse_algorithms.data_source.repository.market_data.utils

        monkeypatch.setattr(
            market_abuse_algorithms.data_source.repository.market_data.utils,
            "get_eod_stats",
            fake_get_eod_stats,
        )

        market_abuse_algorithms.strategy.suspicious_large_order_volume_v2.query.get_market_client = MagicMock()
        market_abuse_algorithms.strategy.suspicious_large_order_volume_v2.query.get_market_client.return_value = FakeMarketClient()

        context = helpers.get_context(thresholds=thresholds, filters=filters)

        strategy = Strategy(context=context)

        strategy.queries.cases_to_analyse = MagicMock()
        strategy.queries.cases_to_analyse.return_value = [mock_get_cases_to_analyse()]

        strategy.check_parent_and_child_orders = MagicMock()
        strategy.check_parent_and_child_orders.side_effect = [
            mock_check_parent_and_child_orders(),
            mock_check_parent_and_child_orders(),
        ]

        strategy.context.commit_audit = False

        strategy.run()

        scenarios = strategy.scenarios

        assert len(scenarios) == 1
        assert scenarios[0].json.get("records") == {
            "orders": ["Order:sloev_10_1:1:NEWO:*************"],
            "executions": [
                "OrderState:sloev_10_1:1:SLOEV101SLOEV10120220822BUYI:FILL:2022-08-22T15:00:00Z:50000.0:*************"
            ],
        }
        assert scenarios[0].json.get("additionalFields") == {
            "topLevel": {
                "ric": "AMC.N",
                "executionsDetected": 1,
                "ordersDetected": 1,
                "earliestOrderTimestamp": "2022-08-22T15:00:00.000",
                "instrumentNameList": ["AMC ENTERTAINMENT HOLDINGS CL A ORD"],
                "instrumentDetected": 1,
                "totalOrderQuantity": 100000.0,
                "clientList": ["account:client1"],
                "counterpartyList": ["account:counterparty1"],
                "traderList": ["account:trader1"],
                "deskList": ["desk1"],
                "advPercentage": [0.**********],
                "adv": [181579.0],
                "marketPriceImprovement": [True],
                "marketImpactPercentagePrice": [0.**********],
                "marketImpactPercentagePriceAbs": [0.**********],
                "nearestMidBefore": [10.965],
                "nearestMidAfter": [11.145],
            }
        }

    def test_case_internal_workflow_15_1(self, helpers, monkeypatch):
        thresholds = {
            "percentageAdv": 0.1,
            "quantityEvaluationType": "orderQuantity",
            "dayAndOrderEvaluationType": "order",
            "minimumQuantity": 10,
            "categoryEvaluationType": "internalFlow",
            "generalEvaluationType": "executingEntity",
        }

        filters = {
            "bool": {
                "must": {
                    "terms": {
                        "sourceKey": [
                            "s3://mar.dev.steeleye.co/flows/order-universal-steeleye-trade-blotter/steeleyeBlotter.mar.sloev.15.csv"
                        ]
                    }
                }
            }
        }

        class HistoricalDataAndChildOrders:
            def __init__(self):
                self.historical_data_counter = 0
                self.orders_counter = 0

            def fake_get_historical_data(
                self,
                start_date,
                end_date,
                date_field,
                event_type,
                instr_comb=None,
                grouping_field_filters=None,
            ):
                self.historical_data_counter += 1

                data_to_return = pd.read_csv(
                    TEST_DATA_END_TO_END.joinpath(
                        f"test_case_internal_workflow_15_1_get_historical_data_{self.historical_data_counter}.csv"
                    ),
                    index_col=0,
                    parse_dates=[
                        "timestamps.orderStatusUpdated",
                        "timestamps.tradingDateTime",
                        "timestamps.orderSubmitted",
                    ],
                    date_parser=custom_date_parser(),
                )

                return data_to_return

            def fake_check_parent_and_child_orders(self, data_result, dataset):
                parent_and_child_orders = [
                    {
                        "orderStatesKeys": [
                            "OrderState:SLOEV_15_10:1:SLOEV1510SLOEV1510120220825BUYI:FILL:2022-08-25T12:29:58Z:300.0:*************"
                        ],
                        "executionsDetected": 1,
                        "ordersKeys": ["Order:SLOEV_15_10:1:NEWO:*************"],
                        "ordersDetected": 1,
                    },
                    {
                        "orderStatesKeys": [
                            "OrderState:SLOEV_15_11:1:SLOEV1511SLOEV1511120220825BUYI:FILL:2022-08-25T12:31:41Z:5000.0:*************"
                        ],
                        "executionsDetected": 1,
                        "ordersKeys": ["Order:SLOEV_15_11:1:NEWO:*************"],
                        "ordersDetected": 1,
                    },
                    {
                        "orderStatesKeys": [
                            "OrderState:SLOEV_15_2:1:SLOEV152SLOEV152120220816BUYI:FILL:2022-08-16T12:30:59Z:250.0:*************"
                        ],
                        "executionsDetected": 1,
                        "ordersKeys": ["Order:SLOEV_15_2:1:NEWO:*************"],
                        "ordersDetected": 1,
                    },
                    {
                        "orderStatesKeys": [
                            "OrderState:SLOEV_15_3:1:SLOEV153SLOEV153120220816BUYI:FILL:2022-08-16T12:31:41Z:250.0:*************"
                        ],
                        "executionsDetected": 1,
                        "ordersKeys": ["Order:SLOEV_15_3:1:NEWO:*************"],
                        "ordersDetected": 1,
                    },
                    {
                        "orderStatesKeys": [
                            "OrderState:SLOEV_15_4:1:SLOEV154SLOEV154120220818BUYI:FILL:2022-08-18T12:31:41Z:250.0:*************"
                        ],
                        "executionsDetected": 1,
                        "ordersKeys": ["Order:SLOEV_15_4:1:NEWO:*************"],
                        "ordersDetected": 1,
                    },
                    {
                        "orderStatesKeys": [
                            "OrderState:SLOEV_15_5:1:SLOEV155SLOEV155120220818BUYI:FILL:2022-08-18T13:29:58Z:250.0:*************"
                        ],
                        "executionsDetected": 1,
                        "ordersKeys": ["Order:SLOEV_15_5:1:NEWO:*************"],
                        "ordersDetected": 1,
                    },
                    {
                        "orderStatesKeys": [
                            "OrderState:SLOEV_15_6:1:SLOEV156SLOEV156120220818BUYI:FILL:2022-08-18T13:30:59Z:250.0:*************"
                        ],
                        "executionsDetected": 1,
                        "ordersKeys": ["Order:SLOEV_15_6:1:NEWO:*************"],
                        "ordersDetected": 1,
                    },
                    {
                        "orderStatesKeys": [
                            "OrderState:SLOEV_15_7:1:SLOEV157SLOEV157120220819BUYI:FILL:2022-08-19T12:29:58Z:250.0:*************"
                        ],
                        "executionsDetected": 1,
                        "ordersKeys": ["Order:SLOEV_15_7:1:NEWO:*************"],
                        "ordersDetected": 1,
                    },
                    {
                        "orderStatesKeys": [
                            "OrderState:SLOEV_15_8:1:SLOEV158SLOEV158120220819BUYI:FILL:2022-08-19T12:31:41Z:5000.0:*************"
                        ],
                        "executionsDetected": 1,
                        "ordersKeys": ["Order:SLOEV_15_8:1:NEWO:*************"],
                        "ordersDetected": 1,
                    },
                    {
                        "orderStatesKeys": [
                            "OrderState:SLOEV_15_9:1:SLOEV159SLOEV159120220822BUYI:FILL:2022-08-22T13:29:58Z:300.0:*************"
                        ],
                        "executionsDetected": 1,
                        "ordersKeys": ["Order:SLOEV_15_9:1:NEWO:*************"],
                        "ordersDetected": 1,
                    },
                ]

                data_to_return = parent_and_child_orders[self.orders_counter]
                self.orders_counter += 1

                return data_to_return

        def mock_get_cases_to_analyse():
            return pd.read_csv(
                TEST_DATA_END_TO_END.joinpath(
                    "test_case_internal_workflow_15_1_get_cases_to_analyse_GAZPq_L.csv"
                ),
                index_col=0,
                parse_dates=[
                    "timestamps.orderStatusUpdated",
                    "timestamps.tradingDateTime",
                    "timestamps.orderSubmitted",
                ],
                date_parser=custom_date_parser(),
            )

        monkeypatch.setattr(
            market_abuse_algorithms.data_source.repository.market_data.client,
            "get_eod_stats",
            fake_get_eod_stats,
        )

        market_abuse_algorithms.strategy.suspicious_large_order_volume_v2.query.get_market_client = MagicMock()
        market_abuse_algorithms.strategy.suspicious_large_order_volume_v2.query.get_market_client.return_value = FakeMarketClient()

        context = helpers.get_context(thresholds=thresholds, filters=filters)

        strategy = Strategy(context=context)

        strategy.queries.cases_to_analyse = MagicMock()
        strategy.queries.cases_to_analyse.return_value = [mock_get_cases_to_analyse()]

        monkeypatch.setattr(
            strategy,
            "check_parent_and_child_orders",
            HistoricalDataAndChildOrders().fake_check_parent_and_child_orders,
        )

        monkeypatch.setattr(
            strategy.queries,
            "get_historical_data",
            HistoricalDataAndChildOrders().fake_get_historical_data,
        )

        strategy.context.commit_audit = False

        strategy.run()

        scenarios = strategy.scenarios

        assert len(scenarios) == 10

        assert scenarios[0].json.get("records") == {
            "orders": ["Order:SLOEV_15_10:1:NEWO:*************"],
            "executions": [
                "OrderState:SLOEV_15_10:1:SLOEV1510SLOEV1510120220825BUYI:FILL:2022-08-25T12:29:58Z:300.0:*************"
            ],
        }
        assert scenarios[0].json.get("additionalFields") == {
            "topLevel": {
                "ric": "GAZPq.L",
                "executionsDetected": 1,
                "ordersDetected": 1,
                "earliestOrderTimestamp": "2022-08-25T12:27:58.000",
                "instrumentNameList": ["PJSC GAZPROM LEVEL 1 ADS (REPR 2 ORD)"],
                "instrumentDetected": 1,
                "totalOrderQuantity": 600.0,
                "clientList": ["account:client1"],
                "counterpartyList": ["account:counterparty1"],
                "traderList": ["account:trader1"],
                "deskList": ["desk1"],
                "advPercentage": [0.**********],
                "adv": [2820.0],
                "evaluationType": "executingEntity",
                "evaluationID": ["lei:894500wota5040khgx73"],
                "marketPriceImprovement": None,
                "marketImpactPercentagePrice": None,
                "marketImpactPercentagePriceAbs": None,
                "nearestMidBefore": None,
                "nearestMidAfter": None,
            }
        }
        assert scenarios[1].json.get("records") == {
            "orders": ["Order:SLOEV_15_11:1:NEWO:*************"],
            "executions": [
                "OrderState:SLOEV_15_11:1:SLOEV1511SLOEV1511120220825BUYI:FILL:2022-08-25T12:31:41Z:5000.0:*************"
            ],
        }
        assert scenarios[1].json.get("additionalFields") == {
            "topLevel": {
                "ric": "GAZPq.L",
                "executionsDetected": 1,
                "ordersDetected": 1,
                "earliestOrderTimestamp": "2022-08-25T12:29:41.000",
                "instrumentNameList": ["PJSC GAZPROM LEVEL 1 ADS (REPR 2 ORD)"],
                "instrumentDetected": 1,
                "totalOrderQuantity": 10000.0,
                "clientList": ["account:client1"],
                "counterpartyList": ["account:counterparty1"],
                "traderList": ["account:trader1"],
                "deskList": ["desk1"],
                "advPercentage": [3.**********],
                "adv": [2820.0],
                "evaluationType": "executingEntity",
                "evaluationID": ["lei:894500wota5040khgx73"],
                "marketPriceImprovement": None,
                "marketImpactPercentagePrice": None,
                "marketImpactPercentagePriceAbs": None,
                "nearestMidBefore": None,
                "nearestMidAfter": None,
            }
        }
        assert scenarios[2].json.get("records") == {
            "orders": ["Order:SLOEV_15_2:1:NEWO:*************"],
            "executions": [
                "OrderState:SLOEV_15_2:1:SLOEV152SLOEV152120220816BUYI:FILL:2022-08-16T12:30:59Z:250.0:*************"
            ],
        }
        assert scenarios[2].json.get("additionalFields") == {
            "topLevel": {
                "ric": "GAZPq.L",
                "executionsDetected": 1,
                "ordersDetected": 1,
                "earliestOrderTimestamp": "2022-08-16T12:28:59.000",
                "instrumentNameList": ["PJSC GAZPROM LEVEL 1 ADS (REPR 2 ORD)"],
                "instrumentDetected": 1,
                "totalOrderQuantity": 500.0,
                "clientList": ["account:client1"],
                "counterpartyList": ["account:counterparty1"],
                "traderList": ["account:trader1"],
                "deskList": ["desk1"],
                "advPercentage": [1.0],
                "adv": [500.0],
                "evaluationType": "executingEntity",
                "evaluationID": ["lei:894500wota5040khgx73"],
                "marketPriceImprovement": None,
                "marketImpactPercentagePrice": None,
                "marketImpactPercentagePriceAbs": None,
                "nearestMidBefore": None,
                "nearestMidAfter": None,
            }
        }
        assert scenarios[3].json.get("records") == {
            "orders": ["Order:SLOEV_15_3:1:NEWO:*************"],
            "executions": [
                "OrderState:SLOEV_15_3:1:SLOEV153SLOEV153120220816BUYI:FILL:2022-08-16T12:31:41Z:250.0:*************"
            ],
        }
        assert scenarios[3].json.get("additionalFields") == {
            "topLevel": {
                "ric": "GAZPq.L",
                "executionsDetected": 1,
                "ordersDetected": 1,
                "earliestOrderTimestamp": "2022-08-16T12:29:41.000",
                "instrumentNameList": ["PJSC GAZPROM LEVEL 1 ADS (REPR 2 ORD)"],
                "instrumentDetected": 1,
                "totalOrderQuantity": 500.0,
                "clientList": ["account:client1"],
                "counterpartyList": ["account:counterparty1"],
                "traderList": ["account:trader1"],
                "deskList": ["desk1"],
                "advPercentage": [1.0],
                "adv": [500.0],
                "evaluationType": "executingEntity",
                "evaluationID": ["lei:894500wota5040khgx73"],
                "marketPriceImprovement": None,
                "marketImpactPercentagePrice": None,
                "marketImpactPercentagePriceAbs": None,
                "nearestMidBefore": None,
                "nearestMidAfter": None,
            }
        }
        assert scenarios[4].json.get("records") == {
            "orders": ["Order:SLOEV_15_4:1:NEWO:*************"],
            "executions": [
                "OrderState:SLOEV_15_4:1:SLOEV154SLOEV154120220818BUYI:FILL:2022-08-18T12:31:41Z:250.0:*************"
            ],
        }
        assert scenarios[4].json.get("additionalFields") == {
            "topLevel": {
                "ric": "GAZPq.L",
                "executionsDetected": 1,
                "ordersDetected": 1,
                "earliestOrderTimestamp": "2022-08-18T12:29:41.000",
                "instrumentNameList": ["PJSC GAZPROM LEVEL 1 ADS (REPR 2 ORD)"],
                "instrumentDetected": 1,
                "totalOrderQuantity": 500.0,
                "clientList": ["account:client1"],
                "counterpartyList": ["account:counterparty1"],
                "traderList": ["account:trader1"],
                "deskList": ["desk1"],
                "advPercentage": [0.**********],
                "adv": [750.0],
                "evaluationType": "executingEntity",
                "evaluationID": ["lei:894500wota5040khgx73"],
                "marketPriceImprovement": None,
                "marketImpactPercentagePrice": None,
                "marketImpactPercentagePriceAbs": None,
                "nearestMidBefore": None,
                "nearestMidAfter": None,
            }
        }
        assert scenarios[5].json.get("records") == {
            "orders": ["Order:SLOEV_15_5:1:NEWO:*************"],
            "executions": [
                "OrderState:SLOEV_15_5:1:SLOEV155SLOEV155120220818BUYI:FILL:2022-08-18T13:29:58Z:250.0:*************"
            ],
        }
        assert scenarios[5].json.get("additionalFields") == {
            "topLevel": {
                "ric": "GAZPq.L",
                "executionsDetected": 1,
                "ordersDetected": 1,
                "earliestOrderTimestamp": "2022-08-18T13:27:58.000",
                "instrumentNameList": ["PJSC GAZPROM LEVEL 1 ADS (REPR 2 ORD)"],
                "instrumentDetected": 1,
                "totalOrderQuantity": 500.0,
                "clientList": ["account:client1"],
                "counterpartyList": ["account:counterparty1"],
                "traderList": ["account:trader1"],
                "deskList": ["desk1"],
                "advPercentage": [0.**********],
                "adv": [750.0],
                "evaluationType": "executingEntity",
                "evaluationID": ["lei:894500wota5040khgx73"],
                "marketPriceImprovement": None,
                "marketImpactPercentagePrice": None,
                "marketImpactPercentagePriceAbs": None,
                "nearestMidBefore": None,
                "nearestMidAfter": None,
            }
        }
        assert scenarios[6].json.get("records") == {
            "orders": ["Order:SLOEV_15_6:1:NEWO:*************"],
            "executions": [
                "OrderState:SLOEV_15_6:1:SLOEV156SLOEV156120220818BUYI:FILL:2022-08-18T13:30:59Z:250.0:*************"
            ],
        }
        assert scenarios[6].json.get("additionalFields") == {
            "topLevel": {
                "ric": "GAZPq.L",
                "executionsDetected": 1,
                "ordersDetected": 1,
                "earliestOrderTimestamp": "2022-08-18T13:28:59.000",
                "instrumentNameList": ["PJSC GAZPROM LEVEL 1 ADS (REPR 2 ORD)"],
                "instrumentDetected": 1,
                "totalOrderQuantity": 500.0,
                "clientList": ["account:client1"],
                "counterpartyList": ["account:counterparty1"],
                "traderList": ["account:trader1"],
                "deskList": ["desk1"],
                "advPercentage": [0.**********],
                "adv": [750.0],
                "evaluationType": "executingEntity",
                "evaluationID": ["lei:894500wota5040khgx73"],
                "marketPriceImprovement": None,
                "marketImpactPercentagePrice": None,
                "marketImpactPercentagePriceAbs": None,
                "nearestMidBefore": None,
                "nearestMidAfter": None,
            }
        }
        assert scenarios[7].json.get("records") == {
            "orders": ["Order:SLOEV_15_7:1:NEWO:*************"],
            "executions": [
                "OrderState:SLOEV_15_7:1:SLOEV157SLOEV157120220819BUYI:FILL:2022-08-19T12:29:58Z:250.0:*************"
            ],
        }
        assert scenarios[7].json.get("additionalFields") == {
            "topLevel": {
                "ric": "GAZPq.L",
                "executionsDetected": 1,
                "ordersDetected": 1,
                "earliestOrderTimestamp": "2022-08-19T12:27:58.000",
                "instrumentNameList": ["PJSC GAZPROM LEVEL 1 ADS (REPR 2 ORD)"],
                "instrumentDetected": 1,
                "totalOrderQuantity": 500.0,
                "clientList": ["account:client1"],
                "counterpartyList": ["account:counterparty1"],
                "traderList": ["account:trader1"],
                "deskList": ["desk1"],
                "advPercentage": [0.5],
                "adv": [1000.0],
                "evaluationType": "executingEntity",
                "evaluationID": ["lei:894500wota5040khgx73"],
                "marketPriceImprovement": None,
                "marketImpactPercentagePrice": None,
                "marketImpactPercentagePriceAbs": None,
                "nearestMidBefore": None,
                "nearestMidAfter": None,
            }
        }
        assert scenarios[8].json.get("records") == {
            "orders": ["Order:SLOEV_15_8:1:NEWO:*************"],
            "executions": [
                "OrderState:SLOEV_15_8:1:SLOEV158SLOEV158120220819BUYI:FILL:2022-08-19T12:31:41Z:5000.0:*************"
            ],
        }
        assert scenarios[8].json.get("additionalFields") == {
            "topLevel": {
                "ric": "GAZPq.L",
                "executionsDetected": 1,
                "ordersDetected": 1,
                "earliestOrderTimestamp": "2022-08-19T12:29:41.000",
                "instrumentNameList": ["PJSC GAZPROM LEVEL 1 ADS (REPR 2 ORD)"],
                "instrumentDetected": 1,
                "totalOrderQuantity": 10000.0,
                "clientList": ["account:client1"],
                "counterpartyList": ["account:counterparty1"],
                "traderList": ["account:trader1"],
                "deskList": ["desk1"],
                "advPercentage": [10.0],
                "adv": [1000.0],
                "evaluationType": "executingEntity",
                "evaluationID": ["lei:894500wota5040khgx73"],
                "marketPriceImprovement": None,
                "marketImpactPercentagePrice": None,
                "marketImpactPercentagePriceAbs": None,
                "nearestMidBefore": None,
                "nearestMidAfter": None,
            }
        }
        assert scenarios[9].json.get("records") == {
            "orders": ["Order:SLOEV_15_9:1:NEWO:*************"],
            "executions": [
                "OrderState:SLOEV_15_9:1:SLOEV159SLOEV159120220822BUYI:FILL:2022-08-22T13:29:58Z:300.0:*************"
            ],
        }
        assert scenarios[9].json.get("additionalFields") == {
            "topLevel": {
                "ric": "GAZPq.L",
                "executionsDetected": 1,
                "ordersDetected": 1,
                "earliestOrderTimestamp": "2022-08-22T13:27:58.000",
                "instrumentNameList": ["PJSC GAZPROM LEVEL 1 ADS (REPR 2 ORD)"],
                "instrumentDetected": 1,
                "totalOrderQuantity": 600.0,
                "clientList": ["account:client1"],
                "counterpartyList": ["account:counterparty1"],
                "traderList": ["account:trader1"],
                "deskList": ["desk1"],
                "advPercentage": [0.**********],
                "adv": [3375.0],
                "evaluationType": "executingEntity",
                "evaluationID": ["lei:894500wota5040khgx73"],
                "marketPriceImprovement": None,
                "marketImpactPercentagePrice": None,
                "marketImpactPercentagePriceAbs": None,
                "nearestMidBefore": None,
                "nearestMidAfter": None,
            }
        }

    def test_zero_adtv_that_shouldnt_generate_alerts(self, helpers, monkeypatch):
        thresholds = {
            "percentageAdv": 0.1,
            "quantityEvaluationType": "executedQuantity",
            "dayAndOrderEvaluationType": "day",
            "generalEvaluationType": "trader",
            "minimumQuantity": 1000,
            "categoryEvaluationType": "market",
            "marketDataEvaluationType": "averageDailyTradedVolume",
        }

        filters = {
            "bool": {
                "must": {
                    "terms": {
                        "sourceKey": [
                            "s3://hesham.uat.steeleye.co/flows/order-universal-steeleye-trade-blotter/seBlotter.Sloev.ENG10440.csv"
                        ]
                    }
                }
            }
        }

        def mock_get_cases_to_analyse():
            return pd.read_csv(
                TEST_DATA_END_TO_END.joinpath(
                    "cases_to_analyse_test_zero_adtv_that_shouldnt_generate_alerts.csv"
                ),
                index_col=0,
                parse_dates=[
                    "timestamps.orderStatusUpdated",
                    "timestamps.tradingDateTime",
                    "timestamps.orderSubmitted",
                ],
                date_parser=custom_date_parser(),
            )

        monkeypatch.setattr(
            market_abuse_algorithms.data_source.repository.market_data.client,
            "fetch_eod_stats_with_time_margin",
            fake_get_eod_stats_with_margin,
        )

        context = helpers.get_context(thresholds=thresholds, filters=filters)

        strategy = Strategy(context=context)

        strategy.queries.cases_to_analyse = MagicMock()
        strategy.queries.cases_to_analyse.return_value = [mock_get_cases_to_analyse()]

        strategy.context.commit_audit = False

        strategy.run()

        scenarios = strategy.scenarios

        assert len(scenarios) == 0

    def test_intraday_threshold_empty_volume_zero_alerts(self, helpers, monkeypatch):
        thresholds = {
            "percentageAdv": 0.01,
            "quantityEvaluationType": "executedQuantity",
            "dayAndOrderEvaluationType": "day",
            "minimumQuantity": 10,
            "categoryEvaluationType": "market",
            "marketDataEvaluationType": "intraDayTradedVolume",
            "generalEvaluationType": "client",
        }

        filters = {
            "bool": {
                "must": {
                    "terms": {
                        "sourceKey": [
                            "s3://iris.uat.steeleye.co/flows/order-universal-steeleye-trade-blotter/SE_TestFile_SLOV_TC2_20240724.csv"
                        ]
                    }
                }
            }
        }

        def mock_get_cases_to_analyse():
            return pd.read_csv(
                TEST_DATA_END_TO_END.joinpath(
                    "test_intraday_threshold_empty_volume_zero_alerts_get_cases_to_analyse.csv"
                ),
                index_col=0,
                parse_dates=[
                    "timestamps.orderStatusUpdated",
                    "timestamps.tradingDateTime",
                    "timestamps.orderSubmitted",
                ],
                date_parser=custom_date_parser(),
            )

        market_abuse_algorithms.strategy.suspicious_large_order_volume_v2.query.get_market_client = MagicMock()
        market_abuse_algorithms.strategy.suspicious_large_order_volume_v2.query.get_market_client.return_value = FakeMarketClient()
        context = helpers.get_context(thresholds=thresholds, filters=filters)

        strategy = Strategy(context=context)

        strategy.queries.cases_to_analyse = MagicMock()
        strategy.queries.cases_to_analyse.return_value = [mock_get_cases_to_analyse()]

        strategy.context.commit_audit = False

        strategy.run()

        scenarios = strategy.scenarios

        assert len(scenarios) == 0

    def test_intraday_threshold_check_volume_one_alert(self, helpers, monkeypatch):
        thresholds = {
            "percentageAdv": 0.01,
            "quantityEvaluationType": "executedQuantity",
            "dayAndOrderEvaluationType": "day",
            "generalEvaluationType": "client",
            "minimumQuantity": 10,
            "categoryEvaluationType": "market",
            "marketDataEvaluationType": "intraDayTradedVolume",
        }

        filters = {
            "bool": {
                "must": {
                    "terms": {
                        "sourceKey": [
                            "s3://iris.uat.steeleye.co/flows/order-universal-steeleye-trade-blotter/SE_TestFile_SLOV_TC1_20240724.csv"
                        ]
                    }
                }
            }
        }

        def mock_get_cases_to_analyse():
            return pd.read_csv(
                TEST_DATA_END_TO_END.joinpath(
                    "test_intraday_threshold_check_volume_one_alert_get_cases_to_analyse.csv"
                ),
                index_col=0,
                parse_dates=[
                    "timestamps.orderStatusUpdated",
                    "timestamps.tradingDateTime",
                    "timestamps.orderSubmitted",
                ],
                date_parser=custom_date_parser(),
            )

        market_abuse_algorithms.strategy.suspicious_large_order_volume_v2.query.get_market_client = MagicMock()
        market_abuse_algorithms.strategy.suspicious_large_order_volume_v2.query.get_market_client.return_value = FakeMarketClient()

        context = helpers.get_context(thresholds=thresholds, filters=filters)

        strategy = Strategy(context=context)

        strategy.queries.cases_to_analyse = MagicMock()
        strategy.queries.cases_to_analyse.return_value = [mock_get_cases_to_analyse()]

        strategy.context.commit_audit = False

        strategy.run()

        scenarios = strategy.scenarios

        assert len(scenarios) == 1
        assert all(scenarios[0].json["additionalFields"]["topLevel"]["advPercentage"])
        assert all(scenarios[0].json["additionalFields"]["topLevel"]["adv"])
