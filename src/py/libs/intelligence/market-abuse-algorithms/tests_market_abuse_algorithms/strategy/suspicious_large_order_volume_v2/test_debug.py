import logging
import pytest
from market_abuse_algorithms.strategy.suspicious_large_order_volume_v2.strategy import (
    Strategy,
)

logger = logging.getLogger(__name__)


@pytest.mark.usefixtures("skip_test_in_ci")
class TestDebuggerSuspiciousLargeOrderVolumeV2:
    def test_case_debug(self, helpers):
        """Test Suspicious Large Order VolumeV2 generic."""

        thresholds = {
            "percentageAdv": 0.1,
            "quantityEvaluationType": "executedQuantity",
            "dayAndOrderEvaluationType": "day",
            "minimumQuantity": 1,
            "categoryEvaluationType": "market",
            "marketDataEvaluationType": "averageDailyTradedVolume",
            "generalEvaluationType": "executingEntity",
        }
        filters = {
            "bool": {
                "filter": [
                    {
                        "terms": {
                            "sourceKey": [
                                "s3://shrenik.uat.steeleye.co/aries/ingress/nonstreamed/evented/trade_sink_ns/engadine_eze/skip_tc/Engadine_SteelEye_Executions_092624.csv"  # noqa: E501
                            ]
                        }
                    },
                    {"range": {"&timestamp": {"gte": "2024-10-10T00:00:00.000000Z"}}},
                ]
            }
        }
        context = helpers.get_context(thresholds=thresholds, filters=filters)

        strategy = Strategy(context=context)
        strategy.run()
        scenarios = strategy.scenarios

        # assert len(scenarios) != 0

        if len(scenarios) > 0:
            for s in scenarios:
                logger.info(
                    f"{s.json.get('records').get('orders')[0]}, {s.json.get('additionalFields').get('topLevel').get('earliestOrderTimestamp')}, {s.json.get('additionalFields').get('topLevel').get('adv')[0]}"  # noqa: E501
                )
