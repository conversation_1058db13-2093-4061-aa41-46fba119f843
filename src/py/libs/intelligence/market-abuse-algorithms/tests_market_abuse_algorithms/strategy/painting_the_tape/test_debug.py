import pytest
from market_abuse_algorithms.strategy.painting_the_tape.strategy import Strategy


@pytest.mark.usefixtures("skip_test_in_ci")
class TestDebugPaintingTheTapeV1:
    def test_debug(self, helpers):
        thresholds = {
            "adv30DayEma": 0,
            "evaluationType": "All",
            "lookBackPeriod": 180,
            "marketAdv30DayEma": 0,
            "priceDifference": 0,
            "volumeDifference": 0,
        }

        filters = {
            "bool": {
                "must": {
                    "terms": {
                        "sourceKey": [
                            "s3://benjamin.uat.steeleye.co/flows/order-universal-steeleye-trade-blotter/Painting_The_Tape_use_cases_csv_v1.csv"  # noqa: E501
                        ]
                    }
                }
            }
        }

        context = helpers.get_context(thresholds=thresholds, filters=filters)

        strategy = Strategy(context=context)

        strategy.run()

        strategy
