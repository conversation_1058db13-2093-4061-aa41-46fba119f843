# ruff: noqa: E501
import pytest
from market_abuse_algorithms.strategy.layering_v2.strategy import Strategy
from pathlib import Path

TEST_DATA = Path(__file__).parent.joinpath("test_data")

FILE_6 = "s3://mar.uat.steeleye.co/flows/order-universal-steeleye-trade-blotter/steeleyeBlotter.mar.layeringAndBookBalance.6.csv"
FILE_9 = "s3://mar.uat.steeleye.co/flows/order-universal-steeleye-trade-blotter/steeleyeBlotter.mar.layeringAndBookBalance.9.csv"
FILE_10 = "s3://mar.uat.steeleye.co/flows/order-universal-steeleye-trade-blotter/steeleyeBlotter.mar.layeringAndBookBalance.10.csv"
FILE_11 = "s3://mar.uat.steeleye.co/flows/order-universal-steeleye-trade-blotter/steeleyeBlotter.mar.layeringAndBookBalance.11.csv"
FILE_12 = "s3://mar.uat.steeleye.co/flows/order-universal-steeleye-trade-blotter/steeleyeBlotter.mar.layeringAndBookBalance.12.csv"
FILE_13 = "s3://mar.uat.steeleye.co/flows/order-universal-steeleye-trade-blotter/steeleyeBlotter.mar.layeringAndBookBalance.13.csv"
FILE_14 = "s3://mar.uat.steeleye.co/flows/order-universal-steeleye-trade-blotter/steeleyeBlotter.mar.layeringAndBookBalance.14.csv"
FILE_16 = "s3://mar.uat.steeleye.co/flows/order-universal-steeleye-trade-blotter/steeleyeBlotter.mar.layeringAndBookBalance.16.csv"
FILE_17 = "s3://mar.uat.steeleye.co/flows/order-universal-steeleye-trade-blotter/steeleyeBlotter.mar.layeringAndBookBalance.17.csv"
FILE_18 = "s3://mar.uat.steeleye.co/flows/order-universal-steeleye-trade-blotter/seBlotter.mar.LayeringV2.17.csv"
FILE_19 = "s3://mar.dev.steeleye.co/flows/order-universal-steeleye-trade-blotter/steeleyeBlotter.mar.layeringAndBookBalance.19.csv"
FILE_20 = "s3://mar.dev.steeleye.co/flows/order-universal-steeleye-trade-blotter/steeleyeBlotter.mar.layeringAndBookBalance.20.csv"


@pytest.mark.usefixtures("skip_test_in_ci")
class TestRealCasesLayeringV2:
    def test_case_6_1(self, helpers):
        thresholds = {
            "behaviourType": "layering",
            "evaluationType": "executingEntity",
            "layeringMaximumPriceLevel": 5,
            "layeringNumberOfPriceLevels": 2,
            "layeringOrderPercentage": 0.2,
            "layeringTimeWindow": 1,
        }

        filters = {"bool": {"must": {"terms": {"sourceKey": [FILE_6]}}}}

        context = helpers.get_context(thresholds=thresholds, filters=filters)

        strategy = Strategy(context=context)

        strategy.run()
        assert len(strategy.scenarios) == 1

    def test_case_6_3(self, helpers):
        thresholds = {
            "behaviourType": "layering",
            "evaluationType": "client",
            "layeringMaximumPriceLevel": 5,
            "layeringNumberOfPriceLevels": 2,
            "layeringOrderPercentage": 0.2,
            "layeringTimeWindow": 1,
        }

        filters = {"bool": {"must": {"terms": {"sourceKey": [FILE_6]}}}}

        context = helpers.get_context(thresholds=thresholds, filters=filters)

        strategy = Strategy(context=context)

        strategy.run()
        assert len(strategy.scenarios) == 1

    def test_case_9_1(self, helpers):
        thresholds = {
            "behaviourType": "layering",
            "evaluationType": "executingEntity",
            "layeringMaximumPriceLevel": 5,
            "layeringNumberOfPriceLevels": 2,
            "layeringOrderPercentage": 0.2,
            "layeringTimeWindow": 1,
        }

        filters = {"bool": {"must": {"terms": {"sourceKey": [FILE_9]}}}}

        context = helpers.get_context(thresholds=thresholds, filters=filters)

        strategy = Strategy(context=context)

        strategy.run()
        assert len(strategy.scenarios) == 1

    def test_case_9_2(self, helpers):
        thresholds = {
            "behaviourType": "layering",
            "evaluationType": "client",
            "layeringMaximumPriceLevel": 5,
            "layeringNumberOfPriceLevels": 2,
            "layeringOrderPercentage": 0.2,
            "layeringTimeWindow": 1,
        }

        filters = {"bool": {"must": {"terms": {"sourceKey": [FILE_9]}}}}

        context = helpers.get_context(thresholds=thresholds, filters=filters)

        strategy = Strategy(context=context)

        strategy.run()
        assert len(strategy.scenarios) == 1

    def test_case_9_3(self, helpers):
        thresholds = {
            "behaviourType": "layering",
            "evaluationType": "desk",
            "layeringMaximumPriceLevel": 5,
            "layeringNumberOfPriceLevels": 2,
            "layeringOrderPercentage": 0.2,
            "layeringTimeWindow": 1,
        }

        filters = {"bool": {"must": {"terms": {"sourceKey": [FILE_9]}}}}

        context = helpers.get_context(thresholds=thresholds, filters=filters)

        strategy = Strategy(context=context)

        strategy.run()
        assert len(strategy.scenarios) == 1

    def test_case_10_1(self, helpers):
        thresholds = {
            "behaviourType": "layering",
            "evaluationType": "executingEntity",
            "layeringMaximumPriceLevel": 5,
            "layeringNumberOfPriceLevels": 2,
            "layeringOrderPercentage": 0.2,
            "layeringTimeWindow": 1,
        }

        filters = {"bool": {"must": {"terms": {"sourceKey": [FILE_10]}}}}

        context = helpers.get_context(thresholds=thresholds, filters=filters)

        strategy = Strategy(context=context)

        strategy.run()
        assert len(strategy.scenarios) == 1

    def test_case_10_2(self, helpers):
        thresholds = {
            "behaviourType": "layering",
            "evaluationType": "client",
            "layeringMaximumPriceLevel": 5,
            "layeringNumberOfPriceLevels": 2,
            "layeringOrderPercentage": 0.2,
            "layeringTimeWindow": 1,
        }

        filters = {"bool": {"must": {"terms": {"sourceKey": [FILE_10]}}}}

        context = helpers.get_context(thresholds=thresholds, filters=filters)

        strategy = Strategy(context=context)

        strategy.run()
        assert len(strategy.scenarios) == 1

    def test_case_12_1(self, helpers):
        thresholds = {
            "behaviourType": "layering",
            "evaluationType": "executingEntity",
            "layeringMaximumPriceLevel": 5,
            "layeringNumberOfPriceLevels": 2,
            "layeringOrderPercentage": 0.2,
            "layeringTimeWindow": 10,
        }

        filters = {"bool": {"must": {"terms": {"sourceKey": [FILE_12]}}}}

        context = helpers.get_context(thresholds=thresholds, filters=filters)

        strategy = Strategy(context=context)

        strategy.run()
        assert len(strategy.scenarios) == 1

    def test_case_13_1(self, helpers):
        thresholds = {
            "behaviourType": "layering",
            "evaluationType": "executingEntity",
            "layeringMaximumPriceLevel": 5,
            "layeringNumberOfPriceLevels": 2,
            "layeringOrderPercentage": 0.2,
            "layeringTimeWindow": 30,
        }

        filters = {"bool": {"must": {"terms": {"sourceKey": [FILE_13]}}}}

        context = helpers.get_context(thresholds=thresholds, filters=filters)

        strategy = Strategy(context=context)

        strategy.run()
        assert len(strategy.scenarios) == 1

    def test_case_14_1(self, helpers):
        thresholds = {
            "behaviourType": "bookImbalance",
            "evaluationType": "executingEntity",
            "layeringMaximumPriceLevel": 5,
            "layeringNumberOfPriceLevels": 2,
            "layeringOrderPercentage": 0.2,
            "layeringTimeWindow": 10,
        }

        filters = {"bool": {"must": {"terms": {"sourceKey": [FILE_14]}}}}

        context = helpers.get_context(thresholds=thresholds, filters=filters)

        strategy = Strategy(context=context)

        strategy.run()
        assert len(strategy.scenarios) == 1

    def test_case_16_1(self, helpers):
        thresholds = {
            "behaviourType": "layering",
            "evaluationType": "executingEntity",
            "layeringMaximumPriceLevel": 5,
            "layeringNumberOfPriceLevels": 2,
            "layeringOrderPercentage": 0.2,
            "layeringTimeWindow": 30,
        }

        filters = {"bool": {"must": {"terms": {"sourceKey": [FILE_16]}}}}

        context = helpers.get_context(thresholds=thresholds, filters=filters)

        strategy = Strategy(context=context)

        strategy.run()
        assert len(strategy.scenarios) == 1

    def test_case_17_1(self, helpers):
        thresholds = {
            "behaviourType": "layering",
            "evaluationType": "executingEntity",
            "layeringMaximumPriceLevel": 5,
            "layeringNumberOfPriceLevels": 2,
            "layeringOrderPercentage": 0.2,
            "layeringTimeWindow": 1,
        }

        filters = {"bool": {"must": {"terms": {"sourceKey": [FILE_17]}}}}

        context = helpers.get_context(thresholds=thresholds, filters=filters)

        strategy = Strategy(context=context)

        strategy.run()
        assert len(strategy.scenarios) == 1

    def test_case_18_1(self, helpers):  # TODO: no data in mar.uat
        thresholds = {
            "behaviourType": "layering",
            "evaluationType": "trader",
            "layeringMaximumPriceLevel": 5,
            "layeringNumberOfPriceLevels": 2,
            "layeringOrderPercentage": 0.1,
            "layeringTimeWindow": 900,
        }

        filters = {"bool": {"must": {"terms": {"sourceKey": [FILE_18]}}}}

        context = helpers.get_context(thresholds=thresholds, filters=filters)

        strategy = Strategy(context=context)

        strategy.run()
        assert len(strategy.scenarios) == 1

    def test_case_19_1(self, helpers):
        thresholds = {
            "behaviourType": "layering",
            "evaluationType": "executingEntity",
            "layeringMaximumPriceLevel": 5,
            "layeringNumberOfPriceLevels": 2,
            "layeringOrderPercentage": 0.2,
            "layeringTimeWindow": 1,
        }

        filters = {"bool": {"must": {"terms": {"sourceKey": [FILE_19]}}}}

        context = helpers.get_context(thresholds=thresholds, filters=filters)

        strategy = Strategy(context=context)

        strategy.run()
        assert len(strategy.scenarios) == 1

    def test_case_20_1(self, helpers):
        thresholds = {
            "behaviourType": "layering",
            "evaluationType": "executingEntity",
            "layeringMaximumPriceLevel": 5,
            "layeringNumberOfPriceLevels": 2,
            "layeringOrderPercentage": 0.2,
            "layeringTimeWindow": 30,
        }

        filters = {"bool": {"must": {"terms": {"sourceKey": [FILE_20]}}}}

        context = helpers.get_context(thresholds=thresholds, filters=filters)

        strategy = Strategy(context=context)

        strategy.run()
        assert len(strategy.scenarios) == 1
