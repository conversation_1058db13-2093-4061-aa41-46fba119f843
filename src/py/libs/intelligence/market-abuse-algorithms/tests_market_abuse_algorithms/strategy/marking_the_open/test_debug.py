import pytest
from market_abuse_algorithms.strategy.marking_the_open.strategy import Strategy


@pytest.mark.usefixtures("skip_test_in_ci")
class TestDebuggerMarkingTheOpen:
    def test_case_debug(self, helpers):
        thresholds = {
            "client20DayAdv": 0,
            "lookBackPeriod": 300,
            "market20DayAdv": 0,
            "markingType": "Marking the open",
            "minimumNotionalCurrency": "AUD",
            "priceSpike": 0.005,
            "minimumNotional": 0,
        }

        filters = {
            "bool": {
                "filter": [
                    {
                        "range": {
                            "timestamps.orderSubmitted": {
                                "gte": "2024-10-10T00:01:14.256000+00:00",
                                "lte": "2024-10-14T20:00:14.256000+00:00",
                            }
                        }
                    }
                ]
            }
        }

        context = helpers.get_context(thresholds=thresholds, filters=filters)

        strategy = Strategy(context=context)

        strategy.run()

        scenarios = strategy.scenarios
        scenarios
