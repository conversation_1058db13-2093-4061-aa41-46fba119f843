# ruff: noqa: E501
import json
import pandas as pd
import pytest
import uuid
from addict import addict
from datetime import datetime
from market_abuse_algorithms.data_source.static.ref_data.trading_hours_report import (
    CoppClarkColumn,
)
from market_abuse_algorithms.data_source.static.sdp.order import OrderField
from market_abuse_algorithms.strategy.abstract_marking_the_price.query import (
    MarkingThePriceQueries as Queries,
)
from market_abuse_algorithms.strategy.base.strategy import (
    market_abuse_audit_object,
    singleton_audit_object,
)
from market_abuse_algorithms.strategy.marking_the_open.static import DFColumns, OpenType
from market_abuse_algorithms.strategy.marking_the_open.strategy import Strategy
from pathlib import Path
from tests_market_abuse_algorithms.strategy.abstract_marking_the_price.mock_data import (
    fake_market_client,
)
from tests_market_abuse_algorithms.strategy.marking_the_open.mock_data import (
    mock_marking_the_open_query,
)
from unittest.mock import MagicMock

TEST_DATA = Path(__file__).parent.joinpath("test_data")
TEST_DATA_TC5 = Path(__file__).parent.joinpath("test_data/test_case_5")


class FakeClient:
    @staticmethod
    def search_after_query(query, **kwargs):
        return pd.DataFrame.from_dict(
            {
                "calendarStart": [
                    datetime.strptime("2021-10-11 00:00:00", "%Y-%m-%d %H:%M:%S"),
                    None,
                    datetime.strptime("2021-10-11 00:00:00", "%Y-%m-%d %H:%M:%S"),
                    datetime.strptime("2021-10-11 00:00:00", "%Y-%m-%d %H:%M:%S"),
                ],
                "calendarEnd": ["2021-10-12", None, "2021-10-13", "2021-10-14"],
                "id": [1, 2, 3, 4],
            }
        )


class FakeQueries:
    _th_marking_type = OpenType.MARKING_THE_OPEN
    datetime_to_timestamp = Queries.datetime_to_timestamp


class FakeClientEmptyData:
    @staticmethod
    def search_after_query(query, **kwargs):
        return pd.DataFrame.from_dict(
            {
                "calendarStart": [
                    datetime.strptime("2021-10-11 00:00:00", "%Y-%m-%d %H:%M:%S"),
                    None,
                    datetime.strptime("2021-10-11 00:00:00", "%Y-%m-%d %H:%M:%S"),
                    datetime.strptime("2021-10-11 00:00:00", "%Y-%m-%d %H:%M:%S"),
                ],
                "calendarEnd": [None, None, None, None],
                "id": [1, 2, 3, 4],
            }
        )


class FakeQueriesEmptyData:
    _th_marking_type = OpenType.MARKING_THE_OPEN
    datetime_to_timestamp = Queries.datetime_to_timestamp


class FakeClientWrongData:
    @staticmethod
    def search_after_query(query, **kwargs):
        return pd.DataFrame.from_dict(
            {
                "calendarStart": [
                    datetime.strptime("2021-10-11 00:00:00", "%Y-%m-%d %H:%M:%S"),
                    None,
                    datetime.strptime("2021-10-11 00:00:00", "%Y-%m-%d %H:%M:%S"),
                    datetime.strptime("2021-10-11 00:00:00", "%Y-%m-%d %H:%M:%S"),
                ],
                "calendarEnd": [
                    "foo",
                    "bar",
                    datetime.strptime("2021-10-12 00:00:00", "%Y-%m-%d %H:%M:%S"),
                    23123,
                ],
                "id": [1, 2, 3, 4],
            }
        )


class FakeQueriesWrongData:
    _th_marking_type = OpenType.MARKING_THE_OPEN
    datetime_to_timestamp = Queries.datetime_to_timestamp


@pytest.fixture()
def mock_mto_query(monkeypatch, *args, **kwargs):
    mock_marking_the_open_query(monkeypatch)


@pytest.fixture
def fake_venues():
    return ["XLON"]


@pytest.fixture
def fake_data():
    """
    Process & Read fake data files with date parsing
    :return: pandas DataFrame with fake data
    """

    def _foo(file_path, dt_cols):
        result = pd.read_csv(
            file_path,
            index_col=0,
            sep=",",
            parse_dates=dt_cols,
        )
        return result

    return _foo


@pytest.fixture
def tc5_context(helpers, mock_mto_query):
    filters = {
        "bool": {
            "must": {
                "terms": {
                    "sourceKey": [
                        "s3://mar.dev.steeleye.co/flows/order-universal-steeleye-trade-blotter/steeleyeBlotter.mar.mtc.ENG-6409.3.csv",
                        "s3://mar.dev.steeleye.co/flows/order-universal-steeleye-trade-blotter/steeleyeBlotter.mar.mtc.ENG-6409.client.trades.2.csv",
                    ]
                }
            }
        }
    }
    context = helpers.get_context(thresholds={}, filters=filters)
    return context


@pytest.fixture
def fake_evaluate_thresholds(monkeypatch):
    def _fake_evaluate_thresholds(*args, **kwargs):
        pass

    import market_abuse_algorithms.strategy.abstract_marking_the_price.query

    monkeypatch.setattr(
        market_abuse_algorithms.strategy.abstract_marking_the_price.query.MarkingThePriceQueries,
        "_evaluate_thresholds",
        _fake_evaluate_thresholds,
    )


class TestMarkingTheOpen:
    def test_create_scenario(self, helpers, fake_evaluate_thresholds):
        thresholds = {
            "client20DayAdv": 0,
            "lookBackPeriod": 7200,
            "market20DayAdv": 0,
            "markingType": "Marking the open",
            "minimumNotionalCurrency": "USD",
            "priceSpike": 0,
            "minimumNotional": 0,
        }

        filters = {}

        context = helpers.get_context(thresholds=thresholds, filters=filters)

        strategy = Strategy(context=context)

        result = pd.read_csv(
            TEST_DATA.joinpath("test_create_scenario.csv"),
            parse_dates=[
                DFColumns.ORDER_SUBMITTED_TS_LOCAL,
                OrderField.TS_ORD_SUBMITTED,
            ],
            index_col=0,
        )

        strategy.queries.get_additional_fields_result = MagicMock()
        strategy.queries.get_additional_fields_result.return_value = pd.DataFrame()

        strategy._create_scenarios(result=result)

        assert (
            strategy.scenarios[0].json["additionalFields"]["topLevel"]["orderSubmittedTSLocal"]
            == "2022-07-28T07:13:00.000"
        )
        assert (
            strategy.scenarios[2].json["additionalFields"]["topLevel"]["orderSubmittedTSLocal"]
            == "2022-07-28T08:59:00.000"
        )

    def test_test_case_2_1(self, helpers, fake_data, fake_evaluate_thresholds):
        thresholds = {
            "client20DayAdv": 0,
            "lookBackPeriod": 7200,
            "market20DayAdv": 0,
            "markingType": "Marking the open",
            "minimumNotionalCurrency": "GBP",
            "priceSpike": 0,
            "minimumNotional": 0,
        }

        filters = {
            "bool": {
                "must": {
                    "terms": {
                        "sourceKey": [
                            "s3://mar.uat.steeleye.co/flows/order-universal-steeleye-trade-blotter/steeleyeBlotter.mar.markingTheOpen.2.csv"
                        ]
                    }
                }
            }
        }

        context = helpers.get_context(thresholds=thresholds, filters=filters)

        strategy = Strategy(context=context)

        strategy.queries.get_additional_fields_result = MagicMock()
        strategy.queries.get_additional_fields_result.return_value = pd.DataFrame()

        data = fake_data(
            TEST_DATA.joinpath("marking_the_open_tc_2_data.csv"),
            ["timestamps.orderSubmitted"],
        )
        thr_data = fake_data(
            TEST_DATA.joinpath("marking_the_open_tc_2_1_thr_data.csv"),
            ["Calendar End", "Calendar Start"],
        )

        strategy._apply_strategy_mini_batch(data=data, thr_data=thr_data)

        assert len(strategy.scenarios) == 1

    def test_test_case_2_4(self, helpers, fake_data, fake_evaluate_thresholds):
        thresholds = {
            "client20DayAdv": 0,
            "lookBackPeriod": 5000,
            "market20DayAdv": 0.01,
            "markingType": "Marking the open (including open-auction)",
            "minimumNotionalCurrency": "USD",
            "priceSpike": 0.01,
            "minimumNotional": 1,
        }

        filters = {
            "bool": {
                "must": {
                    "terms": {
                        "sourceKey": [
                            "s3://mar.uat.steeleye.co/flows/order-universal-steeleye-trade-blotter/steeleyeBlotter.mar.markingTheOpen.2.csv"
                        ]
                    }
                }
            }
        }

        context = helpers.get_context(thresholds=thresholds, filters=filters)

        strategy = Strategy(context=context)

        strategy.queries.get_additional_fields_result = MagicMock()
        strategy.queries.get_additional_fields_result.return_value = pd.DataFrame()

        data = fake_data(
            TEST_DATA.joinpath("marking_the_open_tc_2_data.csv"),
            ["timestamps.orderSubmitted"],
        )
        thr_data = fake_data(
            TEST_DATA.joinpath("marking_the_open_tc_2_4_thr_data.csv"),
            ["Calendar End", "Calendar Start"],
        )
        market_data = fake_data(
            TEST_DATA.joinpath("marking_the_open_tc_2_4_market_data.csv"),
            ["Date"],
        )
        price_spike_data = fake_data(
            TEST_DATA.joinpath("marking_the_open_tc_2_4_price_spike_data.csv"),
            ["price_spike_dt_start_utc", "price_spike_dt_end_utc"],
        )

        strategy.queries.get_market_data_adv = MagicMock()
        strategy.queries.get_market_data_adv.return_value = market_data

        strategy.queries.get_market_data_for_price_spike = MagicMock()
        strategy.queries.get_market_data_for_price_spike.return_value = price_spike_data

        strategy._apply_strategy_mini_batch(data=data, thr_data=thr_data)

        assert len(strategy.scenarios) == 2

    def test_test_case_2_1_open_auction(self, helpers, fake_data, fake_evaluate_thresholds):
        thresholds = {
            "client20DayAdv": 0,
            "lookBackPeriod": 7200,
            "market20DayAdv": 0,
            "markingType": "Marking the open-auction",
            "minimumNotionalCurrency": "GBP",
            "priceSpike": 0,
            "minimumNotional": 0,
        }

        filters = {
            "bool": {
                "must": {
                    "terms": {
                        "sourceKey": [
                            "s3://mar.uat.steeleye.co/flows/order-universal-steeleye-trade-blotter/steeleyeBlotter.mar.markingTheOpen.2.csv"
                        ]
                    }
                }
            }
        }

        context = helpers.get_context(thresholds=thresholds, filters=filters)

        strategy = Strategy(context=context)

        data = fake_data(
            TEST_DATA.joinpath("marking_the_open_tc_2_data.csv"),
            ["timestamps.orderSubmitted"],
        )
        thr_data = fake_data(
            TEST_DATA.joinpath("marking_the_open_tc_2_1_open_auction_thr_data.csv"),
            ["Calendar End", "Calendar Start"],
        )

        strategy._apply_strategy_mini_batch(data=data, thr_data=thr_data)

        # does not produce alerts. orderSubmittedTSLocal outside the window
        assert len(strategy.scenarios) == 0

    def test_no_venue_audit(self, helpers, mock_mto_query):
        singleton_audit_object.delete_local_audit_files()

        thresholds = {
            "client20DayAdv": 0,
            "lookBackPeriod": 7200,
            "market20DayAdv": 0,
            "markingType": "Marking the open",
            "minimumNotionalCurrency": "GBP",
            "priceSpike": 0,
            "minimumNotional": 0,
        }

        filters = {}

        context = helpers.get_context(thresholds=thresholds, filters=filters)

        strategy = Strategy(context=context)

        strategy.queries.get_venues_to_analyse_venues = MagicMock()
        strategy.queries.get_venues_to_analyse_venues.return_value = []

        strategy._get_thr_data()

        with open(singleton_audit_object.mar_audit_path.joinpath("StepAudit.json"), "r") as f:
            data = f.readlines()
        assert len(data) == 1

        with open(
            singleton_audit_object.mar_audit_path.joinpath("AggregatedStepAudit.json"),
            "r",
        ) as f:
            data = f.readlines()
        assert len(data) == 1

    def test_thr_data_audit(self, helpers, mock_mto_query):
        singleton_audit_object.delete_local_audit_files()

        thresholds = {
            "client20DayAdv": 0,
            "lookBackPeriod": 7200,
            "market20DayAdv": 0,
            "markingType": "Marking the open",
            "minimumNotionalCurrency": "GBP",
            "priceSpike": 0,
            "minimumNotional": 0,
        }

        filters = {}

        context = helpers.get_context(thresholds=thresholds, filters=filters)

        strategy = Strategy(context=context)

        strategy._get_thr_data = MagicMock()
        strategy._get_thr_data.return_value = pd.DataFrame()

        strategy._apply_strategy()

        with open(singleton_audit_object.mar_audit_path.joinpath("StepAudit.json"), "r") as f:
            data = f.readlines()
        assert len(data) == 1

        with open(
            singleton_audit_object.mar_audit_path.joinpath("AggregatedStepAudit.json"),
            "r",
        ) as f:
            data = f.readlines()
        assert len(data) == 1

    def test_record_analysed_audit(self, helpers, mock_mto_query):
        singleton_audit_object.delete_local_audit_files()

        thresholds = {
            "client20DayAdv": 0,
            "lookBackPeriod": 7200,
            "market20DayAdv": 0,
            "markingType": "Marking the open",
            "minimumNotionalCurrency": "GBP",
            "priceSpike": 0,
            "minimumNotional": 0,
        }

        filters = {}

        context = helpers.get_context(thresholds=thresholds, filters=filters)

        strategy = Strategy(context=context)

        strategy._get_thr_data = MagicMock()
        strategy._get_thr_data.return_value = pd.read_csv(TEST_DATA.joinpath("thr_data.csv"))

        strategy.queries.get_data_to_analyse = MagicMock()
        strategy.queries.get_data_to_analyse.return_value = [
            pd.read_csv(TEST_DATA.joinpath("data.csv"))
        ]

        strategy._apply_strategy_mini_batch = MagicMock()
        strategy._apply_strategy_mini_batch.return_value = None

        market_abuse_audit_object.records_analysed = 0

        strategy._apply_strategy()

        assert market_abuse_audit_object.records_analysed > 0

    def test_apply_strategy_mini_batch_audit(self, helpers, mock_mto_query):
        singleton_audit_object.delete_local_audit_files()

        thresholds = {
            "client20DayAdv": 0,
            "lookBackPeriod": 7200,
            "market20DayAdv": 0,
            "markingType": "Marking the open",
            "minimumNotionalCurrency": "GBP",
            "priceSpike": 0,
            "minimumNotional": 0,
        }

        filters = {}

        context = helpers.get_context(thresholds=thresholds, filters=filters)

        strategy = Strategy(context=context)

        thr_data = pd.read_csv(
            TEST_DATA.joinpath("thr_data.csv"),
            index_col=0,
        )
        thr_data[CoppClarkColumn.CALENDAR_START] = datetime.now()
        thr_data[CoppClarkColumn.CALENDAR_END] = pd.to_datetime(
            thr_data[CoppClarkColumn.CALENDAR_END], format="mixed"
        )

        strategy._apply_strategy_mini_batch(
            data=pd.read_csv(
                TEST_DATA.joinpath("data.csv"),
                index_col=0,
                parse_dates=[
                    OrderField.TS_ORD_SUBMITTED,
                ],
            ),
            thr_data=thr_data,
        )

        with open(singleton_audit_object.mar_audit_path.joinpath("StepAudit.json"), "r") as f:
            data = f.readlines()
        assert len(data) == 1

        with open(
            singleton_audit_object.mar_audit_path.joinpath("AggregatedStepAudit.json"),
            "r",
        ) as f:
            data = f.readlines()
        assert len(data) == 1

    def test_apply_strategy_mini_batch_audit_1(self, helpers, mock_mto_query):
        singleton_audit_object.delete_local_audit_files()

        thresholds = {
            "client20DayAdv": 0,
            "lookBackPeriod": 7200,
            "market20DayAdv": 0,
            "markingType": "Marking the open",
            "minimumNotionalCurrency": "GBP",
            "priceSpike": 0,
            "minimumNotional": 0,
        }

        filters = {}

        context = helpers.get_context(thresholds=thresholds, filters=filters)

        strategy = Strategy(context=context)

        strategy._calc_marking_times = MagicMock()
        strategy._calc_marking_times.return_value = pd.DataFrame()

        thr_data = pd.read_csv(
            TEST_DATA.joinpath("thr_data.csv"),
            index_col=0,
        )
        thr_data[CoppClarkColumn.CALENDAR_START] = pd.to_datetime(
            thr_data[CoppClarkColumn.CALENDAR_START], format="mixed"
        )
        thr_data[CoppClarkColumn.CALENDAR_END] = pd.to_datetime(
            thr_data[CoppClarkColumn.CALENDAR_END], format="mixed"
        )

        strategy._apply_strategy_mini_batch(
            data=pd.read_csv(
                TEST_DATA.joinpath("data.csv"),
                index_col=0,
                parse_dates=[
                    OrderField.TS_ORD_SUBMITTED,
                ],
            ),
            thr_data=thr_data,
        )

        with open(singleton_audit_object.mar_audit_path.joinpath("StepAudit.json"), "r") as f:
            data = f.readlines()
        assert len(data) == 2

        with open(
            singleton_audit_object.mar_audit_path.joinpath("AggregatedStepAudit.json"),
            "r",
        ) as f:
            data = f.readlines()
        assert len(data) == 2

    def test_apply_strategy_mini_batch_audit_2(self, helpers, mock_mto_query):
        singleton_audit_object.delete_local_audit_files()

        thresholds = {
            "client20DayAdv": 0,
            "lookBackPeriod": 7200,
            "market20DayAdv": 0,
            "markingType": "Marking the open",
            "minimumNotionalCurrency": "GBP",
            "priceSpike": 0,
            "minimumNotional": 0,
        }

        filters = {}

        context = helpers.get_context(thresholds=thresholds, filters=filters)

        strategy = Strategy(context=context)

        thr_data = pd.read_csv(
            TEST_DATA.joinpath("thr_data.csv"),
            index_col=0,
        )
        thr_data[CoppClarkColumn.CALENDAR_START] = pd.to_datetime(
            thr_data[CoppClarkColumn.CALENDAR_START], format="mixed"
        )
        thr_data[CoppClarkColumn.CALENDAR_END] = pd.to_datetime(
            thr_data[CoppClarkColumn.CALENDAR_END], format="mixed"
        )

        calc_marking_time = pd.read_csv(
            TEST_DATA.joinpath("calc_marking_time.csv"),
            index_col=0,
        )
        calc_marking_time[DFColumns.MARKET_TIME_END_LOCAL] = pd.NA
        calc_marking_time[DFColumns.MARKET_TIME_START_LOCAL] = pd.NA

        strategy._calc_marking_times = MagicMock()
        strategy._calc_marking_times.return_value = calc_marking_time

        strategy._apply_strategy_mini_batch(
            data=pd.read_csv(
                TEST_DATA.joinpath("data.csv"),
                index_col=0,
                parse_dates=[
                    OrderField.TS_ORD_SUBMITTED,
                ],
            ),
            thr_data=thr_data,
        )

        with open(singleton_audit_object.mar_audit_path.joinpath("StepAudit.json"), "r") as f:
            data = f.readlines()
        assert len(data) == 2

        with open(
            singleton_audit_object.mar_audit_path.joinpath("AggregatedStepAudit.json"),
            "r",
        ) as f:
            data = f.readlines()
        assert len(data) == 2

    def test_calc_marking_time_audit(self, helpers, mock_mto_query):
        singleton_audit_object.delete_local_audit_files()

        thresholds = {
            "client20DayAdv": 0,
            "lookBackPeriod": 7200,
            "market20DayAdv": 0,
            "markingType": "Marking the open",
            "minimumNotionalCurrency": "GBP",
            "priceSpike": 0,
            "minimumNotional": 0,
        }

        filters = {}

        context = helpers.get_context(thresholds=thresholds, filters=filters)

        strategy = Strategy(context=context)

        data = pd.read_csv(
            TEST_DATA.joinpath("calc_marking_time.csv"),
            index_col=0,
        )
        data[CoppClarkColumn.PHASE_TYPE] = pd.NA

        method_id = str(uuid.uuid4())
        strategy._calc_marking_times(data, method_id)

        with open(singleton_audit_object.mar_audit_path.joinpath("StepAudit.json"), "r") as f:
            data = f.readlines()
        assert len(data) == 3

    def test_calc_marking_time_audit_1(self, helpers, mock_mto_query):
        singleton_audit_object.delete_local_audit_files()

        thresholds = {
            "client20DayAdv": 0,
            "lookBackPeriod": 7200,
            "market20DayAdv": 0,
            "markingType": "Marking the open-auction",
            "minimumNotionalCurrency": "GBP",
            "priceSpike": 0,
            "minimumNotional": 0,
        }

        filters = {}

        context = helpers.get_context(thresholds=thresholds, filters=filters)

        strategy = Strategy(context=context)

        data = pd.read_csv(
            TEST_DATA.joinpath("calc_marking_time.csv"),
            index_col=0,
        )
        data[CoppClarkColumn.PHASE_TYPE] = pd.NA

        method_id = str(uuid.uuid4())
        strategy._calc_marking_times(data, method_id)

        with open(singleton_audit_object.mar_audit_path.joinpath("StepAudit.json"), "r") as f:
            data = f.readlines()
        assert len(data) == 3

    def test_calc_marking_time_audit_2(self, helpers, mock_mto_query):
        singleton_audit_object.delete_local_audit_files()

        thresholds = {
            "client20DayAdv": 0,
            "lookBackPeriod": 7200,
            "market20DayAdv": 0,
            "markingType": "Marking the open (including open-auction)",
            "minimumNotionalCurrency": "GBP",
            "priceSpike": 0,
            "minimumNotional": 0,
        }

        filters = {}

        context = helpers.get_context(thresholds=thresholds, filters=filters)

        strategy = Strategy(context=context)

        data = pd.read_csv(
            TEST_DATA.joinpath("calc_marking_time.csv"),
            index_col=0,
        )

        method_id = str(uuid.uuid4())
        strategy._calc_marking_times(data, method_id)

        with open(singleton_audit_object.mar_audit_path.joinpath("StepAudit.json"), "r") as f:
            data = f.readlines()
        assert len(data) == 2

    def test_algo_audit(self, helpers, mock_mto_query):
        singleton_audit_object.delete_local_audit_files()

        thresholds = {
            "client20DayAdv": 1,
            "lookBackPeriod": 7200,
            "market20DayAdv": 0,
            "markingType": "Marking the open",
            "minimumNotionalCurrency": "GBP",
            "priceSpike": 0,
            "minimumNotional": 0,
        }

        filters = {}

        context = helpers.get_context(thresholds=thresholds, filters=filters)

        strategy = Strategy(context=context)

        data = pd.read_csv(
            TEST_DATA.joinpath("algo_data.csv"),
            index_col=0,
            parse_dates=[
                OrderField.TS_ORD_SUBMITTED,
                DFColumns.ORDER_SUBMITTED_TS_LOCAL,
                DFColumns.MARKET_TIME_START_LOCAL,
                DFColumns.MARKET_TIME_END_LOCAL,
            ],
        )
        data[DFColumns.MARKET_TIME_START_LOCAL] = data[DFColumns.MARKET_TIME_START_LOCAL].dt.time
        data[DFColumns.MARKET_TIME_END_LOCAL] = data[DFColumns.MARKET_TIME_END_LOCAL].dt.time

        strategy.queries.get_client_adv = MagicMock()
        strategy.queries.get_client_adv.return_value = pd.DataFrame()

        strategy._algo(data)

        with open(singleton_audit_object.mar_audit_path.joinpath("StepAudit.json"), "r") as f:
            data = f.readlines()
        assert len(data) == 3

        with open(
            singleton_audit_object.mar_audit_path.joinpath("AggregatedStepAudit.json"),
            "r",
        ) as f:
            data = f.readlines()
        assert len(data) == 3

    def test_algo_audit_1(self, helpers, mock_mto_query):
        singleton_audit_object.delete_local_audit_files()

        thresholds = {
            "client20DayAdv": 0,
            "lookBackPeriod": 7200,
            "market20DayAdv": 1,
            "markingType": "Marking the open",
            "minimumNotionalCurrency": "GBP",
            "priceSpike": 0,
            "minimumNotional": 0,
        }

        filters = {}

        context = helpers.get_context(thresholds=thresholds, filters=filters)

        strategy = Strategy(context=context)

        data = pd.read_csv(
            TEST_DATA.joinpath("algo_data.csv"),
            index_col=0,
            parse_dates=[
                OrderField.TS_ORD_SUBMITTED,
                DFColumns.ORDER_SUBMITTED_TS_LOCAL,
                DFColumns.MARKET_TIME_START_LOCAL,
                DFColumns.MARKET_TIME_END_LOCAL,
            ],
        )
        data[DFColumns.MARKET_TIME_START_LOCAL] = data[DFColumns.MARKET_TIME_START_LOCAL].dt.time
        data[DFColumns.MARKET_TIME_END_LOCAL] = data[DFColumns.MARKET_TIME_END_LOCAL].dt.time

        strategy.queries.get_market_data_adv = MagicMock()
        strategy.queries.get_market_data_adv.return_value = pd.DataFrame()

        strategy._algo(data)

        with open(singleton_audit_object.mar_audit_path.joinpath("StepAudit.json"), "r") as f:
            data = f.readlines()
        assert len(data) == 2

        with open(
            singleton_audit_object.mar_audit_path.joinpath("AggregatedStepAudit.json"),
            "r",
        ) as f:
            data = f.readlines()
        assert len(data) == 2

    def test_algo_audit_2(self, helpers, mock_mto_query):
        singleton_audit_object.delete_local_audit_files()
        thresholds = {
            "client20DayAdv": 0,
            "lookBackPeriod": 7200,
            "market20DayAdv": 0,
            "markingType": "Marking the open",
            "minimumNotionalCurrency": "GBP",
            "priceSpike": 1,
            "minimumNotional": 0,
        }

        filters = {}

        context = helpers.get_context(thresholds=thresholds, filters=filters)

        strategy = Strategy(context=context)

        data = pd.read_csv(
            TEST_DATA.joinpath("algo_data.csv"),
            index_col=0,
            parse_dates=[
                OrderField.TS_ORD_SUBMITTED,
                DFColumns.ORDER_SUBMITTED_TS_LOCAL,
                DFColumns.MARKET_TIME_START_LOCAL,
                DFColumns.MARKET_TIME_END_LOCAL,
            ],
        )
        data[DFColumns.MARKET_TIME_START_LOCAL] = data[DFColumns.MARKET_TIME_START_LOCAL].dt.time
        data[DFColumns.MARKET_TIME_END_LOCAL] = data[DFColumns.MARKET_TIME_END_LOCAL].dt.time

        strategy.queries.get_market_data_for_price_spike = MagicMock()
        strategy.queries.get_market_data_for_price_spike.return_value = pd.DataFrame()

        strategy._algo(data)

        with open(singleton_audit_object.mar_audit_path.joinpath("StepAudit.json"), "r") as f:
            data = f.readlines()
        assert len(data) == 2

        with open(
            singleton_audit_object.mar_audit_path.joinpath("AggregatedStepAudit.json"),
            "r",
        ) as f:
            data = f.readlines()
        assert len(data) == 2

    def test_case_5_1(self, fake_data, tc5_context, monkeypatch):
        """Test Case 5.1 Should pass the marketAdv and clientAdv thresholds and
        produce 1 alert."""
        with open(TEST_DATA_TC5.joinpath("client_adv_result.json")) as json_file:
            client_adv = addict.Dict(json.load(json_file))

        scenario_fields = pd.read_csv(TEST_DATA_TC5.joinpath("scenario_fields.csv"), index_col=0)

        data = fake_data(
            TEST_DATA_TC5.joinpath("tc5_data.csv"),
            ["timestamps.orderSubmitted"],
        )
        thr_data = fake_data(
            TEST_DATA_TC5.joinpath("thr_data.csv"), ["Calendar End", "Calendar Start"]
        )

        tc5_context.thresholds = {
            "client20DayAdv": 0.19,
            "lookBackPeriod": 600,
            "market20DayAdv": 0.19,
            "markingType": "Marking the open",
            "minimumNotionalCurrency": "EUR",
            "priceSpike": 0,
            "minimumNotional": 10000,
        }
        strategy = Strategy(context=tc5_context)

        monkeypatch.setattr(
            strategy.queries,
            "_market_data_client",
            fake_market_client(),
        )

        strategy.queries._sdp_repository.search_query = MagicMock()
        strategy.queries._sdp_repository.search_query.return_value = client_adv
        strategy.queries.get_additional_fields_result = MagicMock()
        strategy.queries.get_additional_fields_result.return_value = scenario_fields

        expected_market_adv = 2012373.70000
        expected_client_adv = 3256792.32000

        strategy._apply_strategy_mini_batch(data=data, thr_data=thr_data)

        alerts = strategy.scenarios
        assert len(alerts) == 1

        top_level = alerts[0]._scenario.additionalFields["topLevel"]
        assert top_level["marketAdv"] == expected_market_adv
        assert top_level["clientAdv"] == expected_client_adv

    def test_case_5_3(self, fake_data, tc5_context, monkeypatch):
        """Test Case 5.3 Should pass the clientAdv thresholds and produce 1
        alert."""
        data = fake_data(
            TEST_DATA_TC5.joinpath("tc5_data.csv"),
            ["timestamps.orderSubmitted"],
        )
        thr_data = fake_data(
            TEST_DATA_TC5.joinpath("thr_data.csv"), ["Calendar End", "Calendar Start"]
        )

        with open(TEST_DATA_TC5.joinpath("client_adv_result.json")) as json_file:
            client_adv = addict.Dict(json.load(json_file))

        scenario_fields = pd.read_csv(TEST_DATA_TC5.joinpath("scenario_fields.csv"), index_col=0)

        tc5_context.thresholds = {
            "client20DayAdv": 0.479,
            "lookBackPeriod": 600,
            "market20DayAdv": 0.0,
            "markingType": "Marking the open",
            "minimumNotionalCurrency": "EUR",
            "priceSpike": 0,
            "minimumNotional": 10000,
        }
        strategy = Strategy(context=tc5_context)

        monkeypatch.setattr(
            strategy.queries,
            "_market_data_client",
            fake_market_client(),
        )

        strategy.queries._sdp_repository.search_query = MagicMock()
        strategy.queries._sdp_repository.search_query.return_value = client_adv
        strategy.queries.get_additional_fields_result = MagicMock()
        strategy.queries.get_additional_fields_result.return_value = scenario_fields

        expected_client_adv = 3256792.32000

        strategy._apply_strategy_mini_batch(data=data, thr_data=thr_data)

        alerts = strategy.scenarios
        assert len(alerts) == 1

        top_level = alerts[0]._scenario.additionalFields["topLevel"]
        assert top_level["clientAdv"] == expected_client_adv
        assert "marketAdv" not in list(top_level)

    def test_case_5_4(self, fake_data, tc5_context, monkeypatch):
        """Test Case 5.4 Does not produce alerts because it fails the clientAdv
        threshold."""
        data = fake_data(
            TEST_DATA_TC5.joinpath("tc5_data.csv"),
            ["timestamps.orderSubmitted"],
        )
        thr_data = fake_data(
            TEST_DATA_TC5.joinpath("thr_data.csv"), ["Calendar End", "Calendar Start"]
        )

        with open(TEST_DATA_TC5.joinpath("client_adv_result.json")) as json_file:
            client_adv = addict.Dict(json.load(json_file))

        tc5_context.thresholds = {
            "client20DayAdv": 0.49,
            "lookBackPeriod": 600,
            "market20DayAdv": 0,
            "markingType": "Marking the open",
            "minimumNotionalCurrency": "EUR",
            "priceSpike": 0,
            "minimumNotional": 10000,
        }
        strategy = Strategy(context=tc5_context)

        strategy.queries._sdp_repository.search_query = MagicMock()
        strategy.queries._sdp_repository.search_query.return_value = client_adv

        data = strategy._get_thr_times(data=data, thr_data=thr_data)
        data = strategy._filter_data_by_thr_time_period(data=data)
        adv_data = strategy._add_client_adv(data=data)

        inst_adv_mask = adv_data[DFColumns.ORDER_QTY_INST_ADV] > strategy._th_client_20_day_adv

        assert adv_data[inst_adv_mask].empty
