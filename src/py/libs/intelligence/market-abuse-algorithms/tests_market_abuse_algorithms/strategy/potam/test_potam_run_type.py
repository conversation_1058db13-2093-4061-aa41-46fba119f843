# ruff: noqa: E501
# type: ignore
import pytest
from market_abuse_algorithms.strategy.base.strategy import (
    market_abuse_audit_object,
    singleton_audit_object,
)
from market_abuse_algorithms.strategy.potam.static import (
    RunTypeMode,
    StrategyMetaField,
    ThresholdsNames,
)
from market_abuse_algorithms.strategy.potam.strategy import Strategy
from tests_market_abuse_algorithms.strategy.potam.fakers import (
    mock_get_orders_to_analyse_geral,
    mock_get_potam_cases_geral,
    mock_non_potam_hits_matching_order_id,
    mock_potam_repository,
)
from unittest.mock import MagicMock


@pytest.fixture()
def mock_potam_isins(monkeypatch, *args, **kwargs):
    mock_potam_repository(monkeypatch)


market_abuse_audit_object.records_analysed = 0

filters = {
    "bool": {
        "must": [
            {"range": {"&timestamp": {"gte": "2017-01-01T00:00:00.000000"}}},
        ]
    }
}


class TestPotamRunTypes:
    def test_run_type_threshold_all_date_range(self, helpers, monkeypatch, mock_potam_isins):
        import market_abuse_algorithms.strategy.potam.query

        market_abuse_algorithms.strategy.potam.query.PotamQuery.get_orders_to_analyse = MagicMock()
        market_abuse_algorithms.strategy.potam.query.PotamQuery.get_orders_to_analyse.return_value = mock_get_orders_to_analyse_geral()
        market_abuse_algorithms.strategy.potam.query.PotamQuery.get_potam_cases = MagicMock()
        market_abuse_algorithms.strategy.potam.query.PotamQuery.get_potam_cases.return_value = (
            mock_get_potam_cases_geral()
        )
        market_abuse_algorithms.strategy.potam.query.PotamQuery.non_potam_hits_matching_order_id = (
            MagicMock()
        )
        market_abuse_algorithms.strategy.potam.query.PotamQuery.non_potam_hits_matching_order_id.return_value = mock_non_potam_hits_matching_order_id()

        thresholds = {
            ThresholdsNames.EXTENDED_WINDOW_DAYS_AFTER: 10,
            ThresholdsNames.EXTENDED_WINDOW_DAYS_BEFORE: 10,
        }

        context = helpers.get_context(thresholds=thresholds, filters=filters)
        strategy = Strategy(context=context)

        potam_isins = strategy.queries.isins_from_potam_cases()
        orders = strategy.queries.orders_to_analyse(isins=potam_isins)

        assert not orders.empty

        isins_from_orders = orders[StrategyMetaField.ISIN].unique().tolist()

        potam_cases = strategy.queries.potam_cases_to_analyse(isins_from_orders)

        orders = strategy.add_date_column_to_orders(orders)
        hits = strategy._algo(orders=orders, potam_cases=potam_cases)

        assert hits.empty
        singleton_audit_object.delete_local_audit_files()

    def test_run_type_threshold_outside_mode(self, helpers, monkeypatch, mock_potam_isins):
        import market_abuse_algorithms.data_source.repository.base
        import market_abuse_algorithms.strategy.potam.query

        market_abuse_algorithms.strategy.potam.query.PotamQuery.get_orders_to_analyse = MagicMock()
        market_abuse_algorithms.strategy.potam.query.PotamQuery.get_orders_to_analyse.return_value = mock_get_orders_to_analyse_geral()
        market_abuse_algorithms.strategy.potam.query.PotamQuery.get_potam_cases = MagicMock()
        market_abuse_algorithms.strategy.potam.query.PotamQuery.get_potam_cases.return_value = (
            mock_get_potam_cases_geral()
        )
        market_abuse_algorithms.strategy.potam.query.PotamQuery.non_potam_hits_matching_order_id = (
            MagicMock()
        )
        market_abuse_algorithms.strategy.potam.query.PotamQuery.non_potam_hits_matching_order_id.return_value = mock_non_potam_hits_matching_order_id()

        thresholds = {
            ThresholdsNames.EXTENDED_WINDOW_DAYS_AFTER: 6,
            ThresholdsNames.EXTENDED_WINDOW_DAYS_BEFORE: 6,
            ThresholdsNames.RUN_TYPE: RunTypeMode.OUTSIDE,
        }

        context = helpers.get_context(thresholds=thresholds, filters=filters)
        strategy = Strategy(context=context)

        potam_isins = strategy.queries.isins_from_potam_cases()
        orders = strategy.queries.orders_to_analyse(isins=potam_isins)

        assert not orders.empty

        isins_from_orders = orders[StrategyMetaField.ISIN].unique().tolist()

        potam_cases = strategy.queries.potam_cases_to_analyse(isins_from_orders)

        orders = strategy.add_date_column_to_orders(orders)
        hits = strategy._algo(orders=orders, potam_cases=potam_cases)

        assert hits.empty
        singleton_audit_object.delete_local_audit_files()

    def test_run_type_threshold_inside_mode(self, helpers, monkeypatch, mock_potam_isins):
        import market_abuse_algorithms.strategy.potam.query

        market_abuse_algorithms.strategy.potam.query.PotamQuery.get_orders_to_analyse = MagicMock()
        market_abuse_algorithms.strategy.potam.query.PotamQuery.get_orders_to_analyse.return_value = mock_get_orders_to_analyse_geral()
        market_abuse_algorithms.strategy.potam.query.PotamQuery.get_potam_cases = MagicMock()
        market_abuse_algorithms.strategy.potam.query.PotamQuery.get_potam_cases.return_value = (
            mock_get_potam_cases_geral()
        )
        market_abuse_algorithms.strategy.potam.query.PotamQuery.non_potam_hits_matching_order_id = (
            MagicMock()
        )
        market_abuse_algorithms.strategy.potam.query.PotamQuery.non_potam_hits_matching_order_id.return_value = mock_non_potam_hits_matching_order_id()

        thresholds = {
            ThresholdsNames.EXTENDED_WINDOW_DAYS_AFTER: 6,
            ThresholdsNames.EXTENDED_WINDOW_DAYS_BEFORE: 6,
            ThresholdsNames.RUN_TYPE: RunTypeMode.OUTSIDE,
        }

        context = helpers.get_context(thresholds=thresholds, filters=filters)
        strategy = Strategy(context=context)

        potam_isins = strategy.queries.isins_from_potam_cases()
        orders = strategy.queries.orders_to_analyse(isins=potam_isins)

        assert not orders.empty

        isins_from_orders = orders[StrategyMetaField.ISIN].unique().tolist()

        potam_cases = strategy.queries.potam_cases_to_analyse(isins_from_orders)

        orders_with_date_column = strategy.add_date_column_to_orders(orders)

        hits = strategy._algo(orders=orders_with_date_column, potam_cases=potam_cases)

        assert hits.empty
        singleton_audit_object.delete_local_audit_files()
