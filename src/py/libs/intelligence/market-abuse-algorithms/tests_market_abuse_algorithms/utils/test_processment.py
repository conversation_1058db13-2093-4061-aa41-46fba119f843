# ruff: noqa: E501
import datetime
import pandas as pd
import pytest
import unittest.mock as mock
from market_abuse_algorithms.data_source.query.sdp.order import OrderQuery
from market_abuse_algorithms.data_source.static.sdp.order import OrderField
from market_abuse_algorithms.data_source.static.utility import InstrumentRegexPatterns
from market_abuse_algorithms.strategy.insider_trading_news_refinitiv.static import (
    TransactionVolumeCurrency,
)
from market_abuse_algorithms.strategy.utils import get_sentiment_price_column
from market_abuse_algorithms.utils.processment import (
    exclude_orders,
    filter_instruments,
    get_grouping_columns,
    get_query_string_to_audit,
    process_executions_results,
)
from pathlib import Path
from se_elastic_schema.components.mar.strategy.insider_trading_v3.thresholds import (
    EvaluationType,
)
from typing import Dict

TEST_DATA = Path(__file__).parent.joinpath("test_data")


class TestEvaluationTypeVenue:
    def test_evaluation_column_check_and_fallback_no_evaluation_type_grouping(self, helpers):
        """Test to assert the evaluation type is set to Company where no
        evaluation type grouping occurs It is expected for resulting grouping
        columns to be the same as the original."""

        data = pd.DataFrame({"instrument": ["foo123", "bar456"]})

        grouping_columns = {"instrumentGrouping": "instrument"}

        evaluation_type = EvaluationType.COMPANY

        result_grouping_columns: Dict = get_grouping_columns(
            data=data,
            evaluation_type=evaluation_type,
        )

        assert result_grouping_columns == grouping_columns

    def test_evaluation_column_check_and_fallback_evaluation_type_grouping_existing_column(
        self, helpers
    ):
        """Test to assert the evaluation type is set to Trader and the column
        exists in the data It is expected for resulting grouping columns to be
        the same as the original."""
        data = pd.DataFrame(
            {
                "instrument": ["foo123", "bar456"],
                "traderFileIdentifier": ["trader_1", "trader_2"],
            }
        )

        grouping_columns = {
            "instrumentGrouping": "instrument",
            "evaluationGrouping": "traderFileIdentifier",
        }

        evaluation_type = EvaluationType.TRADER

        result_grouping_columns = get_grouping_columns(
            data=data,
            evaluation_type=evaluation_type,
        )

        assert result_grouping_columns == grouping_columns

    def test_evaluation_column_check_and_fallback_evaluation_type_grouping_non_existing_column_return_none(
        self, helpers
    ):
        """Test to assert the evaluation type is set to Trader and the column
        does not exist in the data Expected for resulting grouping columns is
        set to None."""
        data = pd.DataFrame({"instrument": ["foo123", "bar456"]})

        evaluation_type = EvaluationType.TRADER

        result_grouping_columns = get_grouping_columns(
            data=data,
            evaluation_type=evaluation_type,
        )
        assert result_grouping_columns is None

    def test_evaluation_column_check_and_fallback_evaluation_type_grouping_non_existing_column_modify_field(
        self, helpers
    ):
        """Test to assert the evaluation type is set to Fund and the column
        does not exist in the data Expected for resulting grouping columns have
        the evaluationGrouping field modified to fallback field."""
        data = pd.DataFrame({"instrument": ["foo123", "bar456"]})

        evaluation_type = EvaluationType.FUND

        result_grouping_columns = get_grouping_columns(
            data=data,
            evaluation_type=evaluation_type,
        )

        expected_grouping_columns = {
            "instrumentGrouping": "instrument",
            "evaluationGrouping": "traderFileIdentifier",
        }

        assert result_grouping_columns == expected_grouping_columns


def test_get_query_string_to_audit_with_pd_datetime():
    query = OrderQuery()

    filters = {
        "bool": {
            "filter": [
                {
                    "range": {
                        "&timestamp": {
                            "gte": pd.to_datetime("2024-01-16T16:21:31.229967Z"),
                            "lte": "2024-01-16T23:21:31.229967Z",
                        }
                    }
                }
            ]
        }
    }

    query.add_iris_filters(filters)

    new_query = get_query_string_to_audit(query=query)

    expected_query = "{'query': {'bool': {'must_not': [{'exists': {'field': '&expiry'}}], 'must': [{'terms': {'&model': ['Order']}}], 'filter': [{'terms': {'executionDetails.orderStatus': ['NEWO']}}, {'range': {'&timestamp': {'gte': Timestamp('2024-01-16 16:21:31.229967+0000', tz='UTC'), 'lte': '2024-01-16T23:21:31.229967Z'}}}]}}}"
    assert new_query == expected_query


def test_get_query_string_to_audit_with_date_as_string():
    query = OrderQuery()

    filters = {
        "bool": {
            "filter": [
                {
                    "range": {
                        "&timestamp": {
                            "gte": "2024-01-16T16:21:31.229967Z",
                            "lte": "2024-01-16T23:21:31.229967Z",
                        }
                    }
                }
            ]
        }
    }

    query.add_iris_filters(filters)

    new_query = get_query_string_to_audit(query=query)

    expected_query = '{\n    "query": {\n        "bool": {\n            "must_not": [\n                {\n                    "exists": {\n                        "field": "&expiry"\n                    }\n                }\n            ],\n            "must": [\n                {\n                    "terms": {\n                        "&model": [\n                            "Order"\n                        ]\n                    }\n                }\n            ],\n            "filter": [\n                {\n                    "terms": {\n                        "executionDetails.orderStatus": [\n                            "NEWO"\n                        ]\n                    }\n                },\n                {\n                    "range": {\n                        "&timestamp": {\n                            "gte": "2024-01-16T16:21:31.229967Z",\n                            "lte": "2024-01-16T23:21:31.229967Z"\n                        }\n                    }\n                }\n            ]\n        }\n    }\n}'
    assert new_query == expected_query


def test_get_query_string_to_audit_with_date_as_none():
    query = OrderQuery()

    filters = {
        "bool": {
            "filter": [
                {
                    "range": {
                        "&timestamp": {
                            "gte": None,
                            "lte": "2024-01-16T23:21:31.229967Z",
                        }
                    }
                }
            ]
        }
    }

    query.add_iris_filters(filters)

    new_query = get_query_string_to_audit(query=query)

    expected_query = '{\n    "query": {\n        "bool": {\n            "must_not": [\n                {\n                    "exists": {\n                        "field": "&expiry"\n                    }\n                }\n            ],\n            "must": [\n                {\n                    "terms": {\n                        "&model": [\n                            "Order"\n                        ]\n                    }\n                }\n            ],\n            "filter": [\n                {\n                    "terms": {\n                        "executionDetails.orderStatus": [\n                            "NEWO"\n                        ]\n                    }\n                },\n                {\n                    "range": {\n                        "&timestamp": {\n                            "gte": null,\n                            "lte": "2024-01-16T23:21:31.229967Z"\n                        }\n                    }\n                }\n            ]\n        }\n    }\n}'

    assert new_query == expected_query


def test_get_query_string_to_audit_error_to_dict():
    new_query = get_query_string_to_audit(query="")

    expected_query = ""
    assert new_query == expected_query


def test_processment_exclude_orders_diff_types():
    dataset_to_exclude = pd.read_csv(
        TEST_DATA.joinpath("test_processment_exclude_orders_dataset_to_exclude.csv"),
        index_col=0,
    )
    execution_parents = pd.read_csv(
        TEST_DATA.joinpath("test_processment_exclude_orders_execution_parents.csv"),
        index_col=0,
    )

    execution_parents["timestamps.orderSubmitted"] = execution_parents[
        "timestamps.orderSubmitted"
    ].apply(lambda x: pd.Timestamp(x))

    execution_parents["timestamps.orderStatusUpdated"] = execution_parents[
        "timestamps.orderStatusUpdated"
    ].apply(lambda x: pd.Timestamp(x))

    observation_period = {
        "start": pd.Timestamp("2024-07-12 00:00:00"),
        "end": pd.Timestamp("2024-07-16 23:59:59"),
    }
    method_id = "teste1234455"
    event_day = datetime.datetime(2024, 7, 17, 0, 0)

    orders_states_with_events = exclude_orders(
        dataset_to_exclude=dataset_to_exclude,
        execution_parents=execution_parents,
        observation_period=observation_period,
        method_id=method_id,
        event_day=event_day,
    )

    assert not orders_states_with_events.empty
    assert orders_states_with_events.shape == (4, 64)


def test_get_sentiment_price_column_with_best_ex():
    data = pd.read_csv(
        TEST_DATA.joinpath("sentiment_price_column_data.csv"),
    )

    currency = TransactionVolumeCurrency.GBP

    selected_column = get_sentiment_price_column(data=data, currency=currency)

    assert selected_column == OrderField.get_best_exc_ord_ecb_ref_rate_ccy("GBP")


def test_get_sentiment_price_column_with_best_ex_with_missing_row():
    data = pd.read_csv(
        TEST_DATA.joinpath("sentiment_price_column_data.csv"),
    )

    data.iloc[0, 2] = pd.NA

    currency = TransactionVolumeCurrency.GBP

    selected_column = get_sentiment_price_column(data=data, currency=currency)

    assert selected_column == OrderField.PC_FD_INIT_QTY


def test_get_sentiment_price_column_with_best_ex_native_currency():
    data = pd.read_csv(
        TEST_DATA.joinpath("sentiment_price_column_data.csv"),
    )

    data.iloc[:, 0] = "GBP"

    currency = TransactionVolumeCurrency.GBP

    selected_column = get_sentiment_price_column(data=data, currency=currency)

    assert selected_column == OrderField.BEST_EXC_DATA_ORD_VOL_NATIVE


def test_get_sentiment_price_column_without_bext_ex():
    data = pd.read_csv(
        TEST_DATA.joinpath("sentiment_price_column_data.csv"),
    )

    data = data.drop(
        columns=[
            OrderField.BEST_EXC_DATA_ORD_VOL_NATIVE_CURRENCY,
            OrderField.get_best_exc_ord_ecb_ref_rate_ccy("GBP"),
        ]
    )

    currency = TransactionVolumeCurrency.GBP

    selected_column = get_sentiment_price_column(data=data, currency=currency)

    assert selected_column == OrderField.PC_FD_INIT_QTY


def test_filter_instruments():
    """Test which verifies all instruments are returned when both
    instrumentIdCode and underlying columns exist."""
    data_to_be_analysed = pd.read_csv(
        TEST_DATA.joinpath("mc_140_instrument_data.csv"),
    )

    list_of_instruments = ["NL0010273215"]
    expected_output = {"DE000C1N6LG5EURXEUR", "NL0010273215", "DE000C1N6LG5"}

    output = filter_instruments(
        list_of_instruments=list_of_instruments, data_to_be_analysed=data_to_be_analysed
    )

    assert set(output) == expected_output


@pytest.mark.parametrize(
    "csv_filename, cds_present, expected_shape",
    [
        ("mc_8_cds_data_derivatives.csv", True, (6, 4)),
        ("mc_8_cds_data_non_derivatives.csv", True, (6, 4)),
        ("mc_8_non_cds_data_derivatives.csv", False, (6, 4)),
        ("mc_8_non_cds_data_non_derivatives.csv", False, (5, 4)),
    ],
)
def test_process_executions_results_cds_check(csv_filename, cds_present, expected_shape):
    """Test which verifies that CDS orders are analyzed."""
    data_to_be_analysed = pd.read_csv(
        TEST_DATA.joinpath(csv_filename),
    )

    output = process_executions_results(raw_query_df=data_to_be_analysed, initial_query=True)

    assert (
        any(output.loc[:, OrderField.INST_CLASSIFICATION].str.match(InstrumentRegexPatterns.CDS))
        == cds_present
    )
    assert output.shape == expected_shape


def test_process_executions_results_derivatives_check():
    """Test which verifies that derivatives executions are analyzed."""
    data_to_be_analysed = pd.read_csv(
        TEST_DATA.joinpath("ucm_162_derivatives.csv"),
    )
    output = process_executions_results(raw_query_df=data_to_be_analysed, initial_query=False)

    assert output.shape == (5, 16)

    with mock.patch(
        "market_abuse_algorithms.utils.processment.process_derivative_results"
    ) as mock_process_derivative_results:
        _ = process_executions_results(raw_query_df=data_to_be_analysed, initial_query=False)
    mock_process_derivative_results.assert_called()
