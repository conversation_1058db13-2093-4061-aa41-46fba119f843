{"&id": "280d5a04-0c1f-4b2c-a93d-998c76fefcd7", "createdByAdmin": true, "query": {"description": "Test Aries RunCommsWatch", "filter": {"chunks": [{"category": "people/sent/name", "f": "sentByField('name',['<PERSON><PERSON>'])", "id": "2f955f1b-9b57-49ff-9e01-376861378f76"}], "flangVersion": "1.2"}, "kind": "BESPOKE", "name": "Test Aries RunCommsWatch"}, "pendingChange": true, "&key": "SurveillanceWatch:280d5a04-0c1f-4b2c-a93d-998c76fefcd7:1683639074793", "type": "SCHEDULED", "createdOn": "2023-05-09T13:31:14Z", "&model": "SurveillanceWatch", "queryType": "COMMUNICATIONS", "&version": 1, "createdBy": "miguel.pinto", "name": "Test Aries RunCommsWatch", "&hash": "12e4bdd8f18a8cd96afff565fd2cd19ca625e79780472fdc9bcaccdeb58d1629", "&timestamp": 1683639074793, "&user": "<EMAIL>", "status": "ACTIVE"}