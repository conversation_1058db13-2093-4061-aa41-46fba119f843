import addict
import json
from pathlib import Path
from se_elastic_schema.models.tenant.surveillance.surveillance_watch import SurveillanceWatch

TEST_DATA_PATH = Path(__file__).parent.parent.joinpath("data")


def get_surveillance_watch(watch_filename: str) -> SurveillanceWatch:
    sample_surveillance_watch = json.loads(TEST_DATA_PATH.joinpath(watch_filename).read_text())
    return SurveillanceWatch.validate(sample_surveillance_watch)


def get_watch_query_generated(filename: str) -> addict.Dict:
    sample_dict: dict = json.loads(TEST_DATA_PATH.joinpath(filename).read_text())
    return addict.Dict(sample_dict)
