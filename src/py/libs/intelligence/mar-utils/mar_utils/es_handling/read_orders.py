import flang
import logging
import numpy as np
import pandas as pd
from mar_utils.static.order_fields import OrderFields
from se_elastic_schema.models import Order
from se_elastic_schema.models.tenant.surveillance.surveillance_watch import SurveillanceWatch
from se_es_utils.search_after import search_after_query
from se_es_utils.slim_record_handler import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from typing import Dict, List, Optional, Tuple

log = logging.getLogger(__name__)


class ReadOrders:
    def __init__(self, record_handler: SlimRecordHandler):
        self.record_handler: <PERSON><PERSON>ecordHandler = record_handler

    def execute_detect_orders_search(
        self,
        watch_record: SurveillanceWatch,
        group_filter: List,
        tenant: str,
        date_orders_to_read: List[str],
        filters=dict,
        excluded_order_ids: Optional[List[str]] = None,
        required_fields: List = [],
        include_fields: List = [],
        **kwargs,
    ) -> pd.DataFrame:
        """Read orders from ES and construct a Dask DataFrame.

        - read from a directory in local filesystem / S3 bucket / Azure
        - discover all date-named parquet files (*.parq extension)
        - filter to the required date range
        - read and construct a Dask DataFrame (pointer to the data) which can be
        partially downloaded by distributed workers

        :param date_orders_to_read:
        :param watch_record:
        :param tenant:
        :param date_range:
        :param excluded_order_ids:
        :param model_index:
        :param kwargs:
        :return:  ddf.DataFrame: Single tenant order dataframe build from many parquet files
        """
        index = self.record_handler.get_es_alias(Order, tenant)

        required_columns = required_fields + include_fields

        filters_watch = watch_record.query.filters

        return self.es_detect_orders_search(
            strategy_filters=filters_watch,
            index=index,
            date_orders_to_read=date_orders_to_read,
            required_columns=required_columns,
            excluded_order_ids=excluded_order_ids,
            group_filter=group_filter,
            filters=filters,
        )

    def es_detect_orders_search(
        self,
        strategy_filters: str,
        index: str,
        date_orders_to_read: List[str],
        required_columns: List,
        filters: dict,
        excluded_order_ids: List[str] | None = None,
        group_filter: Optional[List] = None,
    ) -> pd.DataFrame:
        """

        :param strategy_filters:
        :param watch_record:
        :param date_orders_to_read:
        :param required_columns:
        :param excluded_order_ids:
        :return:
        """

        if not date_orders_to_read:
            log.warning("There are not date_orders_to_read.")

        if excluded_order_ids is None:
            excluded_order_ids = []

        log.info(f"required_columns: {required_columns}")

        # Add columns required by the filters
        if strategy_filters:
            parse_tree = flang.parse(strategy_filters)
            required_columns.extend(self.extract_field_names_from_filters(parse_tree=parse_tree))
        required_columns = list(set(required_columns))

        orders = parse_date_readers(
            data_list=[
                read_orders_by_date(
                    record_handler=self.record_handler,
                    date=date,
                    date_column=OrderFields.DATE,
                    columns_to_fetch=required_columns,
                    excluded_order_ids=excluded_order_ids,
                    index=index,
                    group_filter=group_filter,
                    filters=filters,
                )
                for date in date_orders_to_read
            ]
        )

        essential_cols: List[str] = [
            col
            for col in [
                self.record_handler.meta.key,
                OrderFields.ORD_IDENT_ID_CODE,
                self.record_handler.meta.id,
            ]
            if col in orders.columns
        ]

        orders = orders.dropna(subset=essential_cols)
        num_orders = len(orders.index)

        if num_orders == 0:
            log.warning("No records selected matched all filters.")
        log.info(
            "After discarding files missing mandatory columns and applying all filters "
            f"there are {num_orders} records."
        )
        return orders

    def extract_field_names_from_filters(self, parse_tree: Tuple) -> List[str]:
        """Extract the field names needed to apply a given filter.

        A breadth first transversal of the tree is performed
        during which all field names are collected in a list.
        Raises an error if a cycle is detected.

        Example:
            >>> parse_tree_deep = (
                "and",
                ("or", ("in", "a_field", ["a_value"]), ("in", "b_field", ["b_value"])),
                ("in", "c_field", ["c_value"]),
            )
            >>> extract_field_names_from_filters(parse_tree_deep)
            ["c_field", "a_field", "b_field"]

        :raises ValueError: When a cycle is detected.
        :return: All field names needed for the filter
        :rtype: List[str]
        """
        field_names = []
        nodes_to_see = [parse_tree]
        nodes_seen = []
        log.info("Started extract_field_names_from_filters")
        while len(nodes_to_see) > 0:
            if nodes_to_see[0] in nodes_seen:
                raise ValueError("Cycle detected! Method can only handle DAGs")
            else:
                nodes_seen.append(nodes_to_see[0])

            head, *tail = nodes_to_see.pop(0)
            if head in ("and", "or", "not"):
                nodes_to_see.extend(tail)
            else:
                field_names.append(tail[0])
        log.info(f"Found {len(field_names)} field names needed for filter.")
        return field_names


def read_orders_by_date(
    record_handler: SlimRecordHandler,
    date: str,
    date_column: str,
    columns_to_fetch: List[str],
    filters: dict,
    excluded_order_ids: Optional[List],
    index: str,
    group_filter: Optional[List] = None,
):
    """Get all orders for a given datetime.

    :param index:
    :param date:
    :param watch_record:
    :param date_column:
    :param columns_to_fetch:
    :param asset_class:
    :param excluded_order_ids:
    :return:
    """
    query: Dict = {
        "query": {
            "bool": {
                "must_not": [{"exists": {"field": record_handler.meta.expiry}}],
                "filter": [{"term": {date_column: date}}],
            }
        },
        "_source": columns_to_fetch,
        "sort": [
            {record_handler.meta.timestamp: {"order": "desc"}},
            {record_handler.meta.key: {"order": "asc"}},
        ],
    }

    if filters and filters.get("bool", {}).get("filter", None):
        query["query"]["bool"]["filter"].extend(filters["bool"]["filter"])

    if filters and filters.get("bool", {}).get("must_not", None):
        query["query"]["bool"]["must_not"].extend(filters["bool"]["must_not"])

    if group_filter:
        try:
            query["query"]["bool"]["filter"].extend(group_filter)
        except Exception:
            query["query"]["bool"]["filter"] = group_filter

    if excluded_order_ids:
        order_ids_filter = {"terms": {OrderFields.ORD_IDENT_ID_CODE: excluded_order_ids}}
        query["query"]["bool"]["must_not"].append(order_ids_filter)

    logging.info(f"Running query {query}")

    results = search_after_query(
        query_dict=query,
        index=index,
        es_client=record_handler.es_repo,
        query_total_hits=5000,
        request_timeout=600,
    )

    results_df: pd.DataFrame = pd.json_normalize(results)
    del results

    for col in columns_to_fetch:
        if col not in results_df.columns:
            results_df[col] = pd.NA

    return results_df[columns_to_fetch].astype(np.dtype(object))


def read_orders_by_query(
    query: Dict,
    index: str,
    record_handler: SlimRecordHandler,
):
    """
    :param query:
    :param index:
    :return:
    """
    results: List[dict] = search_after_query(
        query_dict=query,
        index=index,
        es_client=record_handler.es_repo,
        query_total_hits=5000,
        request_timeout=600,
    )

    return pd.json_normalize(results)


def parse_date_readers(data_list: list[pd.DataFrame]) -> pd.DataFrame:
    data: pd.DataFrame = pd.concat(data_list, axis=0) if data_list else pd.DataFrame()

    if data.empty:
        return data

    if OrderFields.TS_ORD_SUBMITTED in data:
        timestamp_submitted: pd.Series[str] = data[OrderFields.TS_ORD_SUBMITTED].apply(
            lambda x: x if pd.isnull(x) else x.strip("Z")
        )
        data[OrderFields.TS_ORD_SUBMITTED] = pd.to_datetime(timestamp_submitted, errors="coerce")

    if OrderFields.TS_TRADING_DATE_TIME in data:
        timestamp_trading_date_time: pd.Series[str] = data[OrderFields.TS_TRADING_DATE_TIME].apply(
            lambda x: x if pd.isnull(x) else x.strip("Z")
        )
        data[OrderFields.TS_TRADING_DATE_TIME] = pd.to_datetime(
            timestamp_trading_date_time, errors="coerce"
        )

    return data
