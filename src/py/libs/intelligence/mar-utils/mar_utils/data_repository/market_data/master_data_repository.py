import backoff
import httpx
import itertools
import logging
import pandas as pd
import time
from mar_utils.exceptions import FailOnUnexpectedError
from mar_utils.static.ric import RICColumns
from master_data_api_client.api.rics import Rics as MasterDataRics
from master_data_api_client.api_client.auth_client import AuthClient as MasterDataAuthClient
from master_data_api_client.api_client.client import ApiClient as MasterDataApiClient
from typing import Any

logger = logging.getLogger(__name__)


class MasterDataRepository:
    __slots__ = (
        "master_data_api_host",
        "cognito_client_id",
        "cognito_client_secret",
        "cognito_auth_url",
        "master_data_storage_bucket",
        "trade_surveillance_directory",
        "ric_root_direcotry",
    )

    def __init__(
        self,
        master_data_api_host: str,
        cognito_client_id: str,
        cognito_client_secret: str,
        cognito_auth_url: str,
        master_data_storage_bucket: str,
    ) -> None:
        self.master_data_api_host = master_data_api_host
        self.cognito_client_id = cognito_client_id
        self.cognito_client_secret = cognito_client_secret
        self.cognito_auth_url = cognito_auth_url
        self.master_data_storage_bucket = master_data_storage_bucket

        self.trade_surveillance_directory: str = "lake/trade-surveilllance/datasources/"
        self.ric_root_direcotry: str = "lake/ingress/ric/curated/{RIC}/"

    @property
    def master_data_api_client(self) -> MasterDataApiClient:
        """Returns master-data-api-client."""
        return self.__master_data_api_client()

    @property
    def master_data_rics_client(self) -> MasterDataRics:
        """Returns master-data-api Ric api client."""
        return MasterDataRics(self.master_data_api_client)

    def __master_data_api_client(self) -> MasterDataApiClient:
        """Returns the default master-data-api API client which can be used in
        the other APIs of it. For example:

        If you want to use `Selerity` endpoints of master-data-api you can simply
        do:

        from master_data_api_client.api.selerity import Selerity
        selerity_client = Selerity(self.__master_data_api_client())
        """
        return MasterDataApiClient(
            host=self.master_data_api_host,
            auth_client=MasterDataAuthClient(
                client_id=self.cognito_client_id,
                client_secret=self.cognito_client_secret,
                oauth2_url=self.cognito_auth_url,
            ),
        )

    def get_ric_map(
        self,
        unique_instrument_ids: list[str] | None = None,
        instrument_combinations: list[list] | None = None,
        use_prefix_query: bool = False,
    ) -> pd.DataFrame:
        """Fetches the RIC details for unique Instrument Unique Identifiers
        from the master-data-api and returns the 1:1 row mapping of Identifier
        and Ric.

        :param unique_instrument_ids: List of unique instrument identifiers
        :param instrument_combinations: Nested list of instrument identifiers
        :param use_prefix_query: Param to be passed to master-data-api to use prefix or not
        :return: Dataframe with 1:1 mapping of Instrument and Ric
        """
        if not unique_instrument_ids:
            if not instrument_combinations:
                raise FailOnUnexpectedError(
                    "No unique instrument ids or combinations provided to fetch ric."
                )
            else:
                unique_instrument_ids = list(itertools.chain(*instrument_combinations))

        instrument_unique_ids = set(unique_instrument_ids)
        logger.info(f"Starting RICs fetch for {len(instrument_unique_ids)} unique instruments.")
        start = time.perf_counter()

        rics_response = self.fetch_rics_by_instrument_identifiers(
            instrument_unique_ids=list(instrument_unique_ids), use_prefix_query=use_prefix_query
        )

        if not rics_response:
            logger.warning(f"No RICs found for the Instruments: {instrument_unique_ids}")
            return pd.DataFrame()

        instrument_and_ric_df = pd.DataFrame(
            {RICColumns.InstrumentUniqueIdentifier: list(set(instrument_unique_ids))}
        ).merge(
            how="left", right=pd.DataFrame(rics_response), on=RICColumns.InstrumentUniqueIdentifier
        )

        preferred_ric_not_null = instrument_and_ric_df[RICColumns.PreferredRIC].notnull()

        identifiers_ric_not_found = (
            instrument_and_ric_df.loc[
                ~preferred_ric_not_null, RICColumns.InstrumentUniqueIdentifier
            ]
            .unique()
            .tolist()
        )
        if identifiers_ric_not_found:
            logger.warning(
                f"Failied to find RIC(s) for these identifier(s): {identifiers_ric_not_found}"
            )

        logger.info(f"Bulk fetched RICs in {time.perf_counter() - start:.2f} seconds...")
        return instrument_and_ric_df.loc[
            preferred_ric_not_null,
            [RICColumns.InstrumentUniqueIdentifier, RICColumns.PreferredRIC],
        ]

    @backoff.on_exception(
        backoff.expo,
        exception=httpx.RequestError,
        max_tries=3,
    )
    def fetch_rics_by_instrument_identifiers(
        self, instrument_unique_ids: list[str], use_prefix_query: bool = True
    ) -> dict[str, Any] | None:
        """Calls master-data-api bulk rics endpoint to fetch the rics for
        unique instrument identifiers.

        Note: It uses prefix query by default which is used to fetch Rics for
        ISINs too. set it to false if not needed.

        :param instrument_unique_ids: Unique instrument identifiers
        :param use_prefix_query: Boolean value to indicate if to fetch rics for ISINs or not
        """
        try:
            response: dict[str, Any] = (
                self.master_data_rics_client.post_bulk_rics_by_instrument_ids(
                    instrument_ids=instrument_unique_ids,
                    use_prefix_query=use_prefix_query,
                ).content["hits"]
            )
            return response
        except Exception as e:  # pragma: no cover
            logger.warning(f"Failed to fetch the rics with reason: {e}")
            return None

    @backoff.on_exception(
        backoff.expo,
        exception=Exception,
        max_tries=3,
    )
    def get_trade_surveillance_data(
        self,
        file_name: str,
        separator: str = ",",
        is_parquet: bool = False,
    ) -> pd.DataFrame:
        """Fetches the TradeSurveillanceData data from the master-data
        bucket."""
        file_uri = (
            f"{self.master_data_storage_bucket}/{self.trade_surveillance_directory}{file_name}"
        )
        logger.info(f"Fetching Trade Surveillance data from: {file_uri}")

        try:
            if is_parquet:
                return pd.read_parquet(file_uri)
            return pd.read_csv(filepath_or_buffer=file_uri, sep=separator)
        except Exception as e:  # pragma: no cover
            logger.warning(f"Failed to fetch Trade Surveillance data, reason: {e}")
            return pd.DataFrame()

    @backoff.on_exception(
        backoff.expo,
        exception=Exception,
        max_tries=3,
    )
    def fetch_ric_eod_data(self, ric: str) -> pd.DataFrame:
        try:
            ric_file_uri = (
                f"{self.master_data_storage_bucket}/{self.ric_root_direcotry.format(RIC=ric)}"
                f"{ric}__EOD_ROLLING_STATS.parquet"
            )
            logger.info(f"Fetching RIC data from {ric_file_uri}")
            return pd.read_parquet(ric_file_uri)
        except Exception as e:  # pragma: no cover
            logger.warning(f"Failed to fetch the RIC {ric} EOD data. Reason: {e}")
            return pd.DataFrame()
