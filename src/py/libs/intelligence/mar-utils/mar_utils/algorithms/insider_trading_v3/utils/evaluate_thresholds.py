import pandas as pd
import scipy.stats as st  # type: ignore
from mar_utils.algorithms.insider_trading_v3.static import EventDirection
from mar_utils.data_repository.type import RangeResult


def check_confidence_interval(
    price_variations: pd.Series,
    close_price_variation: float,
    price_variation_mkt_data: float,
) -> RangeResult | None:
    """Evaluate confidence interval.

    :param price_variations:
    :param close_price_variation:
    :param price_variation_mkt_data:
    :return:
    """
    price_variations_mean = price_variations.mean()
    confidence_interval: tuple[float, float] = st.t.interval(
        alpha=price_variation_mkt_data,
        df=len(price_variations) - 1,
        loc=price_variations_mean,
        scale=st.sem(price_variations),
    )

    if confidence_interval[0] <= close_price_variation <= confidence_interval[1]:
        return None

    return RangeResult(start=confidence_interval[0], end=confidence_interval[1])


def check_direction_condition(
    direction: str, price_variation: float, interval: RangeResult
) -> bool:
    """Checks if direction and the close_price_variation are in the same
    orientation.

    :param direction: Value of the direction (up or down)
    :param price_variation: Value of the close price variation
    :param interval: Containing the values for the confidence interval
    :return: bool returns false if not in the same orientation
    """
    return (
        direction != EventDirection.UP or price_variation > interval.end or interval.end <= 0
    ) and (
        direction != EventDirection.DOWN or price_variation < interval.start or interval.start >= 0
    )
