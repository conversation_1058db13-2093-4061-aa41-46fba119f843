import logging
import pandas as pd
from mar_utils.algorithms.common.timewindow import Disorderly<PERSON>indow, TimeWindow
from mar_utils.static.order_parquet_schema_fields import OrderParquetSchemaFields
from typing import Any, Union

log = logging.getLogger(__name__)


def get_instrument_id_code(data: pd.DataFrame) -> Union[pd.Series, None]:
    """Get the instrument id code  for a group of orders.

    :param data: pd.Dataframe, data to get the orders' id
    :return: pd.Series with the list of unique ids
    """

    if OrderParquetSchemaFields.InstrumentDetailsInstrumentIdCode in data.columns:
        instrument_id_code: pd.Series = (
            data.loc[:, OrderParquetSchemaFields.InstrumentDetailsInstrumentIdCode]
            .unique()
            .tolist()
        )

    else:
        log.info(
            f"{OrderParquetSchemaFields.InstrumentDetailsInstrumentIdCode} columns not in data. "
            f"Fallback to {OrderParquetSchemaFields.InstrumentDetailsExtAlternativeIdentifier}."
        )

        data["__instrument_code__"] = pd.NA

        instrument_id_columns = [
            OrderParquetSchemaFields.InstrumentDetailsInstrumentIdCode,
            OrderParquetSchemaFields.InstrumentDetailsExtAlternativeIdentifier,
            OrderParquetSchemaFields.InstrumentDetailsExtUniqueIdentifier,
        ]
        for col in instrument_id_columns:
            if col in data.columns:
                data["__instrument_code__"] = data["__instrument_code__"].fillna(data[col])

        instrument_id_code: pd.Series = data.loc[:, "__instrument_code__"].unique().tolist()  # type: ignore[no-redef]

    return instrument_id_code


def get_unique_value_for_alert(data: pd.DataFrame, column_name: str) -> Any:
    """Get the unique value in a column value list and return it in a list.

    :param: data pd.DataFrame with alert data
    :param: column_name, column name to search the data for
    :return: List w/ an unique value
    """
    return data.loc[:, column_name].unique().tolist() if column_name in data.columns else pd.NA


def set_time_thresholds(time_field: Union[DisorderlyWindow, TimeWindow]) -> pd.Timedelta:
    """This method converts time window thresholds to pd.Timedelta."""

    time_threshold_timedelta = pd.Timedelta(
        value=time_field.value,
        unit=time_field.unit.value,
    )

    return time_threshold_timedelta


def sort_by_date(data: pd.DataFrame, date_column: str) -> pd.DataFrame:
    """Sort data by the date column (OrderField.TS_ORD_SUBMITTED or
    OrderField.TS_ORD_UPDATED)

    :param data: pd.Dataframe, data to be sorted
    :param date_column: str, name of time column to sort the data
    :return: pd.DataFrame, sorted data
    """
    log.debug(f"Sort date by {date_column}")
    if date_column not in data.columns:
        log.error(f"Column {date_column} not present in the data columns.")
        raise ValueError(f"Column {date_column} not in data to analyse.")

    sort_data: pd.DataFrame = data.sort_values(by=[date_column], ascending=True)
    return sort_data
