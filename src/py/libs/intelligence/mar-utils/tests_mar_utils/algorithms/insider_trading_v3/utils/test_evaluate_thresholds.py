import pandas as pd
from mar_utils.algorithms.insider_trading_v3.utils.evaluate_thresholds import (
    check_confidence_interval,
)
from mar_utils.data_repository.type import RangeResult


class TestEvaluateThresholds:
    def test_check_confidence_interval(self) -> None:
        result = check_confidence_interval(
            price_variations=pd.Series([1, 2, 10, 21, 20]),
            close_price_variation=8.1,
            price_variation_mkt_data=0.1,
        )
        assert result == RangeResult(start=10.2300020278063, end=11.369997972193701)
