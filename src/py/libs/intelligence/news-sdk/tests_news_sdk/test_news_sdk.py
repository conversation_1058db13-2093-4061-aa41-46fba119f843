from datetime import datetime as dt


def test_rdp_token(mock_httpx):
    from news_sdk.rdp_token import RDPToken

    creds = {
        "rdp": {
            "username": "this is a username",
            "password": "this is a password",
            "scope": "trapi",
            "client_id": "this is a client id",
        }
    }

    t = RDPToken(credentials=creds)
    assert len(mock_httpx.mock_calls) == 7

    assert [list(x) for x in mock_httpx.mock_calls] == [
        [
            "post",
            ("https://api.refinitiv.com/auth/oauth2/v1/token",),
            {
                "headers": {"Accept": "application/json"},
                "data": {
                    "username": "this is a username",
                    "password": "this is a password",
                    "grant_type": "password",
                    "scope": "trapi",
                    "takeExclusiveSignOnControl": "true",
                },
                "auth": ("this is a client id", ""),
                "timeout": 30,
            },
        ],
        ["raise_for_status", (), {}],
        ["json", (), {}],
        ["json().__getitem__", ("expires_in",), {}],
        ["json().__getitem__().__int__", (), {}],
        ["json().__getitem__", ("refresh_token",), {}],
        ["json().__getitem__", ("access_token",), {}],
    ]
    mock_httpx.mock_calls
    mock_httpx.reset_mock()

    t.refresh_token()

    refresh_calls = [list(x) for x in mock_httpx.mock_calls]
    first_called = refresh_calls.pop(0)
    assert refresh_calls == [
        ["raise_for_status", (), {}],
        ["json", (), {}],
        ["json().__getitem__", ("access_token",), {}],
        ["json().__getitem__", ("refresh_token",), {}],
        ["json().__getitem__", ("expires_in",), {}],
        ["json().__getitem__().__int__", (), {}],
    ]
    assert first_called[2]["data"]["refresh_token"] == t.token


def test_nmf(mock_httpx, mock_boto, mock_rdp_token, mock_boto_session):
    from news_sdk.news_messages_filtered import NewsSubscription

    ns = NewsSubscription(None)

    sub = ns.subscribe_to_news(headlines=False)
    assert set(sub.keys()) == {"endpoint", "crypto_key", "subscription_id"}
    cloud_creds = ns.get_cloud_credentials()
    assert set(cloud_creds.keys()) == {"access_id", "session_token", "secret_key"}

    activate_sub = ns.start_news_subscription(headlines=True)
    assert set(activate_sub.keys()) == {"endpoint", "crypto_key", "subscription_id"}
    ns.remove_message("This is the message id", retry=False)

    batch_consumption = ns.batch_consume_messages(batch_size=10, retry=False)
    assert len(batch_consumption) == 1
    assert batch_consumption[0] == mock_boto_session.receive_message.return_value["Messages"][0]

    ns.get_attribs()


def test_sqs_consumer(mock_httpx, mock_boto, mock_boto_session):
    from news_sdk.consume_sqs_events import SQSNewsConsumer

    con = SQSNewsConsumer(
        access_id="irrelevant", secret_key="irrelevant", session_token="irrelevant"
    )

    attribs = con.get_attributes(
        endpoint="irrelevant",
    )
    assert attribs

    rb = con.retrieve_batch(
        endpoint="irrelevant",
        batch_size=1,
    )
    assert rb == mock_boto_session.receive_message.return_value["Messages"]
    con.remove_single(
        receipt_handle="irrelevant",
        endpoint="irrelevant",
    )


def test_client(mock_boto, mock_httpx, mock_rdp_token):
    from news_sdk.client import NewsDataClient

    client = NewsDataClient()

    client.get_headlines(
        rics=["META.OQ"],
        date_from=dt(2023, 1, 1),
        date_to=dt(2023, 1, 2),
    )

    client._get_next_page_headlines(cursor="irrelevant")

    client.get_stories(story_ids=[{"storyId": "this is a story id"}])

    client.get_story(story_id="this is a story id for a single story")
