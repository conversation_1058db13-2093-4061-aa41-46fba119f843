import json
from flang import QueryGenerator, build_query
from se_elastic_schema.models import Order, SurveillanceWatch
from se_elastic_schema.static.surveillance import WatchType
from se_es_utils.slim_record_handler import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>


def base_aggregated_groups(
    watch_record: SurveillanceWatch,
    from_date,
    to_date,
    record_handler: <PERSON><PERSON><PERSON>ord<PERSON><PERSON><PERSON>,
) -> dict:
    """Elastic search base query with the watch information.

    :param watch_record: surveillance watch record
    :param from_date: daterange with the date from
    :param to_date: daterange with the date to
    :param record_handler: <PERSON><PERSON><PERSON>ord<PERSON><PERSON><PERSON> instance
    :return: dict with the base query
    """
    query = {
        "size": 0,
        "query": {
            "bool": {
                "filter": [
                    {"bool": {"must_not": [{"exists": {"field": record_handler.meta.expiry}}]}}
                ]
            }
        },
    }
    # Scheduled watch might have type SCHEDULED or be NULL
    is_scheduled_watch = True
    if watch_record.type and watch_record.type != WatchType.SCHEDULED:
        is_scheduled_watch = False

    if watch_record.query.filters:
        # ToDO: this is a hack to reduce major change in query building
        # Basically, for empty queries, we're adding a json with empty bool
        # in get_watch_filters. The below to avoid breaking the current code
        if not watch_record.query.filters == json.dumps(dict(bool=dict(filter=list()))):
            watch_filters = get_watch_filters(
                watch_filters=watch_record.query.filters,
                exclude_date_filters=is_scheduled_watch,
            )
            query["query"]["bool"]["filter"].append(watch_filters)  # type: ignore[index]

    if is_scheduled_watch and from_date:
        query_range = {
            "range": {
                record_handler.meta.timestamp: {
                    "gte": from_date,
                    "lte": to_date,
                }
            }
        }
        query["query"]["bool"]["filter"][0]["bool"]["filter"] = [query_range]  # type: ignore[index]

    return query


def get_watch_filters(watch_filters: str, exclude_date_filters: bool = False):
    """Get the watch filters to add to query.

    :param watch_filters: str
    :param exclude_date_filters: bool
    :return:
    """
    paths_to_nested_fields = [
        "parties.buyer",
        "parties.client",
        "parties.seller",
        "parties.trader",
    ]

    filters = build_query(
        expr_str=watch_filters,
        gen=QueryGenerator(nested_paths=paths_to_nested_fields),
    ).to_dict()

    return filters


def cardinality_agg_query_per_field(field: str) -> dict:
    """Creates the agg dict for the cardinaly count.

    :param field: field, str with the field
    :retuns: dict
    """
    return {"instrumentUniqueIdentifier_count": {"cardinality": {"field": field}}}


def generate_agg_query(eval_agg: dict, num_partitions: int, partition: int) -> dict:
    """Creates the agg dict for the grouping.

    :param eval_agg: gg dict based on the evaluation_type_column
    :param num_partitions: int with the number of partitions for the agg
    :param partition: int with the partitions to fetch for the agg
    :retuns: dict
    """
    agg_dict = {
        "instrumentUniqueIdentifier_agg": {
            "terms": {
                "field": "instrumentDetails.instrument.ext.instrumentUniqueIdentifier",
                "size": 5000,
                "include": {"partition": partition, "num_partitions": num_partitions},
            },
            "aggs": eval_agg,
        }
    }

    return agg_dict


def get_aggregated_groups(
    base_query: dict,
    evaluation_type_column: str,
    tenant: str,
    record_handler: SlimRecordHandler,
) -> list[dict]:
    """Creates the Elastic search agg based on the
    instrumentDetails.instrument.ext.instrumentUniqueIdentifier and in the
    evaluation_type_column. And fetchs the groups.

    :param base_query: base query, dict
    :param evaluation_type_column: str with the evaluation type field
    :param tenant: str with tenant
    :param record_handler: SlimRecordHandler instance
    :return: List with a dict of the groups {INSTR: [EVAL1;EVAL2]}
    """
    cardinal_query = base_query
    cardinal_query["aggs"] = cardinality_agg_query_per_field(
        field="instrumentDetails.instrument.ext.instrumentUniqueIdentifier"
    )

    cardinal_res = record_handler.search_records_by_query(
        query=cardinal_query, tenant=tenant, model=Order
    )

    total_count = cardinal_res["aggregations"]["instrumentUniqueIdentifier_count"]["value"]

    # NOTE: always provide an additional partition because cardinal_query gets approx
    # results. Therefore, an additional partition to insure a full fetch of the groups

    num_partition = total_count // 5000 + 2 if total_count % 5000 > 0 else 1

    agg_groups_list: list = []

    if total_count == 0:
        return agg_groups_list

    agg_query = base_query
    eval_agg_dict = {
        "evaluationType_agg": {"terms": {"field": evaluation_type_column, "size": 10000}}
    }

    for partition in range(num_partition):
        agg_query["aggs"] = generate_agg_query(
            eval_agg=eval_agg_dict, partition=partition, num_partitions=num_partition
        )
        result = record_handler.search_records_by_query(query=agg_query, tenant=tenant, model=Order)
        agg_groups_list.extend(result["aggregations"]["instrumentUniqueIdentifier_agg"]["buckets"])

    return agg_groups_list
