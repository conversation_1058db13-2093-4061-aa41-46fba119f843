from front_running_v2_apply_strategy.static import EvaluationType, Flow
from market_abuse_algorithms.strategy.base.models import CommonThresholds, TimeUnit
from pydantic import Field, validator
from pydantic.class_validators import root_validator
from pydantic.dataclasses import dataclass
from typing import Dict, Optional, Union


@dataclass
class TimeWindow:
    """Class with validations regarding the TimeWindow threshold."""

    unit: TimeUnit
    value: int = Field(..., ge=0, le=216000)

    @validator("unit")
    def check_unit(cls, v: TimeUnit) -> TimeUnit:
        """Validation for the unit of the time window.

        :param v: unit parameter
        :return:
        """
        valid_units = [TimeUnit.SECONDS, TimeUnit.MINUTES, TimeUnit.HOURS]
        if v not in valid_units:
            raise ValueError(f"`{v}` not valid unit for this strategy: {valid_units}")

        return v

    @validator("value")
    def check_value(cls, v: int, values: Dict[str, Union[int, TimeUnit]]) -> int:
        """Validates that the timedelta is between the lower and upper bounds.

        :param v: amount of time units
        :param values: dictionary with time window
        :return:
        """
        unit = values.get("unit")

        upper_bound_seconds = 216000

        upper_bounds = {
            TimeUnit.SECONDS: upper_bound_seconds,
            TimeUnit.MINUTES: upper_bound_seconds / 60,
            TimeUnit.HOURS: upper_bound_seconds / 3600,
        }

        upper_bound = upper_bounds.get(unit)  # type: ignore[arg-type]

        if v < 0 or v > upper_bound:  # type: ignore[operator]
            raise ValueError(f"Failed check 0 < {v} (value) < {upper_bound}. Unit: {unit}")

        return v


@dataclass
class TimeWindowFrontrunningExecution:
    """Class with validations regarding the TimeWindow threshold."""

    unit: TimeUnit
    value: int = Field(..., ge=0, le=216000)

    @validator("unit")
    def check_unit(cls, v: TimeUnit) -> TimeUnit:
        """Validation for the unit of the time window.

        :param v: unit parameter
        :return:
        """
        valid_units = [TimeUnit.SECONDS, TimeUnit.MINUTES, TimeUnit.HOURS]
        if v not in valid_units:
            raise ValueError(f"`{v}` not valid unit for this strategy: {valid_units}")

        return v

    @validator("value")
    def check_value(cls, v: int, values: Dict[str, Union[int, TimeUnit]]) -> int:
        """Validates that the timedelta is between the lower and upper bounds.

        :param v: amount of time units
        :param values: dictionary with time window
        :return:
        """
        unit = values.get("unit")

        upper_bound_seconds = 216000

        upper_bounds = {
            TimeUnit.SECONDS: upper_bound_seconds,
            TimeUnit.MINUTES: upper_bound_seconds / 60,
            TimeUnit.HOURS: upper_bound_seconds / 3600,
        }

        upper_bound = upper_bounds.get(unit)  # type: ignore[arg-type]

        if v < 0 or v > upper_bound:  # type: ignore[operator]
            raise ValueError(f"Failed check 0 < {v} (value) < {upper_bound}. Unit: {unit}")

        return v


class Thresholds(CommonThresholds):
    adv: Optional[float] = Field(
        None,
        ge=0,
        le=10,
        description="Determines the size of the order/s that are being frontrun",
    )
    evaluationType: Optional[EvaluationType] = Field(
        None,
        description="Allows you to specify that either the Desk, Trader, or the Portfolio manager are the same on "  # noqa: E501
        "both the Prop and Client orders",
    )
    flow: Flow = Field(
        ..., description="Allows you to select the nature of the check you wish to run"
    )
    frontrunOrderVolume: Optional[int] = Field(
        None,
        ge=0,
        le=10000000,
        description="Specifies the minimum size that the detected frontrun order has to be",
    )
    priceImprovement: bool = Field(
        False,
        description="Flag which indicates if price comparison is made between the volume weighted execution price "  # noqa: E501
        "of the frontrunning order vs the volume weighted execution price of the frontrun order/s",
    )
    timeWindow: TimeWindow = Field(
        ...,
        description="Time difference of the frontrunning order, and the Frontrun order",
    )
    timeWindowFrontrunningExecution: Optional[TimeWindowFrontrunningExecution] = Field(
        None,
        description="Time window added to the order submitted time of the frontrun order",
    )
    volumeDifference: float = Field(
        ...,
        ge=0,
        le=1,
        description="Specifies the difference between the frontrunning order, and the detected frontrun order/s",  # noqa: E501
    )
    crossProductActivity: bool = Field(
        default=False, description="Enables or disables capturing of cross product activities"
    )

    @root_validator(pre=False)
    def check_presence_of_srtw_if_dsa(cls, values):
        if values.get("evaluationType") and (
            values.get("flow") is Flow.PAD_VS_NON_PAD or values.get("flow") is Flow.DESK_VS_DESK
        ):
            raise ValueError(
                "`evaluationType` cannot defined when `flow` is Pad vs. anything or Desk vs. Desk"
            )

        return values
