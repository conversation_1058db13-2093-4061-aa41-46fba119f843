# mypy: disable-error-code="attr-defined, misc"
import pandas as pd
from market_abuse_algorithms.strategy.base.scenario import Abstract<PERSON><PERSON>nar<PERSON>, TradeColumns
from market_abuse_algorithms.strategy.base.static import ScenarioFields
from painting_the_tape_v2_apply_strategy.static import AlgoColumnsEnum


class Scenario(AbstractScenario):
    def _trade_columns(self) -> TradeColumns:
        return TradeColumns(single=[], multiple=["orders", "executions"])

    def _build_scenario(self) -> pd.Series:
        records = {
            "orders": self._result.pop(AlgoColumnsEnum.ORDERS.value),
            "executions": self._result.pop(AlgoColumnsEnum.ORDERS_STATE_KEYS.value),
        }

        alert = dict(
            thresholds=self._thresholds,
            records=records,
            additionalFields={ScenarioFields.TOP_LEVEL: self._result},
        )

        scenario: pd.Series = pd.Series(alert)

        return scenario
