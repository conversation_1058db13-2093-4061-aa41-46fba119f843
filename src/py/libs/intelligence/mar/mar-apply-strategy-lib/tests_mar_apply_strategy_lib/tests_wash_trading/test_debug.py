import pytest
from pathlib import Path
from unittest.mock import MagicMock
from wash_trading_apply_strategy.apply_strategy import ApplyStrategy

TEST_DATA = Path(__file__).parent.joinpath("test_data")


@pytest.mark.usefixtures("skip_test_in_ci")
class TestDebuggerWashTrading:
    def test_case_debug(self, helpers, mock_apply_strategy_kwargs):
        thresholds = {
            "excludeMatchingTimestamps": False,
            "maxPriceDifference": 50,
            "maxTimeWindow": 30,
            "maxVolumeDifference": 0.005,
            "numberOfCounterparties": "single",
        }

        filters = {}

        mock_apply_strategy_kwargs["context"] = helpers.get_context(
            thresholds=thresholds, filters=filters
        )
        strategy = ApplyStrategy(**mock_apply_strategy_kwargs)

        # Explicit mock to s3 upload
        strategy.auditor.write_audit_to_cloud = MagicMock()
        strategy.auditor.write_alerts_and_scenarios_to_cloud = MagicMock()
        strategy.write_alerts_and_scenarios_to_cloud = MagicMock()

        strategy.run()

        strategy.total_scenarios
