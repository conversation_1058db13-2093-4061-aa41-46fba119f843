import os
from aries_io_event.io_param import IOParamFieldSet
from aries_task_link.models import AriesTaskResult
from se_db_utils.database import Database
from se_es_utils.slim_record_handler import SlimRecordHandler

DATABASE_URL = "*************************************/restricted_list"
TENANT = "mar"

os.environ["TENANT_DB_PG_URL"] = "*************************************/tenant_db"


def test_apply_strategy_no_orders_for_restrictions(
    fake_es_client_8,
    mock_surveillance_watch,
    mock_surveillance_watch_execution,
    mock_restricted_list_with_restrictions,
    mock_restrictions,
    sample_aries_input,
    monkeypatch,
    mocker,
):
    pg_database = Database(db_url=DATABASE_URL, echo=False)

    with pg_database.session(tenant_schema=TENANT) as db_session:

        def watch(args, *kwargs):
            return mock_surveillance_watch

        def watch_execution(args, *kwargs):
            return mock_surveillance_watch_execution

        from surveillance_utils import watch_execution_handling, watch_handling

        # monkeypatch.setattr(elasticsearch6, "ElasticsearchRepository", fake_es_client_6)

        monkeypatch.setattr(SlimRecordHandler, "get_es_repo", lambda *a, **k: fake_es_client_8())
        monkeypatch.setattr(watch_handling, "fetch_watch_record", watch)

        from restricted_list_v2_apply_strategy.apply_strategy_task import (  # type: ignore
            RestrictedListV2Task,
        )

        monkeypatch.setattr(watch_execution_handling, "create_watch_execution", watch_execution)

        mocker.patch.object(
            RestrictedListV2Task,
            "fetch_restricted_list_watch_record",
            return_value=mock_restricted_list_with_restrictions,
        )

        mocker.patch.object(
            RestrictedListV2Task, "fetch_watch_restrictions", return_value=mock_restrictions
        )
        mocker.patch.object(
            RestrictedListV2Task, "get_orders_for_single_instrument", return_value={}
        )

        rl_v2_task = RestrictedListV2Task(aries_task_input=sample_aries_input)

        restricted_list_alerts = rl_v2_task._apply_strategy(db_session=db_session)
        try:
            if (
                restricted_list_alerts.empty
                or not restricted_list_alerts
                or restricted_list_alerts is None
            ):
                result = AriesTaskResult(output_param=IOParamFieldSet(params={}))
            else:
                alerts = {
                    key: [i[key] for i in restricted_list_alerts]
                    for key in restricted_list_alerts[0]
                }

                result = AriesTaskResult(output_param=IOParamFieldSet(params=alerts))

        except Exception as e:
            result = e  # type: ignore[assignment]

        assert result.output_param is not None
        assert result.output_param.params == {}


def test_apply_strategy_no_restrictions(
    fake_es_client_8,
    mock_surveillance_watch,
    mock_surveillance_watch_execution,
    mock_restricted_list_with_restrictions,
    sample_aries_input,
    monkeypatch,
    mocker,
):
    pg_database = Database(db_url=DATABASE_URL, echo=False)

    with pg_database.session(tenant_schema=TENANT) as db_session:

        def watch(args, *kwargs):
            return mock_surveillance_watch

        def watch_execution(args, *kwargs):
            return mock_surveillance_watch_execution

        from surveillance_utils import watch_execution_handling, watch_handling

        monkeypatch.setattr(SlimRecordHandler, "get_es_repo", lambda *a, **k: fake_es_client_8())
        monkeypatch.setattr(watch_handling, "fetch_watch_record", watch)

        from restricted_list_v2_apply_strategy.apply_strategy_task import (  # type: ignore
            RestrictedListV2Task,
        )

        monkeypatch.setattr(watch_execution_handling, "create_watch_execution", watch_execution)

        mocker.patch.object(
            RestrictedListV2Task,
            "fetch_restricted_list_watch_record",
            return_value=mock_restricted_list_with_restrictions,
        )
        mocker.patch.object(RestrictedListV2Task, "fetch_watch_restrictions", return_value=[])

        rl_v2_task = RestrictedListV2Task(aries_task_input=sample_aries_input)

        restricted_list_alerts = rl_v2_task._apply_strategy(db_session=db_session)

        try:
            if (
                restricted_list_alerts.empty
                or not restricted_list_alerts
                or restricted_list_alerts is None
            ):
                result = AriesTaskResult(output_param=IOParamFieldSet(params={}))
            else:
                alerts = {
                    key: [i[key] for i in restricted_list_alerts]
                    for key in restricted_list_alerts[0]
                }

                result = AriesTaskResult(output_param=IOParamFieldSet(params=alerts))

        except Exception as e:
            result = e  # type: ignore[assignment]

        assert result.output_param is not None
        assert result.output_param.params == {}
