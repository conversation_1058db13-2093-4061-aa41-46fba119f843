from aries_io_event.io_param import IOParamFieldSet
from aries_io_event.task import TaskFieldSet
from aries_io_event.workflow import WorkflowFieldSet
from aries_task_link.models import AriesTaskInput


def sample_input() -> AriesTaskInput:
    workflow = WorkflowFieldSet(
        start_timestamp="2024-01-01T13:21:05.604697",
        name="restricted_list_v2_algo",
        stack="dev-shared-2",
        tenant="mares8",
    )
    input_param = IOParamFieldSet(
        params={
            "watch_id": "8f8b3048-0292-47d1-919d-d0bc7983b269",
            "watch_execution_id": "24068924-6e78-4649-b325-bbd3447b179c",
        }
    )
    task = TaskFieldSet(name="mar_apply_strategy", version="latest", success=False)
    return AriesTaskInput(workflow=workflow, input_param=input_param, task=task)
