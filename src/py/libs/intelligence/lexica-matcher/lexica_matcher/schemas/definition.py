"""Lexica definition schema.

source:
https://steeleye.atlassian.net/wiki/spaces/PRODUCT/pages/1677066301/cSurv+2.0+-+SteelEye+Lexica+Pre-processing#Source-to-target-model-mapping
"""

import enum
import logging
import pandas as pd
from pydantic import BaseModel, Field, root_validator, validator
from se_elastic_schema.static.comms.analytics import AnalyticsTermTypeEnum
from typing import List, Optional


class SearchType(str, enum.Enum):
    AND = "AND"
    OR = "OR"


class LexicaDefinition(BaseModel):
    behaviour: Optional[str] = Field(
        ...,
        description="this is the behavioural policy that a given term belongs to - policies are "
        "fully described",
    )
    behaviourId: Optional[str] = Field(
        ...,
        description="this is the id of the behavioural policy that a given term belongs to - "
        "policies are fully described",
    )
    subBehaviour: Optional[str] = Field(
        ..., description="this is the behavioural sub-policy that a given term belongs to"
    )
    subBehaviourId: Optional[str] = Field(
        ..., description="this is the id of the behavioural sub-policy that a given term belongs to"
    )
    termId: Optional[str] = Field(..., description="this is the id associated with the termBase")
    opFuzziness: int = Field(
        default=0, description="this is any degree of fuzziness applied to a given lexica search"
    )
    opProximity: int = Field(
        ...,
        description="the degree of proximity that a multiplicity of words in a termToSearchFor "
        "must be collocated within",
    )
    opStemming: bool = Field(
        default=False,
        description="value to denote whether stemming should be applied to a given lexica search",
    )
    termBase: Optional[str] = Field(
        ...,
        description="the base term of the termToSearchFor , not used for searching, just for "
        "storing (e.g., for “We Tried”, the base term would be “We”)",
    )
    termExclusionSearchType: Optional[SearchType] = Field(
        None, description="the kind of search relating to excluded terms, either an OR, or AND"
    )
    termExclusions: Optional[List[str]] = Field(
        None,
        description="this is a list of terms that, if collocated with termToSearchFor, are "
        "excluded as a ‘match’",
    )
    termExclusionTopics: Optional[List[str]] = Field(
        None,
        description="a list of topics, that if a given term is collocated with, "
        "are excluded as a ‘match’",
    )
    termLanguage: str = Field(..., description="Contains the language the termToSearchFor is in")
    termPaired: Optional[str] = Field(
        None,
        description="the paired term of the termToSearchFor , not used for searching, just for "
        "storing (e.g., for “We Tried”, the base term would be “Tried”)",
    )
    termToSearchFor: str = Field(
        ..., description="this is the actual lexica term being searched for"
    )
    termType: AnalyticsTermTypeEnum = Field(
        ...,
        description="Contains either “Phrase”, or “Collocated Term”, "
        "not used for searching, just for storing",
    )

    @validator("opProximity", pre=True)
    def validate_proximity(cls, v):
        """Validates the Proximity of a LexicaDefinition to try to suppress
        errors in the in the processing part of the LexicaMatcher.

        :param v: value of the Proximity
        :return: 10 if the Proximity value is bigger than 10; else returns its original value
        """
        limit = 10

        if v > limit:
            logging.warning(
                f"Value of the Proximity {v} is bigger than {limit}, changing it to {limit} to "
                "avoid processing issues"
            )
            v = limit

        return v

    @root_validator(pre=False)
    def exclusion_search_type_validation(cls, values):
        """Function to check if the field exclusion search type is correctly
        populated.

        :param values: Lexica exclusion values
        :return: Return the values or an Error if the items is not correctly populated
        """
        ex_terms_presence = (
            isinstance(values.get("termExclusions"), list) and len(values.get("termExclusions")) > 0
        )

        ex_topics_presence = (
            isinstance(values.get("termExclusionTopics"), list)
            and len(values.get("termExclusionTopics")) > 0
        )

        ex_stype_missing = pd.isna(values.get("termExclusionSearchType"))

        if (ex_terms_presence | ex_topics_presence) and ex_stype_missing:
            raise Exception(
                "Exclusion term, exclusion topic, exclusion search type: are not correctly defined"
            )

        return values
