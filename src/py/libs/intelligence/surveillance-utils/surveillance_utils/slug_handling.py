import random
from se_elastic_schema.elastic_schema.core.steeleye_schema_model import SteelEyeSchemaBaseModelES8
from se_elastic_schema.models.tenant.surveillance.surveillance_watch import SurveillanceWatch
from se_elasticsearch.repository.elasticsearch6 import ElasticsearchRepository
from se_es_utils import sequence_service
from slugify import slugify
from surveillance_utils.static import SEQUENCE_IDS, SEQUENCE_NAMES
from typing import List, Optional, Type


def add_slug_to_hits_and_validate(
    es_client: ElasticsearchRepository,
    list_alert_hits: List[dict],
    surveillance_watch: SurveillanceWatch,
    tenant: str,
    alert_model: Type[SteelEyeSchemaBaseModelES8],
    elastic_api_key: str,
    sequence_name_index: str = "alert",
) -> List[dict]:
    """Adds a slug to an alert hit and then validates the alert hit with.

    :param es_client:
    :param list_alert_hits: List of alert hits without a slug
    :param tenant: name of the tenant
    :param surveillance_watch: SurveillanceWatch model
    :param alert_model: Alert model to validate the alert hit with
    :param elastic_api_key: ELASTIC API KEY
    :param sequence_name_index: sequence index name, eg alert or scenario

    :return: List with validated Alerts for each alert_hit
    """

    sequence = sequence_service.SequenceService(
        es_client.client, tenant, elastic_api_key=elastic_api_key
    )
    sequence_name = (
        SEQUENCE_NAMES[surveillance_watch.queryType]
        if surveillance_watch.queryType in SEQUENCE_NAMES
        else surveillance_watch.queryType.value
    )

    sequence_doc_id = SEQUENCE_IDS.get(
        surveillance_watch.queryType.value, surveillance_watch.queryType.value
    )

    validated_alerts_models: List = []
    for alert_hit in list_alert_hits:
        alert_hit["slug"] = _make_slug(
            sequence_name,
            number=sequence.next(
                sequence_name=f"{tenant}-{sequence_doc_id}-{sequence_name_index}",
            ),
        )

        validated_alerts_models.append(alert_model.validate(alert_hit).to_dict(exclude_none=True))

    return validated_alerts_models


def _make_slug(text: str, number: Optional[int] = None) -> str:
    """Generates a slug from any text. If `number` is not provided, this will
    append a random number prefixed by `X` to the string.

    Examples::

        make_slug(text="I am a thing", 123)
        >> "i-am-a-thing-123"

        make_slug(text="Blah blah blah long long")
        >> "blah-blah-bl-X1234"

        make_slug("", number=123):
        >> "123"
    :param text: text to slugify
    :param number: number to add at the end of the text
    """
    num = f"x{random.randint(100, 999)}" if number is None else f"s{number}"
    if text:
        slug = slugify(text, max_length=12)
        return f"{slug}-{num}"
    return str(num)
