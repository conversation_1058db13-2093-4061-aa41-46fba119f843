# type: ignore
from se_elastic_schema.elastic_schema.core.steeleye_record_validation import (
    SteelEyeRecordValidationRule,
)
from se_elastic_schema.static.validations import (
    ModulesAffectedEnum,
    SeverityEnum,
    SteeleyeValidationCategoryEnum,
)
from se_sql_pydantic_models.models.restricted_list import Permanency, RestrictedList, Restriction
from se_sql_pydantic_models.steeleye_validations.error_codes import SteeleyeErrorCodesEnum
from tenant_db.schema.restricted_list import MinimumSizeType


class SteeleyeValidation900(SteelEyeRecordValidationRule):
    code = SteeleyeErrorCodesEnum.SE_DV_900
    category = SteeleyeValidationCategoryEnum.IDENTIFIERS
    modules_affected = [
        ModulesAffectedEnum.RESTRICTED_LIST,
    ]
    severity = SeverityEnum.HIGH

    @classmethod
    def run_record_validation(cls, record: RestrictedList, **kwargs):
        """
        if [restrictedList.uniqueId] is not populated
        then:
        RAISE ERROR!
        """
        if isinstance(record, RestrictedList):
            if record and not record.alternateId:
                raise cls.steeleye_validation_error(
                    message="Missing alternateId for `Restricted List`",
                    field_path="alternateId",
                )


class SteeleyeValidation901(SteelEyeRecordValidationRule):
    code = SteeleyeErrorCodesEnum.SE_DV_901
    category = SteeleyeValidationCategoryEnum.IDENTIFIERS
    modules_affected = [
        ModulesAffectedEnum.RESTRICTED_LIST,
    ]
    severity = SeverityEnum.HIGH

    @classmethod
    def run_record_validation(cls, record: RestrictedList, **kwargs):
        """
        if [restrictedList.name] is not populated
        then:
        RAISE ERROR!
        """
        if isinstance(record, RestrictedList):
            if record and not record.name:
                raise cls.steeleye_validation_error(
                    message="Missing name for `Restricted List`",
                    field_path="name",
                )


class SteeleyeValidation902(SteelEyeRecordValidationRule):
    code = SteeleyeErrorCodesEnum.SE_DV_902
    category = SteeleyeValidationCategoryEnum.IDENTIFIERS
    modules_affected = [
        ModulesAffectedEnum.RESTRICTED_LIST,
    ]
    severity = SeverityEnum.HIGH

    @classmethod
    def run_record_validation(cls, record: RestrictedList, **kwargs):
        """
        if [restrictedList.listType] is not populated
        then:
        RAISE ERROR!
        """
        if isinstance(record, RestrictedList):
            if record and not record.listType:
                raise cls.steeleye_validation_error(
                    message="Missing listType for `Restricted List`",
                    field_path="listType",
                )


class SteeleyeValidation903(SteelEyeRecordValidationRule):
    code = SteeleyeErrorCodesEnum.SE_DV_903
    category = SteeleyeValidationCategoryEnum.DATES
    modules_affected = [
        ModulesAffectedEnum.RESTRICTED_LIST,
    ]
    severity = SeverityEnum.HIGH

    @classmethod
    def run_record_validation(cls, record: RestrictedList, **kwargs):
        """
        if [restrictedList.created] is not populated
        then:
        RAISE ERROR!
        """
        if isinstance(record, RestrictedList):
            if record and not record.createdDateTime:
                raise cls.steeleye_validation_error(
                    message="Missing createdDateTime for `Restricted List`",
                    field_path="createdDateTime",
                )


class SteeleyeValidation904(SteelEyeRecordValidationRule):
    code = SteeleyeErrorCodesEnum.SE_DV_904
    category = SteeleyeValidationCategoryEnum.DATES
    modules_affected = [
        ModulesAffectedEnum.RESTRICTED_LIST,
    ]
    severity = SeverityEnum.HIGH

    @classmethod
    def run_record_validation(cls, record: Restriction, **kwargs):
        """
        if [restriction.permanency] is not populated
        then:
        RAISE ERROR!
        """
        if isinstance(record, Restriction):
            if record and not record.permanency:
                raise cls.steeleye_validation_error(
                    message="Missing permanency for `Restriction`",
                    field_path="restriction.permanency",
                )


class SteeleyeValidation905(SteelEyeRecordValidationRule):
    code = SteeleyeErrorCodesEnum.SE_DV_905
    category = SteeleyeValidationCategoryEnum.IDENTIFIERS
    modules_affected = [
        ModulesAffectedEnum.RESTRICTED_LIST,
    ]
    severity = SeverityEnum.HIGH

    @classmethod
    def run_record_validation(cls, record: Restriction, **kwargs):
        """
        if [restriction.identifier] is not populated
        then:
        RAISE ERROR!
        """
        if isinstance(record, Restriction):
            if record and not record.identifier:
                raise cls.steeleye_validation_error(
                    message="Missing identifier for `Restriction`",
                    field_path="restriction.identifier",
                )


class SteeleyeValidation906(SteelEyeRecordValidationRule):
    code = SteeleyeErrorCodesEnum.SE_DV_906
    category = SteeleyeValidationCategoryEnum.IDENTIFIERS
    modules_affected = [
        ModulesAffectedEnum.RESTRICTED_LIST,
    ]
    severity = SeverityEnum.HIGH

    @classmethod
    def run_record_validation(cls, record: Restriction, **kwargs):
        """
        if [restriction.identifierType] is not populated
        then:
        RAISE ERROR!
        """
        if isinstance(record, Restriction):
            if record and not record.identifierType:
                raise cls.steeleye_validation_error(
                    message="Missing identifierType for `Restriction`",
                    field_path="restriction.identifierType",
                )


class SteeleyeValidation907(SteelEyeRecordValidationRule):
    code = SteeleyeErrorCodesEnum.SE_DV_907
    category = SteeleyeValidationCategoryEnum.QUANTITIES
    modules_affected = [
        ModulesAffectedEnum.RESTRICTED_LIST,
    ]
    severity = SeverityEnum.HIGH

    @classmethod
    def run_record_validation(cls, record: Restriction, **kwargs):
        """
        if [restriction.minimumSizeType] is not populated
        and [restriction.minimumSize] is populated
        then:
        RAISE ERROR!
        """
        if isinstance(record, Restriction):
            if record and not record.minimumSizeType and record.minimumSize:
                raise cls.steeleye_validation_error(
                    message="Missing minimumSizeType when "
                    "minimumSize is populated for `Restriction`",
                    field_path="restriction.minimumSizeType",
                )


class SteeleyeValidation908(SteelEyeRecordValidationRule):
    code = SteeleyeErrorCodesEnum.SE_DV_908
    category = SteeleyeValidationCategoryEnum.QUANTITIES
    modules_affected = [
        ModulesAffectedEnum.RESTRICTED_LIST,
    ]
    severity = SeverityEnum.HIGH

    @classmethod
    def run_record_validation(cls, record: Restriction, **kwargs):
        """
        if [restriction.minimumSize] is not populated
        and [restriction.minimumSizeType] is populated
        then:
        RAISE ERROR!
        """
        if isinstance(record, Restriction):
            if record and not record.minimumSize and record.minimumSizeType:
                raise cls.steeleye_validation_error(
                    message="Missing minimumSize when "
                    "minimumSizeType is populated for `Restriction`",
                    field_path="restriction.minimumSize",
                )


class SteeleyeValidation909(SteelEyeRecordValidationRule):
    code = SteeleyeErrorCodesEnum.SE_DV_909
    category = SteeleyeValidationCategoryEnum.DATES
    modules_affected = [
        ModulesAffectedEnum.RESTRICTED_LIST,
    ]
    severity = SeverityEnum.HIGH

    @classmethod
    def run_record_validation(cls, record: Restriction, **kwargs):
        """
        if [restriction.permanency] is Indefinite
        and [restriction.fromDateTime]
        or [restriction.toDateTime] is populated
        then:
        RAISE ERROR!
        """
        if isinstance(record, Restriction):
            if (
                record
                and record.permanency == Permanency.INDEFINITE.value
                and (record.fromDateTime or record.toDateTime)
            ):
                raise cls.steeleye_validation_error(
                    message=f"`Restriction` fromDateTime or toDateTime must not "
                    f"be populated when permanency is {Permanency.INDEFINITE.value}",
                    field_path="restriction.permanency",
                )


class SteeleyeValidation910(SteelEyeRecordValidationRule):
    code = SteeleyeErrorCodesEnum.SE_DV_910
    category = SteeleyeValidationCategoryEnum.DATES
    modules_affected = [
        ModulesAffectedEnum.RESTRICTED_LIST,
    ]
    severity = SeverityEnum.HIGH

    @classmethod
    def run_record_validation(cls, record: Restriction, **kwargs):
        """
        if [restriction.permanency] is Temporary
        and [restriction.fromDateTime]
        or [restriction.toDateTime] is not populated
        then:
        RAISE ERROR!
        """
        if isinstance(record, Restriction):
            if (
                record
                and record.permanency == Permanency.TEMPORARY.value
                and not (record.fromDateTime and record.toDateTime)
            ):
                raise cls.steeleye_validation_error(
                    message=f"`Restriction` fromDateTime or toDateTime must "
                    f"be populated when permanency is {Permanency.TEMPORARY.value}",
                    field_path="restriction.permanency",
                )


class SteeleyeValidation911(SteelEyeRecordValidationRule):
    code = SteeleyeErrorCodesEnum.SE_DV_911
    category = SteeleyeValidationCategoryEnum.DATES
    modules_affected = [
        ModulesAffectedEnum.RESTRICTED_LIST,
    ]
    severity = SeverityEnum.HIGH

    @classmethod
    def run_record_validation(cls, record: Restriction, **kwargs):
        """if [restriction.fromDateTime] is greater than [restriction.toDateTime]
        then:
        RAISE ERROR!
        """
        if isinstance(record, Restriction):
            if record and (
                record.fromDateTime
                and record.toDateTime
                and record.fromDateTime >= record.toDateTime
            ):
                raise cls.steeleye_validation_error(
                    message="`Restriction` fromDateTime must be lesser than toDateTime",
                    field_path="restriction.fromDateTime",
                )


class SteeleyeValidation912(SteelEyeRecordValidationRule):
    code = SteeleyeErrorCodesEnum.SE_DV_912
    category = SteeleyeValidationCategoryEnum.METADATA
    modules_affected = [
        ModulesAffectedEnum.RESTRICTED_LIST,
    ]
    severity = SeverityEnum.HIGH

    @classmethod
    def run_record_validation(cls, record: Restriction, **kwargs):
        """
        if [restriction.created] is not populated
        then:
        RAISE ERROR!
        """
        if isinstance(record, Restriction):
            if record and not record.createdDateTime:
                raise cls.steeleye_validation_error(
                    message="Missing createdDateTime in `Restriction`",
                    field_path="restriction.createdDateTime",
                )


class SteeleyeValidation913(SteelEyeRecordValidationRule):
    code = SteeleyeErrorCodesEnum.SE_DV_913
    category = SteeleyeValidationCategoryEnum.METADATA
    modules_affected = [
        ModulesAffectedEnum.RESTRICTED_LIST,
    ]
    severity = SeverityEnum.HIGH

    @classmethod
    def run_record_validation(cls, record: Restriction, **kwargs):
        """
        if [restriction.createdBy] is not populated
        then:
        RAISE ERROR!
        """
        if isinstance(record, Restriction):
            if record and not record.createdBy:
                raise cls.steeleye_validation_error(
                    message="Missing createdBy in `Restriction`",
                    field_path="restriction.createdBy",
                )


class SteeleyeValidation914(SteelEyeRecordValidationRule):
    code = SteeleyeErrorCodesEnum.SE_DV_914
    category = SteeleyeValidationCategoryEnum.QUANTITIES
    modules_affected = [
        ModulesAffectedEnum.RESTRICTED_LIST,
    ]
    severity = SeverityEnum.HIGH

    @classmethod
    def run_record_validation(cls, record: Restriction, **kwargs):
        """if [restriction.minimumSizeCurrency] is not
        populated and [restriction.minimumSizeType] in [Order Notional, Executed Notional]
        then:
        RAISE ERROR!
        """
        if isinstance(record, Restriction):
            if (
                record
                and not record.minimumSizeCurrency
                and record.minimumSizeType
                in [
                    MinimumSizeType.ORDER_NOTIONAL.value,
                    MinimumSizeType.EXECUTED_NOTIONAL.value,
                ]
            ):
                raise cls.steeleye_validation_error(
                    message=f"`Restriction` minimumSizeCurrency must be populated "
                    f"when minimumSizeType is either "
                    f"{MinimumSizeType.ORDER_NOTIONAL.value} or "
                    f"{MinimumSizeType.EXECUTED_NOTIONAL.value}",
                    field_path="restriction.minimumSizeCurrency",
                )
