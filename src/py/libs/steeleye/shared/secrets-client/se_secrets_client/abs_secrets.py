import abc
import addict
from typing import Any


class AbstractVault(abc.ABC):
    """The abstract class that all our Secrets Stores should inherit from."""

    @abc.abstractmethod
    def get_secret(self, path: str, field: str) -> str:
        """Fetches singular secret.

        :param path: path to the secret in vault, e.g. "/{role}/secrets/test_secret"
        :param field: the key of secret
        :return: the secret value in string format
        """
        raise Exception("Abstract class method")

    @abc.abstractmethod
    def get_secrets(self, path: str) -> addict.Dict:
        """
        :param path: vault's path to the secret
        :return: the secret values
        """
        raise Exception("Abstract class method")

    @abc.abstractmethod
    def put_secret(self, path: str, **secrets) -> Any:
        """
        :param path: vault's path to the secret
        :param secrets: secrets to create

        :return: the secret value in string format
        """
        raise Exception("Abstract class method")
