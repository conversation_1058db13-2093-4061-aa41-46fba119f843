import logging
from elastic_transport import Transport  # pants: no-infer-dep
from elasticsearch8 import Elasticsearch
from se_elastic_schema.models.tenant.surveillance.sequence import Sequence as SequenceModel

logger = logging.getLogger(__name__)


class SequenceError(Exception):
    pass


class Sequence:
    def __init__(self, name: str, transport: Transport, index: str, elastic_api_key: str):
        """

        :param name: Name of the sequence
        :param transport: Transport Client
        :param index: ElasticSearch Index
        :param elastic_api_key: ELASTIC API KEY
        """
        self._name: str = name
        self._transport: Transport = transport
        self.elastic_api_key = elastic_api_key

        raw = '{{ "index":{{ "_index":"{index}","_id":"{name}"}}}}\n{{}}\n'
        self._request: str = raw.format(index=index, name=name)

    def next(self) -> int:
        """Generate the next valid integer in the sequence atomically.

        :returns: An integer that is the next value in the sequence.
        :raises: SequenceError
        """
        logger.debug("Getting next value for sequence %s", self._name)

        headers = {
            "Content-Type": "application/json",
            "Authorization": f"ApiKey {self.elastic_api_key}",
        }

        results = self._transport.perform_request(
            "POST",
            "/_bulk",
            body=self._request,
            headers=headers,
        ).body

        logger.debug("Sequence increment results for sequence %s: %s", self._name, results)

        if results["errors"]:
            raise SequenceError(f"Can't retrieve new IDs in sequence '{self._name}'")

        return int(results["items"][0]["index"]["_version"])


class SequenceFactory:
    """A sequence generator for ElasticSearch.

    Usage::

        es = Elasticsearch("http://127.0.0.1:9200")
        factory = SequenceFactory(raw_client=es)

        bob = factory.sequence("bob")
        id = bob.next()
    """

    def __init__(self, raw_client: Elasticsearch, tenant: str, elastic_api_key: str):
        """
        :param raw_client: A Elasticsearch client instance that the factory will use
                           to communicate with the data store.
        :param tenant: Name of the tenant to which the sequence should look into
        :param elastic_api_key: ELASTIC API KEY
        """
        self._es: Elasticsearch = raw_client
        self._index: str = SequenceModel.get_elastic_index_alias(tenant=tenant)
        self._elastic_api_key: str = elastic_api_key

    def sequence(self, name: str) -> Sequence:
        """Generates a Sequence iterator with ``name``.

        :param name: the sequence's name as a ``str``.
        :returns: a primed Sequence
        """
        return Sequence(
            name=name,
            transport=self.transport,
            index=self.index,
            elastic_api_key=self._elastic_api_key,
        )

    @property
    def index(self) -> str:
        return self._index

    @property
    def transport(self) -> Transport:
        return self._es.transport


class SequenceService:
    def __init__(self, es_client, tenant: str, elastic_api_key: str):
        """
        :param es_client: ElasticSearch client instance
        :param tenant: Name of the tenant to which the sequence should look into
        :param elastic_api_key: ELASTIC API KEY
        """
        self.seq_factory = SequenceFactory(
            raw_client=es_client, tenant=tenant, elastic_api_key=elastic_api_key
        )
        self.seq_cache: dict = {}

    def next(self, sequence_name: str):
        if sequence_name not in self.seq_cache:
            self.seq_cache[sequence_name] = self.seq_factory.sequence(sequence_name)
        return self.seq_cache[sequence_name].next()
