import fsspec
import os
from addict import addict
from fsspec import AbstractFileSystem
from se_enums.cloud import Cloud<PERSON>roviderEnum


def get_filesystem(
    cloud: str, lake_prefix: str, aws_region: str | None = None
) -> AbstractFileSystem:
    """Create destination filesystem for storage from fsspec
    Args:
        cloud: (str): cloud viz aws, azure etc
        lake_prefix: (str): lake_prefix viz s3://{tenant}.{stack}.steeleye.co/ if cloud is aws
        aws_region (str/None): aws region
    Returns:
        fs : filesystem: (AbstractFileSystem)
    """
    storage_options = addict.Dict()

    if cloud == CloudProviderEnum.AWS:
        storage_options.s3_additional_kwargs.ACL = "bucket-owner-full-control"
        if aws_region is not None:
            # for handling cases like citic where there bucket is different
            # from default IRELAND aws region
            storage_options.config_kwargs.region_name = aws_region
    elif cloud == CloudProviderEnum.AZURE:
        # This ENV variable must be present when the Cloud Provider is Azure
        # based on our convention that we use for Azure at the moment, Single storage account
        # for a stack/cell.
        storage_options.account_name = os.environ["AZURE_STORAGE_ACCOUNT_NAME"]
        storage_options.anon = False
    # Commented this to get rid of the tests, we will anyway have the cloud agnostic sdk
    # else:
    #     raise ValueError(f"Unsupported cloud provider: {cloud}")

    fs, _, (_,) = fsspec.get_fs_token_paths(lake_prefix, storage_options=storage_options.to_dict())
    return fs
