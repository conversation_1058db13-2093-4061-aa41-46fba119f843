python_sources(
    sources=["copilot_utils/**/*.py"],
    dependencies=["//:3rdparty#pytz", ":tiktoken_cache"],
)
python_test_utils(
    name="utils",
    sources=[
        "tests_copilot_utils/conftest.py",
    ],
)

python_tests(
    name="tests",
    sources=["tests_copilot_utils/**/test_*.py"],
    dependencies=[":utils", "//:3rdparty#pytz", ":tiktoken_cache"],
)

resource(
    name="tiktoken_cache",
    source="copilot_utils/tiktoken_encodings/fb374d419588a4632f3f557e76b4b70aebbca790",
    description="The cache for tiktoken",
)
