from enum import auto
from se_enums.core import BaseStrEnum


class EsActionEnum(BaseStrEnum):
    """For determining what kind of es action the event requires.

    Used by ApplyMeta when writing
    the ndjson used to upsert or create records. Please see bulk helpers in the elasticsearch docs
    below for a complete guide to their actions
    https://elasticsearch-py.readthedocs.io/en/7.10.0/helpers.html
    """

    CREATE = auto()
    INDEX = auto()
    UPDATE = auto()
    DELETE = auto()
