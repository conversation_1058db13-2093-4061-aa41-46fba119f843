import base64
import httpx
import logging
import time
from aries_se_api_client.auth.abstract_auth_client import AbstractAuthClient
from aries_se_api_client.auth.static import AUTH_EXPIRY_BUFFER_SECS, DEFAULT_AUTH_EXPIRY_SECS

log = logging.getLogger(__name__)


class SteeleyeCredentialsAuthClient(AbstractAuthClient):
    _expiring_at: float | None
    _auth_header: tuple[str, str] | None

    def __init__(self, auth_url: str, auth_user_email: str, auth_user_password: str):
        self._auth_header = None
        self._expiring_at = None
        self._auth_url = auth_url
        self._auth_user_email = auth_user_email
        self._auth_user_password = auth_user_password

    def _authorize(
        self,
    ):
        encoded = base64.b64encode(
            bytes(f"{self._auth_user_email}:{self._auth_user_password}", "utf8")
        ).decode("utf8")
        headers = {"Authorization": f"RESIDENT {encoded}"}

        response = httpx.post(url=self._auth_url, headers=headers)
        return self._handle_response(response)

    def _handle_response(self, response: httpx.Response):
        if response.status_code == httpx.codes.BAD_REQUEST:
            error_msg = response.json()["error"]
            if error_msg == "invalid_client":
                raise ValueError("Invalid credentials")
            if error_msg == "unauthorized_client":
                raise ValueError("Client is not authorized to perform this operation")
        return response

    def get_authorization_header(self, *args, cached=True, **kwargs) -> tuple[str, str]:
        """Returns auth header. If cached is True, then previously stored auth
        token is returned, otherwise a new token is returned.

        @param scopes:
        @param cached:
        @return:
        """
        if cached and self._auth_header and self._expiring_at and time.time() < self._expiring_at:
            log.debug(f"Reusing auth header {self._auth_header}")
            return self._auth_header

        response = self._authorize()
        response_expiry = response.json().get("ttlExpiry")
        if not response_expiry:
            response_expiry = DEFAULT_AUTH_EXPIRY_SECS
        self._expiring_at = time.time() + response_expiry + AUTH_EXPIRY_BUFFER_SECS
        token = response.json()["attributes"]["ACCESS_TOKEN"]
        auth_token = base64.b64encode(bytes(f"{self._auth_user_email}:{token}", "utf8")).decode(
            "utf8"
        )
        self._auth_header = ("Authorization", f"TOKEN {auth_token}")
        return self._auth_header
