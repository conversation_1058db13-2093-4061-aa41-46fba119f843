python_sources(
    sources=["aries_se_comms_tasks/**/*.py"],
    dependencies=[
        "//:se_libs#e164-converter",
        # some test .pkl files hold the qualified_reference of se-schema enums
        "//:se_libs#se-schema",
        "//:3rdparty#pycryptodome",
        "//:3rdparty#lxml",
        "//:3rdparty#webvtt-py",
        "//:3rdparty#azure-ai-translation-text",
        "//:se_libs#se-market-data-utils",
    ],
)

python_test_utils(name="conftest", sources=["tests_aries_se_comms_tasks/**/conftest.py"])
python_tests(
    name="tests",
    sources=["tests_aries_se_comms_tasks/**/test_*.py"],
    dependencies=[
        "//:3rdparty#pycryptodome",
        "//:3rdparty#lxml",
        ":conftest",
        ":email_test_files",
        ":generics_test_files",
        ":voice_test_files",
        ":message_test_files",
        ":text_test_files",
        ":transcription_test_files",
        ":bloomberg_test_files",
        ":utilities_test_files",
    ],
    extra_env_vars=["FFMPEG_BIN_PATH"],
)

resources(
    name="email_test_files",
    sources=[
        "tests_aries_se_comms_tasks/feeds/email/**/*.pkl",
        "tests_aries_se_comms_tasks/feeds/email/transform/**/*eml",
        "tests_aries_se_comms_tasks/feeds/email/**/*.ndjson",
    ],
)

resources(
    name="voice_test_files",
    sources=[
        "tests_aries_se_comms_tasks/feeds/voice/**/*.pkl",
        "tests_aries_se_comms_tasks/feeds/voice/**/*.mp3",
        "tests_aries_se_comms_tasks/feeds/voice/**/*.wav",
        "tests_aries_se_comms_tasks/feeds/voice/**/*.json",
        "tests_aries_se_comms_tasks/recording/**/*.mp3",
        "tests_aries_se_comms_tasks/recording/**/*.mp4",
        "tests_aries_se_comms_tasks/recording/**/*.opus",
        "tests_aries_se_comms_tasks/recording/**/*.wav",
        "tests_aries_se_comms_tasks/voice/**/*.xml",
        "tests_aries_se_comms_tasks/voice/data/buckets/**/*",
        "tests_aries_se_comms_tasks/feeds/zoom/data/**/*.pkl",
        "tests_aries_se_comms_tasks/converter/data/buckets/**/*",
    ],
)

resources(
    name="message_test_files",
    sources=[
        "tests_aries_se_comms_tasks/feeds/message/**/*.eml",
        "tests_aries_se_comms_tasks/feeds/message/**/*.pkl",
        "tests_aries_se_comms_tasks/feeds/message/**/*.xml",
        "tests_aries_se_comms_tasks/feeds/message/**/*.zip",
        "tests_aries_se_comms_tasks/feeds/message/**/*.json",
        "tests_aries_se_comms_tasks/feeds/message/slack_chat/data/**/*",
        "tests_aries_se_comms_tasks/feeds/message/ms_teams_chat/data/buckets/**/*",
    ],
)

resources(
    name="text_test_files",
    sources=[
        "tests_aries_se_comms_tasks/feeds/text/**/*.json",
    ],
)

resources(
    name="transcription_test_files",
    sources=[
        "tests_aries_se_comms_tasks/transcription/**/*.pkl",
        "tests_aries_se_comms_tasks/transcription/**/*.wav",
        "tests_aries_se_comms_tasks/transcription/**/*.json",
        "tests_aries_se_comms_tasks/transcription/**/*.ndjson",
    ],
)

resources(
    name="bloomberg_test_files",
    sources=[
        "tests_aries_se_comms_tasks/feeds/bloomberg/**/*.pdf",
        "tests_aries_se_comms_tasks/feeds/bloomberg/**/*.jpg",
        "tests_aries_se_comms_tasks/feeds/bloomberg/**/*.png",
        "tests_aries_se_comms_tasks/feeds/bloomberg/**/*.html",
        "tests_aries_se_comms_tasks/feeds/bloomberg/**/*.txt",
        "tests_aries_se_comms_tasks/feeds/bloomberg/**/*.xml",
        "tests_aries_se_comms_tasks/feeds/bloomberg/**/*.ndjson",
    ],
)

resources(
    name="utilities_test_files",
    sources=[
        "tests_aries_se_comms_tasks/utilities/**/*.json",
    ],
)

resources(
    name="generics_test_files",
    sources=[
        "tests_aries_se_comms_tasks/generic/attachment/data/buckets/**/*",
    ],
)
