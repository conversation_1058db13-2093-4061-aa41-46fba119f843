import logging
from aries_se_comms_tasks.feeds.message.refinitiv_tr_eikon.helper_functions import (  # type:ignore[attr-defined]
    eval_literal_string_list,
)
from aries_se_comms_tasks.monitored_users.abstractions.abstract_chat_monitored_users import (
    AbstractChatMonitoredUsers,
)
from aries_se_comms_tasks.monitored_users.utils import (
    get_communications_emails_set,
    get_im_accounts_set,
)
from se_core_tasks.mymarket.static import PersonColumns
from se_elastic_schema.static.reference import ImAccountType
from typing import Set

logger = logging.getLogger(__name__)


class RefinitivTrEikonMonitoredUsers(AbstractChatMonitoredUsers):
    """Refinitiv TR Eikon/Reuters Eikon Monitored users class.

    Any generic logic is present here. Client-specific logic should be
    stored in derived classes.
    """

    def fetch_monitored_users(self, tenant: str, **kwargs):
        """Fetches monitored users from Elasticsearch for TR Eikon and
        populates self.monitored_user_ids."""
        if not self.monitored_user_filtering_flag:
            return
        monitored_user_df = self.fetch_monitored_user_df_from_elastic(
            tenant=tenant,
            columns_to_fetch=[
                PersonColumns.COMMUNICATIONS_EMAILS,
                PersonColumns.COMMUNICATIONS_IMACCOUNTS,
            ],
        )
        # Extract all emails and add them to a set
        emails_set = get_communications_emails_set(monitored_person_df=monitored_user_df)

        # Extract IM account IDs with label 'Reuters' or 'Live Chat' and add them to a set
        im_accounts_set = get_im_accounts_set(
            monitored_person_df=monitored_user_df,
            im_account_labels=[ImAccountType.REUTERS.value, ImAccountType.LIVE_CHAT.value],
        )

        self.monitored_user_ids = emails_set.union(im_accounts_set)

    def get_involved_user_ids_set(self, participants_list_string: str, **kwargs) -> Set[str]:  # type:ignore[override]
        """Gets the user ids involved by converting the user ids in
        participants_list into a set.

        :param participants_list_string: Literal string of participants --
        that evaluates to a List of strings when the Chat contains
        multiple participants, and to a single String when there is just one participant.
        :returns Set of user ids involved in the current chat
        """
        participants_list = eval_literal_string_list(literal_string_list=participants_list_string)
        involved_user_ids = set(map(lambda x: x.lower(), participants_list))
        return involved_user_ids
