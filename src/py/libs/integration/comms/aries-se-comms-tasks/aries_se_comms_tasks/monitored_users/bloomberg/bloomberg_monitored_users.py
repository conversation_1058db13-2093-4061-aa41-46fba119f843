import logging
from aries_se_comms_tasks.monitored_users.abstractions.abstract_chat_monitored_users import (
    AbstractChatMonitoredUsers,
)
from aries_se_comms_tasks.monitored_users.bloomberg.static import (
    BloombergFileType,
    BloombergIb19<PERSON>ventTags,
    BloombergTags,
)
from aries_se_comms_tasks.monitored_users.utils import (
    get_communications_emails_set,
    get_im_accounts_set,
)
from aries_se_core_tasks.utilities.data_utils import ensure_list
from se_core_tasks.mymarket.static import PersonColumns
from se_elastic_schema.static.reference import ImAccountType
from typing import Callable, Dict, List, Set

logger = logging.getLogger(__name__)


class BloombergMonitoredUsers(AbstractChatMonitoredUsers):
    """Bloomberg Monitored users class.

    Any generic logic is present here. Client-specific logic should be
    created in derived classes.
    """

    def fetch_monitored_users(self, tenant: str, **kwargs):
        """Fetches monitored users from Elasticsearch for Bloomberg and
        populates self.monitored_user_ids."""
        if not self.monitored_user_filtering_flag:
            return
        monitored_user_df = self.fetch_monitored_user_df_from_elastic(
            tenant=tenant,
            columns_to_fetch=[
                PersonColumns.COMMUNICATIONS_EMAILS,
                PersonColumns.COMMUNICATIONS_IMACCOUNTS,
            ],
        )
        # Extract all emails and add them to a set
        emails_set = get_communications_emails_set(monitored_person_df=monitored_user_df)

        # Extract IM account IDs with label 'BBG' or 'Live Chat' and add them to a set
        im_accounts_set = get_im_accounts_set(
            monitored_person_df=monitored_user_df,
            im_account_labels=[ImAccountType.BBG.value, ImAccountType.LIVE_CHAT.value],
        )

        self.monitored_user_ids = emails_set.union(im_accounts_set)

    def get_involved_user_ids_set(  # type: ignore[override]
        self, conversation_dict: dict, file_type: BloombergFileType, **kwargs
    ) -> Set[str]:
        """Uses conversation_dict to get a set of users involved in a Bloomberg
        conversation or email.

        :param conversation_dict: Conversation dictionary (IB19 or MSG)
        :param file_type: Bloomberg file type (IB19 or MSG)
        :returns Set of user ids involved in the conversation
        """

        # Define a dictionary to map file types to set creation methods
        set_creation_methods: Dict[BloombergFileType, Callable[[dict], Set[str]]] = {
            BloombergFileType.IB19: self._create_ib19_user_ids_set,
            BloombergFileType.MSG: self._create_msg_user_ids_set,
        }
        # Get the set creation method for the given file type
        create_user_ids_set: Callable[[dict], Set[str]] | None = set_creation_methods.get(file_type)
        if not set_creation_methods:
            raise NotImplementedError("Only Bloomberg MSG and IB19 file types are allowed")
        # Get involved user IDs based on file type
        involved_user_ids = create_user_ids_set(conversation_dict=conversation_dict)  # type: ignore[misc, call-arg]
        return involved_user_ids

    def _create_ib19_user_ids_set(self, conversation_dict: dict) -> Set[str]:
        """For an IB19 conversation_dict, add all the monitored user ids to a
        set.

        To do this, we extract each of the following tags from conversation_dict,
        if available:
        <Message><User>
        <Attachment><User>
        <ParticipantEntered><User>
        <ParticipantLeft><User>
        <Invite><Inviter>
        <Invite><Invitee>

        From each of these tags, we extract the following sub-tags if present to
        get the involved_user_ids:
        <LoginName>, <EmailAddress>, <CorporateEmailAddress> and <ClientID1>

        :param conversation_dict: Bloomberg IB19 conversation dictionary
        :returns Set containing all the involved user ids in the conversation
        """
        involved_user_ids: Set[str] = set()
        required_keys = {
            BloombergIb19EventTags.MESSAGE,
            BloombergIb19EventTags.ATTACHMENT,
            BloombergIb19EventTags.PARTICIPANT_ENTERED,
            BloombergIb19EventTags.PARTICIPANT_LEFT,
        }
        tags_to_extract = BloombergTags.get_ib19_user_id_tags()
        for key_ in required_keys:
            # xmltodict returns a list or a dict based on how many values of
            # the same keys are present
            for dict_ in ensure_list(dct=conversation_dict, key=key_):
                user = dict_.get(BloombergTags.USER, {})
                involved_user_ids |= self._extract_involved_user_ids(
                    user_details_dict=user, tags_to_extract=tags_to_extract
                )
        # We use different sub-keys for invites
        for invite_dict in ensure_list(dct=conversation_dict, key=BloombergIb19EventTags.INVITE):
            inviter = invite_dict.get(BloombergTags.INVITER, {})
            invitee = invite_dict.get(BloombergTags.INVITEE, {})
            involved_user_ids |= self._extract_involved_user_ids(
                user_details_dict=inviter, tags_to_extract=tags_to_extract
            )
            involved_user_ids |= self._extract_involved_user_ids(
                user_details_dict=invitee, tags_to_extract=tags_to_extract
            )
        return involved_user_ids

    def _create_msg_user_ids_set(self, conversation_dict: dict) -> Set[str]:
        """For an MSG conversation_dict, add all the monitored user ids to a
        set.

        To do this, we extract each of the following tags from conversation_dict,
        if available:
        <Sender><UserInfo>
        <Recipient><UserInfo>

        Recipient can be a list containing multiple recipient dicts. In addition,
        a recipient can have an inner ForwardedTo tag, which itself can contain
        more recipient dicts/list of dicts.

        From each of these tags, we extract the following sub-tags if present to
        get the involved_user_ids:
        <ClientID1>, <BloombergEmailAddress> and <CorporateEmailAddress>

        :param conversation_dict: Bloomberg MSG conversation dictionary
        :returns Set containing all the involved user ids in the conversation
        """
        involved_user_ids: Set[str] = set()
        sender_dict = conversation_dict.get(BloombergTags.SENDER, {})
        involved_user_ids |= self._extract_involved_user_ids(
            user_details_dict=sender_dict.get(BloombergTags.USER_INFO, {}),
            tags_to_extract=BloombergTags.get_msg_user_id_tags(),
        )
        for recipient_dict in ensure_list(dct=conversation_dict, key=BloombergTags.RECIPIENT):
            involved_user_ids |= self._get_recipient_ids_set(recipient_dict=recipient_dict)

        return involved_user_ids

    def _get_recipient_ids_set(self, recipient_dict: dict) -> Set[str]:
        """This is a recursive function to get all the recipient user ids for a
        recipient dictionary. For each recipient dictionary, this function gets
        the user ids from the userInfo tag. It then checks if there is a
        ForwardedTo component. If there is, it extracts each forwarded
        recipient and the user ids therein.

        :param recipient_dict: Recipient dictionary
        :returns Set of recipient user ids
        """
        recipient_user_ids = set()
        user_info = recipient_dict.get(BloombergTags.USER_INFO, {})
        recipient_user_ids |= self._extract_involved_user_ids(
            user_details_dict=user_info, tags_to_extract=BloombergTags.get_msg_user_id_tags()
        )
        forwarded_to_dict = recipient_dict.get(BloombergTags.FORWARDED_TO, {})
        for forwarded_recipient_dict in ensure_list(
            dct=forwarded_to_dict, key=BloombergTags.RECIPIENT
        ):
            recipient_user_ids |= self._get_recipient_ids_set(
                recipient_dict=forwarded_recipient_dict
            )
        return recipient_user_ids

    @staticmethod
    def _extract_involved_user_ids(user_details_dict: dict, tags_to_extract: List[str]) -> Set[str]:
        """Extracts each of the tags in tags_to_extract from user_details_dict
        For ib19: If available, we extract each of <BloombergEmailAddress>,

        <EmailAddress> and <ClientID1> from user_details_dict, and add them to
        the involved_user_ids_set.

        For msg:
        If available, we extract each of <LoginName>, <EmailAddress>,
        <CorporateEmailAddress> and <ClientID1> from user_details_dict, and add
        them to the involved_user_ids_set.

        NOTE: It is possible that the tags may have no content, i.e., None. This is
        handled as well while creating the set.

        :param user_details_dict: User details dictionary, from which sub-tags are
        retrieved
        :param tags_to_extract: List of tags to extract from user_details_dict
        :returns None, mutates involved_user_ids_set
        """

        user_ids_set = set(
            value.lower()
            for key, value in user_details_dict.items()
            if key in tags_to_extract and value is not None
        )
        return user_ids_set
