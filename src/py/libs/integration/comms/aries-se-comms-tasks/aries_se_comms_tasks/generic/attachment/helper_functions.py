# mypy: disable-error-code="attr-defined"
import logging
import pandas as pd
from aries_se_comms_tasks.generic.attachment.static import Attachment<PERSON>olum<PERSON>, FileInfoSubFields
from aries_se_core_tasks.core.core_dataclasses import CloudAction, CloudFile
from aries_se_core_tasks.io.utils import FileInfo, get_file_info
from aries_se_core_tasks.io.write.upload_file import Params as UploadFileParams
from aries_se_core_tasks.io.write.upload_file import run_upload_file
from pandas._libs.missing import NAType
from pathlib import Path
from se_data_lake.cloud_utils import get_bucket_and_key, get_file_uri
from se_enums.cloud import Cloud<PERSON><PERSON>iderEnum
from typing import Any, Dict, List

logger = logging.getLogger(__name__)


def upload_and_get_schema_format_attachments_list(
    local_files_list: List[str],
    cloud_provider: CloudProviderEnum,
    bucket: str,
    upload_key_prefix: str,
) -> List[dict] | None:
    """Uploads attachments in the list of nested attachments and puts the in
    schema format. Returns a list of attachments in schema format.

    :param local_files_list: Local attachment path
    :param cloud_provider: Cloud provider
    :param bucket: Bucket
    :param upload_key_prefix: Upload key prefix
    :return: List of attachments in schema format or an empty list
    """
    uploaded_attachments = run_upload_file(
        upload_target=[
            CloudFile(
                file_path=Path(local_attachment),
                bucket_name=bucket,
                key_name=Path(upload_key_prefix).joinpath(Path(local_attachment).name).as_posix(),
                action=CloudAction.UPLOAD,
            )
            for local_attachment in local_files_list
        ],
        params=UploadFileParams(),
        cloud_provider=cloud_provider,
    )
    uploaded_file_uris = [
        get_file_uri(
            cloud_provider=cloud_provider,
            key=attachment.key_name,
            bucket=attachment.bucket_name,
        )
        for attachment in uploaded_attachments.targets
    ]

    file_info_list = [get_metadata(file_uri=file_uri) for file_uri in uploaded_file_uris]
    transformed_attachments = [
        transform_attachment(
            file_url=file_uri,
            metadata=metadata,
        )
        for file_uri, metadata in zip(uploaded_file_uris, file_info_list)
    ]
    return transformed_attachments


def get_schema_attachments(cloud_file_uris: List[str] | str) -> List[dict] | None:
    """For attachments which are already in the cloud, it returns a list of
    attachments in the 'attachments' field schema format.

    :param cloud_file_uris: Attachment file uri/uris to upload
    :return: List of attachments in schema format or an empty list
    """
    if isinstance(cloud_file_uris, str):
        cloud_file_uris = [cloud_file_uris]
    file_info_list = [get_metadata(file_uri=file_uri) for file_uri in cloud_file_uris]
    transformed_attachments = [
        transform_attachment(
            file_url=file_uri,
            metadata=metadata,
        )
        for file_uri, metadata in zip(cloud_file_uris, file_info_list)
    ]
    return transformed_attachments


def get_metadata(file_uri: str) -> FileInfo | NAType:
    try:
        return get_file_info(path=file_uri)
    except FileNotFoundError as e:
        logger.warning(f"Error accessing file: {file_uri} {e}")
        return pd.NA


def transform_attachment(
    file_url: str,
    metadata: FileInfo | NAType | None,
) -> Dict[str, Any]:
    """Transforms metadata from S3 into desired fields.

    :param file_url: Cloud url
    :param metadata: metadata dict
    :return: transformed dict
    """
    if pd.isna(metadata) or not metadata:  # type: ignore
        empty_attachment = {
            AttachmentColumns.FILE_NAME: pd.NA,
            AttachmentColumns.FILE_TYPE: pd.NA,
            AttachmentColumns.SIZE_IN_BYTES: pd.NA,
            AttachmentColumns.FILE_INFO: {
                FileInfoSubFields.LOCATION: {
                    FileInfoSubFields.BUCKET: pd.NA,
                    FileInfoSubFields.KEY: pd.NA,
                },
                FileInfoSubFields.LAST_MODIFIED: pd.NA,
                FileInfoSubFields.CONTENT_LENGTH: pd.NA,
                FileInfoSubFields.VERSION_ID: pd.NA,
            },
        }
        return empty_attachment

    bucket, key_ = get_bucket_and_key(file_uri=file_url)

    key_as_path: Path = Path(key_)

    attachment = {
        AttachmentColumns.FILE_NAME: key_as_path.name,
        AttachmentColumns.FILE_TYPE: key_as_path.suffix.lower(),
        AttachmentColumns.SIZE_IN_BYTES: metadata.size,
        AttachmentColumns.FILE_INFO: {
            FileInfoSubFields.LOCATION: {
                FileInfoSubFields.BUCKET: bucket,
                FileInfoSubFields.KEY: str(key_),
            },
            FileInfoSubFields.LAST_MODIFIED: metadata.last_modified.strftime("%Y-%m-%dT%H:%M:%S"),
            FileInfoSubFields.CONTENT_LENGTH: metadata.size,
            FileInfoSubFields.VERSION_ID: metadata.version_id,
        },
    }

    return attachment
