# type: ignore
import ast
import logging
import numpy as np
import pandas as pd
from api_sdk.auth import tenant_from_realm
from aries_se_comms_tasks.abstractions.transformations.abstract_transcript_transformations import (
    AbstractTranscriptTransformations,
)
from aries_se_comms_tasks.transcription.intelligent_voice.static import (
    IV_MODEL_LANG_MAPPING,
    IVSourceColumns,
    IVSourceSRT,
    IVSourceSRTSpeaker,
    IVSourceTags,
    TempCols,
)
from aries_se_comms_tasks.transcription.intelligent_voice.utils import (
    fetch_segments,
    fetch_sentiment,
)
from aries_se_comms_tasks.transcription.static import TranscriptionFields, TranscriptModelFields
from aries_se_core_tasks.core.exception import TaskException
from aries_se_core_tasks.datetime.convert_datetime import Params as CDParams
from aries_se_core_tasks.datetime.convert_datetime import run_convert_datetime
from aries_se_core_tasks.datetime.datetime_difference import Params as DDParams
from aries_se_core_tasks.datetime.datetime_difference import run_datetime_difference
from aries_se_core_tasks.transform.map.map_attribute import Params as MAParams
from aries_se_core_tasks.transform.map.map_attribute import run_map_attribute
from aries_se_core_tasks.transform.map.map_conditional import Params as MapConditionalParams
from aries_se_core_tasks.transform.map.map_conditional import run_map_conditional
from se_core_tasks.datetime.convert_datetime import ConvertTo
from se_core_tasks.map.map_conditional import Case
from se_elastic_schema.static.tenant_configuration import FeatureFlags
from se_elastic_schema.static.timestamp import TimeUnitEnum
from se_elastic_schema.static.transcript import TokenTypeEnum, VendorEnum
from typing import List, Optional, Union

logger = logging.getLogger(__name__)


class SkipIfNoTranscriptionsContainingText(TaskException):
    pass


class IntelligenceVoiceFeedTranscriptMappings(AbstractTranscriptTransformations):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.transcript_files_ftp = kwargs.get("transcript_files_ftp")
        self.tenant_config_feature_flags = kwargs.get("tenant_config_feature_flags")

    def process(self) -> pd.DataFrame:
        self.pre_process()
        self.is_transcribed_by_steeleye()
        self.id_()
        self.is_annotated()
        self.job_id()
        self.languages()
        self.recording_source_key()
        self.transcript_source_key()
        self.model_language()
        self.model_vendor()
        self.model_id()
        self.number_of_languages()
        self.datetime_transcription_end()
        self.datetime_transcription_start()
        self.datetime_job_submitted()
        self.datetime_transcription_duration_value()
        self.datetime_transcription_duration_unit()
        self.text()
        self.tokens()
        self.confidence()
        self.speakers()
        self.count_tokens()
        self.count_distinct_tokens()
        self.count_speakers()
        self.translations()
        self.segments()
        self.sentiment()
        if FeatureFlags.AZURE_PROCESSING.value not in self.tenant_config_feature_flags:
            # If tenant is eligible for copilot, then summary and topics
            # are populated in transcription_copilot flow
            self.summary()
            self.topics()

        self.post_process()
        return self.target_df.fillna(pd.NA)

    def _pre_process(self):
        self.tenant = tenant_from_realm(realm=self.realm)
        # add mandatory columns
        required_cols = IVSourceColumns().all()
        for col in required_cols:
            if col not in self.source_frame.columns:
                self.source_frame.loc[:, col] = pd.NA

    def _post_process(self):
        # columns which will be used in downstream tasks are populated in post_process
        self.target_df.loc[:, TranscriptionFields.TRANSCRIPTION_STATUS_DESCRIPTION] = (
            self.source_frame.loc[:, IVSourceColumns.TRANSCRIPTION_STATUS_DESCRIPTION]
        )

    def _is_transcribed_by_steeleye(self) -> Union[pd.Series, pd.DataFrame]:
        """True if we hit the API in IV Submit and IV Get.

        False if IV FTP the transcript files to us.
        """
        transcribed_by_steeleye = True if not self.transcript_files_ftp else False
        return pd.DataFrame(
            data=transcribed_by_steeleye,
            index=self.source_frame.index,
            columns=[TranscriptModelFields.IS_TRANSCRIBED_BY_STEELEYE],
        )

    def _id_(self) -> Union[pd.Series, pd.DataFrame]:
        """Map from IVSourceColumns.DOC_ID."""
        return self.source_frame.loc[:, IVSourceColumns.TRANSCRIPT_ITEM_ID].fillna(
            self.source_frame.loc[:, IVSourceColumns.DOC_ID].astype(str)
        )

    def _is_annotated(self) -> Union[pd.Series, pd.DataFrame]:
        """
        Static value = False
        """
        return pd.DataFrame(
            data="False",
            index=self.source_frame.index,
            columns=[TranscriptModelFields.IS_ANNOTATED],
        )

    def _job_id(self) -> Union[pd.Series, pd.DataFrame]:
        """Populated from "IVSourceColumns.TRANSCRIPT_ITEM_ID" that comes from
        the IV API response and has a fallback to DOC_ID."""
        return (
            self.source_frame.loc[:, IVSourceColumns.TRANSCRIPT_ITEM_ID]
            .astype(str)
            .fillna(self.source_frame.loc[:, IVSourceColumns.DOC_ID].astype(str))
        )

    def _number_of_languages(self) -> Union[pd.Series, pd.DataFrame]:
        """Populates from IVSourceColumns.SOURCE_AUDIO_LANGUAGES."""
        self.source_frame.loc[:, IVSourceColumns.SOURCE_AUDIO_LANGUAGES] = self.source_frame.loc[
            :, IVSourceColumns.SOURCE_AUDIO_LANGUAGES
        ].apply(lambda x: ast.literal_eval(x) if isinstance(x, str) else x)
        return self.source_frame.loc[:, IVSourceColumns.SOURCE_AUDIO_LANGUAGES].str.len()

    def _recording_source_key(self) -> Union[pd.Series, pd.DataFrame]:
        """Populated from "recordingSourceKey" which comes from the IV Feed
        input CSV."""
        return run_map_attribute(
            source_frame=self.source_frame,
            params=MAParams(
                target_attribute=TranscriptModelFields.RECORDING_SOURCE_KEY,
                source_attribute=TranscriptModelFields.RECORDING_SOURCE_KEY,
            ),
            skip_serializer=True,
        )

    def _transcript_source_key(self) -> Union[pd.Series, pd.DataFrame]:
        """Populated from "transcriptSourceKey" which comes from the IV Feed
        input CSV."""
        return self.source_frame.loc[:, TranscriptModelFields.TRANSCRIPT_SOURCE_KEY]

    def _model_vendor(self) -> Union[pd.Series, pd.DataFrame]:
        """
        Static value = "Intelligent Voice"
        """
        return pd.DataFrame(
            data=VendorEnum.INTELLIGENT_VOICE.value,
            index=self.source_frame.index,
            columns=[TranscriptModelFields.MODEL_VENDOR],
        )

    def _model_id(self) -> Union[pd.Series, pd.DataFrame]:
        """Populated from "modelNames" which comes from the IV Feed input CSV.

        When there are multiple languages, the IV Get Transcription flow
        always sends the Xlang model as the first element
        """
        self.source_frame.loc[:, IVSourceColumns.MODEL_NAMES] = self.source_frame.loc[
            :, IVSourceColumns.MODEL_NAMES
        ].apply(lambda x: ast.literal_eval(x) if isinstance(x, str) else x)
        return self.source_frame.loc[:, IVSourceColumns.MODEL_NAMES].str[0]

    def _model_language(self) -> Union[pd.Series, pd.DataFrame]:
        """Populated from "self.model_lang" deduced in _languages() method.

        Assumes _languages() is executed before this

        IMPORTANT: It is only executed IF the languages key is present in
        SRTs. It is not available when we get the transcripts directly from
        IV.
        """

        if self.target_df.loc[:, TranscriptModelFields.LANGUAGES].notnull().any():
            return self.target_df.loc[:, TranscriptModelFields.LANGUAGES].apply(
                lambda x: self._fetch_model_language(languages=x)
            )

    def _datetime_transcription_start(self) -> Union[pd.Series, pd.DataFrame]:
        """UTC Datetime."""
        return run_convert_datetime(
            source_frame=self.source_frame,
            params=CDParams(
                source_attribute=IVSourceColumns.PROCESSING_START_TIME,
                target_attribute=TranscriptModelFields.DATETIME_TRANSCRIPTION_START,
                convert_to=ConvertTo.DATETIME.value,
            ),
            skip_serializer=True,
        )

    def _datetime_transcription_end(self) -> Union[pd.Series, pd.DataFrame]:
        """UTC Datetime."""
        return run_convert_datetime(
            source_frame=self.source_frame,
            params=CDParams(
                source_attribute=IVSourceColumns.PROCESSING_END_TIME,
                target_attribute=TranscriptModelFields.DATETIME_TRANSCRIPTION_END,
                convert_to=ConvertTo.DATETIME.value,
            ),
            skip_serializer=True,
        )

    def _datetime_job_submitted(self) -> Union[pd.Series, pd.DataFrame]:
        """UTC Datetime."""
        return run_convert_datetime(
            source_frame=self.source_frame,
            params=CDParams(
                source_attribute=IVSourceColumns.PROCESSING_START_TIME,
                target_attribute=TranscriptModelFields.DATETIME_JOB_SUBMITTED,
                convert_to=ConvertTo.DATETIME.value,
            ),
            skip_serializer=True,
        )

    def _datetime_transcription_duration_value(self) -> Union[pd.Series, pd.DataFrame]:
        """How long the transcription took in seconds.

        This is obtained from the difference between the transcription
        end and start time. If that is null, it is obtained from the
        duration column
        """
        temp_df = run_datetime_difference(
            source_frame=self.target_df,
            params=DDParams(
                start_column=TranscriptModelFields.DATETIME_TRANSCRIPTION_START,
                end_column=TranscriptModelFields.DATETIME_TRANSCRIPTION_END,
                target_column=TempCols.TRANSCRIPTION_END_MINUS_START,
                format_output_as_seconds=True,
            ),
            skip_serializer=True,
        )
        return run_map_conditional(
            source_frame=pd.concat(
                [self.source_frame.loc[:, [IVSourceColumns.DURATION]], temp_df], axis=1
            ),
            params=MapConditionalParams(
                cases=[
                    Case(
                        query=f"(`{TempCols.TRANSCRIPTION_END_MINUS_START}`.notnull())",
                        attribute=TempCols.TRANSCRIPTION_END_MINUS_START,
                    ),
                    Case(
                        query=f"(`{TempCols.TRANSCRIPTION_END_MINUS_START}`.isnull())",
                        attribute=IVSourceColumns.DURATION,
                    ),
                ],
                target_attribute=TranscriptModelFields.DATETIME_TRANSCRIPTION_DURATION_VALUE,
            ),
            skip_serializer=True,
        )

    def _datetime_transcription_duration_unit(self) -> Union[pd.Series, pd.DataFrame]:
        """
        Static value = "seconds"
        """
        return pd.DataFrame(
            data=TimeUnitEnum.SECONDS.value,
            index=self.source_frame.index,
            columns=[TranscriptModelFields.DATETIME_TRANSCRIPTION_DURATION_UNIT],
        )

    def _languages(self) -> Union[pd.Series, pd.DataFrame]:
        """Populates languages."""
        return np.vectorize(self._fetch_languages)(
            srts=self.source_frame.loc[:, IVSourceColumns.SRTS],
        )

    def _text(self) -> Union[pd.Series, pd.DataFrame]:
        """Map from IVSourceColumns.ALL_TEXT."""
        return self.source_frame.loc[:, IVSourceColumns.ALL_TEXT_PER_SPEAKER].fillna(
            self.source_frame.loc[:, IVSourceColumns.ALL_TEXT]
        )

    def _confidence(self) -> Union[pd.Series, pd.DataFrame]:
        """Confidence on transcription.Assumes _tokens() method is called
        before this."""
        return np.vectorize(self._fetch_confidence)(
            tokens=self.target_df.loc[:, TranscriptModelFields.TOKENS]
        )

    def _count_tokens(self) -> Union[pd.Series, pd.DataFrame]:
        """Populates count.tokens."""
        return (self.target_df[TranscriptModelFields.TOKENS].str.len()).astype((pd.Int64Dtype()))

    def _count_distinct_tokens(self) -> Union[pd.Series, pd.DataFrame]:
        """Number of distinct tokens (words) in the transcript."""
        return self.target_df.loc[:, TranscriptModelFields.TOKENS].apply(
            lambda x: len(set(tkn[TranscriptModelFields.TOKEN] for tkn in x))
        )

    def _count_speakers(self) -> Union[pd.Series, pd.DataFrame]:
        """Populates count.speakers."""
        return (self.target_df[TranscriptModelFields.SPEAKERS].str.len()).astype((pd.Int64Dtype()))

    def _segments(self) -> Union[pd.Series, pd.DataFrame]:
        """Populates segments.

        When IV directly send us transcripts, turnConstructionUnits
        might not be present. This case is handled.
        """
        if self.source_frame.loc[:, IVSourceColumns.TURN_CONSTRUCTION_UNITS].notnull().any():
            return np.vectorize(fetch_segments)(
                tcus=self.source_frame.loc[:, IVSourceColumns.TURN_CONSTRUCTION_UNITS],
                target_language=self.source_frame.loc[:, IVSourceColumns.TARGET_LANGUAGE],
            )

    def _sentiment(self) -> Union[pd.Series, pd.DataFrame]:
        """Populates sentiment."""
        if self.source_frame.loc[:, IVSourceColumns.TURN_CONSTRUCTION_UNITS].notnull().any():
            return np.vectorize(fetch_sentiment)(
                tcus=self.source_frame.loc[:, IVSourceColumns.TURN_CONSTRUCTION_UNITS]
            )

    def _speakers(self) -> Union[pd.Series, pd.DataFrame]:
        """Populates speakers."""
        return np.vectorize(self._fetch_speakers)(
            tokens=self.target_df.loc[:, TranscriptModelFields.TOKENS]
        )

    def _summary(self):
        """Populates summary."""
        return self.source_frame.loc[:, IVSourceColumns.SUMMARY]

    def _tokens(self) -> Union[pd.Series, pd.DataFrame]:
        """Populates tokens."""
        return np.vectorize(self._fetch_tokens)(
            srt_list=self.source_frame.loc[:, IVSourceColumns.SRTS]
        )

    def _topics(self) -> Union[pd.Series, pd.DataFrame]:
        """Populates tokens."""
        return np.vectorize(self._fetch_topics)(tags=self.source_frame.loc[:, IVSourceColumns.TAGS])

    @staticmethod
    def _fetch_tokens(srt_list: List[dict]) -> List[dict]:
        """
        :param srt_list: List[dict]
        :return: List[dict]
        Fetches all tokens in the transcript as a List of dict
        """
        tokens = []
        for index, data in enumerate(srt_list):
            token_occurrences = {}
            tkn = data.get(IVSourceSRT.WORD)
            if tkn in token_occurrences:
                token_occurrences[tkn] += 1
            else:
                token_occurrences[tkn] = 1
            token = {
                TranscriptModelFields.TOKEN: tkn,
                TranscriptModelFields.TOKEN_TYPE: TokenTypeEnum.WORD.value,
                TranscriptionFields.TIME: {
                    TranscriptionFields.START: data.get(IVSourceSRT.TIMESTAMP),
                    TranscriptionFields.END: data.get(IVSourceSRT.TIMESTAMP)
                    + data.get(IVSourceSRT.LENGTH),
                    TranscriptModelFields.DURATION: data.get(IVSourceSRT.LENGTH),
                },
                TranscriptModelFields.IS_ANNOTATED: False,
                TranscriptModelFields.INDEX: index,
                TranscriptModelFields.OCCURRENCE: token_occurrences[tkn],
                TranscriptModelFields.CONFIDENCE: data.get(IVSourceSRT.SCORE),
                TranscriptModelFields.SPEAKER_ID: data.get(IVSourceSRT.SPEAKER, {}).get(
                    IVSourceSRTSpeaker.NO
                ),
                TranscriptModelFields.SPEAKER: data.get(IVSourceSRT.SPEAKER, {}).get(
                    IVSourceSRTSpeaker.NAME
                ),
                TranscriptModelFields.SPEAKER_USER_ID: data.get(IVSourceSRT.SPEAKER, {}).get(
                    IVSourceSRTSpeaker.ID
                ),
                TranscriptModelFields.LANGUAGE: [
                    {TranscriptModelFields.CODE: data.get(IVSourceSRT.LANGUAGE)}
                ],
            }
            tokens.append(token)
        return tokens

    @staticmethod
    def _fetch_speakers(tokens: List[dict]) -> List[dict]:
        """
        :param tokens: dict
        :return: List[dict]
        Fetches speakers data as a list of dict containing "id" and "percentage" fields
        """
        speakers = []
        if not tokens:
            return speakers

        tokens_df = pd.DataFrame(tokens)
        for col in [TranscriptionFields.TIME, TranscriptModelFields.SPEAKER_ID]:
            if col not in tokens_df.columns:
                tokens_df[col] = pd.NA
        tokens_df[TranscriptModelFields.DURATION] = tokens_df[TranscriptionFields.TIME].str.get(
            TranscriptModelFields.DURATION
        )
        total_duration = tokens_df[TranscriptModelFields.DURATION].sum()
        speaker_duration: pd.Series = tokens_df.groupby([TranscriptModelFields.SPEAKER_ID])[
            TranscriptModelFields.DURATION
        ].sum()
        speaker_percentage: pd.Series = (
            round((speaker_duration / total_duration) * 100) if total_duration > 0 else pd.Series()
        )
        all_speakers = tokens_df[TranscriptModelFields.SPEAKER_ID].tolist()
        for speaker_id in set(all_speakers):
            speaker_dict = {
                TranscriptModelFields.ID: speaker_id,
                TranscriptModelFields.PERCENTAGE: speaker_percentage.get(speaker_id, pd.NA),
            }
            speakers.append(speaker_dict)
        return speakers

    @staticmethod
    def _fetch_topics(tags: List[dict]) -> Optional[List[dict]]:
        """Returns topics List."""
        if not tags:
            return None
        topics = []
        for _, data in enumerate(tags):
            position = (
                data.get(IVSourceTags.POSITION)[0]
                if isinstance(data.get(IVSourceTags.POSITION), list)
                else data.get(IVSourceTags.POSITION, {})
            )
            tag = {
                TranscriptModelFields.VALUE: data.get(IVSourceTags.TAG),
                TranscriptionFields.TIME: {
                    TranscriptionFields.START: position.get(IVSourceTags.TIMESTAMP),
                    TranscriptionFields.END: position.get(IVSourceTags.TIMESTAMP)
                    + position.get(IVSourceTags.OFFSET),
                    TranscriptModelFields.DURATION: position.get(IVSourceTags.OFFSET),
                },
            }
            topics.append(tag)
        return topics

    @staticmethod
    def _fetch_languages(srts: List[dict]) -> Optional[List[dict]]:
        """Returns a list of dict with each like {'code': 'ENGLISH',
        'percentage': 77.75'}.

        Logic: First we calculate the weighted duration(length) by dividing the duration by sum of
        all durations. We then groupby languages and sum it.

        NOTE: The 'language' field is generated in IvGetTranscriptionResults, and is
        not available in the source IV transcript.
        If the language field is not present, None is returned
        """
        srt_df = pd.DataFrame(srts)
        if any(
            column not in srt_df.columns
            for column in (TranscriptionFields.LANGUAGE, IVSourceSRT.LENGTH)
        ):
            return
        srt_df[TempCols.WEIGHTED_DURATION] = (
            srt_df[IVSourceSRT.LENGTH] / srt_df[IVSourceSRT.LENGTH].sum()
        ) * 100
        group_by_languages_sum: pd.Series = srt_df.groupby(TranscriptModelFields.LANGUAGE)[
            TempCols.WEIGHTED_DURATION
        ].sum()
        return [
            {TranscriptModelFields.CODE: index, TranscriptModelFields.PERCENTAGE: value}
            for index, value in group_by_languages_sum.items()
        ]

    @staticmethod
    def _fetch_model_language(languages: Optional[List[dict]]) -> Optional[str]:
        """Returns the language(code) with the highest percentage value and
        maps it to IV_MODEL_LANG_MAPPING.

        Assumes _languages() has run before this
        """
        languages_df = pd.DataFrame(languages)
        if any(
            column not in languages_df.columns
            for column in (TranscriptModelFields.PERCENTAGE, TranscriptModelFields.CODE)
        ):
            return
        lang = languages_df.loc[languages_df[TranscriptModelFields.PERCENTAGE].idxmax()][
            TranscriptModelFields.CODE
        ]
        return IV_MODEL_LANG_MAPPING.get(lang)

    @staticmethod
    def _fetch_confidence(tokens: List[dict]) -> Optional[float]:
        """
        :param tokens: List[dict]
        :return: Optional[float]
        Fetches overall confidence of the transcript by aggregating the tokens confidence
        """
        overall_confidence = None
        if not tokens:
            return overall_confidence
        tokens_df = pd.DataFrame(tokens)
        if TranscriptionFields.CONFIDENCE in tokens_df.columns:
            overall_confidence = tokens_df[TranscriptionFields.CONFIDENCE].sum() / len(tokens_df)
        return overall_confidence

    def _translations(self) -> Union[pd.Series, pd.DataFrame]:
        """Populates translations."""
        return np.vectorize(self._fetch_translation)(
            translated_text=self.source_frame.loc[:, IVSourceColumns.TRANSLATED_TEXT],
            target_language=self.source_frame.loc[:, IVSourceColumns.TARGET_LANGUAGE],
            transcript_source_key=self.source_frame.loc[
                :, TranscriptModelFields.TRANSCRIPT_SOURCE_KEY
            ],
        )

    @staticmethod
    def _fetch_translation(
        translated_text: str, target_language: str, transcript_source_key: str
    ) -> Optional[List[dict]]:
        """Gets the translation in the required format.

        :param translated_text: Translated text
        :param target_language: Translated language
        :param transcript_source_key: Transcript source key, used as the translation ID
        :return: Translation in the required format
        """
        if pd.isna(translated_text):
            return
        return [
            {
                TranscriptModelFields.ID: transcript_source_key,
                TranscriptModelFields.MODEL: {
                    TranscriptModelFields.VENDOR: VendorEnum.AWS.value,
                    TranscriptModelFields.TARGET_LANGUAGE: target_language,
                    TranscriptModelFields.TEXT: translated_text,
                },
            }
        ]

    def _tokens_token_type(self):
        """Not Implemented."""

    def _tokens_time_start(self):
        """Not Implemented."""

    def _tokens_time_end(self):
        """Not Implemented."""

    def _tokens_time_duration(self):
        """Not Implemented."""

    def _tokens_token(self):
        """Not Implemented."""

    def _tokens_is_annotated(self):
        """Not Implemented."""

    def _tokens_index(self):
        """Not Implemented."""

    def _tokens_occurence(self):
        """Not Implemented."""

    def _tokens_confidence(self):
        """Not Implemented."""

    def _tokens_speaker_id(self):
        """Not Implemented."""

    def _tokens_language_code(self):
        """Not Implemented."""

    def _translations_id(self):
        """Not Implemented."""

    def _translations_model_vendor(self):
        """Not Implemented."""

    def _translations_model_target_language(self):
        """Not Implemented."""

    def _translations_model_text(self):
        """Not Implemented."""

    def _speakers_id(self):
        """Not Implemented."""

    def _speakers_percentage(self):
        """Not Implemented."""
