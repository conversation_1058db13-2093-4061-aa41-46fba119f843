import logging
import pandas as pd
from aries_se_comms_tasks.transcription.intelligent_voice.static import (
    INTELLIGENT_VOICE_LANGUAGE_TO_SCHEMA_LANGUAGE_MAPPING,
    IntelligentVoiceSingleLanguageEnum,
    IntelligentVoiceTranscriptFields,
    IVSourceColumns,
)
from aries_se_comms_tasks.utilities.translation import translate_text
from aries_se_core_tasks.core.integration_task import IntegrationTask
from se_elastic_schema.static.transcript import LanguageCodeEnum, TranslationVendorEnum
from typing import List, Optional, Tuple

logger = logging.getLogger(__name__)


class LanguageModelFields:
    ID = "id"
    MODEL_FULL_NAME = "modelFullName"
    WEIGHT = "weight"


class TempColumns:
    COMBINED_ALL_TEXT = "__combined_all_text__"


class IvDirectPreprocessor(IntegrationTask):
    """There is a difference in the input to IvTransformTranscription when we
    get the transcript file directly from IV and when we hit the APIs and
    produce the output file in IvGetTranscription.

    This task bridges the gap for the case where we get transcript files from
    IV by performing tasks which were otherwise being done in IvGetTranscription.

    If the param transcript_files_ftp is False or None, this task just returns
    the source_frame

    For cases where the files are FTP'd, the task does the following:

    1. Translation of all the text using the parameters translation_language and
    translation_enabled
    2. Get model names and model languages from the language_models field
    3. Add languages to each word in SRTs
    4. Add the processing start and end time
    """

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        # Dictionary which holds a mapping from model id to model name
        self.model_dict = {}
        self.id_to_language_dict = {}

    def _run(
        self,
        source_frame: pd.DataFrame,
        transcript_files_ftp: bool,
        translation_to_be_done: bool,
        translation_provider: Optional[TranslationVendorEnum],
        translation_source_language: Optional[LanguageCodeEnum],
        translation_target_language: Optional[LanguageCodeEnum],
    ) -> pd.DataFrame:
        if source_frame.empty:
            return source_frame

        # If the transcript files are not ftp'd, we have already gone through
        # iv_get_transcription, and this task doesn't need to do anything
        if not transcript_files_ftp:
            return source_frame

        target_df = source_frame.copy()

        target_df.loc[:, IVSourceColumns.TRANSCRIPT_ITEM_ID] = target_df.loc[
            :, IVSourceColumns.DOC_ID
        ]

        language_models_not_null_mask = target_df.loc[
            :, IVSourceColumns.LANGUAGE_MODELS_IV_DIRECT
        ].notnull()

        target_df[IVSourceColumns.MODEL_NAMES] = target_df[IVSourceColumns.MODEL_NAMES].astype(
            "object"
        )
        target_df.loc[language_models_not_null_mask, IVSourceColumns.MODEL_NAMES] = target_df.loc[
            language_models_not_null_mask, IVSourceColumns.LANGUAGE_MODELS_IV_DIRECT
        ].apply(lambda x: self._process_language_models(language_models=x))

        # Now, self.model_dict contains ALL possible model id: model name pairs.
        # Convert the model names into languages and add to self.id_to_language_dict[

        for model_id, model_name in self.model_dict.items():
            lang = IntelligentVoiceSingleLanguageEnum.get_lang_from_model_name_ignore_suffix(
                model_name=model_name
            )
            schema_lang = INTELLIGENT_VOICE_LANGUAGE_TO_SCHEMA_LANGUAGE_MAPPING.get(lang)
            self.id_to_language_dict[model_id] = schema_lang

        target_df[IVSourceColumns.SRTS], target_df[IVSourceColumns.SOURCE_AUDIO_LANGUAGES] = zip(
            *target_df.loc[:, IVSourceColumns.SRTS].apply(lambda x: self._process_srts(srts=x))
        )

        # If translation_to_be_done is passed in the kwargs, the source frame column
        # IVSourceColumns.ALL_TEXT is translated in this task. IVSourceColumns.TRANSLATED_TEXT,
        # if already populated, will be overwritten in this case

        if translation_to_be_done and translation_target_language:
            target_df = self._get_translation(
                target_df=target_df,
                translation_provider=translation_provider,
                translation_source_language=translation_source_language,
                translation_target_language=translation_target_language,
            )
        else:
            logger.warning(
                f"Translation will not be done because "
                f"translation_to_be_done = {translation_to_be_done} and"
                f" translation_target_language= {translation_target_language}"
            )

        target_df.loc[:, IVSourceColumns.PROCESSING_END_TIME] = target_df.loc[
            :, IVSourceColumns.PROCESSING_END_TIME_IV_DIRECT
        ]
        target_df.loc[:, IVSourceColumns.PROCESSING_START_TIME] = target_df.loc[
            :, IVSourceColumns.PROCESSING_START_TIME_IV_DIRECT
        ]
        return target_df.fillna(pd.NA)

    def _process_srts(self, srts: List[dict]) -> Tuple[list, list]:
        """Adds languages to each SRT dictionary if there is a match between
        the processing model and self.id_to_language_dict.

        As a side effect, it also updates the set self.unique_languages
        """
        unique_languages = set()
        updated_srts = []
        for item in srts:
            model_id = item[IntelligentVoiceTranscriptFields.PROCESSING_MODELS][0]

            # Get language from model_id
            language = self.id_to_language_dict.get(model_id)
            if language is not None:
                item[IntelligentVoiceTranscriptFields.LANGUAGE] = language
                unique_languages.add(language)
            updated_srts.append(item)

        return updated_srts, list(unique_languages)

    def _process_language_models(self, language_models: List[dict]) -> List[str]:
        """
        This function does the following:
        1. Extracts a list of language models and returns it in a list
        2. SIDE EFFECT: As a side effect, it updates self.model_dict. For
        each model ID, it adds the language obtained in step 2 as a value

        language_models is of the form:
        "language_models":
        [
        {
            "id": 19,
            "modelFullName": "IntelligentVoice_x-languageid_16kHz_3_en.ja.fr_V4.1_NASRv2",
            "weight": 0.8
        },
        {
            "id": 18,
            "modelFullName": "IntelligentVoice_fr_16kHz_1024_general_V1_NASRv3.1",
            "weight": 0.2
        },
        {
            "id": 17,
            "modelFullName": "IntelligentVoice_en-001_16kHz_128_general_V0_NASRv4",
            "weight": 1.0
        },
        {
            "id": 13,
            "modelFullName": "IntelligentVoice_ja_16kHz_60000_daiwa_V2_NASRv2",
            "weight": 1.0
        }
        ]

        """
        model_names = []
        for model_ in language_models:
            id_ = model_.get(LanguageModelFields.ID)
            model_name = model_.get(LanguageModelFields.MODEL_FULL_NAME)
            self.model_dict[id_] = model_name
            model_names.append(model_name)
        return model_names  # type: ignore[return-value]

    def _get_translation(
        self,
        target_df: pd.DataFrame,
        translation_provider: Optional[TranslationVendorEnum],
        translation_source_language: Optional[LanguageCodeEnum],
        translation_target_language: Optional[LanguageCodeEnum],
    ):
        """This function does translation using either the TCUs or the allText/
        allTextPerSpeaker. If TCUs are present, it goes through each TCU and
        populates the translatedTCU inside the tcu dict. It then combines all
        the TCU translations to get an overall 'translatedText'.

        If TCUs are not present, it translates allTextPerSpeaker/allText
        to get the translatedText
        :param target_df: Df containing all source and target cols
        :param translation_provider: Translation provider
        :param translation_source_language: Translation source language
        :param translation_target_language: Translation target language
        :return Translated text as a string or None.
        :mutates the dictionary tcu with the translated tcu text
        """

        # IVSourceColumns.TURN_CONSTRUCTION_UNITS is [] if there are no TCUs
        tcus_present_mask = target_df.loc[:, IVSourceColumns.TURN_CONSTRUCTION_UNITS].notnull() & (
            target_df.loc[:, IVSourceColumns.TURN_CONSTRUCTION_UNITS].str.len() > 0
        )

        target_df.loc[
            tcus_present_mask,
            [IVSourceColumns.TURN_CONSTRUCTION_UNITS, IVSourceColumns.TRANSLATED_TEXT],
        ] = target_df.loc[tcus_present_mask].apply(
            lambda row: self._translate_tcus_list(
                tcus_list=row[IVSourceColumns.TURN_CONSTRUCTION_UNITS],
                translation_provider=translation_provider,
                translation_source_language=translation_source_language,
                translation_target_language=translation_target_language,
                source_languages_from_srts=row[IVSourceColumns.SOURCE_AUDIO_LANGUAGES],
            ),
            axis=1,
        )

        # If all rows have TCUs, there's no need to proceed further
        if tcus_present_mask.all():
            return target_df

        target_df.loc[:, TempColumns.COMBINED_ALL_TEXT] = target_df[
            IVSourceColumns.ALL_TEXT_PER_SPEAKER
        ].fillna(target_df.loc[:, IVSourceColumns.ALL_TEXT])

        combined_all_text_notnull_mask = target_df.loc[:, TempColumns.COMBINED_ALL_TEXT].notnull()
        target_df.loc[
            combined_all_text_notnull_mask & ~tcus_present_mask, IVSourceColumns.TRANSLATED_TEXT
        ] = target_df.loc[
            combined_all_text_notnull_mask & ~tcus_present_mask, TempColumns.COMBINED_ALL_TEXT
        ].apply(
            lambda text: translate_text(
                text=text,
                translation_provider=translation_provider,
                translation_target_language=translation_target_language,
                translation_source_language=translation_source_language,
            )
        )
        target_df.loc[:, IVSourceColumns.TARGET_LANGUAGE] = translation_target_language
        target_df = target_df.drop(columns=[TempColumns.COMBINED_ALL_TEXT])
        return target_df

    @staticmethod
    def _translate_tcu(
        tcu: dict,
        translation_provider: TranslationVendorEnum,
        translation_source_language: Optional[LanguageCodeEnum],
        translation_target_language: Optional[LanguageCodeEnum],
    ) -> str | None:
        """For a single TCU, this function gets the translation of the TCU
        text, and MUTATES the tcu by adding the translated text in a key
        IntelligentVoiceTranscriptFields.TRANSLATED_TCU.

        It also returns the translated text as a string, which the calling function can
        use to combine all the translated strings into a single string

        :param tcu: Turn construction unit dictionary
        :param translation_provider: Translation provider
        :param translation_source_language: Translation source language
        :param translation_target_language: Translation target language
        :returns Translated text as a string or None.
        :mutates the dictionary tcu with the translated tcu text
        """
        tcu_text = tcu.get("text")
        if not tcu_text:
            logger.info(f"TCU {tcu.get('id')} has no text to translate")
            return None

        try:
            translated_tcu = translate_text(
                text=tcu_text,
                translation_provider=translation_provider,
                translation_source_language=translation_source_language,
                translation_target_language=translation_target_language,
            )
            logger.info(f"Translation successful for TCU {tcu.get('id')}")
            tcu[IntelligentVoiceTranscriptFields.TRANSLATED_TCU] = translated_tcu
            return translated_tcu

        except Exception as e:
            message = f"An error occurred during translation the TCU {tcu.get('id')}: {e}"
            logger.warning(message)
            return None

    def _translate_tcus_list(
        self,
        tcus_list: List[dict],
        translation_provider: TranslationVendorEnum,
        translation_source_language: Optional[LanguageCodeEnum],
        translation_target_language: Optional[LanguageCodeEnum],
        source_languages_from_srts: LanguageCodeEnum,
    ) -> pd.Series:
        """
        This function takes as input a list of Turn construction
        unit dictionaries. It iterates through each tcu dict
        in tcus_list and sends it to _translate_tcu(), where the
        tcu is mutated with the translated tcu text. It also
        gets back the translated tcu text, combines it with all
        the other translated tcu texts in tcus_list and returns
        2 things:
        1. A list of mutated tcu dictionaries (with translated tcu
        text added.
        2. A consolidated translated_text field containing all the tcus'
        translated text

        :param tcus_list: Turn construction unit dictionary
        :param translation_provider: Translation provider
        :param translation_source_language: Translation source language
        :param translation_target_language: Translation target language
        :param source_languages_from_srts: Source languages obtained by going
        through all the srts (tokens).
        :returns Translated text as a string or None.
        :mutates the dictionary tcu with the translated tcu text
        """
        translated_text_list = []
        updated_tcus_list = []

        # If we have a single source language for all the SRTs, we can
        # use this as the source language for the translation.
        # If there are > 1 languages from SRTs, the source language is
        # translation_source_languages or None
        # Note that if translation_source_language is provided, this  takes
        # precedence
        if len(source_languages_from_srts) == 1 and not translation_source_language:
            translation_source_language = source_languages_from_srts[0]

        for tcu in tcus_list:
            # Gets a mutated version of TCU with the translated TCU text
            # as well as the translated tcu text as a string
            translated_tcu_text = self._translate_tcu(
                tcu=tcu,
                translation_provider=translation_provider,
                translation_source_language=translation_source_language,
                translation_target_language=translation_target_language,
            )
            if translated_tcu_text:
                translated_text_list.append(translated_tcu_text)
            updated_tcus_list.append(tcu)

        return pd.Series(
            {
                IVSourceColumns.TURN_CONSTRUCTION_UNITS: updated_tcus_list,
                IVSourceColumns.TRANSLATED_TEXT: ". ".join(translated_text_list),
            }
        )


def run_iv_direct_preprocessor(
    source_frame: pd.DataFrame,
    transcript_files_ftp: bool = False,
    translation_to_be_done: bool = False,
    translation_provider: Optional[TranslationVendorEnum] = None,
    translation_source_language: Optional[LanguageCodeEnum] = None,
    translation_target_language: Optional[LanguageCodeEnum] = None,
    app_metrics_path: Optional[str] = None,
    audit_path: Optional[str] = None,
) -> pd.DataFrame:
    task = IvDirectPreprocessor(
        app_metrics_path=app_metrics_path,
        audit_path=audit_path,
    )
    return task.run(  # type: ignore[no-any-return]
        source_frame=source_frame,
        transcript_files_ftp=transcript_files_ftp,
        translation_to_be_done=translation_to_be_done,
        translation_provider=translation_provider,
        translation_source_language=translation_source_language,
        translation_target_language=translation_target_language,
    )
