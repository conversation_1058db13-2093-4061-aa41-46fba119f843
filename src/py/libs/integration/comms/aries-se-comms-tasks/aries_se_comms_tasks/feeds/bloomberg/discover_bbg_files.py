import logging
from aries_se_core_tasks.core.integration_task import IntegrationTask
from aries_se_core_tasks.utilities.cloud import s3_list_files
from aries_se_core_tasks.utilities.s3_utils import get_bucket_and_key_from_file_url
from cloud_utils.cloud import cloud_list_files
from pathlib import Path
from se_data_lake.cloud_utils import get_cloud_provider_from_file_uri, get_cloud_provider_prefix
from se_enums.cloud import CloudProviderEnum
from typing import List, Optional, Tuple

logger_ = logging.getLogger(__name__)


class DiscoverBbgFiles(IntegrationTask):
    """Returns email, chat and attachment files based on the input file_url For
    chat files: Checks in `bloomberg_ib19_attachments` in the same leve where
    the input xml file is stored for Chat Attachments if the input file in a
    Chat File For email files: Checks in `bloomberg_msg_attachments_` in the
    same leve where the input xml file is stored for Chat Attachments if the
    input file in a Chat File.

    Any file which doesn't match the expected pattern for chats or
    emails aren't processed and is returned as invalid files.
    """

    def _run(self, file_url: str) -> Tuple[Optional[str], Optional[str], List[str], List[str]]:
        return self.process(file_url=file_url)

    @staticmethod
    def process(file_url: str) -> Tuple[Optional[str], Optional[str], List[str], List[str]]:
        chat_file = None
        mail_file = None
        invalid_files = []
        # get the directory from file url in event
        file_name = Path(file_url).name
        bucket, key = get_bucket_and_key_from_file_url(file_url)
        attachment_dir_suffix = key.split(".")[-2]
        attachment_dir = key.rsplit("/", 1)[0]

        if "ib19" in file_name:
            chat_file = file_url
            attachment_dir = f"{attachment_dir}/bloomberg_ib19_attachments_{attachment_dir_suffix}/"
        elif "msg" in file_name:
            mail_file = file_url
            attachment_dir = f"{attachment_dir}/bloomberg_msg_attachments_{attachment_dir_suffix}/"
        else:
            logger_.error(f"Unknown file type: {file_url}")
            invalid_files.append(file_url)
        logger_.info(f"Finding attachments in directory {attachment_dir}")

        cloud_provider = get_cloud_provider_from_file_uri(file_uri=file_url)
        cloud_provider_prefix = get_cloud_provider_prefix(value=cloud_provider)

        # Keeping S3 specific implementation because it should have better performance
        if cloud_provider == CloudProviderEnum.AWS:
            cloud_objects = s3_list_files(bucket=bucket, folder_path=attachment_dir, logger=logger_)
        else:
            cloud_objects = cloud_list_files(
                cloud_provider=cloud_provider,
                bucket=bucket,
                folder_path=attachment_dir,
                return_absolute_path=False,
                return_files_only=True,
            )

        attachment_files = [
            f"{cloud_provider_prefix}{Path(bucket, attachment_dir, relative_file_path).as_posix()}"
            for relative_file_path in cloud_objects
        ]
        logger_.info(
            f"Found Mail file - {mail_file}, "
            f"chat file {chat_file} and "
            f"attachment files - {attachment_files}"
        )
        return mail_file, chat_file, attachment_files, invalid_files


def run_discover_bbg_files(
    file_url: str,
    app_metrics_path: Optional[str] = None,
    audit_path: Optional[str] = None,
):
    task = DiscoverBbgFiles(app_metrics_path=app_metrics_path, audit_path=audit_path)

    return task.run(file_url=file_url)
