# type: ignore
import logging
import pandas as pd
from aries_se_comms_tasks.abstractions.transformations.abstract_voice_transformations import (
    AbstractVoiceTransformations,
)
from aries_se_comms_tasks.feeds.voice.a1_voice.static import (
    A1_VOICE_FLOW_NAME,
    A1SourceColumnsMappings,
    A1TempColumns,
    FileUrlColumns,
)
from aries_se_comms_tasks.generic.participants.participant_identifiers import (
    Params as ParamsParticipantIdentifiers,
)
from aries_se_comms_tasks.generic.participants.participant_identifiers import (
    run_participant_identifiers,
)
from aries_se_comms_tasks.voice.static import WAVEFORM_FILE_NAME, WAVEFORM_FILE_PREFIX, CallColumns
from aries_se_core_tasks.datetime.convert_datetime import run_convert_datetime
from aries_se_core_tasks.datetime.increment_datetime import Params as ParamsIncrementDatetime
from aries_se_core_tasks.datetime.increment_datetime import run_increment_datetime
from aries_se_core_tasks.transform.map.map_attribute import run_map_attribute
from aries_se_core_tasks.utilities.frame_manipulation import add_missing_columns
from se_core_tasks.datetime.convert_datetime import ConvertTo
from se_core_tasks.datetime.convert_datetime import Params as ConvertDatetimeParams
from se_core_tasks.map.map_attribute import CastTo
from se_core_tasks.map.map_attribute import Params as MapAttributeParams
from se_core_tasks.utils.datetime import DatetimeFormat
from se_data_lake.cloud_utils import get_bucket, get_key
from typing import List

logger = logging.getLogger(__name__)


class A1VoiceMappings(AbstractVoiceTransformations):
    """Transformation for A1 calls."""

    def __init__(
        self,
        source_frame: pd.DataFrame,
        realm: str,
        converted_uploaded_file_uri: List[str],
        source_file_url: str,
        **kwargs,
    ):
        super().__init__(source_frame=source_frame, logger=logger, realm=realm)
        self.realm = realm
        self.converted_uploaded_file_uri = converted_uploaded_file_uri
        self.source_file_url = source_file_url

    def _pre_process(self):
        self.pre_process_df: pd.DataFrame = pd.DataFrame(index=self.source_frame.index)
        self.target_df: pd.DataFrame = pd.DataFrame(index=self.source_frame.index)
        self.post_process_df = pd.DataFrame(index=self.source_frame.index)

        self.source_frame: pd.DataFrame = add_missing_columns(
            dataframe=self.source_frame, columns=A1SourceColumnsMappings().all()
        )

        self.pre_process_df = pd.concat(
            [self.pre_process_df, self._call_participant_identifiers()],
            axis=1,
        )

    def process(self):
        self.pre_process()

        # Generic fields
        self.call_duration()
        self.id()
        self.internal()

        # Identifiers
        self.identifiers_all_country_codes()
        self.identifiers_all_ids()
        self.identifiers_from_id()
        self.identifiers_from_user_id()
        self.identifiers_to_ids()
        self.identifiers_to_user_id()

        # Metadata
        self.metadata_source_client()
        self.metadata_source_file_info_location_bucket()
        self.metadata_source_file_info_location_key()
        self.source_index()
        self.source_key()

        # Timestamps
        self.timestamps_local_timestamp_start()
        self.timestamps_local_timestamp_end()
        self.timestamps_timestamp_start()
        self.timestamps_timestamp_connected()
        self.timestamps_timestamp_end()

        # Waveform
        self.waveform_file_info_location_bucket()
        self.waveform_file_info_location_key()
        self.post_process()

        return self.target_df

    def _post_process(self):
        """Populates the derived attachment key in a temp column, this temp
        column needs to be dropped downstream."""
        self.target_df.loc[:, A1TempColumns.ATTACHMENT_PATH] = self.converted_uploaded_file_uri

    def _call_duration(self) -> pd.Series:
        """Populates CallColumns.CALL_DURATION in target_df from
        A1SourceColumnsMappings.DURATION."""
        return run_convert_datetime(
            source_frame=self.source_frame,
            params=ConvertDatetimeParams(
                source_attribute=A1SourceColumnsMappings.DURATION,
                source_unit="s",
                target_datetime_format="%H:%M:%S",
                target_attribute=CallColumns.CALL_DURATION,
            ),
            skip_serializer=True,
        ).loc[:, CallColumns.CALL_DURATION]

    def _id(self) -> pd.Series:
        """Populates CallColumns.ID in target_df from
        A1SourceColumnsMappings.ID."""
        return pd.Series(
            data=self.source_frame.loc[:, A1SourceColumnsMappings.ID],
            index=self.source_frame.index,
            name=CallColumns.ID,
        )

    def _identifiers_all_country_codes(self) -> pd.Series:
        """Populate CallColumns.IDENTIFIERS_ALL_COUNTRY_CODES."""
        return self.pre_process_df.loc[:, CallColumns.IDENTIFIERS_ALL_COUNTRY_CODES]

    def _identifiers_all_ids(self) -> pd.Series:
        """Returns a series containing CallColumns.IDENTIFIERS_ALL_IDS."""
        return self.pre_process_df.loc[:, CallColumns.IDENTIFIERS_ALL_IDS]

    def _identifiers_from_id(self) -> pd.Series:
        """Populate CallColumns.IDENTIFIERS_FROM_ID."""
        return self.pre_process_df.loc[:, CallColumns.IDENTIFIERS_FROM_ID]

    def _identifiers_from_id_addl_info(self) -> pd.Series:
        """Populates CallColumns.IDENTIFIERS_FROM_ADDL_INFO."""
        return self.pre_process_df.loc[:, CallColumns.IDENTIFIERS_FROM_ADDL_INFO]

    def _identifiers_from_user_id(self) -> pd.Series:
        return self.pre_process_df.loc[:, A1TempColumns.FROM_USER_ID]

    def _identifiers_to_ids(self) -> pd.Series:
        """Populate CallColumns.IDENTIFIERS_TO_IDS."""
        return self.pre_process_df.loc[:, CallColumns.IDENTIFIERS_TO_IDS]

    def _identifiers_to_ids_addl_info(self) -> pd.Series:
        """Populate CallColumns.IDENTIFIERS_TO_ADDL_INFO."""
        return self.pre_process_df.loc[:, CallColumns.IDENTIFIERS_TO_ADDL_INFO]

    def _identifiers_to_user_id(self) -> pd.Series:
        """Populates CallColumns.IDENTIFIERS_TO_USER_ID with the remote party
        entry point."""
        return self.pre_process_df.loc[:, A1TempColumns.TO_USER_ID]

    def _internal(self) -> pd.Series:
        """Populates CallColumns.INTERNAL with True if the direction is not
        'out', False otherwise."""
        return ~self.source_frame.loc[:, A1SourceColumnsMappings.DIRECTION].str.lower().str.match(
            "out"
        )

    def _metadata_source_client(self) -> pd.Series:
        """Creates a series for CallColumns.METADATA_SOURCE_CLIENT with a
        static value."""
        return pd.Series(
            data="AlternitOne Exponential",
            index=self.source_frame.index,
            name=CallColumns.METADATA_SOURCE_CLIENT,
        )

    def _metadata_source_file_info_location_bucket(self) -> pd.Series:
        """Populates CallColumns.METADATA_SOURCE_FILE_INFO_LOCATION_BUCKET."""
        return self.source_frame.loc[:, FileUrlColumns.XML_FILE_URL].apply(
            lambda x: get_bucket(file_uri=x)
        )

    def _metadata_source_file_info_location_key(self) -> pd.Series:
        """Populates CallColumns.METADATA_SOURCE_FILE_INFO_LOCATION_KEY."""
        return self.source_frame.loc[:, FileUrlColumns.XML_FILE_URL].apply(
            lambda x: get_key(file_uri=x)
        )

    def _source_index(self) -> pd.Series:
        """Returns a Series containing the sourceIndex column."""
        return pd.Series(
            data=self.source_frame.index.values,
            index=self.source_frame.index,
            name=CallColumns.SOURCE_INDEX,
        )

    def _source_key(self) -> pd.Series:
        """Returns a data frame containing the sourceKey column from
        FileUrlColumns.METADATA_FILE_URL."""
        return pd.Series(
            data=self.source_file_url,
            index=self.source_frame.index,
            name=CallColumns.SOURCE_KEY,
        )

    def _timestamps_local_timestamp_start(self) -> pd.Series:
        """Populates a series for CallColumns.TIMESTAMPS_LOCAL_TIMESTAMP_START
        from A1SourceColumnsMappings.TIMESTAMP."""
        return run_convert_datetime(
            source_frame=self.source_frame,
            params=ConvertDatetimeParams(
                source_attribute=A1SourceColumnsMappings.TIMESTAMP,
                target_attribute=CallColumns.TIMESTAMPS_LOCAL_TIMESTAMP_START,
                convert_to=ConvertTo.DATETIME.value,
            ),
            skip_serializer=True,
        )[CallColumns.TIMESTAMPS_LOCAL_TIMESTAMP_START]

    def _timestamps_local_timestamp_end(self) -> pd.Series:
        """Populates a series for CallColumns.TIMESTAMPS_LOCAL_TIMESTAMP_END
        from A1SourceColumnsMappings.TIMESTAMP and DURATION."""
        return run_increment_datetime(
            source_frame=pd.concat(
                [
                    self.source_frame.loc[:, A1SourceColumnsMappings.DURATION],
                    self.target_df.loc[:, CallColumns.TIMESTAMPS_LOCAL_TIMESTAMP_START],
                ],
                axis=1,
            ),
            params=ParamsIncrementDatetime(
                source_datetime_attribute=CallColumns.TIMESTAMPS_LOCAL_TIMESTAMP_START,
                source_datetime_format=DatetimeFormat.DATETIME,
                source_incrementor_attribute=A1SourceColumnsMappings.DURATION,
                source_incrementor_unit="s",
                target_attribute=CallColumns.TIMESTAMPS_LOCAL_TIMESTAMP_END,
            ),
            skip_serializer=True,
        ).loc[:, CallColumns.TIMESTAMPS_LOCAL_TIMESTAMP_END]

    def _timestamps_timestamp_connected(self) -> pd.Series:
        """Populates a series for CallColumns.TIMESTAMPS_CONNECTED from
        CallColumns.TIMESTAMPS_LOCAL_TIMESTAMP_START."""
        return self.target_df.loc[:, CallColumns.TIMESTAMPS_LOCAL_TIMESTAMP_START]

    def _timestamps_timestamp_end(self) -> pd.Series:
        """Populates a series for CallColumns.TIMESTAMPS_TIMESTAMP_END from
        CallColumns.TIMESTAMPS_LOCAL_TIMESTAMP_END."""
        return self.target_df.loc[:, CallColumns.TIMESTAMPS_LOCAL_TIMESTAMP_END]

    def _timestamps_timestamp_start(self) -> pd.Series:
        """Populates a series for CallColumns.TIMESTAMPS_TIMESTAMP_START from
        CallColumns.TIMESTAMPS_LOCAL_TIMESTAMP_START."""
        return self.target_df.loc[:, CallColumns.TIMESTAMPS_LOCAL_TIMESTAMP_START]

    def _waveform_file_info_location_bucket(self) -> pd.Series:
        """Populates CallColumns.WAVEFORM_FILE_INFO_LOCATION_BUCKET."""
        return pd.Series(
            data=self.realm,
            index=self.source_frame.index,
            name=CallColumns.WAVEFORM_FILE_INFO_LOCATION_BUCKET,
        )

    def _waveform_file_info_location_key(self) -> pd.Series:
        """Populates CallColumns.WAVEFORM_FILE_INFO_LOCATION_KEY."""
        return (
            WAVEFORM_FILE_PREFIX
            + A1_VOICE_FLOW_NAME
            + "/"
            + self.target_df.loc[:, CallColumns.ID]
            + "/"
            + WAVEFORM_FILE_NAME
        )

    def _call_participant_identifiers(self) -> pd.DataFrame:
        """Calls ParticipantIdentifiers and returns the results in a
        dataframe."""

        # fromUserId -> propagate to self.pre_process_df to map to identifiers.fromUserId
        self.pre_process_df[A1TempColumns.FROM_USER_ID] = pd.DataFrame(
            data=self.source_frame.loc[:, A1SourceColumnsMappings.LOCAL_PARTY],
            index=self.source_frame.index,
        )

        # toUserId -> propagate to self.pre_process_df to map to identifiers.toUserId
        self.pre_process_df[A1TempColumns.TO_USER_ID] = pd.DataFrame(
            data=self.source_frame.loc[:, A1SourceColumnsMappings.REMOTE_PARTY_ENTRY_POINT],
            index=self.source_frame.index,
        )

        participants_df = pd.DataFrame(index=self.source_frame.index)

        # fromId
        participants_df[A1TempColumns.FROM_ID] = pd.DataFrame(
            data=self.source_frame.loc[:, A1SourceColumnsMappings.LOCAL_PARTY],
            index=self.source_frame.index,
        )[A1SourceColumnsMappings.LOCAL_PARTY].str.split("@", expand=True)[0]

        # toIds
        participants_df[A1TempColumns.TO_IDS_LIST] = run_map_attribute(
            source_frame=self.source_frame,
            params=MapAttributeParams(
                source_attribute=A1SourceColumnsMappings.REMOTE_PARTY,
                target_attribute=A1TempColumns.TO_IDS_LIST,
                cast_to=CastTo.STRING_LIST,
                list_delimiter=";",
            ),
            skip_serializer=True,
        )[A1TempColumns.TO_IDS_LIST]

        return run_participant_identifiers(
            source_frame=participants_df,
            params=ParamsParticipantIdentifiers(
                source_from_identifier=A1TempColumns.FROM_ID,
                source_to_identifiers=A1TempColumns.TO_IDS_LIST,
            ),
            skip_serializer=True,
        )

    def _connected(self) -> pd.Series:
        raise NotImplementedError("Not in use.")

    def _fault(self) -> pd.Series:
        raise NotImplementedError("Not in use.")

    def _attachments(self) -> pd.Series:
        raise NotImplementedError("Not in use.")

    def _call_duration_speaking(self) -> pd.Series:
        raise NotImplementedError("Not in use.")

    def _call_type(self) -> pd.Series:
        raise NotImplementedError("Not in use.")

    def _charge(self) -> pd.Series:
        raise NotImplementedError("Not in use.")

    def _conference_call(self) -> pd.Series:
        raise NotImplementedError("Not in use.")

    def _direction(self) -> pd.Series:
        raise NotImplementedError("Not in use.")

    def _has_attachment(self) -> pd.Series:
        raise NotImplementedError("Not in use.")

    def _identifiers_all_domains(self) -> pd.Series:
        raise NotImplementedError("Not in use.")

    def _identifiers_bcc_ids(self) -> pd.Series:
        raise NotImplementedError("Not in use.")

    def _identifiers_cc_ids(self) -> pd.Series:
        raise NotImplementedError("Not in use.")

    def _identifiers_from_device_id(self) -> pd.Series:
        raise NotImplementedError("Not in use.")

    def _identifiers_from_ip(self) -> pd.Series:
        raise NotImplementedError("Not in use.")

    def _identifiers_host_id(self) -> pd.Series:
        raise NotImplementedError("Not in use.")

    def _identifiers_on_behalf_of(self) -> pd.Series:
        raise NotImplementedError("Not in use.")

    def _identifiers_to_device_id(self) -> pd.Series:
        raise NotImplementedError("Not in use.")

    def _identifiers_to_ip(self) -> pd.Series:
        raise NotImplementedError("Not in use.")

    def _is_multi_channel(self) -> pd.Series:
        raise NotImplementedError("Not in use.")

    def _is_dealer_board(self) -> pd.Series:
        raise NotImplementedError("Not in use.")

    def _join_reason(self) -> pd.Series:
        raise NotImplementedError("Not in use.")

    def _meta_model(self) -> pd.Series:
        raise NotImplementedError("Not in use.")

    def _metadata_content_type(self) -> pd.Series:
        raise NotImplementedError("Not in use.")

    def _metadata_encoding_type(self) -> pd.Series:
        raise NotImplementedError("Not in use.")

    def _metadata_header(self) -> pd.Series:
        raise NotImplementedError("Not in use.")

    def _metadata_in_reply_to(self) -> pd.Series:
        raise NotImplementedError("Not in use.")

    def _metadata_message_id(self) -> pd.Series:
        raise NotImplementedError("Not in use.")

    def _metadata_reference_id(self) -> pd.Series:
        raise NotImplementedError("Not in use.")

    def _metadata_size_in_bytes(self) -> pd.Series:
        raise NotImplementedError("Not in use.")

    def _metadata_source_device_type(self) -> pd.Series:
        raise NotImplementedError("Not in use.")

    def _participants(self) -> pd.Series:
        raise NotImplementedError("Not in use.")

    def _rate(self) -> pd.Series:
        raise NotImplementedError("Not in use.")

    def _timestamps_created(self) -> pd.Series:
        raise NotImplementedError("Not in use")

    def _timestamps_duration_unit(self) -> pd.Series:
        raise NotImplementedError("Not in use")

    def _timestamps_duration_value(self) -> pd.Series:
        raise NotImplementedError("Not in use")

    def _transcribed(self) -> pd.Series:
        raise NotImplementedError("Not in use.")

    def _voice_file(self) -> pd.Series:
        raise NotImplementedError("Not in use.")

    def _is_transcription_excluded(self) -> pd.Series:
        raise NotImplementedError("Not in use.")

    def _transcription_exclusion_reason(self) -> pd.Series:
        raise NotImplementedError("Not in use.")
