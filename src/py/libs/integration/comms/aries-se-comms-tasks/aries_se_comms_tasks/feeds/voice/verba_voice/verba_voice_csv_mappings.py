# type: ignore
import pandas as pd
from aries_se_comms_tasks.abstractions.transformations.abstract_voice_transformations import (
    AbstractVoiceTransformations,
)
from aries_se_comms_tasks.feeds.voice.verba_voice.static import VERBA_FLOW_NAME
from aries_se_comms_tasks.generic.participants.link_participants import (
    Params as LinkParticipantsParams,
)
from aries_se_comms_tasks.generic.participants.link_participants import (
    run_link_participants,
)
from aries_se_comms_tasks.generic.participants.participant_identifiers import (
    Params as ParticipantsIdentifiersParams,
)
from aries_se_comms_tasks.generic.participants.participant_identifiers import (
    run_participant_identifiers,
)
from aries_se_comms_tasks.voice.static import WAVEFORM_FILE_NAME, WAVEFORM_FILE_PREFIX, CallColumns
from aries_se_core_tasks.datetime.convert_datetime import (
    Params as ConvertDatetimeParams,
)
from aries_se_core_tasks.datetime.convert_datetime import (
    run_convert_datetime,
)
from aries_se_core_tasks.generic.generate_record_identifiers_for_df import (  # type: ignore[attr-defined] # noqa: E501
    Params as GenerateRecordFileIdentifiersForDfParams,
)
from aries_se_core_tasks.generic.generate_record_identifiers_for_df import (
    run_generate_record_identifiers_for_df,
)
from aries_se_core_tasks.static import MetaModel
from aries_se_core_tasks.transform.map.map_attribute import (
    Params as MapAttributeParams,
)
from aries_se_core_tasks.transform.map.map_attribute import (
    run_map_attribute,
)
from aries_se_core_tasks.transform.map.map_conditional import (
    Params as MapConditionalParams,
)
from aries_se_core_tasks.transform.map.map_conditional import (
    run_map_conditional,
)
from se_core_tasks.datetime.convert_datetime import ConvertTo
from se_core_tasks.map.map_attribute import CastTo
from se_core_tasks.map.map_conditional import Case
from se_core_tasks.utils.frame_manipulation import add_missing_columns
from se_data_lake.cloud_utils import get_bucket, get_bucket_and_key
from se_elastic_schema.models import Call
from se_enums.cloud import CloudProviderEnum


class SourceColumns:
    DURATION = "duration"
    DIRECTION_USER = "direction (user)"
    END_DATETIME = "end datetime"
    FROM = "from"
    FROM_INFO = "from info"
    METADATA_FILE_URL = "metadata_file_url"
    SOURCE_PLATFORM = "source platform"
    USER_DISPLAY_NAME = "user display name"
    TO = "to"
    TO_INFO = "to info"
    START_DATETIME = "start datetime"
    USER_LOGIN_ID = "user login id"
    VERBA_CALL_ID = "verba call id"

    @classmethod
    def all(cls):
        fields = []
        for name, value in vars(cls).items():
            if not (name.startswith("__") or isinstance(value, classmethod)):
                fields.append(value)
        return fields


class TempColumns:
    END_DATETIME = "__end_datetime__"
    FROM_ID = "__from_id__"
    FROM_DEVICE_ID = "__from_device_id__"
    TO_DEVICE_ID = "to_device_id"
    TO_IDS = "__to_ids__"
    START_DATETIME = "__start_datetime__"


class DirectionUserEnum:
    INCOMING = "Incoming"
    OUTGOING = "Outgoing"


SOURCE_DATETIME_FORMAT = "%Y.%m.%d %H:%M:%S.%f"


class VerbaVoiceCsvMappings(AbstractVoiceTransformations):
    def __init__(
        self,
        source_file_uri: str,
        streamed: bool,
        tenant: str,
        cloud_provider: CloudProviderEnum,
        **kwargs,
    ):
        super().__init__(**kwargs)

        self.streamed = streamed
        self.tenant = tenant
        self.source_file_uri = source_file_uri
        self.source_file_bucket = get_bucket(file_uri=self.source_file_uri)
        self.cloud_provider = cloud_provider

    def _pre_process(self):
        # Ensure all required columns exist
        self.source_frame = add_missing_columns(
            dataframe=self.source_frame, dataframe_columns=SourceColumns.all()
        )
        self.pre_process_df: pd.DataFrame
        (
            self.pre_process_df[CallColumns.METADATA_SOURCE_FILE_INFO_LOCATION_BUCKET],
            self.pre_process_df[CallColumns.METADATA_SOURCE_FILE_INFO_LOCATION_KEY],
        ) = zip(*self.source_frame[SourceColumns.METADATA_FILE_URL].apply(get_bucket_and_key))

        self.pre_process_df = pd.concat(
            [self.pre_process_df, self._call_participants_identifiers()],
            axis=1,
        )

        self.pre_process_df = pd.concat(
            [self.pre_process_df, self._call_link_participants()],
            axis=1,
        )

        self.pre_process_df = pd.concat(
            [
                self.pre_process_df,
                run_convert_datetime(
                    source_frame=self.source_frame,
                    params=ConvertDatetimeParams(
                        source_attribute=SourceColumns.START_DATETIME,
                        target_attribute=TempColumns.START_DATETIME,
                        convert_to=ConvertTo.DATETIME,
                        source_attribute_format=SOURCE_DATETIME_FORMAT,
                    ),
                    skip_serializer=True,
                ),
                run_convert_datetime(
                    source_frame=self.source_frame,
                    params=ConvertDatetimeParams(
                        source_attribute=SourceColumns.END_DATETIME,
                        target_attribute=TempColumns.END_DATETIME,
                        convert_to=ConvertTo.DATETIME,
                        source_attribute_format=SOURCE_DATETIME_FORMAT,
                    ),
                    skip_serializer=True,
                ),
            ],
            axis=1,
        )

    def process(self):
        self.pre_process()

        self.call_duration()

        self.id()

        self.join_reason()

        self.metadata_source_client()
        self.metadata_source_file_info_location_bucket()
        self.metadata_source_file_info_location_key()

        self.timestamps_local_timestamp_end()
        self.timestamps_local_timestamp_start()
        self.timestamps_timestamp_connected()
        self.timestamps_timestamp_end()
        self.timestamps_timestamp_start()

        self.identifiers_all_country_codes()
        self.identifiers_all_ids()
        self.identifiers_from_device_id()
        self.identifiers_from_id()
        self.identifiers_from_id_addl_info()
        self.identifiers_to_device_id()
        self.identifiers_to_ids()
        self.identifiers_to_ids_addl_info()

        self.participants()

        self.source_index()
        self.source_key()

        self.waveform_file_info_location_bucket()
        self.waveform_file_info_location_key()

        return self.target_df

    def _call_duration(self) -> pd.Series:
        return self.source_frame.loc[:, SourceColumns.DURATION]

    def _id(self):
        return self.source_frame.loc[:, SourceColumns.VERBA_CALL_ID]

    def _identifiers_all_country_codes(self):
        """Populates CallColumns.IDENTIFIERS_ALL_COUNTRY_CODES column."""
        return self.pre_process_df.loc[:, CallColumns.IDENTIFIERS_ALL_COUNTRY_CODES]

    def _identifiers_all_ids(self) -> pd.Series:
        """Populates CallColumns.IDENTIFIERS_ALL_IDS column."""
        return self.pre_process_df.loc[:, CallColumns.IDENTIFIERS_ALL_IDS]

    def _identifiers_from_device_id(self) -> pd.Series:
        # Note: some of the specs rules where aggregated when possible
        return run_map_conditional(
            source_frame=self.source_frame,
            params=MapConditionalParams(
                target_attribute=CallColumns.IDENTIFIERS_FROM_DEVICE_ID,
                cases=[
                    Case(
                        query=(
                            f"`{SourceColumns.DIRECTION_USER}` == '{DirectionUserEnum.INCOMING}'"
                            f" | ("
                            f"`{SourceColumns.DIRECTION_USER}` == '{DirectionUserEnum.OUTGOING}'"
                            f" & `{SourceColumns.USER_LOGIN_ID}`.isnull()"
                            f")"
                        ),
                        attribute=SourceColumns.FROM_INFO,
                    ),
                    Case(
                        query=(
                            f"`{SourceColumns.DIRECTION_USER}` == '{DirectionUserEnum.OUTGOING}'"
                            f" & `{SourceColumns.USER_LOGIN_ID}`.notnull()"
                        ),
                        attribute=SourceColumns.USER_DISPLAY_NAME,
                    ),
                ],
            ),
            skip_serializer=True,
        ).loc[:, CallColumns.IDENTIFIERS_FROM_DEVICE_ID]

    def _identifiers_from_id(self) -> pd.Series:
        """Populates CallColumns.IDENTIFIERS_FROM_ID field."""
        return self.pre_process_df.loc[:, CallColumns.IDENTIFIERS_FROM_ID]

    def _identifiers_from_id_addl_info(self):
        """Populates CallColumns.IDENTIFIERS_FROM_ID_ADDL_INFO."""
        return self.pre_process_df.loc[:, CallColumns.IDENTIFIERS_FROM_ADDL_INFO]

    def _identifiers_to_device_id(self) -> pd.Series:
        """
        Note: some of the specs rules where aggregated when possible.

        - If (`SourceColumns.DIRECTION_USER` is `DirectionUserEnum.OUTGOING` or
        if `SourceColumns.DIRECTION_USER` is `DirectionUserEnum.INCOMING` and
        `SourceColumns.USER_LOGIN_ID` is null) populate with `SourceColumns.TO`

        - If (`SourceColumns.DIRECTION_USER` is `DirectionUserEnum.INCOMING` and
        `SourceColumns.USER_LOGIN_ID` is not null) populate with `SourceColumns.USER_LOGIN_ID`
        """
        #
        return run_map_conditional(
            source_frame=self.source_frame,
            params=MapConditionalParams(
                target_attribute=CallColumns.IDENTIFIERS_TO_DEVICE_ID,
                cases=[
                    Case(
                        query=(
                            f"`{SourceColumns.DIRECTION_USER}` == '{DirectionUserEnum.OUTGOING}'"
                            f" | ("
                            f"`{SourceColumns.DIRECTION_USER}` == '{DirectionUserEnum.INCOMING}'"
                            f" & `{SourceColumns.USER_LOGIN_ID}`.isnull()"
                            f")"
                        ),
                        attribute=SourceColumns.TO_INFO,
                    ),
                    Case(
                        query=(
                            f"`{SourceColumns.DIRECTION_USER}` == '{DirectionUserEnum.INCOMING}'"
                            f" & `{SourceColumns.USER_LOGIN_ID}`.notnull()"
                        ),
                        attribute=SourceColumns.USER_DISPLAY_NAME,
                    ),
                ],
            ),
            skip_serializer=True,
        ).loc[:, CallColumns.IDENTIFIERS_TO_DEVICE_ID]

    def _identifiers_to_ids(self) -> pd.Series:
        """Populates CallColumns.IDENTIFIERS_TO_IDS."""
        return self.pre_process_df.loc[:, CallColumns.IDENTIFIERS_TO_IDS]

    def _identifiers_to_ids_addl_info(self):
        """Populates CallColumns.IDENTIFIERS_TO_ID_ADDL_INFO."""
        return self.pre_process_df.loc[:, CallColumns.IDENTIFIERS_TO_ADDL_INFO]

    def _participants(self) -> pd.Series:
        return self.pre_process_df.loc[:, CallColumns.PARTICIPANTS]

    def _meta_model(self) -> pd.Series:
        """Not Implemented."""

    def _metadata_source_client(self) -> pd.Series:
        return "Verba - " + self.source_frame.loc[:, SourceColumns.SOURCE_PLATFORM]

    def _source_index(self) -> pd.Series:
        """Returns a data frame containing the sourceIndex column."""
        return pd.Series(
            data=self.source_frame.index.values,
            index=self.source_frame.index,
            name=CallColumns.SOURCE_INDEX,
        )

    def _source_key(self) -> pd.Series:
        return pd.Series(
            data=self.source_file_uri,
            index=self.source_frame.index,
            name=CallColumns.SOURCE_KEY,
        )

    def _timestamps_local_timestamp_end(self) -> pd.Series:
        return self.pre_process_df.loc[:, TempColumns.END_DATETIME]

    def _timestamps_local_timestamp_start(self) -> pd.Series:
        return self.pre_process_df.loc[:, TempColumns.START_DATETIME]

    def _timestamps_timestamp_connected(self) -> pd.Series:
        return self.pre_process_df.loc[:, TempColumns.START_DATETIME]

    def _timestamps_timestamp_end(self) -> pd.Series:
        return self.pre_process_df.loc[:, TempColumns.END_DATETIME]

    def _timestamps_timestamp_start(self) -> pd.Series:
        return self.pre_process_df.loc[:, TempColumns.START_DATETIME]

    def _join_reason(self) -> pd.Series:
        return self.source_frame.loc[:, SourceColumns.DIRECTION_USER]

    def _call_participants_identifiers(self):
        """Calls ParticipantsIdentifiers and returns the results in a data
        frame."""
        ids_df: pd.DataFrame = pd.DataFrame(index=self.source_frame.index)

        is_incoming_direction_user_mask = (
            self.source_frame[SourceColumns.DIRECTION_USER] == DirectionUserEnum.INCOMING
        )

        is_outgoing_direction_user_mask = (
            self.source_frame[SourceColumns.DIRECTION_USER] == DirectionUserEnum.OUTGOING
        )

        user_login_id_not_null_mask = self.source_frame[SourceColumns.USER_LOGIN_ID].notnull()

        # Note: some of the specs rules where aggregated
        # when possible
        ids_df.loc[
            is_incoming_direction_user_mask
            | (is_outgoing_direction_user_mask & ~user_login_id_not_null_mask),
            TempColumns.FROM_ID,
        ] = self.source_frame.loc[:, SourceColumns.FROM]

        ids_df.loc[
            is_outgoing_direction_user_mask & user_login_id_not_null_mask,
            TempColumns.FROM_ID,
        ] = self.source_frame.loc[:, SourceColumns.USER_LOGIN_ID]

        ids_df.loc[
            is_outgoing_direction_user_mask
            | (is_incoming_direction_user_mask & ~user_login_id_not_null_mask),
            TempColumns.TO_IDS,
        ] = run_map_attribute(
            source_frame=self.source_frame,
            params=MapAttributeParams(
                source_attribute=SourceColumns.TO,
                target_attribute=TempColumns.TO_IDS,
                cast_to=CastTo.STRING_LIST,
                list_delimiter=";;;",  # Only has one value
            ),
            skip_serializer=True,
        ).loc[:, TempColumns.TO_IDS]

        ids_df.loc[
            is_incoming_direction_user_mask & user_login_id_not_null_mask,
            TempColumns.TO_IDS,
        ] = run_map_attribute(
            source_frame=self.source_frame,
            params=MapAttributeParams(
                source_attribute=SourceColumns.USER_LOGIN_ID,
                target_attribute=TempColumns.TO_IDS,
                cast_to=CastTo.STRING_LIST,
                list_delimiter=";;;",  # Only has one value
            ),
            skip_serializer=True,
        ).loc[:, TempColumns.TO_IDS]

        ids_df = ids_df.fillna(value=pd.NA)

        return run_participant_identifiers(
            source_frame=ids_df,
            params=ParticipantsIdentifiersParams(
                source_from_identifier=TempColumns.FROM_ID,
                source_to_identifiers=TempColumns.TO_IDS,
            ),
            skip_serializer=True,
        )

    def _call_link_participants(self) -> pd.DataFrame:
        identifier_column_name: str = "record_identifier"
        # Get the sourceKey, this is used for the audit keys
        audit_id_result = run_generate_record_identifiers_for_df(
            source_frame=self.pre_process_df,
            params=GenerateRecordFileIdentifiersForDfParams(
                data_model=MetaModel.CALL, target_record_identifier_col=identifier_column_name
            ),
            streamed=self.streamed,
            cloud_provider=self.cloud_provider,
            skip_serializer=True,
        )
        return run_link_participants(
            source_frame=audit_id_result,
            params=LinkParticipantsParams(
                target_participants_column=CallColumns.PARTICIPANTS,
            ),
            data_models=[Call],
            streamed=self.streamed,
            record_identifier_column=identifier_column_name,
            tenant=self.tenant,
            skip_serializer=True,
        )

    def _waveform_file_info_location_bucket(self) -> pd.Series:
        """Populates CallColumns.WAVEFORM_FILE_INFO_LOCATION_BUCKET."""
        return pd.Series(
            data=self.source_file_bucket,
            index=self.source_frame.index,
            name=CallColumns.WAVEFORM_FILE_INFO_LOCATION_BUCKET,
        )

    def _waveform_file_info_location_key(self) -> pd.Series:
        """Populates CallColumns.WAVEFORM_FILE_INFO_LOCATION_KEY."""
        return (
            WAVEFORM_FILE_PREFIX
            + VERBA_FLOW_NAME
            + "/"
            + self.target_df.loc[:, CallColumns.ID]
            + "/"
            + WAVEFORM_FILE_NAME
        )

    def _post_process(self):
        """Not required."""

    def _attachments(self) -> pd.Series:
        """Not Implemented."""

    def _call_type(self) -> pd.Series:
        """Not Implemented."""

    def _charge(self) -> pd.Series:
        """Not Implemented."""

    def _conference_call(self) -> pd.Series:
        """Not Implemented."""

    def _connected(self) -> pd.Series:
        """Not Implemented.

        Populated downstream in the flow
        """

    def _direction(self) -> pd.Series:
        """Not Implemented."""

    def _identifiers_host_id(self) -> pd.Series:
        """Not Implemented."""

    def _rate(self) -> pd.Series:
        """Not Implemented."""

    def _metadata_source_file_info_location_bucket(self) -> pd.Series:
        return self.pre_process_df.loc[:, CallColumns.METADATA_SOURCE_FILE_INFO_LOCATION_BUCKET]

    def _metadata_source_file_info_location_key(self) -> pd.Series:
        return self.pre_process_df.loc[:, CallColumns.METADATA_SOURCE_FILE_INFO_LOCATION_KEY]

    def _call_duration_speaking(self) -> pd.Series:
        """Not Implemented."""

    def _fault(self) -> pd.Series:
        """Not Implemented."""

    def _has_attachment(self) -> pd.Series:
        """Not Implemented."""

    def _identifiers_all_domains(self) -> pd.Series:
        """Not Implemented."""

    def _identifiers_bcc_ids(self) -> pd.Series:
        """Not Implemented."""

    def _identifiers_cc_ids(self) -> pd.Series:
        """Not Implemented."""

    def _identifiers_from_ip(self) -> pd.Series:
        """Not Implemented."""

    def _identifiers_from_user_id(self) -> pd.Series:
        """Not Implemented."""

    def _identifiers_on_behalf_of(self) -> pd.Series:
        """Not Implemented."""

    def _identifiers_to_ip(self) -> pd.Series:
        """Not Implemented."""

    def _identifiers_to_user_id(self) -> pd.Series:
        """Not Implemented."""

    def _internal(self) -> pd.Series:
        """Not Implemented."""

    def _is_multi_channel(self) -> pd.Series:
        """Not Implemented."""

    def _is_dealer_board(self) -> pd.Series:
        """Not Implemented."""

    def _metadata_content_type(self) -> pd.Series:
        """Not Implemented."""

    def _metadata_encoding_type(self) -> pd.Series:
        """Not Implemented."""

    def _metadata_header(self) -> pd.Series:
        """Not Implemented."""

    def _metadata_in_reply_to(self) -> pd.Series:
        """Not Implemented."""

    def _metadata_message_id(self) -> pd.Series:
        """Not Implemented."""

    def _metadata_reference_id(self) -> pd.Series:
        """Not Implemented."""

    def _metadata_size_in_bytes(self) -> pd.Series:
        """Not Implemented."""

    def _metadata_source_device_type(self) -> pd.Series:
        """Not Implemented."""

    def _timestamps_created(self) -> pd.Series:
        pass

    def _timestamps_duration_unit(self) -> pd.Series:
        pass

    def _timestamps_duration_value(self) -> pd.Series:
        pass

    def _transcribed(self) -> pd.Series:
        """Not Implemented."""

    def _voice_file(self) -> pd.Series:
        """Not Implemented."""

    def _is_transcription_excluded(self) -> pd.Series:
        raise NotImplementedError("Not in use.")

    def _transcription_exclusion_reason(self) -> pd.Series:
        raise NotImplementedError("Not in use.")
