import base64
import boto3
import email
import fsspec
import json
import logging
import pandas as pd
import pytz
import re
from aries_se_comms_tasks.feeds.email.transform.static import EmailColumns
from aries_se_core_tasks.io.utils import FileInfo, get_file_info
from aries_se_core_tasks.utilities.data_utils import object2string
from aries_se_core_tasks.utilities.s3_utils import get_s3_client
from Crypto.Cipher import AES
from dateutil import parser
from email.utils import getaddresses
from io import BytesIO
from pathlib import Path
from se_boltons.decorators import retry
from se_enums.cloud import CloudProviderEnum
from typing import Any, Dict, List, Optional, Set, Tuple, Union

REGEX_FIRST_ID = r"[a-zA-Z0-9\.]+@[a-zA-Z0-9\.]+[^@\.]"

logger_ = logging.getLogger(__name__)


class AWSMetaKeys:
    X_AMZ_KEY_V2 = "x-amz-key-v2"
    X_AMZ_IV = "x-amz-iv"
    X_AMZ_MATDESC = "x-amz-matdesc"
    X_AMZ_CEK_ALG = "x-amz-cek-alg"


class AWSDecryptCols:
    AES_GCM_NOPADDING = "AES/GCM/NoPadding"


def msg_to_string(part: email.message.Message, encoding=None) -> str:
    """convert msg object to string."""
    try:
        return object2string(bytes(part), encoding)  # type: ignore
    except UnicodeEncodeError:
        try:
            bytes_content = bytes(part)
            return bytes_content.decode("utf-8")
        except UnicodeEncodeError:
            bytes_content = bytes(part)
            return bytes_content.decode("latin-1")
    except KeyError:
        nested_add_headers(part, "Content-Transfer-Encoding", "8bit")
        return object2string(part, encoding)  # type: ignore


def nested_add_headers(part: email.message.Message, header: str, value: str) -> None:
    """add the given header key in message.

    :param part: Message object
    :param header: string
    :param value: string
    :return:
    """
    if header not in part.keys():
        part.add_header(header, value)

    for i in part.get_payload():
        if isinstance(i, email.message.Message):
            nested_add_headers(i, header, value)


class InvalidEmailIDException(Exception):
    pass


def split_email_identifier(email_id: Union[str, None]) -> Tuple[str, str]:
    # TODO: add pydoc and change to named tuple
    # skip None identifiers, and those with no @ sign
    if (email_id is None) or ("@" not in email_id):
        raise InvalidEmailIDException()
    # skip identifiers with @ as first or last char
    first_and_last_char = (email_id[0], email_id[-1])
    if "@" in first_and_last_char:
        raise InvalidEmailIDException()

    # for weird addresses like foo@@bar.co.uk
    email_id = email_id.replace("@@", "@")
    if email_id.count("@") > 1:
        # edge case ids like "<EMAIL><EMAIL>"
        email_id = re.search(REGEX_FIRST_ID, email_id)[0]  # type: ignore

    local_part, domain = email_id.split("@")

    return local_part, domain


def rfc5322(inp: Union[str, None]) -> str:
    inp = inp or ""
    inp = inp if isinstance(inp, str) else object2string(inp)
    val = "".join(c for c in inp if is_rfc5322_char(c))
    return val.lower()


def get_email_address_domain(mail_id: str) -> str:
    if is_empty(mail_id):
        return ""
    else:
        d = mail_id.split("@")[-1]
        d = d.split(">")[0]
        d = d.lower().replace("<", "")
        return d


def listify_and_split(mail_ids: Optional[str], delimiter=";") -> List[str]:
    """Split string into unique elements.

    Return a list no matter what (empty list for None input)
    """
    if mail_ids is None:
        return []
    else:
        return list(set(mail_ids.split(delimiter)))


def process_mail_id_headers(hdrs: List[Union[str, email.header.Header]]) -> List[str]:
    ret_val = set()
    for hdr in hdrs:
        if isinstance(hdr, email.header.Header):
            hdr = email.header.decode_header(hdr)  # type: ignore

        if isinstance(hdr, list):
            for str_value, encoding in hdr:
                if encoding and "unknown" in encoding:
                    encoding = "utf-8"
                ret_val.update(
                    process_one_id_header(object2string(str_value, encoding))  # type: ignore
                )
        elif isinstance(hdr, str):
            ret_val.update(process_one_id_header(hdr))

    return list(ret_val)


def process_one_id_header(mail_id: str) -> Set[str]:
    # Use a non-strict parser to get email addresses like 'Undisclosed recipients:;'.
    # The strict parser (which is the default in Python 3.11.10 and upwards) will
    # remove values like these because it doesn't consider them to be valid email
    # addresses in To, Bcc etc.
    ids = getaddresses(
        mail_id.replace("\n", "").replace("\r", "").replace("\t", "").split(","),
        strict=False,  # type: ignore[call-arg]
    )
    all_ids = {normalize_email_id(x[1]) for x in ids}

    ids_with_at = {_id for _id in all_ids if "@" in _id}
    if ids_with_at:
        return ids_with_at
    else:
        return all_ids


def normalize_email_id(inp: str) -> str:
    return "".join(c for c in inp if is_rfc5322_char(c)).lower()


def is_rfc5322_char(inp: str) -> bool:
    rfc5322_pattern = "!#$%&*+-/=?^_{|}~@."
    return inp.isalnum() or inp in rfc5322_pattern


def format_date(date: str) -> Any:
    """Formatted datetime string, timezone-adjusted to UTC.

    Args:
        date (str): input string timestamp
        logger: logger instance

    Returns:
        Union[str, None]: unified format timestamp
    """
    try:
        parsed_date = parser.parse(date, fuzzy=True)
        if parsed_date.utcoffset() is None:
            # Naive datetime, so we arbitrarily decide to make it UTC
            # Should not happen, as RFC2822 requires timezone offset
            parsed_date = parsed_date.replace(tzinfo=pytz.utc)
        else:
            parsed_date = parsed_date.astimezone(tz=pytz.utc)
        return parsed_date.strftime(EmailColumns.TIMESTAMP_FORMAT)
    except Exception:
        logger_.warning(f"Failed to parse Date: {object2string(date)} in incoming mail")
        return None


def is_empty(val: Union[str, None]) -> bool:
    """SteelEye-specific way of making sure it's EMPTYYY :)

    Args:
        val (Union[str, None]): the value

    Returns:
        bool: the VERDICT!
    """
    return (val is None) or (isinstance(val, str) and (val.isspace() or val.strip() == ""))


def get_charset_with_override(charset: str) -> str:
    """Returns the charset value if override isn't found for any encoding.

    :param charset: The charset value
    :return: The charset override or the charset received
    """
    # This override is just for chinese traditional
    # if needed we can add the other overrides here too
    return {"gb2312": "gbk"}.get(charset, charset)


class GetEmlFileFromS3:
    def __init__(self):
        self.s3_client = get_s3_client()

    @retry(backoff_factor=0.4, max_backoff=1, max_retry=3)
    def get_file(self, file_url: str, tmp_local_dir: str) -> tuple[str, Dict]:
        """download file content from s3 and decrypt it.

        :param file_url: s3 uri
        :param tmp_local_dir: temp local dir where file should be stored
        :return: tuple of decrypted file content and get object response
        """
        if not file_url.lower().startswith("s3://"):
            logger_.error("Param file_url must start with s3://")
            raise ValueError("Please prepend s3:// to file_url")

        # TODO: move s3 uri parsing to utility method
        s3_bucket, s3_key = file_url[5:].split("/", 1)
        response = self.s3_client.get_object(Bucket=s3_bucket, Key=s3_key)
        filename = Path(s3_key).name
        local_file_path = Path(tmp_local_dir).joinpath(filename).as_posix()
        if self._has_decrypt_metadata(response):
            bucket_loc = self.s3_client.get_bucket_location(Bucket=s3_bucket)
            bucket_region = bucket_loc.get("LocationConstraint")
            content = self._decrypt(response, bucket_region=bucket_region)
        else:
            content = response.get("Body").read()

        response.pop("Body", None)
        with open(Path(tmp_local_dir).joinpath(local_file_path), "wb") as f:
            f.write(content)
        return local_file_path, response

    @classmethod
    def _has_decrypt_metadata(cls, response) -> bool:
        """check if response has decrypt metdata code is copied from commons-
        sink https://github.com/steeleye/commons-sink/blob/ad119d47246fc6fc09d7
        d1a959440358ca424c8e/commons_sink/s3_client.py#L220.

        :param response:
        :return: True/False
        """
        missing_keys = list()
        for required_key in [
            AWSMetaKeys.X_AMZ_KEY_V2,
            AWSMetaKeys.X_AMZ_IV,
            AWSMetaKeys.X_AMZ_MATDESC,
            AWSMetaKeys.X_AMZ_CEK_ALG,
        ]:
            if required_key not in response.get("Metadata"):
                missing_keys.append(required_key)

        return not len(missing_keys) > 0

    @classmethod
    def _decrypt(cls, response, bucket_region: str) -> bytes:
        """decrypts response body from s3 get_object.

        :param response: response from s3 get_object call
        :param bucket_region: name of the region used to configure kms client
        :return: string
        """
        metadata = response.get("Metadata")
        envelope_key = base64.b64decode(metadata.get(AWSMetaKeys.X_AMZ_KEY_V2))
        envelope_iv = base64.b64decode(metadata.get(AWSMetaKeys.X_AMZ_IV))
        encrypt_ctx = json.loads(metadata.get(AWSMetaKeys.X_AMZ_MATDESC))
        original_size = metadata.get("x-amz-unencrypted-content-length")
        cek_alg = metadata.get(AWSMetaKeys.X_AMZ_CEK_ALG)

        kms = boto3.client("kms", region_name=bucket_region)
        plaintext_key = kms.decrypt(CiphertextBlob=envelope_key, EncryptionContext=encrypt_ctx).get(
            "Plaintext"
        )

        # todo map cek_alg to AES mode properly
        mode = AES.MODE_GCM if cek_alg == AWSDecryptCols.AES_GCM_NOPADDING else AES.MODE_CBC
        decrypter = AES.new(plaintext_key, mode, envelope_iv)  # type: ignore

        content = BytesIO()
        chunk_size = 16 * 1024
        while True:
            chunk = response.get("Body").read(chunk_size)
            if len(chunk) == 0:
                break
            content.write(decrypter.decrypt(chunk))
        if original_size is not None:
            content.truncate(int(original_size))

        return content.getvalue()


class GetEmlFileFromCloud:
    @staticmethod
    def get_file(file_url, tmp_local_dir) -> Tuple[str, FileInfo]:
        filename = Path(file_url).name
        local_file_path = Path(tmp_local_dir).joinpath(filename).as_posix()
        cloud_fs, _, (_,) = fsspec.get_fs_token_paths(file_url)
        with open(local_file_path, "wb") as output_file:
            with cloud_fs.open(file_url, "rb") as input_file:
                output_file.write(input_file.read())
                cloud_response = get_file_info(path=file_url)
        return local_file_path, cloud_response


def download_eml(
    file_url: str, tmp_local_dir: str, cloud_provider: CloudProviderEnum
) -> "pd.Series[Any]":
    """for given pandas series with file details, download file content.

    :param file_url: Cloud file url to be downloaded
    :param tmp_local_dir: temp local dir where email will be downloaded
    :param cloud_provider: The cloud provider like AWS, Azure etc.
    :return: series with local_file_path and cloud response
    """
    if cloud_provider == CloudProviderEnum.AWS:
        eml_downloader_object = GetEmlFileFromS3()
    else:
        eml_downloader_object = GetEmlFileFromCloud()  # type: ignore

    try:
        local_file_path, cloud_response = eml_downloader_object.get_file(
            file_url=file_url, tmp_local_dir=tmp_local_dir
        )
        return pd.Series([local_file_path, cloud_response])  # type: ignore
    except Exception as e:
        error_message = f"Failed to fetch email content for {file_url} Error:{e}"
        logger_.exception(error_message)
        return pd.Series([pd.NA, pd.NA])


def encode_headers(header_decoded_parts):
    try:
        encoding = get_charset_with_override(header_decoded_parts[0][1]) or "utf-8"
        # Override the encoding value in the decoded headers and default to
        # UTF-8 if no value for encoding is there.
        # Uses the python email built in make_header method as it parses all
        # the extra whitespace or so.
        header_value = email.header.make_header(
            [(x[0], get_charset_with_override(x[1]) or encoding) for x in header_decoded_parts]
        )
    except (UnicodeDecodeError, UnicodeEncodeError, LookupError):
        # EU-10174 -> Here we are trying to decode the string with the detected encoding
        # if it fails then catch and process it using latin-1
        try:
            header_value = "".join(
                part[0].decode(encoding) if isinstance(part[0], bytes) else part[0]  # type: ignore
                for part in header_decoded_parts
            )
        except (UnicodeDecodeError, LookupError):
            # We want to default to utf-8 but in many cases when it fails, using
            # latin-1 seems to solve the issue. So it's better to ingest than fail
            # in such scenarios.
            encoding = "latin-1"
            header_value = "".join(
                part[0].decode(encoding) if isinstance(part[0], bytes) else part[0]  # type: ignore
                for part in header_decoded_parts
            )
    return header_value, encoding
