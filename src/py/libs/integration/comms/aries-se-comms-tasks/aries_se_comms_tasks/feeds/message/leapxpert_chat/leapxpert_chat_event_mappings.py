import pandas as pd
from aries_se_comms_tasks.abstractions.transformations.abstract_chat_event_transformations import (  # type: ignore[attr-defined] # noqa: E501
    AbstractChatEventTransformations,
)
from aries_se_comms_tasks.chat_event.static import ChatEventColumns
from aries_se_comms_tasks.feeds.message.leapxpert_chat.base import LeapXpertChatBase
from aries_se_comms_tasks.feeds.message.leapxpert_chat.static import (
    UTC_DATETIME_FORMAT,
    Channels,
    ChatEventValues,
    SourceColumns,
    TempColumns,
)
from aries_se_comms_tasks.generic.participants.participant_identifiers import (  # type: ignore[attr-defined] # noqa: E501
    Params as ParticipantIdentifiersParams,
)
from aries_se_comms_tasks.generic.participants.participant_identifiers import (  # type: ignore[attr-defined] # noqa: E501
    run_participant_identifiers,
)
from aries_se_core_tasks.io.utils import FileInfo
from aries_se_core_tasks.transform.map.map_value import (  # type: ignore[attr-defined] # noqa: E501
    Params as MapValueParams,
)
from aries_se_core_tasks.transform.map.map_value import (
    run_map_value,
)
from datetime import datetime
from pandas._libs.missing import NAType
from se_core_tasks.map.map_value import RegexReplaceMap
from se_core_tasks.utils.datetime import DatetimeFormat
from se_data_lake.cloud_utils import get_bucket, get_key
from se_elastic_schema.static.communication import ChatEventType


class LeapXpertChatEventMappings(AbstractChatEventTransformations, LeapXpertChatBase):  # type: ignore[misc]
    source_frame: pd.DataFrame
    target_df: pd.DataFrame
    pre_process_df: pd.DataFrame
    post_process_df: pd.DataFrame

    source_file_uri: str
    metadata_file_info: FileInfo

    def __init__(self, source_file_uri: str, metadata_file_info: FileInfo, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.source_file_uri = source_file_uri
        self.metadata_file_info = metadata_file_info

    def _pre_process(self):
        self.pre_process_df = pd.concat(
            [
                self.pre_process_df,
                self.process_ids(
                    pre_process_df=self.pre_process_df, source_frame=self.source_frame
                ),
                self.get_timestamp(source_frame=self.source_frame),
            ],
            axis=1,
        )

        self.pre_process_df = pd.concat(
            [
                self.pre_process_df,
                run_participant_identifiers(
                    source_frame=pd.concat(
                        [
                            self.pre_process_df.loc[
                                :,
                                [
                                    TempColumns.FROM_ID,
                                    TempColumns.ON_BEHALF_OF_ID,
                                ],
                            ],
                        ],
                        axis=1,
                    ),
                    params=ParticipantIdentifiersParams(
                        source_from_identifier=TempColumns.FROM_ID,
                        source_on_behalf_identifier=TempColumns.ON_BEHALF_OF_ID,
                    ),
                    skip_serializer=True,
                ),
            ],
            axis=1,
        )

    def process(self) -> pd.DataFrame:
        self.pre_process()

        self.event_type()
        self.room_id()
        self.source_key()

        # Timestamps
        self.timestamps_timestamp_connected()
        self.timestamps_timestamp_start()
        self.timestamps_timestamp_end()
        self.timestamps_local_timestamp_end()
        self.timestamps_local_timestamp_start()

        # Participant Identifiers
        self.identifiers_from_id()
        self.identifiers_from_user_id()
        self.identifiers_from_id_addl_info()
        self.identifiers_on_behalf_of()
        self.identifiers_all_ids()
        self.identifiers_all_country_codes()

        # Metadata
        self.metadata_source_file_info_location_bucket()
        self.metadata_source_file_info_location_key()
        self.metadata_message_id()
        self.metadata_reference_id()
        self.metadata_size_in_bytes()
        self.metadata_source_client()
        self.metadata_source_file_info_content_length()
        self.metadata_source_file_info_content_md5()
        self.metadata_source_file_info_metadata()
        self.metadata_source_file_info_version_id()
        self.metadata_source_file_info_last_modified()
        self.metadata_source_file_info_processed()
        self.metadata_thread_id()

        return self.target_df

    def _event_type(self) -> pd.Series:
        return run_map_value(
            source_frame=self.source_frame,
            params=MapValueParams(
                target_attribute=ChatEventColumns.EVENT_TYPE,
                source_attribute=SourceColumns.CONVERSATION_INFO_CONTENT,
                value_map={
                    ChatEventValues.JOINED: ChatEventType.ENTERED.value,
                    ChatEventValues.LEFT: ChatEventType.LEFT.value,
                    ChatEventValues.STARTED_CONVO: ChatEventType.ROOM_OPEN.value,
                },
            ),
            skip_serializer=True,
        ).loc[:, ChatEventColumns.EVENT_TYPE]

    def _identifiers_all_country_codes(self):
        return self.pre_process_df.loc[:, ChatEventColumns.IDENTIFIERS_ALL_COUNTRY_CODES]

    def _identifiers_all_ids(self) -> pd.Series:
        return self.pre_process_df.loc[:, ChatEventColumns.IDENTIFIERS_ALL_IDS]

    def _identifiers_from_id(self) -> pd.Series:
        return self.pre_process_df.loc[:, ChatEventColumns.IDENTIFIERS_FROM_ID]

    def _identifiers_from_user_id(self) -> pd.Series:
        return self.source_frame.loc[:, SourceColumns.SENDER_USER_ID]

    def _identifiers_from_id_addl_info(self) -> pd.Series:
        return self.pre_process_df.loc[:, ChatEventColumns.IDENTIFIERS_FROM_ADDL_INFO]

    def _identifiers_on_behalf_of(self) -> pd.Series:
        return self.pre_process_df.loc[:, ChatEventColumns.IDENTIFIERS_ON_BEHALF_OF]

    def _identifiers_to_ids(self):
        """Not Implemented."""

    def _identifiers_to_ids_addl_info(self):
        """Not Implemented."""

    def _identifiers_to_user_id(self):
        """Not Implemented."""

    def _metadata_source_file_info_location_bucket(self) -> pd.Series:
        return pd.Series(
            index=self.source_frame.index,
            data=get_bucket(file_uri=self.source_file_uri),
            name=ChatEventColumns.METADATA_SOURCE_FILE_INFO_LOCATION_BUCKET,
        )

    def _metadata_source_file_info_location_key(self) -> pd.Series:
        return pd.Series(
            index=self.source_frame.index,
            data=get_key(file_uri=self.source_file_uri),
            name=ChatEventColumns.METADATA_SOURCE_FILE_INFO_LOCATION_KEY,
        )

    def _metadata_message_id(self) -> pd.Series:
        return self.source_frame.loc[:, SourceColumns.ID]

    def _metadata_reference_id(self) -> pd.Series:
        return self.source_frame.loc[:, SourceColumns.ID]

    def _metadata_size_in_bytes(self) -> pd.Series:
        return pd.Series(
            index=self.source_frame.index,
            data=self.metadata_file_info.size,
            name=ChatEventColumns.METADATA_SIZE_IN_BYTES,
        )

    def _metadata_source_client(self) -> pd.Series:
        return run_map_value(
            source_frame=self.source_frame,
            params=MapValueParams(
                source_attribute=SourceColumns.SENDER_CHANNEL,
                target_attribute=ChatEventColumns.METADATA_SOURCE_CLIENT,
                case_insensitive=True,
                regex_replace_map=[
                    RegexReplaceMap(regex=r"[\w-]*whatsapp[\w-]*", replace_value="whatsapp"),
                    RegexReplaceMap(regex=r"[\w-]*wechat[\w-]*", replace_value="wechat"),
                    RegexReplaceMap(regex=r"[\w-]*telegram[\w-]*", replace_value="telegram"),
                    RegexReplaceMap(regex=r"[\w-]*imessage[\w-]*", replace_value="imessage"),
                    RegexReplaceMap(regex=r"[\w-]*leap[\w-]*", replace_value="leap"),
                    RegexReplaceMap(regex=r"[\w-]*wecom[\w-]*", replace_value="wecom"),
                ],
                value_map=Channels.VALUE_MAP,
                preserve_original=True,
            ),
            skip_serializer=True,
        ).loc[:, ChatEventColumns.METADATA_SOURCE_CLIENT]

    def _metadata_thread_id(self) -> pd.Series:
        return self.target_df.loc[:, ChatEventColumns.ROOM_ID]

    def _metadata_source_file_info_content_length(self) -> pd.Series:
        return pd.Series(
            index=self.source_frame.index,
            data=self.metadata_file_info.size,
            name=ChatEventColumns.METADATA_SOURCE_FILE_INFO_CONTENT_LENGTH,
        )

    def _metadata_source_file_info_content_md5(self) -> pd.Series:
        e_tag: str | NAType = pd.NA

        try:
            e_tag = self.metadata_file_info.e_tag  # type: ignore

            e_tag = e_tag.replace("'", "").replace('"', "")  # type: ignore[union-attr]
        except AttributeError:
            pass

        return pd.Series(
            index=self.source_frame.index,
            data=e_tag,
            name=ChatEventColumns.METADATA_SOURCE_FILE_INFO_CONTENT_MD5,
        )

    def _metadata_source_file_info_metadata(self) -> pd.Series:
        metadata = self.metadata_file_info.metadata

        return pd.Series(
            index=self.source_frame.index,
            data=metadata,
            name=ChatEventColumns.METADATA_SOURCE_FILE_INFO_METADATA,
        )

    def _metadata_source_file_info_version_id(self) -> pd.Series:
        version_id = self.metadata_file_info.version_id

        return pd.Series(
            index=self.source_frame.index,
            data=version_id,
            name=ChatEventColumns.METADATA_SOURCE_FILE_INFO_VERSION_ID,
        )

    def _metadata_source_file_info_last_modified(self) -> pd.Series:
        return pd.Series(
            index=self.source_frame.index,
            data=self.metadata_file_info.last_modified.strftime(DatetimeFormat.DATETIME),
            name=ChatEventColumns.METADATA_SOURCE_FILE_INFO_LAST_MODIFIED,
        )

    def _metadata_source_file_info_processed(self) -> pd.Series:
        return pd.Series(
            index=self.source_frame.index,
            data=datetime.utcnow().strftime(UTC_DATETIME_FORMAT),
            name=ChatEventColumns.METADATA_SOURCE_FILE_INFO_PROCESSED,
        )

    def _source_key(self) -> pd.Series:
        return pd.Series(
            index=self.source_frame.index,
            data=self.source_file_uri,
            name=ChatEventColumns.SOURCE_KEY,
        )

    def _room_id(self) -> pd.Series:
        """Populates ChatEventColumns.ROOM_ID from
        SourceColumns.INTERACTION_ID."""
        return self.source_frame.loc[:, SourceColumns.INTERACTION_ID].str.replace(
            pat=r"_.*?$", repl="", regex=True
        )

    def _timestamps_local_timestamp_end(self) -> pd.Series:
        """Populates TIMESTAMPS_LOCAL_TIMESTAMP_END."""
        return self.pre_process_df.loc[:, TempColumns.TIMESTAMP]

    def _timestamps_local_timestamp_start(self) -> pd.Series:
        """Populates TIMESTAMPS_LOCAL_TIMESTAMP_START."""
        return self.pre_process_df.loc[:, TempColumns.TIMESTAMP]

    def _timestamps_timestamp_connected(self) -> pd.Series:
        """Populates TIMESTAMPS_TIMESTAMP_CONNECTED."""
        return self.pre_process_df.loc[:, TempColumns.TIMESTAMP]

    def _timestamps_timestamp_end(self) -> pd.Series:
        """Populates TIMESTAMPS_TIMESTAMP_END."""
        return self.pre_process_df.loc[:, TempColumns.TIMESTAMP]

    def _timestamps_timestamp_start(self) -> pd.Series:
        """Populates TIMESTAMPS_TIMESTAMP_START."""
        return self.pre_process_df.loc[:, TempColumns.TIMESTAMP]

    def _post_process(self):
        """Not Implemented."""

    def _event_qualifier(self):
        """Not Implemented."""

    def _identifiers_all_domains(self):
        """Not Implemented."""

    def _identifiers_domains(self):
        """Not Implemented."""

    def _identifiers_from_device_id(self):
        """Not Implemented."""

    def _identifiers_to_device_id(self):
        """Not Implemented."""

    def _identifiers_to_ip(self):
        """Not Implemented."""

    def _metadata_source_device_type(self):
        """Not Implemented."""

    def _participants(self):
        """This is populated downstream in the flow."""

    def _timestamps_created(self):
        """Not Implemented."""
