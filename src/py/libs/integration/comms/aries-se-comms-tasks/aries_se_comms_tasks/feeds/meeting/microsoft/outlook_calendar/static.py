from aries_se_core_tasks.utilities.data_utils import BaseColumns

ATTACHMENTS_FOLDER_NAME = "attachments/"
FORWARD_SLASH = "/"
JSON_FILE_SUFFIX = ".json"
SOURCE_CLIENT = "Outlook Calendar Meeting"
OUTLOOK_CALENDAR_FLOW_NAME = "outlook_calendar_meetings_transform"


class AttachmentColumns(BaseColumns):
    CONTENT = "content"
    FILEINFO_CONTENTLENGTH = "fileInfo.contentLength"
    FILEINFO_LOCATION_BUCKET = "fileInfo.location.bucket"
    FILEINFO_LOCATION_KEY = "fileInfo.location.key"
    FILEINFO_PROCESSED = "fileInfo.processed"
    FILEINFO_VERSIONID = "fileInfo.versionId"
    FILENAME = "fileName"
    FILETYPE = "fileType"
    MIME_AG = "mimeTag"
    SIZEINBYTES = "sizeInBytes"


class SourceOutlookCalendarColumns(BaseColumns):
    ATTENDEES = "attendees"
    BODY_CONTENT = "body.content"
    BODY_PREVIEW = "bodyPreview"
    CATEGORIES = "categories"
    CREATED_DATE_TIME = "createdDateTime"
    END_DATE_TIME = "end.dateTime"
    END_TIME_ZONE = "end.timeZone"
    HAS_ATTACHMENTS = "hasAttachments"
    I_CAL_UID = "iCalUId"
    ID = "id"
    IMPORTANCE = "importance"
    IS_ALL_DAY = "isAllDay"
    IS_CANCELLED = "isCancelled"
    IS_RESPONSE_REQUESTED = "responseRequested"
    LOCATION_DISPLAY_NAME = "location.displayName"
    LOCATION_UNIQUE_ID = "location.uniqueId"
    OCCURRENCE_ID = "occurrenceId"
    ORGANIZER_EMAIL_ADDRESS_ADDRESS = "organizer.emailAddress.address"
    ORGANIZER_EMAIL_ADDRESS_NAME = "organizer.emailAddress.name"
    RECURRENCE = "recurrence"
    RECURRENCE_PATTERN_TYPE = "recurrence.pattern.type"
    SENSITIVITY = "sensitivity"
    SERIES_MASTER_ID = "seriesMasterId"
    START_DATE_TIME = "start.dateTime"
    START_TIME_ZONE = "start.timeZone"
    SUBJECT = "subject"
    TYPE = "type"
    WEB_LINK = "webLink"


class SourceAttendeesEmailAddressFields:
    ADDRESS = "address"
    EMAIL_ADDRESS = "emailAddress"
    NAME = "name"


class SourceAttachmentFields(BaseColumns):
    CONTENT_BYTES = "contentBytes"
    CONTENT_TYPE = "contentType"
    ID = "id"
    LAST_MODIFIED_DATE_TIME = "lastModifiedDateTime"
    NAME = "name"
    SIZE = "size"


class TempColumns:
    ATTACHMENTS_PATH = "ATTACHMENTS_PATH"
    ATTENDEE_IDENTIFIERS = "__attendee_identifiers__"
    ATTENDEE_LIST = "__attendee_list__"
    FROM_IDENTIFIER = "__from_identifier__"
    HOST_IDENTIFIER = "__host_identifier__"
    MEETING_DURATION_DELTA = "MEETING_DURATION_DELTA"
    MEETING_PATH = "__MEETING_PATH__"
    REALM = "REALM"
    RECORDING_KEY = "__RECORDING_KEY__"
    RECORDING_PATH = "__RECORDING_PATH__"
    TO_IDENTIFIERS = "__to_identifiers__"
    TRANSCRIPT_PATH = "__TRANSCRIPT_PATH__"


class TempAttachmentColumns(BaseColumns):
    REALM = "realm"
    FILE_INFO_LOCATION_BUCKET = "fileInfo.location.bucket"
    FILE_INFO_LOCATION_KEY = "fileInfo.location.key"
    SIZE_IN_BYTES = "sizeInBytes"
    FILE_INFO_CONTENT_LENGTH = "fileInfo.contentLength"
    FILE_INFO_VERSION_ID = "fileInfo.versionId"
    FILE_INFO_PROCESSED = "fileInfo.processed"
    VERSION_ID = "versionId"
