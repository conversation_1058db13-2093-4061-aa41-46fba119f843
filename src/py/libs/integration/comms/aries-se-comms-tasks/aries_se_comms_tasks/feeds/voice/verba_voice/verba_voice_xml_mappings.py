# mypy: disable-error-code="arg-type, return-value"
import pandas as pd
from aries_se_comms_tasks.abstractions.transformations.abstract_voice_transformations import (
    AbstractVoiceTransformations,
)
from aries_se_comms_tasks.feeds.voice.verba_voice.static import (
    SOURCE_DATETIME_FORMAT,
    VERBA_FLOW_NAME,
    ParticipantsSourceColumns,
    SourceColumns,
    TempColumns,
)
from aries_se_comms_tasks.generic.participants.link_participants import (  # type: ignore[attr-defined] # noqa: E501
    Params as LinkParticipantsParams,
)
from aries_se_comms_tasks.generic.participants.link_participants import (  # type: ignore[attr-defined] # noqa: E501
    run_link_participants,
)
from aries_se_comms_tasks.generic.participants.participant_identifiers import (  # type: ignore[attr-defined] # noqa: E501
    Params as ParticipantsIdentifiersParams,
)
from aries_se_comms_tasks.generic.participants.participant_identifiers import (  # type: ignore[attr-defined] # noqa: E501
    run_participant_identifiers,
)
from aries_se_comms_tasks.voice.static import WAVEFORM_FILE_NAME, WAVEFORM_FILE_PREFIX, CallColumns
from aries_se_core_tasks.datetime.convert_datetime import (  # type: ignore[attr-defined] # noqa: E501
    Params as ConvertDatetimeParams,
)
from aries_se_core_tasks.datetime.convert_datetime import (  # type: ignore[attr-defined] # noqa: E501
    run_convert_datetime,
)
from aries_se_core_tasks.datetime.datetime_difference import (  # type: ignore[attr-defined] # noqa: E501
    Params as DatetimeDifferenceParams,
)
from aries_se_core_tasks.datetime.datetime_difference import (
    run_datetime_difference,
)
from aries_se_core_tasks.generic.generate_record_identifiers_for_df import (  # type: ignore[attr-defined] # noqa: E501
    Params as GenerateRecordFileIdentifiersForDfParams,
)
from aries_se_core_tasks.generic.generate_record_identifiers_for_df import (  # type: ignore[attr-defined] # noqa: E501
    run_generate_record_identifiers_for_df,
)
from aries_se_core_tasks.static import MetaModel
from aries_se_core_tasks.transform.map.map_attribute import (  # type: ignore[attr-defined] # noqa: E501
    Params as MapAttributeParams,
)
from aries_se_core_tasks.transform.map.map_attribute import (  # type: ignore[attr-defined] # noqa: E501
    run_map_attribute,
)
from aries_se_core_tasks.transform.map.map_conditional import (  # type: ignore[attr-defined] # noqa: E501
    Params as MapConditionalParams,
)
from aries_se_core_tasks.transform.map.map_conditional import (  # type: ignore[attr-defined] # noqa: E501
    run_map_conditional,
)
from se_core_tasks.datetime.convert_datetime import ConvertTo
from se_core_tasks.map.map_conditional import Case
from se_data_lake.cloud_utils import get_bucket
from se_elastic_schema.models import Call
from se_enums.cloud import CloudProviderEnum
from typing import List


class VerbaVoiceXmlMappings(AbstractVoiceTransformations):
    source_frame: pd.DataFrame
    target_df: pd.DataFrame
    pre_process_df: pd.DataFrame

    source_file_uri: str
    source_file_bucket: str

    streamed: bool
    tenant: str
    cloud_provider: CloudProviderEnum

    def __init__(
        self,
        source_file_uri: str,
        streamed: bool,
        tenant: str,
        cloud_provider: CloudProviderEnum,
        **kwargs,
    ):
        super().__init__(**kwargs)

        self.streamed = streamed
        self.tenant = tenant
        self.source_file_uri = source_file_uri
        self.source_file_bucket = get_bucket(file_uri=self.source_file_uri)
        self.cloud_provider = cloud_provider

    def _pre_process(self):
        self.pre_process_df = pd.concat(
            [self.pre_process_df, self._call_participants_identifiers()],
            axis=1,
        )

    def process(self):
        self.pre_process()

        self.call_duration()

        self.conference_call()

        self.id()
        self.identifiers_from_device_id()
        self.identifiers_from_id()
        self.identifiers_from_id_addl_info()
        self.identifiers_to_device_id()
        self.identifiers_to_ids()
        self.identifiers_to_ids_addl_info()
        self.identifiers_all_country_codes()
        self.identifiers_all_ids()

        self.meta_model()

        self.metadata_source_client()
        self.metadata_source_file_info_location_bucket()
        self.metadata_source_file_info_location_key()

        self.participants()

        self.source_index()
        self.source_key()

        self.timestamps_local_timestamp_end()
        self.timestamps_local_timestamp_start()
        self.timestamps_timestamp_end()  # Has to be after timestamps_local_timestamp_end
        self.timestamps_timestamp_connected()
        self.timestamps_timestamp_start()  # Has to be after timestamps_timestamp_connected

        self.waveform_file_info_location_key()
        self.waveform_file_info_location_bucket()

        return self.target_df

    def _call_duration(self) -> pd.Series:
        result: pd.Series = run_datetime_difference(
            source_frame=self.source_frame,
            params=DatetimeDifferenceParams(
                start_column=SourceColumns.START_TIME,
                end_column=SourceColumns.END_TIME,
                start_format=SOURCE_DATETIME_FORMAT,
                end_format=SOURCE_DATETIME_FORMAT,
                target_column=CallColumns.CALL_DURATION,
                format_output=True,
            ),
            skip_serializer=True,
        ).loc[:, CallColumns.CALL_DURATION]

        return result

    def _conference_call(self) -> pd.Series:
        result: pd.Series = run_map_conditional(
            source_frame=self.source_frame,
            params=MapConditionalParams(
                target_attribute=CallColumns.CONFERENCE_CALL,
                cases=[
                    Case(
                        query=f"`{SourceColumns.CONFERENCE}`.str.fullmatch('no|false', case=False, na=False)",  # noqa: E501
                        value=False,
                    ),
                    Case(
                        query=f"~(`{SourceColumns.CONFERENCE}`.str.fullmatch('no|false', case=False, na=False))",  # noqa: E501
                        value=True,
                    ),
                ],
            ),
            skip_serializer=True,
        ).loc[:, CallColumns.CONFERENCE_CALL]

        return result

    def _id(self) -> pd.Series:
        result: pd.Series = run_map_attribute(
            source_frame=self.source_frame,
            params=MapAttributeParams(
                source_attribute=SourceColumns.ID,
                target_attribute=CallColumns.ID,
            ),
            skip_serializer=True,
        ).loc[:, CallColumns.ID]

        return result

    def _identifiers_all_country_codes(self) -> pd.Series:
        return self.pre_process_df.loc[:, CallColumns.IDENTIFIERS_ALL_COUNTRY_CODES]

    def _identifiers_all_ids(self) -> pd.Series:
        return self.pre_process_df.loc[:, CallColumns.IDENTIFIERS_ALL_IDS]

    def _identifiers_from_device_id(self) -> pd.Series:
        return self.source_frame.loc[:, SourceColumns.PLATFORM_ID]

    def _identifiers_from_id(self) -> pd.Series:
        return self.pre_process_df.loc[:, CallColumns.IDENTIFIERS_FROM_ID]

    def _identifiers_from_id_addl_info(self):
        return self.pre_process_df.loc[:, CallColumns.IDENTIFIERS_FROM_ADDL_INFO]

    def _identifiers_to_device_id(self) -> pd.Series:
        return self.source_frame.loc[:, SourceColumns.PLATFORM_ID]

    def _identifiers_to_ids(self) -> pd.Series:
        return self.pre_process_df.loc[:, CallColumns.IDENTIFIERS_TO_IDS]

    def _identifiers_to_ids_addl_info(self) -> pd.Series:
        return self.pre_process_df.loc[:, CallColumns.IDENTIFIERS_TO_ADDL_INFO]

    def _participants(self) -> pd.Series:
        identifier_column_name: str = "record_identifier"

        # Get the sourceKey, this is used for the audit keys
        audit_id_result = run_generate_record_identifiers_for_df(
            source_frame=self.pre_process_df,
            params=GenerateRecordFileIdentifiersForDfParams(
                data_model=MetaModel.CALL, target_record_identifier_col=identifier_column_name
            ),
            streamed=self.streamed,
            cloud_provider=self.cloud_provider,
            skip_serializer=True,
        )

        result: pd.Series = run_link_participants(
            source_frame=audit_id_result,
            params=LinkParticipantsParams(
                target_participants_column=CallColumns.PARTICIPANTS,
            ),
            data_models=[Call],
            streamed=self.streamed,
            record_identifier_column=identifier_column_name,
            tenant=self.tenant,
            skip_serializer=True,
        ).loc[:, CallColumns.PARTICIPANTS]

        return result

    def _metadata_source_client(self) -> pd.Series:
        result: pd.Series = run_map_conditional(
            source_frame=self.source_frame,
            params=MapConditionalParams(
                target_attribute=CallColumns.METADATA_SOURCE_CLIENT,
                cases=[
                    Case(
                        query=f"`{SourceColumns.PLATFORM_ID}`.str.fullmatch('msteams', case=False, na=False)",  # noqa: E501
                        value="Verba MSTeams",
                    ),
                    Case(
                        query=f"`{SourceColumns.PLATFORM_ID}`.str.fullmatch('cisco-network', case=False, na=False)",  # noqa: E501
                        value="Verba Cisco",
                    ),
                    Case(
                        query=f"`{SourceColumns.PLATFORM_ID}`.str.fullmatch('ip-trade', case=False, na=False)",  # noqa: E501
                        value="BT Turret (Verba)",
                    ),
                ],
            ),
            skip_serializer=True,
        ).loc[:, CallColumns.METADATA_SOURCE_CLIENT]

        return result

    def _metadata_source_file_info_location_bucket(self) -> pd.Series:
        return self.source_frame.loc[:, TempColumns.METADATA_SOURCE_BUCKET]

    def _metadata_source_file_info_location_key(self) -> pd.Series:
        return self.source_frame.loc[:, TempColumns.METADATA_SOURCE_KEY]

    def _source_index(self) -> pd.Series:
        return pd.Series(
            data=self.source_frame.index.values,
            index=self.source_frame.index,
            name=CallColumns.SOURCE_INDEX,
        )

    def _source_key(self) -> pd.Series:
        return pd.Series(
            data=self.source_file_uri,
            index=self.source_frame.index,
            name=CallColumns.SOURCE_KEY,
        )

    def _timestamps_local_timestamp_end(self) -> pd.Series:
        result: pd.Series = run_convert_datetime(
            self.source_frame,
            params=ConvertDatetimeParams(
                source_attribute=SourceColumns.END_TIME,
                source_attribute_format=SOURCE_DATETIME_FORMAT,
                target_attribute=CallColumns.TIMESTAMPS_LOCAL_TIMESTAMP_END,
                convert_to=ConvertTo.DATETIME,
            ),
            skip_serializer=True,
        ).loc[:, CallColumns.TIMESTAMPS_LOCAL_TIMESTAMP_END]

        return result

    def _timestamps_local_timestamp_start(self) -> pd.Series:
        result: pd.Series = run_convert_datetime(
            self.source_frame,
            params=ConvertDatetimeParams(
                source_attribute=SourceColumns.START_TIME,
                source_attribute_format=SOURCE_DATETIME_FORMAT,
                target_attribute=CallColumns.TIMESTAMPS_LOCAL_TIMESTAMP_START,
                convert_to=ConvertTo.DATETIME,
            ),
            skip_serializer=True,
        ).loc[:, CallColumns.TIMESTAMPS_LOCAL_TIMESTAMP_START]

        return result

    def _timestamps_timestamp_connected(self) -> pd.Series:
        result: pd.Series = run_convert_datetime(
            self.source_frame,
            params=ConvertDatetimeParams(
                source_attribute=SourceColumns.START_TIME,
                source_attribute_format=SOURCE_DATETIME_FORMAT,
                target_attribute=CallColumns.TIMESTAMPS_TIMESTAMP_CONNECTED,
                convert_to=ConvertTo.DATETIME,
            ),
            skip_serializer=True,
        ).loc[:, CallColumns.TIMESTAMPS_TIMESTAMP_CONNECTED]

        return result

    def _timestamps_timestamp_end(self) -> pd.Series:
        return self.target_df.loc[:, CallColumns.TIMESTAMPS_LOCAL_TIMESTAMP_END]

    def _timestamps_timestamp_start(self) -> pd.Series:
        return self.target_df.loc[:, CallColumns.TIMESTAMPS_TIMESTAMP_CONNECTED]

    def _waveform_file_info_location_bucket(self) -> pd.Series:
        return pd.Series(
            data=self.source_file_bucket,
            index=self.source_frame.index,
            name=CallColumns.WAVEFORM_FILE_INFO_LOCATION_BUCKET,
        )

    def _waveform_file_info_location_key(self) -> pd.Series:
        return (
            WAVEFORM_FILE_PREFIX
            + VERBA_FLOW_NAME
            + "/"
            + self.target_df.loc[:, CallColumns.ID]
            + "/"
            + WAVEFORM_FILE_NAME
        )

    def _call_participants_identifiers(self) -> pd.DataFrame:
        to_ids_df = pd.DataFrame(index=self.source_frame.index)
        to_ids_df[TempColumns.TO_IDS_LIST] = pd.NA

        cisco_network_mask = self.source_frame.loc[:, SourceColumns.PLATFORM_ID].str.fullmatch(
            "cisco-network|ip-trade", case=False, na=False
        )
        ms_teams_mask = self.source_frame.loc[:, SourceColumns.PLATFORM_ID].str.fullmatch(
            "msteams", case=False, na=False
        )

        if cisco_network_mask.any():
            to_ids_df.loc[cisco_network_mask, TempColumns.TO_IDS_LIST] = self.source_frame.loc[
                cisco_network_mask, SourceColumns.DESTINATION_CALLER_ID
            ].map(lambda x: [x])

        if ms_teams_mask.any():
            to_ids_df.loc[ms_teams_mask, TempColumns.TO_IDS_LIST] = self.source_frame.loc[
                ms_teams_mask,
                [SourceColumns.PARTICIPANTS, SourceColumns.SOURCE_CALLER_ID],
            ].apply(
                lambda x: self._unpack_participants(
                    participants=x[SourceColumns.PARTICIPANTS],
                    from_id=x[SourceColumns.SOURCE_CALLER_ID],
                ),
                axis=1,
            )

        result: pd.DataFrame = run_participant_identifiers(
            source_frame=pd.concat([to_ids_df, self.source_frame], axis=1),
            params=ParticipantsIdentifiersParams(
                source_from_identifier=SourceColumns.SOURCE_CALLER_ID,
                source_to_identifiers=TempColumns.TO_IDS_LIST,
            ),
            skip_serializer=True,
        )

        return result

    @staticmethod
    def _unpack_participants(participants: list, from_id: str) -> List[str]:
        """Loops through the participant list under participants, and puts the
        caller names into a list, after subtracting the from_id.

        E.g. participants value:
        [
            {
        "_text": "\n\t\t",
        "participant":
        [
            {
                "_text": "\n\t\t\t",
                "callerid":
                [{
                        "_text": "ADE"
                    }],
                "name":
                [{
                        "_text": "Adam James Seagrave (ADE)"
                    }],
                "role":
                [{
                        "_text": "Conferee"
                    }],
                "start_time":
                [{
                        "_text": "2022.07.18 14:36:20.555"
                    }],
                "end_time":
                [{
                        "_text": "2022.07.18 15:03:05.940"
                    }],
                "modality":
                [{
                        "_text": "Audio, Data, Video"
                    }],
                "end_cause":
                [{
                        "_text": "0"
                    }]
            },
            {
                "_text": "\n\t\t\t",
                "callerid":
                [{
                        "_text": "MIRI"
                    }],
                "name":
                [{
                        "_text": "Michael Howard Ridley (MIRI)"
                    }],
                "role":
                [{
                        "_text": "Conferee"
                    }],
                "start_time":
                [{
                        "_text": "2022.07.18 14:36:20.555"
                    }],
                "end_time":
                [{
                        "_text": "2022.07.18 15:03:05.940"
                    }],
                "modality":
                [{
                        "_text": "Audio, Video, Data"
                    }],
                "end_cause":
                [{
                    "_text": "0"
                    }]
            }

        :param participants: Complex Nested list of to ids for MS Teams
        :param from_id: From ID which needs to be subtracted from the participants
        :return parsed list of to ids
        """
        if pd.isna(participants):
            return []

        participant_names: List[str] = []

        for participant in participants[0].get(ParticipantsSourceColumns.PARTICIPANT):
            name = participant.get(ParticipantsSourceColumns.NAME, [{}])[0].get(
                ParticipantsSourceColumns.TEXT
            )

            participant_names.append(name)

        # Filter out Nones (present in participants where the name field is not present)
        # and remove the from_id from participant_names
        unique_to_ids: List[str] = list(set(filter(None, participant_names)) - {from_id})

        return sorted(unique_to_ids)

    def _post_process(self):
        """Not implemented."""

    def _attachments(self):
        """Not implemented."""

    def _call_duration_speaking(self):
        """Not implemented."""

    def _call_type(self):
        """Not implemented."""

    def _charge(self):
        """Not implemented."""

    def _connected(self):
        """Not implemented."""

    def _direction(self):
        """Not implemented."""

    def _identifiers_host_id(self):
        """Not implemented."""

    def _meta_model(self):
        """Not implemented."""

    def _fault(self):
        """Not implemented."""

    def _has_attachment(self):
        """Not implemented."""

    def _identifiers_all_domains(self):
        """Not implemented."""

    def _identifiers_bcc_ids(self):
        """Not implemented."""

    def _identifiers_cc_ids(self):
        """Not implemented."""

    def _identifiers_from_ip(self):
        """Not implemented."""

    def _identifiers_from_user_id(self):
        """Not implemented."""

    def _identifiers_on_behalf_of(self):
        """Not implemented."""

    def _identifiers_to_ip(self):
        """Not implemented."""

    def _identifiers_to_user_id(self):
        """Not implemented."""

    def _internal(self):
        """Not implemented."""

    def _is_multi_channel(self):
        """Not implemented."""

    def _is_dealer_board(self):
        """Not implemented."""

    def _join_reason(self):
        """Not implemented."""

    def _metadata_content_type(self):
        """Not implemented."""

    def _metadata_encoding_type(self):
        """Not implemented."""

    def _metadata_header(self):
        """Not implemented."""

    def _metadata_in_reply_to(self):
        """Not implemented."""

    def _metadata_message_id(self):
        """Not implemented."""

    def _metadata_reference_id(self):
        """Not implemented."""

    def _metadata_size_in_bytes(self):
        """Not implemented."""

    def _metadata_source_device_type(self):
        """Not implemented."""

    def _rate(self):
        """Not implemented."""

    def _timestamps_created(self):
        """Not implemented."""

    def _timestamps_duration_unit(self):
        """Not implemented."""

    def _timestamps_duration_value(self):
        """Not implemented."""

    def _transcribed(self):
        """Not implemented."""

    def _voice_file(self):
        """Not implemented."""

    def _is_transcription_excluded(self) -> pd.Series:
        raise NotImplementedError("Not in use.")

    def _transcription_exclusion_reason(self) -> pd.Series:
        raise NotImplementedError("Not in use.")
