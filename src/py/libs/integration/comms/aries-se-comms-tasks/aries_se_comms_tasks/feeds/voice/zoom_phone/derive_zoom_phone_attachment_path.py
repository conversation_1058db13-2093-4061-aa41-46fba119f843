# type: ignore

import logging
import pandas as pd
from aries_se_comms_tasks.feeds.voice.zoom_phone.static import (
    ATTACHMENTS_FOLDER_NAME,
    TRANSCRIPT_FILE_SUFFIX,
    CallLogColumns,
    TempColumns,
)
from aries_se_core_tasks.core.integration_task import IntegrationTask
from aries_se_core_tasks.core.params import TaskParams
from cloud_utils.cloud import cloud_list_files
from pydantic import Field
from se_enums.cloud import Cloud<PERSON><PERSON>iderEnum
from typing import Optional
from urllib.parse import urlparse

logger_ = logging.getLogger(__name__)


class Params(TaskParams):
    recording_file_extension: str = Field(
        ".mp3",
        description="Recording file extension",
    )
    metadata_file_name: str = Field(
        "call_logs.json",
        description="Source metadata file name",
    )


class DeriveZoomPhoneAttachmentPath(IntegrationTask):
    """This task derives the cloud path of the attachments(audio file and
    transcript) using the CallLogColumns.ID field in the metadata file."""

    def _run(
        self,
        source_frame: pd.DataFrame,
        file_url: str,
        realm: str,
        params: Params,
        cloud_provider: CloudProviderEnum,
    ) -> pd.DataFrame:
        return self.process(
            source_frame=source_frame,
            file_url=file_url,
            realm=realm,
            params=params,
            cloud_provider=cloud_provider,
        )

    @classmethod
    def process(
        cls,
        source_frame: pd.DataFrame,
        file_url: str,
        realm: str,
        cloud_provider: CloudProviderEnum,
        params: Params,
    ):
        df = cls._required_cols(source_frame=source_frame)
        target_df = pd.DataFrame(
            index=source_frame.index,
            columns=[
                TempColumns.RECORDING_PATH,
                TempColumns.TRANSCRIPT_PATH,
                TempColumns.SOURCE_FILE_PATH,
            ],
            data=pd.NA,
        )
        # Attachments folder in the same level as the file_url
        attachments_path = file_url.replace(params.metadata_file_name, ATTACHMENTS_FOLDER_NAME)

        recording_exist_mask = (df[CallLogColumns.RECORDING_ID].notnull()) | (
            df[CallLogColumns.VOICE_MAIL_ID].notnull()
        )
        target_df.loc[:, TempColumns.RECORDING_PATH] = df.loc[recording_exist_mask].apply(
            lambda x: cls._recording_file_url(
                row=x,
                attachments_path=attachments_path,
                bucket=realm,
                params=params,
                cloud_provider=cloud_provider,
            ),
            axis=1,
        )

        target_df.loc[:, TempColumns.TRANSCRIPT_PATH] = (
            attachments_path
            + df.loc[:, CallLogColumns.ID]
            + "/"
            + df.loc[:, CallLogColumns.ID]
            + TRANSCRIPT_FILE_SUFFIX
        )

        target_df.loc[:, TempColumns.SOURCE_FILE_PATH] = file_url

        return target_df

    @classmethod
    def _recording_file_url(
        cls,
        row: pd.Series,
        attachments_path: str,
        bucket: str,
        params: Params,
        cloud_provider: CloudProviderEnum,
    ) -> str:
        """
        :param row: pd.Series
        :param attachments_path: str
        :param bucket: str
        :param params: Params
        :return: str
        Derive Recording(audio) file url using CallLogColumns.ID and
        attachments_path
        """
        recordings_path = f"{attachments_path}{row.get(CallLogColumns.ID)}/"
        recordings_prefix = urlparse(recordings_path).path[1:]
        recording_key = cls._recording_key(
            bucket=bucket, prefix=recordings_prefix, params=params, cloud_provider=cloud_provider
        )
        if not recording_key:
            logger_.warning(f"Recording file not found for {row.get(CallLogColumns.ID)}")
            return pd.NA

        return f"{recordings_path}{recording_key}"

    @staticmethod
    def _recording_key(
        bucket: str, prefix: str, params: Params, cloud_provider: CloudProviderEnum
    ) -> Optional[str]:
        """
        :param bucket: str
        :param prefix: str
        :param logger: logging.logger
        :param params: Params
        :return: Optional[str]
        Search recording audio file and return key if found
        """
        files_in_path = cloud_list_files(
            cloud_provider=cloud_provider,
            bucket=bucket,
            folder_path=prefix,
            return_absolute_path=False,
            return_files_only=True,
        )
        if not files_in_path:
            return
        for file_name in files_in_path:
            if file_name.endswith(params.recording_file_extension):
                return file_name
        return

    @classmethod
    def _required_cols(cls, source_frame: pd.DataFrame) -> pd.DataFrame:
        cols_used = [CallLogColumns.ID, CallLogColumns.VOICE_MAIL_ID, CallLogColumns.RECORDING_ID]
        df = source_frame.loc[:, source_frame.columns.intersection(cols_used)]
        for col in cols_used:
            if col not in df.columns:
                df.loc[:, col] = pd.NA
        return df


def run_derive_zoom_phone_attachment_path(
    source_frame: pd.DataFrame,
    file_url: str,
    realm: str,
    cloud_provider: CloudProviderEnum,
    params: Params = Params(),
    app_metrics_path: Optional[str] = None,
    audit_path: Optional[str] = None,
    **kwargs,
):
    task = DeriveZoomPhoneAttachmentPath(app_metrics_path=app_metrics_path, audit_path=audit_path)
    return task.run(
        source_frame=source_frame,
        file_url=file_url,
        realm=realm,
        cloud_provider=cloud_provider,
        params=params,
        **kwargs,
    )
