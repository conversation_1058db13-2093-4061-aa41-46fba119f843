# type: ignore
import addict
import fsspec
import humanize
import json
import logging
import nanoid
import os
import pandas as pd
import psutil
import shutil
import xmltodict
from aries_se_comms_tasks.comms_app_metrics_enum import CommsAppMetricsEnum
from aries_se_comms_tasks.feeds.bloomberg.exceptions import InvalidXML
from aries_se_comms_tasks.feeds.bloomberg.utils import parse_parts
from aries_se_comms_tasks.monitored_users.bloomberg.bloomberg_monitored_users import (
    BloombergMonitoredUsers,
)
from aries_se_comms_tasks.monitored_users.bloomberg.static import BloombergFileType
from aries_se_core_tasks.core.generic_app_metrics_enum import GenericAppMetricsEnum
from aries_se_core_tasks.core.integration_task import IntegrationTask
from aries_se_core_tasks.utilities.data_utils import ensure_list
from aries_se_core_tasks.utilities.s3_utils import get_es_model_bucket_key_from_url
from aries_task_link.models import AriesTaskInput
from datetime import datetime
from integration_audit.auditor import upsert_audit
from pathlib import Path
from pyexpat import ExpatError
from se_elastic_schema.models import Email
from se_elastic_schema.static.reference import ImAccountType
from se_io_utils.tempfile_utils import tmp_directory
from typing import Dict, List, Optional, Tuple

logger_ = logging.getLogger(__name__)


class DownloadAndTransformBbgMail(IntegrationTask):
    def _run(
        self,
        aries_task_input: AriesTaskInput,
        mail_file: str,
        attachment_files: List[str],
        monitored_users: BloombergMonitoredUsers,
        streamed: bool,
        source_file_uri: str,
        bbg_email_over_corporate_email: bool = False,
    ) -> Tuple[Optional[str], int, int]:
        self.bbg_email_over_corporate_email = bbg_email_over_corporate_email
        self._attachment_files = attachment_files
        self._n_transformed_messages = 0
        self.monitored_users: BloombergMonitoredUsers = monitored_users
        self._n_non_monitored_conversations = 0

        self.source_file_uri: str = source_file_uri

        trace_id = aries_task_input.workflow.trace_id

        self._source_bucket, self._source_key = get_es_model_bucket_key_from_url(mail_file)

        logger_.info(f"Transforming mail file: {mail_file}")

        fs, _, (_,) = fsspec.get_fs_token_paths(mail_file)

        logger_.info(
            f"current memory usage before reading file: "
            f"{humanize.naturalsize(psutil.Process(os.getpid()).memory_info().rss)} "
        )

        tmp_dir: Path = tmp_directory()
        output_file_path = tmp_dir.joinpath(
            f"{trace_id}_{nanoid.generate(size=6)}_transformed_messages.ndjson"
        )

        with open(output_file_path, "w") as self._transformed_messages_f_out:
            with fs.open(mail_file, "rb") as fin_xml:
                try:
                    xmltodict.parse(xml_input=fin_xml, item_depth=2, item_callback=self.handle)
                except ExpatError as e:
                    logger_.error(f"Invalid XML in file: {mail_file} - {e}")
                    raise InvalidXML(e)

        logger_.info(
            f"current memory usage after parsing file: "
            f"{humanize.naturalsize(psutil.Process(os.getpid()).memory_info().rss)} "
        )

        logger_.info(f"Transformed {self._n_transformed_messages} messages")

        self.update_app_metrics(
            field=GenericAppMetricsEnum.INPUT_COUNT,
            value=self._n_transformed_messages + self._n_non_monitored_conversations,
        )

        if self._n_non_monitored_conversations > 0:
            audit_message = (
                f"{self._n_non_monitored_conversations} non-monitored email(s) were skipped."
            )

            logger_.info(audit_message)

            self.update_app_metrics(
                field=CommsAppMetricsEnum.NON_MONITORED_CONVERSATIONS_COUNT,
                value=self._n_non_monitored_conversations,
            )

            upsert_audit(
                audit_path=self.audit_path,
                streamed=streamed,
                workflow_status=[audit_message],
            )

        if self._n_transformed_messages == 0:
            # If there's no messages, then there's no need to keep an empty file
            shutil.rmtree(tmp_dir)

        return (
            # If the file is empty, there is no need to return an empty file path
            self._transformed_messages_f_out.name if self._n_transformed_messages > 0 else None,
            self._n_transformed_messages,
            self._n_non_monitored_conversations,
        )

    def handle(self, path, item):
        if path[1][0] == "Message":
            # Only add to self._conversation_batch if there is at lest one monitored user
            if self.monitored_users.is_monitored_conversation(
                conversation_dict=item, file_type=BloombergFileType.MSG
            ):
                record = self.transform_extracted_messages(item)

                self._n_transformed_messages += 1

                self._transformed_messages_f_out.write(json.dumps(record, separators=(",", ":")))
                self._transformed_messages_f_out.write("\n")

            else:
                self._n_non_monitored_conversations += 1

        return True

    def transform_extracted_messages(self, xml_dict: Dict) -> Dict:
        record = self._transform_xml_to_model(xml_dict)

        Email.validate(record)

        return record

    @staticmethod
    def get_addresses(msg: Dict, bbg_email_over_corporate_email: bool) -> Dict:
        to_ids = list()
        bcc_ids = list()
        cc_ids = list()
        to_uuids = list()

        for recipient in ensure_list(msg, "Recipient"):
            delivery_type = recipient.get("@DeliveryType")

            corp_to_email = recipient.get("UserInfo", {}).get("CorporateEmailAddress")
            bloomberg_to_email = recipient.get("UserInfo", {}).get("BloombergEmailAddress")

            primary_to_email = (
                (bloomberg_to_email or corp_to_email)
                if bbg_email_over_corporate_email
                else (corp_to_email or bloomberg_to_email)
            )

            if not primary_to_email:
                continue

            if delivery_type == "BCC":
                bcc_ids.append(primary_to_email)
            elif delivery_type == "CC":
                cc_ids.append(primary_to_email)
            elif delivery_type == "TO":
                to_ids.append(primary_to_email)

            to_uuid = recipient.get("UserInfo", {}).get("BloombergUUID")
            if to_uuid:
                to_uuids.append(
                    {
                        "communications": {
                            "imAccounts": [{"id": to_uuid, "label": ImAccountType.BBG.value}]
                        }
                    }
                )

        from_uuid = msg.get("Sender", {}).get("UserInfo", {}).get("BloombergUUID")
        from_ = None
        if from_uuid:
            from_ = {
                "communications": {
                    "imAccounts": [{"id": from_uuid, "label": ImAccountType.BBG.value}]
                }
            }
        corp_from_email = msg.get("Sender", {}).get("UserInfo", {}).get("CorporateEmailAddress")
        bloomberg_from_email = (
            msg.get("Sender", {}).get("UserInfo", {}).get("BloombergEmailAddress")
        )

        primary_from_email = (
            (bloomberg_from_email or corp_from_email)
            if bbg_email_over_corporate_email
            else (corp_from_email or bloomberg_from_email)
        )
        addresses = dict(
            FROM=primary_from_email,
            BEHALF=(
                msg["OrigSender"]["UserInfo"]["BloombergEmailAddress"]
                if "OrigSender" in msg
                else None
            ),
            TO=to_ids,
            CC=cc_ids,
            BCC=bcc_ids,
            FROM_UUID=from_,
            TO_UUIDS=to_uuids,
        )

        return addresses

    def _transform_xml_to_model(self, message: Dict) -> addict.Dict:
        addresses = self.get_addresses(
            msg=message, bbg_email_over_corporate_email=self.bbg_email_over_corporate_email
        )

        domains = dict()
        local_parts = dict()

        for participant_type, email_ids in addresses.items():
            if email_ids is None:
                continue

            email_ids = [email_ids] if not isinstance(email_ids, list) else email_ids

            for email_id in email_ids:
                if (
                    email_id is not None
                    and "@" in email_id
                    and 0 < email_id.index("@") < len(email_id) - 1
                ):
                    local_part, domain = email_id.split("@")

                    local_parts[local_part] = (
                        local_parts[local_part] if local_part in local_parts else set()
                    )

                    local_parts[local_part].add(participant_type)

                    domains[domain] = domains[domain] if domain in domains else set()

                    domains[domain].add(participant_type)

        dt_format = "%Y-%m-%dT%H:%M:%S"
        utc = datetime.utcfromtimestamp(int(message.get("MsgTimeUTC"))).strftime(dt_format)

        local = datetime.strptime(message.get("MsgTime"), "%Y-%m-%d-%H.%M.%S.%f").strftime(
            dt_format
        )

        all_ids: List[Optional[str]] = [
            addresses.get("FROM", None),
            *(addresses.get("TO", []) or []),
            *(addresses.get("CC", []) or []),
            *(addresses.get("BCC", []) or []),
            addresses.get("BEHALF", None),
        ]

        all_ids = [id_ for id_ in all_ids if id_ is not None and not pd.isna(id_)]

        all_ids = sorted(list(set(all_ids)))

        corp_from_email = message.get("Sender", {}).get("UserInfo", {}).get("CorporateEmailAddress")
        bloomberg_from_email = (
            message.get("Sender", {}).get("UserInfo", {}).get("BloombergEmailAddress")
        )
        from_user_id = (
            (corp_from_email or bloomberg_from_email)
            if self.bbg_email_over_corporate_email
            else (bloomberg_from_email or corp_from_email)
        )

        record = addict.Dict(
            {
                "sourceKey": self.source_file_uri,
                "metadata": {
                    "threadId": message.get("MsgID"),
                    "messageId": message.get("MsgID"),
                    "encodingType": message.get("MsgLang"),
                    "source": {
                        "client": "Bloomberg Message",
                        "fileInfo": {
                            "location": {"bucket": self._source_bucket, "key": self._source_key}
                        },
                    },
                },
                "subject": message.get("Subject"),
                "body": {
                    "displayText": message.get("MsgBody"),
                    "text": message.get("MsgBody"),
                    "type": "PLAIN",
                },
                "participants": list(),
                "timestamps": {"timestampStart": utc, "localTimestampStart": local},
                "identifiers": dict(
                    allIds=all_ids,
                    fromId=addresses.get("FROM"),
                    fromUserId=from_user_id,
                    from_=addresses.get("FROM_UUID"),
                    toIds=addresses.get("TO"),
                    to=addresses.get("TO_UUIDS"),
                    ccIds=addresses.get("CC"),
                    bccIds=addresses.get("BCC"),
                    onBehalfOf=addresses.get("BEHALF"),
                    domains=[dict(value=k, types=list(v)) for k, v in domains.items()],
                    localParts=[dict(value=k, types=list(v)) for k, v in local_parts.items()],
                ),
            }
        )

        record.attachments = []

        for attachment_xml_info in ensure_list(message, "Attachment"):
            file_id = attachment_xml_info.pop("FileID", None)

            if file_id:
                for attachment_file in self._attachment_files:
                    if file_id not in attachment_file:
                        continue

                    attachment = addict.Dict()

                    attachment.fileName = attachment_xml_info.get("FileName")

                    attachment.fileType = (
                        attachment.fileName.split(".")[-1]
                        if (attachment.fileName and "." in attachment.fileName)
                        else None
                    )

                    attachment.sizeInBytes = attachment.fileInfo.contentLength = int(
                        attachment_xml_info.get("FileSize", 0)
                    )

                    (
                        attachment.fileInfo.location.bucket,
                        attachment.fileInfo.location.key,
                    ) = get_es_model_bucket_key_from_url(attachment_file)

                    attachment.fileInfo.processed = datetime.utcnow().isoformat()
                    record.attachments.append(attachment)

            record.hasAttachment = len(record.attachments) > 0

            ds, lp = parse_parts(record)

            record.identifiers.domains = ds
            record.identifiers.localParts = lp

        return record


def run_download_and_transform_bbg_mail(
    aries_task_input: AriesTaskInput,
    mail_file: str,
    attachment_files: List[str],
    monitored_users: BloombergMonitoredUsers,
    streamed: bool,
    source_file_uri: str,
    bbg_email_over_corporate_email: bool = False,
    app_metrics_path: Optional[str] = None,
    audit_path: Optional[str] = None,
):
    task = DownloadAndTransformBbgMail(app_metrics_path=app_metrics_path, audit_path=audit_path)
    return task.run(
        aries_task_input=aries_task_input,
        mail_file=mail_file,
        attachment_files=attachment_files,
        monitored_users=monitored_users,
        streamed=streamed,
        source_file_uri=source_file_uri,
        bbg_email_over_corporate_email=bbg_email_over_corporate_email,
    )
