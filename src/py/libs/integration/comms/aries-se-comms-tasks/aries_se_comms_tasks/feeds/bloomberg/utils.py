import chardet
import re
from addict import Dict as AddDict
from collections import defaultdict
from typing import Any, Dict, List, Optional, Tuple, Union


def split_s3_path(s3_path: str) -> Tuple[str, str]:
    if s3_path.startswith("s3://"):
        s3_path = s3_path[5:]
    bucket, key = s3_path.split("/", 1)
    return bucket, key


def is_instantbloomberg(file_name: str) -> bool:
    return ".ib." in file_name or (".IB." in file_name and file_name.endswith(".xml"))


def is_msg(file_name: str) -> bool:
    file_name = file_name.lower()
    return ".msg." in file_name or (".b." in file_name and file_name.endswith(".xml"))


def is_attachment(file_name: str) -> bool:
    file_name = file_name.lower()
    return "bloomberg_attachment" in file_name or (
        ".b." in file_name and file_name.endswith(".att.tar.gz")
    )


def get_domain(address: str) -> str:
    return address.split("@")[-1]


def get_local_part(address: str) -> str:
    return address.split("@")[0]


def valid_email_address(email: str) -> bool:
    email_address_pattern = r"(?P<id>[^@]+)@(?P<domain>[^@]+\.[^@]+)"
    # check dtype for unexpected None types from source data
    return bool(isinstance(email, str) and re.match(email_address_pattern, email))


def parse_parts(email: AddDict) -> Tuple[List[Dict], List[Dict]]:
    domains = defaultdict(set)
    local_parts = defaultdict(set)
    if valid_email_address(email.identifiers.fromId):
        from_id = email.identifiers.fromId
        domains[get_domain(from_id)].add("FROM")
        local_parts[get_local_part(from_id)].add("FROM")

    if valid_email_address(email.identifiers.onBehalfOf):
        behalf = email.identifiers.onBehalfOf
        domains[get_domain(behalf)].add("BEHALF")
        local_parts[get_local_part(behalf)].add("BEHALF")

    for address in email.identifiers.toIds:
        if valid_email_address(address):
            domains[get_domain(address)].add("TO")
            local_parts[get_local_part(address)].add("TO")

    for address in email.identifiers.ccIds:
        if valid_email_address(address):
            domains[get_domain(address)].add("CC")
            local_parts[get_local_part(address)].add("CC")

    for address in email.identifiers.bccIds:
        if valid_email_address(address):
            domains[get_domain(address)].add("BCC")
            local_parts[get_local_part(address)].add("BCC")

    return (
        [{"value": domain, "types": sorted(list(types))} for domain, types in domains.items()],
        [
            {"value": local_part, "types": sorted(list(types))}
            for local_part, types in local_parts.items()
        ],
    )


def rfc5322(inp: Union[str, None]) -> str:
    inp = inp or ""
    inp = inp if isinstance(inp, str) else object2string(inp)
    val = "".join(c for c in inp if is_rfc5322_char(c))
    return val.lower()


def object2string(obj: Optional[Any], encoding="utf-8") -> Optional[str]:
    """Cast Any object to string, unless it's None.

    :param: obj: thing to stringify
    :type: obj: Optional[Any]
    :param: encoding: Optional, Defaults to 'utf-8'.
    :type: str

    :returns: stringified object or None
    :rtype:Optional[str]:
    """
    if obj is None:
        return None

    try:
        val = str(obj, encoding)
    except Exception:
        try:
            encoding = chardet.detect(obj).get("encoding", "utf-8")
            val = str(obj, encoding)
        except Exception:
            val = str(obj)
    return val


def is_rfc5322_char(inp: str) -> bool:
    rfc5322_pattern = "!#$%&*+-/=?^_{|}~@."
    return inp.isalnum() or inp in rfc5322_pattern
