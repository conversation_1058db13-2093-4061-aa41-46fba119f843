from aries_se_core_tasks.utilities.data_utils import BaseColumns
from typing import Dict, List


class SourceColumns(BaseColumns):
    ATTACHMENT_ID: str = "attachment id"
    CHAT_TYPE: str = "Chat Type"
    DATA_SOURCE_NAME: str = "Data Source Name"
    EVENT_DATE: str = "Event Date (YYYYMMDD)"
    EVENT_TIME: str = "Event Time (HH:MM:SS)"
    EVENT_TYPE: str = "Event Type"
    FROM_DEVICE_ID: str = "From Device Id"
    FROM_ID: str = "From Id"
    MESSAGE: str = "Message"
    MESSAGE_ID: str = "Message Id"
    ON_BEHALF_OF: str = "On Behalf Of"
    ROOM_ID: str = "Room Id"
    ROOM_NAME: str = "Room Name"
    TO_IDS: str = "To Ids"

    UNIQUE_MESSAGE_COLUMNS: List[str] = [EVENT_TYPE, ROOM_ID, MESSAGE, FROM_ID]


class TempColumns(BaseColumns):
    AGGREGATED_TO_IDS: str = "__aggregated_to_ids__"
    AGGREGATE_COUNT: str = "__aggregate_count__"
    ALTERNATIVE_MESSAGE_ID: str = "__alternative_message_id__"
    COMMA_JOINED_TO_IDS: str = "__comma_joined_to_ids__"
    DATETIME: str = "__datetime__"
    DATETIME_SORTING: str = "__datetime_sorting__"
    MESSAGE_ID: str = "__message_id__"
    MESSAGE_ORIGINAL: str = "__message_original__"
    SOURCE_INDEX: str = "__source_index__"
    SPLIT_TO_IDS: str = "__split_to_ids__"


class EventTypes:
    ATTACHMENT: str = "ATTACHMENT"
    ENTERED: str = "ENTERED"
    INVITED: str = "INVITED"
    LEFT: str = "LEFT"
    MESSAGE: str = "MESSAGE"
    ROOM_CLOSE: str = "ROOM_CLOSE"
    ROOM_OPEN: str = "ROOM_OPEN"

    MESSAGE_EVENTS: List[str] = [ATTACHMENT, MESSAGE]


MESSAGES_QUERY: str = f"`{SourceColumns.EVENT_TYPE}`.isin({EventTypes.MESSAGE_EVENTS})"

CHAT_EVENTS_QUERY: str = f"~({MESSAGES_QUERY})"

SOURCE_SCHEMA: Dict[str, str] = {x: "string" for x in SourceColumns.all() if isinstance(x, str)}

MESSAGE_MAPPINGS_NAME: str = "steeleye_universal_chat_message_mappings"

CHAT_EVENT_MAPPINGS_NAME: str = "steeleye_universal_chat_event_mappings"

DATETIME_FORMAT = "%Y%m%d %H:%M:%S"

DATA_SOURCE_NAME = "SteelEye Chat Template"
