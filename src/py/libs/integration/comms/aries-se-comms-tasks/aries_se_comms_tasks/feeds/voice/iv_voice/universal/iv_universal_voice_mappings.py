# mypy: disable-error-code="no-any-return,attr-defined"
import logging
import numpy as np
import pandas as pd
from aries_se_comms_tasks.abstractions.transformations.abstract_voice_transformations import (
    AbstractVoiceTransformations,
)
from aries_se_comms_tasks.feeds.voice.iv_voice.static import IV_UNIVERSAL_FLOW_NAME
from aries_se_comms_tasks.feeds.voice.iv_voice.universal.static import (
    FileUrlColumns,
    IvUniversalDataSourceFolders,
    IvUniversalDataSourceNames,
    IVUniversalSourceColumns,
    IVUniversalTempColumns,
)
from aries_se_comms_tasks.generic.participants.participant_identifiers import (
    Params as ParamsParticipantIdentifiers,
)
from aries_se_comms_tasks.generic.participants.participant_identifiers import ParticipantIdentifiers
from aries_se_comms_tasks.voice.static import WAVEFORM_FILE_NAME, WAVEFORM_FILE_PREFIX, CallColumns
from aries_se_core_tasks.datetime.convert_datetime import Params as ParamsConvertDatetime
from aries_se_core_tasks.datetime.convert_datetime import run_convert_datetime
from aries_se_core_tasks.datetime.convert_timedelta import Params as ParamsConvertTimedelta
from aries_se_core_tasks.datetime.convert_timedelta import run_convert_timedelta
from aries_se_core_tasks.datetime.datetime_difference import Params as ParamsDatetimeDifference
from aries_se_core_tasks.datetime.datetime_difference import run_datetime_difference
from aries_se_core_tasks.transform.map.map_value import Params as ParamsMapValue
from aries_se_core_tasks.transform.map.map_value import run_map_value
from pathlib import Path
from se_core_tasks.datetime.convert_datetime import ConvertTo
from se_core_tasks.datetime.convert_timedelta import Unit
from se_core_tasks.utils.datetime import DatetimeFormat
from se_elastic_schema.static.communication import CallDirectionEnum
from typing import Dict

logger = logging.getLogger(__name__)


class IvUniversalVoiceMappings(AbstractVoiceTransformations):
    def __init__(
        self,
        source_frame: pd.DataFrame,
        realm: str,
        batch_file_url: str,
        recording_to_zip_map: Dict[str, str],
        **kwargs,
    ):
        super().__init__(source_frame=source_frame, logger=logger, realm=realm)
        self.file_url: str = batch_file_url
        self.recording_to_zip_map: Dict[str, str] = recording_to_zip_map

    def _pre_process(self) -> None:
        """Calls participant identifiers."""
        self.pre_process_df = pd.concat(  # type: ignore[call-overload]
            [self.pre_process_df, self._call_participant_identifiers()],  # type: ignore[has-type]
            axis=1,
        )

    def process(self):
        self.pre_process()
        self.connected()
        self.direction()
        self.has_attachment()
        self.id()
        self.identifiers_all_country_codes()
        self.identifiers_all_ids()
        self.identifiers_from_id()
        self.identifiers_from_id_addl_info()
        self.identifiers_to_ids()
        self.identifiers_to_ids_addl_info()
        self.metadata_source_client()
        self.metadata_source_file_info_location_bucket()
        self.metadata_source_file_info_location_key()
        self.source_index()
        self.source_key()
        self.timestamps_local_timestamp_start()
        self.timestamps_timestamp_start()
        self.timestamps_local_timestamp_end()
        self.timestamps_timestamp_end()
        self.timestamps_timestamp_connected()
        self.call_duration()  # Needs timestamps
        self.waveform_file_info_location_bucket()
        self.waveform_file_info_location_key()
        self.post_process()
        return self.target_df

    def _post_process(self) -> None:
        """Not Implemented."""

    def _call_duration(self) -> pd.Series:
        """Gets the call duration."""
        call_duration_series = run_convert_timedelta(
            source_frame=self.source_frame,
            params=ParamsConvertTimedelta(
                source_attribute=IVUniversalSourceColumns.METADATA_DURATION,
                target_attribute=CallColumns.CALL_DURATION,
                source_unit=Unit.MILLISECONDS.value,
            ),
            skip_serializer=True,
        ).loc[:, CallColumns.CALL_DURATION]
        call_duration_null_mask = call_duration_series.isnull()

        fallback_call_duration_series = run_datetime_difference(
            source_frame=self.target_df.loc[call_duration_null_mask],
            params=ParamsDatetimeDifference(
                start_column=CallColumns.TIMESTAMPS_TIMESTAMP_START,
                end_column=CallColumns.TIMESTAMPS_TIMESTAMP_END,
                start_format=DatetimeFormat.DATETIME,
                end_format=DatetimeFormat.DATETIME,
                format_output=True,
                target_column=CallColumns.CALL_DURATION,
            ),
            skip_serializer=True,
        )[CallColumns.CALL_DURATION]
        return call_duration_series.fillna(fallback_call_duration_series)

    def _connected(self) -> pd.Series:
        """Set to True (as all the audio files are present in a zip file, and
        the metadata is derived from the audio files)"""
        return pd.Series(index=self.source_frame.index, data=True)

    def _direction(self) -> pd.Series:
        """Populates the direction of the call."""
        return run_map_value(
            source_frame=self.source_frame,
            params=ParamsMapValue(
                source_attribute=IVUniversalSourceColumns.METADATA_DIRECTION,
                target_attribute=CallColumns.DIRECTION,
                case_insensitive=True,
                value_map={
                    "out": CallDirectionEnum.OUTGOING.value,
                    "in": CallDirectionEnum.INCOMING.value,
                },
            ),
            skip_serializer=True,
        ).loc[:, CallColumns.DIRECTION]

    def _has_attachment(self) -> pd.Series:
        """Set to True (as all the audio files are present in a zip file, and
        the metadata is derived from the audio files)"""
        return pd.Series(index=self.source_frame.index, data=True)

    def _id(self) -> pd.Series:
        """Assigns the id from the Recording.id."""
        return self.source_frame.loc[:, IVUniversalSourceColumns.RECORDING_ID]

    def _identifiers_all_country_codes(self) -> pd.Series:
        """Returns a series containing
        CallColumns.IDENTIFIERS_ALL_COUNTRY_CODES."""
        return self.pre_process_df.loc[:, CallColumns.IDENTIFIERS_ALL_COUNTRY_CODES]

    def _identifiers_all_ids(self) -> pd.Series:
        """Returns a series containing CallColumns.IDENTIFIERS_ALL_IDS."""
        return self.pre_process_df.loc[:, CallColumns.IDENTIFIERS_ALL_IDS]

    def _identifiers_from_id(self) -> pd.Series:
        """Returns a series containing CallColumns.IDENTIFIERS_FROM_ID."""
        return self.pre_process_df.loc[:, CallColumns.IDENTIFIERS_FROM_ID]

    def _identifiers_from_id_addl_info(self) -> pd.Series:
        """Returns a series containing
        CallColumns.IDENTIFIERS_FROM_ADDL_INFO."""
        return self.pre_process_df.loc[:, CallColumns.IDENTIFIERS_FROM_ADDL_INFO]

    def _identifiers_to_ids(self) -> pd.Series:
        """Returns a series containing CallColumns.IDENTIFIERS_TO_IDS."""
        return self.pre_process_df.loc[:, CallColumns.IDENTIFIERS_TO_IDS]

    def _identifiers_to_ids_addl_info(self) -> pd.Series:
        """Returns a series containing CallColumns.IDENTIFIERS_TO_ADDL_INFO."""
        return self.pre_process_df.loc[:, CallColumns.IDENTIFIERS_TO_ADDL_INFO]

    def _metadata_source_client(self) -> pd.Series:
        """Populates the metadata source client based on the sub-folder in the
        zip file path.

        The zip file path is obtained from the recording local file path
        via self.recording_to_zip_map
        """
        result = pd.Series(pd.NA, index=self.source_frame.index)
        zip_file_series = self.source_frame.loc[:, FileUrlColumns.RECORDING_LOCAL_FILE_PATH].map(
            self.recording_to_zip_map
        )
        conditions = [
            zip_file_series.str.contains(IvUniversalDataSourceFolders.IPC_FOLDER),
            zip_file_series.str.contains(IvUniversalDataSourceFolders.MS_TEAMS_FOLDER),
        ]

        choices = [IvUniversalDataSourceNames.IPC_UNIGY, IvUniversalDataSourceNames.MS_TEAMS]
        result.loc[:] = np.select(  # type: ignore[call-overload]
            conditions, choices, default=IvUniversalDataSourceNames.INTELLIGENT_VOICE
        )
        return result

    def _metadata_source_file_info_location_bucket(self) -> pd.Series:
        """Populates the bucket of the metadata file."""
        return self.source_frame.loc[:, FileUrlColumns.METADATA_FILE_URL].apply(
            lambda x: Path(x).parts[1]
        )

    def _metadata_source_file_info_location_key(self) -> pd.Series:
        """Populates the key of the metadata file."""
        return self.source_frame.loc[:, FileUrlColumns.METADATA_FILE_URL].apply(
            lambda x: "/".join(Path(x).parts[2:])
        )

    def _source_index(self) -> pd.Series:
        """Returns a Series containing the sourceIndex column."""
        return pd.Series(
            data=self.source_frame.index.values,
            index=self.source_frame.index,
        )

    def _source_key(self) -> pd.Series:
        """Returns the source file url of the batch ndjson file."""
        return pd.Series(data=self.file_url, index=self.source_frame.index)

    def _timestamps_local_timestamp_end(self) -> pd.Series:
        """Returns a Series containing
        CallColumns.TIMESTAMPS_LOCAL_TIMESTAMP_END.

        This datetime is not converted to UTC.

        NOTE: We can get times which are timezone-aware and times which
        are timezone-naive
        E.g. "2023-10-15T08:51:38.431000+0000" and "2023-10-15T08:51:38.431000"
        """
        # We don't use a datetime format as datetimes may or may not have
        # a tz component, and are therefore different formats

        return run_convert_datetime(
            source_frame=self.source_frame,
            params=ParamsConvertDatetime(
                source_attribute=IVUniversalSourceColumns.METADATA_END,
                target_attribute=CallColumns.TIMESTAMPS_LOCAL_TIMESTAMP_END,
                convert_to=ConvertTo.DATETIME,
            ),
            skip_serializer=True,
        )[CallColumns.TIMESTAMPS_LOCAL_TIMESTAMP_END]

    def _timestamps_local_timestamp_start(self) -> pd.Series:
        """Returns a Series containing
        CallColumns.TIMESTAMPS_LOCAL_TIMESTAMP_START.

        This datetime is not converted to UTC.

        NOTE: We can get times which are timezone-aware and times which
        are timezone-naive
        E.g. "2023-10-15T08:51:38.431000+0000" and "2023-10-15T08:51:38.431000"
        """
        # We don't use a datetime format as datetimes may or may not have
        # a tz component, and are therefore different formats
        return run_convert_datetime(
            source_frame=self.source_frame,
            params=ParamsConvertDatetime(
                source_attribute=IVUniversalSourceColumns.METADATA_START,
                target_attribute=CallColumns.TIMESTAMPS_LOCAL_TIMESTAMP_START,
                convert_to=ConvertTo.DATETIME,
            ),
            skip_serializer=True,
        )[CallColumns.TIMESTAMPS_LOCAL_TIMESTAMP_START]

    def _timestamps_timestamp_connected(self) -> pd.Series:
        """Populates a series for
        CallColumns.TIMESTAMPS_LOCAL_TIMESTAMP_CONNECTED from the already-
        populated CallColumns.TIMESTAMPS_TIMESTAMP_START.

        Expects timestamps_timestamp_start() to have been called earlier
        """
        return self.target_df.loc[:, CallColumns.TIMESTAMPS_TIMESTAMP_START]

    def _timestamps_timestamp_end(self) -> pd.Series:
        """Populates a series for CallColumns.TIMESTAMPS_TIMESTAMP_END.

        Unlike CallColumns.TIMESTAMPS_LOCAL_TIMESTAMP_START, this
        datetime is converted to UTC.

        NOTE: We can get times which are timezone-aware and times which
        are timezone-naive
        E.g. "2023-10-15T08:51:38.431000+0000" and "2023-10-15T08:51:38.431000"
        """
        # Use format="ISO8601" to handle both timezone-aware and timezone-naive times
        # (timezone-naive times are assumed to be UTC), timezone-aware times are
        # converted to UTC.
        return pd.to_datetime(
            self.source_frame[IVUniversalSourceColumns.METADATA_END], utc=True, format="ISO8601"
        ).dt.strftime(DatetimeFormat.DATETIME)

    def _timestamps_timestamp_start(self) -> pd.Series:
        """Populates a series for CallColumns.TIMESTAMPS_TIMESTAMP_START.

        Unlike CallColumns.TIMESTAMPS_LOCAL_TIMESTAMP_START, this
        datetime is converted to UTC.

        NOTE: We can get times which are timezone-aware and times which
        are timezone-naive
        E.g. "2023-10-15T08:51:38.431000+0000" and "2023-10-15T08:51:38.431000"
        """
        # Use format="ISO8601" to handle both timezone-aware and timezone-naive times
        # (timezone-naive times are assumed to be UTC), timezone-aware times are
        # converted to UTC.
        return pd.to_datetime(
            self.source_frame[IVUniversalSourceColumns.METADATA_START], utc=True, format="ISO8601"
        ).dt.strftime(DatetimeFormat.DATETIME)

    def _waveform_file_info_location_bucket(self) -> pd.Series:
        """Populates CallColumns.WAVEFORM_FILE_INFO_LOCATION_BUCKET."""
        return pd.Series(
            data=self.realm,
            index=self.source_frame.index,
        )

    def _waveform_file_info_location_key(self) -> pd.Series:
        """Populates CallColumns.WAVEFORM_FILE_INFO_LOCATION_KEY."""
        return (
            WAVEFORM_FILE_PREFIX
            + IV_UNIVERSAL_FLOW_NAME
            + "/"
            + self.target_df.loc[:, CallColumns.ID]
            + "/"
            + WAVEFORM_FILE_NAME
        )

    def _call_participant_identifiers(self) -> pd.DataFrame:
        """
        Derives the from and toids from the list of dicts column
        'Metadata.participants'.
        E.g.
        [
            {
                "id": 0,
                "userID": "<EMAIL>",
                "userName": "Abc Def",
            },
            {
                "id": 1,
                "userID": "ING_CRED_TRADING"
            },
            {
                "id": 1,
                "userID": "<EMAIL>",
                "userName": "Xyz Uvw",
            },
        ],

        Here, the first dict's userID is the from id and the
        userID values from all other dicts form the toIds

        After deriving the from and to Ids, this function calls
        ParticipantIdentifiers and returns the results in a data
        frame.


        """

        from_and_to_df = pd.DataFrame(index=self.source_frame.index)
        from_and_to_df[IVUniversalTempColumns.FROM_ID] = (
            self.source_frame.loc[:, IVUniversalSourceColumns.METADATA_PARTICIPANTS]
            .str[0]
            .str.get("userID")
        )
        from_and_to_df[IVUniversalTempColumns.TO_IDS] = (
            self.source_frame.loc[:, IVUniversalSourceColumns.METADATA_PARTICIPANTS]
            .str[1:]
            .apply(lambda x: [d.get("userID") for d in x])
        )

        return ParticipantIdentifiers.process(
            source_frame=from_and_to_df,
            params=ParamsParticipantIdentifiers(
                source_from_identifier=IVUniversalTempColumns.FROM_ID,
                source_to_identifiers=IVUniversalTempColumns.TO_IDS,
            ),
        )

    def _attachments(self) -> pd.Series:
        raise NotImplementedError("Not used.")

    def _call_duration_speaking(self) -> pd.Series:
        raise NotImplementedError("Not used.")

    def _call_type(self) -> pd.Series:
        raise NotImplementedError("Not used.")

    def _charge(self) -> pd.Series:
        raise NotImplementedError("Not used.")

    def _conference_call(self) -> pd.Series:
        raise NotImplementedError("Not used.")

    def _fault(self) -> pd.Series:
        raise NotImplementedError("Not used.")

    def _identifiers_all_domains(self) -> pd.Series:
        raise NotImplementedError("Not used.")

    def _identifiers_bcc_ids(self) -> pd.Series:
        raise NotImplementedError("Not used.")

    def _identifiers_cc_ids(self) -> pd.Series:
        raise NotImplementedError("Not used.")

    def _identifiers_from_device_id(self) -> pd.Series:
        raise NotImplementedError("Not used.")

    def _identifiers_from_ip(self) -> pd.Series:
        raise NotImplementedError("Not used.")

    def _identifiers_from_user_id(self) -> pd.Series:
        raise NotImplementedError("Not used.")

    def _identifiers_host_id(self) -> pd.Series:
        raise NotImplementedError("Not used.")

    def _identifiers_on_behalf_of(self) -> pd.Series:
        raise NotImplementedError("Not used.")

    def _identifiers_to_device_id(self) -> pd.Series:
        raise NotImplementedError("Not used.")

    def _identifiers_to_ip(self) -> pd.Series:
        raise NotImplementedError("Not used.")

    def _identifiers_to_user_id(self) -> pd.Series:
        raise NotImplementedError("Not used.")

    def _internal(self) -> pd.Series:
        raise NotImplementedError("Not used.")

    def _is_multi_channel(self) -> pd.Series:
        raise NotImplementedError("Not used.")

    def _is_dealer_board(self) -> pd.Series:
        raise NotImplementedError("Not used.")

    def _join_reason(self) -> pd.Series:
        raise NotImplementedError("Not used.")

    def _meta_model(self) -> pd.Series:
        raise NotImplementedError("Not used.")

    def _metadata_content_type(self) -> pd.Series:
        raise NotImplementedError("Not used.")

    def _metadata_encoding_type(self) -> pd.Series:
        raise NotImplementedError("Not used.")

    def _metadata_header(self) -> pd.Series:
        raise NotImplementedError("Not used.")

    def _metadata_in_reply_to(self) -> pd.Series:
        raise NotImplementedError("Not used.")

    def _metadata_message_id(self) -> pd.Series:
        raise NotImplementedError("Not used.")

    def _metadata_reference_id(self) -> pd.Series:
        raise NotImplementedError("Not used.")

    def _metadata_size_in_bytes(self) -> pd.Series:
        raise NotImplementedError("Not used.")

    def _metadata_source_device_type(self) -> pd.Series:
        raise NotImplementedError("Not used.")

    def _participants(self) -> pd.Series:
        raise NotImplementedError("Not used.")

    def _rate(self) -> pd.Series:
        raise NotImplementedError("Not used.")

    def _transcribed(self) -> pd.Series:
        raise NotImplementedError("Not used.")

    def _voice_file(self) -> pd.Series:
        raise NotImplementedError("Not used.")

    def _timestamps_created(self) -> pd.Series:
        raise NotImplementedError("Not used.")

    def _timestamps_duration_unit(self) -> pd.Series:
        raise NotImplementedError("Not used.")

    def _timestamps_duration_value(self) -> pd.Series:
        raise NotImplementedError("Not used.")

    def _is_transcription_excluded(self) -> pd.Series:
        raise NotImplementedError("Not in use.")

    def _transcription_exclusion_reason(self) -> pd.Series:
        raise NotImplementedError("Not in use.")
