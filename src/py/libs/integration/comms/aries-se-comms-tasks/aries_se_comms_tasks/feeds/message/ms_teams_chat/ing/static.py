from aries_se_comms_tasks.message.static import MessageColumns

ING_MS_TEAMS_CHAT_FLOW_NAME = "ing_ms_teams_chat_transform"
ING_MS_TEAMS_CHAT_EVENT_MAPPINGS_NAME = "ing_ms_teams_chat_event_mappings"
ING_MS_TEAMS_MESSAGE_MAPPINGS_NAME = "ing_ms_teams_chat_message_mappings"

ING_MS_TEAMS_DATETIME_FORMAT = "%Y.%m.%d %H:%M:%S.%f"


class TopLevelXmlKeys:
    IM = "im"
    VERBA_CDR = "verbacdr"


class VerbaCdrSourceKeys:
    CALLER_ID = "callerid"
    DESTINATION_CALLER_ID = "destination_caller_id"
    DESTINATION_NAME = "destination_name"
    NAME = "name"
    PARTICIPANT = "participant"
    PARTICIPANTS = "participants"
    SOURCE_CALLER_ID = "source_caller_id"


class ImSourceKeys:
    ATTACHMENTS = "attachments"
    CDR_ID = "cdr_id"
    IS_REPLY = "is_reply"
    MESSAGE = "message"
    MESSAGE_ID = "message_id"
    MESSAGES = "messages"
    PLAIN_MESSAGE = "plain_message"
    SOURCE = "source"
    THREAD_ID = "thread_id"
    TIMESTAMP = "timestamp"
    UPDATE_DELETE = "update_delete"

    @classmethod
    def get_required_df_columns(cls):
        return [
            cls.ATTACHMENTS,
            cls.CDR_ID,
            cls.IS_REPLY,
            cls.MESSAGE_ID,
            cls.PLAIN_MESSAGE,
            cls.SOURCE,
            cls.THREAD_ID,
            cls.TIMESTAMP,
            cls.UPDATE_DELETE,
        ]


class IngTeamsChatDerivedColumns:
    ATTACHMENT_AZURE_STORAGE_URL_LIST = "__attachment_azure_storage_url_list__"
    ATTACHMENTS_SCHEMA_LIST = "__attachments_schema_list__"
    CHAT_EVENT_FROM_ID = "__chat_event_from_id__"
    CHAT_EVENT_TYPE = "__chat_event_type__"
    COMPOSITE_MESSAGE_ID = "__composite_message_id__"
    COMPOSITE_THREAD_ID = "__composite_thread_id__"
    EDITS_LIST = "__edits_list__"
    IS_DELETED = "__is_deleted__"
    IS_EDITED = "__is_edited__"
    TIMESTAMP_CONVERTED = "__timestamp_converted__"
    TIMESTAMP_CREATED = "__timestamp_created__"
    TO_IDS = "__to_ids__"


class AttachmentSubFields:
    ATTACHMENT = "attachment"
    STORAGE_PATH = "storage_path"


class IngTeamsChatTempColumns:
    ATTACHMENT_LOCAL_FILE_PATH = "__attachment_local_file_path__"
    CHAT_EVENTS_EXTRACTED_TEXT = "__chat_event_extracted_text__"
    EDIT_DATA_CONSOLIDATED = "__edit_data_consolidated__"
    ELASTIC_TEXT_TO_EDIT_LIST = "__elastic_text_to_edit_list__"
    PARTICIPANT_EMAILS = "__participant_emails__"


CHAT_EVENT_REGEX = r"System Event:\s+(?P<name>.*?)\s*(?P<event>added to the chat|joined the chat|left the chat|Chat renamed to)"  # noqa: E501


class ExtractMessagesOutputColumns:
    CHAT_EVENT = [
        ImSourceKeys.CDR_ID,
        ImSourceKeys.PLAIN_MESSAGE,
        IngTeamsChatDerivedColumns.TIMESTAMP_CONVERTED,
        IngTeamsChatDerivedColumns.CHAT_EVENT_TYPE,
        IngTeamsChatDerivedColumns.CHAT_EVENT_FROM_ID,
        VerbaCdrSourceKeys.DESTINATION_NAME,
    ]
    MESSAGE = [
        ImSourceKeys.ATTACHMENTS,
        ImSourceKeys.CDR_ID,
        ImSourceKeys.PLAIN_MESSAGE,
        ImSourceKeys.SOURCE,
        IngTeamsChatDerivedColumns.TIMESTAMP_CONVERTED,
        IngTeamsChatDerivedColumns.COMPOSITE_MESSAGE_ID,
        IngTeamsChatDerivedColumns.COMPOSITE_THREAD_ID,
        IngTeamsChatDerivedColumns.IS_DELETED,
        IngTeamsChatDerivedColumns.IS_EDITED,
        IngTeamsChatDerivedColumns.TO_IDS,
        IngTeamsChatDerivedColumns.TIMESTAMP_CREATED,
        VerbaCdrSourceKeys.DESTINATION_NAME,
        IngTeamsChatDerivedColumns.EDITS_LIST,
        IngTeamsChatDerivedColumns.ATTACHMENT_AZURE_STORAGE_URL_LIST,
    ]

    ELASTIC_FIELDS_TO_UPDATE = [
        MessageColumns.BODY_EDITS,
        MessageColumns.BODY_TEXT,
        MessageColumns.METADATA_IS_DELETED,
        MessageColumns.METADATA_IS_EDITED,
        MessageColumns.TIMESTAMPS_TIMESTAMP_START,
    ]

    ELASTIC_META_FIELDS_FOR_UPDATE = [
        "&id",
        "&model",
    ]
