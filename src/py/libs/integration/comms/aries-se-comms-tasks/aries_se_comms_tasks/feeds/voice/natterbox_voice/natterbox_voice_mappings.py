import pandas as pd
from aries_se_comms_tasks.abstractions.transformations.abstract_voice_transformations import (
    AbstractVoiceTransformations,
)
from aries_se_comms_tasks.feeds.voice.natterbox_voice.static import (
    DATETIME_FORMAT,
    FLOW_NAME,
    FileUrlColumns,
    SourceColumns,
    TempColumns,
)
from aries_se_comms_tasks.generic.participants.participant_identifiers import (  # type: ignore[attr-defined] # noqa: E501
    Params as ParticipantIdentifiesParams,
)
from aries_se_comms_tasks.generic.participants.participant_identifiers import (  # type: ignore[attr-defined] # noqa: E501
    run_participant_identifiers,
)
from aries_se_comms_tasks.voice.static import WAVEFORM_FILE_NAME, WAVEFORM_FILE_PREFIX, CallColumns
from aries_se_core_tasks.datetime.convert_datetime import (  # type: ignore[attr-defined] # noqa: E501
    Params as ConvertDatetimeParams,
)
from aries_se_core_tasks.datetime.convert_datetime import (  # type: ignore[attr-defined] # noqa: E501
    run_convert_datetime,
)
from aries_se_core_tasks.transform.dataframe.concat_attributes import (  # type: ignore[attr-defined] # noqa: E501
    Params as ConcatAttributesParams,
)
from aries_se_core_tasks.transform.dataframe.concat_attributes import (  # type: ignore[attr-defined] # noqa: E501
    run_concat_attributes,
)
from aries_se_core_tasks.transform.map.map_attribute import (  # type: ignore[attr-defined] # noqa: E501
    Params as MapAttributeParams,
)
from aries_se_core_tasks.transform.map.map_attribute import (  # type: ignore[attr-defined] # noqa: E501
    run_map_attribute,
)
from aries_se_core_tasks.transform.map.map_conditional import (  # type: ignore[attr-defined] # noqa: E501
    Params as MapConditionalParams,
)
from aries_se_core_tasks.transform.map.map_conditional import (  # type: ignore[attr-defined] # noqa: E501
    run_map_conditional,
)
from se_core_tasks.datetime.convert_datetime import ConvertTo, Unit
from se_core_tasks.map.map_attribute import CastTo
from se_core_tasks.map.map_conditional import Case


class NatterboxVoiceMappings(AbstractVoiceTransformations):
    source_frame: pd.DataFrame
    target_df: pd.DataFrame
    pre_process_df: pd.DataFrame

    source_file_uri: str

    def __init__(self, source_file_uri: str, *args, **kwargs):
        super().__init__(*args, **kwargs)

        self.source_file_uri = source_file_uri

    def _pre_process(self):
        # This type cast is required since pd.to_datetime
        # expects an integer as input, this can be removed
        # once convert_datetime is updated to handle this
        self.source_frame = self.source_frame.astype(
            {
                SourceColumns.CALL_END_EPOCH: int,
                SourceColumns.CALL_START_EPOCH: int,
            }
        )

        self.pre_process_df = pd.concat(
            [self.pre_process_df, self._call_participant_identifiers()],
            axis=1,
        )

    def process(self):
        self.pre_process()
        self.call_duration()
        self.connected()
        self.has_attachment()
        self.id()
        self.identifiers_all_country_codes()
        self.identifiers_all_ids()
        self.identifiers_from_device_id()
        self.identifiers_from_id()
        self.identifiers_from_id_addl_info()
        self.identifiers_from_user_id()
        self.identifiers_to_ids()
        self.identifiers_to_ids_addl_info()
        self.metadata_source_client()
        self.metadata_source_file_info_location_bucket()
        self.metadata_source_file_info_location_key()
        self.source_index()
        self.source_key()
        self.timestamps_local_timestamp_start()
        self.timestamps_timestamp_start()
        self.timestamps_local_timestamp_end()
        self.timestamps_timestamp_end()
        self.timestamps_timestamp_connected()
        self.waveform_file_info_location_bucket()
        self.waveform_file_info_location_key()
        self.post_process()
        return self.target_df

    def _call_duration(self) -> pd.Series:
        return run_convert_datetime(  # type: ignore[no-any-return]
            source_frame=self.source_frame,
            params=ConvertDatetimeParams(
                source_attribute=SourceColumns.CALL_DURATION_SECONDS,
                source_unit=Unit.SECONDS,
                target_attribute=CallColumns.CALL_DURATION,
                target_datetime_format="%H:%M:%S",
            ),
            skip_serializer=True,
        ).loc[:, CallColumns.CALL_DURATION]

    def _connected(self) -> pd.Series:
        call_connected_yes_query = (
            f"`{SourceColumns.CALL_CONNECTED}`.str.fullmatch('yes', case=False, na=False)"
        )

        return (  # type: ignore[no-any-return]
            run_map_conditional(
                source_frame=self.source_frame,
                params=MapConditionalParams(
                    target_attribute=CallColumns.CONNECTED,
                    cases=[
                        Case(
                            query=call_connected_yes_query,
                            value=True,
                        ),
                        Case(
                            query=f"~{call_connected_yes_query}",
                            value=False,
                        ),
                    ],
                ),
                skip_serializer=True,
            )
            .loc[:, CallColumns.CONNECTED]
            .astype(bool)
        )

    def _has_attachment(self) -> pd.Series:
        return (  # type: ignore[no-any-return]
            run_map_conditional(
                source_frame=self.source_frame,
                params=MapConditionalParams(
                    target_attribute=CallColumns.HAS_ATTACHMENT,
                    cases=[
                        Case(
                            query=f"`{FileUrlColumns.RECORDING_FILE_URL}`.notnull()",
                            value=True,
                        ),
                        Case(
                            query=f"`{FileUrlColumns.RECORDING_FILE_URL}`.isnull()",
                            value=False,
                        ),
                    ],
                ),
                skip_serializer=True,
            )
            .loc[:, CallColumns.HAS_ATTACHMENT]
            .astype(bool)
        )

    def _id(self) -> pd.Series:
        return self.source_frame.loc[:, SourceColumns.UUID]

    def _identifiers_all_country_codes(self) -> pd.Series:
        return self.pre_process_df.loc[:, CallColumns.IDENTIFIERS_ALL_COUNTRY_CODES]

    def _identifiers_all_ids(self) -> pd.Series:
        return self.pre_process_df.loc[:, CallColumns.IDENTIFIERS_ALL_IDS]

    def _identifiers_from_device_id(self) -> pd.Series:
        return run_concat_attributes(  # type: ignore[no-any-return]
            source_frame=self.source_frame,
            params=ConcatAttributesParams(
                source_attributes=[
                    SourceColumns.CALLER_FIRST_NAME,
                    SourceColumns.CALLER_LAST_NAME,
                ],
                delimiter=" ",
                target_attribute=CallColumns.IDENTIFIERS_FROM_DEVICE_ID,
            ),
            skip_serializer=True,
        ).loc[:, CallColumns.IDENTIFIERS_FROM_DEVICE_ID]

    def _identifiers_from_id(self) -> pd.Series:
        return self.pre_process_df.loc[:, CallColumns.IDENTIFIERS_FROM_ID]

    def _identifiers_from_id_addl_info(self):
        return self.pre_process_df.loc[:, CallColumns.IDENTIFIERS_FROM_ADDL_INFO]

    def _identifiers_from_user_id(self) -> pd.Series:
        return self.source_frame.loc[:, SourceColumns.CALLER_EMAIL_ADDRESS]

    def _identifiers_to_ids(self) -> pd.Series:
        return self.pre_process_df.loc[:, CallColumns.IDENTIFIERS_TO_IDS]

    def _identifiers_to_ids_addl_info(self) -> pd.Series:
        return self.pre_process_df.loc[:, CallColumns.IDENTIFIERS_TO_ADDL_INFO]

    def _metadata_source_client(self) -> pd.Series:
        return pd.Series(
            data="Natterbox",
            index=self.source_frame.index,
            name=CallColumns.METADATA_SOURCE_CLIENT,
        )

    def _metadata_source_file_info_location_bucket(self) -> pd.Series:
        return self.source_frame.loc[:, TempColumns.S3_BUCKET]

    def _metadata_source_file_info_location_key(self) -> pd.Series:
        return self.source_frame.loc[:, TempColumns.S3_KEY]

    def _source_index(self) -> pd.Series:
        return pd.Series(
            data=self.source_frame.index.values,
            index=self.source_frame.index,
            name=CallColumns.SOURCE_INDEX,
        )

    def _source_key(self) -> pd.Series:
        return pd.Series(
            data=self.source_file_uri,
            index=self.source_frame.index,
            name=CallColumns.SOURCE_KEY,
        )

    def _timestamps_local_timestamp_end(self) -> pd.Series:
        return run_convert_datetime(  # type: ignore[no-any-return]
            source_frame=self.source_frame,
            params=ConvertDatetimeParams(
                source_attribute=SourceColumns.CALL_END_DATE,
                source_attribute_format=DATETIME_FORMAT,
                target_attribute=CallColumns.TIMESTAMPS_LOCAL_TIMESTAMP_END,
                convert_to=ConvertTo.DATETIME,
            ),
            skip_serializer=True,
        ).loc[:, CallColumns.TIMESTAMPS_LOCAL_TIMESTAMP_END]

    def _timestamps_local_timestamp_start(self) -> pd.Series:
        return run_convert_datetime(  # type: ignore[no-any-return]
            source_frame=self.source_frame,
            params=ConvertDatetimeParams(
                source_attribute=SourceColumns.CALL_START_DATE,
                source_attribute_format=DATETIME_FORMAT,
                target_attribute=CallColumns.TIMESTAMPS_LOCAL_TIMESTAMP_START,
                convert_to=ConvertTo.DATETIME,
            ),
            skip_serializer=True,
        ).loc[:, CallColumns.TIMESTAMPS_LOCAL_TIMESTAMP_START]

    def _timestamps_timestamp_end(self) -> pd.Series:
        return run_convert_datetime(  # type: ignore[no-any-return]
            source_frame=self.source_frame,
            params=ConvertDatetimeParams(
                source_attribute=SourceColumns.CALL_END_EPOCH,
                source_unit=Unit.SECONDS,
                target_attribute=CallColumns.TIMESTAMPS_TIMESTAMP_END,
                convert_to=ConvertTo.DATETIME,
            ),
            skip_serializer=True,
        ).loc[:, CallColumns.TIMESTAMPS_TIMESTAMP_END]

    def _timestamps_timestamp_start(self) -> pd.Series:
        return run_convert_datetime(  # type: ignore[no-any-return]
            source_frame=self.source_frame,
            params=ConvertDatetimeParams(
                source_attribute=SourceColumns.CALL_START_EPOCH,
                source_unit=Unit.SECONDS,
                target_attribute=CallColumns.TIMESTAMPS_TIMESTAMP_START,
                convert_to=ConvertTo.DATETIME,
            ),
            skip_serializer=True,
        ).loc[:, CallColumns.TIMESTAMPS_TIMESTAMP_START]

    def _timestamps_timestamp_connected(self) -> pd.Series:
        return run_map_conditional(  # type: ignore[no-any-return]
            source_frame=pd.concat(
                [self.source_frame, self.target_df.loc[:, CallColumns.TIMESTAMPS_TIMESTAMP_START]],
                axis=1,
            ),
            params=MapConditionalParams(
                target_attribute=CallColumns.TIMESTAMPS_TIMESTAMP_CONNECTED,
                cases=[
                    Case(
                        query=f"`{SourceColumns.CALL_CONNECTED}`.str.fullmatch('yes', case=False, na=False)",  # noqa: E501
                        attribute=CallColumns.TIMESTAMPS_TIMESTAMP_START,
                    ),
                ],
            ),
            skip_serializer=True,
        ).loc[:, CallColumns.TIMESTAMPS_TIMESTAMP_CONNECTED]

    def _call_participant_identifiers(self) -> pd.DataFrame:
        conditional_df = pd.concat(
            [
                run_map_conditional(
                    source_frame=self.source_frame,
                    params=MapConditionalParams(
                        target_attribute=TempColumns.IDENTIFIERS_FROM_ID,
                        cases=[
                            Case(
                                query=f"`{SourceColumns.E164_CALLER_NUMBER}`.notnull()",
                                attribute=SourceColumns.E164_CALLER_NUMBER,
                            ),
                            Case(
                                query=f"`{SourceColumns.E164_CALLER_NUMBER}`.isnull()",
                                attribute=SourceColumns.CALLER_NUMBER,
                            ),
                        ],
                    ),
                    skip_serializer=True,
                ),
                run_map_conditional(
                    source_frame=self.source_frame,
                    params=MapConditionalParams(
                        target_attribute=TempColumns.IDENTIFIERS_TO_IDS_STRING,
                        cases=[
                            Case(
                                query=f"`{SourceColumns.E164_CALLED_NUMBER}`.notnull()",
                                attribute=SourceColumns.E164_CALLED_NUMBER,
                            ),
                            Case(
                                query=f"`{SourceColumns.E164_CALLED_NUMBER}`.isnull()",
                                attribute=SourceColumns.CALLED_NUMBER,
                            ),
                        ],
                    ),
                    skip_serializer=True,
                ),
            ],
            axis=1,
        )

        to_ids_df = run_map_attribute(
            source_frame=conditional_df,
            params=MapAttributeParams(
                source_attribute=TempColumns.IDENTIFIERS_TO_IDS_STRING,
                target_attribute=TempColumns.IDENTIFIERS_TO_IDS_LIST,
                cast_to=CastTo.STRING_LIST.value,
                list_delimiter=";",
            ),
            skip_serializer=True,
        )

        return run_participant_identifiers(  # type: ignore[no-any-return]
            source_frame=pd.concat([conditional_df, to_ids_df], axis=1),
            params=ParticipantIdentifiesParams(
                source_from_identifier=TempColumns.IDENTIFIERS_FROM_ID,
                source_to_identifiers=TempColumns.IDENTIFIERS_TO_IDS_LIST,
            ),
            skip_serializer=True,
        )

    def _waveform_file_info_location_bucket(self) -> pd.Series:
        return pd.Series(
            data=self.realm,
            index=self.source_frame.index,
            name=CallColumns.WAVEFORM_FILE_INFO_LOCATION_BUCKET,
        )

    def _waveform_file_info_location_key(self) -> pd.Series:
        return (
            WAVEFORM_FILE_PREFIX
            + FLOW_NAME
            + "/"
            + self.target_df.loc[:, CallColumns.ID]
            + "/"
            + WAVEFORM_FILE_NAME
        )

    def _post_process(self):
        """Not implemented."""

    def _attachments(self):
        """Not implemented."""

    def _call_duration_speaking(self):
        """Not implemented."""

    def _call_type(self):
        """Not implemented."""

    def _charge(self):
        """Not implemented."""

    def _conference_call(self):
        """Not implemented."""

    def _direction(self):
        """Not implemented."""

    def _fault(self):
        """Not implemented."""

    def _identifiers_all_domains(self):
        """Not implemented."""

    def _identifiers_bcc_ids(self):
        """Not implemented."""

    def _identifiers_cc_ids(self):
        """Not implemented."""

    def _identifiers_from_ip(self):
        """Not implemented."""

    def _identifiers_host_id(self):
        """Not implemented."""

    def _identifiers_on_behalf_of(self):
        """Not implemented."""

    def _identifiers_to_device_id(self):
        """Not implemented."""

    def _identifiers_to_ip(self):
        """Not implemented."""

    def _identifiers_to_user_id(self):
        """Not implemented."""

    def _meta_model(self):
        """Not implemented."""

    def _internal(self):
        """Not implemented."""

    def _is_multi_channel(self):
        """Not implemented."""

    def _is_dealer_board(self):
        """Not implemented."""

    def _join_reason(self):
        """Not implemented."""

    def _metadata_content_type(self):
        """Not implemented."""

    def _metadata_encoding_type(self):
        """Not implemented."""

    def _metadata_header(self):
        """Not implemented."""

    def _metadata_in_reply_to(self):
        """Not implemented."""

    def _metadata_message_id(self):
        """Not implemented."""

    def _metadata_reference_id(self):
        """Not implemented."""

    def _metadata_size_in_bytes(self):
        """Not implemented."""

    def _metadata_source_device_type(self):
        """Not implemented."""

    def _participants(self):
        """Not implemented."""

    def _rate(self):
        """Not implemented."""

    def _transcribed(self):
        """Not implemented."""

    def _voice_file(self):
        """Not implemented."""

    def _timestamps_created(self):
        """Not implemented."""

    def _timestamps_duration_unit(self):
        """Not implemented."""

    def _timestamps_duration_value(self):
        """Not implemented."""

    def _is_transcription_excluded(self) -> pd.Series:
        raise NotImplementedError("Not in use.")

    def _transcription_exclusion_reason(self) -> pd.Series:
        raise NotImplementedError("Not in use.")
