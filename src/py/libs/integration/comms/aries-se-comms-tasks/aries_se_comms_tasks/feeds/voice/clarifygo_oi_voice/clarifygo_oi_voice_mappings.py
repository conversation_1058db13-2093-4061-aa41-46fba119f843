# mypy: disable-error-code="attr-defined"
import pandas as pd
from aries_se_comms_tasks.abstractions.transformations.abstract_voice_transformations import (
    AbstractVoiceTransformations,
)
from aries_se_comms_tasks.feeds.voice.clarifygo_oi_voice.static import (
    CLARIFYGO_OI_VOICE_FLOW_NAME,
    ClarifygoOIDatetimeFormats,
    SourceColumns,
    TempColumns,
)
from aries_se_comms_tasks.generic.participants.participant_identifiers import (
    Params as ParticipantIdentifiersParams,
)
from aries_se_comms_tasks.generic.participants.participant_identifiers import (
    run_participant_identifiers,
)
from aries_se_comms_tasks.voice.static import WAVEFORM_FILE_NAME, WAVEFORM_FILE_PREFIX, CallColumns
from aries_se_core_tasks.datetime.convert_datetime import run_convert_datetime
from aries_se_core_tasks.datetime.datetime_difference import run_datetime_difference
from aries_se_core_tasks.transform.map.map_value import Params as ParamsMapValue
from aries_se_core_tasks.transform.map.map_value import run_map_value
from se_core_tasks.datetime.convert_datetime import Params as ConvertDatetimeParams
from se_core_tasks.datetime.datetime_difference import Params as DatetimeDifferenceParams
from se_data_lake.cloud_utils import get_bucket, get_key
from se_elastic_schema.static.communication import CallDirectionEnum


class ClarifygoOIVoiceMappings(AbstractVoiceTransformations):
    source_frame: pd.DataFrame
    target_df: pd.DataFrame
    pre_process_df: pd.DataFrame

    def __init__(
        self,
        source_file_uri: str,
        **kwargs,
    ):
        super().__init__(**kwargs)

        self.source_file_uri: str = source_file_uri

    def process(self) -> pd.DataFrame:
        self.pre_process()

        # Timestamps
        self.timestamps_local_timestamp_end()
        self.timestamps_local_timestamp_start()
        self.timestamps_timestamp_end()
        self.timestamps_timestamp_start()

        self.call_duration()
        self.id()
        self.has_attachment()
        self.direction()
        self.internal()

        # Identifiers
        self.identifiers_from_id()
        self.identifiers_to_ids()
        self.identifiers_all_ids()

        # Metadata
        self.metadata_source_client()
        self.metadata_source_file_info_location_bucket()
        self.metadata_source_file_info_location_key()
        self.source_index()
        self.source_key()

        # Waveform
        self.waveform_file_info_location_bucket()
        self.waveform_file_info_location_key()

        return self.target_df

    def _get_from_id(self) -> pd.Series:
        """Returns a Series with __identifiers_fromid__ .

        It is taken from the Identifiers or Extension column based on if
        the call direction is Incoming or Outgoing resp.
        """
        return self.source_frame[SourceColumns.RECORDING_CALLING_PARTY]

    def _get_to_ids(self) -> pd.Series:
        """Returns a Series with __identifiers_toids__ .

        It's taken from Identifiers or Extension column depending on
        whether the call direction is Incoming or not resp.
        """
        return self.source_frame[SourceColumns.RECORDING_CALLED_PARTY].apply(lambda x: [x])

    def _call_participants_identifiers(self) -> pd.DataFrame:
        return run_participant_identifiers(  # type: ignore[no-any-return]
            source_frame=self.pre_process_df,
            params=ParticipantIdentifiersParams(
                source_from_identifier=TempColumns.FROM_ID,
                source_to_identifiers=TempColumns.TO_IDS_LIST,
            ),
            skip_serializer=True,
        )

    def _pre_process(self) -> None:
        self.pre_process_df[TempColumns.FROM_ID] = self._get_from_id()
        self.pre_process_df[TempColumns.TO_IDS_LIST] = self._get_to_ids()

        self.pre_process_df = pd.concat(
            objs=[self.pre_process_df, self._call_participants_identifiers()],
            axis=1,
        )

    def _call_duration(self) -> pd.Series:
        """Used to populate CallColumns.CALL_DURATION from
        SourceColumns.CALL_DURATION in seconds."""
        return run_datetime_difference(
            source_frame=self.target_df,
            params=DatetimeDifferenceParams(
                start_column=CallColumns.TIMESTAMPS_LOCAL_TIMESTAMP_START,
                end_column=CallColumns.TIMESTAMPS_LOCAL_TIMESTAMP_END,
                start_format=ClarifygoOIDatetimeFormats.INPUT_DATETIME_FORMAT,
                end_format=ClarifygoOIDatetimeFormats.INPUT_DATETIME_FORMAT,
                target_column=CallColumns.CALL_DURATION,
                format_output=True,
            ),
            skip_serializer=True,
        )[CallColumns.CALL_DURATION]

    def _has_attachment(self) -> pd.Series:
        return self.source_frame[SourceColumns.STEELEYE_META_ATTACHMENT_OBJECT_PATH].notnull()

    def _id(self) -> pd.Series:
        """Used to populate CallColumns.ID from SourceColumns.RECORDING_ID."""
        result: pd.Series = self.source_frame[SourceColumns.RECORDING_ID]
        return result

    def _direction(self) -> pd.Series:
        return run_map_value(
            source_frame=self.source_frame,
            params=ParamsMapValue(
                source_attribute=SourceColumns.RECORDING_CALL_TYPE,
                target_attribute=CallColumns.DIRECTION,
                case_insensitive=True,
                value_map={
                    0: CallDirectionEnum.INCOMING.value,
                    1: CallDirectionEnum.OUTGOING.value,
                },
            ),
            skip_serializer=True,
        ).loc[:, CallColumns.DIRECTION]

    def _internal(self) -> pd.Series:
        return self.source_frame[SourceColumns.RECORDING_CALL_TYPE] == 2

    def _identifiers_from_id(self) -> pd.Series:
        """Used to populate CallColumns.IDENTIFIERS_FROM_ID."""
        return self.pre_process_df[TempColumns.FROM_ID]

    def _identifiers_to_ids(self) -> pd.Series:
        """Used to populate CallColumns.IDENTIFIERS_TO_IDS."""
        return self.pre_process_df[TempColumns.TO_IDS_LIST]

    def _identifiers_all_ids(self) -> pd.Series:
        """Populates all_ids from the from and to ids."""
        result: pd.Series = self.pre_process_df[CallColumns.IDENTIFIERS_ALL_IDS]
        return result

    def _metadata_source_client(self) -> pd.Series:
        """Used to populate CallColumns.METADATA_SOURCE_CLIENT with str
        ClarifygoOakinnovate."""
        result: pd.Series = pd.Series(
            data="ClarifygoOakinnovate",
            index=self.source_frame.index,
        )
        return result

    def _metadata_source_file_info_location_bucket(self) -> pd.Series:
        return self.source_frame[SourceColumns.STEELEYE_META_METADATA_OBJECT_PATH].apply(
            lambda x: get_bucket(x)
        )

    def _metadata_source_file_info_location_key(self) -> pd.Series:
        return self.source_frame[SourceColumns.STEELEYE_META_METADATA_OBJECT_PATH].apply(
            lambda x: get_key(x)
        )

    def _source_index(self) -> pd.Series:
        """Used to populate CallColumns.SOURCE_INDEX with source_frame index
        values."""
        result: pd.Series = pd.Series(
            data=self.source_frame.index.values,
            index=self.source_frame.index,
        )
        return result

    def _source_key(self) -> pd.Series:
        """Used to populate CallColumns.SOURCE_KEY with source_file_url."""
        return pd.Series(
            index=self.source_frame.index,
            data=self.source_file_uri,
        )

    def _timestamps_local_timestamp_end(self) -> pd.Series:
        """Converts recording disconnected timestamps, trying both primary and
        secondary formats."""

        # Run conversion with the primary datetime format
        converted_times = run_convert_datetime(
            source_frame=self.source_frame,
            params=ConvertDatetimeParams(
                source_attribute=SourceColumns.RECORDING_DISCONNECTED_TIME,
                source_attribute_format=ClarifygoOIDatetimeFormats.PRIMARY_DATETIME_FORMAT,
                target_attribute=CallColumns.TIMESTAMPS_LOCAL_TIMESTAMP_END,
                target_datetime_format=ClarifygoOIDatetimeFormats.INPUT_DATETIME_FORMAT,
            ),
            skip_serializer=True,
        )[CallColumns.TIMESTAMPS_LOCAL_TIMESTAMP_END]

        null_mask = converted_times.isnull()

        if null_mask.any():
            # Run conversion with the secondary datetime format
            converted_times_fallback = run_convert_datetime(
                source_frame=self.source_frame.loc[null_mask, :],
                params=ConvertDatetimeParams(
                    source_attribute=SourceColumns.RECORDING_DISCONNECTED_TIME,
                    target_attribute=CallColumns.TIMESTAMPS_LOCAL_TIMESTAMP_END,
                    target_datetime_format=ClarifygoOIDatetimeFormats.INPUT_DATETIME_FORMAT,
                ),
                skip_serializer=True,
            )[CallColumns.TIMESTAMPS_LOCAL_TIMESTAMP_END]

            # Fill missing values from the secondary conversion and return the resulting Series
            return converted_times.fillna(converted_times_fallback)
        else:
            return converted_times

    def _timestamps_local_timestamp_start(self) -> pd.Series:
        # Run conversion with the primary datetime format
        converted_times = run_convert_datetime(
            source_frame=self.source_frame,
            params=ConvertDatetimeParams(
                source_attribute=SourceColumns.RECORDING_CONNECTED_TIME,
                source_attribute_format=ClarifygoOIDatetimeFormats.PRIMARY_DATETIME_FORMAT,
                target_attribute=CallColumns.TIMESTAMPS_LOCAL_TIMESTAMP_START,
                target_datetime_format=ClarifygoOIDatetimeFormats.INPUT_DATETIME_FORMAT,
            ),
            skip_serializer=True,
        )[CallColumns.TIMESTAMPS_LOCAL_TIMESTAMP_START]

        null_mask = converted_times.isnull()

        if null_mask.any():
            # Run conversion with the secondary datetime format
            converted_times_fallback = run_convert_datetime(
                source_frame=self.source_frame.loc[null_mask, :],
                params=ConvertDatetimeParams(
                    source_attribute=SourceColumns.RECORDING_CONNECTED_TIME,
                    target_attribute=CallColumns.TIMESTAMPS_LOCAL_TIMESTAMP_START,
                    target_datetime_format=ClarifygoOIDatetimeFormats.INPUT_DATETIME_FORMAT,
                ),
                skip_serializer=True,
            )[CallColumns.TIMESTAMPS_LOCAL_TIMESTAMP_START]

            # Fill missing values from the secondary conversion and return the resulting Series
            return converted_times.fillna(converted_times_fallback)
        else:
            return converted_times

    def _timestamps_timestamp_end(self) -> pd.Series:
        """Used to populate CallColumns.TIMESTAMPS_TIMESTAMP_END in the
        target_df."""
        return self.target_df[CallColumns.TIMESTAMPS_LOCAL_TIMESTAMP_END]

    def _timestamps_timestamp_start(self) -> pd.Series:
        """Used to populate CallColumns.TIMESTAMPS_TIMESTAMP_START in the
        target_df."""
        return self.target_df[CallColumns.TIMESTAMPS_LOCAL_TIMESTAMP_START]

    def _waveform_file_info_location_bucket(self) -> pd.Series:
        """Used to populate CallColumns.WAVEFORM_FILE_INFO_LOCATION_BUCKET from
        the realm."""
        return pd.Series(
            data=self.realm,
            index=self.source_frame.index,
            name=CallColumns.WAVEFORM_FILE_INFO_LOCATION_BUCKET,
        )

    def _waveform_file_info_location_key(self) -> pd.Series:
        """Used to populate CallColumns.WAVEFORM_FILE_INFO_LOCATION_KEY."""
        return (
            WAVEFORM_FILE_PREFIX
            + CLARIFYGO_OI_VOICE_FLOW_NAME
            + "/"
            + self.target_df[CallColumns.ID]
            + "/"
            + WAVEFORM_FILE_NAME
        )

    # --- NON IMPLEMENTED METHODS --- #

    def _connected(self) -> pd.Series:
        raise NotImplementedError("Not in use.")

    def _identifiers_all_country_codes(self) -> pd.Series:
        raise NotImplementedError("Not in use.")

    def _identifiers_from_id_addl_info(self) -> pd.Series:
        raise NotImplementedError("Not in use.")

    def _identifiers_from_user_id(self) -> pd.Series:
        raise NotImplementedError("Not in use.")

    def _identifiers_to_device_id(self) -> pd.Series:
        raise NotImplementedError("Not in use.")

    def _identifiers_to_ids_addl_info(self) -> pd.Series:
        raise NotImplementedError("Not in use.")

    def _join_reason(self) -> pd.Series:
        raise NotImplementedError("Not in use.")

    def _identifiers_from_device_id(self) -> pd.Series:
        raise NotImplementedError("Not in use.")

    def _attachments(self) -> pd.Series:
        raise NotImplementedError("Not in use.")

    def _call_duration_speaking(self) -> pd.Series:
        raise NotImplementedError("Not in use.")

    def _call_type(self) -> pd.Series:
        raise NotImplementedError("Not in use.")

    def _charge(self) -> pd.Series:
        raise NotImplementedError("Not in use.")

    def _conference_call(self) -> pd.Series:
        raise NotImplementedError("Not in use.")

    def _fault(self) -> pd.Series:
        raise NotImplementedError("Not in use.")

    def _identifiers_all_domains(self) -> pd.Series:
        raise NotImplementedError("Not in use.")

    def _identifiers_bcc_ids(self) -> pd.Series:
        raise NotImplementedError("Not in use.")

    def _identifiers_cc_ids(self) -> pd.Series:
        raise NotImplementedError("Not in use.")

    def _identifiers_from_ip(self) -> pd.Series:
        raise NotImplementedError("Not in use.")

    def _identifiers_host_id(self) -> pd.Series:
        raise NotImplementedError("Not in use.")

    def _identifiers_on_behalf_of(self) -> pd.Series:
        raise NotImplementedError("Not in use.")

    def _identifiers_to_ip(self) -> pd.Series:
        raise NotImplementedError("Not in use.")

    def _identifiers_to_user_id(self) -> pd.Series:
        raise NotImplementedError("Not in use.")

    def _is_multi_channel(self) -> pd.Series:
        raise NotImplementedError("Not in use.")

    def _is_dealer_board(self) -> pd.Series:
        raise NotImplementedError("Not in use.")

    def _meta_model(self) -> pd.Series:
        raise NotImplementedError("Not in use.")

    def _metadata_content_type(self) -> pd.Series:
        raise NotImplementedError("Not in use.")

    def _metadata_encoding_type(self) -> pd.Series:
        raise NotImplementedError("Not in use.")

    def _metadata_header(self) -> pd.Series:
        raise NotImplementedError("Not in use.")

    def _metadata_in_reply_to(self) -> pd.Series:
        raise NotImplementedError("Not in use.")

    def _metadata_message_id(self) -> pd.Series:
        raise NotImplementedError("Not in use.")

    def _metadata_reference_id(self) -> pd.Series:
        raise NotImplementedError("Not in use.")

    def _metadata_size_in_bytes(self) -> pd.Series:
        raise NotImplementedError("Not in use.")

    def _metadata_source_device_type(self) -> pd.Series:
        raise NotImplementedError("Not in use.")

    def _participants(self) -> pd.Series:
        raise NotImplementedError("Not in use.")

    def _post_process(self) -> None:
        raise NotImplementedError("Not in use.")

    def _rate(self) -> pd.Series:
        raise NotImplementedError("Not in use.")

    def _timestamps_created(self) -> pd.Series:
        raise NotImplementedError("Not in use.")

    def _timestamps_duration_unit(self) -> pd.Series:
        raise NotImplementedError("Not in use.")

    def _timestamps_duration_value(self) -> pd.Series:
        raise NotImplementedError("Not in use.")

    def _timestamps_timestamp_connected(self) -> pd.Series:
        raise NotImplementedError("Not in use.")

    def _transcribed(self) -> pd.Series:
        raise NotImplementedError("Not in use.")

    def _voice_file(self) -> pd.Series:
        raise NotImplementedError("Not in use.")

    def _is_transcription_excluded(self) -> pd.Series:
        raise NotImplementedError("Not in use.")

    def _transcription_exclusion_reason(self) -> pd.Series:
        raise NotImplementedError("Not in use.")
