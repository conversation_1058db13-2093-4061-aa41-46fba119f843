import pandas as pd
from aries_se_comms_tasks.abstractions.transformations.abstract_voice_transformations import (
    AbstractVoiceTransformations,
)
from aries_se_comms_tasks.feeds.voice.masergy_voice.static import (
    FLOW_NAME,
    SOURCE_CLIENT,
    FileUrlColumns,
    SourceColumns,
    TempColumns,
)
from aries_se_comms_tasks.generic.participants.participant_identifiers import (  # type: ignore[attr-defined]  # noqa: E501
    Params as ParticipantIdentifiersParams,
)
from aries_se_comms_tasks.generic.participants.participant_identifiers import (  # type: ignore[attr-defined]  # noqa: E501
    run_participant_identifiers,
)
from aries_se_comms_tasks.voice.static import WAVEFORM_FILE_NAME, WAVEFORM_FILE_PREFIX, CallColumns
from aries_se_core_tasks.datetime.convert_datetime import (  # type: ignore[attr-defined]  # noqa: E501
    Params as ConvertDatetimeParams,
)
from aries_se_core_tasks.datetime.convert_datetime import (  # type: ignore[attr-defined]  # noqa: E501
    run_convert_datetime,
)
from aries_se_core_tasks.transform.map.map_conditional import (  # type: ignore[attr-defined]  # noqa: E501
    Params as MapConditionalParams,
)
from aries_se_core_tasks.transform.map.map_conditional import (  # type: ignore[attr-defined]  # noqa: E501
    run_map_conditional,
)
from se_core_tasks.datetime.convert_datetime import ConvertTo, Unit
from se_core_tasks.utils.datetime import get_time_from_timedelta
from se_data_lake.cloud_utils import get_bucket, get_key


class MasergyVoiceMappings(AbstractVoiceTransformations):
    source_frame: pd.DataFrame
    target_df: pd.DataFrame
    pre_process_df: pd.DataFrame

    source_file_uri: str

    def __init__(
        self,
        source_file_uri: str,
        **kwargs,
    ):
        super().__init__(**kwargs)

        self.source_file_uri = source_file_uri

    def process(self) -> pd.DataFrame:
        self.pre_process()

        self.call_duration()

        self.id()
        self.identifiers_all_country_codes()
        self.identifiers_all_ids()
        self.identifiers_from_id()
        self.identifiers_from_id_addl_info()
        self.identifiers_to_ids()
        self.identifiers_to_ids_addl_info()
        self.identifiers_from_device_id()
        self.identifiers_from_user_id()
        self.identifiers_to_device_id()
        self.identifiers_to_user_id()

        self.metadata_source_client()
        self.metadata_source_file_info_location_bucket()
        self.metadata_source_file_info_location_key()

        self.source_key()
        self.source_index()

        self.timestamps_local_timestamp_end()
        self.timestamps_local_timestamp_start()
        self.timestamps_timestamp_start()
        self.timestamps_timestamp_end()
        self.timestamps_timestamp_connected()

        self.waveform_file_info_location_bucket()
        self.waveform_file_info_location_key()

        self.conference_call()
        self.connected()

        return self.target_df

    def _pre_process(self) -> None:
        self.source_frame = self.source_frame.fillna(pd.NA)

        self.pre_process_df = pd.concat(
            [self.pre_process_df, self._call_participants_identifiers()],
            axis=1,
        )

        self._temp_timestamps()

    def _call_duration(self) -> pd.Series:
        """Returns CALL_DURATION as string formatted as HH:MM:SS."""
        duration_df = self.source_frame.loc[:, [SourceColumns.DURATION]]
        duration_df.loc[:, CallColumns.CALL_DURATION] = pd.to_timedelta(
            duration_df[SourceColumns.DURATION], unit=Unit.SECONDS.value
        )

        return get_time_from_timedelta(  # type: ignore[no-any-return]
            duration_df[CallColumns.CALL_DURATION]
        )

    def _id(self) -> pd.Series:
        return self.source_frame.loc[:, SourceColumns.CALL_ID]

    def _identifiers_all_ids(self) -> pd.Series:
        return pd.Series(
            data=self.pre_process_df[CallColumns.IDENTIFIERS_ALL_IDS].values,
            index=self.source_frame.index,
            name=CallColumns.IDENTIFIERS_ALL_IDS,
        )

    def _identifiers_from_id(self) -> pd.Series:
        return pd.Series(
            data=self.pre_process_df[CallColumns.IDENTIFIERS_FROM_ID].values,
            index=self.source_frame.index,
            name=CallColumns.IDENTIFIERS_FROM_ID,
        )

    def _identifiers_from_id_addl_info(self) -> pd.Series:
        return pd.Series(
            data=self.pre_process_df[CallColumns.IDENTIFIERS_FROM_ADDL_INFO].values,
            index=self.source_frame.index,
            name=CallColumns.IDENTIFIERS_FROM_ADDL_INFO,
        )

    def _identifiers_to_ids(self) -> pd.Series:
        return pd.Series(
            data=self.pre_process_df[CallColumns.IDENTIFIERS_TO_IDS].values,
            index=self.source_frame.index,
            name=CallColumns.IDENTIFIERS_TO_IDS,
        )

    def _identifiers_to_ids_addl_info(self) -> pd.Series:
        return pd.Series(
            data=self.pre_process_df[CallColumns.IDENTIFIERS_TO_ADDL_INFO].values,
            index=self.source_frame.index,
            name=CallColumns.IDENTIFIERS_TO_ADDL_INFO,
        )

    def _metadata_source_client(self) -> pd.Series:
        return pd.Series(
            data=SOURCE_CLIENT,
            index=self.source_frame.index,
            name=CallColumns.METADATA_SOURCE_CLIENT,
        )

    def _identifiers_all_country_codes(self) -> pd.Series:
        return pd.Series(
            data=self.pre_process_df[CallColumns.IDENTIFIERS_ALL_COUNTRY_CODES].values,
            index=self.source_frame.index,
            name=CallColumns.IDENTIFIERS_ALL_COUNTRY_CODES,
        )

    def _source_key(self) -> pd.Series:
        return pd.Series(
            data=self.source_file_uri,
            index=self.source_frame.index,
            name=CallColumns.SOURCE_KEY,
        )

    def _timestamps_local_timestamp_end(self) -> pd.Series:
        return pd.Series(
            data=self.pre_process_df[TempColumns.DISCONNECT_TIME].values,
            index=self.source_frame.index,
            name=CallColumns.TIMESTAMPS_LOCAL_TIMESTAMP_END,
        )

    def _timestamps_local_timestamp_start(self) -> pd.Series:
        return pd.Series(
            data=self.pre_process_df[TempColumns.CONNECT_TIME].values,
            index=self.source_frame.index,
            name=CallColumns.TIMESTAMPS_LOCAL_TIMESTAMP_START,
        )

    def _timestamps_timestamp_end(self) -> pd.Series:
        return pd.Series(
            data=self.pre_process_df[TempColumns.DISCONNECT_TIME].values,
            index=self.source_frame.index,
            name=CallColumns.TIMESTAMPS_TIMESTAMP_END,
        )

    def _timestamps_timestamp_start(self) -> pd.Series:
        return pd.Series(
            data=self.pre_process_df[TempColumns.CONNECT_TIME].values,
            index=self.source_frame.index,
            name=CallColumns.TIMESTAMPS_TIMESTAMP_START,
        )

    def _waveform_file_info_location_bucket(self) -> pd.Series:
        return pd.Series(
            data=self.realm,
            index=self.source_frame.index,
            name=CallColumns.WAVEFORM_FILE_INFO_LOCATION_BUCKET,
        )

    def _waveform_file_info_location_key(self) -> pd.Series:
        """Populates CallColumns.WAVEFORM_FILE_INFO_LOCATION_KEY."""
        return (
            WAVEFORM_FILE_PREFIX
            + FLOW_NAME
            + "/"
            + self.target_df.loc[:, CallColumns.ID]
            + "/"
            + WAVEFORM_FILE_NAME
        )

    def _source_index(self) -> pd.Series:
        """Returns a Series containing the sourceIndex column."""
        return pd.Series(
            data=self.source_frame.index.values,
            index=self.source_frame.index,
        )

    def _conference_call(self) -> pd.Series:
        return pd.Series(
            data=self.source_frame[SourceColumns.IS_CONFERENCE].values,
            index=self.source_frame.index,
            name=CallColumns.CONFERENCE_CALL,
        )

    def _connected(self) -> pd.Series:
        return run_map_conditional(  # type: ignore[no-any-return]
            source_frame=self.source_frame,
            params=MapConditionalParams(
                target_attribute=CallColumns.CONNECTED,
                query=f"`{SourceColumns.DURATION}` > 0",
                true_value=True,
                false_value=False,
            ),
            skip_serializer=True,
        )[CallColumns.CONNECTED].astype(bool)

    def _identifiers_from_device_id(self) -> pd.Series:
        return pd.Series(
            data=self.source_frame[SourceColumns.FROM_ID].values,
            index=self.source_frame.index,
            name=CallColumns.IDENTIFIERS_FROM_DEVICE_ID,
        ).str.strip()

    def _identifiers_from_user_id(self) -> pd.Series:
        return pd.Series(
            data=self.source_frame[SourceColumns.FROM_NAME].values,
            index=self.source_frame.index,
            name=CallColumns.IDENTIFIERS_FROM_USER_ID,
        ).str.strip()

    def _identifiers_to_device_id(self) -> pd.Series:
        return pd.Series(
            data=self.source_frame[SourceColumns.TO_ID].values,
            index=self.source_frame.index,
            name=CallColumns.IDENTIFIERS_TO_DEVICE_ID,
        ).str.strip()

    def _identifiers_to_user_id(self) -> pd.Series:
        return pd.Series(
            data=self.source_frame[SourceColumns.TO_NAME].values,
            index=self.source_frame.index,
            name=CallColumns.IDENTIFIERS_TO_USER_ID,
        ).str.strip()

    def _metadata_source_file_info_location_bucket(self) -> pd.Series:
        return pd.Series(
            data=self.source_frame[FileUrlColumns.METADATA_FILE_URL].apply(get_bucket).values,
            index=self.source_frame.index,
            name=CallColumns.METADATA_SOURCE_FILE_INFO_LOCATION_BUCKET,
        )

    def _metadata_source_file_info_location_key(self) -> pd.Series:
        return pd.Series(
            data=self.source_frame[FileUrlColumns.METADATA_FILE_URL].apply(get_key).values,
            index=self.source_frame.index,
            name=CallColumns.METADATA_SOURCE_FILE_INFO_LOCATION_KEY,
        )

    def _timestamps_timestamp_connected(self) -> pd.Series:
        return pd.Series(
            data=self.pre_process_df[TempColumns.CONNECT_TIME].values,
            index=self.source_frame.index,
            name=CallColumns.TIMESTAMPS_TIMESTAMP_START,
        )

    def _call_participants_identifiers(self) -> pd.DataFrame:
        return run_participant_identifiers(  # type: ignore[no-any-return]
            source_frame=pd.concat(
                [
                    self.source_frame[SourceColumns.FROM_NUMBER].str.strip().to_frame(),
                    self.source_frame[SourceColumns.TO_NUMBER]
                    .str.strip()
                    .str.split(";;;")
                    .to_frame(),
                ],
                axis=1,
            ),
            params=ParticipantIdentifiersParams(
                source_from_identifier=SourceColumns.FROM_NUMBER,
                source_to_identifiers=SourceColumns.TO_NUMBER,
            ),
            skip_serializer=True,
        )

    def _temp_timestamps(self) -> None:
        self.pre_process_df[TempColumns.CONNECT_TIME] = run_convert_datetime(
            source_frame=self.source_frame,
            params=ConvertDatetimeParams(
                source_attribute=SourceColumns.CONNECT_TIME,
                target_attribute=TempColumns.CONNECT_TIME,
                convert_to=ConvertTo.DATETIME,
                convert_to_utc=True,
            ),
            skip_serializer=True,
        )[TempColumns.CONNECT_TIME]

        self.pre_process_df[TempColumns.DISCONNECT_TIME] = run_convert_datetime(
            source_frame=self.source_frame,
            params=ConvertDatetimeParams(
                source_attribute=SourceColumns.DISCONNECT_TIME,
                target_attribute=TempColumns.DISCONNECT_TIME,
                convert_to=ConvertTo.DATETIME,
                convert_to_utc=True,
            ),
            skip_serializer=True,
        )[TempColumns.DISCONNECT_TIME]

    def _attachments(self):
        """NOT IMPLEMENTED."""

    def _post_process(self) -> None:
        """NOT IMPLEMENTED."""

    def _call_duration_speaking(self):
        """NOT IMPLEMENTED."""

    def _call_type(self):
        """NOT IMPLEMENTED."""

    def _charge(self):
        """NOT IMPLEMENTED."""

    def _direction(self):
        """NOT IMPLEMENTED."""

    def _fault(self):
        """NOT IMPLEMENTED."""

    def _has_attachment(self):
        """NOT IMPLEMENTED."""

    def _identifiers_all_domains(self):
        """NOT IMPLEMENTED."""

    def _identifiers_bcc_ids(self):
        """NOT IMPLEMENTED."""

    def _identifiers_cc_ids(self):
        """NOT IMPLEMENTED."""

    def _identifiers_from_ip(self):
        """NOT IMPLEMENTED."""

    def _identifiers_host_id(self):
        """NOT IMPLEMENTED."""

    def _identifiers_on_behalf_of(self):
        """NOT IMPLEMENTED."""

    def _identifiers_to_ip(self):
        """NOT IMPLEMENTED."""

    def _internal(self):
        """NOT IMPLEMENTED."""

    def _is_multi_channel(self):
        """NOT IMPLEMENTED."""

    def _is_dealer_board(self):
        """NOT IMPLEMENTED."""

    def _join_reason(self):
        """NOT IMPLEMENTED."""

    def _meta_model(self):
        """NOT IMPLEMENTED."""

    def _metadata_content_type(self):
        """NOT IMPLEMENTED."""

    def _metadata_encoding_type(self):
        """NOT IMPLEMENTED."""

    def _metadata_header(self):
        """NOT IMPLEMENTED."""

    def _metadata_in_reply_to(self):
        """NOT IMPLEMENTED."""

    def _metadata_message_id(self):
        """NOT IMPLEMENTED."""

    def _metadata_reference_id(self):
        """NOT IMPLEMENTED."""

    def _metadata_size_in_bytes(self):
        """NOT IMPLEMENTED."""

    def _metadata_source_device_type(self):
        """NOT IMPLEMENTED."""

    def _participants(self):
        """NOT IMPLEMENTED."""

    def _rate(self):
        """NOT IMPLEMENTED."""

    def _transcribed(self):
        """NOT IMPLEMENTED."""

    def _timestamps_created(self):
        """NOT IMPLEMENTED."""

    def _timestamps_duration_unit(self):
        """NOT IMPLEMENTED."""

    def _timestamps_duration_value(self):
        """NOT IMPLEMENTED."""

    def _voice_file(self):
        """NOT IMPLEMENTED."""

    def _is_transcription_excluded(self) -> pd.Series:
        raise NotImplementedError("Not in use.")

    def _transcription_exclusion_reason(self) -> pd.Series:
        raise NotImplementedError("Not in use.")
