from aries_se_core_tasks.utilities.data_utils import BaseColumns


class CommpeakDatetimeFormats:
    INPUT_DATETIME_FORMAT = "%Y-%m-%d %H:%M:%S"
    PRIMARY_DATETIME_FORMAT = "%Y-%m-%dT%H:%M:%S.%f%z"
    SECONDARY_DATETIME_FORMAT = "%Y-%m-%dT%H:%M:%S%z"


COMMPEAK_VOICE_FLOW_NAME = "commpeak_voice_transform"
FILE_NAME = "fileName"


class FileUrlColumns:
    METADATA_FILE_URL = "metadata_file_url"
    METADATA_LOCAL_FILE_PATH = "metadata_local_file_path"
    RECORDING_FILE_URL = "recording_file_url"
    RECORDING_LOCAL_FILE_PATH = "recording_local_file_path"


class TempColumns:
    FINAL_DATETIME = "__final_datetime__"
    FROM_ID = "__from_id__"
    PARTICIPANTS = "__participants__"
    PRIMARY_DATETIME = "__primary_datetime__"
    TO_IDS_LIST = "__to_ids_list__"
    TO_IDS_STRING = "__to_ids_string__"
    SECONDARY_DATETIME = "__secondary_datetime__"
    STATUS = "__status__"


class SourceColumns(BaseColumns):
    LEAD_ID = "lead_id"
    CAMPAIGN_ID = "campaign_id"
    ASSIGNED_USER_ID = "assigned_user_id"
    ASSIGNED_AT = "assigned_at"
    ASSIGNED_USER_PERMANENT = "assigned_user_permanent"
    CALL_AFTER = "call_after"
    WEIGHT = "weight"
    PERMANENT_ASSIGNMENT_EXPIRE_AT = "permanent_assignment_expire_at"
    CALL_AFTER_OUTSIDE_ALLOWED_TIME = "call_after_outside_allowed_time"
    NO_RATIO = "no_ratio"
    CALLS_COUNT = "calls_count"
    RATIO_EXPIRE_AT = "ratio_expire_at"
    OUTBOUND_IVR_DISABLED = "outbound_ivr_disabled"
    OUTBOUND_IVR_RECORDING = "outbound_ivr_recording"
    OVERRIDE_TRUNK_ID = "override_trunk_id"
    STATUS_BEFORE = "status_before"
    STATUS_AFTER = "status_after"
    CALLER_USER = "caller_user"
    LAST_DEPOSIT_ATTEMPT_DATE = "Last_Deposit_Attempt_Date"
    LAST_ONLINE_TIME = "Last_Online_Time"
    SOLITICS_EVENTS = "solitics_events"
    SOLITICS_DATA = "solitics_data"
    LAST_DEPOSIT_STATE = "last_deposit_state"
    FIRST_NAME = "first_name"
    LAST_NAME = "last_name"
    PHONE = "phone"
    PHONE_NORMALIZED = "phone_normalized"
    PHONE2 = "phone2"
    PHONE_NORMALIZED2 = "phone_normalized2"
    ADDRESS1 = "address1"
    ADDRESS2 = "address2"
    STATE = "state"
    CITY = "city"
    ZIP = "zip"
    LAT = "lat"
    LNG = "lng"
    TIMEZONE = "timezone"
    ORIGINAL_IDENTIFIER = "original_identifier"
    SUPPORT_SITE = "support_site"
    PURCHASE_SITE = "purchase_site"
    PURCHASE_DATE = "purchase_date"
    PURCHASE_AMOUNT = "purchase_amount"
    PURCHASE_PRODUCT_NAME = "purchase_product_name"
    PURCHASE_CARD_NAME = "purchase_card_name"
    PURCHASE_CARD_TYPE = "purchase_card_type"
    PURCHASE_CARD_DIGITS = "purchase_card_digits"
    EMAIL = "email"
    BIRTHDATE = "birthdate"
    LOAN_AMOUNT = "loan_amount"
    HLR = "hlr"
    CAMPAIGN_NAME = "campaign_name"
    ASSIGNED_USERNAME = "assigned_username"
    COUNTRY_NAME = "country_name"
    TIMEZONE_NAME = "timezone_name"
    OBJECT_TYPE_ID = "object_type_id"
    OBJECT_ID = "object_id"
    CALLER_USER_ID = "caller_user_id"
    START_AT = "start_at"
    END_AT = "end_at"
    SRC = "src"
    DST = "dst"
    CALL_UUID = "call_uuid"
    AGENT_CALLERID_NAME = "agent_callerid_name"
    AGENT_CALLERID_NUMBER = "agent_callerid_number"
    AGENT_HANGUP_DISPOSITION = "agent_hangup_disposition"
    CLIENT_CALLERID_NAME = "client_callerid_name"
    CLIENT_CALLERID_NUMBER = "client_callerid_number"
    CLIENT_HANGUP_DISPOSITION = "client_hangup_disposition"
    CALL_DURATION = "call_duration"
    CALL_BILL_DURATION = "call_bill_duration"
    STATUS = "status"
    STATUS_ID_BEFORE = "status_id_before"
    STATUS_ID_AFTER = "status_id_after"
    RECORD_FILE = "record_file"
    Q850 = "q850"
    BEEP_DETECTED = "beep_detected"
    RATE = "rate"
    INIT_BLOCK = "init_block"
    BILLING_BLOCK = "billing_block"
    USER_GROUP_ID = "user_group_id"
    SIP_TERM_STATUS = "sip_term_status"
    AMD_STATUS = "amd_status"
    AMD_RESULT = "amd_result"
    ORIGINAL_IP = "original_ip"
    CALL_ID = "call_id"
    CALLER_USERNAME = "caller_username"


class CommpeakExtensions:
    FLAC = "flac"
    MP3 = "mp3"
