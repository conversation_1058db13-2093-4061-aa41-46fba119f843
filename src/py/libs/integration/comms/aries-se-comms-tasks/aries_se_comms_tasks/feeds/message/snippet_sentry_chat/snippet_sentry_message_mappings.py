import pandas as pd
from aries_se_comms_tasks.abstractions.transformations.abstract_message_transformations import (
    AbstractMessageTransformations,
)
from aries_se_comms_tasks.comms_data_sources import CommunicationDataSource
from aries_se_comms_tasks.feeds.message.snippet_sentry_chat.static import (
    SOURCE_CLIENT,
    SOURCE_CLIENT_SUFFIX,
    FileUrlColumns,
    SourceColumns,
    TempColumns,
)
from aries_se_comms_tasks.generic.participants.helper_functions import merge_list_of_dicts
from aries_se_comms_tasks.generic.participants.participant_identifiers import (  # type: ignore[attr-defined] # noqa: E501
    Params as ParticipantsIdentifiersParams,
)
from aries_se_comms_tasks.generic.participants.participant_identifiers import (  # type: ignore[attr-defined] # noqa: E501
    run_participant_identifiers,
)
from aries_se_comms_tasks.message.static import MessageColumns
from aries_se_core_tasks.datetime.convert_datetime import (  # type: ignore[attr-defined] # noqa: E501
    Params as ConvertDatetimeParams,
)
from aries_se_core_tasks.datetime.convert_datetime import (  # type: ignore[attr-defined] # noqa: E501
    run_convert_datetime,
)
from aries_se_core_tasks.transform.map.map_conditional import (  # type: ignore[attr-defined] # noqa: E501
    Params as MapConditionalParams,
)
from aries_se_core_tasks.transform.map.map_conditional import (  # type: ignore[attr-defined] # noqa: E501
    run_map_conditional,
)
from aries_se_core_tasks.transform.map.map_value import (  # type: ignore[attr-defined] # noqa: E501
    Params as MapValueParams,
)
from aries_se_core_tasks.transform.map.map_value import (
    run_map_value,
)
from se_core_tasks.datetime.convert_datetime import ConvertTo, Unit
from se_core_tasks.map.map_conditional import Case
from se_data_lake.cloud_utils import get_bucket, get_key


class SnippetSentryMessageMappings(AbstractMessageTransformations):
    source_frame: pd.DataFrame
    pre_process_df: pd.DataFrame
    target_df: pd.DataFrame

    def __init__(
        self,
        source_file_uri: str,
        attachments_metadata_series: pd.Series,
        **kwargs,
    ):
        super().__init__(**kwargs)

        self.source_file_uri: str = source_file_uri
        self.attachments_metadata_series = attachments_metadata_series

    def process(self):
        self.pre_process()

        self.attachments()

        # Body fields
        self.body_text()
        self.body_display_text()
        self.body_type()

        self.has_attachment()

        # Identifiers
        self.identifiers_all_country_codes()
        self.identifiers_all_ids()
        self.identifiers_from_id()
        self.identifiers_from_id_addl_info()
        self.identifiers_to_ids()
        self.identifiers_to_ids_addl_info()

        # Metadata
        self.metadata_source_client()
        self.metadata_source_file_info_location_bucket()
        self.metadata_source_file_info_location_key()

        self.source_key()

        self.room_id()
        self.room_name()
        self.timestamps_timestamp_start()

        return self.target_df

    def _pre_process(self):
        self.pre_process_df = pd.concat(
            [self.pre_process_df, self._call_participants_identifiers()], axis=1
        )

    def _attachments(self):
        """Populates attachments from self.attachment_series."""
        return self.attachments_metadata_series

    def _body_text(self):
        return run_map_conditional(
            source_frame=self.source_frame,
            params=MapConditionalParams(
                target_attribute=MessageColumns.BODY_TEXT,
                cases=[
                    Case(
                        query=f"~`{SourceColumns.TYPE}`.isin(['image', 'video', 'document', 'vcard', 'ptt', 'sticker'])",  # noqa: E501
                        attribute=SourceColumns.BODY,
                    )
                ],
            ),
            skip_serializer=True,
        ).loc[:, MessageColumns.BODY_TEXT]

    def _body_display_text(self):
        return pd.Series(
            data=self.target_df.loc[:, MessageColumns.BODY_TEXT].values,
            index=self.target_df.index,
            name=MessageColumns.BODY_DISPLAY_TEXT,
        )

    def _body_type(self) -> pd.Series:
        return pd.Series(data="PLAIN", index=self.target_df.index, name=MessageColumns.BODY_TYPE)

    def _has_attachment(self):
        return pd.Series(
            data=self.target_df.loc[:, MessageColumns.ATTACHMENTS].notnull()
            & (self.target_df.loc[:, MessageColumns.ATTACHMENTS].str.len() > 0),
            index=self.target_df.index,
            name=MessageColumns.HAS_ATTACHMENT,
        )

    def _identifiers_all_country_codes(self):
        """Used to populate MessageColumns.IDENTIFIERS_ALL_COUNTRY_CODES."""
        return self.pre_process_df.loc[:, MessageColumns.IDENTIFIERS_ALL_COUNTRY_CODES]

    def _identifiers_all_ids(self) -> pd.Series:
        """Populates all_ids from the from and to ids."""
        return self.pre_process_df.loc[:, MessageColumns.IDENTIFIERS_ALL_IDS]

    def _identifiers_from_id(self) -> pd.Series:
        """Populates MessageColumns.IDENTIFIERS_FROM_ID."""
        return self.pre_process_df.loc[:, MessageColumns.IDENTIFIERS_FROM_ID]

    def _identifiers_from_id_addl_info(self):
        """Populates MessageColumns.IDENTIFIERS_FROM_ADDL_INFO."""
        return self.pre_process_df.loc[:, MessageColumns.IDENTIFIERS_FROM_ADDL_INFO]

    def _identifiers_to_ids(self) -> pd.Series:
        """Populates MessageColumns.IDENTIFIERS_TO_IDS."""
        return self.pre_process_df.loc[:, MessageColumns.IDENTIFIERS_TO_IDS]

    def _identifiers_to_ids_addl_info(self):
        """Populates MessageColumns.IDENTIFIERS_TO_ADDL_INFO."""
        return self.pre_process_df.loc[:, MessageColumns.IDENTIFIERS_TO_ADDL_INFO]

    def _metadata_source_file_info_location_bucket(self) -> pd.Series:
        return pd.Series(
            data=self.source_frame[FileUrlColumns.METADATA_FILE_URI].apply(get_bucket).values,
            index=self.source_frame.index,
            name=MessageColumns.METADATA_SOURCE_FILE_INFO_LOCATION_BUCKET,
        )

    def _metadata_source_file_info_location_key(self) -> pd.Series:
        return pd.Series(
            data=self.source_frame[FileUrlColumns.METADATA_FILE_URI].apply(get_key).values,
            index=self.source_frame.index,
            name=MessageColumns.METADATA_SOURCE_FILE_INFO_LOCATION_KEY,
        )

    def _metadata_source_client(self) -> pd.Series:
        source_client_df = run_map_value(
            source_frame=self.source_frame[[SourceColumns.SOURCE]],
            params=MapValueParams(
                source_attribute=SourceColumns.SOURCE,
                target_attribute=TempColumns.SOURCE,
                value_map={
                    "imessage": CommunicationDataSource.IMESSAGE,
                    "telegram": CommunicationDataSource.TELEGRAM,
                    "wechat": CommunicationDataSource.WECHAT,
                    "whatsapp": CommunicationDataSource.WHATSAPP,
                },
                case_insensitive=True,
                preserve_original=True,
            ),
            skip_serializer=True,
        )

        source_client_df.loc[:, TempColumns.SOURCE] = (
            source_client_df.loc[:, TempColumns.SOURCE].astype("string")
            + " "
            + SOURCE_CLIENT_SUFFIX
        )

        return run_map_conditional(  # type: ignore[no-any-return]
            source_frame=source_client_df,
            params=MapConditionalParams(
                target_attribute=MessageColumns.METADATA_SOURCE_CLIENT,
                cases=[
                    Case(
                        query=f"`{TempColumns.SOURCE}`.notnull()",
                        attribute=TempColumns.SOURCE,
                    ),
                    Case(
                        query=f"`{TempColumns.SOURCE}`.isnull()",
                        value=SOURCE_CLIENT,
                    ),
                ],
            ),
            skip_serializer=True,
        ).loc[:, MessageColumns.METADATA_SOURCE_CLIENT]

    def _source_key(self) -> pd.Series:
        return pd.Series(
            data=self.source_file_uri,
            index=self.target_df.index,
            name=MessageColumns.SOURCE_KEY,
        )

    def _room_id(self) -> pd.Series:
        return pd.Series(
            data=self.source_frame.loc[:, SourceColumns.CHAT_ID].values,
            index=self.target_df.index,
            name=MessageColumns.ROOM_ID,
        )

    def _room_name(self) -> pd.Series:
        return pd.Series(
            data=self.source_frame.loc[:, SourceColumns.CHAT_ID].values,
            index=self.target_df.index,
            name=MessageColumns.ROOM_NAME,
        )

    def _timestamps_timestamp_start(self) -> pd.Series:
        """Populates MessageColumns.TIMESTAMPS_TIMESTAMP_START."""
        return run_convert_datetime(  # type: ignore[no-any-return]
            source_frame=self.source_frame,
            params=ConvertDatetimeParams(
                source_attribute=SourceColumns.TIMESTAMP,
                target_attribute=MessageColumns.TIMESTAMPS_TIMESTAMP_START,
                convert_to=ConvertTo.DATETIME.value,
                source_unit=Unit.SECONDS,
            ),
            skip_serializer=True,
        ).loc[:, MessageColumns.TIMESTAMPS_TIMESTAMP_START]

    def _call_participants_identifiers(self) -> pd.DataFrame:
        participants_df: pd.DataFrame = pd.DataFrame(index=self.source_frame.index)

        participants_df.loc[:, TempColumns.FROM_ID] = pd.Series(
            data=self.source_frame.loc[:, SourceColumns.SENDER_PUSHNAME],
            index=self.source_frame.index,
            name=TempColumns.FROM_ID,
        )

        participants_df.loc[:, TempColumns.TO_IDS] = (
            self.source_frame.loc[:, SourceColumns.RECIPIENTS]
            .apply(lambda x: merge_list_of_dicts(x) if isinstance(x, list) else {})
            .str.get("to")  # type: ignore[arg-type]
        )

        # Replace None with empty []
        participants_df.loc[:, TempColumns.TO_IDS] = participants_df.loc[
            :, TempColumns.TO_IDS
        ].apply(lambda x: x if x is not None else [])

        return run_participant_identifiers(  # type: ignore[no-any-return]
            source_frame=participants_df,
            params=ParticipantsIdentifiersParams(
                source_from_identifier=TempColumns.FROM_ID,
                source_to_identifiers=TempColumns.TO_IDS,
            ),
            skip_serializer=True,
        )

    def _post_process(self):
        """Not Implemented."""

    def _body_edits(self):
        """Not Implemented."""

    def _chat_type(self):
        """Not Implemented."""

    def _counts_reply_count(self):
        """Not Implemented."""

    def _counts_reply_user_count(self):
        """Not Implemented."""

    def _identifiers_all_domains(self):
        """Not Implemented."""

    def _identifiers_domains(self):
        """Not Implemented."""

    def _identifiers_from_device_id(self):
        """Not Implemented."""

    def _identifiers_from_user_id(self):
        """Not Implemented."""

    def _identifiers_mentioned_ids(self):
        """Not Implemented."""

    def _identifiers_on_behalf_of(self):
        """Not Implemented."""

    def _identifiers_saved_by_ids(self):
        """Not Implemented."""

    def _identifiers_to_device_id(self):
        """Not Implemented."""

    def _identifiers_to_ip(self):
        """Not Implemented."""

    def _identifiers_to_user_id(self):
        """Not Implemented."""

    def _metadata_content_type(self):
        """Not Implemented."""

    def _metadata_encoding_type(self):
        """Not Implemented."""

    def _metadata_header(self):
        """Not Implemented."""

    def _metadata_is_edited(self):
        """Not Implemented."""

    def _metadata_is_deleted(self):
        """Not Implemented."""

    def _metadata_is_pinned(self):
        """Not Implemented."""

    def _metadata_in_reply_to(self):
        """Not Implemented."""

    def _metadata_original_message_id(self):
        """Not Implemented."""

    def _metadata_message_id(self):
        """Not Implemented."""

    def _metadata_reacted_to(self):
        """Not Implemented."""

    def _metadata_sub_thread_id(self):
        """Not Implemented."""

    def _meta_model(self):
        """Not Implemented."""

    def _metadata_reference_id(self):
        """Not Implemented."""

    def _metadata_size_in_bytes(self):
        """Not Implemented."""

    def _metadata_source_device_type(self):
        """Not Implemented."""

    def _metadata_thread_id(self):
        """Not Implemented."""

    def _metadata_source_file_info_content_length(self):
        """Not Implemented."""

    def _metadata_source_file_info_content_md5(self):
        """Not Implemented."""

    def _metadata_source_file_info_metadata(self):
        """Not Implemented."""

    def _metadata_source_file_info_version_id(self):
        """Not Implemented."""

    def _metadata_source_file_info_last_modified(self):
        """Not Implemented."""

    def _metadata_source_file_info_processed(self):
        """Not Implemented."""

    def _participants(self):
        """Not Implemented."""

    def _timestamps_created(self):
        """Not Implemented."""

    def _timestamps_local_timestamp_end(self):
        """Not Implemented."""

    def _timestamps_local_timestamp_start(self):
        """Not Implemented."""

    def _timestamps_timestamp_connected(self):
        """Not Implemented."""

    def _timestamps_timestamp_end(self):
        """Not Implemented."""
