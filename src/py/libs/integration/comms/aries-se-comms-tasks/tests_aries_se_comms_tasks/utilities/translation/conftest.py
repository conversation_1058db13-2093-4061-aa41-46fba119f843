import os
import pytest
from addict import addict

os.environ["AWS_DEFAULT_REGION"] = "eu-west-1"


@pytest.fixture()
def sample_french_text() -> str:
    return (
        "Laissez-moi vous raconter l’histoire d'un Poulet. Ses amis l’appellent Petit Poulet."
        " Petit Poulet vit dans une petite maison de poulet normale, dans une petite ville"
        " normale."
        " Il n’est ni grand, ni petit. Il n’est ni gros, ni maigre. Il n’est ni intelligent ni"
        " stupide. "
        "Petit Poulet est un poulet tout à fait normal! J'ai une petite question:"
        " il fait quoi toute la journee?"
        " Par une matinée tout à fait normale, Petit Poulet mange son petit-déjeuner"
        " dans la cuisine."
        " Il aime les toasts avec du beurre et le café à la crème."
    )


@pytest.fixture()
def english_translation() -> str:
    return (
        "Let me tell you the story of a chicken. His friends call him <PERSON> Chicken."
        " Little Chicken lives in a normal little chicken house, in a normal small town."
        " He is neither big nor small. He is not fat or thin. He is not smart or stupid."
        " Petit Poulet is a completely normal chicken! I have a quick question: what"
        " does he do all day? On a completely normal morning, <PERSON> Poulet eats breakfast"
        " in the kitchen. He likes toasts with butter and coffee with cream."
    )


@pytest.fixture()
def translation_config_source_and_target() -> addict.Dict:
    return addict.Dict(
        {
            "id": "7888599a-d886-451d-a7f9-7137d4640929",
            "tenantId": 7,
            "translationEnabled": True,
            "translationTargetLanguage": "FRENCH",
            "createdDateTime": "2024-02-23T07:40:26.643310",
            "translationSourceLanguage": "GERMAN",
            "workflowId": 73,
            "translationProvider": "AWS",
            "updatedDateTime": "2024-02-23T07:40:26.646043",
        }
    )


@pytest.fixture()
def translation_config_only_target() -> addict.Dict:
    return addict.Dict(
        {
            "id": "7888599a-d886-451d-a7f9-7137d4640929",
            "tenantId": 7,
            "translationEnabled": True,
            "translationTargetLanguage": "FRENCH",
            "createdDateTime": "2024-02-23T07:40:26.643310",
            "translationSourceLanguage": None,
            "workflowId": 73,
            "translationProvider": "AWS",
            "updatedDateTime": "2024-02-23T07:40:26.646043",
        }
    )


@pytest.fixture()
def translation_config_disabled() -> addict.Dict:
    return addict.Dict(
        {
            "id": "7888599a-d886-451d-a7f9-7137d4640929",
            "tenantId": 7,
            "translationEnabled": False,
            "translationTargetLanguage": "FRENCH",
            "createdDateTime": "2024-02-23T07:40:26.643310",
            "translationSourceLanguage": None,
            "workflowId": 73,
            "translationProvider": "AWS",
            "updatedDateTime": "2024-02-23T07:40:26.646043",
        }
    )


@pytest.fixture()
def translation_config_not_aws() -> addict.Dict:
    return addict.Dict(
        {
            "id": "7888599a-d886-451d-a7f9-7137d4640929",
            "tenantId": 7,
            "translationEnabled": True,
            "translationTargetLanguage": "FRENCH",
            "createdDateTime": "2024-02-23T07:40:26.643310",
            "translationSourceLanguage": None,
            "workflowId": 73,
            "translationProvider": "Redbox",
            "updatedDateTime": "2024-02-23T07:40:26.646043",
        }
    )


@pytest.fixture()
def translation_config_invalid_enum() -> addict.Dict:
    return addict.Dict(
        {
            "id": "7888599a-d886-451d-a7f9-7137d4640929",
            "tenantId": 7,
            "translationEnabled": True,
            "translationTargetLanguage": "DOTHRAKI",
            "createdDateTime": "2024-02-23T07:40:26.643310",
            "translationSourceLanguage": None,
            "workflowId": 73,
            "translationProvider": "AWS",
            "updatedDateTime": "2024-02-23T07:40:26.646043",
        }
    )
