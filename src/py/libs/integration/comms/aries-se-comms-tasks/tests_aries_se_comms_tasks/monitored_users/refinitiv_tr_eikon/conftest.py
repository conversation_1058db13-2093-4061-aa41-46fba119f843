import pandas as pd
import pytest
from se_elastic_schema.static.reference import ImAccountType
from typing import List, Tuple


@pytest.fixture()
def elastic_monitored_person_frame_mock() -> pd.DataFrame:
    return pd.DataFrame(
        [
            {
                "&id": "a0c6b39e-3503-4133-b04e-d989819b2389",
                "name": "<PERSON>",
                "communications.emails": ["<EMAIL>"],
                "communications.imAccounts": [
                    {"id": "John.doe", "label": ImAccountType.BBG.value},
                ],
            },
            {
                "&id": "a8de180a-e9c6-4213-a821-e1ac1bf6116d",
                "name": "<PERSON>",
                "communications.emails": ["<EMAIL>", "<EMAIL>"],
                "communications.imAccounts": [
                    {"id": "u03pgflh47q", "label": ImAccountType.SLACK},
                    {"id": "janetd", "label": ImAccountType.REUTERS},
                ],
            },
            {
                "&id": "2e92cf20-63ba-45eb-821f-04a81fb47311",
                "name": "Jim Doe",
                "communications.emails": pd.NA,
                "communications.imAccounts": [
                    {"id": "jim.doe", "label": ImAccountType.LIVE_CHAT.value},
                    {"id": "jim.doe_bbg", "label": ImAccountType.BBG.value},
                ],
            },
            {
                "&id": "2e92cf20-63ba-45eb-821f-04a81fb47311",
                "name": "Jack Doe",
                "communications.emails": ["<EMAIL>"],
                "communications.imAccounts": pd.NA,
            },
            {
                "&id": "6293cf20-64ba-46eb-821f-04a41hb47371",
                "name": "Jill Doe",
                "communications.emails": ["<EMAIL>"],
                "communications.imAccounts": [
                    {"id": "jIll.doe", "label": ImAccountType.REUTERS.value},
                    {"id": "JILL.doe_2", "label": ImAccountType.LIVE_CHAT},
                ],
            },
        ]
    )


def refinitiv_eikon_participants_data() -> List[Tuple[str, bool]]:
    return [
        (
            "['<EMAIL>', '<EMAIL>', '<EMAIL>']",
            True,
        ),
        (
            "['<EMAIL>', '<EMAIL>']",
            False,
        ),
        (
            "<EMAIL>",
            False,
        ),
        (
            "<EMAIL>",
            True,
        ),
    ]
