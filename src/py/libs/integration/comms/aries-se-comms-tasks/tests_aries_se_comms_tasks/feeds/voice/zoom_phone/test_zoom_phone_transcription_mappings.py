# type: ignore
import json
import logging
import pandas as pd
import pytest
from aries_se_comms_tasks.feeds.voice.zoom_phone.zoom_phone_transcript_mappings import (
    ZoomTranscriptMappings,
)
from aries_se_comms_tasks.transcription.static import TranscriptModelFields
from pathlib import Path

BASE_PATH = Path(__file__).parent
TRANSCRIPT_PATH = BASE_PATH.joinpath("data/transcripts")

test_data_fetch_tokens = [
    (
        f"{TRANSCRIPT_PATH}/4d413543-591c-47e8-bd90-1784618442ba_transcript.json",
        pd.read_pickle(
            f"{BASE_PATH}/data/expected_result/fetch_tokens/expected_result_4d413543-591c-47e8-bd90-1784618442ba.pkl"
        ),
    ),
]

test_data_all_speakers = [
    (
        f"{TRANSCRIPT_PATH}/4d413543-591c-47e8-bd90-1784618442ba_transcript.json",
        {"+447977172522": "1", "<PERSON> <PERSON>": "2"},
    ),
    (
        f"{TRANSCRIPT_PATH}/191e1e90-382d-4cf1-8efb-41768aa9045e_transcript.json",
        {"<PERSON> Farmer": "2", "Tom <PERSON> SteelEye": "1"},
    ),
    (
        f"{TRANSCRIPT_PATH}/dabcaa23-97ee-4b99-8e4d-dad171c7d8d6_transcript.json",
        {"Mark Farmer": "2", "Tom Scott SteelEye": "1"},
    ),
]

test_data_fetch_speakers = [
    (
        f"{BASE_PATH}/data/fetch_speakers/valid_tokens.json",
        [{"id": "1", "percentage": 17.0}, {"id": "2", "percentage": 83.0}],
    ),
    (
        f"{BASE_PATH}/data/fetch_speakers/tokens_duration_missing.json",
        [{"id": "1", "percentage": pd.NA}, {"id": "2", "percentage": pd.NA}],
    ),
]


class TestZoomTranscriptionMappings:
    """Integration Test for TestZoomPhoneTranscriptionMappings."""

    def test_end_to_end_mappings(
        self,
        source_frame_transcript_mappings_s_1: pd.DataFrame,
        expected_result_zoom_phone_transcript_mapping_s1: pd.DataFrame,
    ):
        """Runs an end-to-end test for Zoom Transcription Mapping."""
        mapping_task = ZoomTranscriptMappings(
            source_frame=source_frame_transcript_mappings_s_1,
            logger=logging.getLogger(),
            realm="test.dev.steeleye.co",
        )
        result = mapping_task.process()
        result = result.drop(TranscriptModelFields.TRANSCRIPT_SOURCE_KEY, axis=1)
        assert result.dropna().equals(expected_result_zoom_phone_transcript_mapping_s1.dropna())

    @pytest.mark.parametrize(
        "source_path_all_speakers, expected_result_all_speakers", test_data_all_speakers
    )
    def test_all_speakers(self, source_path_all_speakers, expected_result_all_speakers):
        """Runs an end-to-end test for Zoom Transcription Mapping."""
        with open(
            source_path_all_speakers,
            "r",
        ) as f:
            content = json.load(f)
        result = ZoomTranscriptMappings.all_speakers(transcript=content)
        assert result == expected_result_all_speakers

    def test_all_speakers_null_source(self):
        """Runs an end-to-end test for Zoom Transcription Mapping."""
        result = ZoomTranscriptMappings.all_speakers(transcript=pd.NA)
        assert pd.isna(result) is True

    @pytest.mark.parametrize(
        "source_path_fetch_tokens, expected_result_fetch_tokens", test_data_fetch_tokens
    )
    def test_fetch_tokens(self, source_path_fetch_tokens, expected_result_fetch_tokens):
        """Runs an end-to-end test for Zoom Transcription Mapping."""
        with open(
            source_path_fetch_tokens,
            "r",
        ) as f:
            content = json.load(f)
        result = ZoomTranscriptMappings._fetch_tokens(
            transcript=content, all_speakers={"+447977172522": 1, "Dom Charlton": 2}
        )
        result_df = pd.DataFrame(result)
        assert result_df.equals(expected_result_fetch_tokens)

    def test_fetch_transcript(self):
        """Runs an end-to-end test for Zoom Transcription Mapping."""
        source_path = f"{TRANSCRIPT_PATH}/4d413543-591c-47e8-bd90-1784618442ba_transcript.json"
        result = ZoomTranscriptMappings._fetch_transcript(
            self,
            row={
                "TRANSCRIPT_PATH": f"{TRANSCRIPT_PATH}/4d413543-591c-47e8-bd90-1784618442ba_transcript.json"  # noqa E501
            },
        )
        expected_result = pd.read_json(source_path)
        result_df = pd.DataFrame(result)
        assert result_df.equals(expected_result)

    @pytest.mark.parametrize(
        "source_path_fetch_speakers, expected_result_fetch_speakers", test_data_fetch_speakers
    )
    def test_fetch_speakers(self, source_path_fetch_speakers, expected_result_fetch_speakers):
        """Runs an end-to-end test for Zoom Transcription Mapping."""
        with open(
            source_path_fetch_speakers,
            "r",
        ) as f:
            content = json.load(f).get("data")
        result = ZoomTranscriptMappings._fetch_speakers(
            tokens=content, all_speakers={"+447977172522": "1", "dummy_1": "2"}
        )
        assert result == expected_result_fetch_speakers

    def test_fetch_speakers_empty_token(self):
        """Runs an end-to-end test for Zoom Transcription Mapping."""
        result = ZoomTranscriptMappings._fetch_speakers(
            tokens=[], all_speakers={"+447977172522": "1", "dummy_2": "2"}
        )
        assert result == []
