��k      �pandas.core.frame��	DataFrame���)��}�(�_mgr��pandas.core.internals.managers��BlockManager���(�pandas._libs.internals��_unpickle_block����pandas._libs.arrays��__pyx_unpickle_NDArrayBacked����pandas.core.arrays.string_��StringArray���J�\U
N��R�h�StringDtype���)��}��storage��python�sb�numpy.core.multiarray��_reconstruct����numpy��ndarray���K ��Cb���R�(KK��h�dtype����O8�����R�(K�|�NNNJ����J����K?t�b�]�(�Kyte Group (EXT FD)��!Market Securities (Dubai) Limited��Nino Options (EXT FD)�h-h-h/h-�ING (GR) (EXT FD) (PM)�h/�*TPICAP IGBB (GR) ((PM) by TR) (Disclaimer)��(Phoenix Futures and Options LLC (EXT FD)�h-et�b}���b�builtins��slice���K KK��R�K��R�hhhJ�\U
N��R�h)��}�hhsbhhK ��h!��R�(KK��h)�]�(�Simon��Brendan��Alfred��Danny��Ashley��Ramon��Lawrence��Barry��Marcel��Sam��John��Peter�et�b}���bh8KKK��R�K��R�hhhJ�\U
N��R�h)��}�hhsbhhK ��h!��R�(KK��h)�]�(�&<EMAIL>��$<EMAIL>��<EMAIL>��<EMAIL>��<EMAIL>��<EMAIL>��&<EMAIL>��<EMAIL>��<EMAIL>��<EMAIL>��<EMAIL>��!<EMAIL>�et�b}���bh8KKK��R�K��R�hhhJ�\U
N��R�h)��}�hhsbhhK ��h!��R�(KK��h)�]�(�
Greenhough��Downes��Kemp��Fowler��Purdy��Koster��Patmore��Smith��
van Diepen��Mahoney��Donohue�h�et�b}���bh8KKK��R�K��R�t�]�(�pandas.core.indexes.base��
_new_Index���h��Index���}�(�data�hhK ��h!��R�(KK��h&�O8�����R�(Kh*NNNJ����J����K?t�b�]�(�6ComplianceActivity.Users.UserInfo.ComplianceSetup.Name��+ComplianceActivity.Users.UserInfo.FirstName��,ComplianceActivity.Users.UserInfo.Identifier��*ComplianceActivity.Users.UserInfo.LastName�et�b�name�Nu��R�h�h�}�(h��numpy.core.numeric��_frombuffer���(�`                                                                       	       
              �h&�i8�����R�(K�<�NNNJ����J����K t�bK���C�t�R�h�Nu��R�e��R��_typ��	dataframe��	_metadata�]��attrs�}��_flags�}��allows_duplicate_labels��sub.