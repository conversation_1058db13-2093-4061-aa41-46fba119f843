import pandas as pd
import pytest
from aries_se_comms_tasks.feeds.voice.zoom_phone.static import CallLogColumns
from pathlib import Path

BASE_PATH = Path(__file__).parent
TRANSCRIPT_PATH = BASE_PATH.joinpath("data/transcripts")
EXPECTED_RESULTS_PATH = BASE_PATH.joinpath("data/expected_result")


@pytest.fixture()
def source_frame_transcript_mappings_s_1() -> pd.DataFrame:
    data = {
        "RECORDING_PATH": ["rec_1", "rec_2", "rec_3", pd.NA],
        "TRANSCRIPT_PATH": [
            f"{TRANSCRIPT_PATH}/4d413543-591c-47e8-bd90-1784618442ba_transcript.json",
            f"{TRANSCRIPT_PATH}/42b51673-ecbd-4253-aa41-ba5f9aa3e15f_transcript.json",
            f"{TRANSCRIPT_PATH}/191e1e90-382d-4cf1-8efb-41768aa9045e_transcript.json",
            pd.NA,
        ],
    }
    return pd.DataFrame(data)


@pytest.fixture()
def source_frame_derive_attachment_path() -> pd.DataFrame:
    data = {
        CallLogColumns.ID: ["id_1", "id_2", "id_3", pd.NA],
        CallLogColumns.VOICE_MAIL_ID: [pd.NA, pd.NA, "vm_3", pd.NA],
        CallLogColumns.RECORDING_ID: ["rec_1", "rec_2", pd.NA, pd.NA],
    }
    return pd.DataFrame(data)


@pytest.fixture()
def expected_result_test_derive_attachment_path() -> pd.DataFrame:
    data = [
        {
            "RECORDING_PATH": "s3://test.dev.steeleye.co/aries/ingress/nonstreamed/polled/zoom_phone/2022/07/13/06/15/attachments/id_1/dummy_rec_key_1",
            "TRANSCRIPT_PATH": "s3://test.dev.steeleye.co/aries/ingress/nonstreamed/polled/zoom_phone/2022/07/13/06/15/attachments/id_1/id_1_transcript.json",
            "SOURCE_FILE_PATH": "s3://test.dev.steeleye.co/aries/ingress/nonstreamed/polled/zoom_phone/2022/07/13/06/15/call_logs.json",
        },
        {
            "RECORDING_PATH": "s3://test.dev.steeleye.co/aries/ingress/nonstreamed/polled/zoom_phone/2022/07/13/06/15/attachments/id_2/dummy_rec_key_2",
            "TRANSCRIPT_PATH": "s3://test.dev.steeleye.co/aries/ingress/nonstreamed/polled/zoom_phone/2022/07/13/06/15/attachments/id_2/id_2_transcript.json",
            "SOURCE_FILE_PATH": "s3://test.dev.steeleye.co/aries/ingress/nonstreamed/polled/zoom_phone/2022/07/13/06/15/call_logs.json",
        },
        {
            "RECORDING_PATH": "s3://test.dev.steeleye.co/aries/ingress/nonstreamed/polled/zoom_phone/2022/07/13/06/15/attachments/id_3/dummy_rec_key_3",
            "TRANSCRIPT_PATH": "s3://test.dev.steeleye.co/aries/ingress/nonstreamed/polled/zoom_phone/2022/07/13/06/15/attachments/id_3/id_3_transcript.json",
            "SOURCE_FILE_PATH": "s3://test.dev.steeleye.co/aries/ingress/nonstreamed/polled/zoom_phone/2022/07/13/06/15/call_logs.json",
        },
    ]
    return pd.DataFrame(data)


@pytest.fixture()
def expected_result_zoom_phone_transcript_mapping_s1() -> pd.DataFrame:
    data: pd.DataFrame = pd.read_pickle(
        f"{EXPECTED_RESULTS_PATH}/expected_result_zoom_phone_transcript_mapping_s1.pkl"
    )
    return data


@pytest.fixture()
def source_frame():
    data = [
        {
            "id": "8f1918fb-fa00-447c-8eb2-9e3d6d98761a",
            "user_id": "Gtz8x5WuRp-wwGviLCiioQ",
            "call_type": "pstn",
            "caller_number": "832",
            "caller_number_type": 1,
            "caller_name": "Jaypal Sihra (SteelEye)",
            "callee_number": "+447721585155",
            "callee_number_type": 2,
            "callee_number_source": "internal",
            "callee_location": "United Kingdom",
            "direction": "outbound",
            "duration": 58,
            "result": "Auto Recorded",
            "date_time": "2022-06-21T15:25:30Z",
            "path": "pstn",
            "charge": "£0.0519",
            "rate": "£0.0519",
            "recording_id": "8f1918fbfa00447c8eb29e3d6d98761a",
            "recording_type": "Automatic",
            "has_voicemail": False,
            "call_id": "7111714781258941606",
            "owner": {
                "type": "user",
                "id": "Gtz8x5WuRp-wwGviLCiioQ",
                "name": "Jaypal Sihra (SteelEye)",
                "extension_number": 832,
            },
            "caller_did_number": "+442038216007",
            "caller_country_code": "44",
            "caller_country_iso_code": "GB",
            "callee_did_number": "+447721585155",
            "callee_country_code": "44",
            "callee_country_iso_code": "GB",
            "call_end_time": "2022-06-21T15:26:35Z",
        },
        {
            "id": "2a7eab8f-917d-4071-be47-70fcaf02faa5",
            "user_id": "j6KUSDIzQWejd4CoHFXNiw",
            "call_type": "pstn",
            "caller_number": "853",
            "caller_number_type": 1,
            "caller_name": "Matthew Loft",
            "callee_number": "+447584884484",
            "callee_number_type": 2,
            "callee_number_source": "internal",
            "callee_location": "United Kingdom",
            "direction": "outbound",
            "duration": 9,
            "result": "Auto Recorded",
            "date_time": "2022-06-21T15:03:31Z",
            "path": "pstn",
            "charge": "£0.0519",
            "rate": "£0.0519",
            "recording_id": "2a7eab8f917d4071be4770fcaf02faa5",
            "recording_type": "Automatic",
            "has_voicemail": False,
            "call_id": "7111709116197039883",
            "owner": {
                "type": "user",
                "id": "j6KUSDIzQWejd4CoHFXNiw",
                "name": "Matthew Loft",
                "extension_number": 853,
            },
            "caller_did_number": "+442038215723",
            "caller_country_code": "44",
            "caller_country_iso_code": "GB",
            "callee_did_number": "+447584884484",
            "callee_country_code": "44",
            "callee_country_iso_code": "GB",
            "call_end_time": "2022-06-21T15:03:57Z",
        },
        {
            "id": "b28a91a3-2dd7-4992-8dd9-c121b6c9b435",
            "user_id": "atYLw-0DSwK8-oLtTTl3sg",
            "call_type": "voip",
            "caller_number": "+31207086360",
            "caller_number_type": 2,
            "caller_number_source": "internal",
            "caller_location": "Amsterdam",
            "callee_number": "847",
            "callee_number_type": 1,
            "callee_name": "Jonathan Omigie",
            "direction": "inbound",
            "duration": 0,
            "result": "Busy",
            "date_time": "2022-08-09T13:34:12Z",
            "path": "pstn",
            "voice_mail_id": "7fd117c263724459ae6ce1b6ba02e17a",
            "has_recording": False,
            "call_id": "7129869268716614063",
            "owner": {
                "type": "user",
                "id": "atYLw-0DSwK8-oLtTTl3sg",
                "name": "Jonathan Omigie",
                "extension_number": 847,
            },
            "caller_country_code": "31",
            "caller_country_iso_code": "NL",
            "callee_did_number": "+442081324395",
            "callee_country_code": "44",
            "callee_country_iso_code": "GB",
            "call_end_time": "2022-08-09T13:34:14Z",
            "department": "",
            "cost_center": "",
            "RECORDING_PATH": f"{BASE_PATH}/data/attachments/b28a91a3-2dd7-4992-8dd9-c121b6c9b435/7fd117c263724459ae6ce1b6ba02e17a.mp3",  # noqa: E501
        },
    ]
    return pd.DataFrame(data)


@pytest.fixture()
def expected_result():
    expected_data = [
        {
            "callDuration": "00:00:58",
            "callType": "PSTN",
            "charge": "£0.0519",
            "connected": "True",
            "hasAttachment": "False",
            "id": "8f1918fb-fa00-447c-8eb2-9e3d6d98761a",
            "identifiers.allCountryCodes": ["GB"],
            "identifiers.allIds": ["+442038216007", "+447721585155"],
            "identifiers.fromId": "+442038216007",
            "identifiers.fromIdAddlInfo": {"raw": "+442038216007", "countryCode": "GB"},
            "identifiers.fromUserId": "Jaypal Sihra (SteelEye)",
            "identifiers.toIds": ["+447721585155"],
            "identifiers.toIdsAddlInfo": [{"raw": "+447721585155", "countryCode": "GB"}],
            "identifiers.toUserId": pd.NA,
            "internal": True,
            "metadata.source.client": "Zoom Phone",
            "rate": "£0.0519",
            "sourceIndex": 0,
            "sourceKey": pd.NA,
            "timestamps.localTimestampEnd": "2022-06-21T15:26:35.000000Z",
            "timestamps.localTimestampStart": "2022-06-21T15:25:30.000000Z",
            "timestamps.timestampEnd": "2022-06-21T15:26:35.000000Z",
            "timestamps.timestampStart": "2022-06-21T15:25:30.000000Z",
            "waveform.location.bucket": "test.dev.steeleye.co",
            "waveform.location.key": "attachments/waveform/ZoomPhoneCallTransform/8f1918fb-fa00-447c-8eb2-9e3d6d98761a/waveform.json",  # noqa: E501
        },
        {
            "callDuration": "00:00:09",
            "callType": "PSTN",
            "charge": "£0.0519",
            "connected": "True",
            "hasAttachment": "False",
            "id": "2a7eab8f-917d-4071-be47-70fcaf02faa5",
            "identifiers.allCountryCodes": ["GB"],
            "identifiers.allIds": ["+442038215723", "+447584884484"],
            "identifiers.fromId": "+442038215723",
            "identifiers.fromIdAddlInfo": {"raw": "+442038215723", "countryCode": "GB"},
            "identifiers.fromUserId": "Matthew Loft",
            "identifiers.toIds": ["+447584884484"],
            "identifiers.toIdsAddlInfo": [{"raw": "+447584884484", "countryCode": "GB"}],
            "identifiers.toUserId": pd.NA,
            "internal": True,
            "metadata.source.client": "Zoom Phone",
            "rate": "£0.0519",
            "sourceIndex": 1,
            "sourceKey": pd.NA,
            "timestamps.localTimestampEnd": "2022-06-21T15:03:57.000000Z",
            "timestamps.localTimestampStart": "2022-06-21T15:03:31.000000Z",
            "timestamps.timestampEnd": "2022-06-21T15:03:57.000000Z",
            "timestamps.timestampStart": "2022-06-21T15:03:31.000000Z",
            "waveform.location.bucket": "test.dev.steeleye.co",
            "waveform.location.key": "attachments/waveform/ZoomPhoneCallTransform/2a7eab8f-917d-4071-be47-70fcaf02faa5/waveform.json",  # noqa: E501
        },
        {
            "callDuration": "00:05:00",
            "callType": "VOIP",
            "charge": pd.NA,
            "connected": False,
            "hasAttachment": False,
            "id": "b28a91a3-2dd7-4992-8dd9-c121b6c9b435",
            "identifiers.allCountryCodes": ["NL", "GB"],
            "identifiers.allIds": ["+31207086360", "+442081324395"],
            "identifiers.fromId": "+31207086360",
            "identifiers.fromIdAddlInfo": {"raw": "+31207086360", "countryCode": "NL"},
            "identifiers.fromUserId": pd.NA,
            "identifiers.toIds": ["+442081324395"],
            "identifiers.toIdsAddlInfo": [{"raw": "+442081324395", "countryCode": "GB"}],
            "identifiers.toUserId": "Jonathan Omigie",
            "internal": True,
            "metadata.source.client": "Zoom Phone",
            "rate": pd.NA,
            "sourceIndex": 2,
            "sourceKey": pd.NA,
            "timestamps.localTimestampEnd": "2022-08-09T13:34:14.000000Z",
            "timestamps.localTimestampStart": "2022-08-09T13:34:12.000000Z",
            "timestamps.timestampEnd": "2022-08-09T13:34:14.000000Z",
            "timestamps.timestampStart": "2022-08-09T13:34:12.000000Z",
            "waveform.location.bucket": "test.dev.steeleye.co",
            "waveform.location.key": "attachments/waveform/ZoomPhoneCallTransform/b28a91a3-2dd7-4992-8dd9-c121b6c9b435/waveform.json",  # noqa: E501
        },
    ]
    return pd.DataFrame(expected_data)
