���      �pandas.core.frame��	DataFrame���)��}�(�_mgr��pandas.core.internals.managers��BlockManager���(�pandas._libs.internals��_unpickle_block����numpy.core.numeric��_frombuffer���(�(            8�@     ȃ@     8�@     ȃ@     8�@��numpy��dtype����f8�����R�(K�<�NNNJ����J����K t�bKK���C�t�R��builtins��slice���KK	K��R�K��R�hh(�(       �C     y      �C     y      G      �h�i8�����R�(KhNNNJ����J����K t�bKK��ht�R�hKKK��R�K��R�h�numpy.core.multiarray��_reconstruct���h�ndarray���K ��Cb���R�(KKK��h�O8�����R�(K�|�NNNJ����J����K?t�b�]�(�ns3://test.dev.steeleye.co/lake/ingress/landing/communications/email/mta_journal/2021/11/15/**********.internal�h?�ns3://test.dev.steeleye.co/lake/ingress/landing/communications/email/mta_journal/2021/11/15/**********.internal�h@h@�test�hAhAhAhA�<black-logo-steeleye_85aef33d-e9cb-4294-a5c4-b1db2e7276e7.png��BSocialLink_Linkedin_32x32_79fca46c-1231-4405-bcf7-36c514245755.png��<black-logo-steeleye_85aef33d-e9cb-4294-a5c4-b1db2e7276e7.png��BSocialLink_Linkedin_32x32_79fca46c-1231-4405-bcf7-36c514245755.png��ASocialLink_Twitter_32x32_ba8e2566-64ff-4f29-835b-80ed4e3090e7.png��png��png��png��png��png��	image/png��	image/png��	image/png��	image/png��	image/png��test.dev.steeleye.co�hQhQhQhQ��aries/ingress/depository/email/attachments/2023/07/19/test_email/9d341ff7ac371b6268c28bef9c6c65916a31c74c39fd78464bf2f77a2cdc5054/**********.internal/black-logo-steeleye_85aef33d-e9cb-4294-a5c4-b1db2e7276e7.png���aries/ingress/depository/email/attachments/2023/07/19/test_email/9d341ff7ac371b6268c28bef9c6c65916a31c74c39fd78464bf2f77a2cdc5054/**********.internal/SocialLink_Linkedin_32x32_79fca46c-1231-4405-bcf7-36c514245755.png���aries/ingress/depository/email/attachments/2023/07/19/test_email/9d341ff7ac371b6268c28bef9c6c65916a31c74c39fd78464bf2f77a2cdc5054/**********.internal/black-logo-steeleye_85aef33d-e9cb-4294-a5c4-b1db2e7276e7.png���aries/ingress/depository/email/attachments/2023/07/19/test_email/9d341ff7ac371b6268c28bef9c6c65916a31c74c39fd78464bf2f77a2cdc5054/**********.internal/SocialLink_Linkedin_32x32_79fca46c-1231-4405-bcf7-36c514245755.png���aries/ingress/depository/email/attachments/2023/07/19/test_email/9d341ff7ac371b6268c28bef9c6c65916a31c74c39fd78464bf2f77a2cdc5054/**********.internal/SocialLink_Twitter_32x32_ba8e2566-64ff-4f29-835b-80ed4e3090e7.png��!9QfrKO0ou_1TjP6ACrf_h2GWiijQOu2W1��!9QfrKO0ou_1TjP6ACrf_h2GWiijQOu2W2��!9QfrKO0ou_1TjP6ACrf_h2GWiijQOu2W3��!9QfrKO0ou_1TjP6ACrf_h2GWiijQOu2W4��!9QfrKO0ou_1TjP6ACrf_h2GWiijQOu2W5��2023-07-21T06:59:38.911459��2023-07-21T06:59:38.911459��2023-07-21T06:59:38.911459��2023-07-21T06:59:38.911459��2023-07-21T06:59:38.911459��
Attachment�hahahahaG�      G�      G�      G�      G�      et�bh(�X                                                         	       
                     �h&K��ht�R�K��R�hh1h3K ��h5��R�(KKK��h;�]�(}�(�&id��test.dev.steeleye.co��	&traitFqn��tenant/configuration��	&ancestor��6TenantConfiguration:test.dev.steeleye.co:1610438215209��emailDomains�]��
steel-eye.com�a�&key��6TenantConfiguration:test.dev.steeleye.co:1610438992064��subscribedMarketAbuseReports�]��ABUSIVE_SQUEEZE�a�subscribedModules�]�(�Market��Communications�e�usageTrackingEnabled���&model��TenantConfiguration��tenantId�hp�featureFlags�]�(�
classifier��threadEmails��zoning��apply_validations�e�userIdleTimeoutMs�J \&�
&timestamp��
1610438992064��&user��AKIAIG2C2VNWA5ZTLKEA�uhnhnhnhnet�bhK
KK��R�K��R�t�]�(�pandas.core.indexes.base��
_new_Index���h��Index���}�(�data�h1h3K ��h5��R�(KK��h�O8�����R�(Kh<NNNJ����J����K?t�b�]�(�file_url��tenant��fileName��fileType��mimeTag��sizeInBytes��fileInfo.location.bucket��fileInfo.location.key��fileInfo.contentLength��fileInfo.versionId��fileInfo.processed��__meta_model__��content��tenant_configuration�et�b�name�Nu��R�h�h�}�(h�h(�(                                           �h&K��ht�R�h�Nu��R�e��R��_typ��	dataframe��	_metadata�]��attrs�}��_flags�}��allows_duplicate_labels��sub.