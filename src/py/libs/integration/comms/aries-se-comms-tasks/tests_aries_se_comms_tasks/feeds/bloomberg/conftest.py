import pandas as pd
import pathlib
import pytest
from aries_io_event.io_param import IOParamFieldSet
from aries_io_event.task import TaskFieldSet
from aries_io_event.workflow import WorkflowFieldSet
from aries_task_link.models import AriesTaskInput
from datetime import datetime

CURRENT_DIR = pathlib.Path(__file__).parent
DATA_PATH = CURRENT_DIR.joinpath("data")
MONITORED_USERS_PATH = DATA_PATH.joinpath("monitored_users")


@pytest.fixture()
def sample_aries_task_input_chat() -> AriesTaskInput:
    workflow = WorkflowFieldSet(
        name="bloomberg",
        stack="dev-blue",
        tenant="test",
        start_timestamp=datetime(2023, 3, 7),
        trace_id="trace",
    )
    input_param = IOParamFieldSet(
        params=dict(
            file_uri="s3://test.dev.steeleye.co/aries/ingest_batching/bloomberg/"
            "2023/06/01/CHAT-fs_5C0A1D7C19040059_1531372380_1531408743.ib19.xml",
            streamed=True,
        )
    )
    task = TaskFieldSet(name="bloomberg_transform", version="latest", success=False)
    return AriesTaskInput(workflow=workflow, input_param=input_param, task=task)


@pytest.fixture()
def sample_aries_task_input_mail() -> AriesTaskInput:
    workflow = WorkflowFieldSet(
        name="bloomberg",
        stack="dev-blue",
        tenant="test",
        start_timestamp=datetime(2023, 3, 7),
        trace_id="trace",
    )
    input_param = IOParamFieldSet(
        params=dict(
            file_uri="s3://test.dev.steeleye.co/aries/ingest_batching/"
            "bloomberg/2023/06/01/Email_5C0A50EB0001457C3F451146.msg.xml",
            streamed=True,
        )
    )
    task = TaskFieldSet(name="bloomberg_transform", version="latest", success=False)
    return AriesTaskInput(workflow=workflow, input_param=input_param, task=task)


@pytest.fixture()
def aries_task_input_ib19_monitored_users() -> AriesTaskInput:
    workflow = WorkflowFieldSet(
        name="bloomberg",
        stack="dev-blue",
        tenant="test",
        start_timestamp=datetime(2023, 3, 7),
        trace_id="trace",
    )
    input_param = IOParamFieldSet(
        params=dict(
            file_uri="s3://test.dev.steeleye.co/aries/ingest_batching/bloomberg/"
            "2023/06/01/f3113.ib19.240208.xml",
            streamed=True,
        )
    )
    task = TaskFieldSet(name="bloomberg_transform", version="latest", success=False)
    return AriesTaskInput(workflow=workflow, input_param=input_param, task=task)


@pytest.fixture()
def aries_task_input_msg_monitored_users() -> AriesTaskInput:
    workflow = WorkflowFieldSet(
        name="bloomberg",
        stack="dev-blue",
        tenant="ing",
        start_timestamp=datetime(2023, 3, 7),
        trace_id="trace",
    )
    input_param = IOParamFieldSet(
        params=dict(
            file_uri="s3://test.dev.steeleye.co/aries/ingest_batching/bloomberg/"
            "2023/06/01/f3113.msg.240208.xml",
            streamed=True,
        )
    )
    task = TaskFieldSet(name="bloomberg_transform", version="latest", success=False)
    return AriesTaskInput(workflow=workflow, input_param=input_param, task=task)


@pytest.fixture()
def expected_result_ib19_messages_no_monitored_users() -> pd.DataFrame:
    return pd.read_json(
        MONITORED_USERS_PATH.joinpath("ib19")
        .joinpath("expected_result_ib19_messages_no_monitored_users.ndjson")
        .as_posix(),
        lines=True,
    )


@pytest.fixture()
def expected_result_ib19_chat_no_monitored_users() -> pd.DataFrame:
    return pd.read_json(
        MONITORED_USERS_PATH.joinpath("ib19")
        .joinpath("expected_result_ib19_chat_events_no_monitored_users.ndjson")
        .as_posix(),
        lines=True,
    )


@pytest.fixture()
def expected_result_ib19_messages_monitored_users() -> pd.DataFrame:
    return pd.read_json(
        MONITORED_USERS_PATH.joinpath("ib19")
        .joinpath("expected_result_ib19_messages_monitored_users.ndjson")
        .as_posix(),
        lines=True,
    )


@pytest.fixture()
def expected_result_ib19_chat_monitored_users() -> pd.DataFrame:
    return pd.read_json(
        MONITORED_USERS_PATH.joinpath("ib19")
        .joinpath("expected_result_ib19_chat_events_monitored_users.ndjson")
        .as_posix(),
        lines=True,
    )


@pytest.fixture()
def expected_result_msg_no_monitored_users() -> pd.DataFrame:
    return pd.read_json(
        MONITORED_USERS_PATH.joinpath("msg")
        .joinpath("expected_result_msg_no_monitored_users.ndjson")
        .as_posix(),
        lines=True,
    )


@pytest.fixture()
def expected_result_msg_monitored_users() -> pd.DataFrame:
    return pd.read_json(
        MONITORED_USERS_PATH.joinpath("msg")
        .joinpath("expected_result_msg_monitored_users.ndjson")
        .as_posix(),
        lines=True,
    )
