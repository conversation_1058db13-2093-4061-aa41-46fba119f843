{"messages": [{"bot_id": "B4ZCTCGRM", "type": "message", "text": "Surprised", "user": "U01PGFLH47Q", "ts": "1670844557.255509", "blocks": [{"type": "image", "block_id": "nIxL", "image_url": "https://media2.giphy.com/media/l3q2K5jinAlChoCLS/giphy.gif?cid=6104955edl9p29aihcfkoc0ltgh20kxmx3hv36l1fqp91m8d&rid=giphy.gif&ct=g", "alt_text": "Surprised", "title": {"type": "plain_text", "text": "Surprised", "emoji": true}, "image_width": 195, "image_height": 229, "image_bytes": 1185694, "is_animated": true, "fallback": "195x229px image"}, {"type": "context", "block_id": "ZWV5t", "elements": [{"type": "image", "image_url": "https://a.slack-edge.com/dc483/img/plugins/giphy/service_32.png", "alt_text": "giphy logo"}, {"type": "mrkdwn", "text": "Posted using /giphy | GIF by <https://giphy.com/mashable/|Mashable>", "verbatim": false}]}], "team": "T3P0PGX18", "bot_profile": {"id": "B4ZCTCGRM", "deleted": false, "name": "giphy", "updated": 1492071940, "app_id": "A0F827J2C", "icons": {"image_36": "https://a.slack-edge.com/dc483/img/plugins/giphy/service_72.png", "image_48": "https://a.slack-edge.com/dc483/img/plugins/giphy/service_48.png", "image_72": "https://a.slack-edge.com/dc483/img/plugins/giphy/service_72.png"}, "team_id": "T3P0PGX18"}}, {"type": "message", "subtype": "channel_join", "ts": "1670839953.584059", "user": "U02AMERNUFM", "text": "<@U02AMERNUFM> has joined the channel", "inviter": "U01PGFLH47Q"}, {"type": "message", "subtype": "channel_leave", "ts": "1660839953.584012", "user": "U024XCUUY8Y", "text": "<@U024XCUUY8Y> has left the channel"}, {"type": "message", "text": "", "files": [{"id": "F04CWA4HUHZ", "created": 1669723686, "timestamp": 1669723686, "name": "Screenshot 2022-11-25 at 6.23.14 PM.png", "title": "Screenshot 2022-11-25 at 6.23.14 PM.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U01PGFLH47Q", "user_team": "T3P0PGX18", "editable": false, "size": 1207653, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T3P0PGX18-F04CWA4HUHZ/screenshot_2022-11-25_at_6.23.14_pm.png", "url_private_download": "https://files.slack.com/files-pri/T3P0PGX18-F04CWA4HUHZ/download/screenshot_2022-11-25_at_6.23.14_pm.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T3P0PGX18-F04CWA4HUHZ-515a601dbd/screenshot_2022-11-25_at_6.23.14_pm_64.png", "thumb_80": "https://files.slack.com/files-tmb/T3P0PGX18-F04CWA4HUHZ-515a601dbd/screenshot_2022-11-25_at_6.23.14_pm_80.png", "thumb_360": "https://files.slack.com/files-tmb/T3P0PGX18-F04CWA4HUHZ-515a601dbd/screenshot_2022-11-25_at_6.23.14_pm_360.png", "thumb_360_w": 360, "thumb_360_h": 234, "thumb_480": "https://files.slack.com/files-tmb/T3P0PGX18-F04CWA4HUHZ-515a601dbd/screenshot_2022-11-25_at_6.23.14_pm_480.png", "thumb_480_w": 480, "thumb_480_h": 312, "thumb_160": "https://files.slack.com/files-tmb/T3P0PGX18-F04CWA4HUHZ-515a601dbd/screenshot_2022-11-25_at_6.23.14_pm_160.png", "thumb_720": "https://files.slack.com/files-tmb/T3P0PGX18-F04CWA4HUHZ-515a601dbd/screenshot_2022-11-25_at_6.23.14_pm_720.png", "thumb_720_w": 720, "thumb_720_h": 468, "thumb_800": "https://files.slack.com/files-tmb/T3P0PGX18-F04CWA4HUHZ-515a601dbd/screenshot_2022-11-25_at_6.23.14_pm_800.png", "thumb_800_w": 800, "thumb_800_h": 520, "thumb_960": "https://files.slack.com/files-tmb/T3P0PGX18-F04CWA4HUHZ-515a601dbd/screenshot_2022-11-25_at_6.23.14_pm_960.png", "thumb_960_w": 960, "thumb_960_h": 623, "thumb_1024": "https://files.slack.com/files-tmb/T3P0PGX18-F04CWA4HUHZ-515a601dbd/screenshot_2022-11-25_at_6.23.14_pm_1024.png", "thumb_1024_w": 1024, "thumb_1024_h": 665, "original_w": 3024, "original_h": 1964, "thumb_tiny": "AwAfADC7hc42j8qdhP7v6Uu35ic0uw5zmgBp2DjGKX5PShkJPWgRkHrQAny5xj9KXaCPuijZ82c0/HGKAEHf60tVkusoW2Y9s0z7acgGPr79KAsXKKrG5IXO0fTNL9pP939adgLFFVhdEn7mPxpHvNiFtmce9ID/2Q==", "permalink": "https://steeleye.slack.com/files/U01PGFLH47Q/F04CWA4HUHZ/screenshot_2022-11-25_at_6.23.14_pm.png", "permalink_public": "https://slack-files.com/T3P0PGX18-F04CWA4HUHZ-27083a68ba", "has_rich_preview": false, "file_access": "visible"}], "upload": false, "user": "U01PGFLH47Q", "display_as_bot": false, "ts": "1669723693.753359", "client_msg_id": "53433f82-b58b-4a5a-b93b-4f068db78484", "thread_ts": "1669723693.753359", "reply_count": 1, "reply_users_count": 1, "latest_reply": "1669724298.484069", "reply_users": ["U02BKE4MRSN"], "replies": [{"user": "U02BKE4MRSN", "ts": "1669724298.484069"}], "is_locked": false, "reactions": [{"name": "bang-head", "users": ["U02BKE4MRSN"], "count": 1}], "file_uploads": [{"id": "F04CWA4HUHZ", "name": "Screenshot 2022-11-25 at 6.23.14 PM.png", "mimetype": "image/png", "user": "U01PGFLH47Q", "s3_path": "s3://test.dev.steeleye.co/slack/test/C04DX1D9JLX/Screenshot 2022-11-25 at 6.23.14 PM.png", "filetype": "png"}]}, {"type": "message", "text": "test 1", "files": [{"id": "F04CWD5RCCS", "created": 1669723672, "timestamp": 1669723672, "name": "drawio-test.pdf", "title": "drawio-test.pdf", "mimetype": "application/pdf", "filetype": "pdf", "pretty_type": "PDF", "user": "U01PGFLH47Q", "user_team": "T3P0PGX18", "editable": false, "size": 64699, "mode": "hosted", "is_external": false, "external_type": "", "is_public": false, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T3P0PGX18-F04CWD5RCCS/drawio-test.pdf", "url_private_download": "https://files.slack.com/files-pri/T3P0PGX18-F04CWD5RCCS/download/drawio-test.pdf", "media_display_type": "unknown", "thumb_pdf": "https://files.slack.com/files-tmb/T3P0PGX18-F04CWD5RCCS-b0be6c6356/drawio-test_thumb_pdf.png", "thumb_pdf_w": 910, "thumb_pdf_h": 1288, "permalink": "https://steeleye.slack.com/files/U01PGFLH47Q/F04CWD5RCCS/drawio-test.pdf", "permalink_public": "https://slack-files.com/T3P0PGX18-F04CWD5RCCS-92d07e357f", "has_rich_preview": false, "file_access": "visible"}], "upload": false, "user": "U01PGFLH47Q", "display_as_bot": false, "ts": "1669723678.216119", "blocks": [{"type": "rich_text", "block_id": "Tp3k", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "test 1"}]}]}], "client_msg_id": "f75763d1-d99c-4767-a582-d24f99944cc4", "file_uploads": [{"id": "F04CWD5RCCS", "name": "drawio-test.pdf", "mimetype": "application/pdf", "user": "U01PGFLH47Q", "s3_path": "s3://test.dev.steeleye.co/slack/test/C04DX1D9JLX/drawio-test.pdf", "filetype": "pdf"}]}, {"client_msg_id": "a2ed37e0-4431-4f6a-b894-a85ec04dcbdc", "type": "message", "text": "Formatting Test\nNew _line._ Another *_sentence._ Yet another* one\n_*~Strike through,~ no strike though,*_ ~once again~ ", "user": "U01PGFLH47Q", "ts": "1670245388.549839", "blocks": [{"type": "rich_text", "block_id": "Fcas", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Formatting Test\nNew "}, {"type": "text", "text": "line. ", "style": {"italic": true}}, {"type": "text", "text": "Another"}, {"type": "text", "text": " "}, {"type": "text", "text": "sentence.", "style": {"bold": true, "italic": true}}, {"type": "text", "text": " Yet another ", "style": {"bold": true}}, {"type": "text", "text": "one\n"}, {"type": "text", "text": "Strike through, ", "style": {"bold": true, "italic": true, "strike": true}}, {"type": "text", "text": "no strike though, ", "style": {"bold": true, "italic": true}}, {"type": "text", "text": "once again", "style": {"strike": true}}, {"type": "text", "text": " ", "style": {"bold": true}}]}]}], "team": "T3P0PGX18"}, {"client_msg_id": "23ce9d11-d359-473b-9997-9402a5602b7a", "type": "message", "text": "• ba\n• bas", "user": "U01PGFLH47Q", "ts": "1670005560.012279", "blocks": [{"type": "rich_text", "block_id": "cSwRQ", "elements": [{"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "ba"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "bas"}]}], "style": "bullet", "indent": 0, "border": 0}]}], "team": "T3P0PGX18", "edited": {"user": "U01PGFLH47Q", "ts": "1670005567.000000"}}, {"client_msg_id": "d02e7281-a17b-4770-9b13-b265afcf112b", "type": "message", "text": "&gt; adsfadfsd", "user": "U01PGFLH47Q", "ts": "1670005538.973099", "blocks": [{"type": "rich_text", "block_id": "eIs", "elements": [{"type": "rich_text_quote", "elements": [{"type": "text", "text": "adsfadfsd"}]}]}], "team": "T3P0PGX18"}, {"client_msg_id": "48771f00-86af-4209-8748-0ef5ec846d69", "type": "message", "text": "<https://api.slack.com/enterprise/discovery/methods>", "user": "U01PGFLH47Q", "ts": "1670844786.475029", "blocks": [{"type": "rich_text", "block_id": "Cfqr", "elements": [{"type": "rich_text_section", "elements": [{"type": "link", "url": "https://api.slack.com/enterprise/discovery/methods"}]}]}], "team": "T3P0PGX18"}, {"client_msg_id": "6531f00-86af-4209-8748-0ef5ec846d69", "type": "message", "text": "<https://api.slack.com/enterprise/discovery/methods>", "user": "U01PGFLH47Q", "ts": "1670844786.475029", "blocks": [{"type": "rich_text", "block_id": "Cfqr", "elements": [{"type": "rich_text_section", "elements": [{"type": "link", "url": "https://api.slack.com/enterprise/discovery/methods", "text": "Discovery link"}]}]}], "team": "T3P0PGX18"}, {"client_msg_id": "a8810239-57b2-423c-b33d-ba0c6fa5428e", "type": "message", "text": "1. asa\n2. asf\n", "user": "U01PGFLH47Q", "ts": "1670005523.079969", "blocks": [{"type": "rich_text", "block_id": "nh+", "elements": [{"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "asa"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "asf"}]}], "style": "ordered", "indent": 0, "border": 0}, {"type": "rich_text_section", "elements": []}]}], "team": "T3P0PGX18"}, {"client_msg_id": "494e7dc0-fcc6-40d0-90ae-b5e79e147ebb", "type": "message", "text": ":wohoo:", "user": "U01PGFLH47Q", "ts": "1672996330.962909", "blocks": [{"type": "rich_text", "block_id": "rU87", "elements": [{"type": "rich_text_section", "elements": [{"type": "emoji", "name": "wohoo"}]}]}], "team": "T3P0PGX18"}], "members": ["U01PGFLH47Q", "U02AMERNUFM"], "info": {"id": "C04DX1D9JLX", "name": "slack-test", "created": 1670503770, "member_count": 2, "is_general": false, "is_private": false, "is_im": false, "is_mpim": false, "is_file": false, "is_deleted": false, "is_archived": false, "creator": "U01PGFLH47Q", "is_moved": false, "is_shared": false, "name_normalized": "slack-test", "is_global_shared": false, "is_org_shared": false, "is_org_mandatory": false, "is_org_default": false, "previous_names": [], "purpose": {"text": "", "set_by": "", "date_set": 0}, "topic": {"text": "", "set_by": "", "date_set": 0}, "retention": {"type": "default", "duration": 0}, "shared": {"shared_team_ids": [], "connected_team_ids": [], "internal_team_ids": [], "connected_limited_team_ids": []}, "has_guests": false}, "edits": []}