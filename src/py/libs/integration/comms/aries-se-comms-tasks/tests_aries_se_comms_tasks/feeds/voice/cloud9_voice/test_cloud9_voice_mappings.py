import logging
import pandas as pd
from aries_se_comms_tasks.feeds.voice.cloud9_voice.cloud9_voice_mappings import Cloud9VoiceMappings
from aries_se_comms_tasks.voice.static import CallColumns
from aries_se_core_tasks.utilities.helpers_for_tests import (  # type: ignore[attr-defined]
    sort_list_columns,
)
from se_enums.cloud import CloudProviderEnum

logger = logging.getLogger(__name__)


class TestCloud9VoiceMappings:
    """Test E2E mappings for Cloud9 voice."""

    def test_end_to_end_mappings(self, source_frame, expected_result):
        task = Cloud9VoiceMappings(
            name="Cloud9VoiceMappings",
            logger=logger,
            source_frame=source_frame,
            realm="shrenik.dev.steeleye.co",
            source_file_url="s3://shrenik.dev.steeleye.co/aries/ingest/cloud9_voice/2023/11/09/cDt-20230310-100940-simaujla1-7242272-1.json",
            cloud_provider=CloudProviderEnum.AWS,
        )

        result = task.process()
        sort_list_columns(
            list_of_list_columns=[
                CallColumns.IDENTIFIERS_TO_IDS,
                CallColumns.IDENTIFIERS_ALL_IDS,
            ],
            result_df=result,
            expected_result_df=expected_result,
        )

        pd.testing.assert_frame_equal(left=result, right=expected_result, check_dtype=False)
