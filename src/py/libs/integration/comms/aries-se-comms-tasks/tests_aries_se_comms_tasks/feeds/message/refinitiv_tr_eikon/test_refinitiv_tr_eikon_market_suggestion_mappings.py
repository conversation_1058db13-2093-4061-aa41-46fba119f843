# type: ignore
import pandas as pd
import pytest
from aries_se_comms_tasks.feeds.message.refinitiv_tr_eikon.helper_functions import (
    SkipIfEmptyEikonMappingsSourceFrame,
)
from aries_se_comms_tasks.transform_maps.market_suggestion_transform_maps import (
    refinitiv_tr_eikon_chat_transform_market_suggestions_map,
)


class TestRefinitivTrEikonMarketSuggestionMappings:
    def test_empty_input(self):
        with pytest.raises(SkipIfEmptyEikonMappingsSourceFrame) as e:
            self._run_mappings(
                source_frame=pd.DataFrame(),
            )
        assert e.match("No MarketSuggestion records found. Nothing to transform.")

    def test_end_to_end(
        self,
        input_market_suggestion_df: pd.DataFrame,
        expected_market_suggestion_parsed_df: pd.DataFrame,
    ):
        result = self._run_mappings(
            source_frame=input_market_suggestion_df,
        )
        pd.testing.assert_frame_equal(left=result, right=expected_market_suggestion_parsed_df)

    @staticmethod
    def _run_mappings(
        source_frame: pd.DataFrame,
    ):
        transformation = refinitiv_tr_eikon_chat_transform_market_suggestions_map.transformation(
            tenant="test"
        )
        mapping_task = transformation(
            source_frame=source_frame,
            input_file_path="s3://test.steeleye.co/random_path/tr-eikon_messages_with_attachments.zip",  # noqa E501
        )
        return mapping_task.process()
