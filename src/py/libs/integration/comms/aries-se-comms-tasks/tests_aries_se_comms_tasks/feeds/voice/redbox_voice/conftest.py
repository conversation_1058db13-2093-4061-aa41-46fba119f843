import pandas as pd
import pytest
from se_elastic_schema.static.transcript import TranscriptionStatusEnum


@pytest.fixture()
def source_frame_redbox() -> pd.DataFrame:
    return pd.DataFrame(
        data={
            "transcriptions": [None],
            "status": ["Success"],
            "mediaTypeIncluded": [["audio/wav"]],
            "mediaExported": [True],
            "nasMonitorCallId": [55813],
            "replayUrl": [None],
            "callIdentifier.recorderId": [12194],
            "callIdentifier.nasId": [1],
            "callIdentifier.callId": [17061],
            "callIdentifier.redBoxCallGuid": ["00000000-0000-0000-0000-000000000000"],
            "metadata.startDateTime": ["2022-05-16T16:31:12.87+01:00"],
            "metadata.callId": [17061],
            "metadata.protocolProcessorId": [1835008],
            "metadata.firstFrame": [214],
            "metadata.lastFrame": [214],
            "metadata.callFlags": [4196532227],
            "metadata.deviceId": [19],
            "metadata.endDateTime": ["2022-05-16T16:31:20.09+01:00"],
            "metadata.maxParties": [1],
            "metadata.duration": ["00:00:07.2190000"],
            "metadata.extension": [None],
            "metadata.otherParty": [None],
            "metadata.direction": ["Unknown"],
            "metadata.agentGroup": [None],
            "metadata.channelName": ["3011"],
            "metadata.callerNumber": [None],
            "metadata.callerName": [None],
            "metadata.calledNumber": [None],
            "metadata.calledName2": [None],
            "metadata.userId": [None],
            "metadata.nasFirstFrame": [0],
            "metadata.nasLastFrame": [0],
            "metadata.deviceReference": [None],
            "metadata.digitalSignature": ["DFB21140F3ADF0A1B2CAFBD2ED88370D"],
            "metadata.extensionName": [None],
            "metadata.segmentId": [0],
            "metadata.nasId": [0],
            "metadata.nasCallId": [17061],
            "metadata.sipCallId": [None],
            "metadata.nasDigitalSignature": [None],
            "metadata.ctiCall": [False],
            "metadata.rtpChannel": ["IPTrade 17"],
            "metadata.redirectingNumber": [None],
            "metadata.redirectingName": [None],
            "metadata.redirectionNumber": [None],
            "metadata.redirectionName": [None],
            "metadata.ipTradeCallType": [0],
            "metadata.374": [None],
            "metadata.redBoxCallGuid": ["00002fa2-0000-42a5-2022-051616311287"],
            "metadata_file_url": [
                "s3://diogo.dev.steeleye.co/flows/comms-voice-redbox/2022-05-17T043055-1476-metadata.json"  # noqa: E501
            ],
            "recording_file_url": [
                "s3://diogo.dev.steeleye.co/attachments/stream/comms-voice-redbox/diogo.dev.steeleye.co/flows/comms-voice-redbox/2022-05-17T043055-1476-media_audio_wav.wav"  # noqa: E501
            ],
            "metadata": [None],
            "sipCallId": [None],
        }
    )


@pytest.fixture()
def expected_result_redbox() -> pd.DataFrame:
    return pd.DataFrame(
        data={
            "callDuration": ["00:00:07"],
            "conferenceCall": [False],
            "direction": [None],
            "id": ["12194|1|17061"],
            "identifiers.allCountryCodes": [None],
            "identifiers.allIds": [["3011", "iptrade17"]],
            "identifiers.fromDeviceId": [None],
            "identifiers.fromId": ["iptrade17"],
            "identifiers.fromIdAddlInfo": [{"raw": "iptrade17", "countryCode": None}],
            "identifiers.fromUserId": [None],
            "identifiers.hostId": [None],
            "identifiers.toDeviceId": [19],
            "identifiers.toIds": [["3011"]],
            "identifiers.toIdsAddlInfo": [[{"raw": "3011", "countryCode": None}]],
            "identifiers.toUserId": [None],
            "metadata.source.client": ["RedBox"],
            "metadata.source.fileInfo.location.bucket": ["diogo.dev.steeleye.co"],
            "metadata.source.fileInfo.location.key": [
                "flows/comms-voice-redbox/2022-05-17T043055-1476-metadata.json"
            ],
            "sourceIndex": [0],
            "sourceKey": ["s3://test.steeleye.co/redbox/input_file.ndjson"],
            "timestamps.localTimestampStart": ["2022-05-16T16:31:12.870000Z"],
            "timestamps.timestampStart": ["2022-05-16T15:31:12.870000Z"],
            "timestamps.localTimestampEnd": ["2022-05-16T16:31:20.090000Z"],
            "timestamps.timestampEnd": ["2022-05-16T15:31:20.090000Z"],
            "waveform.location.bucket": ["test.steeleye.co"],
            "waveform.location.key": [
                "attachments/waveform/redbox_voice_transform/12194|1|17061/waveform.json"  # noqa: E501
            ],
        }
    )


@pytest.fixture()
def source_frame_iv_redbox() -> pd.DataFrame:
    return pd.DataFrame(
        [
            {
                "transcriptions": None,
                "status": "Success",
                "mediaTypeIncluded": ["audio/wav"],
                "mediaExported": True,
                "nasMonitorCallId": 2292113,
                "replayUrl": None,
                "silent": False,
                "callIdentifier.recorderId": 7720,
                "callIdentifier.nasId": 8,
                "callIdentifier.callId": 9278851,
                "callIdentifier.redBoxCallGuid": "00000000-0000-0000-0000-000000000000",
                "metadata.startDateTime": "2024-02-09T11:05:19.9+00:00",
                "metadata.callId": 9278851,
                "metadata.protocolProcessorId": 1835008,
                "metadata.firstFrame": 1716,
                "metadata.lastFrame": 1716,
                "metadata.callFlags": 3800104963,
                "metadata.deviceId": 1003,
                "metadata.endDateTime": "2024-02-09T11:06:19.18+00:00",
                "metadata.maxParties": 2,
                "metadata.duration": "00:00:59",
                "metadata.extension": "8359",
                "metadata.otherParty": "07977353980",
                "metadata.direction": "Outgoing",
                "metadata.agentGroup": None,
                "metadata.channelName": "George Cranfield - DCM",
                "metadata.callerNumber": "8359",
                "metadata.callerName": "George Cranfield - DCM",
                "metadata.calledNumber": "07977353980",
                "metadata.calledName2": None,
                "metadata.userId": None,
                "metadata.nasFirstFrame": 0,
                "metadata.nasLastFrame": 0,
                "metadata.deviceReference": None,
                "metadata.digitalSignature": "77B1DEDD90E411C93423DC57137AE4CF",
                "metadata.extensionName": "George Cranfield - DCM",
                "metadata.segmentId": 9278847,
                "metadata.nasId": 0,
                "metadata.nasCallId": 9278851,
                "metadata.sipCallId": None,
                "metadata.nasDigitalSignature": None,
                "metadata.callSafeNASId": 0.0,
                "metadata.callSafeNASFirstFrame": 0.0,
                "metadata.callSafeNASLastFrame": 0.0,
                "metadata.callSafeState": 0.0,
                "metadata.callSafeTag": None,
                "metadata.callSafeDigitalSignature": None,
                "metadata.ctiCall": False,
                "metadata.rtpChannel": None,
                "metadata.redirectingNumber": None,
                "metadata.redirectingName": None,
                "metadata.redirectionNumber": None,
                "metadata.redirectionName": None,
                "metadata.redBoxCallGuid": "00001e28-008d-9583-2024-020911051990",
                "metadata.callingName": None,
                "metadata.callType": None,
                "metadata.otherPartyName": None,
                "metadata.callingNumber": None,
                "metadata.externalCallId": None,
                "metadata.appearance": None,
                "metadata.mediaType": pd.NA,
                "metadata.conversationId": None,
                "metadata.mail": None,
                "metadata.otherPartyMail": None,
                "metadata.maxParticipants": pd.NA,
                "metadata.recordingReason": None,
                "metadata_local_file_path": "dummy",
                "metadata_file_url": "s3://test.steeleye.co/aries/ingress/92656.metadata.json",
                "transcriptSourceKey": "dummy",
                "recordingSourceKey": "dummy",
                "recording_local_file_path": "dummy",
                "transcriptionStatus": TranscriptionStatusEnum.COMPLETED_SUCCESSFULLY,
            },
            {
                "transcriptions": None,
                "status": "Success",
                "mediaTypeIncluded": ["audio/wav"],
                "mediaExported": True,
                "nasMonitorCallId": 2292109,
                "replayUrl": None,
                "silent": False,
                "callIdentifier.recorderId": 7720,
                "callIdentifier.nasId": 8,
                "callIdentifier.callId": 9278847,
                "callIdentifier.redBoxCallGuid": "00000000-0000-0000-0000-000000000000",
                "metadata.startDateTime": "2024-02-09T11:00:43.43+00:00",
                "metadata.callId": 9278847,
                "metadata.protocolProcessorId": 1835008,
                "metadata.firstFrame": 1716,
                "metadata.lastFrame": 1716,
                "metadata.callFlags": 3800104963,
                "metadata.deviceId": 1003,
                "metadata.endDateTime": "2024-02-09T11:04:19.78+00:00",
                "metadata.maxParties": 2,
                "metadata.duration": "00:03:36.3440000",
                "metadata.extension": "8359",
                "metadata.otherParty": "07977353980",
                "metadata.direction": "Outgoing",
                "metadata.agentGroup": None,
                "metadata.channelName": "George Cranfield - DCM",
                "metadata.callerNumber": "8359",
                "metadata.callerName": "George Cranfield - DCM",
                "metadata.calledNumber": "07977353980",
                "metadata.calledName2": None,
                "metadata.userId": None,
                "metadata.nasFirstFrame": 0,
                "metadata.nasLastFrame": 0,
                "metadata.deviceReference": None,
                "metadata.digitalSignature": "28F2E610699016D39389F920CA8662DA",
                "metadata.extensionName": "George Cranfield - DCM",
                "metadata.segmentId": 9278847,
                "metadata.nasId": 0,
                "metadata.nasCallId": 9278847,
                "metadata.sipCallId": None,
                "metadata.nasDigitalSignature": None,
                "metadata.callSafeNASId": 0.0,
                "metadata.callSafeNASFirstFrame": 0.0,
                "metadata.callSafeNASLastFrame": 0.0,
                "metadata.callSafeState": 0.0,
                "metadata.callSafeTag": None,
                "metadata.callSafeDigitalSignature": None,
                "metadata.ctiCall": False,
                "metadata.rtpChannel": None,
                "metadata.redirectingNumber": None,
                "metadata.redirectingName": None,
                "metadata.redirectionNumber": None,
                "metadata.redirectionName": None,
                "metadata.redBoxCallGuid": "00001e28-008d-957f-2024-020911004343",
                "metadata.callingName": None,
                "metadata.callType": None,
                "metadata.otherPartyName": None,
                "metadata.callingNumber": None,
                "metadata.externalCallId": None,
                "metadata.appearance": None,
                "metadata.mediaType": pd.NA,
                "metadata.conversationId": None,
                "metadata.mail": None,
                "metadata.otherPartyMail": None,
                "metadata.maxParticipants": pd.NA,
                "metadata.recordingReason": None,
                "metadata_local_file_path": "dummy",
                "metadata_file_url": "s3://test.steeleye.co/aries/ingress/92657.metadata.json",
                "transcriptSourceKey": "dummy",
                "recordingSourceKey": "dummy",
                "recording_local_file_path": "dummy",
                "transcriptionStatus": TranscriptionStatusEnum.COMPLETED_SUCCESSFULLY,
            },
            {
                "transcriptions": None,
                "status": "Success",
                "mediaTypeIncluded": ["audio/wav"],
                "mediaExported": True,
                "nasMonitorCallId": 27985,
                "replayUrl": None,
                "silent": False,
                "callIdentifier.recorderId": 12518,
                "callIdentifier.nasId": 1,
                "callIdentifier.callId": 20414,
                "callIdentifier.redBoxCallGuid": "00000000-0000-0000-0000-000000000000",
                "metadata.startDateTime": "2024-02-12T16:59:17.34+00:00",
                "metadata.callId": 20414,
                "metadata.protocolProcessorId": 3342337,
                "metadata.firstFrame": 7726,
                "metadata.lastFrame": 7727,
                "metadata.callFlags": 241369091,
                "metadata.deviceId": 28,
                "metadata.endDateTime": "2024-02-12T17:02:56.86+00:00",
                "metadata.maxParties": 2,
                "metadata.duration": "00:03:39.5180000",
                "metadata.extension": "Tarek.Hasan",
                "metadata.otherParty": "Amy.Chapman",
                "metadata.direction": "Incoming",
                "metadata.agentGroup": None,
                "metadata.channelName": "Tarek.Hasan",
                "metadata.callerNumber": None,
                "metadata.callerName": None,
                "metadata.calledNumber": "Tarek.Hasan",
                "metadata.calledName2": "Hasan, Tarek",
                "metadata.userId": None,
                "metadata.nasFirstFrame": 109625,
                "metadata.nasLastFrame": 0,
                "metadata.deviceReference": None,
                "metadata.digitalSignature": "40D30CD8B49430E6CDDEB7737A169F99",
                "metadata.extensionName": "Hasan, Tarek",
                "metadata.segmentId": 0,
                "metadata.nasId": 0,
                "metadata.nasCallId": 20414,
                "metadata.sipCallId": None,
                "metadata.nasDigitalSignature": None,
                "metadata.callSafeNASId": pd.NA,
                "metadata.callSafeNASFirstFrame": pd.NA,
                "metadata.callSafeNASLastFrame": pd.NA,
                "metadata.callSafeState": pd.NA,
                "metadata.callSafeTag": None,
                "metadata.callSafeDigitalSignature": None,
                "metadata.ctiCall": None,
                "metadata.rtpChannel": None,
                "metadata.redirectingNumber": None,
                "metadata.redirectingName": None,
                "metadata.redirectionNumber": None,
                "metadata.redirectionName": None,
                "metadata.redBoxCallGuid": "000030e6-0000-4fbe-2024-021216591734",
                "metadata.callingName": "Chapman, Amy",
                "metadata.callType": "Call_Teams",
                "metadata.otherPartyName": "Chapman, Amy",
                "metadata.callingNumber": "Amy.Chapman",
                "metadata.externalCallId": "801f5f00-e8f5-47e7-b304-90edd4fed617",
                "metadata.appearance": "Internal",
                "metadata.mediaType": 1.0,
                "metadata.conversationId": "c2ca0538-cb6c-4e02-8bed-f413fc827f6f",
                "metadata.mail": "<EMAIL>",
                "metadata.otherPartyMail": "<EMAIL>",
                "metadata.maxParticipants": 2.0,
                "metadata.recordingReason": "TeamsRecordingPolicy",
                "metadata_local_file_path": "dummy",
                "metadata_file_url": "s3://test.steeleye.co/aries/ingress/92658.metadata.json",
                "transcriptSourceKey": "dummy",
                "recordingSourceKey": "dummy",
                "recording_local_file_path": "dummy",
                "transcriptionStatus": TranscriptionStatusEnum.COMPLETED_SUCCESSFULLY,
            },
            {
                "transcriptions": None,
                "status": "Success",
                "mediaTypeIncluded": ["audio/wav"],
                "mediaExported": True,
                "nasMonitorCallId": 27890,
                "replayUrl": None,
                "silent": False,
                "callIdentifier.recorderId": 12518,
                "callIdentifier.nasId": 1,
                "callIdentifier.callId": 20316,
                "callIdentifier.redBoxCallGuid": "00000000-0000-0000-0000-000000000000",
                "metadata.startDateTime": "2024-02-12T07:29:26.59+00:00",
                "metadata.callId": 20316,
                "metadata.protocolProcessorId": 3342337,
                "metadata.firstFrame": 7172,
                "metadata.lastFrame": 7178,
                "metadata.callFlags": 241369091,
                "metadata.deviceId": 41,
                "metadata.endDateTime": "2024-02-12T07:35:04.82+00:00",
                "metadata.maxParties": 2,
                "metadata.duration": "00:05:38.2170000",
                "metadata.extension": "Takunori.Sumimoto",
                "metadata.otherParty": "Meeting",
                "metadata.direction": "Outgoing",
                "metadata.agentGroup": None,
                "metadata.channelName": "Takunori.Sumimoto",
                "metadata.callerNumber": None,
                "metadata.callerName": None,
                "metadata.calledNumber": "Meeting",
                "metadata.calledName2": "Meeting",
                "metadata.userId": None,
                "metadata.nasFirstFrame": 109071,
                "metadata.nasLastFrame": 0,
                "metadata.deviceReference": None,
                "metadata.digitalSignature": "BAEEC724507DD03E2E961B0499F14361",
                "metadata.extensionName": "Sumimoto, Takunori",
                "metadata.segmentId": 0,
                "metadata.nasId": 0,
                "metadata.nasCallId": 20316,
                "metadata.sipCallId": None,
                "metadata.nasDigitalSignature": None,
                "metadata.callSafeNASId": pd.NA,
                "metadata.callSafeNASFirstFrame": pd.NA,
                "metadata.callSafeNASLastFrame": pd.NA,
                "metadata.callSafeState": pd.NA,
                "metadata.callSafeTag": None,
                "metadata.callSafeDigitalSignature": None,
                "metadata.ctiCall": None,
                "metadata.rtpChannel": None,
                "metadata.redirectingNumber": None,
                "metadata.redirectingName": None,
                "metadata.redirectionNumber": None,
                "metadata.redirectionName": None,
                "metadata.redBoxCallGuid": "000030e6-0000-4f5c-2024-021207292659",
                "metadata.callingName": "Sumimoto, Takunori",
                "metadata.callType": "Meeting_MeetNow",
                "metadata.otherPartyName": "Meeting",
                "metadata.callingNumber": "Takunori.Sumimoto",
                "metadata.externalCallId": "771f5e00-89ec-484a-a464-5b247996957b",
                "metadata.appearance": "Internal",
                "metadata.mediaType": 1.0,
                "metadata.conversationId": "7c3a6d22-77d8-4116-adfb-d58c092c74c6",
                "metadata.mail": "<EMAIL>",
                "metadata.otherPartyMail": None,
                "metadata.maxParticipants": 6.0,
                "metadata.recordingReason": "TeamsRecordingPolicy",
                "metadata_local_file_path": "dummy",
                "metadata_file_url": "s3://test.steeleye.co/aries/ingress/92659.metadata.json",
                "transcriptSourceKey": "dummy",
                "recordingSourceKey": "dummy",
                "recording_local_file_path": "dummy",
                "transcriptionStatus": TranscriptionStatusEnum.COMPLETED_SUCCESSFULLY,
            },
        ]
    )


@pytest.fixture()
def expected_result_iv_redbox() -> pd.DataFrame:
    return pd.DataFrame(
        [
            {
                "callDuration": "00:00:59",
                "conferenceCall": False,
                "direction": "outgoing",
                "id": "7720|8|9278851",
                "identifiers.allCountryCodes": ["GB"],
                "identifiers.allIds": ["+447977353980", "07977353980", "8359"],
                "identifiers.fromDeviceId": 1003,
                "identifiers.fromId": "8359",
                "identifiers.fromIdAddlInfo": {"raw": "8359", "countryCode": None},
                "identifiers.fromUserId": "George Cranfield - DCM",
                "identifiers.hostId": None,
                "identifiers.toDeviceId": None,
                "identifiers.toIds": ["+447977353980"],
                "identifiers.toIdsAddlInfo": [{"raw": "07977353980", "countryCode": "GB"}],
                "identifiers.toUserId": None,
                "metadata.source.client": "RedBox",
                "metadata.source.fileInfo.location.bucket": "test.steeleye.co",
                "metadata.source.fileInfo.location.key": "aries/ingress/92656.metadata.json",
                "sourceIndex": 0,
                "sourceKey": "s3://test.steeleye.co/redbox/input_file.ndjson",
                "timestamps.localTimestampStart": "2024-02-09T11:05:19.900000Z",
                "timestamps.timestampStart": "2024-02-09T11:05:19.900000Z",
                "timestamps.localTimestampEnd": "2024-02-09T11:06:19.180000Z",
                "timestamps.timestampEnd": "2024-02-09T11:06:19.180000Z",
                "waveform.location.bucket": "test.steeleye.co",
                "waveform.location.key": "attachments/waveform/iv_redbox_voice_transform/"
                "7720|8|9278851/waveform.json",
            },
            {
                "callDuration": "00:03:36",
                "conferenceCall": False,
                "direction": "outgoing",
                "id": "7720|8|9278847",
                "identifiers.allCountryCodes": ["GB"],
                "identifiers.allIds": ["+447977353980", "07977353980", "8359"],
                "identifiers.fromDeviceId": 1003,
                "identifiers.fromId": "8359",
                "identifiers.fromIdAddlInfo": {"raw": "8359", "countryCode": None},
                "identifiers.fromUserId": "George Cranfield - DCM",
                "identifiers.hostId": None,
                "identifiers.toDeviceId": None,
                "identifiers.toIds": ["+447977353980"],
                "identifiers.toIdsAddlInfo": [{"raw": "07977353980", "countryCode": "GB"}],
                "identifiers.toUserId": None,
                "metadata.source.client": "RedBox",
                "metadata.source.fileInfo.location.bucket": "test.steeleye.co",
                "metadata.source.fileInfo.location.key": "aries/ingress/92657.metadata.json",
                "sourceIndex": 1,
                "sourceKey": "s3://test.steeleye.co/redbox/input_file.ndjson",
                "timestamps.localTimestampStart": "2024-02-09T11:00:43.430000Z",
                "timestamps.timestampStart": "2024-02-09T11:00:43.430000Z",
                "timestamps.localTimestampEnd": "2024-02-09T11:04:19.780000Z",
                "timestamps.timestampEnd": "2024-02-09T11:04:19.780000Z",
                "waveform.location.bucket": "test.steeleye.co",
                "waveform.location.key": "attachments/waveform/iv_redbox_voice_transform/"
                "7720|8|9278847/waveform.json",
            },
            {
                "callDuration": "00:03:39",
                "conferenceCall": False,
                "direction": "incoming",
                "id": "12518|1|20414",
                "identifiers.allCountryCodes": None,
                "identifiers.allIds": ["amy.chapman", "tarek.hasan"],
                "identifiers.fromDeviceId": None,
                "identifiers.fromId": "amy.chapman",
                "identifiers.fromIdAddlInfo": {"raw": "amy.chapman", "countryCode": None},
                "identifiers.fromUserId": None,
                "identifiers.hostId": None,
                "identifiers.toDeviceId": 28,
                "identifiers.toIds": ["tarek.hasan"],
                "identifiers.toIdsAddlInfo": [{"raw": "tarek.hasan", "countryCode": None}],
                "identifiers.toUserId": "Tarek.Hasan",
                "metadata.source.client": "RedBox",
                "metadata.source.fileInfo.location.bucket": "test.steeleye.co",
                "metadata.source.fileInfo.location.key": "aries/ingress/92658.metadata.json",
                "sourceIndex": 2,
                "sourceKey": "s3://test.steeleye.co/redbox/input_file.ndjson",
                "timestamps.localTimestampStart": "2024-02-12T16:59:17.340000Z",
                "timestamps.timestampStart": "2024-02-12T16:59:17.340000Z",
                "timestamps.localTimestampEnd": "2024-02-12T17:02:56.860000Z",
                "timestamps.timestampEnd": "2024-02-12T17:02:56.860000Z",
                "waveform.location.bucket": "test.steeleye.co",
                "waveform.location.key": "attachments/waveform/iv_redbox_voice_transform/"
                "12518|1|20414/waveform.json",
            },
            {
                "callDuration": "00:05:38",
                "conferenceCall": False,
                "direction": "outgoing",
                "id": "12518|1|20316",
                "identifiers.allCountryCodes": None,
                "identifiers.allIds": ["meeting", "takunori.sumimoto"],
                "identifiers.fromDeviceId": 41,
                "identifiers.fromId": "takunori.sumimoto",
                "identifiers.fromIdAddlInfo": {"raw": "takunori.sumimoto", "countryCode": None},
                "identifiers.fromUserId": "Takunori.Sumimoto",
                "identifiers.hostId": None,
                "identifiers.toDeviceId": None,
                "identifiers.toIds": ["meeting"],
                "identifiers.toIdsAddlInfo": [{"raw": "meeting", "countryCode": None}],
                "identifiers.toUserId": None,
                "metadata.source.client": "RedBox",
                "metadata.source.fileInfo.location.bucket": "test.steeleye.co",
                "metadata.source.fileInfo.location.key": "aries/ingress/92659.metadata.json",
                "sourceIndex": 3,
                "sourceKey": "s3://test.steeleye.co/redbox/input_file.ndjson",
                "timestamps.localTimestampStart": "2024-02-12T07:29:26.590000Z",
                "timestamps.timestampStart": "2024-02-12T07:29:26.590000Z",
                "timestamps.localTimestampEnd": "2024-02-12T07:35:04.820000Z",
                "timestamps.timestampEnd": "2024-02-12T07:35:04.820000Z",
                "waveform.location.bucket": "test.steeleye.co",
                "waveform.location.key": "attachments/waveform/iv_redbox_voice_transform/"
                "12518|1|20316/waveform.json",
            },
        ]
    )


@pytest.fixture()
def source_frame_dcme_iv_redbox() -> pd.DataFrame:
    return pd.DataFrame(
        [
            {
                "transcriptions": None,
                "status": "Success",
                "mediaTypeIncluded": ["audio/wav"],
                "mediaExported": True,
                "nasMonitorCallId": 2292113,
                "replayUrl": None,
                "silent": False,
                "callIdentifier.recorderId": 7720,
                "callIdentifier.nasId": 8,
                "callIdentifier.callId": 9278851,
                "callIdentifier.redBoxCallGuid": "00000000-0000-0000-0000-000000000000",
                "metadata.startDateTime": "2024-02-09T11:05:19.9+00:00",
                "metadata.callId": 9278851,
                "metadata.protocolProcessorId": 1835008,
                "metadata.firstFrame": 1716,
                "metadata.lastFrame": 1716,
                "metadata.callFlags": 3800104963,
                "metadata.deviceId": 1003,
                "metadata.endDateTime": "2024-02-09T11:06:19.18+00:00",
                "metadata.maxParties": 2,
                "metadata.duration": "00:00:59.2810000",
                "metadata.extension": "8359",
                "metadata.otherParty": "07977353980",
                "metadata.direction": "Outgoing",
                "metadata.agentGroup": None,
                "metadata.channelName": "George Cranfield - DCM",
                "metadata.callerNumber": "8359",
                "metadata.callerName": "George Cranfield - DCM",
                "metadata.calledNumber": "07977353980",
                "metadata.calledName2": None,
                "metadata.userId": None,
                "metadata.nasFirstFrame": 0,
                "metadata.nasLastFrame": 0,
                "metadata.deviceReference": None,
                "metadata.digitalSignature": "77B1DEDD90E411C93423DC57137AE4CF",
                "metadata.extensionName": "George Cranfield - DCM",
                "metadata.segmentId": 9278847,
                "metadata.nasId": 0,
                "metadata.nasCallId": 9278851,
                "metadata.sipCallId": None,
                "metadata.nasDigitalSignature": None,
                "metadata.callSafeNASId": 0.0,
                "metadata.callSafeNASFirstFrame": 0.0,
                "metadata.callSafeNASLastFrame": 0.0,
                "metadata.callSafeState": 0.0,
                "metadata.callSafeTag": None,
                "metadata.callSafeDigitalSignature": None,
                "metadata.ctiCall": False,
                "metadata.rtpChannel": None,
                "metadata.redirectingNumber": None,
                "metadata.redirectingName": None,
                "metadata.redirectionNumber": None,
                "metadata.redirectionName": None,
                "metadata.redBoxCallGuid": "00001e28-008d-9583-2024-020911051990",
                "metadata.callingName": None,
                "metadata.callType": None,
                "metadata.otherPartyName": None,
                "metadata.callingNumber": None,
                "metadata.externalCallId": None,
                "metadata.appearance": None,
                "metadata.mediaType": pd.NA,
                "metadata.conversationId": None,
                "metadata.mail": None,
                "metadata.otherPartyMail": None,
                "metadata.maxParticipants": pd.NA,
                "metadata.recordingReason": None,
                "metadata_local_file_path": "dummy",
                "metadata_file_url": "s3://dcme.steeleye.co/aries/ingress/92656.metadata.json",
                "transcriptSourceKey": "dummy",
                "recordingSourceKey": "dummy",
                "recording_local_file_path": "dummy",
                "transcriptionStatus": TranscriptionStatusEnum.COMPLETED_SUCCESSFULLY,
            },
            {
                "transcriptions": None,
                "status": "Success",
                "mediaTypeIncluded": ["audio/wav"],
                "mediaExported": True,
                "nasMonitorCallId": 2292109,
                "replayUrl": None,
                "silent": False,
                "callIdentifier.recorderId": 7720,
                "callIdentifier.nasId": 8,
                "callIdentifier.callId": 9278847,
                "callIdentifier.redBoxCallGuid": "00000000-0000-0000-0000-000000000000",
                "metadata.startDateTime": "2024-02-09T11:00:43.43+00:00",
                "metadata.callId": 9278847,
                "metadata.protocolProcessorId": 1835008,
                "metadata.firstFrame": 1716,
                "metadata.lastFrame": 1716,
                "metadata.callFlags": 3800104963,
                "metadata.deviceId": 1003,
                "metadata.endDateTime": "2024-02-09T11:04:19.78+00:00",
                "metadata.maxParties": 2,
                "metadata.duration": "00:03:36.3440000",
                "metadata.extension": "8359",
                "metadata.otherParty": "07977353980",
                "metadata.direction": "Outgoing",
                "metadata.agentGroup": None,
                "metadata.channelName": "George Cranfield - DCM",
                "metadata.callerNumber": "8359",
                "metadata.callerName": "George Cranfield - DCM",
                "metadata.calledNumber": "07977353980",
                "metadata.calledName2": None,
                "metadata.userId": None,
                "metadata.nasFirstFrame": 0,
                "metadata.nasLastFrame": 0,
                "metadata.deviceReference": None,
                "metadata.digitalSignature": "28F2E610699016D39389F920CA8662DA",
                "metadata.extensionName": "George Cranfield - DCM",
                "metadata.segmentId": 9278847,
                "metadata.nasId": 0,
                "metadata.nasCallId": 9278847,
                "metadata.sipCallId": None,
                "metadata.nasDigitalSignature": None,
                "metadata.callSafeNASId": 0.0,
                "metadata.callSafeNASFirstFrame": 0.0,
                "metadata.callSafeNASLastFrame": 0.0,
                "metadata.callSafeState": 0.0,
                "metadata.callSafeTag": None,
                "metadata.callSafeDigitalSignature": None,
                "metadata.ctiCall": False,
                "metadata.rtpChannel": None,
                "metadata.redirectingNumber": None,
                "metadata.redirectingName": None,
                "metadata.redirectionNumber": None,
                "metadata.redirectionName": None,
                "metadata.redBoxCallGuid": "00001e28-008d-957f-2024-020911004343",
                "metadata.callingName": None,
                "metadata.callType": None,
                "metadata.otherPartyName": None,
                "metadata.callingNumber": None,
                "metadata.externalCallId": None,
                "metadata.appearance": None,
                "metadata.mediaType": pd.NA,
                "metadata.conversationId": None,
                "metadata.mail": None,
                "metadata.otherPartyMail": None,
                "metadata.maxParticipants": pd.NA,
                "metadata.recordingReason": None,
                "metadata_local_file_path": "dummy",
                "metadata_file_url": "s3://dcme.steeleye.co/aries/ingress/92657.metadata.json",
                "transcriptSourceKey": "dummy",
                "recordingSourceKey": "dummy",
                "recording_local_file_path": "dummy",
                "transcriptionStatus": TranscriptionStatusEnum.COMPLETED_SUCCESSFULLY,
            },
            {
                "transcriptions": None,
                "status": "Success",
                "mediaTypeIncluded": ["audio/wav"],
                "mediaExported": True,
                "nasMonitorCallId": 27985,
                "replayUrl": None,
                "silent": False,
                "callIdentifier.recorderId": 12518,
                "callIdentifier.nasId": 1,
                "callIdentifier.callId": 20414,
                "callIdentifier.redBoxCallGuid": "00000000-0000-0000-0000-000000000000",
                "metadata.startDateTime": "2024-02-12T16:59:17.34+00:00",
                "metadata.callId": 20414,
                "metadata.protocolProcessorId": 3342337,
                "metadata.firstFrame": 7726,
                "metadata.lastFrame": 7727,
                "metadata.callFlags": 241369091,
                "metadata.deviceId": 28,
                "metadata.endDateTime": "2024-02-12T17:02:56.86+00:00",
                "metadata.maxParties": 2,
                "metadata.duration": "00:03:39.5180000",
                "metadata.extension": "Tarek.Hasan",
                "metadata.otherParty": "Amy.Chapman",
                "metadata.direction": "Incoming",
                "metadata.agentGroup": None,
                "metadata.channelName": "Tarek.Hasan",
                "metadata.callerNumber": None,
                "metadata.callerName": None,
                "metadata.calledNumber": "Tarek.Hasan",
                "metadata.calledName2": "Hasan, Tarek",
                "metadata.userId": None,
                "metadata.nasFirstFrame": 109625,
                "metadata.nasLastFrame": 0,
                "metadata.deviceReference": None,
                "metadata.digitalSignature": "40D30CD8B49430E6CDDEB7737A169F99",
                "metadata.extensionName": "Hasan, Tarek",
                "metadata.segmentId": 0,
                "metadata.nasId": 0,
                "metadata.nasCallId": 20414,
                "metadata.sipCallId": None,
                "metadata.nasDigitalSignature": None,
                "metadata.callSafeNASId": pd.NA,
                "metadata.callSafeNASFirstFrame": pd.NA,
                "metadata.callSafeNASLastFrame": pd.NA,
                "metadata.callSafeState": pd.NA,
                "metadata.callSafeTag": None,
                "metadata.callSafeDigitalSignature": None,
                "metadata.ctiCall": None,
                "metadata.rtpChannel": None,
                "metadata.redirectingNumber": None,
                "metadata.redirectingName": None,
                "metadata.redirectionNumber": None,
                "metadata.redirectionName": None,
                "metadata.redBoxCallGuid": "000030e6-0000-4fbe-2024-021216591734",
                "metadata.callingName": "Chapman, Amy",
                "metadata.callType": "Call_Teams",
                "metadata.otherPartyName": "Chapman, Amy",
                "metadata.callingNumber": "Amy.Chapman",
                "metadata.externalCallId": "801f5f00-e8f5-47e7-b304-90edd4fed617",
                "metadata.appearance": "Internal",
                "metadata.mediaType": 1.0,
                "metadata.conversationId": "c2ca0538-cb6c-4e02-8bed-f413fc827f6f",
                "metadata.mail": "<EMAIL>",
                "metadata.otherPartyMail": "<EMAIL>",
                "metadata.maxParticipants": 2.0,
                "metadata.recordingReason": "TeamsRecordingPolicy",
                "metadata_local_file_path": "dummy",
                "metadata_file_url": "s3://dcme.steeleye.co/aries/ingress/92658.metadata.json",
                "transcriptSourceKey": "dummy",
                "recordingSourceKey": "dummy",
                "recording_local_file_path": "dummy",
                "transcriptionStatus": TranscriptionStatusEnum.COMPLETED_SUCCESSFULLY,
            },
            {
                "transcriptions": None,
                "status": "Success",
                "mediaTypeIncluded": ["audio/wav"],
                "mediaExported": True,
                "nasMonitorCallId": 27890,
                "replayUrl": None,
                "silent": False,
                "callIdentifier.recorderId": 12518,
                "callIdentifier.nasId": 1,
                "callIdentifier.callId": 20316,
                "callIdentifier.redBoxCallGuid": "00000000-0000-0000-0000-000000000000",
                "metadata.startDateTime": "2024-02-12T07:29:26.59+00:00",
                "metadata.callId": 20316,
                "metadata.protocolProcessorId": 3342337,
                "metadata.firstFrame": 7172,
                "metadata.lastFrame": 7178,
                "metadata.callFlags": 241369091,
                "metadata.deviceId": 41,
                "metadata.endDateTime": "2024-02-12T07:35:04.82+00:00",
                "metadata.maxParties": 2,
                "metadata.duration": "00:05:38.2170000",
                "metadata.extension": "Takunori.Sumimoto",
                "metadata.otherParty": "Meeting",
                "metadata.direction": "Outgoing",
                "metadata.agentGroup": None,
                "metadata.channelName": "Takunori.Sumimoto",
                "metadata.callerNumber": None,
                "metadata.callerName": None,
                "metadata.calledNumber": "Meeting",
                "metadata.calledName2": "Meeting",
                "metadata.userId": None,
                "metadata.nasFirstFrame": 109071,
                "metadata.nasLastFrame": 0,
                "metadata.deviceReference": None,
                "metadata.digitalSignature": "BAEEC724507DD03E2E961B0499F14361",
                "metadata.extensionName": "Sumimoto, Takunori",
                "metadata.segmentId": 0,
                "metadata.nasId": 0,
                "metadata.nasCallId": 20316,
                "metadata.sipCallId": None,
                "metadata.nasDigitalSignature": None,
                "metadata.callSafeNASId": pd.NA,
                "metadata.callSafeNASFirstFrame": pd.NA,
                "metadata.callSafeNASLastFrame": pd.NA,
                "metadata.callSafeState": pd.NA,
                "metadata.callSafeTag": None,
                "metadata.callSafeDigitalSignature": None,
                "metadata.ctiCall": None,
                "metadata.rtpChannel": None,
                "metadata.redirectingNumber": None,
                "metadata.redirectingName": None,
                "metadata.redirectionNumber": None,
                "metadata.redirectionName": None,
                "metadata.redBoxCallGuid": "000030e6-0000-4f5c-2024-021207292659",
                "metadata.callingName": "Sumimoto, Takunori",
                "metadata.callType": "Meeting_MeetNow",
                "metadata.otherPartyName": "Meeting",
                "metadata.callingNumber": "Takunori.Sumimoto",
                "metadata.externalCallId": "771f5e00-89ec-484a-a464-5b247996957b",
                "metadata.appearance": "Internal",
                "metadata.mediaType": 1.0,
                "metadata.conversationId": "7c3a6d22-77d8-4116-adfb-d58c092c74c6",
                "metadata.mail": "<EMAIL>",
                "metadata.otherPartyMail": None,
                "metadata.maxParticipants": 6.0,
                "metadata.recordingReason": "TeamsRecordingPolicy",
                "metadata_local_file_path": "dummy",
                "metadata_file_url": "s3://dcme.steeleye.co/aries/ingress/92659.metadata.json",
                "transcriptSourceKey": "dummy",
                "recordingSourceKey": "dummy",
                "recording_local_file_path": "dummy",
                "transcriptionStatus": TranscriptionStatusEnum.COMPLETED_SUCCESSFULLY,
            },
        ]
    )


@pytest.fixture()
def expected_result_dcme_iv_redbox() -> pd.DataFrame:
    return pd.DataFrame(
        [
            {
                "callDuration": "00:00:59",
                "conferenceCall": False,
                "direction": "outgoing",
                "id": "7720|8|9278851",
                "identifiers.allCountryCodes": ["GB"],
                "identifiers.allIds": ["+447977353980", "07977353980", "8359"],
                "identifiers.fromDeviceId": 1003,
                "identifiers.fromId": "8359",
                "identifiers.fromIdAddlInfo": {"raw": "8359", "countryCode": None},
                "identifiers.fromUserId": "George Cranfield - DCM",
                "identifiers.hostId": None,
                "identifiers.toDeviceId": None,
                "identifiers.toIds": ["+447977353980"],
                "identifiers.toIdsAddlInfo": [{"raw": "07977353980", "countryCode": "GB"}],
                "identifiers.toUserId": None,
                "metadata.source.client": "Landline (RedBox)",
                "metadata.source.fileInfo.location.bucket": "dcme.steeleye.co",
                "metadata.source.fileInfo.location.key": "aries/ingress/92656.metadata.json",
                "sourceIndex": 0,
                "sourceKey": "s3://dcme.steeleye.co/redbox/input_file.ndjson",
                "timestamps.localTimestampStart": "2024-02-09T11:05:19.900000Z",
                "timestamps.timestampStart": "2024-02-09T11:05:19.900000Z",
                "timestamps.localTimestampEnd": "2024-02-09T11:06:19.180000Z",
                "timestamps.timestampEnd": "2024-02-09T11:06:19.180000Z",
                "waveform.location.bucket": "dcme.steeleye.co",
                "waveform.location.key": "attachments/waveform/iv_redbox_voice_transform/"
                "7720|8|9278851/waveform.json",
            },
            {
                "callDuration": "00:03:36",
                "conferenceCall": False,
                "direction": "outgoing",
                "id": "7720|8|9278847",
                "identifiers.allCountryCodes": ["GB"],
                "identifiers.allIds": ["+447977353980", "07977353980", "8359"],
                "identifiers.fromDeviceId": 1003,
                "identifiers.fromId": "8359",
                "identifiers.fromIdAddlInfo": {"raw": "8359", "countryCode": None},
                "identifiers.fromUserId": "George Cranfield - DCM",
                "identifiers.hostId": None,
                "identifiers.toDeviceId": None,
                "identifiers.toIds": ["+447977353980"],
                "identifiers.toIdsAddlInfo": [{"raw": "07977353980", "countryCode": "GB"}],
                "identifiers.toUserId": None,
                "metadata.source.client": "Landline (RedBox)",
                "metadata.source.fileInfo.location.bucket": "dcme.steeleye.co",
                "metadata.source.fileInfo.location.key": "aries/ingress/92657.metadata.json",
                "sourceIndex": 1,
                "sourceKey": "s3://dcme.steeleye.co/redbox/input_file.ndjson",
                "timestamps.localTimestampStart": "2024-02-09T11:00:43.430000Z",
                "timestamps.timestampStart": "2024-02-09T11:00:43.430000Z",
                "timestamps.localTimestampEnd": "2024-02-09T11:04:19.780000Z",
                "timestamps.timestampEnd": "2024-02-09T11:04:19.780000Z",
                "waveform.location.bucket": "dcme.steeleye.co",
                "waveform.location.key": "attachments/waveform/iv_redbox_voice_transform/"
                "7720|8|9278847/waveform.json",
            },
            {
                "callDuration": "00:03:39",
                "conferenceCall": False,
                "direction": "incoming",
                "id": "12518|1|20414",
                "identifiers.allCountryCodes": None,
                "identifiers.allIds": ["amy.chapman", "tarek.hasan"],
                "identifiers.fromDeviceId": None,
                "identifiers.fromId": "amy.chapman",
                "identifiers.fromIdAddlInfo": {"raw": "amy.chapman", "countryCode": None},
                "identifiers.fromUserId": None,
                "identifiers.hostId": None,
                "identifiers.toDeviceId": 28,
                "identifiers.toIds": ["tarek.hasan"],
                "identifiers.toIdsAddlInfo": [{"raw": "tarek.hasan", "countryCode": None}],
                "identifiers.toUserId": "Tarek.Hasan",
                "metadata.source.client": "MS Teams Voice (RedBox)",
                "metadata.source.fileInfo.location.bucket": "dcme.steeleye.co",
                "metadata.source.fileInfo.location.key": "aries/ingress/92658.metadata.json",
                "sourceIndex": 2,
                "sourceKey": "s3://dcme.steeleye.co/redbox/input_file.ndjson",
                "timestamps.localTimestampStart": "2024-02-12T16:59:17.340000Z",
                "timestamps.timestampStart": "2024-02-12T16:59:17.340000Z",
                "timestamps.localTimestampEnd": "2024-02-12T17:02:56.860000Z",
                "timestamps.timestampEnd": "2024-02-12T17:02:56.860000Z",
                "waveform.location.bucket": "dcme.steeleye.co",
                "waveform.location.key": "attachments/waveform/iv_redbox_voice_transform/"
                "12518|1|20414/waveform.json",
            },
            {
                "callDuration": "00:05:38",
                "conferenceCall": False,
                "direction": "outgoing",
                "id": "12518|1|20316",
                "identifiers.allCountryCodes": None,
                "identifiers.allIds": ["meeting", "takunori.sumimoto"],
                "identifiers.fromDeviceId": 41,
                "identifiers.fromId": "takunori.sumimoto",
                "identifiers.fromIdAddlInfo": {"raw": "takunori.sumimoto", "countryCode": None},
                "identifiers.fromUserId": "Takunori.Sumimoto",
                "identifiers.hostId": None,
                "identifiers.toDeviceId": None,
                "identifiers.toIds": ["meeting"],
                "identifiers.toIdsAddlInfo": [{"raw": "meeting", "countryCode": None}],
                "identifiers.toUserId": None,
                "metadata.source.client": "MS Teams Voice (RedBox)",
                "metadata.source.fileInfo.location.bucket": "dcme.steeleye.co",
                "metadata.source.fileInfo.location.key": "aries/ingress/92659.metadata.json",
                "sourceIndex": 3,
                "sourceKey": "s3://dcme.steeleye.co/redbox/input_file.ndjson",
                "timestamps.localTimestampStart": "2024-02-12T07:29:26.590000Z",
                "timestamps.timestampStart": "2024-02-12T07:29:26.590000Z",
                "timestamps.localTimestampEnd": "2024-02-12T07:35:04.820000Z",
                "timestamps.timestampEnd": "2024-02-12T07:35:04.820000Z",
                "waveform.location.bucket": "dcme.steeleye.co",
                "waveform.location.key": "attachments/waveform/iv_redbox_voice_transform/"
                "12518|1|20316/waveform.json",
            },
        ]
    )
