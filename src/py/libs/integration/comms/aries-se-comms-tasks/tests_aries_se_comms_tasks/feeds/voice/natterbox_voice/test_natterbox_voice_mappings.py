import logging
import pandas as pd
from aries_se_comms_tasks.feeds.voice.natterbox_voice.natterbox_voice_mappings import (
    NatterboxVoiceMappings,
)
from aries_se_comms_tasks.voice.static import CallColumns
from aries_se_core_tasks.utilities.helpers_for_tests import (  # type: ignore[attr-defined] # noqa: E501
    sort_list_columns,
)

logger = logging.getLogger(__name__)


class TestNatterboxVoiceMappings:
    def test_end_to_end_transformation(
        self,
        source_frame: pd.DataFrame,
        expected_result: pd.DataFrame,
    ):
        mappings = NatterboxVoiceMappings(
            source_frame=source_frame,
            logger=logger,
            realm="test.dev.steeleye.co",
            source_file_uri="s3://jose.dev.steeleye.co/batch.csv",
        )

        result = mappings.process()

        # Sort list columns
        sort_list_columns(
            list_of_list_columns=[
                CallColumns.IDENTIFIERS_TO_IDS,
                CallColumns.IDENTIFIERS_ALL_IDS,
                CallColumns.IDENTIFIERS_ALL_COUNTRY_CODES,
            ],
            result_df=result,
            expected_result_df=expected_result,
        )

        pd.testing.assert_frame_equal(left=result, right=expected_result)
