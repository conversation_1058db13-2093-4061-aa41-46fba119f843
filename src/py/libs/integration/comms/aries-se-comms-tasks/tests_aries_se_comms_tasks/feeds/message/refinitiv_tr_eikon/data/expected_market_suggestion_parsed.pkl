���	      �pandas.core.frame��	DataFrame���)��}�(�_mgr��pandas.core.internals.managers��BlockManager���(�pandas._libs.internals��_unpickle_block����pandas._libs.arrays��__pyx_unpickle_NDArrayBacked����pandas.core.arrays.string_��StringArray���J�\U
N��R�h�StringDtype���)��}��storage��python�sb�numpy.core.multiarray��_reconstruct����numpy��ndarray���K ��Cb���R�(KK��h�dtype����O8�����R�(K�|�NNNJ����J����K?t�b�]�(�Kyte Group (EXT FD)��!Market Securities (Dubai) Limited��Nino Options (EXT FD)�h-h-h/h-�ING (GR) (EXT FD) (PM)�h/�*TPICAP IGBB (GR) ((PM) by TR) (Disclaimer)��(Phoenix Futures and Options LLC (EXT FD)�h-et�b}���b�builtins��slice���K KK��R�K��R�hhhK ��h!��R�(KKK��h)�]�(]��&<EMAIL>�a]��$<EMAIL>�a]��<EMAIL>�a]��<EMAIL>�a]��<EMAIL>�a]��<EMAIL>�a]��&<EMAIL>�a]��<EMAIL>�a]��<EMAIL>�a]��<EMAIL>�a]��<EMAIL>�a]��!<EMAIL>�aet�bh8KKK��R�K��R�hhhJ�\U
N��R�h)��}�hhsbhhK ��h!��R�(KK��h)�]�(�Simon Greenhough��Brendan Downes��Alfred Kemp��Danny Fowler��Ashley Purdy��Ramon Koster��Lawrence Patmore��Barry Smith��Marcel van Diepen��Sam Mahoney��John Donohue��Peter Smith�et�b}���bh8KKK��R�K��R�hhhK ��h!��R�(KKK��h)�]�(]�}�(�id�hC�label��Thomson Reuters Eikon�ua]�}�(h�hEh�h�ua]�}�(h�hGh�h�ua]�}�(h�hIh�h�ua]�}�(h�hKh�h�ua]�}�(h�hMh�h�ua]�}�(h�hOh�h�ua]�}�(h�hQh�h�ua]�}�(h�hSh�h�ua]�}�(h�hUh�h�ua]�}�(h�hWh�h�ua]�}�(h�hYh�h�uaet�bh8KKK��R�K��R�hhhK ��h!��R�(KKK��h)�]�(h�h�h�h�h�h�h�h�h�h�h�h�et�bh8KKK��R�K��R�hhhK ��h!��R�(KKK��h)�]�(�>Thomson Reuters Eikon | tr-eikon_messages_with_attachments.zip�h�h�h�h�h�h�h�h�h�h�h�et�bh8KKK��R�K��R�t�]�(�pandas.core.indexes.base��
_new_Index���h��Index���}�(�data�hhK ��h!��R�(KK��h&�O8�����R�(Kh*NNNJ����J����K?t�b�]�(�company��emails��name��$sinkIdentifiers.orderFileIdentifiers��$sinkIdentifiers.tradeFileIdentifiers��source�et�b�name�Nu��R�h�h�}�(h��numpy.core.numeric��_frombuffer���(�`                                                                       	       
              �h&�i8�����R�(K�<�NNNJ����J����K t�bK���C�t�R�h�Nu��R�e��R��_typ��	dataframe��	_metadata�]��attrs�}��_flags�}��allows_duplicate_labels��sub.