import pandas as pd
import pytest
from aries_se_comms_tasks.feeds.voice.iv_voice.iv_vendor_combine_files import Params
from aries_se_comms_tasks.transcription.intelligent_voice.static import IVGetTranscriptsTargetFields
from pathlib import Path
from se_core_tasks.core.core_dataclasses import ExtractPathResult
from se_elastic_schema.static.transcript import TranscriptionStatusEnum
from typing import List

CURRENT_PATH = Path(__file__).parent
DATA_PATH = CURRENT_PATH.joinpath("data")
METADATA_IN_RECORDING_PATH = DATA_PATH.joinpath("metadata_in_recording")
METADATA_AND_RECORDING_PATH = DATA_PATH.joinpath("metadata_and_recording")


@pytest.fixture()
def params_metadata_in_recording() -> Params:
    return Params(
        common_pattern_regex=r"^.+",
        recording_file_regex=".wav$",
        transcript_file_regex=".json$",
        s3_recording_url_col=IVGetTranscriptsTargetFields.RECORDING_SOURCE_KEY,
        s3_transcript_url_col=IVGetTranscriptsTargetFields.TRANSCRIPT_SOURCE_KEY,
        recording_local_file_col="recording_local_file_url",
        metadata_in_recording_name=True,
        transcription_status_col=IVGetTranscriptsTargetFields.TRANSCRIPTION_STATUS,
    )


@pytest.fixture()
def input_file_list_metadata_in_recording() -> List[ExtractPathResult]:
    return [
        ExtractPathResult(
            path=METADATA_IN_RECORDING_PATH.joinpath(
                "283-46f99bbc-b5e9-11ee-9aa4-9360add42176-358961811402-447971039973-1705572402-ee-mo-.json"
            )
        ),
        ExtractPathResult(
            path=METADATA_IN_RECORDING_PATH.joinpath(
                "283-46f99bbc-b5e9-11ee-9aa4-9360add42176-358961811402-447971039973-1705572402-ee-mo-.wav"
            )
        ),
        ExtractPathResult(
            path=METADATA_IN_RECORDING_PATH.joinpath(
                "283-cc5c53be-b704-11ee-81f2-3789a07e81e7-447389076667-447977353980-1705694173-ee-mo-.json"
            )
        ),
        ExtractPathResult(
            path=METADATA_IN_RECORDING_PATH.joinpath(
                "283-cc5c53be-b704-11ee-81f2-3789a07e81e7-447389076667-447977353980-1705694173-ee-mo-.wav"
            )
        ),
    ]


@pytest.fixture()
def expected_result_metadata_in_recording() -> pd.DataFrame:
    return pd.DataFrame(
        [
            {
                "transcriptSourceKey": "s3://test.dev.steeleye.co/aries/ingress/depository/attachments/"
                "transcripts/283-46f99bbc-b5e9-11ee-9aa4-9360add42176"
                "-358961811402-447971039973-1705572402-ee-mo-.json",
                "recordingSourceKey": "s3://test.dev.steeleye.co/aries/ingress/depository/attachments/"
                "recordings/283-46f99bbc-b5e9-11ee-9aa4-9360add42176-"
                "358961811402-447971039973-1705572402-ee-mo-.wav",
                "recording_local_file_url": "dummy",
                "transcriptionStatus": TranscriptionStatusEnum.COMPLETED_SUCCESSFULLY,
            },
            {
                "transcriptSourceKey": "s3://test.dev.steeleye.co/aries/ingress/depository/"
                "attachments/transcripts/283-cc5c53be-b704-11ee-81f2-"
                "3789a07e81e7-447389076667-447977353980-1705694173-ee-mo-.json",
                "recordingSourceKey": "s3://test.dev.steeleye.co/aries/ingress/depository/attachments"
                "/recordings/283-cc5c53be-b704-11ee-81f2-3789a07e81e7-"
                "447389076667-447977353980-1705694173-ee-mo-.wav",
                "recording_local_file_url": "dummy",
                "transcriptionStatus": TranscriptionStatusEnum.COMPLETED_SUCCESSFULLY,
            },
        ]
    )


@pytest.fixture()
def params_metadata_and_recording() -> Params:
    return Params(
        common_pattern_regex=r"^.+",
        recording_file_regex=".wav$",
        transcript_file_regex="(?<!metadata).json$",
        metadata_file_regex=".metadata.json$",
        s3_recording_url_col=IVGetTranscriptsTargetFields.RECORDING_SOURCE_KEY,
        s3_transcript_url_col=IVGetTranscriptsTargetFields.TRANSCRIPT_SOURCE_KEY,
        recording_local_file_col="recording_local_file_url",
        metadata_local_file_col="metadata_local_file_url",
        s3_metadata_url_col="metadata_file_url",
        metadata_in_recording_name=False,
        transcription_status_col=IVGetTranscriptsTargetFields.TRANSCRIPTION_STATUS,
    )


@pytest.fixture()
def input_file_list_metadata_and_recording() -> List[ExtractPathResult]:
    return [
        ExtractPathResult(
            path=METADATA_AND_RECORDING_PATH.joinpath("00001e28-008d-2d01-2024-012207050223.json")
        ),
        ExtractPathResult(
            path=METADATA_AND_RECORDING_PATH.joinpath(
                "00001e28-008d-2d01-2024-012207050223.metadata.json"
            )
        ),
        ExtractPathResult(
            path=METADATA_AND_RECORDING_PATH.joinpath("00001e28-008d-2d01-2024-012207050223.wav")
        ),
        ExtractPathResult(
            path=METADATA_AND_RECORDING_PATH.joinpath("00001e28-008d-2d15-2024-012207365674.json")
        ),
        ExtractPathResult(
            path=METADATA_AND_RECORDING_PATH.joinpath(
                "00001e28-008d-2d15-2024-012207365674.metadata.json"
            )
        ),
        ExtractPathResult(
            path=METADATA_AND_RECORDING_PATH.joinpath("00001e28-008d-2d15-2024-012207365674.wav")
        ),
        ExtractPathResult(
            path=METADATA_AND_RECORDING_PATH.joinpath(
                "000030e6-0000-54e9-2024-022117595672 [SILENT].json"
            )
        ),
        ExtractPathResult(
            path=METADATA_AND_RECORDING_PATH.joinpath(
                "000030e6-0000-54e9-2024-022117595672 [SILENT].metadata.json"
            )
        ),
        ExtractPathResult(
            path=METADATA_AND_RECORDING_PATH.joinpath(
                "000030e6-0000-54e9-2024-022117595672 [SILENT].wav"
            )
        ),
    ]


@pytest.fixture()
def expected_result_metadata_and_recording() -> pd.DataFrame:
    return pd.DataFrame(
        [
            {
                "metadata_local_file_url": "dummy",
                "metadata_file_url": "s3://test.dev.steeleye.co/aries/ingress/depository/attachments"
                "/metadata/00001e28-008d-2d01-2024-012207050223.metadata.json",
                "transcriptSourceKey": "s3://test.dev.steeleye.co/aries/ingress/depository/"
                "attachments/transcripts/00001e28-008d-2d01-2024-012207050223.json",
                "recordingSourceKey": "s3://test.dev.steeleye.co/aries/ingress/depository/"
                "attachments/recordings/00001e28-008d-2d01-2024-012207050223.wav",
                "recording_local_file_url": "dummy",
                "transcriptionStatus": TranscriptionStatusEnum.COMPLETED_SUCCESSFULLY,
            },
            {
                "metadata_local_file_url": "dummy",
                "metadata_file_url": "s3://test.dev.steeleye.co/aries/ingress/depository/attachments/"
                "metadata/00001e28-008d-2d15-2024-012207365674.metadata.json",
                "transcriptSourceKey": "s3://test.dev.steeleye.co/aries/ingress/depository/"
                "attachments/transcripts/00001e28-008d-2d15-2024-012207365674.json",
                "recordingSourceKey": "s3://test.dev.steeleye.co/aries/ingress/depository/"
                "attachments/recordings/00001e28-008d-2d15-2024-012207365674.wav",
                "recording_local_file_url": "dummy",
                "transcriptionStatus": TranscriptionStatusEnum.COMPLETED_SUCCESSFULLY,
            },
            {
                "metadata_local_file_url": "dummy",
                "metadata_file_url": "s3://test.dev.steeleye.co/aries/ingress/depository/attachments/"
                "metadata/000030e6-0000-54e9-2024-022117595672 [SILENT].metadata.json",
                "transcriptSourceKey": "s3://test.dev.steeleye.co/aries/ingress/depository/"
                "attachments/transcripts/000030e6-0000-54e9-2024-022117595672 [SILENT].json",
                "recordingSourceKey": "s3://test.dev.steeleye.co/aries/ingress/depository/"
                "attachments/recordings/000030e6-0000-54e9-2024-022117595672 [SILENT].wav",
                "recording_local_file_url": "dummy",
                "transcriptionStatus": TranscriptionStatusEnum.COMPLETED_SUCCESSFULLY,
            },
        ]
    )


@pytest.fixture()
def input_file_list_missing_recording() -> List[ExtractPathResult]:
    return [
        ExtractPathResult(
            path=METADATA_IN_RECORDING_PATH.joinpath(
                "283-46f99bbc-b5e9-11ee-9aa4-9360add42176-358961811402-447971039973-1705572402-ee-mo-.json"
            )
        ),
        ExtractPathResult(
            path=METADATA_IN_RECORDING_PATH.joinpath(
                "283-46f99bbc-b5e9-11ee-9aa4-9360add42176-358961811402-447971039973-1705572402-ee-mo-.wav"
            )
        ),
        ExtractPathResult(
            path=METADATA_IN_RECORDING_PATH.joinpath(
                "283-cc5c53be-b704-11ee-81f2-3789a07e81e7-447389076667-447977353980-1705694173-ee-mo-.json"
            )
        ),
    ]


@pytest.fixture()
def input_file_list_missing_transcript() -> List[ExtractPathResult]:
    return [
        ExtractPathResult(
            path=METADATA_IN_RECORDING_PATH.joinpath(
                "283-46f99bbc-b5e9-11ee-9aa4-9360add42176-358961811402-447971039973-1705572402-ee-mo-.wav"
            )
        ),
        ExtractPathResult(
            path=METADATA_IN_RECORDING_PATH.joinpath(
                "283-cc5c53be-b704-11ee-81f2-3789a07e81e7-447389076667-447977353980-1705694173-ee-mo-.json"
            )
        ),
        ExtractPathResult(
            path=METADATA_IN_RECORDING_PATH.joinpath(
                "283-cc5c53be-b704-11ee-81f2-3789a07e81e7-447389076667-447977353980-1705694173-ee-mo-.wav"
            )
        ),
    ]


@pytest.fixture()
def input_file_list_missing_metadata() -> List[ExtractPathResult]:
    return [
        ExtractPathResult(
            path=METADATA_AND_RECORDING_PATH.joinpath("00001e28-008d-2d01-2024-012207050223.json")
        ),
        ExtractPathResult(
            path=METADATA_AND_RECORDING_PATH.joinpath(
                "00001e28-008d-2d01-2024-012207050223.metadata.json"
            )
        ),
        ExtractPathResult(
            path=METADATA_AND_RECORDING_PATH.joinpath("00001e28-008d-2d01-2024-012207050223.wav")
        ),
        ExtractPathResult(
            path=METADATA_AND_RECORDING_PATH.joinpath("00001e28-008d-2d15-2024-012207365674.json")
        ),
        ExtractPathResult(
            path=METADATA_AND_RECORDING_PATH.joinpath("00001e28-008d-2d15-2024-012207365674.wav")
        ),
    ]
