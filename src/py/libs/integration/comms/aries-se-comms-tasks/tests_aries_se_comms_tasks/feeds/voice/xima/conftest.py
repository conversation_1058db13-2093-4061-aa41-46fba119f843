import pandas as pd
import pytest


@pytest.fixture()
def source_frame() -> pd.DataFrame:
    return pd.DataFrame(
        data={
            "recordingKey": [
                "7a22d842-16ec-4b35-b6d4-54adc0ce61e5-1655219727560",
                "73a06a37-2fde-4df1-92f8-a6d02bc61982-1655219536682",
            ],
            "callKey": [
                "ea920301-bd6b-4fbb-9a86-bece819f3b31-1655219721264-1655219780428",
                "3fc72f82-4876-43d1-8898-8d1c94b62ff2-1655219533185-1655219861412",
            ],
            "startTime": [1655219727560, 1655219536682],
            "duration": [52868, 324730],
            "poolId": [
                "a0b450da-417e-490a-9c79-34c3a540c53c",
                "a0b450da-417e-490a-9c79-34c3a540c53c",
            ],
            "size": [103827, 635658],
            "eventId": [2214259, 2214256],
            "recordingSystemId": [1, 1],
            "status": ["SAVED", "SAVED"],
            "callingParty": ["Oliver Ratcliff(0530)", "Andrew Burdis(0524)"],
            "receivingParty": ["<PERSON> <PERSON>le(0529)", "02038185310"],
            "metadata_file_url": [
                "s3://test.dev.steeleye.co/aries/ingress/streamed/evented/xima_voice/metadata/2022/09/07/7a22d842-16ec-4b35-b6d4-54adc0ce61e5-1655219727560.json",
                "s3://test.dev.steeleye.co/aries/ingress/streamed/evented/xima_voice/metadata/2022/09/07/73a06a37-2fde-4df1-92f8-a6d02bc61982-1655219536682.json",
            ],
            "recording_file_url": [
                "s3://test.dev.steeleye.co/aries/ingress/streamed/evented/xima_voice/recordings/2022/09/07/7a22d842-16ec-4b35-b6d4-54adc0ce61e5-1655219727560.wav",
                "s3://test.dev.steeleye.co/aries/ingress/streamed/evented/xima_voice/recordings/2022/09/07/73a06a37-2fde-4df1-92f8-a6d02bc61982-1655219536682.wav",
            ],
        }
    )


@pytest.fixture()
def expected_result():
    return pd.DataFrame(
        data=[
            {
                "callDuration": "00:00:52",
                "id": "7a22d842-16ec-4b35-b6d4-54adc0ce61e5-1655219727560",
                "identifiers.allIds": ["0529", "0530"],
                "identifiers.fromId": "0530",
                "identifiers.fromIdAddlInfo": {"raw": "0530", "countryCode": None},
                "identifiers.toIds": ["0529"],
                "identifiers.toIdsAddlInfo": [{"raw": "0529", "countryCode": None}],
                "internal": True,
                "metadata.source.client": "XIMA",
                "metadata.source.fileInfo.location.bucket": "test.dev.steeleye.co",
                "metadata.source.fileInfo.location.key": "path/to/file.csv",
                "sourceIndex": 0,
                "sourceKey": "s3://test.dev.steeleye.co/path/to/file.csv",
                "timestamps.localTimestampEnd": "2022-06-14T15:16:20.428000Z",
                "timestamps.localTimestampStart": "2022-06-14T15:15:27.560000Z",
                "timestamps.timestampEnd": "2022-06-14T15:16:20.428000Z",
                "timestamps.timestampStart": "2022-06-14T15:15:27.560000Z",
                "waveform.location.bucket": "test.dev.steeleye.co",
                "waveform.location.key": "attachments/waveform/xima_voice_transform/7a22d842-16ec-4b35-b6d4-54adc0ce61e5-1655219727560/waveform.json",  # noqa: E501
            },
            {
                "callDuration": "00:05:24",
                "id": "73a06a37-2fde-4df1-92f8-a6d02bc61982-1655219536682",
                "identifiers.allIds": ["+442038185310", "02038185310", "0524"],
                "identifiers.fromId": "0524",
                "identifiers.fromIdAddlInfo": {"raw": "0524", "countryCode": None},
                "identifiers.toIds": ["+442038185310"],
                "identifiers.toIdsAddlInfo": [{"raw": "02038185310", "countryCode": "GB"}],
                "internal": True,
                "metadata.source.client": "XIMA",
                "metadata.source.fileInfo.location.bucket": "test.dev.steeleye.co",
                "metadata.source.fileInfo.location.key": "path/to/file.csv",
                "sourceIndex": 1,
                "sourceKey": "s3://test.dev.steeleye.co/path/to/file.csv",
                "timestamps.localTimestampEnd": "2022-06-14T15:17:41.412000Z",
                "timestamps.localTimestampStart": "2022-06-14T15:12:16.681999Z",
                "timestamps.timestampEnd": "2022-06-14T15:17:41.412000Z",
                "timestamps.timestampStart": "2022-06-14T15:12:16.681999Z",
                "waveform.location.bucket": "test.dev.steeleye.co",
                "waveform.location.key": "attachments/waveform/xima_voice_transform/73a06a37-2fde-4df1-92f8-a6d02bc61982-1655219536682/waveform.json",  # noqa: E501
            },
        ]
    )
