import logging
import pandas as pd
from aries_se_comms_tasks.feeds.voice.iv_voice.static import IV_REDBOX_FLOW_NAME
from aries_se_comms_tasks.transform_maps.voice_transform_maps import (
    iv_redbox_voice_transform_map,
    redbox_voice_transform_map,
)
from aries_se_comms_tasks.voice.static import CallColumns
from aries_se_core_tasks.utilities.helpers_for_tests import (  # type: ignore[attr-defined]
    sort_list_columns,
)

logger = logging.getLogger(__name__)


class TestRedboxVoiceMappings:
    """Tests for TestRedboxVoiceMappings."""

    def test_end_to_end_mappings(
        self,
        source_frame_redbox: pd.DataFrame,
        expected_result_redbox: pd.DataFrame,
    ):
        """Runs an end-to-end test for Redbox Voice Mappings."""

        transformation = redbox_voice_transform_map.transformation(tenant="test")
        mapping_task = transformation(
            source_frame=source_frame_redbox,
            realm="test.steeleye.co",
            source_file_url="s3://test.steeleye.co/redbox/input_file.ndjson",
            logger=logger,
        )
        result: pd.DataFrame = mapping_task.process()

        sort_list_columns(
            result_df=result,
            expected_result_df=expected_result_redbox,
            list_of_list_columns=[
                CallColumns.IDENTIFIERS_TO_IDS,
                CallColumns.IDENTIFIERS_ALL_IDS,
            ],
        )

        pd.testing.assert_frame_equal(
            left=result,
            right=expected_result_redbox,
        )

    def test_iv_redbox_end_to_end_mappings(
        self,
        source_frame_iv_redbox: pd.DataFrame,
        expected_result_iv_redbox: pd.DataFrame,
    ):
        """Runs an end-to-end test for Redbox Voice Mappings when called from
        IV Redbox."""

        transformation = iv_redbox_voice_transform_map.transformation(tenant="test")
        mapping_task = transformation(
            source_frame=source_frame_iv_redbox,
            realm="test.steeleye.co",
            source_file_url="s3://test.steeleye.co/redbox/input_file.ndjson",
            logger=logger,
            flow_name=IV_REDBOX_FLOW_NAME,
        )
        result: pd.DataFrame = mapping_task.process()

        sort_list_columns(
            result_df=result,
            expected_result_df=expected_result_iv_redbox,
            list_of_list_columns=[
                CallColumns.IDENTIFIERS_TO_IDS,
                CallColumns.IDENTIFIERS_ALL_IDS,
            ],
        )

        pd.testing.assert_frame_equal(
            left=result, right=expected_result_iv_redbox, check_dtype=False
        )

    def test_dcme_iv_redbox_end_to_end_mappings(
        self,
        source_frame_dcme_iv_redbox: pd.DataFrame,
        expected_result_dcme_iv_redbox: pd.DataFrame,
    ):
        """Runs an end-to-end test for Redbox Voice Mappings when called from
        IV Redbox for the client 'dcme' (which has an override)."""
        transformation = iv_redbox_voice_transform_map.transformation(tenant="dcme")
        mapping_task = transformation(
            source_frame=source_frame_dcme_iv_redbox,
            realm="dcme.steeleye.co",
            source_file_url="s3://dcme.steeleye.co/redbox/input_file.ndjson",
            logger=logger,
            flow_name=IV_REDBOX_FLOW_NAME,
        )

        result: pd.DataFrame = mapping_task.process()

        sort_list_columns(
            result_df=result,
            expected_result_df=expected_result_dcme_iv_redbox,
            list_of_list_columns=[
                CallColumns.IDENTIFIERS_TO_IDS,
                CallColumns.IDENTIFIERS_ALL_IDS,
            ],
        )

        pd.testing.assert_frame_equal(
            left=result, right=expected_result_dcme_iv_redbox, check_dtype=False
        )
