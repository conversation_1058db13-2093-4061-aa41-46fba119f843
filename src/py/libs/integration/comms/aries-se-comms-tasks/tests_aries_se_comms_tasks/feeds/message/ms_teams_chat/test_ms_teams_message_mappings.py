# type: ignore
import boto3
import numpy as np
import pandas as pd
import pytest
from aries_se_comms_tasks.feeds.message.ms_teams_chat.ms_teams_message_mappings import (
    MSTeamsMessageMappings,
    SkipIfEmptyMSTeamsMessageMappingsSourceFrame,
)
from aries_se_comms_tasks.message.static import MessageColumns
from freezegun import freeze_time
from integration_test_utils.aws_helpers.patch_response import (
    mock_aiobotocore_convert_to_response_dict,
)
from moto import mock_aws
from pathlib import Path

BUCKET_NAME_1: str = "foo.steeleye.co"
BUCKET_NAME_2: str = "test.dev.steeleye.co"
DATA_PATH = Path(__file__).parent.joinpath("data")

mock_aiobotocore_convert_to_response_dict()


@pytest.fixture()
def expected_output_frame_1() -> pd.DataFrame:
    return pd.DataFrame(
        {
            "metadata.isEdited": [
                <PERSON><PERSON><PERSON>,
                <PERSON><PERSON><PERSON>,
                <PERSON><PERSON><PERSON>,
                <PERSON><PERSON><PERSON>,
            ],
            "metadata.isDeleted": [
                <PERSON><PERSON><PERSON>,
                <PERSON><PERSON><PERSON>,
                <PERSON><PERSON><PERSON>,
                <PERSON><PERSON><PERSON>,
            ],
            "timestamps.created": [
                "2023-07-31T05:21:02.495000Z",
                "2023-07-31T05:21:30.749000Z",
                "2023-07-31T06:07:04.574000Z",
                "2024-03-11T16:40:50.403000Z",
            ],
            "timestamps.timestampStart": [
                "2023-07-31T05:21:02.495000Z",
                "2023-07-31T05:21:30.749000Z",
                "2023-07-31T06:07:04.574000Z",
                "2024-03-11T16:40:50.403000Z",
            ],
            "body.text": [
                "FGP data loaded.\n",
                "Beta neutral returns cached\n",
                pd.NA,
                " Evans, BenTest",
            ],
            "body.displayText": [
                "FGP data loaded.",
                "Beta neutral returns cached",
                '<attachment id="c2e59c1b7e604d3a9c5b02f3b28d9d76"></attachment>',
                '<div><div><at id="0">Evans, Ben</at>Test</div>\n</div>',
            ],
            "body.edits": [
                pd.NA,
                pd.NA,
                pd.NA,
                pd.NA,
            ],
            "body.type": ["PLAIN", "PLAIN", "PLAIN", "PLAIN"],
            "metadata.messageId": [
                "1690780862495",
                "1690780890749",
                "1690783624574",
                "1710175250403",
            ],
            "metadata.inReplyTo": [None, None, None, "1710153695037"],
            "metadata.source.client": [
                "MS Teams Chat",
                "MS Teams Chat",
                "MS Teams Chat",
                "MS Teams Chat",
            ],
            "metadata.source.fileInfo.location.bucket": [
                "foo.steeleye.co",
                "foo.steeleye.co",
                "foo.steeleye.co",
                "foo.steeleye.co",
            ],
            "metadata.source.fileInfo.location.key": [
                "folder/file.json",
                "folder/file.json",
                "folder/file.json",
                "folder/file.json",
            ],
            "attachments": [
                None,
                None,
                [
                    {
                        "fileName": None,
                        "fileInfo.location.bucket": "foo.steeleye.co",
                        "fileInfo.location.key": "folder/attachments/1690783624574/c2e59c1b7e604d3a9c5b02f3b28d9d76",  # noqa: E501
                        "sizeInBytes": 374,
                    }
                ],
                None,
            ],
            "hasAttachment": [False, False, True, False],
            "chatType": ["channel", "channel", "channel", "channel"],
            "sourceKey": [
                "s3://foo.steeleye.co/folder/file.json",
                "s3://foo.steeleye.co/folder/file.json",
                "s3://foo.steeleye.co/folder/file.json",
                "s3://foo.steeleye.co/folder/file.json",
            ],
            "roomId": [
                "19:31eb9cb5db8449b784054b492126a8f0@thread.tacv2",
                "19:31eb9cb5db8449b784054b492126a8f0@thread.tacv2",
                "19:68926925a7af460aabae1cf1a7fe48be@thread.tacv2",
                "19:64f227c024b44548a3f6d851443de04f@thread.tacv2",
            ],
            "roomName": [
                "Testing a chat name 1",
                "Testing a chat name 3",
                "Testing a chat name 4",
                "Testing a chat name 2",
            ],
            "identifiers.fromId": [
                "<EMAIL>",
                "<EMAIL>",
                pd.NA,
                "<EMAIL>",
            ],
            "identifiers.fromUserId": ["Test Data", "Test Data", pd.NA, "Nishino, Musashi"],
            "identifiers.toIds": [
                ["<EMAIL>", "<EMAIL>"],
                ["<EMAIL>"],
                pd.NA,
                pd.NA,
            ],
            "identifiers.toUserId": [pd.NA, "Test1 Data", pd.NA, pd.NA],
            "identifiers.allIds": [
                ["<EMAIL>", "<EMAIL>", "<EMAIL>"],
                ["<EMAIL>", "<EMAIL>"],
                pd.NA,
                ["<EMAIL>"],
            ],
        }
    )


@pytest.fixture()
def expected_output_frame_2() -> pd.DataFrame:
    columns = [
        "metadata.isEdited",
        "metadata.isDeleted",
        "timestamps.created",
        "timestamps.timestampStart",
        "body.text",
        "body.displayText",
        "body.edits",
        "body.type",
        "metadata.messageId",
        "metadata.inReplyTo",
        "metadata.source.client",
        "metadata.source.fileInfo.location.bucket",
        "metadata.source.fileInfo.location.key",
        "attachments",
        "hasAttachment",
        "chatType",
        "sourceKey",
        "roomId",
        "roomName",
        "identifiers.fromId",
        "identifiers.fromUserId",
        "identifiers.toIds",
        "identifiers.toUserId",
        "identifiers.allIds",
    ]

    data = [
        [
            False,
            False,
            "2024-03-11T13:12:57.482000Z",
            "2024-03-11T13:12:57.482000Z",
            " いただきます。ありがとうございます！\n",
            '<p></p>\n<attachment id="1710162723325"></attachment>\nいただきます。ありがとうございます！\n<p></p>',  # noqa E501
            pd.NA,
            "PLAIN",
            "1710162777482",
            None,
            "MS Teams Chat",
            "test.dev.steeleye.co",
            "aries/ingress/nonstreamed/polled/ms_teams_poll/2024/03/12/k2G3vxJLgChZwvOUksRLW/20240312_Hxw-3n6aed.json",
            [
                {
                    "fileName": None,
                    "fileInfo.location.bucket": "test.dev.steeleye.co",
                    "fileInfo.location.key": "aries/ingress/nonstreamed/polled/ms_teams_poll/2024/03/12/k2G3vxJLgChZwvOUksRLW/attachments/1710162777482/1710162723325",  # noqa E501
                    "sizeInBytes": 5240,
                }
            ],
            True,
            "chat",
            "s3://test.dev.steeleye.co/aries/ingress/nonstreamed/polled/ms_teams_poll/2024/03/12/k2G3vxJLgChZwvOUksRLW/20240312_Hxw-3n6aed.json",
            "19:<EMAIL>",
            "Testing a chat name",
            "<EMAIL>",
            "Nakamura, Shun",
            ["<EMAIL>", "<EMAIL>"],
            pd.NA,
            ["<EMAIL>", "<EMAIL>", "<EMAIL>"],
        ]
    ]

    return pd.DataFrame(data, columns=columns)


@pytest.fixture()
def attachment_data() -> list[dict]:
    return [
        {
            "id": "1710162723325",
            "contentType": "messageReference",
            "contentUrl": None,
            "content": '{"messageId":"1710162723325","messagePreview":"今週の木曜日、NY時間10AMはいかがでしょうか？","messageSender":{"application":null,"device":null,"user":{"userIdentityType":"aadUser","tenantId":null,"id":"1324fbb3-7419-4630-8af9-0abc5b667a62","displayName":"Nakano, Mayumi (External)"}}}',  # noqa E501
            "name": None,
            "thumbnailUrl": None,
            "teamsAppId": None,
        }
    ]


@pytest.fixture()
def source_frame() -> pd.DataFrame:
    row_data = [
        {
            "@odata.type": "#microsoft.graph.chatMessage",
            "id": "1710162777482",
            "replyToId": None,
            "etag": "1710162777482",
            "messageType": "message",
            "createdDateTime": "2024-03-11T13:12:57.482Z",
            "lastModifiedDateTime": "2024-03-11T13:12:57.482Z",
            "lastEditedDateTime": None,
            "deletedDateTime": None,
            "subject": "",
            "summary": None,
            "chatId": "19:<EMAIL>",  # noqa E501
            "importance": "normal",
            "locale": "en-us",
            "webUrl": None,
            "channelIdentity": np.nan,
            "policyViolation": None,
            "eventDetail": np.nan,
            "attachments": list(
                [
                    {
                        "id": "1710162723325",
                        "contentType": "messageReference",
                        "contentUrl": None,
                        "content": '{"messageId":"1710162723325","messagePreview":"今週の木曜日、NY時間10AMはいかがでしょうか？","messageSender":{"application":null,"device":null,"user":{"userIdentityType":"aadUser","tenantId":null,"id":"1324fbb3-7419-4630-8af9-0abc5b667a62","displayName":"Nakano, Mayumi (External)"}}}',  # noqa E501
                        "name": None,
                        "thumbnailUrl": None,
                        "teamsAppId": None,
                    }
                ]
            ),
            "mentions": list([]),
            "reactions": list([]),
            "from.application": np.nan,
            "from.device": np.nan,
            "<EMAIL>": "#microsoft.graph.teamworkUserIdentity",
            "from.user.id": "8d5d60e6-bad7-499d-8ad5-2fe9ac4aed88",
            "from.user.displayName": "Nakamura, Shun",
            "from.user.userIdentityType": "aadUser",
            "from.user.tenantId": "06008bd2-6e59-43fd-97fe-f0eb0a4cd5b6",
            "from.user.email": "<EMAIL>",
            "body.contentType": "html",
            "body.content": '<p></p>\n<attachment id="1710162723325"></attachment>\nいただきます。ありがとうございます！\n<p></p>',  # noqa E501
            "messageHistory": np.nan,
            "from": np.nan,
            "<EMAIL>": np.nan,
            "eventDetail.visibleHistoryStartDateTime": np.nan,
            "eventDetail.members": np.nan,
            "eventDetail.initiator.application": np.nan,
            "eventDetail.initiator.device": np.nan,
            "<EMAIL>": np.nan,
            "eventDetail.initiator.user.id": np.nan,
            "eventDetail.initiator.user.displayName": np.nan,
            "eventDetail.initiator.user.userIdentityType": np.nan,
            "eventDetail.initiator.user.tenantId": np.nan,
            "<EMAIL>": np.nan,
            "replies": np.nan,
            "channelIdentity.teamId": np.nan,
            "channelIdentity.channelId": np.nan,
            "eventDetail.chatId": np.nan,
            "eventDetail.chatDisplayName": np.nan,
            "<EMAIL>": np.nan,
            "chatName": "Testing a chat name",
            "toId": list(
                [
                    {
                        "userId": "fdb71db0-6c8c-4239-a107-fca942d298e2",
                        "email": "<EMAIL>",
                        "displayName": "Test1 Data",
                    },
                    {
                        "userId": "fdb71db0-6c8c-4239-a107-fca942d2xxxx",
                        "email": "<EMAIL>",
                        "displayName": "Test2 Data",
                    },
                ]
            ),
        }
    ]

    return pd.DataFrame({"value": [row_data]})


class TestMsTeamsMessageMappings:
    def test_empty_source_frame_raises_skip(self) -> None:
        with pytest.raises(
            SkipIfEmptyMSTeamsMessageMappingsSourceFrame,
            match="No Messages found. Nothing to transform.",
        ):
            MSTeamsMessageMappings(source_frame=pd.DataFrame(), realm="foo", input_file_path="foo")

    @mock_aws
    def test_mapping_end_to_end_1(self, expected_output_frame_1) -> None:
        script_path = Path(__file__).parent
        input_source_frame_path1 = script_path.joinpath("data", "ms_teams_input.json")

        create_and_add_objects_to_s3_bucket(bucket_name=BUCKET_NAME_1)

        task = MSTeamsMessageMappings(
            source_frame=pd.read_json(path_or_buf=str(input_source_frame_path1)),
            realm=f"s3://{BUCKET_NAME_1}",
            input_file_path=f"s3://{BUCKET_NAME_1}/folder/file.json",
        )

        result = task.process()

        pd.testing.assert_frame_equal(expected_output_frame_1, result)

    @mock_aws
    def test_mapping_end_to_end_2(self, expected_output_frame_2, source_frame) -> None:
        # This test is essentially the same as test_mapping_end_to_end_1
        # but using different data.

        mock_json_path = "aries/ingress/nonstreamed/polled/ms_teams_poll/2024/03/12/k2G3vxJLgChZwvOUksRLW/20240312_Hxw-3n6aed.json"  # noqa E501
        create_and_add_objects_to_s3_bucket(bucket_name=BUCKET_NAME_2)
        task = MSTeamsMessageMappings(
            source_frame, f"s3://{BUCKET_NAME_2}", f"s3://{BUCKET_NAME_2}/{mock_json_path}"
        )
        result = task.process()

        pd.testing.assert_frame_equal(expected_output_frame_2, result)

    @mock_aws
    @freeze_time("2025-01-25 09:09:09")
    def test_normalise_attachment_data(self, attachment_data, source_frame):
        message_id = "1710162777482"
        mock_attachment_path = f"aries/ingress/nonstreamed/polled/ms_teams_poll/2024/03/12/k2G3vxJLgChZwvOUksRLW/attachments/{message_id}/1710162723325"  # noqa E501
        mock_json_path = "aries/ingress/nonstreamed/polled/ms_teams_poll/2024/03/12/k2G3vxJLgChZwvOUksRLW/20240312_Hxw-3n6aed.json"  # noqa E501

        create_and_add_objects_to_s3_bucket(bucket_name=BUCKET_NAME_2)

        ms_teams_mappings = MSTeamsMessageMappings(
            source_frame, f"s3://{BUCKET_NAME_2}", f"s3://{BUCKET_NAME_2}/{mock_json_path}"
        )
        normalised_data = ms_teams_mappings._normalise_attachment_data(message_id, attachment_data)

        assert normalised_data[0][MessageColumns.SIZE_IN_BYTES] == 5240
        assert normalised_data[0][MessageColumns.FILE_INFO_LOCATION_BUCKET] == BUCKET_NAME_2
        assert normalised_data[0][MessageColumns.FILE_INFO_LOCATION_KEY] == mock_attachment_path

    @mock_aws
    def test_metadata_is_edited(self):
        create_and_add_objects_to_s3_bucket(bucket_name=BUCKET_NAME_1)

        source_frame = pd.DataFrame(
            {
                "value": [
                    [
                        {"lastEditedDateTime": "2025-05-05"},
                        {"lastEditedDateTime": "2024-03-11T13:12:57.482000Z"},
                        {"lastEditedDateTime": "foobar"},
                        {"lastEditedDateTime": None},
                        {"lastEditedDateTime": pd.NA},
                    ]
                ]
            }
        )

        task = MSTeamsMessageMappings(
            source_frame=source_frame,
            realm=f"s3://{BUCKET_NAME_1}",
            input_file_path=f"s3://{BUCKET_NAME_1}/folder/file.json",
        )

        task.metadata_is_edited()

        assert task.target_df.loc[:, "metadata.isEdited"].tolist() == [
            True,
            True,
            True,
            False,
            False,
        ]

    @pytest.mark.parametrize(
        "source_frame_edits, target_df_edits, es_edits, expected_output_edits",
        [
            # 1.
            (
                [{"id": "1234"}, {"id": "5678"}],
                [
                    {
                        "metadata.isEdited": False,
                    },
                    {
                        "metadata.isEdited": False,
                    },
                ],
                [None, None],
                [pd.NA, pd.NA],
            ),
            # 2.
            (
                [
                    {
                        "id": "1234",
                        "interBodyText": [
                            {
                                "text": {"content": "after edits"},
                                "timestamp": "2025-05-10T00:00:01.100000Z",
                            }
                        ],
                        "from.user.displayName": "abcd",
                    },
                    {
                        "id": "5678",
                        "interBodyText": {
                            "text": "after edits",
                            "timestamp": "2025-05-10T00:00:01.100000Z",
                        },
                        "from.user.displayName": "efgh",
                    },  # not a list, to test instance check
                ],
                [{"metadata.isEdited": True}, {"metadata.isEdited": True}],
                [],
                [
                    [
                        {
                            "editedBy": "abcd",
                            "text": "after edits\n",
                            "timestampEdited": "2025-05-10T00:00:01.100000Z",
                            "type": "PLAIN",
                        }
                    ],
                    pd.NA,
                ],
            ),
            # 3.
            (
                [
                    {
                        "id": "1234",
                        "interBodyText": [
                            {
                                "text": {"content": "after edits"},
                                "timestamp": "2025-05-10T00:00:01.100000Z",
                            }
                        ],
                        "from.user.displayName": "abcd",
                    },
                    {
                        "id": "1234",
                        "interBodyText": [
                            {
                                "text": {"content": "after edits"},
                                "timestamp": "2025-05-10T00:00:01.100000Z",
                            }
                        ],
                        "from.user.displayName": "abcd",
                    },
                ],
                [
                    {
                        "metadata.isEdited": True,
                        "timestamps.created": "2025-05-09T00:00:01.100000Z",
                        "body.text": "original text\n",
                    },
                    {
                        "metadata.isEdited": True,
                        "timestamps.created": "2025-05-08T00:00:01.100000Z",
                        "body.text": "original text\n",
                    },
                ],
                [
                    {
                        "&id": pd.NA,
                        "body.text": "original text from es\n",
                        "timestamps.created": "2025-05-09T00:00:01.100000Z",
                        "identifiers.fromUserId": "abcd1",
                        "body.edits": pd.NA,
                        "body.displayText": "original text from es\n",
                    },
                    {
                        "&id": pd.NA,
                        "body.text": "original text\n",
                        "timestamps.created": "2025-05-10T00:00:01.100000Z",
                        "identifiers.fromUserId": "abcd",
                        "body.edits": pd.NA,
                        "body.displayText": "original text\n",
                    },  # to test that if what comes from es is a dupe of
                    # what is in the current edit, it gets discarded
                ],
                [
                    [
                        {
                            "editedBy": "abcd",
                            "text": "after edits\n",
                            "timestampEdited": "2025-05-10T00:00:01.100000Z",
                            "type": "PLAIN",
                        },
                        {
                            "editedBy": "abcd",
                            "text": "original text\n",
                            "timestampEdited": "2025-05-09T00:00:01.100000Z",
                            "type": "PLAIN",
                        },
                    ],
                    [
                        {
                            "editedBy": "abcd",
                            "text": "after edits\n",
                            "timestampEdited": "2025-05-10T00:00:01.100000Z",
                            "type": "PLAIN",
                        },
                    ],
                ],
            ),
            # 4.
            (
                [
                    {
                        "id": "1234",
                        "interBodyText": [
                            {
                                "text": {"content": "after edits"},
                                "timestamp": "2025-05-10T00:00:01.100000Z",
                            }
                        ],
                        "from.user.displayName": "abcd",
                    }
                ],
                [
                    {
                        "metadata.isEdited": True,
                        "timestamps.created": "2025-05-08T00:00:01.100000Z",
                        "body.text": "original text\n",
                    }
                ],
                [
                    {
                        "&id": pd.NA,
                        "body.text": "original text from es\n",
                        "timestamps.created": "2025-05-08T00:00:01.100000Z",
                        "identifiers.fromUserId": "abcd",
                        "body.edits": [
                            {
                                "editedBy": "abcd",
                                "text": "edits from es\n",
                                "timestampEdited": "2025-05-09T00:00:01.100000Z",
                                "type": "PLAIN",
                            }
                        ],
                        "body.displayText": "original text from es\n",
                    }
                ],
                [
                    [
                        {
                            "editedBy": "abcd",
                            "text": "after edits\n",
                            "timestampEdited": "2025-05-10T00:00:01.100000Z",
                            "type": "PLAIN",
                        },
                        {
                            "editedBy": "abcd",
                            "text": "original text\n",
                            "timestampEdited": "2025-05-08T00:00:01.100000Z",
                            "type": "PLAIN",
                        },
                        {
                            "editedBy": "abcd",
                            "text": "edits from es\n",
                            "timestampEdited": "2025-05-09T00:00:01.100000Z",
                            "type": "PLAIN",
                        },
                    ]
                ],
            ),
        ],
    )
    @mock_aws
    def test_body_edits(self, source_frame_edits, target_df_edits, es_edits, expected_output_edits):
        # 1. No edits in current file (won't query es. Can't have edits in es but not in current)
        # 2. Edits in current file, entry not existed in ES
        # 2.1 Extra - if interBodyText not a list, not considered
        # 3. Edits in current file, entry existed in ES but w/t edits
        # 3.1 Extra - test drop duplicates if coming from ES
        # 4. Edits in current file, entry existed in ES w/ edits

        source_frame = pd.DataFrame({"value": [source_frame_edits]})

        create_and_add_objects_to_s3_bucket(bucket_name=BUCKET_NAME_1)
        task = MSTeamsMessageMappings(
            source_frame=source_frame,
            realm=f"s3://{BUCKET_NAME_1}",
            input_file_path=f"s3://{BUCKET_NAME_1}/folder/file.json",
        )
        task.target_df = pd.DataFrame(target_df_edits)
        task.es_edits_frame_with_source_index = pd.DataFrame(es_edits)
        task.body_edits()

        assert task.target_df["body.edits"].tolist() == expected_output_edits

    @mock_aws
    def test_pre_process(self):
        create_and_add_objects_to_s3_bucket(bucket_name=BUCKET_NAME_1)

        source_frame = pd.DataFrame(
            {
                "value": [
                    [
                        {
                            "id": "1",
                            "lastEditedDateTime": "2025-05-05",
                            "createdDateTime": "2025-05-05",
                            "toId": [],
                        },
                        {
                            "id": "3",
                            "lastEditedDateTime": "foobar",
                            "createdDateTime": "foobar",
                            "toId": [],
                        },
                        {
                            "id": "4",
                            "lastEditedDateTime": None,
                            "createdDateTime": None,
                            "toId": [],
                        },
                        {
                            "id": "5",
                            "lastEditedDateTime": pd.NA,
                            "createdDateTime": pd.NA,
                            "toId": [],
                        },
                        {
                            "id": "2",
                            "lastEditedDateTime": "2025-05-10T00:00:01.10000Z",
                            "createdDateTime": "2025-05-10T00:00:01.10000Z",
                            "toId": [],
                        },
                    ]
                ]
            }
        )

        task = MSTeamsMessageMappings(
            source_frame=source_frame,
            realm=f"s3://{BUCKET_NAME_1}",
            input_file_path=f"s3://{BUCKET_NAME_1}/folder/file.json",
        )
        task.es_edits_frame = pd.DataFrame(
            {
                "metadata.messageId": ["2"],
                "&id": ["2222"],
                "&timestamp": ["1750951877766"],
            }
        )

        task.pre_process()

        assert task.pre_process_df["__LAST_EDITED_DATE_TIME__"].tolist() == [
            "2025-05-05T00:00:00.000000Z",
            pd.NA,
            pd.NA,
            pd.NA,
            "2025-05-10T00:00:01.100000Z",
        ]
        assert task.pre_process_df["__CREATED_DATE_TIME__"].tolist() == [
            "2025-05-05T00:00:00.000000Z",
            pd.NA,
            pd.NA,
            pd.NA,
            "2025-05-10T00:00:01.100000Z",
        ]
        assert list(task.es_edits_frame_with_source_index.index) == [4]
        assert task.es_edits_frame_with_source_index.iloc[0].tolist() == [
            "2",
            "2",
            "2222",
            "1750951877766",
            pd.NA,
            pd.NA,
            pd.NA,
        ]


def create_and_add_objects_to_s3_bucket(bucket_name: str):
    """Recreate the s3 bucket, and copy all the files from `LOCAL_BUCKET_PATH`
    to s3 mocked bucket.

    :param bucket_name: Bucket name of Mock S3 bucket
    :return: None
    """

    local_bucket_path = DATA_PATH.joinpath("buckets", bucket_name)

    # Create bucket
    s3 = boto3.client("s3", region_name="us-east-1")
    s3.create_bucket(Bucket=bucket_name)

    for file_ in local_bucket_path.rglob("*"):
        if file_.is_file():
            _path = file_.as_posix().replace(f"{local_bucket_path}/", "")

            with open(file_, "rb") as f:
                s3.put_object(Bucket=bucket_name, Key=_path, Body=f.read())
