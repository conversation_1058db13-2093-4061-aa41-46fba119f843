# type: ignore
import pandas as pd
from aries_se_comms_tasks.feeds.message.refinitiv_tr_eikon.helper_functions import (
    eval_literal_string_list,
    get_to_ids,
)


class TestHelperFunctions:
    def test_get_to_ids(self):
        success_scenario = get_to_ids(from_id="<PERSON>", all_ids="['<PERSON>', '<PERSON>', '<PERSON>']")
        failure_scenario_single_string = get_to_ids(from_id="<PERSON>", all_ids="<PERSON>")
        failure_scenario_list = get_to_ids(from_id="<PERSON>", all_ids="['<PERSON>']")
        failure_scenario_na = get_to_ids(from_id="<PERSON>", all_ids=pd.NA)
        failure_scenario_none = get_to_ids(from_id="<PERSON>", all_ids=None)

        assert success_scenario == ["<PERSON>", "<PERSON>"]
        assert failure_scenario_single_string == []
        assert failure_scenario_list == []
        assert failure_scenario_na == []
        assert failure_scenario_none == []

    def test_literal_eval(self):
        assert eval_literal_string_list(literal_string_list="['<PERSON>', '<PERSON>', '<PERSON>']") == [
            "<PERSON>",
            "<PERSON>",
            "<PERSON>",
        ]
        assert eval_literal_string_list(literal_string_list="['<PERSON>']") == ["John"]
        assert eval_literal_string_list(literal_string_list="<PERSON>") == ["John"]
        assert eval_literal_string_list(literal_string_list=None) == []
        assert eval_literal_string_list(literal_string_list=pd.NA) == []
