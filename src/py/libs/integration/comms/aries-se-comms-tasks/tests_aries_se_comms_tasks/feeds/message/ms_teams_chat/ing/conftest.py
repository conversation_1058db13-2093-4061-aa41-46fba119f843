import pandas as pd
import pytest
from se_elastic_schema.static.communication import ChatEventType


@pytest.fixture()
def expected_create_df_messages_and_edits() -> pd.DataFrame:
    return pd.DataFrame(
        [
            {
                "attachments": pd.NA,
                "cdr_id": "2825f246-b7c2-11ee-8497-000d3ab0c4bd",
                "plain_message": "Been thinking about you. Hope it went very well"
                " which would be deserved ✨",
                "source": "<PERSON><PERSON>@ing.com",
                "__timestamp_converted__": "2024-03-27T17:50:27.327000Z",
                "__composite_message_id__": "2825f246-b7c2-11ee-8497-000d3ab0c4bd1711561827326",
                "__composite_thread_id__": pd.NA,
                "__is_deleted__": False,
                "__is_edited__": False,
                "__to_ids__": ["<EMAIL>"],
                "__timestamp_created__": "2024-03-27T17:50:27.327000Z",
                "destination_name": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> (<PERSON>, <PERSON><PERSON>, <PERSON><PERSON> (<PERSON>)",
                "__edits_list__": pd.NA,
                "__attachment_azure_storage_url_list__": pd.NA,
            },
            {
                "attachments": pd.NA,
                "cdr_id": "2825f246-b7c2-11ee-8497-000d3ab0c4bd",
                "plain_message": "Thank you ❤️",
                "source": "<EMAIL>",
                "__timestamp_converted__": "2024-03-27T20:44:06.090000Z",
                "__composite_message_id__": "2825f246-b7c2-11ee-8497-000d3ab0c4bd1711572246090",
                "__composite_thread_id__": pd.NA,
                "__is_deleted__": False,
                "__is_edited__": False,
                "__to_ids__": ["<EMAIL>"],
                "__timestamp_created__": "2024-03-27T20:44:06.090000Z",
                "destination_name": "Rauhala, A.M. (Anna), Hoogland, R. (René)",
                "__edits_list__": pd.NA,
                "__attachment_azure_storage_url_list__": pd.NA,
            },
            {
                "attachments": pd.NA,
                "cdr_id": "2825f246-b7c2-11ee-8497-000d3ab0c4bd",
                "plain_message": "I think it went well.\n(heart, Hoogland, R. (René))",
                "source": "<EMAIL>",
                "__timestamp_converted__": "2024-03-27T20:59:52.897000Z",
                "__composite_message_id__": "2825f246-b7c2-11ee-8497-000d3ab0c4bd1711572254890",
                "__composite_thread_id__": pd.NA,
                "__is_deleted__": False,
                "__is_edited__": True,
                "__to_ids__": ["<EMAIL>"],
                "__timestamp_created__": "2024-03-27T20:44:14.890000Z",
                "destination_name": "Rauhala, A.M. (Anna), Hoogland, R. (René)",
                "__edits_list__": [
                    {
                        "editedBy": "<EMAIL>",
                        "text": "I think it went well.",
                        "timestampEdited": "2024-03-27T20:44:14.890000Z",
                        "type": "PLAIN",
                    }
                ],
                "__attachment_azure_storage_url_list__": pd.NA,
            },
            {
                "attachments": pd.NA,
                "cdr_id": "2825f246-b7c2-11ee-8497-000d3ab0c4bd",
                "plain_message": "Hope you will manage with your workload. Let’s"
                " try to catch properly soon!",
                "source": "<EMAIL>",
                "__timestamp_converted__": "2024-03-27T20:59:58.820000Z",
                "__composite_message_id__": "2825f246-b7c2-11ee-8497-000d3ab0c4bd1711572321310",
                "__composite_thread_id__": pd.NA,
                "__is_deleted__": False,
                "__is_edited__": True,
                "__to_ids__": ["<EMAIL>"],
                "__timestamp_created__": "2024-03-27T20:45:21.310000Z",
                "destination_name": "Rauhala, A.M. (Anna), Hoogland, R. (René)",
                "__edits_list__": [
                    {
                        "editedBy": "<EMAIL>",
                        "text": "Hope you will manage with"
                        " your workload. Let’s try to catch properly soon!",
                        "timestampEdited": "2024-03-27T20:45:21.310000Z",
                        "type": "PLAIN",
                    },
                    {
                        "editedBy": "<EMAIL>",
                        "text": "Hope you will manage with"
                        " your workload. Let’s try to catch properly soon!\n(heart,"
                        " Hoogland, R. (René))",
                        "timestampEdited": "2024-03-27T20:59:49.073000Z",
                        "type": "PLAIN",
                    },
                ],
                "__attachment_azure_storage_url_list__": pd.NA,
            },
            {
                "attachments": pd.NA,
                "cdr_id": "2825f246-b7c2-11ee-8497-000d3ab0c4bd",
                "plain_message": "Definitely!",
                "source": "<EMAIL>",
                "__timestamp_converted__": "2024-03-27T21:00:02.733000Z",
                "__composite_message_id__": "2825f246-b7c2-11ee-8497-000d3ab0c4bd1711573202731",
                "__composite_thread_id__": pd.NA,
                "__is_deleted__": False,
                "__is_edited__": False,
                "__to_ids__": ["<EMAIL>"],
                "__timestamp_created__": "2024-03-27T21:00:02.733000Z",
                "destination_name": "Rauhala, A.M. (Anna), Hoogland, R. (René)",
                "__edits_list__": pd.NA,
                "__attachment_azure_storage_url_list__": pd.NA,
            },
        ]
    )


@pytest.fixture()
def expected_create_df_messages_deletes_and_attachments() -> pd.DataFrame:
    return pd.DataFrame(
        [
            {
                "attachments": pd.NA,
                "cdr_id": "6b6c05c8-4923-11ee-8491-000d3abb4cac",
                "plain_message": "[https://think.ing.com/downloads/pdf/article/china-"
                "economy-not-in-a-great-decline-but-in-a-great-transition"
                "?utm_campaign=March-27_china-economy-not-in-a-great"
                "-decline-but-in-a-great-transition&utm_medium=email&"
                "utm_source=emailing_article&M_BT=935605909] [https://"
                "think.ing.com/downloads/pdf/article/china-economy-not-in"
                "-a-great-decline-but-in-a-great-transition?utm_campaign="
                "March-27_china-economy-not-in-a-great-decline-but-in-a-"
                "great-transition&utm_medium=email&utm_source=emailing_"
                "article&M_BT=935605909]",
                "source": "<EMAIL>",
                "__timestamp_converted__": "2024-03-27T12:50:21.000000Z",
                "__composite_message_id__": "6b6c05c8-4923-11ee-8491-000d3abb4cac1711543796312",
                "__composite_thread_id__": pd.NA,
                "__is_deleted__": True,
                "__is_edited__": False,
                "__to_ids__": ["<EMAIL>"],
                "__timestamp_created__": "2024-03-27T12:49:56.313000Z",
                "destination_name": "Wasantha, D. (Damith), Kalamboussis, J. (Jason)",
                "__edits_list__": [
                    {
                        "editedBy": "<EMAIL>",
                        "text": "[https://think.ing.com/downloads/pdf/article/china-economy-"
                        "not-in-a-great-decline-but-in-a-great-transition?utm_campaign"
                        "=March-27_china-economy-not-in-a-great-decline-but-in-a-great"
                        "-transition&utm_medium=email&utm_source=emailing_article&M_"
                        "BT=935605909] [https://think.ing.com/downloads/pdf/article/"
                        "china-economy-not-in-a-great-decline-but-in-a-great-transition?"
                        "utm_campaign=March-27_china-economy-not-in-a-great-decline-but"
                        "-in-a-great-transition&utm_medium=email&utm_source=emailing_"
                        "article&M_BT=935605909]",
                        "timestampEdited": "2024-03-27T12:49:56.313000Z",
                        "type": "PLAIN",
                    }
                ],
                "__attachment_azure_storage_url_list__": pd.NA,
            },
            {
                "attachments": pd.NA,
                "cdr_id": "6b6c05c8-4923-11ee-8491-000d3abb4cac",
                "plain_message": "[https://think.ing.com/] [https://think.ing.com/]",
                "source": "<EMAIL>",
                "__timestamp_converted__": "2024-03-27T12:50:24.660000Z",
                "__composite_message_id__": "6b6c05c8-4923-11ee-8491-000d3abb4cac1711543824658",
                "__composite_thread_id__": pd.NA,
                "__is_deleted__": False,
                "__is_edited__": False,
                "__to_ids__": ["<EMAIL>"],
                "__timestamp_created__": "2024-03-27T12:50:24.660000Z",
                "destination_name": "Wasantha, D. (Damith), Kalamboussis, J. (Jason)",
                "__edits_list__": pd.NA,
                "__attachment_azure_storage_url_list__": pd.NA,
            },
            {
                "attachments": pd.NA,
                "cdr_id": "6b6c05c8-4923-11ee-8491-000d3abb4cac",
                "plain_message": "China maybe? Also please tell me on Sol Comp"
                " what you found. Thanks",
                "source": "<EMAIL>",
                "__timestamp_converted__": "2024-03-27T12:50:49.997000Z",
                "__composite_message_id__": "6b6c05c8-4923-11ee-8491-000d3abb4cac1711543849997",
                "__composite_thread_id__": pd.NA,
                "__is_deleted__": False,
                "__is_edited__": False,
                "__to_ids__": ["<EMAIL>"],
                "__timestamp_created__": "2024-03-27T12:50:49.997000Z",
                "destination_name": "Wasantha, D. (Damith), Kalamboussis, J. (Jason)",
                "__edits_list__": pd.NA,
                "__attachment_azure_storage_url_list__": pd.NA,
            },
            {
                "attachments": pd.NA,
                "cdr_id": "6b6c05c8-4923-11ee-8491-000d3abb4cac",
                "plain_message": "Basically need feedback on the CTPH results. Thanks",
                "source": "<EMAIL>",
                "__timestamp_converted__": "2024-03-27T12:51:20.923000Z",
                "__composite_message_id__": "6b6c05c8-4923-11ee-8491-000d3abb4cac1711543880921",
                "__composite_thread_id__": pd.NA,
                "__is_deleted__": False,
                "__is_edited__": False,
                "__to_ids__": ["<EMAIL>"],
                "__timestamp_created__": "2024-03-27T12:51:20.923000Z",
                "destination_name": "Wasantha, D. (Damith), Kalamboussis, J. (Jason)",
                "__edits_list__": pd.NA,
                "__attachment_azure_storage_url_list__": pd.NA,
            },
            {
                "attachments": pd.NA,
                "cdr_id": "6b6c05c8-4923-11ee-8491-000d3abb4cac",
                "plain_message": "You told me yesterday ING has 2.5 as rates"
                " YE24. I checked and this is 2.20...even on bloomberg.",
                "source": "<EMAIL>",
                "__timestamp_converted__": "2024-03-27T13:01:56.333000Z",
                "__composite_message_id__": "6b6c05c8-4923-11ee-8491-000d3abb4cac1711544516333",
                "__composite_thread_id__": pd.NA,
                "__is_deleted__": False,
                "__is_edited__": False,
                "__to_ids__": ["<EMAIL>"],
                "__timestamp_created__": "2024-03-27T13:01:56.333000Z",
                "destination_name": "Wasantha, D. (Damith), Kalamboussis, J. (Jason)",
                "__edits_list__": pd.NA,
                "__attachment_azure_storage_url_list__": pd.NA,
            },
            {
                "attachments": pd.NA,
                "cdr_id": "6b6c05c8-4923-11ee-8491-000d3abb4cac",
                "plain_message": "Also Bloomberg CSS is 2.41.",
                "source": "<EMAIL>",
                "__timestamp_converted__": "2024-03-27T13:04:35.710000Z",
                "__composite_message_id__": "6b6c05c8-4923-11ee-8491-000d3abb4cac1711544675709",
                "__composite_thread_id__": pd.NA,
                "__is_deleted__": False,
                "__is_edited__": False,
                "__to_ids__": ["<EMAIL>"],
                "__timestamp_created__": "2024-03-27T13:04:35.710000Z",
                "destination_name": "Wasantha, D. (Damith), Kalamboussis, J. (Jason)",
                "__edits_list__": pd.NA,
                "__attachment_azure_storage_url_list__": pd.NA,
            },
            {
                "attachments": pd.NA,
                "cdr_id": "6b6c05c8-4923-11ee-8491-000d3abb4cac",
                "plain_message": "> [From: Wasantha, D. (Damith)]\n>  📷\n"
                "Please update this as we had discussed."
                " Need to see it.",
                "source": "<EMAIL>",
                "__timestamp_converted__": "2024-03-27T13:09:33.290000Z",
                "__composite_message_id__": "6b6c05c8-4923-11ee-8491-000d3abb4cac1711544973288",
                "__composite_thread_id__": pd.NA,
                "__is_deleted__": False,
                "__is_edited__": False,
                "__to_ids__": ["<EMAIL>"],
                "__timestamp_created__": "2024-03-27T13:09:33.290000Z",
                "destination_name": "Wasantha, D. (Damith), Kalamboussis, J. (Jason)",
                "__edits_list__": pd.NA,
                "__attachment_azure_storage_url_list__": pd.NA,
            },
            {
                "attachments": pd.NA,
                "cdr_id": "6b6c05c8-4923-11ee-8491-000d3abb4cac",
                "plain_message": "Also correct me but the impact is not 7% but 14 %"
                " on an annual basis...",
                "source": "<EMAIL>",
                "__timestamp_converted__": "2024-03-27T13:09:48.157000Z",
                "__composite_message_id__": "6b6c05c8-4923-11ee-8491-000d3abb4cac1711544988155",
                "__composite_thread_id__": pd.NA,
                "__is_deleted__": False,
                "__is_edited__": False,
                "__to_ids__": ["<EMAIL>"],
                "__timestamp_created__": "2024-03-27T13:09:48.157000Z",
                "destination_name": "Wasantha, D. (Damith), Kalamboussis, J. (Jason)",
                "__edits_list__": pd.NA,
                "__attachment_azure_storage_url_list__": pd.NA,
            },
            {
                # "attachments": {
                #     "attachment": {
                #         "id": "0-weu-d20-c0afc7ca5e28d849166dab876a406a9d",
                #         "verba_file_name": "image.png",
                #         "verba_file_size": "123250",
                #         "verba_ccdr_id": "3d955903-ebfb-11ee-8498-000d3abb4cac",
                #         "verba_url": "2024\\03\\27\\3d955903-ebfb-11ee-8498-000d3abb4cac_0
                #         -weu-d20"
                #         "-c0afc7ca5e28d849166dab876a406a9d_(20240327131016062).att",
                #         "message_id": "1711545016062",
                #         "storage_path": "https://ingluwareprodeuwestorage.blob.core.windows.net/"
                #         "2024/03/27/3d955903-ebfb-11ee-8498-000d3abb4cac_0-weu-d20-c0afc7ca5e"
                #         "28d849166dab876a406a9d_(20240327131016062).att",
                #         "storage_type": "azure",
                #         "missing": "false",
                #     }
                # },
                "attachments": pd.NA,
                "cdr_id": "6b6c05c8-4923-11ee-8491-000d3abb4cac",
                "plain_message": '{"verba_attachment_id" : '
                '"0-weu-d20-c0afc7ca5e28d849166dab876a406a9d"}',
                "source": "<EMAIL>",
                "__timestamp_converted__": "2024-03-27T13:10:16.063000Z",
                "__composite_message_id__": "6b6c05c8-4923-11ee-8491-000d3abb4cac1711545016062",
                "__composite_thread_id__": pd.NA,
                "__is_deleted__": False,
                "__is_edited__": False,
                "__to_ids__": ["<EMAIL>"],
                "__timestamp_created__": "2024-03-27T13:10:16.063000Z",
                "destination_name": "Wasantha, D. (Damith), Kalamboussis, J. (Jason)",
                "__edits_list__": pd.NA,
                # "__attachment_azure_storage_url_list__": [
                #     "https://ingluwareprodeuwestorage.blob.core.windows.net/"
                #     "2024/03/27/3d955903-ebfb-11ee-8498-000d3abb4cac_0-weu-d20-"
                #     "c0afc7ca5e28d849166dab876a406a9d_(20240327131016062).att"
                # ],
                "__attachment_azure_storage_url_list__": pd.NA,
            },
            {
                "attachments": pd.NA,
                "cdr_id": "6b6c05c8-4923-11ee-8491-000d3abb4cac",
                "plain_message": "The above is what I see in transcript Nov23,",
                "source": "<EMAIL>",
                "__timestamp_converted__": "2024-03-27T13:10:28.773000Z",
                "__composite_message_id__": "6b6c05c8-4923-11ee-8491-000d3abb4cac1711545028772",
                "__composite_thread_id__": pd.NA,
                "__is_deleted__": False,
                "__is_edited__": False,
                "__to_ids__": ["<EMAIL>"],
                "__timestamp_created__": "2024-03-27T13:10:28.773000Z",
                "destination_name": "Wasantha, D. (Damith), Kalamboussis, J. (Jason)",
                "__edits_list__": pd.NA,
                "__attachment_azure_storage_url_list__": pd.NA,
            },
            {
                # "attachments": {
                #     "attachment": {
                #         "id": "0-weu-d7-857e078f3cd78d3f0f4c538dc556ce55",
                #         "verba_file_name": "image(1).png",
                #         "verba_file_size": "4804",
                #         "verba_ccdr_id": "3d955903-ebfb-11ee-8498-000d3abb4cac",
                #         "verba_url": "2024\\03\\27\\3d955903-ebfb-11ee-8498-000d3abb4cac_
                #         0-weu-d7-"
                #         "857e078f3cd78d3f0f4c538dc556ce55_(20240327132043499).att",
                #         "message_id": "1711545643499",
                #         "storage_path": "https://ingluwareprodeuwestorage.blob.core.windows.net
                #         /2024/03/
                #         27/3d955903-ebfb-11ee"
                #         "-8498-000d3abb4cac_0-weu-d7-857e078f3cd78d3f0f4c538dc556ce55_(20240
                #         327132043
                #         499).att",
                #         "storage_type": "azure",
                #         "missing": "false",
                #     }
                # },
                "attachments": pd.NA,
                "cdr_id": "6b6c05c8-4923-11ee-8491-000d3abb4cac",
                "plain_message": "> [From: Kalamboussis, J. (Jason)]\n> "
                " You told me yesterday ING has 2.5 as rates"
                " YE24. I checked and this is 2.20...even "
                'on bloomberg.\n{"verba_attachment_id" : '
                '"0-weu-d7-857e078f3cd78d3f0f4c538dc556ce55"}',
                "source": "<EMAIL>",
                "__timestamp_converted__": "2024-03-27T13:20:43.500000Z",
                "__composite_message_id__": "6b6c05c8-4923-11ee-8491-000d3abb4cac1711545643499",
                "__composite_thread_id__": pd.NA,
                "__is_deleted__": False,
                "__is_edited__": False,
                "__to_ids__": ["<EMAIL>"],
                "__timestamp_created__": "2024-03-27T13:20:43.500000Z",
                "destination_name": "Wasantha, D. (Damith), Kalamboussis, J. (Jason)",
                "__edits_list__": pd.NA,
                # "__attachment_azure_storage_url_list__": [
                #     "https://ingluwareprodeuwestorage.blob.core.windows.net/"
                #     "2024/03/27/3d955903-ebfb-11ee-8498-000d3abb4cac_0-weu-d7-"
                #     "857e078f3cd78d3f0f4c538dc556ce55_(20240327132043499).att"
                # ],
                "__attachment_azure_storage_url_list__": pd.NA,
            },
            {
                # "attachments": {
                #     "attachment": {
                #         "id": "0-weu-d15-b173e6216964e79cb00a2f08588343bf",
                #         "verba_file_name": "image.gif",
                #         "verba_file_size": "37707",
                #         "verba_ccdr_id": "3d955903-ebfb-11ee-8498-000d3abb4cac",
                #         "verba_url": "2024\\03\\27\\3d955903-ebfb-11ee-8498-000d3abb4cac_0"
                #         "-weu-d15-b173e6216964e79cb00a2f08588343bf_(20240327132047777).att",
                #         "message_id": "1711545647777",
                #         "storage_path": "https://ingluwareprodeuwestorage.blob.core.windows.net/2024/03/27/"
                #         "3d955903-ebfb-11ee-8498-000d3abb4cac_0-weu-d15-b173e6216964e79cb0"
                #         "0a2f08588343bf_(20240327132047777).att",
                #         "storage_type": "azure",
                #         "missing": "false",
                #     }
                # },
                "attachments": pd.NA,
                "cdr_id": "6b6c05c8-4923-11ee-8491-000d3abb4cac",
                "plain_message": '{"verba_attachment_id" : '
                '"0-weu-d15-b173e6216964e79cb00a2f08588343bf"}',
                "source": "<EMAIL>",
                "__timestamp_converted__": "2024-03-27T13:20:47.777000Z",
                "__composite_message_id__": "6b6c05c8-4923-11ee-8491-000d3abb4cac1711545647777",
                "__composite_thread_id__": pd.NA,
                "__is_deleted__": False,
                "__is_edited__": False,
                "__to_ids__": ["<EMAIL>"],
                "__timestamp_created__": "2024-03-27T13:20:47.777000Z",
                "destination_name": "Wasantha, D. (Damith), Kalamboussis, J. (Jason)",
                "__edits_list__": pd.NA,
                # "__attachment_azure_storage_url_list__": [
                #     "https://ingluwareprodeuwestorage.blob.core.windows.net"
                #     "/2024/03/27/3d955903-ebfb-11ee-8498-000d3abb4cac_0-weu-d15"
                #     "-b173e6216964e79cb00a2f08588343bf_(20240327132047777).att"
                # ],
                "__attachment_azure_storage_url_list__": pd.NA,
            },
            {
                "attachments": pd.NA,
                "cdr_id": "6b6c05c8-4923-11ee-8491-000d3abb4cac",
                "plain_message": "> [From: Kalamboussis, J. (Jason)]\n>"
                "  Also correct me but the impact is not"
                " 7% but 14 % on an annual basis...\n"
                "Correct 14% seems right, I made a mistake here I think",
                "source": "<EMAIL>",
                "__timestamp_converted__": "2024-03-27T13:22:48.063000Z",
                "__composite_message_id__": "6b6c05c8-4923-11ee-8491-000d3abb4cac1711545768062",
                "__composite_thread_id__": pd.NA,
                "__is_deleted__": False,
                "__is_edited__": False,
                "__to_ids__": ["<EMAIL>"],
                "__timestamp_created__": "2024-03-27T13:22:48.063000Z",
                "destination_name": "Wasantha, D. (Damith), Kalamboussis, J. (Jason)",
                "__edits_list__": pd.NA,
                "__attachment_azure_storage_url_list__": pd.NA,
            },
            {
                "attachments": pd.NA,
                "cdr_id": "6b6c05c8-4923-11ee-8491-000d3abb4cac",
                "plain_message": "> [From: Kalamboussis, J. (Jason)]\n>  Please"
                " update this as we had discussed. Need to see it."
                "\nI am trying to build a model in capital sheet"
                " for China Solvency seperately to get this chart,"
                " but now arriving at correct Sol 2 impact - so a bit stuck",
                "source": "<EMAIL>",
                "__timestamp_converted__": "2024-03-27T13:23:54.510000Z",
                "__composite_message_id__": "6b6c05c8-4923-11ee-8491-000d3abb4cac1711545834510",
                "__composite_thread_id__": pd.NA,
                "__is_deleted__": False,
                "__is_edited__": False,
                "__to_ids__": ["<EMAIL>"],
                "__timestamp_created__": "2024-03-27T13:23:54.510000Z",
                "destination_name": "Wasantha, D. (Damith), Kalamboussis, J. (Jason)",
                "__edits_list__": pd.NA,
                "__attachment_azure_storage_url_list__": pd.NA,
            },
            {
                "attachments": pd.NA,
                "cdr_id": "6b6c05c8-4923-11ee-8491-000d3abb4cac",
                "plain_message": "Can discuss if yo can call",
                "source": "<EMAIL>",
                "__timestamp_converted__": "2024-03-27T13:24:30.880000Z",
                "__composite_message_id__": "6b6c05c8-4923-11ee-8491-000d3abb4cac1711545870879",
                "__composite_thread_id__": pd.NA,
                "__is_deleted__": False,
                "__is_edited__": False,
                "__to_ids__": ["<EMAIL>"],
                "__timestamp_created__": "2024-03-27T13:24:30.880000Z",
                "destination_name": "Wasantha, D. (Damith), Kalamboussis, J. (Jason)",
                "__edits_list__": pd.NA,
                "__attachment_azure_storage_url_list__": pd.NA,
            },
            {
                # "attachments": {
                #     "attachment": {
                #         "id": "0-weu-d19-184416775549b8632ddb8e86bb6f0599",
                #         "verba_file_name": "image(2).png",
                #         "verba_file_size": "13736",
                #         "verba_ccdr_id": "3d955903-ebfb-11ee-8498-000d3abb4cac",
                #         "verba_url": "2024\\03\\27\\3d955903-ebfb-11ee-8498-000d3"
                #         "abb4cac_0-weu-d19-184416775549b8632ddb8e86bb6f"
                #         "0599_(20240327132552703).att",
                #         "message_id": "1711545952703",
                #         "storage_path": "https://ingluwareprodeuwestorage.blob.core.windows.net/
                #         2024/03/27/"
                #         "3d955903-ebfb-11ee-8498-000d3abb4cac_0-weu-d19-184416775549b8632d
                #         db8e86bb6f0599_"
                #         "(20240327132552703).att",
                #         "storage_type": "azure",
                #         "missing": "false",
                #     }
                # },
                "attachments": pd.NA,
                "cdr_id": "6b6c05c8-4923-11ee-8491-000d3abb4cac",
                "plain_message": '{"verba_attachment_id" : '
                '"0-weu-d19-184416775549b8632ddb8e86bb6f0599"}',
                "source": "<EMAIL>",
                "__timestamp_converted__": "2024-03-27T13:25:52.703000Z",
                "__composite_message_id__": "6b6c05c8-4923-11ee-8491-000d3abb4cac1711545952703",
                "__composite_thread_id__": pd.NA,
                "__is_deleted__": False,
                "__is_edited__": False,
                "__to_ids__": ["<EMAIL>"],
                "__timestamp_created__": "2024-03-27T13:25:52.703000Z",
                "destination_name": "Wasantha, D. (Damith), Kalamboussis, J. (Jason)",
                "__edits_list__": pd.NA,
                # "__attachment_azure_storage_url_list__": [
                #     "https://ingluwareprodeuwestorage.blob.core.windows.net"
                #     "/2024/03/27/3d955903-ebfb-11ee-8498-000d3abb4cac_0-weu-d19-1844167755"
                #     "49b8632ddb8e86bb6f0599_(20240327132552703).att"
                # ],
                "__attachment_azure_storage_url_list__": pd.NA,
            },
            {
                "attachments": pd.NA,
                "cdr_id": "6b6c05c8-4923-11ee-8491-000d3abb4cac",
                "plain_message": "This is all you get in CTIH results for TPL solvency",
                "source": "<EMAIL>",
                "__timestamp_converted__": "2024-03-27T13:26:04.333000Z",
                "__composite_message_id__": "6b6c05c8-4923-11ee-8491-000d3abb4cac1711545964332",
                "__composite_thread_id__": pd.NA,
                "__is_deleted__": False,
                "__is_edited__": False,
                "__to_ids__": ["<EMAIL>"],
                "__timestamp_created__": "2024-03-27T13:26:04.333000Z",
                "destination_name": "Wasantha, D. (Damith), Kalamboussis, J. (Jason)",
                "__edits_list__": pd.NA,
                "__attachment_azure_storage_url_list__": pd.NA,
            },
            {
                "attachments": pd.NA,
                "cdr_id": "6b6c05c8-4923-11ee-8491-000d3abb4cac",
                "plain_message": "nothing else, so difficult to get an idea of the"
                " OF and SCR movement",
                "source": "<EMAIL>",
                "__timestamp_converted__": "2024-03-27T13:26:23.393000Z",
                "__composite_message_id__": "6b6c05c8-4923-11ee-8491-000d3abb4cac1711545983393",
                "__composite_thread_id__": pd.NA,
                "__is_deleted__": False,
                "__is_edited__": False,
                "__to_ids__": ["<EMAIL>"],
                "__timestamp_created__": "2024-03-27T13:26:23.393000Z",
                "destination_name": "Wasantha, D. (Damith), Kalamboussis, J. (Jason)",
                "__edits_list__": pd.NA,
                "__attachment_azure_storage_url_list__": pd.NA,
            },
            {
                "attachments": pd.NA,
                "cdr_id": "6b6c05c8-4923-11ee-8491-000d3abb4cac",
                "plain_message": "The other issue is earlier before 1H23 CTIH"
                " reported Available Capital (OF) and minimum "
                "Capital (SCR), so we had something to work on",
                "source": "<EMAIL>",
                "__timestamp_converted__": "2024-03-27T14:04:06.577000Z",
                "__composite_message_id__": "6b6c05c8-4923-11ee-8491-000d3abb4cac1711548246575",
                "__composite_thread_id__": pd.NA,
                "__is_deleted__": False,
                "__is_edited__": False,
                "__to_ids__": ["<EMAIL>"],
                "__timestamp_created__": "2024-03-27T14:04:06.577000Z",
                "destination_name": "Wasantha, D. (Damith), Kalamboussis, J. (Jason)",
                "__edits_list__": pd.NA,
                "__attachment_azure_storage_url_list__": pd.NA,
            },
            {
                "attachments": pd.NA,
                "cdr_id": "6b6c05c8-4923-11ee-8491-000d3abb4cac",
                "plain_message": "but 2H23 results dont have this, only the 284%",
                "source": "<EMAIL>",
                "__timestamp_converted__": "2024-03-27T14:04:36.930000Z",
                "__composite_message_id__": "6b6c05c8-4923-11ee-8491-000d3abb4cac1711548276930",
                "__composite_thread_id__": pd.NA,
                "__is_deleted__": False,
                "__is_edited__": False,
                "__to_ids__": ["<EMAIL>"],
                "__timestamp_created__": "2024-03-27T14:04:36.930000Z",
                "destination_name": "Wasantha, D. (Damith), Kalamboussis, J. (Jason)",
                "__edits_list__": pd.NA,
                "__attachment_azure_storage_url_list__": pd.NA,
            },
            {
                "attachments": pd.NA,
                "cdr_id": "6b6c05c8-4923-11ee-8491-000d3abb4cac",
                "plain_message": "SOL 2: please prepare and close"
                " all solvencies with Sakuni for end of 1Q. Thanks",
                "source": "<EMAIL>",
                "__timestamp_converted__": "2024-03-27T14:59:44.310000Z",
                "__composite_message_id__": "6b6c05c8-4923-11ee-8491-000d3abb4cac1711551584310",
                "__composite_thread_id__": pd.NA,
                "__is_deleted__": False,
                "__is_edited__": False,
                "__to_ids__": ["<EMAIL>"],
                "__timestamp_created__": "2024-03-27T14:59:44.310000Z",
                "destination_name": "Wasantha, D. (Damith), Kalamboussis, J. (Jason)",
                "__edits_list__": pd.NA,
                "__attachment_azure_storage_url_list__": pd.NA,
            },
        ]
    )


@pytest.fixture()
def messages_from_elasticsearch_edits() -> pd.DataFrame:
    """Mock for records from Elastic for multiple edits and deletes.

    :return: Dataframe containing mock elastic results
    """
    return pd.DataFrame(
        [
            # Record 1: edit where the Elastic record already has body.edits[] populated
            {
                "sourceKey": "s3://test.dev.steeleye.co/aries/ingest_batching/ing_ms_teams_chat/2023/04/19/test.ndjson",
                "&id": "id1",
                "&uniqueProps": ["<EMAIL>", "<EMAIL>"],
                "&key": "Message:id1",
                "roomId": "272d16e3-9e4b-11ed-8486-000d3abb4cac",
                "roomName": "Babich, I. (Inna), Dmytriyev, A. (Anton)",
                "&model": "Message",
                "hasAttachment": False,
                "&version": 1,
                "&hash": "hash1",
                "&timestamp": 1671630400111,
                "&user": "system",
                "metadata.messageId": "272d16e3-9e4b-11ed-8486-000d3abb4cac1695632802785",
                "metadata.source.client": "MS Teams Chat",
                "metadata.threadId": "272d16e3-9e4b-11ed-8486-000d3abb4cac",
                "identifiers.allIds": ["<EMAIL>", "<EMAIL>"],
                "identifiers.fromId": "<EMAIL>",
                "identifiers.fromIdAddlInfo.raw": "<EMAIL>",
                "identifiers.toIds": ["<EMAIL>", "<EMAIL>"],
                "identifiers.toIdsAddlInfo": [
                    {"raw": "<EMAIL>"},
                ],
                "timestamps.timestampStart": "2022-12-27T23:39:19.000000Z",
                "timestamps.created": "2022-12-27T23:39:19.000000Z",
                "body.text": "привет",
                "metadata.isEdited": True,
                "metadata.isDeleted": False,
                "metadata.source.fileInfo.location.bucket": "test.dev.steeleye.co",
                "metadata.source.fileInfo.location.key": "aries/ingress/streamed/"
                "evented/test_file.eml",
                "body.edits": [
                    {
                        "editedBy": "<EMAIL>",
                        "text": "привет инна!",
                        "timestampEdited": "2022-12-21T13:47:16.000000Z",
                    }
                ],
            },
            # Record 2: edit where the original message hasn't been edited before
            {
                "sourceKey": "s3://test.dev.steeleye.co/aries/ingress/streamed/evented/ing_ms_teams_chat/2023/04/19/test.ndjson",
                "&id": "id2",
                "&uniqueProps": ["<EMAIL>", "<EMAIL>"],
                "&key": "Message:id2",
                "roomId": "272d16e3-9e4b-11ed-8486-000d3abb4cac",
                "roomName": "Babich, I. (Inna), Dmytriyev, A. (Anton)",
                "&model": "Message",
                "hasAttachment": False,
                "&version": 1,
                "&hash": "hash2",
                "&timestamp": 1671630400111,
                "&user": "system",
                "metadata.messageId": "272d16e3-9e4b-11ed-8486-000d3abb4cac1695632812234",
                "metadata.source.client": "MS Teams Chat",
                "metadata.threadId": "272d16e3-9e4b-11ed-8486-000d3abb4cac",
                "identifiers.allIds": ["<EMAIL>", "<EMAIL>"],
                "identifiers.fromId": "<EMAIL>",
                "identifiers.fromIdAddlInfo.raw": "<EMAIL>",
                "identifiers.toIds": ["<EMAIL>"],
                "identifiers.toIdsAddlInfo": [
                    {"raw": "<EMAIL>"},
                ],
                "timestamps.timestampStart": "2022-12-21T13:45:55.000000Z",
                "timestamps.created": "2022-12-21T13:45:55.000000Z",
                "body.text": "дара",
                "metadata.isEdited": False,
                "metadata.isDeleted": False,
                "metadata.source.fileInfo.location.bucket": "test.dev.steeleye.co",
                "metadata.source.fileInfo.location.key": "aries/ingress/streamed/"
                "evented/test_file.eml",
                "body.edits": pd.NA,
            },
            # Record 3: Delete where Elastic record already has body.edits[] populated
            {
                "sourceKey": "s3://test.dev.steeleye.co/aries/ingress/streamed/evented/ing_ms_teams_chat/2023/04/19/test.ndjson",
                "&id": "id3",
                "&uniqueProps": ["<EMAIL>", "<EMAIL>"],
                "&key": "Message:id3",
                "roomId": "272d16e3-9e4b-11ed-8486-000d3abb4cac",
                "roomName": "Babich, I. (Inna), Dmytriyev, A. (Anton)",
                "&model": "Message",
                "hasAttachment": False,
                "&version": 1,
                "&hash": "hash3",
                "&timestamp": 1671630400111,
                "&user": "system",
                "metadata.messageId": "272d16e3-9e4b-11ed-8486-000d3abb4cac1695632730397",
                "metadata.source.client": "MS Teams Chat",
                "metadata.threadId": "272d16e3-9e4b-11ed-8486-000d3abb4cac",
                "identifiers.allIds": ["<EMAIL>", "<EMAIL>"],
                "identifiers.fromId": "<EMAIL>",
                "identifiers.fromIdAddlInfo.raw": "<EMAIL>",
                "identifiers.toIds": ["<EMAIL>"],
                "identifiers.toIdsAddlInfo": [
                    {"raw": "<EMAIL>"},
                ],
                "timestamps.timestampStart": "2022-12-21T13:45:55.000000Z",
                "timestamps.created": "2022-12-21T13:45:55.000000Z",
                "body.text": "Привет!",
                "metadata.isEdited": True,
                "metadata.isDeleted": False,
                "metadata.source.fileInfo.location.bucket": "test.dev.steeleye.co",
                "metadata.source.fileInfo.location.key": "aries/ingress/streamed/"
                "evented/test_file.eml",
                "body.edits": [
                    {
                        "editedBy": "<EMAIL>",
                        "text": "привет инна!",
                        "timestampEdited": "2022-12-21T13:47:16.000000Z",
                    }
                ],
            },
            # Record 4: Delete where Elastic record does not have edits
            {
                "sourceKey": "s3://test.dev.steeleye.co/aries/ingress/streamed/evented/ing_ms_teams_chat/2023/04/19/test.ndjson",
                "&id": "id4",
                "&uniqueProps": ["<EMAIL>", "<EMAIL>"],
                "&key": "Message:id4",
                "roomId": "272d16e3-9e4b-11ed-8486-000d3abb4cac",
                "roomName": "Babich, I. (Inna), Dmytriyev, A. (Anton)",
                "&model": "Message",
                "hasAttachment": False,
                "&version": 1,
                "&hash": "hash4",
                "&timestamp": 1671630400111,
                "&user": "system",
                "metadata.messageId": "272d16e3-9e4b-11ed-8486-000d3abb4cac1695632745270",
                "metadata.source.client": "MS Teams Chat",
                "metadata.threadId": "272d16e3-9e4b-11ed-8486-000d3abb4cac",
                "identifiers.allIds": ["<EMAIL>", "<EMAIL>"],
                "identifiers.fromId": "<EMAIL>",
                "identifiers.fromIdAddlInfo.raw": "<EMAIL>",
                "identifiers.toIds": ["<EMAIL>"],
                "identifiers.toIdsAddlInfo": [
                    {"raw": "<EMAIL>"},
                ],
                "timestamps.timestampStart": "2022-12-21T13:45:55.000000Z",
                "timestamps.created": "2022-12-21T13:45:55.000000Z",
                "body.text": "А ЛДК скоро будет, не знаешь?",
                "metadata.isEdited": False,
                "metadata.isDeleted": False,
                "metadata.source.fileInfo.location.bucket": "test.dev.steeleye.co",
                "metadata.source.fileInfo.location.key": "aries/ingress/streamed/"
                "evented/test_file.eml",
                "body.edits": pd.NA,
            },
            # Record 5: 2 edits where record does not have edits populated
            {
                "sourceKey": "s3://test.dev.steeleye.co/aries/ingress/streamed/evented/ing_ms_teams_chat/2023/04/19/test.ndjson",
                "&id": "id5",
                "&uniqueProps": ["<EMAIL>", "<EMAIL>"],
                "&key": "Message:id5",
                "roomId": "272d16e3-9e4b-11ed-8486-000d3abb4cac",
                "roomName": "Babich, I. (Inna), Dmytriyev, A. (Anton)",
                "&model": "Message",
                "hasAttachment": False,
                "&version": 1,
                "&hash": "hash5",
                "&timestamp": 1671630400111,
                "&user": "system",
                "metadata.messageId": "272d16e3-9e4b-11ed-8486-000d3abb4cac1695632773240",
                "metadata.source.client": "MS Teams Chat",
                "metadata.threadId": "272d16e3-9e4b-11ed-8486-000d3abb4cac",
                "identifiers.allIds": ["<EMAIL>", "<EMAIL>"],
                "identifiers.fromId": "<EMAIL>",
                "identifiers.fromIdAddlInfo.raw": "<EMAIL>",
                "identifiers.toIds": ["<EMAIL>"],
                "identifiers.toIdsAddlInfo": [
                    {"raw": "<EMAIL>"},
                ],
                "timestamps.timestampStart": "2022-12-21T13:45:55.000000Z",
                "timestamps.created": "2022-12-21T13:45:55.000000Z",
                "body.text": "дара K",
                "metadata.isEdited": True,
                "metadata.isDeleted": False,
                "metadata.source.fileInfo.location.bucket": "test.dev.steeleye.co",
                "metadata.source.fileInfo.location.key": "aries/ingress/streamed/"
                "evented/test_file.eml",
                "body.edits": [
                    {
                        "editedBy": "<EMAIL>",
                        "text": "я хочу на часик",
                        "timestampEdited": "2022-12-21T13:47:16.000000Z",
                    }
                ],
            },
            # Record 6: 2 edits where record hasn't been edited before
            {
                "sourceKey": "s3://test.dev.steeleye.co/aries/ingress/streamed/evented/ing_ms_teams_chat/2023/04/19/test.ndjson",
                "&id": "id6",
                "&uniqueProps": ["<EMAIL>", "<EMAIL>"],
                "&key": "Message:id6",
                "roomId": "272d16e3-9e4b-11ed-8486-000d3abb4cac",
                "roomName": "Babich, I. (Inna), Dmytriyev, A. (Anton)",
                "&model": "Message",
                "hasAttachment": False,
                "&version": 1,
                "&hash": "hash6",
                "&timestamp": 1671630400111,
                "&user": "system",
                "metadata.messageId": "272d16e3-9e4b-11ed-8486-000d3abb4cac1695632781202",
                "metadata.source.client": "MS Teams Chat",
                "metadata.threadId": "272d16e3-9e4b-11ed-8486-000d3abb4cac",
                "identifiers.allIds": ["<EMAIL>", "<EMAIL>"],
                "identifiers.fromId": "<EMAIL>",
                "identifiers.fromIdAddlInfo.raw": "<EMAIL>",
                "identifiers.toIds": ["<EMAIL>"],
                "identifiers.toIdsAddlInfo": [
                    {"raw": "<EMAIL>"},
                ],
                "timestamps.timestampStart": "2022-12-21T13:45:55.000000Z",
                "timestamps.created": "2022-12-21T13:45:55.000000Z",
                "body.text": "дара",
                "metadata.isEdited": False,
                "metadata.isDeleted": False,
                "metadata.source.fileInfo.location.bucket": "test.dev.steeleye.co",
                "metadata.source.fileInfo.location.key": "aries/ingress/streamed/"
                "evented/test_file.eml",
                "body.edits": pd.NA,
            },
            # Record 7: Edit, then delete where Elastic record has body.edits[] populated
            {
                "sourceKey": "s3://test.dev.steeleye.co/aries/ingress/streamed/evented/ing_ms_teams_chat/2023/04/19/test.ndjson",
                "&id": "id7",
                "&uniqueProps": ["<EMAIL>", "<EMAIL>"],
                "&key": "Message:id7",
                "roomId": "272d16e3-9e4b-11ed-8486-000d3abb4cac",
                "roomName": "Babich, I. (Inna), Dmytriyev, A. (Anton)",
                "&model": "Message",
                "hasAttachment": False,
                "&version": 1,
                "&hash": "hash7",
                "&timestamp": 1671630400111,
                "&user": "system",
                "metadata.messageId": "272d16e3-9e4b-11ed-8486-000d3abb4cac1695632792102",
                "metadata.source.client": "MS Teams Chat",
                "metadata.threadId": "272d16e3-9e4b-11ed-8486-000d3abb4cac",
                "identifiers.allIds": ["<EMAIL>", "<EMAIL>"],
                "identifiers.fromId": "<EMAIL>",
                "identifiers.fromIdAddlInfo.raw": "<EMAIL>",
                "identifiers.toIds": ["<EMAIL>"],
                "identifiers.toIdsAddlInfo": [
                    {"raw": "<EMAIL>"},
                ],
                "timestamps.timestampStart": "2022-12-21T13:45:55.000000Z",
                "timestamps.created": "2022-12-21T13:45:55.000000Z",
                "body.text": "дара KIN",
                "metadata.isEdited": True,
                "metadata.isDeleted": False,
                "metadata.source.fileInfo.location.bucket": "test.dev.steeleye.co",
                "metadata.source.fileInfo.location.key": "aries/ingress/streamed/"
                "evented/test_file.eml",
                "body.edits": [
                    {
                        "editedBy": "<EMAIL>",
                        "text": "я х",
                        "timestampEdited": "2022-12-21T13:47:16.000000Z",
                    }
                ],
            },
            # Record 8: Edit, then delete where Elastic record hasn't been edited
            {
                "sourceKey": "s3://test.dev.steeleye.co/aries/ingress/streamed/evented/ing_ms_teams_chat/2023/04/19/test.ndjson",
                "&id": "id8",
                "&uniqueProps": ["<EMAIL>", "<EMAIL>"],
                "&key": "Message:id8",
                "roomId": "272d16e3-9e4b-11ed-8486-000d3abb4cac",
                "roomName": "Babich, I. (Inna), Dmytriyev, A. (Anton)",
                "&model": "Message",
                "hasAttachment": False,
                "&version": 1,
                "&hash": "hash8",
                "&timestamp": 1671630400111,
                "&user": "system",
                "metadata.messageId": "272d16e3-9e4b-11ed-8486-000d3abb4cac1695632805576",
                "metadata.source.client": "MS Teams Chat",
                "metadata.threadId": "272d16e3-9e4b-11ed-8486-000d3abb4cac",
                "identifiers.allIds": ["<EMAIL>", "<EMAIL>"],
                "identifiers.fromId": "<EMAIL>",
                "identifiers.fromIdAddlInfo.raw": "<EMAIL>",
                "identifiers.toIds": ["<EMAIL>"],
                "identifiers.toIdsAddlInfo": [
                    {"raw": "<EMAIL>"},
                ],
                "timestamps.timestampStart": "2022-12-21T13:45:55.000000Z",
                "timestamps.created": "2022-12-21T13:45:55.000000Z",
                "body.text": "дара KIND",
                "metadata.isEdited": False,
                "metadata.isDeleted": False,
                "metadata.source.fileInfo.location.bucket": "test.dev.steeleye.co",
                "metadata.source.fileInfo.location.key": "aries/ingress/streamed/"
                "evented/test_file.eml",
                "body.edits": pd.NA,
            },
        ]
    )


@pytest.fixture()
def expected_create_df_deletes_and_edits_in_elastic() -> pd.DataFrame:
    return pd.DataFrame(
        [
            {
                # "attachments": {
                #     "attachment": {
                #         "id": "0-weu-d15-fefbcc5aabdaa26c0b9e99fa8af1e0f1",
                #         "verba_file_name": "image.png",
                #         "verba_file_size": "39655",
                #         "verba_ccdr_id": "36dea279-5b83-11ee-8493-000d3abb4cac",
                #         "verba_url": "2023\\09\\25\\36dea279-5b83-11ee-8493-000d3abb4cac"
                #         "_0-weu-d15-fefbcc5aabdaa26c0b9e99fa8af1e0f1_(202"
                #         "30925090735155).att",
                #         "message_id": "1695632855155",
                #         "storage_path": "https://ingluwareprodeuwestorage.blob.core.windows.net/"
                #         "2023/09/25/36dea279-5b83-11ee-8493-000d3abb4cac_0-weu-d15"
                #         "-fefbcc5aabdaa26c0b9e99fa8af1e0f1_(20230925090735155).att",
                #         "storage_type": "azure",
                #         "missing": "false",
                #     }
                # },
                "attachments": pd.NA,
                "cdr_id": "272d16e3-9e4b-11ed-8486-000d3abb4cac",
                "plain_message": '{"verba_attachment_id" : '
                '"0-weu-d15-fefbcc5aabdaa26c0b9e99fa8af1e0f1"}'
                " \n(like, Dmytriyev, A. (Anton))",
                "source": "<EMAIL>",
                "__timestamp_converted__": "2023-09-25T09:07:40.187000Z",
                "__composite_message_id__": "272d16e3-9e4b-11ed-8486-000d3abb4cac1695632855155",
                "__composite_thread_id__": None,
                "__is_deleted__": False,
                "__is_edited__": True,
                "__to_ids__": ["<EMAIL>"],
                "__timestamp_created__": "2023-09-25T09:07:35.157000Z",
                "destination_name": "Babich, I. (Inna), Dmytriyev, A. (Anton)",
                "__edits_list__": [
                    {
                        "editedBy": "<EMAIL>",
                        "text": "'image' [image]",
                        "timestampEdited": "2023-09-25T09:07:35.157000Z",
                        "type": "PLAIN",
                    }
                ],
                # "__attachment_azure_storage_url_list__": [
                #     "https://ingluwareprodeuwestorage.blob.core.windows.net/"
                #     "2023/09/25/36dea279-5b83-11ee-8493-000d3abb4cac_0-weu-"
                #     "d15-fefbcc5aabdaa26c0b9e99fa8af1e0f1_(20230925090735155).att"
                # ],
                "__attachment_azure_storage_url_list__": pd.NA,
            }
        ]
    )


@pytest.fixture()
def expected_update_df_deletes_and_edits_in_elastic() -> pd.DataFrame:
    return pd.DataFrame(
        [
            {
                "sourceKey": "s3://test.dev.steeleye.co/aries/ingress/streamed/evented/ing_ms_teams_chat/2023/04/19/test.ndjson",
                "&id": "id3",
                "roomId": "272d16e3-9e4b-11ed-8486-000d3abb4cac",
                "roomName": "Babich, I. (Inna), Dmytriyev, A. (Anton)",
                "&model": "Message",
                "hasAttachment": False,
                "metadata.messageId": "272d16e3-9e4b-11ed-8486-000d3abb4cac1695632730397",
                "metadata.source.client": "MS Teams Chat",
                "metadata.threadId": "272d16e3-9e4b-11ed-8486-000d3abb4cac",
                "identifiers.allIds": ["<EMAIL>", "<EMAIL>"],
                "identifiers.fromId": "<EMAIL>",
                "identifiers.fromIdAddlInfo.raw": "<EMAIL>",
                "identifiers.toIds": ["<EMAIL>"],
                "identifiers.toIdsAddlInfo": [{"raw": "<EMAIL>"}],
                "timestamps.timestampStart": "2023-09-25T09:05:30.397000Z",
                "timestamps.created": "2022-12-21T13:45:55.000000Z",
                "body.text": None,
                "metadata.isEdited": False,
                "metadata.isDeleted": True,
                "metadata.source.fileInfo.location.bucket": "test.dev.steeleye.co",
                "metadata.source.fileInfo.location.key": "aries/ingress/streamed/"
                "evented/test_file.eml",
                "body.edits": [
                    {
                        "editedBy": "<EMAIL>",
                        "text": "привет инна!",
                        "timestampEdited": "2022-12-21T13:47:16.000000Z",
                    },
                    {
                        "editedBy": "<EMAIL>",
                        "timestampEdited": "2022-12-21T13:45:55.000000Z",
                        "text": "Привет!",
                    },
                ],
            },
            {
                "sourceKey": "s3://test.dev.steeleye.co/aries/ingress/streamed/evented/ing_ms_teams_chat/2023/04/19/test.ndjson",
                "&id": "id4",
                "roomId": "272d16e3-9e4b-11ed-8486-000d3abb4cac",
                "roomName": "Babich, I. (Inna), Dmytriyev, A. (Anton)",
                "&model": "Message",
                "hasAttachment": False,
                "metadata.messageId": "272d16e3-9e4b-11ed-8486-000d3abb4cac1695632745270",
                "metadata.source.client": "MS Teams Chat",
                "metadata.threadId": "272d16e3-9e4b-11ed-8486-000d3abb4cac",
                "identifiers.allIds": ["<EMAIL>", "<EMAIL>"],
                "identifiers.fromId": "<EMAIL>",
                "identifiers.fromIdAddlInfo.raw": "<EMAIL>",
                "identifiers.toIds": ["<EMAIL>"],
                "identifiers.toIdsAddlInfo": [{"raw": "<EMAIL>"}],
                "timestamps.timestampStart": "2023-09-25T09:05:45.270000Z",
                "timestamps.created": "2022-12-21T13:45:55.000000Z",
                "body.text": None,
                "metadata.isEdited": False,
                "metadata.isDeleted": True,
                "metadata.source.fileInfo.location.bucket": "test.dev.steeleye.co",
                "metadata.source.fileInfo.location.key": "aries/ingress/streamed/"
                "evented/test_file.eml",
                "body.edits": [
                    {
                        "editedBy": "<EMAIL>",
                        "timestampEdited": "2022-12-21T13:45:55.000000Z",
                        "text": "А ЛДК скоро будет, не знаешь?",
                    }
                ],
            },
            {
                "sourceKey": "s3://test.dev.steeleye.co/aries/ingress/streamed/evented/ing_ms_teams_chat/2023/04/19/test.ndjson",
                "&id": "id5",
                "roomId": "272d16e3-9e4b-11ed-8486-000d3abb4cac",
                "roomName": "Babich, I. (Inna), Dmytriyev, A. (Anton)",
                "&model": "Message",
                "hasAttachment": False,
                "metadata.messageId": "272d16e3-9e4b-11ed-8486-000d3abb4cac1695632773240",
                "metadata.source.client": "MS Teams Chat",
                "metadata.threadId": "272d16e3-9e4b-11ed-8486-000d3abb4cac",
                "identifiers.allIds": ["<EMAIL>", "<EMAIL>"],
                "identifiers.fromId": "<EMAIL>",
                "identifiers.fromIdAddlInfo.raw": "<EMAIL>",
                "identifiers.toIds": ["<EMAIL>"],
                "identifiers.toIdsAddlInfo": [{"raw": "<EMAIL>"}],
                "timestamps.timestampStart": "2023-09-25T09:07:13.240000Z",
                "timestamps.created": "2022-12-21T13:45:55.000000Z",
                "body.text": "я хочу на часик выйти скоро... Миша тоже не за компом сейчас EDIT2",
                "metadata.isEdited": True,
                "metadata.isDeleted": False,
                "metadata.source.fileInfo.location.bucket": "test.dev.steeleye.co",
                "metadata.source.fileInfo.location.key": "aries/ingress/streamed/"
                "evented/test_file.eml",
                "body.edits": [
                    {
                        "editedBy": "<EMAIL>",
                        "text": "я хочу на часик",
                        "timestampEdited": "2022-12-21T13:47:16.000000Z",
                    },
                    {
                        "editedBy": "<EMAIL>",
                        "timestampEdited": "2022-12-21T13:45:55.000000Z",
                        "text": "дара K",
                    },
                    {
                        "editedBy": "<EMAIL>",
                        "text": "я хочу на часик выйти скоро... Миша тоже не за"
                        " компом сейчас EDIT1",
                        "timestampEdited": "2023-09-25T09:06:13.240000Z",
                        "type": "PLAIN",
                    },
                ],
            },
            {
                "sourceKey": "s3://test.dev.steeleye.co/aries/ingress/streamed/evented/"
                "ing_ms_teams_chat/2023/04/19/test.ndjson",
                "&id": "id6",
                "roomId": "272d16e3-9e4b-11ed-8486-000d3abb4cac",
                "roomName": "Babich, I. (Inna), Dmytriyev, A. (Anton)",
                "&model": "Message",
                "hasAttachment": False,
                "metadata.messageId": "272d16e3-9e4b-11ed-8486-000d3abb4cac1695632781202",
                "metadata.source.client": "MS Teams Chat",
                "metadata.threadId": "272d16e3-9e4b-11ed-8486-000d3abb4cac",
                "identifiers.allIds": ["<EMAIL>", "<EMAIL>"],
                "identifiers.fromId": "<EMAIL>",
                "identifiers.fromIdAddlInfo.raw": "<EMAIL>",
                "identifiers.toIds": ["<EMAIL>"],
                "identifiers.toIdsAddlInfo": [{"raw": "<EMAIL>"}],
                "timestamps.timestampStart": "2023-09-25T09:08:21.203000Z",
                "timestamps.created": "2022-12-21T13:45:55.000000Z",
                "body.text": "привет, заявка уже есть EDIT 2",
                "metadata.isEdited": True,
                "metadata.isDeleted": False,
                "metadata.source.fileInfo.location.bucket": "test.dev.steeleye.co",
                "metadata.source.fileInfo.location.key": "aries/ingress/streamed/"
                "evented/test_file.eml",
                "body.edits": [
                    {
                        "editedBy": "<EMAIL>",
                        "timestampEdited": "2022-12-21T13:45:55.000000Z",
                        "text": "дара",
                    },
                    {
                        "editedBy": "<EMAIL>",
                        "text": "привет, заявка уже есть EDIT 1",
                        "timestampEdited": "2023-09-25T09:07:21.203000Z",
                        "type": "PLAIN",
                    },
                ],
            },
            {
                "sourceKey": "s3://test.dev.steeleye.co/aries/ingress/streamed/evented/ing_ms_teams_chat/2023/04/19/test.ndjson",
                "&id": "id7",
                "roomId": "272d16e3-9e4b-11ed-8486-000d3abb4cac",
                "roomName": "Babich, I. (Inna), Dmytriyev, A. (Anton)",
                "&model": "Message",
                "hasAttachment": False,
                "metadata.messageId": "272d16e3-9e4b-11ed-8486-000d3abb4cac1695632792102",
                "metadata.source.client": "MS Teams Chat",
                "metadata.threadId": "272d16e3-9e4b-11ed-8486-000d3abb4cac",
                "identifiers.allIds": ["<EMAIL>", "<EMAIL>"],
                "identifiers.fromId": "<EMAIL>",
                "identifiers.fromIdAddlInfo.raw": "<EMAIL>",
                "identifiers.toIds": ["<EMAIL>"],
                "identifiers.toIdsAddlInfo": [{"raw": "<EMAIL>"}],
                "timestamps.timestampStart": "2023-09-25T09:07:32.103000Z",
                "timestamps.created": "2022-12-21T13:45:55.000000Z",
                "body.text": "но мы будем сами выдавать EDIT 1",
                "metadata.isEdited": False,
                "metadata.isDeleted": True,
                "metadata.source.fileInfo.location.bucket": "test.dev.steeleye.co",
                "metadata.source.fileInfo.location.key": "aries/ingress/streamed/"
                "evented/test_file.eml",
                "body.edits": [
                    {
                        "editedBy": "<EMAIL>",
                        "text": "я х",
                        "timestampEdited": "2022-12-21T13:47:16.000000Z",
                    },
                    {
                        "editedBy": "<EMAIL>",
                        "timestampEdited": "2022-12-21T13:45:55.000000Z",
                        "text": "дара KIN",
                    },
                    {
                        "editedBy": "<EMAIL>",
                        "text": "но мы будем сами выдавать EDIT 1",
                        "timestampEdited": "2023-09-25T09:06:32.103000Z",
                        "type": "PLAIN",
                    },
                ],
            },
            {
                "sourceKey": "s3://test.dev.steeleye.co/aries/ingest_batching/ing_ms_teams_chat/2023/04/19/test.ndjson",
                "&id": "id1",
                "roomId": "272d16e3-9e4b-11ed-8486-000d3abb4cac",
                "roomName": "Babich, I. (Inna), Dmytriyev, A. (Anton)",
                "&model": "Message",
                "hasAttachment": False,
                "metadata.messageId": "272d16e3-9e4b-11ed-8486-000d3abb4cac1695632802785",
                "metadata.source.client": "MS Teams Chat",
                "metadata.threadId": "272d16e3-9e4b-11ed-8486-000d3abb4cac",
                "identifiers.allIds": ["<EMAIL>", "<EMAIL>"],
                "identifiers.fromId": "<EMAIL>",
                "identifiers.fromIdAddlInfo.raw": "<EMAIL>",
                "identifiers.toIds": ["<EMAIL>", "<EMAIL>"],
                "identifiers.toIdsAddlInfo": [{"raw": "<EMAIL>"}],
                "timestamps.timestampStart": "2023-09-25T09:06:48.213000Z",
                "timestamps.created": "2022-12-27T23:39:19.000000Z",
                "body.text": "ого отлично!\n(laugh, Babich, I. (Inna))",
                "metadata.isEdited": True,
                "metadata.isDeleted": False,
                "metadata.source.fileInfo.location.bucket": "test.dev.steeleye.co",
                "metadata.source.fileInfo.location.key": "aries/ingress/streamed/"
                "evented/test_file.eml",
                "body.edits": [
                    {
                        "editedBy": "<EMAIL>",
                        "text": "привет инна!",
                        "timestampEdited": "2022-12-21T13:47:16.000000Z",
                    },
                    {
                        "editedBy": "<EMAIL>",
                        "timestampEdited": "2022-12-27T23:39:19.000000Z",
                        "text": "привет",
                    },
                ],
            },
            {
                "sourceKey": "s3://test.dev.steeleye.co/aries/ingress/streamed/evented/ing_ms_teams_chat/2023/04/19/test.ndjson",
                "&id": "id8",
                "roomId": "272d16e3-9e4b-11ed-8486-000d3abb4cac",
                "roomName": "Babich, I. (Inna), Dmytriyev, A. (Anton)",
                "&model": "Message",
                "hasAttachment": False,
                "metadata.messageId": "272d16e3-9e4b-11ed-8486-000d3abb4cac1695632805576",
                "metadata.source.client": "MS Teams Chat",
                "metadata.threadId": "272d16e3-9e4b-11ed-8486-000d3abb4cac",
                "identifiers.allIds": ["<EMAIL>", "<EMAIL>"],
                "identifiers.fromId": "<EMAIL>",
                "identifiers.fromIdAddlInfo.raw": "<EMAIL>",
                "identifiers.toIds": ["<EMAIL>"],
                "identifiers.toIdsAddlInfo": [{"raw": "<EMAIL>"}],
                "timestamps.timestampStart": "2023-09-25T09:06:45.577000Z",
                "timestamps.created": "2022-12-21T13:45:55.000000Z",
                "body.text": "там больше 25 млн долл EDIT1",
                "metadata.isEdited": False,
                "metadata.isDeleted": True,
                "metadata.source.fileInfo.location.bucket": "test.dev.steeleye.co",
                "metadata.source.fileInfo.location.key": "aries/ingress/streamed/"
                "evented/test_file.eml",
                "body.edits": [
                    {
                        "editedBy": "<EMAIL>",
                        "timestampEdited": "2022-12-21T13:45:55.000000Z",
                        "text": "дара KIND",
                    },
                    {
                        "editedBy": "<EMAIL>",
                        "text": "там больше 25 млн долл EDIT1",
                        "timestampEdited": "2023-09-25T09:06:45.577000Z",
                        "type": "PLAIN",
                    },
                ],
            },
            {
                "sourceKey": "s3://test.dev.steeleye.co/aries/ingress/streamed/evented/ing_ms_teams_chat/2023/04/19/test.ndjson",
                "&id": "id2",
                "roomId": "272d16e3-9e4b-11ed-8486-000d3abb4cac",
                "roomName": "Babich, I. (Inna), Dmytriyev, A. (Anton)",
                "&model": "Message",
                "hasAttachment": False,
                "metadata.messageId": "272d16e3-9e4b-11ed-8486-000d3abb4cac1695632812234",
                "metadata.source.client": "MS Teams Chat",
                "metadata.threadId": "272d16e3-9e4b-11ed-8486-000d3abb4cac",
                "identifiers.allIds": ["<EMAIL>", "<EMAIL>"],
                "identifiers.fromId": "<EMAIL>",
                "identifiers.fromIdAddlInfo.raw": "<EMAIL>",
                "identifiers.toIds": ["<EMAIL>"],
                "identifiers.toIdsAddlInfo": [{"raw": "<EMAIL>"}],
                "timestamps.timestampStart": "2023-09-25T09:06:57.323000Z",
                "timestamps.created": "2022-12-21T13:45:55.000000Z",
                "body.text": "я в Б2 делаю сейчас\n(like, Babich, I. (Inna))",
                "metadata.isEdited": True,
                "metadata.isDeleted": False,
                "metadata.source.fileInfo.location.bucket": "test.dev.steeleye.co",
                "metadata.source.fileInfo.location.key": "aries/ingress/streamed/"
                "evented/test_file.eml",
                "body.edits": [
                    {
                        "editedBy": "<EMAIL>",
                        "timestampEdited": "2022-12-21T13:45:55.000000Z",
                        "text": "дара",
                    }
                ],
            },
        ]
    )


@pytest.fixture()
def elasticsearch_edits_rerun_file() -> pd.DataFrame:
    return pd.DataFrame(
        [
            {
                "sourceKey": "s3://test.dev.steeleye.co/aries/ingest_batching/ing_ms_teams_chat/"
                "2023/04/19/test.ndjson",
                "&id": "id1",
                "&uniqueProps": ["<EMAIL>", "<EMAIL>"],
                "&key": "Message:id1",
                "roomId": "2825f246-b7c2-11ee-8497-000d3ab0c4bd",
                "roomName": "Rauhala, A.M. (Anna), Hoogland, R. (René)",
                "&model": "Message",
                "hasAttachment": False,
                "&version": 1,
                "&hash": "hash1",
                "&timestamp": 1671630400111,
                "&user": "system",
                "metadata.messageId": "2825f246-b7c2-11ee-8497-000d3ab0c4bd1711572254890",
                "metadata.source.client": "MS Teams Chat",
                "metadata.threadId": "2825f246-b7c2-11ee-8497-000d3ab0c4bd",
                "identifiers.fromId": "<EMAIL>",
                "identifiers.fromIdAddlInfo": {"raw": "<EMAIL>", "countryCode": None},
                "identifiers.toIds": ["<EMAIL>"],
                "identifiers.toIdsAddlInfo": [
                    {"raw": "<EMAIL>", "countryCode": None}
                ],
                "identifiers.allIds": ["<EMAIL>", "<EMAIL>"],
                "identifiers.allDomains": ["ing.com"],
                "timestamps.timestampStart": "2024-03-27T20:59:52.897000Z",
                "timestamps.created": "2022-12-27T23:39:19.000000Z",
                "body.text": "I think it went well.\n(heart, Hoogland, R. (René))",
                "metadata.isEdited": True,
                "metadata.isDeleted": False,
                "metadata.source.fileInfo.location.bucket": "test.dev.steeleye.co",
                "metadata.source.fileInfo.location.key": "aries/ingress/streamed/"
                "evented/test_file.eml",
                "body.edits": [
                    {
                        "editedBy": "<EMAIL>",
                        "text": "I think it went well.",
                        "timestampEdited": "2024-03-27T20:44:14.890000Z",
                        "type": "PLAIN",
                    }
                ],
            },
            {
                "sourceKey": "s3://test.dev.steeleye.co/aries/ingest_batching/ing_ms_teams_chat/"
                "2023/04/19/test.ndjson",
                "&id": "id1",
                "&uniqueProps": ["<EMAIL>", "<EMAIL>"],
                "&key": "Message:id1",
                "roomId": "2825f246-b7c2-11ee-8497-000d3ab0c4bd",
                "roomName": "Rauhala, A.M. (Anna), Hoogland, R. (René)",
                "&model": "Message",
                "hasAttachment": False,
                "&version": 1,
                "&hash": "hash1",
                "&timestamp": 1671630400111,
                "&user": "system",
                "metadata.messageId": "2825f246-b7c2-11ee-8497-000d3ab0c4bd1711572321310",
                "metadata.source.client": "MS Teams Chat",
                "metadata.threadId": "2825f246-b7c2-11ee-8497-000d3ab0c4bd",
                "identifiers.fromId": "<EMAIL>",
                "identifiers.fromIdAddlInfo": {"raw": "<EMAIL>", "countryCode": None},
                "identifiers.toIds": ["<EMAIL>"],
                "identifiers.toIdsAddlInfo": [
                    {"raw": "<EMAIL>", "countryCode": None}
                ],
                "identifiers.allIds": ["<EMAIL>", "<EMAIL>"],
                "identifiers.allDomains": ["ing.com"],
                "timestamps.timestampStart": "2024-03-27T20:59:58.820000Z",
                "timestamps.created": "2024-03-27T20:45:21.310000Z",
                "body.text": "Hope you will manage with your workload. Let’s"
                " try to catch properly soon!",
                "metadata.isEdited": True,
                "metadata.isDeleted": False,
                "metadata.source.fileInfo.location.bucket": "test.dev.steeleye.co",
                "metadata.source.fileInfo.location.key": "aries/ingress/streamed/"
                "evented/test_file.eml",
                "body.edits": [
                    {
                        "editedBy": "<EMAIL>",
                        "text": "Hope you will manage with"
                        " your workload. Let’s try to catch properly soon!",
                        "timestampEdited": "2024-03-27T20:45:21.310000Z",
                        "type": "PLAIN",
                    },
                    {
                        "editedBy": "<EMAIL>",
                        "text": "Hope you will manage with"
                        " your workload. Let’s try to catch properly soon!\n(heart,"
                        " Hoogland, R. (René))",
                        "timestampEdited": "2024-03-27T20:59:49.073000Z",
                        "type": "PLAIN",
                    },
                ],
            },
        ]
    )


@pytest.fixture()
def expected_create_df_rerun_file_with_elastic_edits() -> pd.DataFrame:
    return pd.DataFrame(
        [
            {
                "attachments": None,
                "cdr_id": "2825f246-b7c2-11ee-8497-000d3ab0c4bd",
                "plain_message": "Been thinking about you. Hope it went very"
                " well which would be deserved ✨",
                "source": "<EMAIL>",
                "__timestamp_converted__": "2024-03-27T17:50:27.327000Z",
                "__composite_message_id__": "2825f246-b7c2-11ee-8497-000d3ab0c4bd1711561827326",
                "__composite_thread_id__": None,
                "__is_deleted__": False,
                "__is_edited__": False,
                "__to_ids__": ["<EMAIL>"],
                "__timestamp_created__": "2024-03-27T17:50:27.327000Z",
                "destination_name": "Rauhala, A.M. (Anna), Hoogland, R. (René)",
                "__edits_list__": None,
                "__attachment_azure_storage_url_list__": pd.NA,
            },
            {
                "attachments": None,
                "cdr_id": "2825f246-b7c2-11ee-8497-000d3ab0c4bd",
                "plain_message": "Thank you ❤️",
                "source": "<EMAIL>",
                "__timestamp_converted__": "2024-03-27T20:44:06.090000Z",
                "__composite_message_id__": "2825f246-b7c2-11ee-8497-000d3ab0c4bd1711572246090",
                "__composite_thread_id__": None,
                "__is_deleted__": False,
                "__is_edited__": False,
                "__to_ids__": ["<EMAIL>"],
                "__timestamp_created__": "2024-03-27T20:44:06.090000Z",
                "destination_name": "Rauhala, A.M. (Anna), Hoogland, R. (René)",
                "__edits_list__": None,
                "__attachment_azure_storage_url_list__": pd.NA,
            },
            {
                "attachments": None,
                "cdr_id": "2825f246-b7c2-11ee-8497-000d3ab0c4bd",
                "plain_message": "Definitely!",
                "source": "<EMAIL>",
                "__timestamp_converted__": "2024-03-27T21:00:02.733000Z",
                "__composite_message_id__": "2825f246-b7c2-11ee-8497-000d3ab0c4bd1711573202731",
                "__composite_thread_id__": None,
                "__is_deleted__": False,
                "__is_edited__": False,
                "__to_ids__": ["<EMAIL>"],
                "__timestamp_created__": "2024-03-27T21:00:02.733000Z",
                "destination_name": "Rauhala, A.M. (Anna), Hoogland, R. (René)",
                "__edits_list__": None,
                "__attachment_azure_storage_url_list__": pd.NA,
            },
        ]
    )


@pytest.fixture()
def expected_chat_events_df_chat_events_only() -> pd.DataFrame:
    return pd.DataFrame(
        [
            {
                "cdr_id": "4bede774-5625-11ee-8491-000d3ab0c4bd",
                "plain_message": "System Event: Chat renamed to: Call meeting rooms",
                "__timestamp_converted__": "2023-09-25T06:16:48.203000Z",
                "__chat_event_type__": ChatEventType.ROOM_NAME_UPDATED,
                "__chat_event_from_id__": "unknown",
                "destination_name": "Call meeting rooms",
            },
            {
                "cdr_id": "4bede774-5625-11ee-8491-000d3ab0c4bd",
                "plain_message": "System Event: MR-NL-TRC.00.045-Bloomberg (Restricted)"
                " joined the chat",
                "__timestamp_converted__": "2023-09-25T06:16:48.347000Z",
                "__chat_event_type__": ChatEventType.ENTERED,
                "__chat_event_from_id__": "<EMAIL>",
                "destination_name": "Call meeting rooms",
            },
            {
                "cdr_id": "4bede774-5625-11ee-8491-000d3ab0c4bd",
                "plain_message": "System Event: Haring, D.E. (Dennis) joined the chat",
                "__timestamp_converted__": "2023-09-25T06:16:48.347000Z",
                "__chat_event_type__": ChatEventType.ENTERED,
                "__chat_event_from_id__": "<EMAIL>",
                "destination_name": "Call meeting rooms",
            },
            {
                "cdr_id": "4bede774-5625-11ee-8491-000d3ab0c4bd",
                "plain_message": "System Event: Bruine, S.S. de (Sietske) joined the chat",
                "__timestamp_converted__": "2023-09-25T06:16:48.347000Z",
                "__chat_event_type__": ChatEventType.ENTERED,
                "__chat_event_from_id__": "<EMAIL>",
                "destination_name": "Call meeting rooms",
            },
        ]
    )


@pytest.fixture()
def chat_event_mappings_expected_output() -> pd.DataFrame:
    return pd.DataFrame(
        [
            {
                "eventQualifier": "System Event: Chat renamed to: Call meeting rooms",
                "eventType": ChatEventType.ROOM_NAME_UPDATED,
                "identifiers.fromId": "unknown",
                "identifiers.fromIdAddlInfo": {"raw": "unknown", "countryCode": None},
                "identifiers.allIds": ["unknown"],
                "metadata.source.client": "MS Teams Chat",
                "metadata.source.fileInfo.location.bucket": "test.dev.steeleye.co",
                "metadata.source.fileInfo.location.key": "folder/test.eml",
                "metadata.threadId": "4bede774-5625-11ee-8491-000d3ab0c4bd",
                "roomId": "4bede774-5625-11ee-8491-000d3ab0c4bd",
                "sourceKey": "s3://test.dev.steeleye.co/folder/batch.ndjson",
                "timestamps.created": "2023-09-25T06:16:48.203000Z",
                "timestamps.timestampStart": "2023-09-25T06:16:48.203000Z",
            },
            {
                "eventQualifier": "System Event: MR-NL-TRC.00.045-Bloomberg (Restricted)"
                " joined the chat",
                "eventType": ChatEventType.ENTERED,
                "identifiers.fromId": "<EMAIL>",
                "identifiers.fromIdAddlInfo": {
                    "raw": "<EMAIL>",
                    "countryCode": None,
                },
                "identifiers.allIds": ["<EMAIL>"],
                "metadata.source.client": "MS Teams Chat",
                "metadata.source.fileInfo.location.bucket": "test.dev.steeleye.co",
                "metadata.source.fileInfo.location.key": "folder/test.eml",
                "metadata.threadId": "4bede774-5625-11ee-8491-000d3ab0c4bd",
                "roomId": "4bede774-5625-11ee-8491-000d3ab0c4bd",
                "sourceKey": "s3://test.dev.steeleye.co/folder/batch.ndjson",
                "timestamps.created": "2023-09-25T06:16:48.347000Z",
                "timestamps.timestampStart": "2023-09-25T06:16:48.347000Z",
            },
            {
                "eventQualifier": "System Event: Haring, D.E. (Dennis) joined the chat",
                "eventType": ChatEventType.ENTERED,
                "identifiers.fromId": "<EMAIL>",
                "identifiers.fromIdAddlInfo": {"raw": "<EMAIL>", "countryCode": None},
                "identifiers.allIds": ["<EMAIL>"],
                "metadata.source.client": "MS Teams Chat",
                "metadata.source.fileInfo.location.bucket": "test.dev.steeleye.co",
                "metadata.source.fileInfo.location.key": "folder/test.eml",
                "metadata.threadId": "4bede774-5625-11ee-8491-000d3ab0c4bd",
                "roomId": "4bede774-5625-11ee-8491-000d3ab0c4bd",
                "sourceKey": "s3://test.dev.steeleye.co/folder/batch.ndjson",
                "timestamps.created": "2023-09-25T06:16:48.347000Z",
                "timestamps.timestampStart": "2023-09-25T06:16:48.347000Z",
            },
            {
                "eventQualifier": "System Event: Bruine, S.S. de (Sietske) joined the chat",
                "eventType": ChatEventType.ENTERED,
                "identifiers.fromId": "<EMAIL>",
                "identifiers.fromIdAddlInfo": {
                    "raw": "<EMAIL>",
                    "countryCode": None,
                },
                "identifiers.allIds": ["<EMAIL>"],
                "metadata.source.client": "MS Teams Chat",
                "metadata.source.fileInfo.location.bucket": "test.dev.steeleye.co",
                "metadata.source.fileInfo.location.key": "folder/test.eml",
                "metadata.threadId": "4bede774-5625-11ee-8491-000d3ab0c4bd",
                "roomId": "4bede774-5625-11ee-8491-000d3ab0c4bd",
                "sourceKey": "s3://test.dev.steeleye.co/folder/batch.ndjson",
                "timestamps.created": "2023-09-25T06:16:48.347000Z",
                "timestamps.timestampStart": "2023-09-25T06:16:48.347000Z",
            },
        ]
    )


@pytest.fixture()
def message_mappings_expected_output() -> pd.DataFrame:
    return pd.DataFrame(
        [
            {
                "body.edits": None,
                "body.text": "Been thinking about you. Hope it went very well"
                " which would be deserved ✨",
                "body.type": "PLAIN",
                "metadata.messageId": "2825f246-b7c2-11ee-8497-000d3ab0c4bd1711561827326",
                "metadata.isEdited": False,
                "metadata.isDeleted": False,
                "metadata.source.client": "MS Teams Chat",
                "metadata.source.fileInfo.location.bucket": "test.dev.steeleye.co",
                "metadata.source.fileInfo.location.key": "folder/test.eml",
                "metadata.subThreadId": None,
                "metadata.threadId": "2825f246-b7c2-11ee-8497-000d3ab0c4bd",
                "attachments": None,
                "hasAttachment": False,
                "timestamps.created": "2024-03-27T17:50:27.327000Z",
                "timestamps.timestampStart": "2024-03-27T17:50:27.327000Z",
                "sourceKey": "s3://test.dev.steeleye.co/folder/batch.ndjson",
                "roomId": "2825f246-b7c2-11ee-8497-000d3ab0c4bd",
                "roomName": "Rauhala, A.M. (Anna), Hoogland, R. (René)",
                "identifiers.fromId": "<EMAIL>",
                "identifiers.fromIdAddlInfo": {"raw": "<EMAIL>", "countryCode": None},
                "identifiers.toIds": ["<EMAIL>"],
                "identifiers.toIdsAddlInfo": [{"raw": "<EMAIL>", "countryCode": None}],
                "identifiers.allIds": ["<EMAIL>", "<EMAIL>"],
                "identifiers.allDomains": ["ing.com"],
            },
            {
                "body.edits": None,
                "body.text": "Thank you ❤️",
                "body.type": "PLAIN",
                "metadata.messageId": "2825f246-b7c2-11ee-8497-000d3ab0c4bd1711572246090",
                "metadata.isEdited": False,
                "metadata.isDeleted": False,
                "metadata.source.client": "MS Teams Chat",
                "metadata.source.fileInfo.location.bucket": "test.dev.steeleye.co",
                "metadata.source.fileInfo.location.key": "folder/test.eml",
                "metadata.subThreadId": None,
                "metadata.threadId": "2825f246-b7c2-11ee-8497-000d3ab0c4bd",
                "attachments": None,
                "hasAttachment": False,
                "timestamps.created": "2024-03-27T20:44:06.090000Z",
                "timestamps.timestampStart": "2024-03-27T20:44:06.090000Z",
                "sourceKey": "s3://test.dev.steeleye.co/folder/batch.ndjson",
                "roomId": "2825f246-b7c2-11ee-8497-000d3ab0c4bd",
                "roomName": "Rauhala, A.M. (Anna), Hoogland, R. (René)",
                "identifiers.fromId": "<EMAIL>",
                "identifiers.fromIdAddlInfo": {"raw": "<EMAIL>", "countryCode": None},
                "identifiers.toIds": ["<EMAIL>"],
                "identifiers.toIdsAddlInfo": [
                    {"raw": "<EMAIL>", "countryCode": None}
                ],
                "identifiers.allIds": ["<EMAIL>", "<EMAIL>"],
                "identifiers.allDomains": ["ing.com"],
            },
            {
                "body.edits": [
                    {
                        "editedBy": "<EMAIL>",
                        "text": "I think it went well.",
                        "timestampEdited": "2024-03-27T20:44:14.890000Z",
                        "type": "PLAIN",
                    }
                ],
                "body.text": "I think it went well.\n(heart, Hoogland, R. (René))",
                "body.type": "PLAIN",
                "metadata.messageId": "2825f246-b7c2-11ee-8497-000d3ab0c4bd1711572254890",
                "metadata.isEdited": True,
                "metadata.isDeleted": False,
                "metadata.source.client": "MS Teams Chat",
                "metadata.source.fileInfo.location.bucket": "test.dev.steeleye.co",
                "metadata.source.fileInfo.location.key": "folder/test.eml",
                "metadata.subThreadId": None,
                "metadata.threadId": "2825f246-b7c2-11ee-8497-000d3ab0c4bd",
                "attachments": None,
                "hasAttachment": False,
                "timestamps.created": "2024-03-27T20:44:14.890000Z",
                "timestamps.timestampStart": "2024-03-27T20:59:52.897000Z",
                "sourceKey": "s3://test.dev.steeleye.co/folder/batch.ndjson",
                "roomId": "2825f246-b7c2-11ee-8497-000d3ab0c4bd",
                "roomName": "Rauhala, A.M. (Anna), Hoogland, R. (René)",
                "identifiers.fromId": "<EMAIL>",
                "identifiers.fromIdAddlInfo": {"raw": "<EMAIL>", "countryCode": None},
                "identifiers.toIds": ["<EMAIL>"],
                "identifiers.toIdsAddlInfo": [
                    {"raw": "<EMAIL>", "countryCode": None}
                ],
                "identifiers.allIds": ["<EMAIL>", "<EMAIL>"],
                "identifiers.allDomains": ["ing.com"],
            },
            {
                "body.edits": [
                    {
                        "editedBy": "<EMAIL>",
                        "text": "Hope you will manage with your workload. Let’s try"
                        " to catch properly soon!",
                        "timestampEdited": "2024-03-27T20:45:21.310000Z",
                        "type": "PLAIN",
                    },
                    {
                        "editedBy": "<EMAIL>",
                        "text": "Hope you will manage with your workload. Let’s try"
                        " to catch properly soon!\n(heart, Hoogland, R. (René))",
                        "timestampEdited": "2024-03-27T20:59:49.073000Z",
                        "type": "PLAIN",
                    },
                ],
                "body.text": "Hope you will manage with your workload."
                " Let’s try to catch properly soon!",
                "body.type": "PLAIN",
                "metadata.messageId": "2825f246-b7c2-11ee-8497-000d3ab0c4bd1711572321310",
                "metadata.isEdited": True,
                "metadata.isDeleted": False,
                "metadata.source.client": "MS Teams Chat",
                "metadata.source.fileInfo.location.bucket": "test.dev.steeleye.co",
                "metadata.source.fileInfo.location.key": "folder/test.eml",
                "metadata.subThreadId": None,
                "metadata.threadId": "2825f246-b7c2-11ee-8497-000d3ab0c4bd",
                "attachments": None,
                "hasAttachment": False,
                "timestamps.created": "2024-03-27T20:45:21.310000Z",
                "timestamps.timestampStart": "2024-03-27T20:59:58.820000Z",
                "sourceKey": "s3://test.dev.steeleye.co/folder/batch.ndjson",
                "roomId": "2825f246-b7c2-11ee-8497-000d3ab0c4bd",
                "roomName": "Rauhala, A.M. (Anna), Hoogland, R. (René)",
                "identifiers.fromId": "<EMAIL>",
                "identifiers.fromIdAddlInfo": {"raw": "<EMAIL>", "countryCode": None},
                "identifiers.toIds": ["<EMAIL>"],
                "identifiers.toIdsAddlInfo": [
                    {"raw": "<EMAIL>", "countryCode": None}
                ],
                "identifiers.allIds": ["<EMAIL>", "<EMAIL>"],
                "identifiers.allDomains": ["ing.com"],
            },
            {
                "body.edits": None,
                "body.text": "Definitely!",
                "body.type": "PLAIN",
                "metadata.messageId": "2825f246-b7c2-11ee-8497-000d3ab0c4bd1711573202731",
                "metadata.isEdited": False,
                "metadata.isDeleted": False,
                "metadata.source.client": "MS Teams Chat",
                "metadata.source.fileInfo.location.bucket": "test.dev.steeleye.co",
                "metadata.source.fileInfo.location.key": "folder/test.eml",
                "metadata.subThreadId": None,
                "metadata.threadId": "2825f246-b7c2-11ee-8497-000d3ab0c4bd",
                "attachments": None,
                "hasAttachment": False,
                "timestamps.created": "2024-03-27T21:00:02.733000Z",
                "timestamps.timestampStart": "2024-03-27T21:00:02.733000Z",
                "sourceKey": "s3://test.dev.steeleye.co/folder/batch.ndjson",
                "roomId": "2825f246-b7c2-11ee-8497-000d3ab0c4bd",
                "roomName": "Rauhala, A.M. (Anna), Hoogland, R. (René)",
                "identifiers.fromId": "<EMAIL>",
                "identifiers.fromIdAddlInfo": {"raw": "<EMAIL>", "countryCode": None},
                "identifiers.toIds": ["<EMAIL>"],
                "identifiers.toIdsAddlInfo": [{"raw": "<EMAIL>", "countryCode": None}],
                "identifiers.allIds": ["<EMAIL>", "<EMAIL>"],
                "identifiers.allDomains": ["ing.com"],
            },
        ]
    )
