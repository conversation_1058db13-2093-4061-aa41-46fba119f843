import datetime
import pandas as pd
import pytest
from addict import addict
from aries_io_event.io_param import IOParamFieldSet
from aries_io_event.task import TaskFieldSet
from aries_io_event.workflow import WorkflowFieldSet
from aries_se_comms_tasks.transcription.static import TranscriptionFields
from aries_task_link.models import AriesTaskInput
from mock.mock import call
from pathlib import Path
from se_io_utils.json_utils import read_json
from typing import Dict, List

CURRENT_PATH = Path(__file__).parent
DATA_PATH = CURRENT_PATH.joinpath("data")
EXPECTED_RESULTS_PATH = DATA_PATH.joinpath("expected_results")
JSON_TRANSCRIPT_1_PATH = EXPECTED_RESULTS_PATH.joinpath("json_transcript_1.json")
JSON_TRANSCRIPT_2_PATH = EXPECTED_RESULTS_PATH.joinpath("json_transcript_2.json")

JSON_TRANSCRIPT_NO_TRANSLATION_PATH = EXPECTED_RESULTS_PATH.joinpath("json_no_translation.json")


@pytest.fixture()
def sample_iv_config() -> dict:
    return {
        "languages": ["en-001", "fr-001"],
        "diarization_enabled": True,
        "sentiment_processing_enabled": True,
        "summarization_enabled": True,
        "treat_all_files_as_single_channel": True,
        "asr_min_segment": 12,
    }


@pytest.fixture()
def translation_config() -> addict.Dict:
    return addict.Dict(
        {
            "id": "7888599a-d886-451d-a7f9-7137d4640929",
            "tenantId": 7,
            "translationEnabled": True,
            "translationTargetLanguage": "GERMAN",
            "createdDateTime": "2024-02-23T07:40:26.643310",
            "translationSourceLanguage": None,
            "workflowId": 73,
            "translationProvider": "AWS",
            "updatedDateTime": "2024-02-23T07:40:26.646043",
        }
    )


@pytest.fixture()
def iv_config_same_language_translation() -> dict:
    return {
        "languages": ["en-001", "es-001"],
        "diarization_enabled": True,
        "sentiment_processing_enabled": True,
        "summarization_enabled": True,
        "treat_all_files_as_single_channel": True,
        "asr_min_segment": 12,
    }


@pytest.fixture()
def translation_config_same_language_translation() -> addict.Dict:
    return addict.Dict(
        {
            "id": "7888599a-d886-451d-a7f9-7137d4640929",
            "tenantId": 7,
            "translationEnabled": True,
            "translationTargetLanguage": "SPANISH",
            "createdDateTime": "2024-02-23T07:40:26.643310",
            "translationSourceLanguage": None,
            "workflowId": 73,
            "translationProvider": "AWS",
            "updatedDateTime": "2024-02-23T07:40:26.646043",
        }
    )


@pytest.fixture
def source_frame_iv_submit_transcription_jobs() -> pd.DataFrame:
    data = {
        TranscriptionFields.DURATION: ["01:23:45", "00:10:21", "00:00:45", "00:00:45"],
        TranscriptionFields.ATTACHMENT_BUCKET: [
            "test.dev.steeleye.co",
            "test.dev.steeleye.co",
            "test.dev.steeleye.co",
            "test.dev.steeleye.co",
        ],
        TranscriptionFields.ATTACHMENT_KEY: [
            "flows/comms/test/file1.wav",
            "flows/comms/test/file2.wav",
            "flows/comms/test/file3.wav",
            "flows/comms/test/file4.wav",
        ],
        "&id": ["id1", "id2", "id3", "id3"],
        "&hash": ["hash1", "hash2", "hash3", "hash3"],
        "&model": ["Call"] * 4,
        "random_column_1": [1, 2, 3, 4],
        "random_column_2": [1, 2, 3, 4],
    }
    return pd.DataFrame(data)


@pytest.fixture()
def expected_result_iv_submit_transcription_jobs() -> pd.DataFrame:
    return pd.DataFrame(
        [
            {
                "callDuration": "01:23:45",
                "voiceFile.fileInfo.location.bucket": "test.dev.steeleye.co",
                "voiceFile.fileInfo.location.key": "flows/comms/test/file1.wav",
                "&id": "id1",
                "&hash": "hash1",
                "file_path": "s3://test.dev.steeleye.co/flows/comms/test/file1.wav",
                "item_id": "123",
            },
            {
                "callDuration": "00:10:21",
                "voiceFile.fileInfo.location.bucket": "test.dev.steeleye.co",
                "voiceFile.fileInfo.location.key": "flows/comms/test/file2.wav",
                "&id": "id2",
                "&hash": "hash2",
                "file_path": "s3://test.dev.steeleye.co/flows/comms/test/file2.wav",
                "item_id": "123",
            },
            {
                "callDuration": "00:00:45",
                "voiceFile.fileInfo.location.bucket": "test.dev.steeleye.co",
                "voiceFile.fileInfo.location.key": "flows/comms/test/file3.wav",
                "&id": "id3",
                "&hash": "hash3",
                "file_path": "s3://test.dev.steeleye.co/flows/comms/test/file3.wav",
                "item_id": "123",
            },
        ]
    )


@pytest.fixture()
def aries_task_input_iv_get_transcription() -> AriesTaskInput:
    workflow = WorkflowFieldSet(
        trace_id="test2",
        name="iv_get_transcription",
        stack="dev-blue",
        tenant="pinafore",
        start_timestamp=datetime.datetime(2023, 7, 13, 13, 5, 7, 469485),
    )
    input_param = IOParamFieldSet(
        params=dict(
            file_uri="s3://test.dev.steeleye.co/aries/ingest/transcription/intelligent_voice/in_progress",
            dynamic_task=dict(
                name="iv_get_transcription_sub_workflow",
                task_reference_name="iv_get_transcription_sub_workflow_ref",
                type="SUB_WORKFLOW",
                input_parameters={
                    "common": "some_common_argument",
                },
            ),
        )
    )
    task = TaskFieldSet(name="iv_get_transcription", version="latest", success=False)
    return AriesTaskInput(workflow=workflow, input_param=input_param, task=task)


@pytest.fixture()
def asr_models_mock() -> Dict[str, List[dict]]:
    return {
        "languageModels": [
            {
                "backedUp": False,
                "createdBy": "IntelligentVoice",
                "description": "general",
                "id": 66,
                "languageCode": "en-001",
                "lexiconSize": 24,
                "modelFullName": "IntelligentVoice_en-001_16kHz_24_general_V1_NASRv5.1",
                "ocrLanguage": {"description": "English", "id": 1, "language": "en"},
                "sampleRate": "16kHz",
                "status": "enabled",
                "version": "V1_NASRv5.1",
            },
            {
                "backedUp": False,
                "createdBy": "IntelligentVoice",
                "description": "general",
                "id": 72,
                "languageCode": "fr-001",
                "lexiconSize": 24,
                "modelFullName": "IntelligentVoice_fr-001_16kHz_24_general_V1_NASRv5.1",
                "ocrLanguage": {"description": "French", "id": 2, "language": "fr"},
                "sampleRate": "16kHz",
                "status": "enabled",
                "version": "V1_NASRv5.1",
            },
            {
                "backedUp": False,
                "createdBy": "IntelligentVoice",
                "description": "general",
                "id": 76,
                "languageCode": "de-001",
                "lexiconSize": 24,
                "modelFullName": "IntelligentVoice_de-001_16kHz_24_general_V1_NASRv5.1",
                "sampleRate": "16kHz",
                "status": "enabled",
                "version": "V1_NASRv5.1",
            },
            {
                "backedUp": False,
                "createdBy": "IntelligentVoice",
                "description": "general",
                "id": 77,
                "languageCode": "es-001",
                "lexiconSize": 24,
                "modelFullName": "IntelligentVoice_es-001_16kHz_24_general_V1_NASRv5.1",
                "ocrLanguage": {"description": "Spanish", "id": 3, "language": "es"},
                "sampleRate": "16kHz",
                "status": "enabled",
                "version": "V1_NASRv5.1",
            },
            {
                "backedUp": False,
                "createdBy": "IntelligentVoice",
                "description": "en.es",
                "id": 63,
                "languageCode": "x-languageid",
                "lexiconSize": 2,
                "modelFullName": "IntelligentVoice_x-languageid_16kHz_2_en.es_V1.1_NASRv2",
                "sampleRate": "16kHz",
                "status": "enabled",
                "version": "V1.1_NASRv2",
            },
            {
                "backedUp": False,
                "createdBy": "IntelligentVoice",
                "description": "en.fr",
                "id": 60,
                "languageCode": "x-languageid",
                "lexiconSize": 2,
                "modelFullName": "IntelligentVoice_x-languageid_16kHz_2_en.fr_V1.1_NASRv2",
                "sampleRate": "16kHz",
                "status": "enabled",
                "version": "V1.1_NASRv2",
            },
            {
                "backedUp": False,
                "createdBy": "IntelligentVoice",
                "description": "en.de",
                "id": 55,
                "languageCode": "x-languageid",
                "lexiconSize": 2,
                "modelFullName": "IntelligentVoice_x-languageid_16kHz_2_en.de_V1.1_NASRv2",
                "sampleRate": "16kHz",
                "status": "enabled",
                "version": "V1.1_NASRv2",
            },
            {
                "backedUp": False,
                "createdBy": "IntelligentVoice",
                "description": "trader",
                "id": 78,
                "languageCode": "en-001",
                "lexiconSize": 24,
                "modelFullName": "IntelligentVoice_en-001_16kHz_24_trader_V1_NASRv5.1",
                "ocrLanguage": {"description": "English", "id": 1, "language": "en"},
                "sampleRate": "16kHz",
                "status": "enabled",
                "version": "V1_NASRv5.1",
            },
        ]
    }


@pytest.fixture()
def triton_models_mock() -> List[dict]:
    return [
        {
            "description": "Multilingual-cased",
            "fullModelVersion": "Sentiment_v4.0_Multilingual-cased",
            "id": 7,
            "modelName": "Sentiment",
            "modelStatus": "enabled",
            "sentimentModelOptions": {
                "bertTokenizerPath": "/usr/app/src/sentiment_worker/multi_cased_L-12_H-768_A-12/",
                "displayName": "bert_multilingual_sentiment_v4.onnx",
                "doLowerCase": False,
                "inferUrl": "bert_multilingual_sentiment_v4_onnx",
                "maxDeconvolutionChunkLength": 120,
                "modelType": 4,
                "sentimentInferenceInputDimension": 250,
                "sentimentInferenceMaxBatchSize": 8,
                "sentimentPreTokenisedLength": 200,
            },
            "version": 4.0,
        },
        {
            "description": "XLM-Roberta-Multilingual",
            "fullModelVersion": "Sentiment_v5.0_XLM-Roberta-Multilingual",
            "id": 21,
            "modelName": "Sentiment",
            "modelStatus": "enabled",
            "sentimentModelOptions": {
                "bertTokenizerPath": "/usr/app/src/sentiment_worker/autotokenizer_sentiment_v5.0/",
                "displayName": "xlm_roberta_multilingual_sentiment_v5.0.onnx",
                "doLowerCase": False,
                "inferUrl": "sentiment_v5.0",
                "maxDeconvolutionChunkLength": 120,
                "modelType": 5,
                "sentimentInferenceInputDimension": 250,
                "sentimentInferenceMaxBatchSize": 8,
                "sentimentPreTokenisedLength": 200,
            },
            "version": 5.0,
        },
    ]


@pytest.fixture()
def translate_side_effect() -> List[str]:
    return [
        "Bonjour! Je voudrais ouvrir un compte. Très bien. Haben Sie eine Identitätskarte?"
        " Ich habe einen deutschen Reisepass. Ta marche. Avez -vous donc un titre de séjour?"
        " Ja, ich habe es mitgebracht. Gut. Endlich, hast du etwas mit deiner Adresse?"
        " Eine Rechnung zum Beispiel? Oh, ich glaube nicht. Ist es erforderlich?"
        " Ja, kannst du morgen zurückkehren? Gegen 14 Uhr'",
        "oder so? Ja, dann komme ich morgen wieder. Hab einen schönen Tag. Du auch.",
        "Ich möchte kaufen",
        "Ich habe diese tolle Wohnung neben dem Park gesehen. Aber ich weiß nicht,"
        " ob das Haus noch verfügbar ist.",
    ]


@pytest.fixture()
def expected_result_iv_get_transcription_results() -> pd.DataFrame:
    return pd.DataFrame(
        data=[
            {
                "marker_file_path": "aries/ingest/transcription/intelligent_voice/"
                "in_progress/test_iv_get_1/1001775.json",
                "&id": "14154836357_442070605120_20220114_151727_I:+14154836357:['+442070605120']:"
                "2022-01-14T15:17:27Z:2022-01-14T15:18:40Z",
                "&hash": "hash1",
                "&model": "Call",
                "processingStartTime": "2023-08-24T12:17:24.000Z",
                "processingEndTime": "2023-08-24T12:50:27.000Z",
                "transcriptItemId": "1001775",
                "transcriptSourceKey": "s3://test.dev.steeleye.co/aries/ingress/depository/"
                "transcription/intelligent_voice/iv_get_transcription/"
                "2023/07/13/test2/3c369003cb51d43fe04824ea1afba66b3b41b797b1afb30782b62908bc1a4238"
                "/6a0591b024ddb252c9cffd74c9612d70_french_english.json",
                "recordingSourceKey": "s3://test.dev.steeleye.co/aries/ingress/depository/"
                "focus_horizon_voice/attachments/recordings/14154836357_"
                "442070605120_20220114_151727_I.mp3",
                "modelIds": [60, 72, 66],
                "modelNames": [
                    "IntelligentVoice_x-languageid_16kHz_2_en.fr_V1.1_NASRv2",
                    "IntelligentVoice_fr-001_16kHz_24_general_V1_NASRv5.1",
                    "IntelligentVoice_en-001_16kHz_24_general_V1_NASRv5.1",
                ],
                "sourceAudioLanguages": ["FRENCH", "ENGLISH"],
                "transcriptionStatus": "Transcription completed successfully",
                "callDuration": "00:01:13",
                "targetLanguage": "GERMAN",
                "isEmptyTranscript": False,
            },
            {
                "marker_file_path": "aries/ingest/transcription/intelligent_voice/in_progress/"
                "test_iv_get_1/1001777.json",
                "&id": "14154834357_442070605120_20220114_151727_I:+14154834357:['+442070605120']"
                ":2022-01-14T15:17:27Z:2022-01-14T15:17:38Z",
                "&hash": "hash1",
                "&model": "Call",
                "processingStartTime": "2023-08-24T12:31:24.000Z",
                "processingEndTime": "2023-08-24T12:56:27.000Z",
                "transcriptItemId": "1001777",
                "transcriptSourceKey": "s3://test.dev.steeleye.co/aries/ingress/depository/"
                "transcription/intelligent_voice/iv_get_transcription/"
                "2023/07/13/test2/3c369003cb51d43fe04824ea1afba66b3b41b797b1afb30782b62908bc1a4238"
                "/edda0b603282f56eb3fd519a3d7c11b3_french_english.json",
                "recordingSourceKey": "s3://test.dev.steeleye.co/aries/ingress/depository/"
                "focus_horizon_voice/attachments/recordings/"
                "14154834357_442070605120_20220114_151727_I.mp3",
                "modelIds": [60, 72, 66],
                "modelNames": [
                    "IntelligentVoice_x-languageid_16kHz_2_en.fr_V1.1_NASRv2",
                    "IntelligentVoice_fr-001_16kHz_24_general_V1_NASRv5.1",
                    "IntelligentVoice_en-001_16kHz_24_general_V1_NASRv5.1",
                ],
                "sourceAudioLanguages": ["FRENCH", "ENGLISH"],
                "transcriptionStatus": "Transcription completed successfully",
                "callDuration": "00:00:11",
                "targetLanguage": "GERMAN",
                "isEmptyTranscript": False,
            },
        ],
        index=[0, 2],
    )


@pytest.fixture()
def expected_result_iv_get_transcription_results_same_translation_language():
    return pd.DataFrame(
        [
            {
                "marker_file_path": "aries/ingest/transcription/intelligent_voice"
                "/in_progress/test_iv_get_1/1000016.json",
                "&id": "447943358113_442080605120_20230717_074414_I:+447943358113:"
                "['+442080605120']:2023-07-17T07:44:14Z:2023-07-17T07:45:38Z",
                "&hash": "21db52fb3de6161f109fb647a83a97218a99144a7e8cda474fd80f1ab87d423a",
                "&model": "Call",
                "processingStartTime": "2023-09-08T14:12:04.000Z",
                "processingEndTime": "2023-09-08T14:16:06.000Z",
                "transcriptItemId": "1000016",
                "transcriptSourceKey": "s3://test.dev.steeleye.co/aries/ingress/depository/"
                "transcription/intelligent_voice/iv_get_transcription/"
                "2023/07/13/test2/3c369003cb51d43fe04824ea1afba66b3b41b797b1afb30782b62908bc1a4238"
                "/fc2cb1388734fe6b"
                "6c05da8fa93606b3_spanish.json",
                "recordingSourceKey": "s3://test.dev.steeleye.co/aries/ingress/depository/"
                "focus_horizon_voice/attachments/recordings/"
                "447943358113_442080605120_20230717_074414_I.mp3",
                "modelIds": [77],
                "modelNames": ["IntelligentVoice_es-001_16kHz_24_general_V1_NASRv5.1"],
                "sourceAudioLanguages": ["SPANISH"],
                "transcriptionStatus": "Transcription completed successfully",
                "callDuration": "00:01:24",
                "targetLanguage": "SPANISH",
                "isEmptyTranscript": False,
            }
        ]
    )


@pytest.fixture()
def expected_call_args_get_transcription_write_json():
    return [
        call(read_json(JSON_TRANSCRIPT_1_PATH.as_posix())),
        call(
            read_json(JSON_TRANSCRIPT_2_PATH.as_posix()),
        ),
    ]


@pytest.fixture()
def expected_call_args_get_transcription_write_json_same_translation_language():
    return [
        call(read_json(JSON_TRANSCRIPT_NO_TRANSLATION_PATH.as_posix())),
    ]


@pytest.fixture()
def source_frame_empty_transcripts_iv_create_batches() -> pd.DataFrame:
    return pd.DataFrame(
        [
            {
                "marker_file_path": "aries/ingest/test/1001080.json",
                "&id": "447943358113_442080605120_20230717_074414_I:+447943358113:"
                "['+442080605120']:2023-07-17T07:44:14Z:2023-07-17T07:45:38Z",
                "&hash": "21db52fb3de6161f109fb647a83a97218a99144a7e8cda474fd80f1ab87d423a",
                "&model": "Call",
                "processingStartTime": "2023-09-12T13:35:06.000Z",
                "processingEndTime": "2023-09-13T13:16:02.000Z",
                "transcriptItemId": "1001080",
                "transcriptSourceKey": "s3://ashwath.dev.steeleye.co/aries/ingress/"
                "depository/transcription/intelligent_voice/"
                "iv_get_transcription/2023/09/17/test_iv_get_1/"
                "6a48ec26614311ab0fad97fef3dbe3dd8b78297a09e5"
                "0b7e6eae7e48a4b7976c/ce4676b35d24aa7aec58c62cf677afbb_.json",
                "recordingSourceKey": "s3://ashwath.dev.steeleye.co/aries/ingress/depository/"
                "focus_horizon_voice/attachments/recordings/"
                "447943358113_442080605120_20230717_074414_I.mp3",
                "modelIds": [],
                "modelNames": [],
                "sourceAudioLanguages": [],
                "transcriptionStatus": "Transcription completed successfully",
                "callDuration": "00:00:01",
                "targetLanguage": "ENGLISH",
                "isEmptyTranscript": True,
            }
        ]
    )
