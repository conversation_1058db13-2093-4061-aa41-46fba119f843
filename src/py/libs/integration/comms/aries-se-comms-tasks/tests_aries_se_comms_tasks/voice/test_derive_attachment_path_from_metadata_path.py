import numpy as np
import os
import pandas as pd
import pytest
from aries_se_comms_tasks.voice.derive_attachment_path_from_metadata_path import (
    DeriveAttachmentPathFromMetadataPath,
    FailIfSourceDataInvalid,
    Params,
    run_derive_attachment_path_from_metadata_path,
)
from integration_wrapper.static import IntegrationAriesTaskVariables
from pathlib import Path
from shutil import rmtree

PATH = Path(__file__).parent
SERIALIZER_TMP_DIR = PATH.joinpath("serializer_tmp_dir")

SERIALIZER_TMP_DIR.mkdir(parents=True, exist_ok=True)

os.environ[IntegrationAriesTaskVariables.SERIALIZER_TMP_DIR] = SERIALIZER_TMP_DIR.as_posix()


@pytest.fixture(scope="session", autouse=True)
def cleanup(request):
    def _end():
        rmtree(SERIALIZER_TMP_DIR)

    request.addfinalizer(_end)


@pytest.fixture()
def empty_source_frame() -> pd.DataFrame:
    return pd.DataFrame(columns=["metadata_file_url", "realm"])


@pytest.fixture()
def batch_source_frame() -> pd.DataFrame:
    return pd.DataFrame(
        {
            "metadata_file_url": [
                "s3://dummy.dev.steeleye.co/stream/comms-voice-test/"
                "20210701_100144_441614497395_441615498344_I_49.json",
                "s3://dummy.dev.steeleye.co/stream/comms-voice-test/"
                "20210702_091932_447730761461_441615498342_I_12.json",
            ],
            "realm": ["dummy.dev.steeleye.co", "dummy.dev.steeleye.co"],
        }
    )


@pytest.fixture()
def batch_source_frame_uppercase_extension() -> pd.DataFrame:
    return pd.DataFrame(
        {
            "metadata_file_url": [
                "s3://dummy.dev.steeleye.co/stream/comms-voice-test/"
                "20210701_100144_441614497395_441615498344_I_49.JSON"
            ],
            "realm": ["dummy.dev.steeleye.co"],
        }
    )


@pytest.fixture()
def batch_source_frame_with_one_null_url() -> pd.DataFrame:
    return pd.DataFrame(
        {
            "metadata_file_url": [
                np.nan,
                "s3://dummy.dev.steeleye.co/stream/comms-voice-test/"
                "20210701_100144_441614497395_441615498344_I_49.json",
                "s3://dummy.dev.steeleye.co/stream/comms-voice-test/"
                "20210702_091932_447730761461_441615498342_I_12.json",
            ],
            "realm": [
                "dummy.dev.steeleye.co",
                "dummy.dev.steeleye.co",
                "dummy.dev.steeleye.co",
            ],
        }
    )


@pytest.fixture()
def attachment_within_metadata_name_batch_source_frame() -> pd.DataFrame:
    return pd.DataFrame(
        {
            "metadata_file_url": [
                "s3://dummy.dev.steeleye.co/comms-voice-test/6b1f5f00-89c3-4f9c-9d9b-193aa415d59f.wav.hsmetadata.xml",  # noqa: E501
                pd.NA,
                "s3://dummy.dev.steeleye.co/comms-voice-test/501f5f00-3812-47e0-861a-0215f1c960e3.wav.hsmetadata.xml",  # noqa: E501
            ],
        }
    )


@pytest.fixture()
def attachment_within_metadata_name_batch_source_frame_with_subfolder() -> pd.DataFrame:
    return pd.DataFrame(
        {
            "metadata_file_url": [
                "s3://dummy.dev.steeleye.co/comms-voice-test/6b1f5f00/6b1f5f00-89c3-4f9c-9d9b-193aa415d59f.wav.hsmetadata.xml",  # noqa E501
                pd.NA,
                "s3://dummy.dev.steeleye.co/comms-voice-test/501f5f00/501f5f00-3812-47e0-861a-0215f1c960e3.wav.hsmetadata.xml",  # noqa E501
            ],
        }
    )


@pytest.fixture()
def batch_source_frame_with_dates_and_metadata_suffix() -> pd.DataFrame:
    return pd.DataFrame(
        {
            "metadata_file_url": [
                "s3://dummy.dev.steeleye.co/stream/comms-voice-test/2022/02/26/20220226_100144_441614497395_441615498344_metadata.json",  # noqa: E501
                "s3://dummy.dev.steeleye.co/stream/comms-voice-test/2022/11/08/20221108_091932_447730761461_441615498342_metadata.json",  # noqa: E501
            ]
        }
    )


@pytest.fixture()
def expected_result_for_batch() -> pd.DataFrame:
    return pd.DataFrame(
        {
            "metadata_file_url": [
                "s3://dummy.dev.steeleye.co/stream/comms-voice-test/20210701_100144_441614497395_441615498344_I_49.json",  # noqa: E501
                "s3://dummy.dev.steeleye.co/stream/comms-voice-test/20210702_091932_447730761461_441615498342_I_12.json",  # noqa: E501
            ],
            "recording_file_url": [
                "s3://dummy.dev.steeleye.co/attachments/stream/comms-voice-test/20210701_100144_441614497395_441615498344_I_49.wav",  # noqa: E501
                "s3://dummy.dev.steeleye.co/attachments/stream/comms-voice-test/20210702_091932_447730761461_441615498342_I_12.wav",  # noqa: E501
            ],
        }
    )


@pytest.fixture()
def expected_result_for_batch_uppercase_extension() -> pd.DataFrame:
    return pd.DataFrame(
        {
            "metadata_file_url": [
                "s3://dummy.dev.steeleye.co/stream/comms-voice-test/20210701_100144_441614497395_441615498344_I_49.JSON"  # noqa: E501
            ],
            "recording_file_url": [
                "s3://dummy.dev.steeleye.co/attachments/stream/comms-voice-test/20210701_100144_441614497395_441615498344_I_49.WAV"  # noqa: E501
            ],
        }
    )


@pytest.fixture()
def expected_result_for_attachment_within_metadata_name_batch() -> pd.DataFrame:
    return pd.DataFrame(
        {
            "metadata_file_url": [
                "s3://dummy.dev.steeleye.co/comms-voice-test/6b1f5f00-89c3-4f9c-9d9b-193aa415d59f.wav.hsmetadata.xml",  # noqa: E501
                "s3://dummy.dev.steeleye.co/comms-voice-test/501f5f00-3812-47e0-861a-0215f1c960e3.wav.hsmetadata.xml",  # noqa: E501
            ],
            "recording_file_url": [
                "s3://dummy.dev.steeleye.co/attachments/comms-voice-test/6b1f5f00-89c3-4f9c-9d9b-193aa415d59f.wav",  # noqa: E501
                "s3://dummy.dev.steeleye.co/attachments/comms-voice-test/501f5f00-3812-47e0-861a-0215f1c960e3.wav",  # noqa: E501
            ],
        }
    )


@pytest.fixture()
def expected_result_for_attachment_within_metadata_name_batch_with_subfolder() -> pd.DataFrame:
    return pd.DataFrame(
        {
            "metadata_file_url": [
                "s3://dummy.dev.steeleye.co/comms-voice-test/6b1f5f00/6b1f5f00-89c3-4f9c-9d9b-193aa415d59f.wav.hsmetadata.xml",  # noqa E501
                "s3://dummy.dev.steeleye.co/comms-voice-test/501f5f00/501f5f00-3812-47e0-861a-0215f1c960e3.wav.hsmetadata.xml",  # noqa E501
            ],
            "recording_file_url": [
                "s3://dummy.dev.steeleye.co/attachments/comms-voice-test/6b1f5f00/6b1f5f00-89c3-4f9c-9d9b-193aa415d59f.wav",  # noqa E501
                "s3://dummy.dev.steeleye.co/attachments/comms-voice-test/501f5f00/501f5f00-3812-47e0-861a-0215f1c960e3.wav",  # noqa E501
            ],
        }
    )


@pytest.fixture()
def expected_result_for_batch_with_metadata_suffix() -> pd.DataFrame:
    return pd.DataFrame(
        {
            "metadata_file_url": [
                "s3://dummy.dev.steeleye.co/stream/comms-voice-test/2022/02/26/20220226_100144_441614497395_441615498344_metadata.json",  # noqa: E501
                "s3://dummy.dev.steeleye.co/stream/comms-voice-test/2022/11/08/20221108_091932_447730761461_441615498342_metadata.json",  # noqa: E501
            ],
            "recording_file_url": [
                "s3://dummy.dev.steeleye.co/attachments/comms-voice-test/2022/02/26/20220226_100144_441614497395_441615498344_media_audio_wav.wav",  # noqa: E501
                "s3://dummy.dev.steeleye.co/attachments/comms-voice-test/2022/11/08/20221108_091932_447730761461_441615498342_media_audio_wav.wav",  # noqa: E501
            ],
        }
    )


@pytest.fixture()
def params_fixture() -> Params:
    return Params(
        metadata_file_url_column="metadata_file_url",
        recording_file_path_prefix="attachments/stream/comms-voice-test",
        recording_file_extension=".wav",
        target_recording_file_url_column="recording_file_url",
    )


@pytest.fixture()
def attachment_within_metadata_name_params_fixture() -> Params:
    return Params(
        metadata_file_url_column="metadata_file_url",
        recording_file_path_prefix="attachments/comms-voice-test",
        recording_file_extension=".wav",
        target_recording_file_url_column="recording_file_url",
        recording_name_subset_of_metadata_name=True,
    )


@pytest.fixture()
def attachment_within_metadata_name_params_fixture_with_parts_to_extract() -> Params:
    return Params(
        metadata_file_url_column="metadata_file_url",
        recording_file_path_prefix="attachments/comms-voice-test",
        recording_file_extension=".wav",
        target_recording_file_url_column="recording_file_url",
        recording_name_subset_of_metadata_name=True,
        parts_from_metadata_url_to_use_in_recording_url=[-2, -1],
    )


@pytest.fixture()
def params_with_parts_to_extract_and_replace_fixture() -> Params:
    return Params(
        metadata_file_url_column="metadata_file_url",
        recording_file_path_prefix="attachments/comms-voice-test",
        recording_file_extension=".wav",
        target_recording_file_url_column="recording_file_url",
        parts_from_metadata_url_to_use_in_recording_url=[-4, -1],
        replace_substring_in_file_name={"metadata": "media_audio_wav"},
    )


class TestDeriveAttachmentPathFromMetadataPath:
    """Test Suite for DeriveAttachmentPathFromMetadataPath."""

    def test_empty_source_frame(self, empty_source_frame, params_fixture):
        """Test for when the source frame is empty."""
        with pytest.raises(FailIfSourceDataInvalid):
            run_derive_attachment_path_from_metadata_path(
                source_frame=empty_source_frame,
                params=params_fixture,
                skip_serializer=True,
            )

    def test_non_empty_source_frame(
        self,
        batch_source_frame,
        params_fixture,
        expected_result_for_batch,
    ):
        """Test for the case where the batch df is present and has no null
        values."""
        result = run_derive_attachment_path_from_metadata_path(
            source_frame=batch_source_frame,
            params=params_fixture,
            skip_serializer=True,
        )

        pd.testing.assert_frame_equal(left=result, right=expected_result_for_batch)

    def test_non_empty_source_frame_uppercase_extension(
        self,
        batch_source_frame_uppercase_extension,
        params_fixture,
        expected_result_for_batch_uppercase_extension,
    ):
        """Test for the case where the extension for uppercase extension."""
        result = run_derive_attachment_path_from_metadata_path(
            source_frame=batch_source_frame_uppercase_extension,
            params=params_fixture,
            skip_serializer=True,
        )

        pd.testing.assert_frame_equal(
            left=result, right=expected_result_for_batch_uppercase_extension
        )

    def test_non_empty_source_frame_with_one_null_file_url(
        self,
        batch_source_frame_with_one_null_url,
        params_fixture,
        expected_result_for_batch,
    ):
        """Test for the case where the batch df is present and has 1 null
        metadata file url value.

        Nulls are dropped, so the row with null metadata file url is not
        present in the output
        """
        result = run_derive_attachment_path_from_metadata_path(
            source_frame=batch_source_frame_with_one_null_url,
            params=params_fixture,
            skip_serializer=True,
        )

        pd.testing.assert_frame_equal(left=result, right=expected_result_for_batch)

    def test_non_empty_source_frame_with_attachment_name_subset_of_metadata_name(
        self,
        attachment_within_metadata_name_batch_source_frame,
        attachment_within_metadata_name_params_fixture,
        expected_result_for_attachment_within_metadata_name_batch,
    ):
        """Test for the case where the metadata and recording names are as per
        the foll.

        format:
        recording file name is entirely contained in the metadata file name
        """
        result = run_derive_attachment_path_from_metadata_path(
            source_frame=attachment_within_metadata_name_batch_source_frame,
            params=attachment_within_metadata_name_params_fixture,
            skip_serializer=True,
        )

        pd.testing.assert_frame_equal(
            left=result, right=expected_result_for_attachment_within_metadata_name_batch
        )

    def test_parts_from_metadata_url_to_use_in_recording_url(
        self,
        attachment_within_metadata_name_batch_source_frame_with_subfolder,
        attachment_within_metadata_name_params_fixture_with_parts_to_extract,
        expected_result_for_attachment_within_metadata_name_batch_with_subfolder,
    ):
        """Test for the case where the
        parts_from_metadata_url_to_use_in_recording_url is used to get the date
        from the metadata_file_url path into the recording_file_url path."""
        result = run_derive_attachment_path_from_metadata_path(
            source_frame=attachment_within_metadata_name_batch_source_frame_with_subfolder,
            params=attachment_within_metadata_name_params_fixture_with_parts_to_extract,
            skip_serializer=True,
        )

        pd.testing.assert_frame_equal(
            left=result,
            right=expected_result_for_attachment_within_metadata_name_batch_with_subfolder,
        )

    def test_replace_substring_in_file_name(
        self,
        batch_source_frame_with_dates_and_metadata_suffix,
        params_with_parts_to_extract_and_replace_fixture,
        expected_result_for_batch_with_metadata_suffix,
    ):
        """Test for the case where the
        parts_from_metadata_url_to_use_in_recording_url is used to get the date
        from the metadata_file_url path into the recording_file_url path."""
        result = run_derive_attachment_path_from_metadata_path(
            source_frame=batch_source_frame_with_dates_and_metadata_suffix,
            params=params_with_parts_to_extract_and_replace_fixture,
            skip_serializer=True,
        )

        pd.testing.assert_frame_equal(
            left=result, right=expected_result_for_batch_with_metadata_suffix
        )

    def test_it_can_derive_path_from_s3_uri(self):
        result = run_derive_attachment_path_from_metadata_path(
            source_frame=pd.DataFrame(
                {
                    "metadata_file_url": [
                        "s3://dummy.dev.steeleye.co/comms-voice-test/6b1f5f00/6b1f5f00-89c3-4f9c-9d9b-193aa415d59f.xml",  # noqa E501
                        "s3://dummy.dev.steeleye.co/comms-voice-test/501f5f00/501f5f00-3812-47e0-861a-0215f1c960e3.xml",  # noqa E501
                    ],
                }
            ),
            params=Params(
                metadata_file_url_column="metadata_file_url",
                recording_file_extension=".mp4",
                target_recording_file_url_column="recording_file_url",
                derive_prefix_from_metadata_url=True,
            ),
            skip_serializer=True,
        )

        pd.testing.assert_frame_equal(
            left=result,
            right=pd.DataFrame(
                {
                    "metadata_file_url": [
                        "s3://dummy.dev.steeleye.co/comms-voice-test/6b1f5f00/6b1f5f00-89c3-4f9c-9d9b-193aa415d59f.xml",  # noqa E501
                        "s3://dummy.dev.steeleye.co/comms-voice-test/501f5f00/501f5f00-3812-47e0-861a-0215f1c960e3.xml",  # noqa E501
                    ],
                    "recording_file_url": [
                        "s3://dummy.dev.steeleye.co/comms-voice-test/6b1f5f00/6b1f5f00-89c3-4f9c-9d9b-193aa415d59f.mp4",  # noqa E501
                        "s3://dummy.dev.steeleye.co/comms-voice-test/501f5f00/501f5f00-3812-47e0-861a-0215f1c960e3.mp4",  # noqa E501
                    ],
                }
            ),
        )

    def test_it_can_get_path_from_s3_uri(self):
        s3_uri: str = "s3://test.dev.steeleye.com/aries/onboarding/nice_voice/file.csv"

        assert (
            DeriveAttachmentPathFromMetadataPath.get_path_from_s3_uri(uri=s3_uri)
            == "aries/onboarding/nice_voice"
        )

    def test_it_return_original_value_if_not_a_valid_s3_uri(self):
        s3_uri: str = "test.dev.steeleye.com/aries/onboarding/nice_voice/file.csv"

        assert DeriveAttachmentPathFromMetadataPath.get_path_from_s3_uri(uri=s3_uri) == s3_uri
