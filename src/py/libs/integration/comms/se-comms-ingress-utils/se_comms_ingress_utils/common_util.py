import backoff
import filetype  # type: ignore
import httpx
import logging
import nanoid
from addict import addict
from aries_config_api_compatible_client.tenant_workflow import CompatibleTenantWorkflowAPIClient
from aries_io_event.io_param import IOParamFieldSet
from aries_io_event.model import IOEvent
from aries_io_event.task import TaskFieldSet
from aries_io_event.workflow import WorkflowFieldSet
from aries_se_api_client.client import AriesApiClient
from aries_task_link.models import AriesTaskInput
from data_platform_config_api_client.tenant_workflow import TenantWorkflowAPI
from datetime import datetime, time, timedelta
from pydantic import BaseModel, Field
from se_comms_ingress_utils.custom_exceptions import (
    EitherStartOrEndDateNotExists,
    FromDateGreaterThanToDate,
    ToDateGreaterThanYesterday,
)
from typing import Any, Dict, Optional, Tuple, Union

logger = logging.getLogger("common_util")


class PollIntervalAndBackfill(BaseModel):
    from_date: datetime = Field()
    to_date: datetime = Field()
    backfill: bool = Field()


def ingestion_trigger_event(
    target_path: str,
    workflow_name: str,
    tenant_name: str,
    stack_name: str,
    task_name: str,
    streamed: bool,
    version: str,
    aries_task_to_domain_dict: dict,
) -> IOEvent:
    """Create IOEvent for producing to kafka
    Parameters:
        target_path (str): The target path.
        workflow_name (str): The name of the workflow.
        tenant_name (str): The name of the tenant.
        stack_name (str): The name of the stack.
        task_name (str): The name of the task.
        streamed (bool): A flag indicating if the data is streamed.
        version (str): The version of the task.
        aries_task_to_domain_dict(dict): The task to domain

    Returns:
        IOEvent: The generated IOEvent for Kafka.
    """
    workflow = WorkflowFieldSet(
        trace_id=nanoid.generate(),
        name=workflow_name,
        stack=stack_name,
        tenant=tenant_name,
        start_timestamp=datetime.utcnow(),
    )
    io_param = IOParamFieldSet(
        params=dict(
            file_uri=target_path,
            streamed=streamed,
            aries_task_to_domain=aries_task_to_domain_dict,
        )
    )
    task = TaskFieldSet(name=task_name, version=version, success=True)
    return IOEvent(workflow=workflow, io_param=io_param, task=task)


def get_from_to_dates(event: Dict[str, str]) -> Tuple[Optional[str], Optional[str]]:
    """
    Gets from date and to date between which the poller can run.
    This method although common, is specific to pollers like Eikon/EE Mobile where we
    need to get t-1 day's data.
    Parameters:
        event (Dict[str, str]): The event containing 'from_date' and 'to_date' parameters.

    Returns:
        Tuple[Optional[str], Optional[str]]: A tuple containing the 'from' date and 'to' date.
    Raises:
        FromDateGreaterThanToDate: If 'from_date is greater than to_date.
        ToDateGreaterThanYesterday: If 'to_date is greater than yesterday's date.
    """
    try:
        yesterday = datetime.strftime((datetime.utcnow() - timedelta(days=1)), "%Y-%m-%d")
        if "from_date" in event and "to_date" in event:
            poll_from_param = event.get("from_date")
            poll_to_param = event.get("to_date")

            if poll_from_param and poll_to_param:
                if poll_from_param > poll_to_param:
                    raise FromDateGreaterThanToDate()

                if poll_to_param > yesterday:
                    raise ToDateGreaterThanYesterday()

        else:
            poll_from_param = yesterday
            poll_to_param = yesterday
        return poll_from_param, poll_to_param
    except FromDateGreaterThanToDate as exc:
        logger.exception(
            f"poll from date:{poll_from_param} can't be greater"
            f" than poll to date:{poll_to_param} - {exc}"
        )
        raise FromDateGreaterThanToDate from exc
    except ToDateGreaterThanYesterday as exc:
        logger.exception(
            f"poll to date:{poll_to_param} can't be more than yesterday's date - {exc}"
        )
        raise ToDateGreaterThanYesterday from exc


def has_date_range_in_event(aries_task_input: AriesTaskInput) -> bool:
    if (
        "from_date" in aries_task_input.input_param.params
        and "to_date" in aries_task_input.input_param.params
    ):
        return True
    return False


def guess_filetype(file: bytes, filename: Union[str, None] = None) -> Any:
    """returns the filetype first from the file content and from filename if
    file fails.

    Parameters:
        file (bytes): content of the file
        filename (Optional[Union[str, None]]): The name of the file
    Returns:
        str or None
    """
    kind = filetype.guess(file)
    if not kind:
        if filename:
            tmp = filename.split(".")
            if len(tmp) > 1:
                return tmp[-1]
        return None

    return kind.extension


@backoff.on_exception(backoff.expo, httpx.RequestError, max_tries=3)
def update_poller_last_execution_time(
    last_execution_time,
    tenant_name: str,
    workflow_name: str,
    flow_env_vars,
) -> addict.Dict:
    """update poller last exec time.

    Parameters:
        last_execution_time
        tenant_name (str): The name of the tenant.
        workflow_name (str): The name of the workflow.
        flow_env_vars (Dict[str, Any]): Environment variables related to the flow.
    """
    tenant_data_source_api = TenantWorkflowAPI(
        AriesApiClient(host=flow_env_vars.data_platform_config_api_url)
    )
    return CompatibleTenantWorkflowAPIClient.update(
        tenant_workflow_api=tenant_data_source_api,
        json_body={"workflow_last_executed": last_execution_time},
        tenant_name=tenant_name,
        workflow_name=workflow_name,
    )


def get_start_end_date(
    start_date: Union[str, datetime], end_date: Union[str, datetime]
) -> tuple[datetime, datetime]:
    """Method used for validation of start and end date."""
    if (start_date and not end_date) or (end_date and not start_date):
        logger.error("Either start date or end date does not exist")
        raise EitherStartOrEndDateNotExists()
    if not isinstance(start_date, datetime):
        try:
            start_date = datetime.strptime(start_date, "%Y-%m-%d")
        except ValueError:
            try:
                start_date = datetime.strptime(start_date, "%Y-%m-%dT%H:%M:%SZ")  # type: ignore[arg-type]
            except ValueError:
                logger.error(f"Invalid start date format:{start_date}")
                raise
    if not isinstance(end_date, datetime):
        try:
            end_date = datetime.strptime(end_date, "%Y-%m-%d")
        except ValueError:
            try:
                end_date = datetime.strptime(end_date, "%Y-%m-%dT%H:%M:%SZ")  # type: ignore[arg-type]
            except ValueError:
                logger.error(f"Invalid end date format:{end_date}")
                raise
    if start_date > end_date:
        logger.error(f"start date:{start_date} can't be greater than end date:{end_date}")
        raise FromDateGreaterThanToDate()
    logger.info(f"Back-filling from Start date: {start_date}, End date: {end_date}")
    return start_date, end_date


def get_poll_interval_and_backfill(
    aries_task_input: AriesTaskInput, workflow_last_executed: Optional[str], timestamp_now: datetime
) -> PollIntervalAndBackfill:
    """
    Determines the polling interval and whether backfilling is required based on task input.
    This method calculates the `from_date` and `to_date` used for polling data. If the
    `aries_task_input` contains an explicit date range (`from_date` and `to_date`), that range
    is used and the method flags it as a backfill. Otherwise, it infers the range based on the
    last known execution time or defaults to the previous day.
    Parameters:
        aries_task_input:  contain a `params` dictionary with optional `from_date` and `to_date`.
        workflow_last_executed: The timestamp (in ISO format) of the last execution
        timestamp_now: The current timestamp
    Returns:
        PollIntervalAndBackfill: A dataclass containing `from_date`, `to_date`, and `backfill`.
    Notes:
        - This method assumes both `from_date` and `to_date` must be present in `params`
          to consider it a valid backfill request.
    """

    if has_date_range_in_event(aries_task_input):
        from_date, to_date = get_start_end_date(
            start_date=aries_task_input.input_param.params["from_date"],
            end_date=aries_task_input.input_param.params["to_date"],
        )
        backfill = True
    else:
        if workflow_last_executed:
            # get the last execution time from the db
            from_date = datetime.strptime(workflow_last_executed, "%Y-%m-%dT%H:%M:%S")
        else:
            from_date = datetime.combine(timestamp_now - timedelta(days=1), time.min)
        to_date = timestamp_now
        backfill = False
    return PollIntervalAndBackfill(from_date=from_date, to_date=to_date, backfill=backfill)


def get_poll_from_date_and_backfill(
    aries_task_input: AriesTaskInput, workflow_last_executed: Optional[str], timestamp_now: datetime
) -> PollIntervalAndBackfill:
    """
    Determines the start date for polling and whether backfilling is required.
    This method is similar to `get_poll_interval_and_backfill`, but supports specifically just
    `from_date`. It attempts to parse `from_date` from a string in multiple formats
    and uses `timestamp_now` as the `to_date`. If no `from_date` is provided, the logic falls back
    to the last execution time or defaults to the previous day.

    Parameters:
        aries_task_input:  contain a `params` dictionary with optional `from_date` (NO `to_date`)
        workflow_last_executed: The timestamp (in ISO format) of the last execution
        timestamp_now: The current timestamp
    Returns:
        PollIntervalAndBackfill: A dataclass containing `from_date`, `to_date`, and `backfill`.
    Notes:
        - Use this method when only a `from_date` is expected or supported by the task input schema.
        - Supported input formats for `from_date` include:
            - ISO date: "YYYY-MM-DD"
            - ISO datetime with Zulu: "YYYY-MM-DDTHH:MM:SSZ"
    """

    if "from_date" in aries_task_input.input_param.params:
        input_from_date = aries_task_input.input_param.params["from_date"]
        if not isinstance(input_from_date, datetime):
            try:
                from_date = datetime.strptime(input_from_date, "%Y-%m-%d")
            except ValueError:
                try:
                    from_date = datetime.strptime(input_from_date, "%Y-%m-%dT%H:%M:%SZ")  # type: ignore[arg-type]
                except ValueError:
                    logger.error(f"Invalid start date format:{input_from_date}")
                    raise
        else:
            from_date = input_from_date

        backfill = True
    else:
        if workflow_last_executed:
            # get the last execution time from the db
            from_date = datetime.strptime(workflow_last_executed, "%Y-%m-%dT%H:%M:%S")
        else:
            from_date = datetime.combine(timestamp_now - timedelta(days=1), time.min)
        backfill = False

    to_date = timestamp_now
    return PollIntervalAndBackfill(from_date=from_date, to_date=to_date, backfill=backfill)
