from se_comms_graph_api_utils.calendar import CalendarAP<PERSON>
from se_comms_graph_api_utils.group import GroupAPI
from se_comms_graph_api_utils.user import UserAP<PERSON>


def test_get_user_details(httpx_mock, graph_api_client):
    expected_value = {"value": [{"id": str(i), "userPrincipalName": str(i)} for i in range(4)]}
    httpx_mock.add_response(
        method="POST",
        url="https://login.microsoftonline.com/ms_tenant_id/oauth2/v2.0/token",
        status_code=200,
        json={"access_token": "access_token"},
        match_content=b"client_id=client_id&client_secret=client_secret"
        b"&grant_type=client_credentials&scope=https%3A%2F%2Fgraph.microsoft.com%2F.default",
    )
    httpx_mock.add_response(
        method="GET",
        url="https://graph.microsoft.com/v1.0/users?$select=id,userPrincipalName&$top=999",
        status_code=200,
        json=expected_value,
    )

    api_client = UserAPI(graph_api_client=graph_api_client)
    entries = list(api_client.get_user_details())
    assert entries == [("0", "0"), ("1", "1"), ("2", "2"), ("3", "3")]


def test_get_team_details(httpx_mock, graph_api_client):
    expected_value = {
        "value": [
            dict(id="1", displayName="displayName1", resourceProvisioningOptions=["Team"]),
            dict(
                id="2", displayName="displayName2", resourceProvisioningOptions=["Team", "Channel"]
            ),
            dict(id="3", displayName="displayName", resourceProvisioningOptions=[]),
            dict(id="4"),
        ]
    }
    httpx_mock.add_response(
        method="POST",
        url="https://login.microsoftonline.com/ms_tenant_id/oauth2/v2.0/token",
        status_code=200,
        json={"access_token": "access_token"},
        match_content=b"client_id=client_id&client_secret=client_secret"
        b"&grant_type=client_credentials&scope=https%3A%2F%2Fgraph.microsoft.com%2F.default",
    )
    httpx_mock.add_response(
        method="GET",
        url="https://graph.microsoft.com/v1.0/groups?$select=id,display"
        "Name,resourceProvisioningOptions&$top=999",
        status_code=200,
        json=expected_value,
    )

    api_client = GroupAPI(graph_api_client=graph_api_client)
    entries = list(api_client.get_team_details())
    assert entries == [("1", "displayName1"), ("2", "displayName2")]


def test_get_user_ids(httpx_mock, graph_api_client):
    expected_value = {"value": [{"id": str(i)} for i in range(7)]}
    httpx_mock.add_response(
        method="POST",
        url="https://login.microsoftonline.com/ms_tenant_id/oauth2/v2.0/token",
        status_code=200,
        json={"access_token": "access_token"},
        match_content=b"client_id=client_id&client_secret=client_secret"
        b"&grant_type=client_credentials&scope=https%3A%2F%2Fgraph.microsoft.com%2F.default",
    )
    httpx_mock.add_response(
        method="GET",
        url="https://graph.microsoft.com/v1.0/users?$select=id&$top=999",
        status_code=200,
        json=expected_value,
    )

    api_client = UserAPI(graph_api_client=graph_api_client)
    entries = list(api_client.get_user_ids())
    assert entries == ["0", "1", "2", "3", "4", "5", "6"]


def test_get_calendar_ids(httpx_mock, graph_api_client):
    user_id = "1"
    expected_value = {"value": [{"id": str(i)} for i in range(7)]}
    httpx_mock.add_response(
        method="POST",
        url="https://login.microsoftonline.com/ms_tenant_id/oauth2/v2.0/token",
        status_code=200,
        json={"access_token": "access_token"},
        match_content=b"client_id=client_id&client_secret=client_secret"
        b"&grant_type=client_credentials&scope=https%3A%2F%2Fgraph.microsoft.com%2F.default",
    )
    httpx_mock.add_response(
        method="GET",
        url=f"https://graph.microsoft.com/v1.0/users/{user_id}/calendars?select=id&$top=999",
        status_code=200,
        json=expected_value,
    )

    api_client = CalendarAPI(graph_api_client=graph_api_client)
    entries = list(api_client.get_calendar_ids(user_id))
    assert entries == ["0", "1", "2", "3", "4", "5", "6"]
