from logging import getLogger
from se_comms_graph_api_utils.client import AbstractGraphAPIClient, GraphApiClient, RequestMethods

TEAMS_API = "/groups?$select=id,displayName,resourceProvisioningOptions&$top=999"
GROUP_MEMBERS_API = (
    "/groups/{id}/members/microsoft.graph.user?$select=id,userPrincipalName&$top=999"
)
GET_CHANNELS_API = "/teams/{team_id}/allChannels?$select=id,displayName"
LIST_CHANNEL_MEMBERS_API = "teams/{team_id}/channels/{channel_id}/members"

logger = getLogger(__name__)


class GroupAPI(AbstractGraphAPIClient):
    """Contains all the Group APIs."""

    def __init__(self, graph_api_client: GraphApiClient):
        super().__init__(graph_api_client)

    def get_team_details(self):
        all_groups = self._graph_api_client.paginated_query(api_endpoint=TEAMS_API)
        team_details = [
            (i["id"], i["displayName"])
            for i in all_groups
            if "Team" in i.get("resourceProvisioningOptions", list())
        ]
        logger.info(f"Number of teams: {len(team_details)}")
        return team_details

    def get_group_members(self, user_group):
        # userPrincipalName info is not available if user.read.all application
        # permission is not granted for the app
        return [
            (i["id"], i.get("userPrincipalName", i["id"]))
            for i in self._graph_api_client.paginated_query(
                api_endpoint=GROUP_MEMBERS_API.format(id=user_group)
            )
        ]

    def get_channels(self, team_id):
        all_channels = self._graph_api_client.do_request(
            url=GET_CHANNELS_API.format(team_id=team_id), method=RequestMethods.GET
        ).json()["value"]
        logger.info(f"Number of channels for team-id {team_id}: {len(all_channels)}")
        return all_channels

    def list_channel_members(self, team_id, channel_id):
        response = self._graph_api_client.do_request(
            url=LIST_CHANNEL_MEMBERS_API.format(team_id=team_id, channel_id=channel_id),
            method=RequestMethods.GET,
        ).json()
        channel_member_ids = [i["userId"] for i in response["value"]]
        logger.info(
            f"Number of channel members for team-id {team_id},"
            f" channel-id: {channel_id}: {len(channel_member_ids)}"
        )
        return channel_member_ids
