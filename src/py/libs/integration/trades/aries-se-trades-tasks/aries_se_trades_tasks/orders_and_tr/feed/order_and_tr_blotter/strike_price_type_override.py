import pandas as pd
from aries_se_core_tasks.core.integration_task import IntegrationTask
from se_trades_tasks.order_and_tr.feed.order_and_tr_universal_steeleye_trade_blotter.strike_price_type_override import (  # noqa: E501
    Params,
)
from se_trades_tasks.order_and_tr.feed.order_and_tr_universal_steeleye_trade_blotter.strike_price_type_override import (  # noqa: E501
    run_strike_price_type_override as run_tasks_lib,
)
from typing import Optional


class StrikePriceTypeOverride(IntegrationTask):
    def _run(
        self,
        source_frame: pd.DataFrame,
        params: Params = None,
        **kwargs,
    ) -> pd.DataFrame:
        res: pd.DataFrame = run_tasks_lib(source_frame=source_frame, params=params, **kwargs)
        return res


def run_strike_price_type_override(
    source_frame: pd.DataFrame,
    params: Params = None,
    app_metrics_path: Optional[str] = None,
    audit_path: Optional[str] = None,
    **kwargs,
):
    task = StrikePriceTypeOverride(app_metrics_path=app_metrics_path, audit_path=audit_path)
    return task.run(source_frame=source_frame, params=params, **kwargs)
