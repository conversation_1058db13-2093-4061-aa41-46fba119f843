import logging
import pandas as pd
from aries_se_core_tasks.core.integration_task import IntegrationTask
from aries_se_core_tasks.io.read.cloud.download_file import run_download_file
from aries_se_core_tasks.utilities.frame_manipulation import add_missing_columns
from aries_se_trades_tasks.order.transformations.feed.order_eze_oms_soft.static import (
    EzeSoftOmsAllocationsSourceColumns,
    EzeSoftOmsExecutionsSourceColumns,
    EzeSoftOmsOrdersSourceColumns,
    Prefix,
)
from pathlib import Path
from se_trades_tasks.order.static import add_prefix
from typing import Optional, Tuple

logger = logging.getLogger(__name__)


class PreprocessEzeOmsSoft(IntegrationTask):
    def _run(
        self,
        multiple_files_input_controller_result: pd.DataFrame,
        temp_storage: str,
        **kwargs,
    ) -> Tuple[str, str, str]:
        executions_file_url = str(
            multiple_files_input_controller_result.loc[0, "cloud_executions_file_url"]
        )
        allocations_file_url = str(
            multiple_files_input_controller_result.loc[0, "cloud_allocations_file_url"]
        )
        orders_file_url = str(
            multiple_files_input_controller_result.loc[0, "cloud_orders_file_url"]
        )

        executions_local_file_path = run_download_file(
            file_url=executions_file_url,
        )
        allocations_local_file_path = run_download_file(
            file_url=allocations_file_url,
        )
        orders_local_file_path = run_download_file(
            file_url=orders_file_url,
        )

        executions_df = pd.read_csv(executions_local_file_path, skiprows=1, delimiter="|")

        allocations_df = pd.read_csv(allocations_local_file_path, skiprows=1, delimiter="|")

        orders_df = pd.read_csv(orders_local_file_path, skiprows=1, delimiter="|")

        # Add prefixes to column names for identification
        executions_df = executions_df.rename(
            columns=lambda x: add_prefix(prefix=Prefix.EXECUTIONS, delimiter="_", attribute=x)
        )
        allocations_df = allocations_df.rename(
            columns=lambda x: add_prefix(prefix=Prefix.ALLOCATIONS, delimiter="_", attribute=x)
        )
        orders_df = orders_df.rename(
            columns=lambda x: add_prefix(prefix=Prefix.ORDERS, delimiter="_", attribute=x)
        )

        orders_df = add_missing_columns(
            dataframe=orders_df,
            columns=EzeSoftOmsOrdersSourceColumns.all(),
        )
        executions_df = add_missing_columns(
            dataframe=executions_df,
            columns=EzeSoftOmsExecutionsSourceColumns.all(),
        )
        allocations_df = add_missing_columns(
            dataframe=allocations_df,
            columns=EzeSoftOmsAllocationsSourceColumns.all(),
        )

        final_executions_df = create_final_order_df(orders_df, executions_df, allocations_df)
        final_allocations_df = create_final_allocations_df(orders_df, executions_df, allocations_df)

        final_orders_df = final_executions_df.drop_duplicates(
            subset=[EzeSoftOmsOrdersSourceColumns.ORDER_ID]
        )

        final_orders_path = Path(temp_storage).joinpath("final_orders_df.csv").as_posix()
        final_executions_path = Path(temp_storage).joinpath("final_executions_df.csv").as_posix()
        final_allocations_path = Path(temp_storage).joinpath("final_allocations_df.csv").as_posix()

        final_orders_df.to_csv(final_orders_path, index=False)
        final_executions_df.to_csv(final_executions_path, index=False)
        final_allocations_df.to_csv(final_allocations_path, index=False)

        return final_orders_path, final_executions_path, final_allocations_path


def create_final_order_df(
    orders_df: pd.DataFrame, executions_df: pd.DataFrame, allocations_df: pd.DataFrame
) -> pd.DataFrame:
    merged_orders_executions_df = orders_df.merge(
        right=executions_df,
        how="left",
        left_on=EzeSoftOmsOrdersSourceColumns.ORDER_ID,
        right_on=EzeSoftOmsExecutionsSourceColumns.ORDER_ID,
    )
    return merged_orders_executions_df.merge(
        right=allocations_df,
        how="left",
        left_on=EzeSoftOmsOrdersSourceColumns.ORDER_ID,
        right_on=EzeSoftOmsAllocationsSourceColumns.ORDER_ID,
    )


def create_final_allocations_df(
    orders_df: pd.DataFrame, executions_df: pd.DataFrame, allocations_df: pd.DataFrame
) -> pd.DataFrame:
    merged_allocations_orders_df = allocations_df.merge(
        right=orders_df[
            EzeSoftOmsOrdersSourceColumns.required_columns_from_orders_for_final_allocations_df()
        ],
        how="left",
        left_on=EzeSoftOmsAllocationsSourceColumns.ORDER_ID,
        right_on=EzeSoftOmsOrdersSourceColumns.ORDER_ID,
    )
    return merged_allocations_orders_df.merge(
        right=executions_df[
            EzeSoftOmsExecutionsSourceColumns.required_columns_from_executions_for_final_allocations_df()
        ],
        how="left",
        left_on=EzeSoftOmsAllocationsSourceColumns.ORDER_ID,
        right_on=EzeSoftOmsExecutionsSourceColumns.ORDER_ID,
    )


def run_preprocess_eze_oms_soft(
    multiple_files_input_controller_result: pd.DataFrame,
    temp_storage: str,
    app_metrics_path: Optional[str] = None,
    audit_path: Optional[str] = None,
    **kwargs,
) -> Tuple[str, str, str]:
    task = PreprocessEzeOmsSoft(app_metrics_path=app_metrics_path, audit_path=audit_path)

    return task.run(
        multiple_files_input_controller_result=multiple_files_input_controller_result,
        temp_storage=temp_storage,
        **kwargs,
    )
