import fsspec
import logging
import pandas as pd
from aries_se_core_tasks.core.integration_task import IntegrationTask
from aries_se_core_tasks.core.params import TaskParams
from aries_se_trades_tasks.order.transformations.feed.order_feed_cfox.static import (
    RECEIVED_TIMESTAMP_MAP,
    SUBMITTED_TIMESTAMP_MAP,
    SourceColumns,
)
from pydantic import Field
from typing import Optional

logger = logging.getLogger(__name__)


class Params(TaskParams):
    encoding: Optional[str] = Field(
        default=None,
        description="Encoding to be passed into pandas.read_csv. Only populate this"
        "parameter if you are sure that the input file will always "
        "have the same encoding i.e. `utf-8`",
    )
    delimiter: str = Field(
        default=",",
        description="Character that separate each field int the input file",
    )
    order_id_column: str = Field(
        default="Order ID",
        description="Column name in the header that contains the Order ID for the records in the"
        " input file",
    )
    event_type_column: str = Field(
        default="Event type",
        description="Column name in the header that contains the Event Type for the records in the"
        " input file",
    )
    timestamp_column: str = Field(
        default="Event Timestamp",
        description="Column name in the header that contains the Transaction Timestamp for the"
        " records in the input file",
    )
    received_type_value: str = Field(
        default="A",
        description="Value in the 'Event Type' column wich shows that the 'Transaction Timestamp'"
        " in this record should be mapped to the order 'timestamps.orderReceived'",
    )
    submitted_type_value: str = Field(
        default="0",
        description="Value in the 'Event Type' column wich shows that the 'Transaction Timestamp'"
        " in this record should be mapped to the order 'timestamps.orderSubmitted'",
    )


class MapTimestampsByOrderId(IntegrationTask):
    """This task returns a dictionary with two mappings:
    'received_timestamp_map' that maps an 'Order Id' to
    'timestamps.orderReceived', and 'submitted_timestamp_map' that maps 'Order
    Id' to 'timestamps.orderSubmitted'.

    The task extract all order ids needed from the 'input_df' and read,
    line by line, the 'csv_file_uri' looking for the order ids. It stops
    processing the file if the file ends or if all order ids have been
    mapped.
    """

    def _run(
        self, csv_file_uri: str, input_df: pd.DataFrame, params: Params, **kwargs
    ) -> dict[str, dict]:
        order_id_in_input = self._get_order_ids_in_input(input_df)

        received_order_ids = order_id_in_input.copy()
        submitted_order_ids = order_id_in_input.copy()
        received_timestamp_map = {}
        submitted_timestamp_map = {}

        csv_file_lines = self._read_file_generator(
            csv_file_uri=csv_file_uri,
            delimiter=params.delimiter,
        )

        order_id_pos, type_pos, timestamp_pos = self._extract_positions_from_header(
            csv_file_lines=csv_file_lines,
            order_id_column=params.order_id_column,
            event_type_column=params.event_type_column,
            timestamp_column=params.timestamp_column,
        )

        for line in csv_file_lines:
            if not received_order_ids and not submitted_order_ids:
                break

            if (
                line[type_pos] == params.received_type_value
                and line[order_id_pos] in received_order_ids
            ):
                received_timestamp_map.update({line[order_id_pos]: line[timestamp_pos]})
                received_order_ids.remove(line[order_id_pos])

            elif (
                line[type_pos] == params.submitted_type_value
                and line[order_id_pos] in submitted_order_ids
            ):
                submitted_timestamp_map.update({line[order_id_pos]: line[timestamp_pos]})
                submitted_order_ids.remove(line[order_id_pos])

        return {
            RECEIVED_TIMESTAMP_MAP: received_timestamp_map,
            SUBMITTED_TIMESTAMP_MAP: submitted_timestamp_map,
        }

    def _get_order_ids_in_input(self, input_df: pd.DataFrame) -> list[str]:
        order_id_list = input_df[SourceColumns.ORDER_ID].to_list()
        return list(set(order_id_list))

    def _extract_positions_from_header(
        self,
        csv_file_lines,
        order_id_column: str,
        event_type_column: str,
        timestamp_column: str,
    ) -> tuple[int, int, int]:
        header = next(csv_file_lines)
        order_id_pos = header.index(order_id_column)
        type_pos = header.index(event_type_column)
        timestamp_pos = header.index(timestamp_column)
        return order_id_pos, type_pos, timestamp_pos

    def _read_file_generator(self, csv_file_uri: str, delimiter: str):
        with fsspec.open(csv_file_uri, "r") as file:
            for line in file:
                yield line.strip().split(delimiter)


def run_map_timestamps_by_order_id(
    csv_file_uri: str,
    input_df: pd.DataFrame,
    params: Params,
    app_metrics_path: Optional[str] = None,
    audit_path: Optional[str] = None,
    **kwargs,
):
    task = MapTimestampsByOrderId(app_metrics_path=app_metrics_path, audit_path=audit_path)
    return task.run(csv_file_uri=csv_file_uri, input_df=input_df, params=params, **kwargs)
