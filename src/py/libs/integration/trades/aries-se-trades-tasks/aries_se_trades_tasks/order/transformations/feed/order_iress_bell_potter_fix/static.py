from aries_se_core_tasks.utilities.data_utils import BaseColumns


class IressBellPotterSourceColumns(BaseColumns):
    ACCOUNT = "Account"
    ACCOUNT_TYPE = "AccountType"
    AVG_PX = "AvgPx"
    CFI_CODE = "CFICode"
    CURRENCY = "Currency"
    EX_DESTINATION = "ExDestination"
    EXEC_ID = "ExecID"
    EXEC_TYPE = "ExecType"
    FF_10010 = "ff_10010"
    FF_10051 = "ff_10051"
    FF_1057 = "ff_1057"
    FF_109 = "ff_109"
    FF_5000 = "ff_5000"  # MarketOrderNo
    FF_5001 = "ff_5001"
    FF_5100 = "ff_5100"
    FF_5265 = "ff_5265"  # OrderClass
    FF_8138 = "ff_8138"
    FF_8139 = "ff_8139"
    FF_8140 = "ff_8140"
    FF_8200 = "ff_8200"
    FF_9026 = "ff_9026"
    FF_9027 = "ff_9027"
    LAST_CAPACITY = "LastCapacity"
    LAST_MKT = "LastMkt"
    LAST_PX = "LastPx"
    LAST_QTY = "LastQty"
    LEAVES_QTY = "LeavesQty"
    LEG_LAST_PX = "LegLastPx"
    LEG_SIDE = "LegSide"
    MATURITY_DATE = "MaturityDate"
    MSG_SEQ_NUM = "MsgSeqNum"
    MSG_TYPE = "MsgType"
    NO_LEGS = "NoLegs"
    ON_BEHALF_OF_COMP_ID = "OnBehalfOfCompID"
    ORD_STATUS = "OrdStatus"
    ORD_TYPE = "OrdType"
    ORDER_CAPACITY = "OrderCapacity"
    ORDER_ID = "OrderID"
    ORDER_QTY = "OrderQty"
    ORDER_RESTRICTIONS = "OrderRestrictions"
    PARTY_ID = "PartyID"
    PARTY_ROLE = "PartyRole"
    PRICE = "Price"
    PRICE_TYPE = "PriceType"
    SECURITY_EXCHANGE = "SecurityExchange"
    SECURITY_ID = "SecurityID"
    SENDING_TIME = "SendingTime"
    SECURITY_TYPE = "SecurityType"
    SIDE = "Side"
    STOP_PX = "StopPx"
    STRIKE_PRICE = "StrikePrice"
    SYMBOL = "Symbol"
    TARGET_COMP_ID = "TargetCompID"
    TEXT = "Text"
    TIME_IN_FORCE = "TimeInForce"
    TRANSACT_TIME = "TransactTime"


class IressBellPotterTempColumns:
    ASSET_CLASS = "__asset_class__"
    CURRENCY = "__currency__"
    INSTRUMENT_CLASSIFICATION = "__instrument_classification__"
    INSTRUMENT_EXT_ALTERNATIVE_INSTRUMENT_ID = "__instrument_ext_alternative_instrument_id__"
    INSTRUMENT_EXT_UNIQUE_INSTRUMENT_ID = "__instrument_ext_unique_instrument_id__"
    INSTRUMENT_FULL_NAME = "__instrument_full_name__"
    INSTRUMENT_IS_CREATED_THROUGH_FALLBACK = "__instrument__is_created_through_fallback__"
    INT_DF_5000 = "__int_df_5000__"
    ORDER_PREFIX = "__order_prefix__"
    ISIN = "__isin__"
    VENUE = "__venue__"
    TRANSACT_TIME = "__transact_time__"


class HierarchyColumns:
    PARENT: str = "Parent"
    CHILD: str = "Child"
