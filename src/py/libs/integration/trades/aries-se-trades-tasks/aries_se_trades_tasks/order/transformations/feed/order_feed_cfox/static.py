from aries_se_core_tasks.utilities.data_utils import BaseColumns


class SourceColumns(BaseColumns):
    ACCOUNT = "ACCOUNT"
    ALGO_ID = "ALGOID"
    AVG_PRICE = "AVGPRICE"
    BLOOMBERG_TICKER = "BL<PERSON>OMBERGTICKER"
    BLOOMBERG_TICKER_BBPK = "BLOOM<PERSON>RG<PERSON><PERSON>KERBBPK"
    CLIENT_ORDER_ID = "CLIENTORDERID"
    CUMULATIVE_QUANTITY = "CUMULATIVEQUANTITY"
    CURRENCY = "CURRENCY"
    CUSTSETTL_TYPE = "CUSTSETTLTYPE"
    CUSTSETTL_TYPE_DESCRIPTION = "CUSTSETTLTYPEDESCRIPTION"
    DELIVER_TO_COMP_ID = "DELIVERTOCOMPID"
    EVENT_TIMESTAMP = "EVENTTIMESTAMP"
    EVENT_TYPE = "EVENTTYPE"
    EVENT_TYPE_DESCRIPTION = "EVENTTYPEDESCRIPTION"
    EXEC_ID = "EXECID"
    EXECUTING_ENTITY = "EXECUTINGENTITY"
    EXPIRY_DATE_TIME = "EXPIRYDATETIME"
    ID_SOURCE = "IDSOURCE"
    INSTRUMENT = "INSTRUMENT"
    LAST_PRICE = "LASTPRICE"
    LAST_QTY = "LASTQTY"
    MANUAL_ORDER_IDENTIFIER = "MANUALORDERIDENTIFIER"
    MATURITY_MONTH_YEAR = "MATURITYMONTHYEAR"
    MLEG_REPORTING_TYPE = "MLEGREPORTINGTYPE"
    OPEN_CLOSE_INDICATOR = "OPENCLOSEINDICATOR"
    OPTION_TYPE = "OPTIONTYPE"
    ORDER_ID = "ORDERID"
    ORDER_QUANTITY = "ORDERQUANTITY"
    ORDER_TYPE = "ORDERTYPE"
    ORDER_TYPE_DESCRIPTION = "ORDERTYPEDESCRIPTION"
    ORIG_CLORDER_ID = "ORIGCLORDERID"
    PARENT_ORDER_ID = "PARENTORDERID"
    PRICE = "PRICE"
    REMAINING_QUANTITY = "REMAININGQUANTITY"
    SECURITY_EXCHANGE = "SECURITYEXCHANGE"
    SECURITY_TYPE = "SECURITYTYPE"
    SETTLEMENT_DATE = "SETTLEMENTDATE"
    SIDE = "SIDE"
    TICKER_SYMBOL = "TICKERSYMBOL"
    TIME_IN_FORCE = "TIMEINFORCE"
    TIME_IN_FORCE_DESCRIPTION = "TIMEINFORCEDESCRIPTION"
    TRADE_DATE = "TRADEDATE"
    TRADER_ID = "TRADERID"
    TRANSACTION_TIMESTAMP = "TRANSACTIONTIMESTAMP"


class TempColumns(BaseColumns):
    ACCOUNT_FIRM = "__account_firm__"
    ASSET_CLASS = "__asset_class__"
    BUY_SELL_INDICATOR = "__buy_sell_indicator__"
    CREATED_THROUGH_FALLBACK = "__created_through_fallbak__"
    EXPIRY_DATE = "__expiry_date__"
    ID_ACCOUNT = "__id_account__"
    ID_EXECUTING_ENTITY = "__id_executing_entity__"
    ID_TRADER_ID = "__id_trader_id__"
    INSTRUMENT_CLASSIFICATION = "__instrument_classification__"
    INSTRUMENT_FULL_NAME = "__instrument_full_name__"
    INSTRUMENT_IS_CREATED_THROUGH_FALLBACK = "__instrument_is_created_through_fallback__"
    INSTRUMENT_UNIQUE_ID = "__instrument_unique_id__"
    IS_ICEBERG = "__is_iceberg__"
    MAPPED_BUY_SELL = "__mapped_buy_sell__"
    MAPPED_TIMESTAMPS_ORDER_RECEIVED = "__mapped_order_received_dt__"
    MAPPED_TIMESTAMPS_ORDER_SUBMITTED = "__mapped_order_submitted_dt__"
    META_MODEL = "__meta_model__"
    OPTION_STRIKE_PRICE = "__option_strike_price__"
    OPTION_TYPE = "__option_type__"
    VALIDITY_PERIOD = "__validity_period__"
    INPUT_CURRENCY = "__input_currency__"
    PRICE_NOTATION = "__price_notation__"
    QUANTITY_CURRENCY_2 = "__quantity_currency__"
    QUANTITY_NOTATION = "__quantity_notation__"
    UNDERLYING_SYMBOL = "__undelying_symbol__"
    UNDERLYING_SYMBOL_AND_EXPIRY_CODE = "__underlying_symbol_and_expiry_code__"
    VENUE = "__venue__"


class MicLookupFileColumns:
    EXCHANGE_NAME = "Exchange Name"
    MIC_CODE = "MIC Code"


ORDER_FEED_CFOX_HANDLER: str = "order_feed_cfox_handler_message_mappings"
RECEIVED_TIMESTAMP_MAP: str = "received_timestamp_map"
SUBMITTED_TIMESTAMP_MAP: str = "submitted_timestamp_map"
