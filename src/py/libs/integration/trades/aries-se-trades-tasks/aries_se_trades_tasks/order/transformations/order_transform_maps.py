from aries_se_trades_tasks.order.transformations.feed.order_aladdin_v2.mapping_overrides.example.order_aladdin_v2_schroders_client_side_mappings import (  # noqa E501
    OrderAladdinV2SchrodersClientSideMappings,
)
from aries_se_trades_tasks.order.transformations.feed.order_aladdin_v2.mapping_overrides.example.order_aladdin_v2_schroders_market_side_mappings import (  # noqa E501
    OrderAladdinV2SchrodersMarketSideMappings,
)
from aries_se_trades_tasks.order.transformations.feed.order_aladdin_v2.order_aladdin_v2_client_side_mappings import (  # noqa: E501
    OrderAladdinV2ClientSideMappings,
)
from aries_se_trades_tasks.order.transformations.feed.order_aladdin_v2.order_aladdin_v2_market_side_mappings import (  # noqa: E501
    OrderAladdinV2MarketSideMappings,
)
from aries_se_trades_tasks.order.transformations.feed.order_crd.order_crd_mappings import (  # noqa: E501
    OrderCrdMappings,
)
from aries_se_trades_tasks.order.transformations.feed.order_eze_oms_soft.order_eze_oms_soft_orders_mappings import (  # noqa: E501
    OrderEzeOmsSoftMappings,
)

from aries_se_trades_tasks.order.transformations.feed.order_eze_oms_soft.order_eze_oms_soft_allocations_mappings import (  # noqa: E501
    AllocationsOrderEzeOmsSoftMappings,
)
from aries_se_trades_tasks.order.transformations.feed.order_eze_oms_soft.order_eze_oms_soft_executions_mappings import (  # noqa: E501
    ExecutionsOrderEzeOmsSoftMappings,
)
from aries_se_trades_tasks.order.transformations.feed.order_feed_cfox.order_feed_cfox_mappings import (  # noqa: E501
    OrderFeedCFOXMappings,
)
from aries_se_trades_tasks.order.transformations.feed.order_flextrade_bell_potter_fix.order_flextrade_bell_potter_fix_mappings import (  # noqa E501
    OrderFlextradeBellPotterFixMappings,
)
from aries_se_trades_tasks.order.transformations.feed.order_iress_bell_potter_fix.order_iress_bell_potter_fix_mappings import (  # noqa E501
    OrderIressBellPotterFixMappings,
)
from aries_se_trades_tasks.order.transformations.feed.order_tradeweb.order_tradeweb_mappings import (  # noqa E501
    OrderTradeWebMappings,
)
from aries_se_trades_tasks.orders_and_tr.transformations.feed.order_tr_fidessa_eod.order_tr_fidessa_eod_order_mappings import (  # noqa E501
    OrderTrFidessaEODOrderTransformations,
)
from aries_se_trades_tasks.orders_and_tr.transformations.feed.order_tr_fidessa_eod.overrides.oppenheimer_order_tr_fidessa_eod_order_mappings import (  # noqa E501
    OppenheimerOrderTrFidessaEODOrderTransformations,
)
from aries_se_trades_tasks.order.transformations.feed.order_blotter.overrides.boundarycreek import (
    BoundarycreekSteeleyeUniversalOrderBlotterTransformations,
)
from aries_se_trades_tasks.order.transformations.feed.order_blotter.overrides.fil import (
    FilSteeleyeUniversalOrderBlotterTransformations,
)
from aries_se_trades_tasks.order.transformations.feed.order_blotter.overrides.insight import (
    InsightSteeleyeUniversalOrderBlotterTransformations,
)
from aries_se_trades_tasks.order.transformations.feed.order_blotter.overrides.thornbridge import (
    ThornbridgeSteeleyeUniversalOrderBlotterTransformations,
)
from aries_se_trades_tasks.order.transformations.feed.order_blotter.steeleye_universal_order_blotter_transformations import (  # noqa E501
    SteeleyeUniversalOrderBlotterTransformations,
)
from se_core_tasks.abstractions.transformations.transform_map import TransformMap

order_aladdin_v2_client_side_map = TransformMap(
    default=OrderAladdinV2ClientSideMappings,
    map={
        "schroders": OrderAladdinV2SchrodersClientSideMappings,
    },
)
order_aladdin_v2_market_side_map = TransformMap(
    default=OrderAladdinV2MarketSideMappings,
    map={
        "schroders": OrderAladdinV2SchrodersMarketSideMappings,
    },
)

order_blotter_map = TransformMap(
    default=SteeleyeUniversalOrderBlotterTransformations,
    map={
        "boundarycreek": BoundarycreekSteeleyeUniversalOrderBlotterTransformations,
        "insight": InsightSteeleyeUniversalOrderBlotterTransformations,
        "thornbridge": ThornbridgeSteeleyeUniversalOrderBlotterTransformations,
        "rohan": FilSteeleyeUniversalOrderBlotterTransformations,
        "fil-uat": FilSteeleyeUniversalOrderBlotterTransformations,
        "fil": FilSteeleyeUniversalOrderBlotterTransformations,
    },
)

order_feed_cfox_map = TransformMap(default=OrderFeedCFOXMappings, map={})

order_crd_map = TransformMap(default=OrderCrdMappings, map={})

fidessa_eod = TransformMap(
    default=OrderTrFidessaEODOrderTransformations,
    map={
        "oppenheimer": OppenheimerOrderTrFidessaEODOrderTransformations,
        "dom": OppenheimerOrderTrFidessaEODOrderTransformations,
    },
)

order_flextrade_bell_potter_fix_map = TransformMap(
    default=OrderFlextradeBellPotterFixMappings, map={}
)

order_iress_bell_potter_fix_map = TransformMap(default=OrderIressBellPotterFixMappings, map={})

order_tradeweb_map = TransformMap(default=OrderTradeWebMappings, map={})

order_eze_oms_soft_orders_map = TransformMap(default=OrderEzeOmsSoftMappings, map={})

order_eze_oms_soft_executions_map = TransformMap(default=ExecutionsOrderEzeOmsSoftMappings, map={})

order_eze_oms_soft_allocations_map = TransformMap(
    default=AllocationsOrderEzeOmsSoftMappings, map={}
)
