# type: ignore
import logging
import pandas as pd
from aries_se_core_tasks.core.integration_task import IntegrationTask
from se_trades_tasks.tr.party.identifiers.generic_tr_party_identifiers import Params
from se_trades_tasks.tr.party.identifiers.generic_tr_party_identifiers import (
    run_generic_tr_party_identifiers as run_task_from_lib,
)
from typing import Optional

logger = logging.getLogger(__name__)


class GenericTrPartyIdentifiers(IntegrationTask):
    def _run(
        self,
        source_frame: pd.DataFrame,
        params: Params,
        **kwargs,
    ) -> pd.DataFrame:
        return run_task_from_lib(source_frame=source_frame, params=params, **kwargs)


def run_generic_tr_party_identifiers(
    source_frame: pd.DataFrame,
    params: Params,
    app_metrics_path: Optional[str] = None,
    audit_path: Optional[str] = None,
    **kwargs,
) -> pd.DataFrame:
    task = GenericTrPartyIdentifiers(app_metrics_path=app_metrics_path, audit_path=audit_path)

    return task.run(
        source_frame=source_frame,
        params=params,
        **kwargs,
    )
