import pandas as pd
from aries_se_trades_tasks.tr.transformations.bbg.emsi.orders.chelverton_tr_bbg_emsi_orders_transformations import (  # noqa: E501
    ChelvertonTrBBGEMSIOrdersTransformations,
)
from aries_se_trades_tasks.tr.transformations.bbg.emsi.orders.tr_bbg_emsi_orders_transformations import (  # noqa: E501
    TrBBGEMSIOrdersTransformations,
)


class TestTrBBGEMSIOrdersTransformations:
    """these mappings are tested on:

    src/py/tasks/integration/aries/trades/tr/tr-bbg-emsi-orders/tests_tr_bbg_emsi_orders/test_tr_bbg_emsi_orders.py
    """

    def test_assert_mappings(self):
        assert TrBBGEMSIOrdersTransformations  # type: ignore[truthy-function]

    def test_get_trader_notes_trader(self):
        source_frame = pd.DataFrame(
            {
                "ACCOUNT": ["123", "456", pd.NA, pd.NA],
                "TRADERNOTES": ["GLTH - 1AAA", "GLTH | 1AAA", "GLTH - 1AAA", "GLTH | 1AAA"],
                "ROUTEFILLEDAMOUNT": [1, 1, 1, 1],
            }
        )

        class_instance = ChelvertonTrBBGEMSIOrdersTransformations(
            source_file_uri="s3://test.dev.steeleye.co/aries/ingress/nonstreamed/evented/tr_bbg_emsi_orders/source.csv",
            tenant="chelverton",
            es_client=None,
            source_frame=source_frame,
        )

        result = class_instance.get_trader_notes_trader()

        assert result.tolist() == ["123", "456", "GLTH", "GLTH"]
