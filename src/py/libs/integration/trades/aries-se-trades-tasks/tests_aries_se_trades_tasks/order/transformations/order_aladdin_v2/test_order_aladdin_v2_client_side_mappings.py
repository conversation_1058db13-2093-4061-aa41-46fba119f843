import pandas as pd
from aries_se_trades_tasks.order.transformations.feed.order_aladdin_v2.order_aladdin_v2_client_side_mappings import (  # noqa: E501
    OrderAladdinV2ClientSideMappings,
)
from aries_se_trades_tasks.order.transformations.feed.order_aladdin_v2.static import (
    FileTypes,
    OrderDetailSourceColumns,
    OrderSourceColumns,
    TransactionSourceColumns,
)
from freezegun import freeze_time
from integration_test_utils.aws_helpers.patch_response import (
    mock_aiobotocore_convert_to_response_dict,
)
from integration_test_utils.aws_helpers.s3_test_helpers import create_and_add_objects_to_s3_bucket
from moto import mock_aws
from pathlib import Path
from se_elastic_schema.static.mifid2 import OrderStatus
from se_trades_tasks.order.static import ModelPrefix, OrderColumns, add_prefix

BUCKET_NAME = "test.dev.steeleye.co"
SCRIPT_PATH: Path = Path(__file__).parent
LOCAL_BUCKET_PATH = Path(__file__).parent.joinpath("data/buckets", BUCKET_NAME)

mock_aiobotocore_convert_to_response_dict()


class MockESClient:
    def __init__(self, mock_es_scroll: pd.DataFrame):
        self.mock_es_scroll = mock_es_scroll
        self.MAX_TERMS_SIZE = 1024

    def scroll(self, **kwargs):
        return self.mock_es_scroll


@mock_aws
@freeze_time(time_to_freeze="2024-02-10 14:20:00.000000+00:00")
class TestOrderAladdinV2BaseMappings:
    def test_execution_details_order_status(self):
        create_and_add_objects_to_s3_bucket(bucket_name=BUCKET_NAME, source_path=LOCAL_BUCKET_PATH)

        # 6 input orders

        # 1. null orderStatus because quantity booked is 0
        # 2. null orderStatus because quantity booked is null
        # 3. CAME orderStatus because quantity booked is 0 and quantity is 0 and orderStatus is C
        # 4. PARF orderStatus - regular Order
        # 5. PARF orderStatus - quantity booked is the same as quantity and orderStatus is F
        # BUT initial quantity is the same as the previous order event's initial quantity
        # 6. CAME orderStatus - quantity booked is the same as quantity and orderStatus is F
        # and initial quantity is less than the previous order event's initial quantity
        source_frame = pd.DataFrame(
            {
                add_prefix(
                    FileTypes.ORDER_DETAILS, OrderDetailSourceColumns.ORDER_DETAIL_QUANTITY_BOOKED
                ): [
                    0,
                    pd.NA,
                    0,
                    50,
                    50,
                    50,
                ],
                add_prefix(
                    FileTypes.ORDER_DETAILS, OrderDetailSourceColumns.ORDER_DETAIL_QUANTITY
                ): [
                    100,
                    100,
                    0,
                    50,
                    50,
                    50,
                ],
                add_prefix(FileTypes.ORDER, OrderSourceColumns.ORDER_STATUS): [
                    "A",
                    "A",
                    "C",
                    "A",
                    "F",
                    "F",
                ],
                add_prefix(FileTypes.ORDER_DETAILS, OrderDetailSourceColumns.ORIG_ORDER_ID): [
                    "orig_order_id_x",
                    "orig_order_id_y",
                    "orig_order_id_z",
                    "orig_order_id_q",
                    "orig_order_id_w",
                    "orig_order_id_e",
                ],
                add_prefix(FileTypes.ORDER, OrderSourceColumns.OWNER_TYPE): [pd.NA] * 6,
                add_prefix(FileTypes.TRANSACTION, TransactionSourceColumns.SEC_TICKER): [
                    "ticker",
                ]
                * 6,
            },
            index=[10, 11, 12, 13, 14, 15],
        )

        es_scroll_frame = pd.DataFrame(
            {
                OrderColumns.ID: [
                    "order_id_5",
                    "order_id_5",
                    "order_id_6",
                    "order_id_6",
                ],
                OrderColumns.TIMESTAMPS_ORDER_RECEIVED: [
                    "2024-02-09T14:20:00.000000Z",
                    "2024-02-10T14:20:00.000000Z",
                    "2024-02-09T13:20:00.000000Z",
                    "2024-02-10T13:20:00.000000Z",
                ],
                OrderColumns.PRICE_FORMING_DATA_INITIAL_QUANTITY: [
                    50,
                    50,
                    70,
                    70,
                ],
            }
        )

        target_frame = pd.DataFrame(
            {
                add_prefix(ModelPrefix.ORDER_STATE, OrderColumns.ID): [
                    "order_id_1",
                    "order_id_2",
                    "order_id_3",
                    "order_id_4",
                    "order_id_5",
                    "order_id_6",
                ],
                OrderColumns.PRICE_FORMING_DATA_INITIAL_QUANTITY: [
                    100,
                    100,
                    0,
                    50,
                    50,
                    50,
                ],
            },
            index=[10, 11, 12, 13, 14, 15],
        )
        target_frame.index.name = "__index__"

        result_frame = pd.DataFrame(
            {
                add_prefix(ModelPrefix.ORDER, OrderColumns.EXECUTION_DETAILS_ORDER_STATUS): [
                    OrderStatus.NEWO
                ]
                * 6,
                add_prefix(ModelPrefix.ORDER_STATE, OrderColumns.EXECUTION_DETAILS_ORDER_STATUS): [
                    pd.NA,
                    pd.NA,
                    OrderStatus.CAME,
                    OrderStatus.PARF,
                    OrderStatus.PARF,
                    OrderStatus.CAME,
                ],
            },
            index=[10, 11, 12, 13, 14, 15],
        )

        class_instance = OrderAladdinV2ClientSideMappings(
            source_file_uri="foo",
            file_uri="s3://test.dev.steeleye.co/aries/ingest/order_aladdin_v2/"
            "2025/02/10/S6gRboG5aQ870xfy87l_v/file_splitter_by_criteria/"
            "foo_FI.20241103_market_side_orders_batch_0.csv",
            tenant="test",
            es_client=MockESClient(mock_es_scroll=es_scroll_frame),
            order_id_cache_path="s3://test.dev.steeleye.co/aries/ingest/order_aladdin_v2/"
            "2025/02/10/random_id/file_splitter_by_criteria/"
            "instrument_cache.json",
            source_frame=source_frame,
        )

        class_instance.target_df = target_frame
        class_instance.execution_details_order_status()

        # fillna("foo") is required to compare the pd.NA values
        # otherwise it throws a "pd.NA is ambiguous" error
        pd.testing.assert_frame_equal(
            class_instance.target_df.loc[
                :,
                [
                    add_prefix(ModelPrefix.ORDER, OrderColumns.EXECUTION_DETAILS_ORDER_STATUS),
                    add_prefix(
                        ModelPrefix.ORDER_STATE, OrderColumns.EXECUTION_DETAILS_ORDER_STATUS
                    ),
                ],
            ].fillna("foo"),
            result_frame.fillna("foo"),
            check_dtype=False,
        )

        assert class_instance.cancelled_quantity_diff_by_order_id_map == {
            "order_id_6": 20,
        }

    def test_price_forming_data_traded_quantity(self):
        create_and_add_objects_to_s3_bucket(bucket_name=BUCKET_NAME, source_path=LOCAL_BUCKET_PATH)

        # 3 input orders

        # 1. First order event in the lifecycle.
        # The algo will use the quantity booked as the traded quantity
        # 2. Second order event in the lifecycle.
        # The algo will calculate the traded quantity based on the prior event
        # 3. Partially-filled cancellation. The algo will re-use the canceled quantity
        # calculated in the orderStatus mapping.

        source_frame = pd.DataFrame(
            {
                add_prefix(
                    FileTypes.ORDER_DETAILS, OrderDetailSourceColumns.ORDER_DETAIL_QUANTITY_BOOKED
                ): [
                    100,
                    70,
                    80,
                ],
                add_prefix(FileTypes.ORDER_DETAILS, OrderDetailSourceColumns.ORIG_ORDER_ID): [
                    "orig_order_id_x",
                    "orig_order_id_y",
                    "orig_order_id_z",
                ],
                add_prefix(FileTypes.ORDER, OrderSourceColumns.ORDER_STATUS): [
                    "status",
                ]
                * 3,
                add_prefix(FileTypes.ORDER, OrderSourceColumns.OWNER_TYPE): [pd.NA] * 3,
                add_prefix(FileTypes.TRANSACTION, TransactionSourceColumns.SEC_TICKER): [
                    "ticker",
                ]
                * 3,
            },
            index=[10, 11, 12],
        )
        source_frame.index.name = "__index__"

        es_scroll_frame = pd.DataFrame(
            {
                OrderColumns.ID: [
                    "order_id_2",
                    "order_id_2",
                ],
                OrderColumns.TRANSACTION_DETAILS_TRADING_DATE_TIME: [
                    "2024-02-09T14:20:00.000000Z",
                    "2024-02-10T14:20:00.000000Z",
                ],
                OrderColumns.ORDER_IDENTIFIERS_SEQUENCE_NUMBER: [
                    0,
                    50,
                ],
            }
        )

        target_frame = pd.DataFrame(
            {
                add_prefix(ModelPrefix.ORDER_STATE, OrderColumns.ID): [
                    "order_id_1",
                    "order_id_2",
                    "order_id_3",
                ],
                add_prefix(ModelPrefix.ORDER_STATE, OrderColumns.EXECUTION_DETAILS_ORDER_STATUS): [
                    OrderStatus.PARF,
                    OrderStatus.PARF,
                    OrderStatus.CAME,
                ],
            },
            index=[10, 11, 12],
        )

        class_instance = OrderAladdinV2ClientSideMappings(
            source_file_uri="foo",
            file_uri="s3://test.dev.steeleye.co/aries/ingest/order_aladdin_v2/"
            "2025/02/10/S6gRboG5aQ870xfy87l_v/file_splitter_by_criteria/"
            "foo_FI.20241103_market_side_orders_batch_0.csv",
            tenant="test",
            es_client=MockESClient(mock_es_scroll=es_scroll_frame),
            order_id_cache_path="s3://test.dev.steeleye.co/aries/ingest/order_aladdin_v2/"
            "2025/02/10/random_id/file_splitter_by_criteria/"
            "instrument_cache.json",
            source_frame=source_frame,
        )

        class_instance.target_df = target_frame
        class_instance.cancelled_quantity_diff_by_order_id_map = {"order_id_3": 30}
        class_instance.price_forming_data_traded_quantity()

        result_frame = pd.DataFrame(
            {
                add_prefix(
                    ModelPrefix.ORDER_STATE, OrderColumns.PRICE_FORMING_DATA_TRADED_QUANTITY
                ): [
                    100,
                    20,
                    30,
                ],
            },
            index=[10, 11, 12],
        )

        pd.testing.assert_frame_equal(
            class_instance.target_df.loc[
                :,
                [
                    add_prefix(
                        ModelPrefix.ORDER_STATE, OrderColumns.PRICE_FORMING_DATA_TRADED_QUANTITY
                    )
                ],
            ],
            result_frame,
            check_dtype=False,
        )

    def test_order_class(self):
        create_and_add_objects_to_s3_bucket(bucket_name=BUCKET_NAME, source_path=LOCAL_BUCKET_PATH)
        source_frame = pd.DataFrame(
            {
                add_prefix(FileTypes.TRANSACTION, TransactionSourceColumns.EXEC_CPCY_TYPE): [
                    "icross",
                    "iCross",
                    pd.NA,
                    "bar",
                    "foo",
                ],
                add_prefix(FileTypes.ORDER_DETAILS, OrderDetailSourceColumns.ORIG_ORDER_ID): [
                    "orig_order_id_x",
                    "orig_order_id_y",
                    "orig_order_id_z",
                    pd.NA,
                    pd.NA,
                ],
                add_prefix(FileTypes.ORDER, OrderSourceColumns.ORDER_STATUS): [
                    "status",
                ]
                * 3
                + ["F"] * 2,
                add_prefix(FileTypes.ORDER, OrderSourceColumns.OWNER_TYPE): [pd.NA] * 5,
                add_prefix(FileTypes.TRANSACTION, TransactionSourceColumns.SEC_TICKER): [
                    "ticker",
                ]
                * 3
                + ["X123", "ABC OTC FOO"],
            }
        )
        expected_result = [
            "iCross",
            "iCross",
            pd.NA,
            pd.NA,
            "OTC",
        ]

        class_instance = OrderAladdinV2ClientSideMappings(
            source_file_uri="foo",
            file_uri="s3://test.dev.steeleye.co/aries/ingest/order_aladdin_v2/"
            "2025/02/10/S6gRboG5aQ870xfy87l_v/file_splitter_by_criteria/"
            "foo_EQ.20241103_market_side_orders_batch_0.csv",
            tenant="test",
            es_client=MockESClient(mock_es_scroll=pd.DataFrame()),
            order_id_cache_path="s3://test.dev.steeleye.co/aries/ingest/order_aladdin_v2/"
            "2025/02/10/random_id/file_splitter_by_criteria/"
            "instrument_cache.json",
            source_frame=source_frame,
        )

        result = class_instance._order_class()
        assert result.tolist() == expected_result

    def test_id(self):
        create_and_add_objects_to_s3_bucket(bucket_name=BUCKET_NAME, source_path=LOCAL_BUCKET_PATH)
        source_frame = pd.DataFrame(
            {
                add_prefix(FileTypes.ORDER_DETAILS, OrderDetailSourceColumns.ORIG_ORDER_ID): [
                    "orig_order_id_x",
                    "orig_order_id_y",
                    pd.NA,
                    pd.NA,
                    pd.NA,
                ],
                add_prefix(FileTypes.ORDER_DETAILS, OrderDetailSourceColumns.ORDER_ID): [
                    "order_id_x",
                    "order_id_y",
                    "order_id_z",
                    "order_id_4",  # this order id is duplicated
                    "order_id_4",  # this order id is duplicated
                ],
                add_prefix(FileTypes.ORDER_DETAILS, OrderDetailSourceColumns.PORTFOLIO_ID): [
                    "portfolio_id_x",
                    "portfolio_id_y",
                    "portfolio_id_z",
                    "portfolio_id_4",  # this portfolio id is duplicated
                    "portfolio_id_4",  # this portfolio id is duplicated
                ],
                add_prefix(FileTypes.ORDER_DETAILS, "__swarm_raw_index__"): [
                    str(x) for x in list(range(5))
                ],
                add_prefix(FileTypes.ORDER, OrderSourceColumns.ORDER_STATUS): [
                    "status",
                    "status",
                    "F",
                    "F",
                    "F",
                ],
                add_prefix(FileTypes.ORDER, OrderSourceColumns.OWNER_TYPE): [pd.NA] * 5,
                add_prefix(FileTypes.TRANSACTION, TransactionSourceColumns.SEC_TICKER): [
                    "ticker",
                    "ticker",
                    "ABC OTC FOO",
                    "X123",
                    "X123",
                ],
            }
        )
        expected_result = [
            "C|order_id_x_orig_order_id_x_portfolio_id_x",
            "C|order_id_y_orig_order_id_y_portfolio_id_y",
            "C|order_id_z_portfolio_id_z",
            "C|order_id_4_portfolio_id_4_3",
            "C|order_id_4_portfolio_id_4_4",
        ]

        class_instance = OrderAladdinV2ClientSideMappings(
            source_file_uri="foo",
            file_uri="s3://test.dev.steeleye.co/aries/ingest/order_aladdin_v2/"
            "2025/02/10/S6gRboG5aQ870xfy87l_v/file_splitter_by_criteria/"
            "foo_EQ.20241103_market_side_orders_batch_0.csv",
            tenant="test",
            es_client=MockESClient(mock_es_scroll=pd.DataFrame()),
            order_id_cache_path="s3://test.dev.steeleye.co/aries/ingest/order_aladdin_v2/"
            "2025/02/10/random_id/file_splitter_by_criteria/"
            "instrument_cache.json",
            source_frame=source_frame,
        )

        result = class_instance._id()
        assert result[add_prefix(ModelPrefix.ORDER, OrderColumns.ID)].tolist() == expected_result
        assert (
            result[add_prefix(ModelPrefix.ORDER_STATE, OrderColumns.ID)].tolist() == expected_result
        )

    def test_average_price_cache(self):
        create_and_add_objects_to_s3_bucket(bucket_name=BUCKET_NAME, source_path=LOCAL_BUCKET_PATH)
        source_frame = pd.DataFrame(
            {
                add_prefix(FileTypes.ORDER, OrderSourceColumns.AVG_PRICE): [
                    12.7,
                    pd.NA,  # validate that null prices won't break anything
                    0.7,  # this will get overridden by the cache value
                    19.1,
                    15.0,
                ],
                add_prefix(FileTypes.ORDER_DETAILS, OrderDetailSourceColumns.ORIG_ORDER_ID): [
                    "orig_order_id_x",
                    "orig_order_id_y",
                    "orig_order_id_z",
                    "orig_order_id_q",
                    "orig_order_id_w",
                ],
                add_prefix(FileTypes.ORDER_DETAILS, OrderDetailSourceColumns.ORDER_ID): [
                    "order_id_x",
                    "order_id_y",
                    "order_id_z",  # this order id is present in the cache
                    "order_id_q",
                    "order_id_w",
                ],
                add_prefix(FileTypes.ORDER, OrderSourceColumns.ORDER_STATUS): [
                    "status",
                ]
                * 5,
                add_prefix(FileTypes.ORDER, OrderSourceColumns.OWNER_TYPE): [pd.NA] * 5,
                add_prefix(FileTypes.TRANSACTION, TransactionSourceColumns.SEC_TICKER): [
                    "ticker",
                ]
                * 5,
            }
        )
        expected_result = [
            12.7,
            pd.NA,
            2.0,
            19.1,
            15.0,
        ]

        class_instance = OrderAladdinV2ClientSideMappings(
            source_file_uri="foo",
            file_uri="s3://test.dev.steeleye.co/aries/ingest/order_aladdin_v2/"
            "2025/02/10/S6gRboG5aQ870xfy87l_v/file_splitter_by_criteria/"
            "foo_FI.20241103_market_side_orders_batch_0.csv",
            tenant="test",
            es_client=MockESClient(mock_es_scroll=pd.DataFrame()),
            order_id_cache_path="s3://test.dev.steeleye.co/aries/ingest/order_aladdin_v2/"
            "2025/02/10/random_id/file_splitter_by_criteria/"
            "instrument_cache.json",
            source_frame=source_frame,
        )

        class_instance.price_forming_data_price()
        assert (
            class_instance.target_df[OrderColumns.PRICE_FORMING_DATA_PRICE].tolist()
            == expected_result
        )
