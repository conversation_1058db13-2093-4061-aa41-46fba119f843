import boto3
from pathlib import Path


def create_and_add_objects_to_s3_bucket(bucket_name: str, source_path: Path):
    """Recreate the s3 bucket, and copy all the files from `LOCAL_BUCKET_PATH`
    to s3 mocked bucket.

    :param bucket_name: Bucket name of Mock S3 bucket
    :param source_path: Local path to the files to be uploaded
    :return: None
    """

    # Create bucket
    s3 = boto3.client("s3", region_name="us-east-1")

    s3.create_bucket(Bucket=bucket_name)

    for file_ in source_path.rglob("*"):
        if file_.is_file():
            _path = file_.as_posix().replace(f"{source_path}/", "")

            with open(file_, "rb") as f:
                s3.put_object(Bucket=bucket_name, Key=_path, Body=f.read())
