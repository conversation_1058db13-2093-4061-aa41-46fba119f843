import matplotlib.pyplot as plt
import re
from pathlib import Path

HAS_FILESIZE = False
MEMORYTRACKER_FILE = "memorytracker.txt"
FILESIZE_FILE = "filesize.txt"
CURRENT_PATH = Path(__file__).parent

file_sizes = []
if HAS_FILESIZE:
    with open(f"{CURRENT_PATH}/{FILESIZE_FILE}", "r") as file:
        for line in file:
            # Extract the file size (second column)
            file_size = float(line.split()[1])
            file_sizes.append(file_size)

# Extract both memory values and indices where "Processing call" appears
memory_values = []
processing_indices = []
file_sizes_expanded = []
file_size_counter = -1
file_start = False
CURRENT_PATH = Path(__file__).parent.parent
with open(f"{CURRENT_PATH}/{MEMORYTRACKER_FILE}", "r") as file:
    for index, line in enumerate(file):
        if re.search(r"Processing call 0", line):
            file_start = True
        if re.search(r"Count of waveforms uploaded", line):
            break
        if file_start:
            mem_match = re.search(r"Memory consumption: (\d+) MB", line)
            if mem_match:
                memory_values.append(float(mem_match.group(1)))
                if HAS_FILESIZE:
                    file_sizes_expanded.append(file_sizes[file_size_counter])

            # Check for processing call logs
            if re.search(r"Processing call \d+", line):
                processing_indices.append(len(memory_values) - 1)
                file_size_counter += 1

if HAS_FILESIZE:
    # Generate the updated line graph
    fig, ax1 = plt.subplots(figsize=(10, 5))

    # Primary Y-axis (Memory Consumption)
    ax1.set_xlabel("Time (arbitrary units)")
    ax1.set_ylabel("Memory Consumption (MB)", color="b")
    ax1.plot(memory_values, marker=None, linestyle="-", color="b", label="Memory Consumption (MB)")
    ax1.scatter(
        processing_indices,
        [memory_values[i] for i in processing_indices],
        color="r",
        s=50,
        label="Processing Call Event",
        edgecolors="black",
    )
    ax1.tick_params(axis="y", labelcolor="b")
    ax1.grid(True)

    # Secondary Y-axis (File Sizes)
    ax2 = ax1.twinx()
    ax2.set_ylabel("File Size (MB)", color="green")
    ax2.plot(file_sizes_expanded, linestyle="-", color="green", label="File Size (MB)")
    ax2.tick_params(axis="y", labelcolor="green")

    # Combine legends from both axes
    lines_1, labels_1 = ax1.get_legend_handles_labels()
    lines_2, labels_2 = ax2.get_legend_handles_labels()
    ax1.legend(lines_1 + lines_2, labels_1 + labels_2, loc="upper left")
else:
    # Generate the updated line graph
    plt.figure(figsize=(10, 5))
    # Highlight processing call points with red dots
    plt.scatter(
        processing_indices,
        [memory_values[i] for i in processing_indices],
        color="r",
        s=50,
        label="Processing Call Event",
        edgecolors="black",
    )
    plt.plot(memory_values, marker=None, linestyle="-", color="b", label="Memory Consumption (MB)")
    plt.plot(file_sizes_expanded, label="File Size (MB)", color="green")
    plt.xlabel("Time (arbitrary units)")
    plt.ylabel("Memory Consumption (MB)")

plt.title("Memory Consumption Over Time with Processing Call Markers")
plt.legend()
plt.grid(True)
plt.show()
