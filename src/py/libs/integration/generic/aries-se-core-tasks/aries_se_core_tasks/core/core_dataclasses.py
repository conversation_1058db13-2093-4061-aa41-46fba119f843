import pandas as pd
from dataclasses import dataclass
from enum import auto
from pathlib import Path
from se_enums.core import BaseStrEnum
from typing import List, Optional


@dataclass
class FrameProducerResult:
    frame: pd.DataFrame
    batch_index: Optional[int] = None


@dataclass
class TransformResult:
    target: pd.DataFrame
    batch_index: Optional[int] = None


class CloudAction(BaseStrEnum):
    UPLOAD = auto()
    DOWNLOAD = auto()


class S3Action(BaseStrEnum):
    # use this enum not the S3Action from `se-core-tasks` as we want to leverage BaseStrEnum
    UPLOAD = auto()
    DOWNLOAD = auto()


@dataclass
class CloudFile:
    bucket_name: str
    key_name: str
    action: CloudAction | S3Action
    file_path: Optional[Path] = None
    raw_bytes: Optional[bytes] = None
    bytes: Optional[int] = 0
    last_modified: Optional[float] = 0.0  # seconds since UNIX epoch

    def __post_init__(self):
        if self.file_path and self.raw_bytes:
            raise ValueError("file_path and raw_bytes cannot be used together")

        if not self.file_path and not self.raw_bytes:
            raise ValueError("At-least one of file_path or raw_bytes must be provided")


@dataclass
class S3File(CloudFile):
    # use this dataclass not the S3File from `se-core-tasks` as we
    # want to leverage BaseStrEnum for `action`
    pass


@dataclass
class CloudTargetResult:
    targets: List[CloudFile | S3File]


@dataclass
class S3TargetResult(CloudTargetResult):
    # use this dataclass not the S3TargetResult from `se-core-tasks`
    # as we want to leverage BaseStrEnum for `targets.[].action`
    pass


@dataclass(frozen=True)
class SerializerResult:
    empty: bool
    index: pd.Index
    pickle_path: Path
    shape: tuple[int, int]

    def frame(self) -> pd.DataFrame:
        return pd.read_pickle(self.pickle_path)  # type: ignore
