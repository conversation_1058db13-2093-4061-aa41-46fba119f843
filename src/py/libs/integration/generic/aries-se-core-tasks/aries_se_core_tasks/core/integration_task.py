import json
import logging
from abc import abstractmethod
from aries_se_core_tasks.utilities.serializer import serializer
from threading import Lock
from typing import Any, Optional

logger_ = logging.getLogger(__name__)
lock = Lock()


class IntegrationTask:
    """Wrapper Class for all Data Integration Tasks (akin to Prefect Tasks)
    that require tracking app metrics and/or audit information."""

    def __init__(
        self,
        app_metrics_path: Optional[str] = None,
        audit_path: Optional[str] = None,
    ):
        self.app_metrics_path = app_metrics_path
        self.audit_path = audit_path

    @serializer
    def run(self, *args, **kwargs: Any) -> Any:
        return self._run(*args, **kwargs)

    @abstractmethod
    def _run(self, *args, **kwargs: Any) -> Any:
        raise NotImplementedError

    def update_app_metrics(self, field: str, value: int):
        """Update the `app_metrics.json` file that is created for each Flow
        run. Increment the `field` key with the `value` value. Concurrent
        access to the `app_metrics.json` file is managed by `threading.lock()`,
        meaning that the file will only be interacted with, one thread at a
        time.

        :param field: App Metric field that will be updated (
        must be listed in the app_metrics_template of the Aries Task)
        :param value: Integer value to increment the `field` with
        i.e. a count of something, or timestamp in ms
        """

        if not self.app_metrics_path:
            return

        with lock:
            with open(self.app_metrics_path, "r") as file:
                report = json.loads(file.read())

            # if the field does not exist we want to raise a KeyError as all fields should
            # be specified in the `app_metrics_template` of the IntegrationAriesTask object
            report[field] += value

            with open(self.app_metrics_path, "w") as file:
                file.write(json.dumps(report))
