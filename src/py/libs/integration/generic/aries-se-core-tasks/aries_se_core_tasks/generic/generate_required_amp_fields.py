import pandas as pd
from aries_se_core_tasks.core.exception import TaskException
from aries_se_core_tasks.core.integration_task import IntegrationTask
from aries_se_core_tasks.core.params import TaskParams
from aries_se_core_tasks.static import MetaModel
from indict import Indict
from pydantic import Field
from se_elastic_schema.elastic_schema.core.steeleye_schema_model import SteelEyeSchemaBaseModelES8
from se_elastic_schema.models import find_model
from typing import Optional, Type


class FailIfModelCouldNotBeResolved(TaskException):
    pass


class Params(TaskParams):
    data_model: MetaModel | Type[SteelEyeSchemaBaseModelES8] = Field(
        ...,
        description="Schema Meta Model name E.g.: Call, Email "
        "OR fully qualified Schema AbstractSteeleyeModel",
    )


class GenerateRequiredAmpFields(IntegrationTask):
    """This task generates the &id, &hash, &key, &uniqueProps and &model for
    cases where they are required before ApplyMeta."""

    def _run(self, source_frame: pd.DataFrame, params: Params, **kwargs) -> pd.DataFrame:
        return self.process(
            source_frame=source_frame,
            params=params,
        )

    @classmethod
    def process(cls, source_frame: pd.DataFrame, params: Params) -> pd.DataFrame:
        target = pd.DataFrame(index=source_frame.index)

        if isinstance(params.data_model, MetaModel):
            data_model = find_model(params.data_model)
        else:
            data_model = params.data_model

        if data_model is None:
            raise FailIfModelCouldNotBeResolved(f"{params.data_model} could not be resolved")

        fields_list = ["&id", "&hash", "&key", "&model", "&uniqueProps"]
        target[fields_list] = source_frame.apply(
            lambda row: cls._get_id_and_hash(
                row=row,
                data_model=data_model,
            ),
            axis=1,
            result_type="expand",
        )
        return target

    @staticmethod
    def _get_id_and_hash(row: pd.Series, data_model: Type[SteelEyeSchemaBaseModelES8]):
        record = (
            Indict(
                obj=row,
                from_dataframe_params={"date_format": "iso", "orient": "records"},
            )
            .remove_empty()
            .unflatten()
            .to_dict()
        )
        record = data_model(**record)
        record.apply_steeleye_meta()
        return record.id__, record.hash__, record.key__, record.model__, record.uniqueProps__


def run_generate_required_amp_fields(
    params: Params,
    source_frame: pd.DataFrame,
    app_metrics_path: Optional[str] = None,
    audit_path: Optional[str] = None,
    **kwargs,
) -> pd.DataFrame:
    task = GenerateRequiredAmpFields(app_metrics_path=app_metrics_path, audit_path=audit_path)

    return task.run(source_frame=source_frame, params=params, **kwargs)  # type: ignore[no-any-return]
