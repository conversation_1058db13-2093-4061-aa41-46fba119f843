from se_enums.core import BaseStrEnum


class MetaModel(BaseStrEnum):
    ACCOUNT_PERSON = "AccountPerson"
    ATTACHMENT = "Attachment"
    CALL = "Call"
    CHAT_EVENT = "ChatEvent"
    EMAIL = "Email"
    INFO_BARRIER_PROJECT = "InfoBarrierProject"
    MARKET_COUNTERPARTY = "MarketCounterparty"
    MARKET_PERSON = "MarketPerson"
    MARKET_SUGGESTION = "MarketSuggestion"
    MEETING = "Meeting"
    MESSAGE = "Message"
    ORDER = "Order"
    QUARANTINED_ORDER = "QuarantinedOrder"
    POSITION = "Position"
    RTS22_TRANSACTION = "RTS22Transaction"
    QUARANTINED_RTS22_TRANSACTION = "QuarantinedRTS22Transaction"
    SINK_RECORD_AUDIT = "SinkRecordAudit"
    TEXT = "Text"
    TRANSCRIPT = "Transcript"
    # Waveform is not an ES model, but has been added here, so it can be
    # used in Conductor JSON files which are used in finish_flow
    WAVEFORM = "Waveform"

    # CaseRecord Models
    CASE_EMAIL = "CaseEmail"
    CASE_CALL = "CaseCall"
    CASE_MEETING = "CaseMeeting"
    CASE_TEXT = "CaseText"
    CASE_MESSAGE = "CaseMessage"
    CASE_ORDER = "CaseOrder"

    # Surveillance Alert models
    COMMUNICATION_ALERT = "CommunicationAlert"
    ORDER_ALERT = "OrderAlert"
    MARKET_ABUSE_SCENARIO_TAG = "MarketAbuseScenarioTag"
    MARKET_ABUSE_ALERT = "MarketAbuseAlert"

    # Postgres Models
    COMMENT = "Comment"
    PARTY = "Party"
    RESTRICTION_PARTY_ASSOCIATION = "RestrictionParty"
    RESTRICTED_LIST = "RestrictedList"
    RESTRICTION = "Restriction"
    WATCH_REASON = "WatchReason"
