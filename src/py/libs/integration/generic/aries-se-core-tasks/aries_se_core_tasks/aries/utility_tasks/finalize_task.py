from aries_se_core_tasks.aries.utility_tasks.finish_flow import add_nested_params
from aries_se_core_tasks.io.create_ndjson_path import create_ndjson_path
from aries_se_core_tasks.io.write.write_ndjson import run_write_ndjson
from aries_se_core_tasks.utilities.serializer import serializer
from se_enums.elastic_search import EsActionEnum


@serializer
def create_path_and_upload_model_results(
    aries_task_input,
    tenant_bucket,
    model,
    final_transformed_df,
    app_metrics_path,
    audit_path,
    es_action: EsActionEnum = EsActionEnum.INDEX,
):
    # Create the appropriate path where the df ndjson result is to be uploaded
    ndjson_path = create_ndjson_path(
        tenant_bucket=tenant_bucket,
        aries_task_input=aries_task_input,
        model=model,
    )

    if final_transformed_df.empty:
        ndjson_path = None  # type: ignore[assignment]
    else:
        # Write the data frame into a ndjson file to the generated ndjson path
        run_write_ndjson(
            source_serializer_result=final_transformed_df,
            output_filepath=ndjson_path,
            audit_output=True,
            app_metrics_path=app_metrics_path,
            audit_path=audit_path,
        )

    # Create the final output param
    output = add_nested_params(
        file_uri=ndjson_path,
        es_action=es_action,
        data_model=model.get_reference().get_qualified_reference(),
        ignore_empty_file_uri=True,
    )
    return output
