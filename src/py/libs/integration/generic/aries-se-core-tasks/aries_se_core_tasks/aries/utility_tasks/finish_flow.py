import json
from typing import Any, Dict, List, Optional


def finish_flow(
    result_path: str,
    result_data: Optional[Dict[str, Any]] = None,
    **kwargs,
):
    """Task must run at the end of all Flows. The goal is to write a local
    `result_path` JSON file with the results of the Flow. This is the simplest
    way to convey the results of a Flow outside its scope, to an AriesTask. All
    input kwargs will be written into the JSON file as is.

    How to use it:
    - A typical Integration Flow will produce an NDJSON file of
    transformed records and upload it to S3
    - The Task that achieves this will return a `string` depicting the filepath
    - The result of that Task can be passed to `FinishFlow` to
    produce a JSON file with the filepath
    - With `result_data` it is also possible to pass directly a
    dictionary with the results instead of relying on kwargs

    Example:

    [INPUT]
    result_path = ".../result.json"
    ndjson_filepath = "s3://test.dev.steeleye.co/lake/transformed_result.ndjson"
    activate_feature_xyz = False

    [OUTPUT]
    A JSON file will be written in `result_path` with the following content:
    {
        "ndjson_filepath" : "s3://test.dev.steeleye.co/lake/transformed_result.ndjson",
        "activate_feature_xyz" : false
    }

    :param result_path: Absolute location of the JSON file to write the results to
    :param result_data: Result to write to the JSON file
    :param kwargs: Keyword Arguments that will be written to the JSON file
    """

    result_data = result_data or {}
    kwargs = kwargs or {}

    result_data.update(kwargs)

    with open(result_path, "w") as file:
        file.write(json.dumps(result_data))


def add_nested_params(**kwargs) -> Dict[str, Any]:
    return dict(params=kwargs)


def create_io_params_list(iterable_item: Dict[str, List], **kwargs):
    """This method is supposed to be used in flows where we intend to use
    dynamic fork join.

    It expects two args. The iterable item is the part that changes and
    the rest of the common items in the expected params are sent as
    keyword arguments.
    For example:
        if iterable_item is a dict like this:
            { "param1": [1,2,3], "param2": [4,5,6] }
        and kwargs is a dict like this (from the keyword arguments):
            { "param3": 7, "param4": 8 }
        then the output will be a dict like this:
            { "io_params_list": [
            { "io_param": { "params": { "param1": 1, "param2": 4, "param3": 7, "param4": 8 } } },
            { "io_param": { "params": { "param1": 2, "param2": 5, "param3": 7, "param4": 8 } } },
            { "io_param": { "params": { "param1": 3, "param2": 6, "param3": 7, "param4": 8 } } }
            ] }
    :param iterable_item: A dict with the key as the param name and the value as a list of values
    :param kwargs: A dict with the key as the param name and the value as the param value
    :return: A dict with the key as "io_params" and the value as a list of dicts with
            the key as "params" and the value as a dict of params
    """
    list_of_io_params = []
    keys_to_add = iterable_item.keys()

    # We're creating a list of dicts with each list having the same keys as the iterable_item
    additional_fields = [
        dict(zip(keys_to_add, vals)) for vals in zip(*(iterable_item[k] for k in keys_to_add))
    ]
    for fields in additional_fields:
        # Instead of using the update method, we're using the dict method to merge the two dicts
        # because the update method will update the original dict and we don't want that
        list_of_io_params.append({"io_param": {"params": dict(kwargs, **fields)}})
    return dict(io_params_list=list_of_io_params)
