import logging
from aries_se_core_tasks.core.generic_app_metrics_enum import GenericAppMetricsEnum
from aries_se_core_tasks.core.integration_task import IntegrationTask
from integration_audit.auditor import convert_swarm_audit_to_aries_workflow_level_audit
from se_core_tasks.core.core_dataclasses import FileSplitterResult
from se_core_tasks.io.read.csv_file_splitter import CsvFileSplitter as CoreCsvFileSplitter
from se_core_tasks.io.read.csv_file_splitter import Params
from typing import List, Optional, Union

logger = logging.getLogger(__name__)


class CsvFileSplitter(IntegrationTask):
    def _run(
        self,
        streamed: bool,
        params: Params,
        csv_path: str,
        realm: str,
        sources_dir: str,
        raw_index_name: str,
        skiprows: Union[int, List[int]],
        **kwargs,
    ) -> List[FileSplitterResult]:
        lib_task = CoreCsvFileSplitter()

        result: List[FileSplitterResult] = lib_task.execute(
            params=params,
            csv_path=csv_path,
            realm=realm,
            sources_dir=sources_dir,
            raw_index_name=raw_index_name,
            skiprows=skiprows,
            **kwargs,
        )

        convert_swarm_audit_to_aries_workflow_level_audit(
            task=lib_task, streamed=streamed, audit_path=self.audit_path
        )

        if params.audit_input_rows and len(result) > 0 and result[0].input_total_count is not None:
            # Auditing the number of records which were present in the input file
            self.update_app_metrics(
                field=GenericAppMetricsEnum.INPUT_COUNT,
                value=result[0].input_total_count,
            )

        return result


def run_csv_file_splitter(
    streamed: bool,
    params: Params,
    csv_path: str,
    realm: Optional[str] = None,
    sources_dir: Optional[str] = None,
    raw_index_name: str = "__swarm_raw_index__",
    skiprows: Optional[Union[int, List[int]]] = None,
    app_metrics_path: Optional[str] = None,
    audit_path: Optional[str] = None,
    **kwargs,
):
    task = CsvFileSplitter(app_metrics_path=app_metrics_path, audit_path=audit_path)

    return task.run(
        streamed=streamed,
        params=params,
        csv_path=csv_path,
        realm=realm,
        sources_dir=sources_dir,
        raw_index_name=raw_index_name,
        skiprows=skiprows,
        **kwargs,
    )
