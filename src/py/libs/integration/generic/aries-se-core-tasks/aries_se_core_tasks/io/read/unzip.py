# type: ignore
import logging
from aries_se_core_tasks.core.integration_task import IntegrationTask
from pathlib import Path
from se_core_tasks.core.core_dataclasses import ExtractPathResult
from se_core_tasks.io.read.unzip import Params
from se_core_tasks.io.read.unzip import run_unzip as run_task_from_lib
from typing import List, Optional, Union

logger = logging.getLogger(__name__)


class Unzip(IntegrationTask):
    def _run(
        self,
        extract_result: Union[ExtractPathResult, str, Path],
        params: Params,
        **kwargs,
    ) -> Union[ExtractPathResult, List[ExtractPathResult]]:
        result = run_task_from_lib(extract_result=extract_result, params=params, **kwargs)

        extract_result_as_path = (
            extract_result.path
            if isinstance(extract_result, ExtractPathResult)
            else Path(extract_result)
            if isinstance(extract_result, str)
            else extract_result
        )

        if isinstance(result, List):
            return [
                ExtractPathResult(path=x.path, parent_file_path=extract_result_as_path)
                for x in result
            ]
        else:
            return ExtractPathResult(path=result.path, parent_file_path=extract_result_as_path)


def run_unzip(
    extract_result: Union[ExtractPathResult, str, Path],
    params: Params = Params(),
    app_metrics_path: Optional[str] = None,
    audit_path: Optional[str] = None,
    **kwargs,
):
    task = Unzip(app_metrics_path=app_metrics_path, audit_path=audit_path)
    return task.run(params=params, extract_result=extract_result, **kwargs)
