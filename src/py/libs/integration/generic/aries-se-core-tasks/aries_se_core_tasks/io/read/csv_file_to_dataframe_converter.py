import pandas as pd
from aries_se_core_tasks.core.integration_task import IntegrationTask
from pathlib import Path
from se_core_tasks.io.read.csv_file_to_dataframe_converter import Params
from se_core_tasks.io.read.csv_file_to_dataframe_converter import (
    run_csv_file_to_dataframe_converter as run_task_from_lib,
)


class CsvFileToDataframeConverter(IntegrationTask):
    """It converts a given csv file to a dataframe."""

    def _run(
        self,
        path: Path,
        params: Params,
        **kwargs,
    ) -> pd.DataFrame:
        result: pd.DataFrame = run_task_from_lib(path=path, params=params)

        return result


def run_csv_file_to_dataframe_converter(
    params: Params,
    path: Path,
    **kwargs,
) -> pd.DataFrame:
    task = CsvFileToDataframeConverter()

    result: pd.DataFrame = task.run(
        params=params,
        path=path,
        **kwargs,
    )

    return result
