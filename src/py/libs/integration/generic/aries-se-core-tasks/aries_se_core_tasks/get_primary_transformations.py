# type: ignore
import logging
import pandas as pd
from aries_se_comms_tasks.feeds.meeting.microsoft.outlook_calendar.static import (
    OUTLOOK_CALENDAR_FLOW_NAME,
)
from aries_se_comms_tasks.feeds.meeting.zoom.static import (
    MEETING_MAPPINGS_NAME as ZOOM_MEETINGS_MEETING_MAPPINGS_NAME,
)
from aries_se_comms_tasks.feeds.meeting.zoom.static import (
    MESSAGE_MAPPINGS_NAME as ZOOM_MEETINGS_MESSAGE_MAPPINGS_NAME,
)
from aries_se_comms_tasks.feeds.meeting.zoom.static import (
    TRANSCRIPT_MAPPINGS_NAME as ZOOM_MEETINGS_TRANSCRIPT_MAPPINGS_NAME,
)
from aries_se_comms_tasks.feeds.message.asc_chat.static import ASC_CHAT_FLOW_NAME
from aries_se_comms_tasks.feeds.message.deepview_chat.static import (
    FLOW_NAME as DEEPVIEW_CHAT_MESSAGE_FLOW_NAME,
)
from aries_se_comms_tasks.feeds.message.enmacc_chat.static import ENMACC_CHAT_FLOW_NAME
from aries_se_comms_tasks.feeds.message.ice_chat.static import (
    CHAT_EVENT_MAPPINGS_NAME as ICE_CHAT_CHAT_EVENT_MAPPINGS_NAME,
)
from aries_se_comms_tasks.feeds.message.ice_chat.static import (
    MESSAGE_MAPPINGS_NAME as ICE_CHAT_MESSAGE_MAPPINGS_NAME,
)
from aries_se_comms_tasks.feeds.message.ice_vantage_chat.static import (
    ICE_VANTAGE_CHAT_CHAT_EVENT_MAPPINGS_NAME,
    ICE_VANTAGE_CHAT_MESSAGE_MAPPINGS_NAME,
)
from aries_se_comms_tasks.feeds.message.leapxpert_chat.static import (
    CHAT_EVENT_MAPPINGS_NAME as LEAPXPERT_CHAT_CHAT_EVENT_MAPPINGS_NAME,
)
from aries_se_comms_tasks.feeds.message.leapxpert_chat.static import (
    MESSAGE_MAPPINGS_NAME as LEAPXPERT_CHAT_MESSAGE_MAPPINGS_NAME,
)
from aries_se_comms_tasks.feeds.message.ms_teams_chat.ing.static import (
    ING_MS_TEAMS_CHAT_EVENT_MAPPINGS_NAME,
    ING_MS_TEAMS_MESSAGE_MAPPINGS_NAME,
)
from aries_se_comms_tasks.feeds.message.o2_sms.static import FLOW_NAME as O2_SMS_MESSAGE_FLOW_NAME
from aries_se_comms_tasks.feeds.message.onsim_sms.static import ONSIM_SMS_FLOW_NAME
from aries_se_comms_tasks.feeds.message.refinitiv_fxt_chat.static import (
    FLOW_NAME as REFINITIV_FXT_CHAT_FLOW_NAME,
)
from aries_se_comms_tasks.feeds.message.slack_chat.static import (
    CHAT_EVENT_MAPPINGS_NAME as SLACK_CHAT_CHAT_EVENT_MAPPINGS_NAME,
)
from aries_se_comms_tasks.feeds.message.slack_chat.static import (
    MESSAGE_MAPPINGS_NAME as SLACK_CHAT_MESSAGE_MAPPINGS_NAME,
)
from aries_se_comms_tasks.feeds.message.snippet_sentry_chat.static import (
    FLOW_NAME as SNIPPET_SENTRY_CHAT_FLOW_NAME,
)
from aries_se_comms_tasks.feeds.message.steeleye_universal_chat.static import (
    CHAT_EVENT_MAPPINGS_NAME as STEELEYE_UNIVERSAL_CHAT_CHAT_EVENT_MAPPINGS_NAME,
)
from aries_se_comms_tasks.feeds.message.steeleye_universal_chat.static import (
    MESSAGE_MAPPINGS_NAME as STEELEYE_UNIVERSAL_CHAT_MESSAGE_MAPPINGS_NAME,
)
from aries_se_comms_tasks.feeds.message.symphony_xml_chat.static import (
    CHAT_EVENT_MAPPINGS as SYMPHONY_XML_CHAT_CHAT_EVENT,
)
from aries_se_comms_tasks.feeds.message.symphony_xml_chat.static import (
    MESSAGE_MAPPINGS as SYMPHONY_XML_CHAT_MESSAGE,
)
from aries_se_comms_tasks.feeds.message.telemessage.static import TELEMESSAGE_MESSAGE_TRANSFORM
from aries_se_comms_tasks.feeds.message.truphone_message.static import (
    FLOW_NAME as TRUPHONE_MESSAGE_FLOW_NAME,
)
from aries_se_comms_tasks.feeds.message.voxsmart_message.static import (
    VOXSMART_MESSAGE_MAPPINGS_NAME,
)
from aries_se_comms_tasks.feeds.voice.a1_voice.static import A1_VOICE_FLOW_NAME
from aries_se_comms_tasks.feeds.voice.asc_voice.static import FLOW_NAME as ASC_VOICE_FLOW_NAME
from aries_se_comms_tasks.feeds.voice.avaya_voice.static import (
    FLOW_NAME as AVAYA_VOICE_FLOW_NAME,
)
from aries_se_comms_tasks.feeds.voice.bp.asc_teams.static import BP_ASC_TEAMS_VOICE_FLOW_NAME
from aries_se_comms_tasks.feeds.voice.bp.cloud9.static import BP_CLOUD9_VOICE_FLOW_NAME
from aries_se_comms_tasks.feeds.voice.bp.dubber.static import BP_DUBBER_VOICE_FLOW_NAME
from aries_se_comms_tasks.feeds.voice.bp.twilio.static import BP_TWILIO_VOICE_FLOW_NAME
from aries_se_comms_tasks.feeds.voice.bp.vodafone.static import BP_VODAFONE_VOICE_FLOW_NAME
from aries_se_comms_tasks.feeds.voice.callcabinet_feed_voice.static import (
    CALL_CABINET_FEED_VOICE_FLOW_NAME,
)
from aries_se_comms_tasks.feeds.voice.clarifygo_oi_voice.static import CLARIFYGO_OI_VOICE_FLOW_NAME
from aries_se_comms_tasks.feeds.voice.cloud9_voice.static import CLOUD9_VOICE_FLOW_NAME
from aries_se_comms_tasks.feeds.voice.commpeak_voice.static import COMMPEAK_VOICE_FLOW_NAME
from aries_se_comms_tasks.feeds.voice.dubber_voice.static import FLOW_NAME as DUBBER_VOICE_FLOW_NAME
from aries_se_comms_tasks.feeds.voice.gamma_horizon_voice.static import (
    GAMMA_HORIZON_VOICE_FLOW_NAME,
)
from aries_se_comms_tasks.feeds.voice.iv_voice.static import (
    IV_KERV_VOICE_FLOW_NAME,
    IV_REDBOX_FLOW_NAME,
    IV_UNIVERSAL_FLOW_NAME,
)
from aries_se_comms_tasks.feeds.voice.kerv_voice.static import KERV_VOICE_FLOW_NAME
from aries_se_comms_tasks.feeds.voice.luna_advance_voice.static import (
    FLOW_NAME as LUNA_ADVANCE_VOICE_MAPPINGS_NAME,
)
from aries_se_comms_tasks.feeds.voice.masergy_voice.static import (
    FLOW_NAME as MASERGY_VOICE_FLOW_NAME,
)
from aries_se_comms_tasks.feeds.voice.natterbox_voice.static import (
    FLOW_NAME as NATTERBOX_VOICE_FLOW_NAME,
)
from aries_se_comms_tasks.feeds.voice.nice_voice.static import FLOW_NAME as NICE_VOICE_FLOW_NAME
from aries_se_comms_tasks.feeds.voice.numonix_ix_cloud_voice.static import (
    FLOW_NAME as NUMONIX_IX_CLOUD_VOICE_FLOW_NAME,
)
from aries_se_comms_tasks.feeds.voice.o2_voice.static import FLOW_NAME as O2_VOICE_FLOW_NAME
from aries_se_comms_tasks.feeds.voice.onsim_voice.static import ONSIM_VOICE_FLOW_NAME
from aries_se_comms_tasks.feeds.voice.primodialler_voice.static import PRIMODIALLER_VOICE_FLOW_NAME
from aries_se_comms_tasks.feeds.voice.redbox_voice.static import REDBOX_FLOW_NAME
from aries_se_comms_tasks.feeds.voice.ringcentral_voice.static import (
    FLOW_NAME as RINGCENTRAL_VOICE_FLOW_NAME,
)
from aries_se_comms_tasks.feeds.voice.santander_voice.static import (
    FLOW_NAME as SANTANDER_VOICE_FLOW_NAME,
)
from aries_se_comms_tasks.feeds.voice.sgc_voice.static import FLOW_NAME as SGC_VOICE_FLOW_NAME
from aries_se_comms_tasks.feeds.voice.steeleye_universal_voice.steeleye_universal_static import (
    STEELEYE_UNIVERSAL_VOICE_FLOW_NAME,
)
from aries_se_comms_tasks.feeds.voice.telemessage.static import TELEMESSAGE_VOICE_TRANSFORM
from aries_se_comms_tasks.feeds.voice.teleware_voice.static import (
    FLOW_NAME as TELEWARE_VOICE_FLOW_NAME,
)
from aries_se_comms_tasks.feeds.voice.three_cx_voice.static import THREE_CX_VOICE_FLOW_NAME
from aries_se_comms_tasks.feeds.voice.truphone_voice.static import (
    FLOW_NAME as TRUPHONE_VOICE_FLOW_NAME,
)
from aries_se_comms_tasks.feeds.voice.verba_voice.static import (
    VERBA_CSV_FLOW_NAME as VERBA_VOICE_CSV_MAPPINGS_NAME,
)
from aries_se_comms_tasks.feeds.voice.verba_voice.static import (
    VERBA_XML_FLOW_NAME as VERBA_VOICE_XML_MAPPINGS_NAME,
)
from aries_se_comms_tasks.feeds.voice.via_voice.static import VIA_VOICE_FLOW_NAME
from aries_se_comms_tasks.feeds.voice.vibework_tollring_voice.static import (
    FLOW_NAME as VIBEWORK_TOLLRING_VOICE_FLOW_NAME,
)
from aries_se_comms_tasks.feeds.voice.vmo2_voice.static import VMO2_VOICE_FLOW_NAME
from aries_se_comms_tasks.feeds.voice.voip_voice.static import FLOW_NAME as VOICE_VOICE_FLOW_NAME
from aries_se_comms_tasks.feeds.voice.voxsmart_voice.static import VOXSMART_VOICE_MAPPINGS_NAME
from aries_se_comms_tasks.feeds.voice.wavenet_voice.static import (
    FLOW_NAME as WAVENET_VOICE_FLOW_NAME,
)
from aries_se_comms_tasks.feeds.voice.xima_voice.static import FLOW_NAME as XIMA_VOICE_FLOW_NAME
from aries_se_comms_tasks.feeds.voice.zoom_phone.static import (
    ZOOM_PHONE_VOICE_FLOW_NAME,
    ZOOM_PHONE_VOICE_TRANSCRIPT_FLOW_NAME,
)
from aries_se_comms_tasks.feeds.voice.zoom_trium.static import ZOOM_TRIUM_VOICE_FLOW_NAME
from aries_se_comms_tasks.info_barrier.info_barrier_project_mappings import (
    InfoBarrierProjectMappings,
)
from aries_se_comms_tasks.info_barrier.static import INFO_BARRIER_PROJECT_FLOW_NAME
from aries_se_comms_tasks.transform_maps import (
    chat_event_transform_maps,
    email_transform_maps,
    market_suggestion_transform_maps,
    meeting_transform_maps,
    message_transform_maps,
    transcript_transform_maps,
    voice_transform_maps,
)
from aries_se_comms_tasks.transform_maps.message_transform_maps import (
    leapxpert_chat_event_transform_map,
    leapxpert_message_transform_map,
    ms_teams_chat_transform_messages_map,
)
from aries_se_comms_tasks.transform_maps.text_transform_maps import kerv_text_transform_map
from aries_se_comms_tasks.transform_maps.voice_transform_maps import (
    luna_advance_voice_transform_map,
)
from aries_se_core_tasks.core.integration_task import IntegrationTask
from aries_se_trades_tasks.order.static import OrderWorkflowNames
from aries_se_trades_tasks.order.transformations import order_transform_maps
from aries_se_trades_tasks.order.transformations.feed.order_aladdin_v2.static import (
    ORDER_ALADDIN_V2_CLIENT_SIDE_FEED_NAME,
    ORDER_ALADDIN_V2_MARKET_SIDE_FEED_NAME,
)
from aries_se_trades_tasks.order.transformations.feed.order_eze_oms_soft.static import (
    ORDER_EZE_OMS_SOFT_ALLOCATIONS,
    ORDER_EZE_OMS_SOFT_EXECUTIONS,
    ORDER_EZE_OMS_SOFT_ORDERS,
)
from aries_se_trades_tasks.order.transformations.feed.order_feed_cfox.static import (
    ORDER_FEED_CFOX_HANDLER,
)
from aries_se_trades_tasks.tr.static import RTS22TransactionWorkflowNames
from aries_se_trades_tasks.transform_maps import rts22_transaction_transform_maps
from integration_generic_relational_tasks.restricted_list_transform.restricted_list_mappings import (  # noqa E501
    RestrictedListMappings,
)
from integration_generic_relational_tasks.restricted_list_transform.static import (
    RESTRICTED_LIST_FLOW_NAME,
)
from mymarket_tasks.feeds.person.avatrade_client_data_handler.static import (
    AVATRADE_CLIENT_DATA_HANDLER,
)
from mymarket_tasks.feeds.person.bp.static import MYMARKET_BP_PERSON_FLOW_NAME
from mymarket_tasks.feeds.person.slack.static import (
    MYMARKET_SLACK_ACCOUNT_PERSON_TRANSFORM,
    MYMARKET_SLACK_MARKET_SUGGESTION_TRANSFORM,
)
from mymarket_tasks.feeds.person.universal_steeleye_person.static import (
    MYMARKET_UNIVERSAL_STEELEYE_PERSON_HANDLER,
)
from mymarket_tasks.transform_maps import person_transform_maps
from se_core_tasks.abstractions.transformations.transform_map import TransformMap
from se_trades_tasks.order.transformations import (
    order_transform_maps as order_trades_transform_maps,
)
from typing import Dict, List, Optional

logger = logging.getLogger(__name__)


class GetPrimaryTransformations(IntegrationTask):
    """This task serves to call the appropriate Transformation class' process
    method."""

    def _run(
        self,
        source_frame: pd.DataFrame,
        tenant: str,
        flow: str,
        realm: str,
        override_transform_maps: List[TransformMap] | None = None,
        **kwargs,
    ) -> pd.DataFrame:
        if source_frame.empty:
            return source_frame

        flow_id_to_map_list: Dict[str, List[TransformMap]] = {
            "deepgram_feed": [transcript_transform_maps.deepgram_feed_transform_map],
            "focus_horizon_voice_transform": [
                voice_transform_maps.focus_horizon_voice_transform_map
            ],
            "iv_transform_transcript": [
                transcript_transform_maps.intelligent_voice_feed_transform_map
            ],
            "kerv_text_transform": [kerv_text_transform_map],
            "ms_graph_email_transform": [email_transform_maps.ms_graph_email_transform_map],
            "ms_teams_chat_messages": [ms_teams_chat_transform_messages_map],
            "order_blotter": [order_transform_maps.order_blotter_map],
            "refinitiv_tr_eikon_chat_transform_chat_events": [
                chat_event_transform_maps.refinitiv_tr_eikon_chat_transform_chat_events_map
            ],
            "refinitiv_tr_eikon_chat_transform_market_suggestions": [
                market_suggestion_transform_maps.refinitiv_tr_eikon_chat_transform_market_suggestions_map
            ],
            "refinitiv_tr_eikon_chat_transform_messages": [
                message_transform_maps.refinitiv_tr_eikon_chat_transform_messages_map
            ],
            "the_comms_guys_voice_transform": [voice_transform_maps.the_comms_guys_transform_map],
            A1_VOICE_FLOW_NAME: [voice_transform_maps.a1_voice_transform_map],
            ASC_CHAT_FLOW_NAME: [message_transform_maps.asc_message_transform_map],
            ASC_VOICE_FLOW_NAME: [voice_transform_maps.asc_voice_transform_map],
            AVATRADE_CLIENT_DATA_HANDLER: [
                person_transform_maps.avatrade_client_data_handler_market_person_transform_map
            ],
            AVAYA_VOICE_FLOW_NAME: [voice_transform_maps.avaya_voice_transform_map],
            BP_ASC_TEAMS_VOICE_FLOW_NAME: [voice_transform_maps.bp_asc_teams_voice_transform_map],
            BP_CLOUD9_VOICE_FLOW_NAME: [voice_transform_maps.bp_cloud9_voice_transform_map],
            BP_DUBBER_VOICE_FLOW_NAME: [voice_transform_maps.bp_dubber_voice_transform_map],
            BP_TWILIO_VOICE_FLOW_NAME: [voice_transform_maps.bp_twilio_voice_transform_map],
            BP_VODAFONE_VOICE_FLOW_NAME: [voice_transform_maps.bp_vodafone_voice_transform_map],
            CALL_CABINET_FEED_VOICE_FLOW_NAME: [
                voice_transform_maps.call_cabinet_feed_voice_transform_map
            ],
            CLARIFYGO_OI_VOICE_FLOW_NAME: [voice_transform_maps.clarifygo_oi_voice_transform_map],
            CLOUD9_VOICE_FLOW_NAME: [voice_transform_maps.cloud9_voice_transform_map],
            COMMPEAK_VOICE_FLOW_NAME: [voice_transform_maps.commpeak_voice_transform_map],
            DEEPVIEW_CHAT_MESSAGE_FLOW_NAME: [message_transform_maps.deepview_chat_transform_map],
            DUBBER_VOICE_FLOW_NAME: [voice_transform_maps.dubber_voice_transform_map],
            ENMACC_CHAT_FLOW_NAME: [message_transform_maps.enmacc_chat_message_transform_map],
            GAMMA_HORIZON_VOICE_FLOW_NAME: [voice_transform_maps.gamma_horizon_voice_transform_map],
            ICE_CHAT_CHAT_EVENT_MAPPINGS_NAME: [
                chat_event_transform_maps.ice_chat_transform_chat_event_map
            ],
            ICE_CHAT_MESSAGE_MAPPINGS_NAME: [
                message_transform_maps.ice_chat_transform_messages_map
            ],
            ICE_VANTAGE_CHAT_CHAT_EVENT_MAPPINGS_NAME: [
                chat_event_transform_maps.ice_vantage_chat_transform_chat_event_map
            ],
            ICE_VANTAGE_CHAT_MESSAGE_MAPPINGS_NAME: [
                message_transform_maps.ice_vantage_chat_transform_messages_map
            ],
            INFO_BARRIER_PROJECT_FLOW_NAME: [
                TransformMap(default=InfoBarrierProjectMappings, map={})
            ],
            ING_MS_TEAMS_CHAT_EVENT_MAPPINGS_NAME: [
                chat_event_transform_maps.ing_ms_teams_chat_event_transform_map
            ],
            ING_MS_TEAMS_MESSAGE_MAPPINGS_NAME: [
                message_transform_maps.ing_ms_teams_chat_message_transform_map
            ],
            IV_KERV_VOICE_FLOW_NAME: [voice_transform_maps.iv_kerv_voice_transform_map],
            IV_REDBOX_FLOW_NAME: [voice_transform_maps.iv_redbox_voice_transform_map],
            IV_UNIVERSAL_FLOW_NAME: [voice_transform_maps.iv_universal_voice_transform_map],
            KERV_VOICE_FLOW_NAME: [voice_transform_maps.kerv_voice_transform_map],
            LEAPXPERT_CHAT_CHAT_EVENT_MAPPINGS_NAME: [leapxpert_chat_event_transform_map],
            LEAPXPERT_CHAT_MESSAGE_MAPPINGS_NAME: [leapxpert_message_transform_map],
            LUNA_ADVANCE_VOICE_MAPPINGS_NAME: [luna_advance_voice_transform_map],
            MASERGY_VOICE_FLOW_NAME: [voice_transform_maps.masergy_voice_transform_map],
            MYMARKET_BP_PERSON_FLOW_NAME: [person_transform_maps.mymarket_bp_person_transform_map],
            MYMARKET_SLACK_MARKET_SUGGESTION_TRANSFORM: [
                person_transform_maps.mymarket_slack_market_suggestion_transform
            ],
            MYMARKET_SLACK_ACCOUNT_PERSON_TRANSFORM: [
                person_transform_maps.mymarket_slack_account_person_transform
            ],
            MYMARKET_UNIVERSAL_STEELEYE_PERSON_HANDLER: [
                person_transform_maps.mymarket_universal_steeleye_person_transform_map
            ],
            NATTERBOX_VOICE_FLOW_NAME: [voice_transform_maps.natterbox_voice_transform_map],
            NICE_VOICE_FLOW_NAME: [voice_transform_maps.nice_voice_transform_map],
            NUMONIX_IX_CLOUD_VOICE_FLOW_NAME: [
                voice_transform_maps.numonix_ix_cloud_voice_transform_map
            ],
            O2_SMS_MESSAGE_FLOW_NAME: [message_transform_maps.o2_sms_message_transform_map],
            O2_VOICE_FLOW_NAME: [voice_transform_maps.o2_voice_transform_map],
            ONSIM_VOICE_FLOW_NAME: [voice_transform_maps.onsim_voice_transform_map],
            ONSIM_SMS_FLOW_NAME: [message_transform_maps.onsim_sms_transform_map],
            OrderWorkflowNames.ORDER_CRD: [order_transform_maps.order_crd_map],
            ORDER_FEED_CFOX_HANDLER: [order_transform_maps.order_feed_cfox_map],
            OrderWorkflowNames.ORDER_IRESS_BELL_POTTER_FIX: [
                order_transform_maps.order_iress_bell_potter_fix_map
            ],
            OrderWorkflowNames.ORDER_FLEXTRADE_BELL_POTTER_FIX: [
                order_transform_maps.order_flextrade_bell_potter_fix_map
            ],
            OrderWorkflowNames.ORDER_THORNBRIDGE_CRIMS: [
                order_trades_transform_maps.thornbridge_crims_order_transform_map
            ],
            OrderWorkflowNames.ORDER_TR_FIDESSA_EOD_ORDER: [order_transform_maps.fidessa_eod],
            OrderWorkflowNames.ORDER_TRADEWEB: [order_transform_maps.order_tradeweb_map],
            OUTLOOK_CALENDAR_FLOW_NAME: [
                meeting_transform_maps.outlook_calendar_meeting_transform_map
            ],
            PRIMODIALLER_VOICE_FLOW_NAME: [voice_transform_maps.primodialler_voice_transform_map],
            REDBOX_FLOW_NAME: [voice_transform_maps.redbox_voice_transform_map],
            REFINITIV_FXT_CHAT_FLOW_NAME: [
                message_transform_maps.refinitiv_fx_chat_transform_messages_map
            ],
            RESTRICTED_LIST_FLOW_NAME: [TransformMap(default=RestrictedListMappings, map={})],
            RINGCENTRAL_VOICE_FLOW_NAME: [voice_transform_maps.ringcentral_voice_transform_map],
            RTS22TransactionWorkflowNames.TR_BBG_EMSI_ORDERS: [
                rts22_transaction_transform_maps.bbg_emsi_orders_transform_map
            ],
            RTS22TransactionWorkflowNames.ORDER_TR_FIDESSA_EOD_TR: [
                rts22_transaction_transform_maps.fidessa_eod
            ],
            SANTANDER_VOICE_FLOW_NAME: [voice_transform_maps.santander_voice_transform_map],
            SGC_VOICE_FLOW_NAME: [voice_transform_maps.sgc_voice_transform_map],
            SLACK_CHAT_CHAT_EVENT_MAPPINGS_NAME: [
                chat_event_transform_maps.slack_chat_event_transform_map
            ],
            SLACK_CHAT_MESSAGE_MAPPINGS_NAME: [message_transform_maps.slack_message_transform_map],
            SNIPPET_SENTRY_CHAT_FLOW_NAME: [
                message_transform_maps.snippet_sentry_chat_transform_map
            ],
            VMO2_VOICE_FLOW_NAME: [voice_transform_maps.vmo2_voice_transform_map],
            STEELEYE_UNIVERSAL_CHAT_CHAT_EVENT_MAPPINGS_NAME: [
                chat_event_transform_maps.steeleye_universal_chat_event_transform_map
            ],
            STEELEYE_UNIVERSAL_CHAT_MESSAGE_MAPPINGS_NAME: [
                message_transform_maps.steeleye_universal_message_transform_map
            ],
            STEELEYE_UNIVERSAL_VOICE_FLOW_NAME: [
                voice_transform_maps.steeleye_universal_voice_transform_map
            ],
            SYMPHONY_XML_CHAT_CHAT_EVENT: [
                chat_event_transform_maps.symphony_xml_chat_message_transform_map
            ],
            SYMPHONY_XML_CHAT_MESSAGE: [
                message_transform_maps.symphony_xml_chat_message_transform_map
            ],
            TELEMESSAGE_MESSAGE_TRANSFORM: [
                message_transform_maps.telemessage_message_transform_map
            ],
            TELEMESSAGE_VOICE_TRANSFORM: [voice_transform_maps.telemessage_transform_map],
            TELEWARE_VOICE_FLOW_NAME: [voice_transform_maps.teleware_voice_transform_map],
            THREE_CX_VOICE_FLOW_NAME: [voice_transform_maps.three_cx_voice_transform_map],
            TRUPHONE_MESSAGE_FLOW_NAME: [message_transform_maps.truphone_message_transform_map],
            TRUPHONE_VOICE_FLOW_NAME: [voice_transform_maps.truphone_transform_map],
            VERBA_VOICE_CSV_MAPPINGS_NAME: [voice_transform_maps.verba_voice_csv_transform_map],
            VERBA_VOICE_XML_MAPPINGS_NAME: [voice_transform_maps.verba_voice_xml_transform_map],
            VIA_VOICE_FLOW_NAME: [voice_transform_maps.via_voice_transform_map],
            VIBEWORK_TOLLRING_VOICE_FLOW_NAME: [
                voice_transform_maps.vibework_tollring_voice_transform_map
            ],
            VOICE_VOICE_FLOW_NAME: [voice_transform_maps.voip_voice_transform_map],
            VOXSMART_MESSAGE_MAPPINGS_NAME: [message_transform_maps.voxsmart_message_transform_map],
            VOXSMART_VOICE_MAPPINGS_NAME: [voice_transform_maps.voxsmart_voice_transform_map],
            WAVENET_VOICE_FLOW_NAME: [voice_transform_maps.wavenet_voice_transform_map],
            XIMA_VOICE_FLOW_NAME: [voice_transform_maps.xima_voice_transform_map],
            ZOOM_MEETINGS_MEETING_MAPPINGS_NAME: [
                meeting_transform_maps.zoom_meetings_meeting_transform_map
            ],
            ZOOM_MEETINGS_MESSAGE_MAPPINGS_NAME: [
                message_transform_maps.zoom_meetings_message_transform_map
            ],
            ZOOM_MEETINGS_TRANSCRIPT_MAPPINGS_NAME: [
                transcript_transform_maps.zoom_meetings_transcript_transform_map
            ],
            ZOOM_PHONE_VOICE_FLOW_NAME: [voice_transform_maps.zoom_phone_voice_transform_map],
            ZOOM_PHONE_VOICE_TRANSCRIPT_FLOW_NAME: [
                transcript_transform_maps.zoom_phone_voice_transcript_transform_map
            ],
            ZOOM_TRIUM_VOICE_FLOW_NAME: [voice_transform_maps.zoom_trium_voice_transform_map],
            ORDER_ALADDIN_V2_CLIENT_SIDE_FEED_NAME: [
                order_transform_maps.order_aladdin_v2_client_side_map
            ],
            ORDER_ALADDIN_V2_MARKET_SIDE_FEED_NAME: [
                order_transform_maps.order_aladdin_v2_market_side_map
            ],
            ORDER_EZE_OMS_SOFT_ORDERS: [order_transform_maps.order_eze_oms_soft_orders_map],
            ORDER_EZE_OMS_SOFT_EXECUTIONS: [order_transform_maps.order_eze_oms_soft_executions_map],
            ORDER_EZE_OMS_SOFT_ALLOCATIONS: [
                order_transform_maps.order_eze_oms_soft_allocations_map
            ],
        }

        if override_transform_maps:
            transform_map_list = override_transform_maps
        else:
            transform_map_list = flow_id_to_map_list.get(flow)

        results = []

        for transform_map in transform_map_list:
            transformation = transform_map.transformation(tenant=tenant)(
                source_frame=source_frame,
                logger=logger,
                realm=realm,
                tenant=tenant,
                **kwargs,
            )
            results.append(transformation.process())

        return pd.concat(results, axis=1)


def run_get_primary_transformations(
    source_frame: pd.DataFrame,
    flow: str,
    tenant: str,
    realm: str,
    app_metrics_path: Optional[str] = None,
    audit_path: Optional[str] = None,
    **kwargs,
):
    task = GetPrimaryTransformations(app_metrics_path=app_metrics_path, audit_path=audit_path)
    return task.run(
        source_frame=source_frame,
        flow=flow,
        realm=realm,
        tenant=tenant,
        **kwargs,
    )
