import json
import logging
from typing import Generator

logger = logging.getLogger(__name__)


def lazy_load_ndjson(path: str, file_uri: str) -> Generator[dict, None, None]:
    """This function is used to load a ndjson file line by line.

    :param path: path to the ndjson file
    :param file_uri: the file uri that is being processed
    :return:
    """
    current_line: int = 1

    with open(path, "r") as f:
        for line in f:
            try:
                yield json.loads(line)
            except json.decoder.JSONDecodeError:
                logger.error(f"Failed to parse line {current_line} of {file_uri}. 'Line: {line}'")

            current_line += 1
