import gc
import logging
import os
import pandas as pd
import psutil
import uuid
from aries_se_core_tasks.core.core_dataclasses import SerializerResult
from integration_wrapper.static import IntegrationAriesTaskVariables
from pathlib import Path
from se_io_utils.tempfile_utils import tmp_directory
from typing import Any, Callable, TypeVar, cast

logger = logging.getLogger(__name__)

F = TypeVar("F", bound=Callable[..., Any])


def serializer(func: F) -> F:
    """
    - https://mypy.readthedocs.io/en/stable/generics.html#declaring-decorators

    - This utility is meant to be used as a decorator anywhere
    the output is a dataframe is converted to a `SerializerResult`
    and if the input is `SerializerResult`, they're converted to dataframe

    Scenarios handled:
        Serialization:
            - If output is a dataframe:
                Action: Returns a `SerializerResult` after saving df to disk
            - If output is a list/tuple of dataframes:
                Action: Returns a list/tuple of `SerializerResult` if all items in
                list/tuple is df after saving all dfs to disk
        Deserialization:
            - If any input arg is a `SerializerResult`:
                The dataframe is loaded from pickle and sent as an arg to the method
            - If any input arg is a list/tuple:
                If all elements are `SerializerResult`, they're loaded as dfs
                and passed to the method
            - If any input arg is a dict:
                If any of the values in the dict is `SerializerResult`, it is loaded as
                df and passed to the method.
    """

    def convert_between_df_and_pkl(*args, **kwargs: Any) -> Any:
        logger_name: str = ""
        if len(args) > 0 and hasattr(args[0], "__class__"):
            # If decorator is being used inside a class
            # use that class as the logger
            logger_name = args[0].__class__.__name__
        if not len(logger_name):
            # Otherwise if is a method, get the module and method name
            logger_name = f"{func.__module__}@{func.__name__}"

        process = psutil.Process(os.getpid())
        if not kwargs.get("skip_deserialize", False) and not os.environ.get(
            IntegrationAriesTaskVariables.IS_SERIALIZER_DISABLED
        ):
            logger.debug(
                f"Memory Utilization at the start of {logger_name}: %f",
                process.memory_info().rss / (1024 * 1024),
            )

            deserialize(args=args, kwargs=kwargs, logger_name=logger_name)
        else:
            logger.debug("Deserializing skipped")

        # As discussed using `*args` maybe not be the correct way,
        # but is cleanest way for the time being
        result = func(*args, **kwargs)

        # Serialize the output, if was not marked as skipped
        # default: always serialize
        if (
            not kwargs.get("skip_serializer", False)
            and isinstance(result, (pd.DataFrame, list, tuple, dict))
            and not os.environ.get(IntegrationAriesTaskVariables.IS_SERIALIZER_DISABLED)
        ):
            source_dir = str(
                os.getenv(
                    IntegrationAriesTaskVariables.SERIALIZER_TMP_DIR, tmp_directory().as_posix()
                )
            )
            base_path = Path(source_dir).joinpath(logger_name)

            output = convert_df_to_transform_pickle_result(
                data=result, base_path=base_path, logger_name=logger_name
            )

            del result
            gc.collect()

            logger.debug(
                f"Memory Utilization at the end of {logger_name}: %f",
                process.memory_info().rss / (1024 * 1024),
            )

            return output
        return result

    def deserialize(args, kwargs, logger_name: str):
        """Deserializes the pkl kwargs and returns data frames."""

        # De-serialize the inputs
        # It is only serializing the `kwargs`, so this way enforces
        # the developer to not use positional arguments
        for key_ in kwargs:
            if isinstance(kwargs[key_], SerializerResult):
                try:
                    kwargs[key_] = pd.read_pickle(kwargs[key_].pickle_path)
                except Exception as e:
                    logger.warning(
                        f"Could not convert pkl to dataframe, "
                        f"failing with {e}skipping typecasting for {logger_name}"
                    )
            elif isinstance(kwargs[key_], (list, tuple)):
                was_tuple: bool = False

                if isinstance(kwargs[key_], tuple):
                    was_tuple = True
                    kwargs[key_] = list(kwargs[key_])

                for idx, value in enumerate(kwargs[key_]):
                    if isinstance(value, SerializerResult):
                        try:
                            kwargs[key_][idx] = pd.read_pickle(value.pickle_path)
                        except Exception as e:
                            logger.warning(
                                f"Could not convert pkl to dataframe, "
                                f"failing with {e}skipping typecasting for {logger_name}"
                            )
                    elif isinstance(value, (list, tuple)):
                        inner_was_tuple: bool = False

                        if isinstance(kwargs[key_], tuple):
                            inner_was_tuple = True
                            kwargs[key_][idx] = list(kwargs[key_][idx])

                        for inner_idx, inner_value in enumerate(kwargs[key_][idx]):
                            if isinstance(inner_value, SerializerResult):
                                try:
                                    kwargs[key_][idx][inner_idx] = pd.read_pickle(
                                        inner_value.pickle_path
                                    )
                                except Exception as e:
                                    logger.warning(
                                        f"Could not convert pkl to dataframe, "
                                        f"failing with {e}skipping typecasting for {logger_name}"
                                    )
                        if inner_was_tuple:
                            kwargs[key_][idx] = tuple(kwargs[key_][idx])

                if was_tuple:
                    kwargs[key_] = tuple(kwargs[key_])

            elif isinstance(kwargs[key_], dict):
                for value in kwargs[key_]:
                    if isinstance(kwargs[key_][value], SerializerResult):
                        try:
                            kwargs[key_][value] = pd.read_pickle(kwargs[key_][value].pickle_path)
                        except Exception as e:
                            logger.warning(
                                f"Could not convert pkl to dataframe, failing with {e} "
                                f"skipping typecasting for {logger_name}"
                            )
                    else:
                        kwargs[key_][value] = kwargs[key_][value]

    return cast(F, convert_between_df_and_pkl)


def convert_df_to_transform_pickle_result(data: Any, logger_name: str, base_path: Path) -> Any:
    """Saves the dataframes into the disk and returns ``SerializerResult`

    :param data: data to be serialized if possible
    :param logger_name: the name of where this serializer was applied, e.g. class or method
    :param base_path: base dir where dfs needs to be stored
    :return: pickle path or input data.
    """
    if not (isinstance(data, (pd.DataFrame, list, tuple, dict))):
        return data

    base_path.mkdir(parents=True, exist_ok=True)

    if isinstance(data, pd.DataFrame):
        return write_pkl(df=data, directory=base_path, file_prefix=logger_name)

    elif isinstance(data, (list, tuple)):
        was_tuple: bool = False

        if isinstance(data, tuple):
            data = list(data)
            was_tuple = True

        for idx, value in enumerate(data):
            if isinstance(value, pd.DataFrame):
                data[idx] = write_pkl(df=value, directory=base_path, file_prefix=logger_name)

            # Handle lists of list of dataframes
            elif isinstance(value, (list, tuple)):
                inner_was_tuple: bool = False

                if isinstance(value, tuple):
                    value = list(value)
                    inner_was_tuple = True

                for inner_idx, inner_value in enumerate(value):
                    if isinstance(inner_value, pd.DataFrame):
                        data[idx][inner_idx] = write_pkl(
                            df=inner_value, directory=base_path, file_prefix=logger_name
                        )

                if inner_was_tuple:
                    data[idx] = tuple(data[idx])

        if was_tuple:
            return tuple(data)

    elif isinstance(data, dict):
        for key in data:
            if isinstance(data[key], pd.DataFrame):
                data[key] = write_pkl(df=data[key], directory=base_path, file_prefix=logger_name)

    return data


def write_pkl(df: pd.DataFrame, directory: Path, file_prefix: str) -> SerializerResult:
    """Write a dataframe to a pickle file.

    :param df: the dataframe to be written
    :param directory: path to the directory where the pickle file will be written
    :param file_prefix: prefix of the pickle file name
    :return:
    """
    filename = Path(f"{file_prefix}-{uuid.uuid4()}.pkl")
    path = directory.joinpath(filename)

    # This should not catch any exception
    # If it fails, its probably developers fault.
    df.to_pickle(path.as_posix())

    return SerializerResult(empty=df.empty, index=df.index, pickle_path=path, shape=df.shape)
