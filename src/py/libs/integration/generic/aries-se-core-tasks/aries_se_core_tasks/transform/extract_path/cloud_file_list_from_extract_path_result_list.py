# type: ignore
import logging
from aries_se_core_tasks.core.core_dataclasses import CloudFile, S3File
from aries_se_core_tasks.core.integration_task import IntegrationTask
from se_core_tasks.core.core_dataclasses import ExtractPathResult
from se_core_tasks.transform.extract_path.cloud_file_list_from_extract_path_result_list import (
    Params,
)
from se_core_tasks.transform.extract_path.cloud_file_list_from_extract_path_result_list import (
    run_cloud_file_list_from_extract_path_result_list as run_task_from_lib,
)
from typing import List, Optional, Union

logger = logging.getLogger(__name__)


class CloudFileListFromExtractPathResultList(IntegrationTask):
    def _run(
        self,
        extract_result_list: Union[ExtractPathResult, List[ExtractPathResult]],
        realm: str,
        params: Params,
        **kwargs,
    ) -> List[CloudFile]:
        return run_task_from_lib(
            extract_result_list=extract_result_list, realm=realm, params=params, **kwargs
        )


def run_cloud_file_list_from_extract_path_result_list(
    params: Params,
    extract_result_list: Union[ExtractPathResult, List[ExtractPathResult]],
    realm: str,
    app_metrics_path: Optional[str] = None,
    audit_path: Optional[str] = None,
    **kwargs,
) -> List[S3File]:
    task = CloudFileListFromExtractPathResultList(
        app_metrics_path=app_metrics_path, audit_path=audit_path
    )

    return task.run(
        extract_result_list=extract_result_list,
        realm=realm,
        params=params,
        **kwargs,
    )
