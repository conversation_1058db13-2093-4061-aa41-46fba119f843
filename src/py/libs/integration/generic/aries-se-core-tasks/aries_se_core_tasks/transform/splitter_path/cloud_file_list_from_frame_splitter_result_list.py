# type: ignore
import logging
from aries_se_core_tasks.core.core_dataclasses import (
    CloudFile,
)
from aries_se_core_tasks.core.integration_task import IntegrationTask
from se_core_tasks.core.core_dataclasses import FileSplitterResult
from se_core_tasks.transform.cloud_file_list_from_frame_splitter_result_list import Params
from se_core_tasks.transform.cloud_file_list_from_frame_splitter_result_list import (
    run_cloud_file_list_from_file_splitter_result_list as run_task_from_lib,
)
from typing import List, Optional

logger = logging.getLogger(__name__)


class CloudFileListFileSplitterResultList(IntegrationTask):
    def _run(
        self,
        file_splitter_result_list: List[FileSplitterResult],
        file_url: str,
        params: Params,
        **kwargs,
    ) -> Optional[List[CloudFile]]:
        return run_task_from_lib(
            file_splitter_result_list=file_splitter_result_list,
            file_url=file_url,
            params=params,
            **kwargs,
        )


def run_cloud_file_list_from_file_splitter_result_list(
    params: Optional[Params] = None,
    file_splitter_result_list: List[FileSplitterResult] = None,
    file_url: str = None,
    app_metrics_path: Optional[str] = None,
    audit_path: Optional[str] = None,
    **kwargs,
) -> Optional[List[CloudFile]]:
    task = CloudFileListFileSplitterResultList(
        app_metrics_path=app_metrics_path, audit_path=audit_path
    )
    return task.run(
        file_splitter_result_list=file_splitter_result_list,
        file_url=file_url,
        params=params,
        **kwargs,
    )
