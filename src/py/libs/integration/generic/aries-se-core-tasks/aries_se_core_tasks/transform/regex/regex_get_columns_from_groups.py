# type: ignore
import logging
import pandas as pd
from aries_se_core_tasks.core.integration_task import IntegrationTask
from se_core_tasks.transform.regex.regex_get_columns_from_groups import Params
from se_core_tasks.transform.regex.regex_get_columns_from_groups import (
    run_regex_get_columns_from_groups as run_task_from_lib,
)
from typing import Optional

logger = logging.getLogger(__name__)


class RegexGetColumnsFromGroups(IntegrationTask):
    def _run(
        self,
        source_frame: pd.DataFrame,
        params: Params,
        **kwargs,
    ) -> pd.DataFrame:
        return run_task_from_lib(source_frame=source_frame, params=params, **kwargs)


def run_regex_get_columns_from_groups(
    source_frame: pd.DataFrame,
    params: Params,
    app_metrics_path: Optional[str] = None,
    audit_path: Optional[str] = None,
    **kwargs,
) -> pd.DataFrame:
    task = RegexGetColumnsFromGroups(app_metrics_path=app_metrics_path, audit_path=audit_path)

    return task.run(
        params=params,
        source_frame=source_frame,
        **kwargs,
    )
