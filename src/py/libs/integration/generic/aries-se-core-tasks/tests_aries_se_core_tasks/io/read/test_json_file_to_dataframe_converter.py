import pandas as pd
from aries_se_core_tasks.io.read.json_file_to_dataframe_converter import (
    Params,
    run_json_file_to_dataframe_converter,
)
from pathlib import Path

BASE_PATH: Path = Path(__file__).parent
DATA_PATH: Path = BASE_PATH.joinpath("data")


class TestJsonFileToDataframeConverter:
    def test_it_can_convert_json_into_dataframe(self):
        result = run_json_file_to_dataframe_converter(
            path=DATA_PATH.joinpath("json/file_1.json"),
            params=Params(),
            skip_serializer=True,
        )

        pd.testing.assert_frame_equal(
            left=result,
            right=pd.DataFrame(
                data={
                    "key": ["messageName"],
                    "value": ["7bd72749d38d4fc3811de38a91f079d8"],
                    "properties": [
                        {
                            "Call start time": ["2022-11-02 07:21:28"],
                            "Call end time": ["2022-11-02 08:21:30"],
                            "Geography": ["BOA"],
                            "Comm Type": ["mobilephone"],
                            "Comm Date": ["20221102"],
                            "documentCreationDate": ["20221102T072128.000+0100"],
                        }
                    ],
                },
            ),
        )

    def test_it_can_convert_json_into_dataframe_select_columns(self):
        """Test it can add and remove columns."""
        result = run_json_file_to_dataframe_converter(
            path=DATA_PATH.joinpath("json/file_1.json"),
            params=Params(columns=["key", "value", "new_column"]),
            skip_serializer=True,
        )

        pd.testing.assert_frame_equal(
            left=result,
            right=pd.DataFrame(
                data={
                    "key": ["messageName"],
                    "value": ["7bd72749d38d4fc3811de38a91f079d8"],
                    "new_column": [pd.NA],
                },
            ),
        )

    def test_it_can_convert_json_into_dataframe_with_normalize(self):
        result = run_json_file_to_dataframe_converter(
            path=DATA_PATH.joinpath("json/file_1.json"),
            params=Params(
                normalize=True,
            ),
            skip_serializer=True,
        )

        pd.testing.assert_frame_equal(
            left=result,
            right=pd.DataFrame(
                data={
                    "key": ["messageName"],
                    "value": ["7bd72749d38d4fc3811de38a91f079d8"],
                    "properties.Call start time": [["2022-11-02 07:21:28"]],
                    "properties.Call end time": [["2022-11-02 08:21:30"]],
                    "properties.Geography": [["BOA"]],
                    "properties.Comm Type": [["mobilephone"]],
                    "properties.Comm Date": [["20221102"]],
                    "properties.documentCreationDate": [["20221102T072128.000+0100"]],
                },
            ),
        )

    def test_it_can_convert_json_into_dataframe_with_normalize_and_select_columns(self):
        """Test it can add and remove columns."""

        result = run_json_file_to_dataframe_converter(
            path=DATA_PATH.joinpath("json/file_1.json"),
            params=Params(
                normalize=True,
                columns=[
                    "key",
                    "value",
                    "properties.Call start time",
                    "properties.Geography",
                    "new_column",
                ],
            ),
            skip_serializer=True,
        )

        pd.testing.assert_frame_equal(
            left=result,
            right=pd.DataFrame(
                data={
                    "key": ["messageName"],
                    "value": ["7bd72749d38d4fc3811de38a91f079d8"],
                    "properties.Call start time": [["2022-11-02 07:21:28"]],
                    "properties.Geography": [["BOA"]],
                    "new_column": [pd.NA],
                },
            ),
        )
