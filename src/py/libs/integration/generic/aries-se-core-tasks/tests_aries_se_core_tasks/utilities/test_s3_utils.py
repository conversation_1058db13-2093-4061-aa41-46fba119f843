import datetime
from aries_se_core_tasks.utilities.s3_utils import get_bucket_and_key_from_file_url, s3_list_files
from botocore.exceptions import BotoCoreError


class MockPaginator:
    def __init__(self, page_response, raise_botocore_error):
        self.paginate_response = page_response
        self.raise_botocore_error = raise_botocore_error

    def paginate(self, *args, **kwargs):
        if self.raise_botocore_error:
            raise BotoCoreError

        return self.paginate_response


class MockS3Client:
    def __init__(self, page_response, raise_botocore_error=False):
        self.page_response = page_response
        self.raise_botocore_error = raise_botocore_error

    def get_paginator(self, *args, **kwargs):
        return MockPaginator(self.page_response, self.raise_botocore_error)


class TestS3Utils:
    """Test cases for S3 utilities."""

    def test_s3_list_files(self, mocker):
        """Test s3_list_files."""

        bucket = "onefinancial.uat.steeleye.co"
        folder_path = "mapping_tables/one-zero/mt5_user_list"

        response = [
            {
                "IsTruncated": False,
                "Name": "onefinancial.uat.steeleye.co",
                "MaxKeys": 1000,
                "Prefix": "",
                "Contents": [
                    {
                        "Key": "mapping_tables/one-zero/mt5_user_list/",
                        "LastModified": datetime.datetime(2021, 7, 5, 14, 48, 1),
                        "ETag": '"d41d8cd98f00b204e9800998ecf8427e"',
                        "Size": 0,
                        "StorageClass": "STANDARD",
                    },
                    {
                        "Key": "mapping_tables/one-zero/mt5_user_list/2020-09-28 210212 - MT5.csv",
                        "LastModified": datetime.datetime(2021, 7, 5, 14, 49, 10),
                        "ETag": '"9deccffb3f52de8e0f1a146b3e53c0d5"',
                        "Size": 81628,
                        "StorageClass": "STANDARD",
                    },
                    {
                        "Key": "mapping_tables/one-zero/mt5_user_list/2020-10-12 210159 - MT5.csv",
                        "LastModified": datetime.datetime(2021, 7, 5, 14, 49, 10),
                        "ETag": '"d7f62f6c450a7d0fffe4e26ee42a43f4"',
                        "Size": 83294,
                        "StorageClass": "STANDARD",
                    },
                ],
                "EncodingType": "url",
                "ResponseMetadata": {
                    "RequestId": "abc123",
                    "HTTPStatusCode": 200,
                    "HostId": "abc123",
                },
            }
        ]

        expected_file_list = [
            "2020-09-28 210212 - MT5.csv",
            "2020-10-12 210159 - MT5.csv",
        ]
        mocker.patch(
            "aries_se_core_tasks.utilities.s3_utils.get_s3_client"
        ).return_value = MockS3Client(response)

        files_list = s3_list_files(bucket=bucket, folder_path=folder_path)
        assert files_list == expected_file_list

    def test_s3_list_files_throws_exception(self, mocker):
        """Test s3_list_files."""

        bucket = "onefinancial.uat.steeleye.co"
        folder_path = "mapping_tables/one-zero/mt5_user_list"

        mocker.patch(
            "aries_se_core_tasks.utilities.s3_utils.get_s3_client"
        ).return_value = MockS3Client(None, True)

        assert s3_list_files(bucket=bucket, folder_path=folder_path) is None

    def test_get_bucket_and_key_from_file_url(self):
        expected_bucket = "test.dev.steeleye.com"
        expected_key = "aries/ingress/streamed/evented/2023/06/01/transformed.ndjson"
        result_bucket, result_key = get_bucket_and_key_from_file_url(
            file_url=f"s3://{expected_bucket}/{expected_key}"
        )
        assert result_bucket == expected_bucket
        assert result_key == expected_key
