import pandas as pd
import pytest
from aries_se_core_tasks.generic.generate_required_amp_fields import Params
from aries_se_core_tasks.static import MetaModel


@pytest.fixture()
def params_fixture_call() -> Params:
    return Params(data_model=MetaModel.CALL)


@pytest.fixture()
def source_frame_call() -> pd.DataFrame:
    return pd.DataFrame(
        [
            {
                "callDuration": "00:09:08",
                "direction": "outgoing",
                "id": "0d8eee28-ae40-11ee-9ba2-3789a07e81e7",
                "identifiers.allCountryCodes": ["GB"],
                "identifiers.allIds": [
                    "+442087310200",
                    "+447929841931",
                    "442087310200",
                    "447929841931",
                ],
                "identifiers.fromId": "+442087310200",
                "identifiers.fromDeviceId": "+442087310200",
                "identifiers.fromIdAddlInfo": {"raw": "442087310200", "countryCode": "GB"},
                "identifiers.toIds": ["+447929841931"],
                "identifiers.toDeviceId": "+447929841931",
                "identifiers.toIdsAddlInfo": [{"raw": "447929841931", "countryCode": "GB"}],
                "metadata.source.client": "Kerv Voice",
                "sourceIndex": 0,
                "sourceKey": "s3://ashwath.dev.steeleye.co/aries/ingest_batching/iv_kerv_voice/2024/01/17/zip_test.ndjson",
                "timestamps.localTimestampStart": "2024-01-08T16:08:32.000000Z",
                "timestamps.localTimestampEnd": "2024-01-08T16:17:40.000000Z",
                "timestamps.timestampConnected": "2024-01-08T16:08:32.000000Z",
                "timestamps.timestampEnd": "2024-01-08T16:17:40.000000Z",
                "timestamps.timestampStart": "2024-01-08T16:08:32.000000Z",
                "voiceFile.fileName": "22468152_283-0d8eee28-ae40-11ee-9ba2-3789a07e81e7-"
                "442087310200-447929841931-1704730062-ee-mo-.wav",
                "voiceFile.fileType": ".mp3",
                "voiceFile.sizeInBytes": 4385709,
                "voiceFile.fileInfo.location.bucket": "ashwath.dev.steeleye.co",
                "voiceFile.fileInfo.location.key": "aries/ingress/"
                "depository/iv_kerv_voice/attachments/"
                "2024/01/24/test_iv_kerv_voice_transform1/"
                "6cb1cc8f37ae847d307500cf7be30bb21768d7df8edf19"
                "baf663237800f5b47b/recordings/"
                "22468152_283-0d8eee28-ae40-11ee-9ba2-3789a07e81e7-442087310200-"
                "447929841931-1704730062-ee-mo-.wav",
                "voiceFile.fileInfo.lastModified": "2024-01-24T15:24:45",
                "voiceFile.fileInfo.contentLength": 4385709,
                "voiceFile.fileInfo.versionId": "lz9EGsD01FV1PoPfMwoEr_8Wcs9oVaJu",
                "hasAttachment": True,
                "connected": True,
                "participants": None,
            },
            {
                "callDuration": "00:04:51",
                "direction": "outgoing",
                "id": "5d4cc358-ae41-11ee-9f95-9360add42176",
                "identifiers.allCountryCodes": ["GB"],
                "identifiers.allIds": [
                    "+442087310200",
                    "+447929841931",
                    "442087310200",
                    "447929841931",
                ],
                "identifiers.fromId": "+442087310200",
                "identifiers.fromDeviceId": "+442087310200",
                "identifiers.fromIdAddlInfo": {"raw": "442087310200", "countryCode": "GB"},
                "identifiers.toIds": ["+447929841931"],
                "identifiers.toDeviceId": "+447929841931",
                "identifiers.toIdsAddlInfo": [{"raw": "447929841931", "countryCode": "GB"}],
                "metadata.source.client": "Kerv Voice",
                "sourceIndex": 1,
                "sourceKey": "s3://ashwath.dev.steeleye.co/aries/ingest_batching/"
                "iv_kerv_voice/2024/01/17/zip_test.ndjson",
                "timestamps.localTimestampStart": "2024-01-08T16:17:04.000000Z",
                "timestamps.localTimestampEnd": "2024-01-08T16:21:55.000000Z",
                "timestamps.timestampConnected": "2024-01-08T16:17:04.000000Z",
                "timestamps.timestampEnd": "2024-01-08T16:21:55.000000Z",
                "timestamps.timestampStart": "2024-01-08T16:17:04.000000Z",
                "voiceFile.fileName": "22468160_283-5d4cc358-ae41-11ee-9f95-9360add42176"
                "-442087310200-447929841931-1704730626-ee-mo-.wav",
                "voiceFile.fileType": ".mp3",
                "voiceFile.sizeInBytes": 2330541,
                "voiceFile.fileInfo.location.bucket": "ashwath.dev.steeleye.co",
                "voiceFile.fileInfo.location.key": "aries/ingress/depository/iv_kerv_voice/"
                "attachments/2024/01/24/test_iv_kerv_voice_transform1/"
                "6cb1cc8f37ae847d307500cf7be30bb21768d7df8edf19baf663237800f5b47b"
                "/recordings/22468160_283-5d4cc358-ae41-11ee-9f95-9360add42176-"
                "442087310200-447929841931-1704730626-ee-mo-.wav",
                "voiceFile.fileInfo.lastModified": "2024-01-24T15:24:57",
                "voiceFile.fileInfo.contentLength": 2330541,
                "voiceFile.fileInfo.versionId": "LkYPlvHv8LUp7g..hzn7mfmreyQWTKcQ",
                "hasAttachment": True,
                "connected": True,
                "participants": None,
            },
        ]
    )


@pytest.fixture()
def expected_result_call() -> pd.DataFrame:
    return pd.DataFrame(
        [
            {
                "&id": "0d8eee28-ae40-11ee-9ba2-3789a07e81e7:+442087310200:"
                "['+447929841931']:2024-01-08T16:08:32Z:2024-01-08T16:17:40Z",
                "&hash": "0f74e6083b637ba2a88a29512ed6a405a3820fcc68a3565b0e7d1d2f91c5092c",
                "&key": "Call:0d8eee28-ae40-11ee-9ba2-3789a07e81e7:+442087310200:"
                "['+447929841931']:2024-01-08T16:08:32Z:2024-01-08T16:17:40Z:1706108763000",
                "&model": "Call",
                "&uniqueProps": ["+442087310200", "+447929841931"],
            },
            {
                "&id": "5d4cc358-ae41-11ee-9f95-9360add42176:+442087310200:"
                "['+447929841931']:2024-01-08T16:17:04Z:2024-01-08T16:21:55Z",
                "&hash": "cbdfaf81f244f00299079d075b46f07cc05accf7b5c71772327838ca7d3999ca",
                "&key": "Call:5d4cc358-ae41-11ee-9f95-9360add42176:+442087310200:"
                "['+447929841931']:2024-01-08T16:17:04Z:2024-01-08T16:21:55Z:1706108763000",
                "&model": "Call",
                "&uniqueProps": ["+442087310200", "+447929841931"],
            },
        ]
    )
