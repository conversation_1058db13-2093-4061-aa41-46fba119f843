import json
import pytest
import time
from abc import ABC
from aries_se_core_tasks.aries.sink_file_audit.static import StatusDescriptionsOfSyntheticRecords
from aries_se_core_tasks.aries.sink_file_audit.workflow_audits_collector import (
    assemble_final_audit,
    collect_all_workflow_audits,
)
from integration_audit.auditor import AuditorStatic<PERSON>ields
from moto import mock_aws
from se_core_tasks.abstractions.abstract_mock_s3 import AbstractMockS3
from typing import Any, Dict, List
from unittest.mock import call, patch


@pytest.fixture
def audit_jsons() -> List[Dict[str, Any]]:
    # 110 created - 100 + 10 for file1.fix and file2.fix
    # 40 errored - all for file2.fix

    return [
        {AuditorStaticFields.INPUT_FILES: {}, AuditorStaticFields.WORKFLOW_STATUS: []},
        {
            AuditorStaticFields.INPUT_FILES: {
                "file2.fix": {
                    "Order": {
                        "errored": 20,
                        "duplicate": 10,
                        "status": [
                            "LinkInstruments failed",
                            StatusDescriptionsOfSyntheticRecords.IS_SYNTHETIC_NEWO.value,
                        ],
                    },
                    "QuarantinedOrder": {"created": 10, "status": []},
                }
            },
            AuditorStaticFields.WORKFLOW_STATUS: ["X happened in this Workflow"],
        },
        {
            AuditorStaticFields.INPUT_FILES: {
                "file2.fix": {
                    "Order": {"errored": 10, "status": ["Missing mandatory `buySell` field"]}
                }
            },
            AuditorStaticFields.WORKFLOW_STATUS: [],
        },
        {
            AuditorStaticFields.INPUT_FILES: {
                "file1.fix": {"Order": {"created": 10, "status": []}},
                "file2.fix": {
                    "Order": {
                        "errored": 10,
                        "status": ["Records are not ES schema-compliant"],
                        "created": 100,
                    }
                },
            },
            AuditorStaticFields.WORKFLOW_STATUS: ["Y happened in this Workflow"],
        },
        {
            AuditorStaticFields.INPUT_FILES: {"audit_file.json": {"SinkFileAudit": {"created": 1}}},
            AuditorStaticFields.WORKFLOW_STATUS: [],
        },
    ]


@pytest.fixture
def final_audit_json() -> Dict[str, Any]:
    # 110 created - 100 + 10 for file1.fix and file2.fix
    # 40 errored - all for file2.fix

    return {
        AuditorStaticFields.INPUT_FILES: {
            "file2.fix": {
                "Order": {
                    "created": 100,
                    "errored": 40,
                    "skipped": 0,
                    "duplicate": 10,
                    "updated": 0,
                    "status": ["SC1", "SC2", "SC3", "SC4"],
                    AuditorStaticFields.IS_SYNTHETIC: False,
                },
                "QuarantinedOrder": {
                    "created": 10,
                    "errored": 0,
                    "skipped": 0,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [],
                    AuditorStaticFields.IS_SYNTHETIC: True,
                },
            },
            "file1.fix": {
                "Order": {
                    "created": 10,
                    "errored": 0,
                    "skipped": 0,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [],
                    AuditorStaticFields.IS_SYNTHETIC: False,
                }
            },
        },
        AuditorStaticFields.STATUS_CODES: {
            "SC1": "LinkInstruments failed",
            "SC2": StatusDescriptionsOfSyntheticRecords.IS_SYNTHETIC_NEWO.value,
            "SC3": "Missing mandatory `buySell` field",
            "SC4": "Records are not ES schema-compliant",
        },
        AuditorStaticFields.WORKFLOW_STATUS: [
            "X happened in this Workflow",
            "Y happened in this Workflow",
        ],
    }


@mock_aws
class TestWorkflowAuditsCollector:
    def test_collect_all_workflow_audits(self):
        mock_s3_instance = WorkflowAuditsCollectorMockS3()
        mock_s3_instance.create_mock_bucket(bucket="test.dev.steeleye.co")

        # NOTE: This list of files emulates retrying the ES-Connector Conductor Task for a given
        # Workflow as per the 2 ES Connector audits. The WorkflowAuditsCollector will always take
        # the latest audit
        mock_s3_instance.delayed_load_data_into_mock_s3(
            files_names=[
                "aries/ingest_audits/sink_file_audit/email/test_email_transform1/"
                "transform/tf1.json",
                "aries/ingest_audits/sink_file_audit/email/test_email_transform1/"
                "apply_meta/am1.json",
                "aries/ingest_audits/sink_file_audit/email/test_email_transform1/"
                "es_connector/es_first_file.json",
                "aries/ingest_audits/sink_file_audit/email/test_email_transform1/"
                "es_connector/es_last_file.json",
                "aries/ingest_audits/sink_file_audit/email/test_email_transform1/"
                "sink_file_audit_finalize/sfaf1.json",
            ]
        )

        result = collect_all_workflow_audits(
            bucket="s3://test.dev.steeleye.co",
            audits_path="s3://test.dev.steeleye.co/aries/ingest_audits/"
            "sink_file_audit/email/test_email_transform1",
        )

        assert sorted(result) == sorted(
            [
                "s3://test.dev.steeleye.co/aries/ingest_audits/sink_file_audit/email/"
                "test_email_transform1/transform/tf1.json",
                "s3://test.dev.steeleye.co/aries/ingest_audits/sink_file_audit/email/"
                "test_email_transform1/es_connector/es_first_file.json",
                "s3://test.dev.steeleye.co/aries/ingest_audits/sink_file_audit/email/"
                "test_email_transform1/apply_meta/am1.json",
                "s3://test.dev.steeleye.co/aries/ingest_audits/sink_file_audit/email/"
                "test_email_transform1/es_connector/es_last_file.json",
            ]
        )

    def test_assemble_final_audit(self, mocker, audit_jsons, final_audit_json):
        mock_read_json = mocker.patch(
            "aries_se_core_tasks.aries.sink_file_audit.workflow_audits_collector.read_json"
        )
        mock_read_json.side_effect = audit_jsons

        # we want to "read" 3 audit files due to the side_effect
        result = assemble_final_audit(input_audits=["audit.json"] * 4, streamed=True)
        result["input_files"]["file2.fix"]["Order"]["status"] = sorted(
            result["input_files"]["file2.fix"]["Order"]["status"]
        )
        # needed because the expected result has a plain dict
        # but the actual result has a defaultdict
        assert json.loads(json.dumps(result)) == final_audit_json

    def test_it_returns_empty_list_when_no_audits_found(self):
        mock_s3_instance = WorkflowAuditsCollectorMockS3()
        mock_s3_instance.create_mock_bucket(bucket="test.dev.steeleye.co")

        with patch(
            "aries_se_core_tasks.aries.sink_file_audit.workflow_audits_collector.logger"
        ) as mock_logger:
            result = collect_all_workflow_audits(
                bucket="s3://test.dev.steeleye.co",
                audits_path="s3://test.dev.steeleye.co/aries/ingest_audits/"
                "sink_file_audit/email/test_email_transform1",
            )

            mock_logger.assert_has_calls(
                calls=[
                    call.info(
                        "No audits found for s3://test.dev.steeleye.co/aries/ingest_audits/sink_file_audit/email/test_email_transform1"  # noqa: E501
                    ),
                ],
            )

        assert result == []


class WorkflowAuditsCollectorMockS3(AbstractMockS3, ABC):  # type: ignore
    def load_data_into_mock_s3(self, files_names: List[str]):
        for name in files_names:
            self.s3.put_object(
                Bucket=self.bucket,
                Key=name,
                Body=json.dumps({"foo": "bar"}),
            )

    def delayed_load_data_into_mock_s3(self, files_names: List[str]):
        for name in files_names:
            # Delay 1 second to ensure that the WorkflowAuditsCollector
            # always chooses the latest file
            if "last_file" in name:
                time.sleep(1)

            self.s3.put_object(
                Bucket=self.bucket,
                Key=name,
                Body=json.dumps({"foo": "bar"}),
            )
