import pandas as pd
from mock.mock import DEFAULT, patch
from mymarket_tasks.feeds.person.avatrade_client_data_handler.check_existing_person_ids import (
    run_check_existing_person_ids,
)


class TestCheckExistingPersonIds:
    @patch.multiple(
        target="mymarket_tasks.feeds.person.avatrade_client_data_handler.check_existing_person_ids",
        get_es_config=DEFAULT,
        get_repository_by_cluster_version=DEFAULT,
    )
    def test_end_to_end(self, check_ids_source_frame, check_ids_expected_frame, **kwargs):
        kwargs["get_repository_by_cluster_version"].return_value.scroll.return_value = pd.DataFrame(
            {
                "uniqueIds": [
                    ["<EMAIL>", "random_id"],
                    ["random_id2", "random_id3"],
                ]
            }
        )

        result = run_check_existing_person_ids(
            source_frame=check_ids_source_frame, tenant="dummy", skip_serializer=True
        )

        pd.testing.assert_frame_equal(result, check_ids_expected_frame)

    @patch.multiple(
        target="mymarket_tasks.feeds.person.avatrade_client_data_handler.check_existing_person_ids",
        get_es_config=DEFAULT,
        get_repository_by_cluster_version=DEFAULT,
    )
    def test_no_ids_in_es(self, check_ids_source_frame, **kwargs):
        kwargs[
            "get_repository_by_cluster_version"
        ].return_value.scroll.return_value = pd.DataFrame()

        result = run_check_existing_person_ids(
            source_frame=check_ids_source_frame, tenant="dummy", skip_serializer=True
        )

        pd.testing.assert_frame_equal(result, check_ids_source_frame)
