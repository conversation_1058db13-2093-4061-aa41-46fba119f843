import logging
import pandas as pd
from aries_se_core_tasks.core.integration_task import IntegrationTask
from aries_se_core_tasks.core.params import TaskParams
from e164_converter.converter import Converter
from itertools import chain
from pydantic import Field
from se_elastic_schema.static.reference import ImAccountType
from typing import List

converter = Converter()


class Prefixes:
    ACCOUNT = "account"
    ID = "id"


class ColumnKeys:
    DIALING_CODE = "dialingCode"
    EXTENSION = "extension"
    ID = "id"
    LABEL = "label"
    NUMBER = "number"


class ColumnLabels:
    MOBILE = "MOBILE"
    MSTEAMS = "MS Teams"
    WORK = "WORK"
    OTHER = "OTHER"


class TempColumns:
    COMMUNICATION_IDS_UNIQUE = "communication_ids_unique"
    EXTENSION = "extension"
    MOBILE_PHONE = "mobile_phone"
    MOBILE_PHONE_DIALING_CODE = "mobile_phone_dialing_code"
    OTHER_PHONE = "other_phone"
    OTHER_PHONE_DIALING_CODE = "other_phone_dialing_code"
    TRADE_FILE_ID_ACCOUNT_PREFIX = "trade_file_id_account"
    TRADE_FILE_IDS_UNIQUE = "trade_file_ids_unique"
    TRADE_FILE_ID_ID_PREFIX = "trade_file_id_id"
    WORK_PHONE = "work_phone"
    WORK_PHONE_DIALING_CODE = "work_phone_dialing_code"


class Params(TaskParams):
    source_phonenumber_work_col: str = Field(
        ..., description="Name of the work phone number column"
    )
    source_phonenumber_mobile_col: str = Field(
        ...,
        description="Name of the mobile phone number column",
    )
    source_phonenumber_other_col: str = Field(
        ...,
        description="Name of the other phone number column",
    )
    source_phonenumber_extension_col: str = Field(
        ...,
        description="Name of the mobile phone number extension column",
    )
    source_chatid_bbg_col: str = Field(
        ...,
        description="Name of the Bloomberg chat id column",
    )
    source_chatid_msteams_col: str = Field(
        ...,
        description="Name of the MS Teams chat id column",
    )
    source_chatid_other_col_prefix: str = Field(
        ...,
        description="Column Prefix for other chat id columns. There"
        "can be multiple columns with this prefix",
    )
    source_email_col_prefix: str = Field(
        ...,
        description="Column Prefix for email addresses. There"
        "can be multiple columns with this prefix",
    )
    source_trade_file_id_col_prefix: str = Field(
        ...,
        description="Column Prefix for trade file identifiers. There"
        "can be multiple columns with this prefix",
    )
    source_desk_col_prefix: str = Field(
        ...,
        description="Column Prefix for structure desks."
        " There can be multiple columns with this prefix",
    )
    target_phone_numbers_col: str = Field(
        default="communications.phoneNumbers",
        description="Name of target column containing all phone numbers in the required schema"
        " format",
    )
    target_emails_col: str = Field(
        default="communications.emails",
        description="Name of target column containing all email IDs in the required schema format",
    )
    target_imaccounts_col: str = Field(
        default="communications.imAccounts",
        description="Name of target column containing all imAccount IDs in the required schema"
        " format",
    )
    target_trade_file_ids_col: str = Field(
        default="sinkIdentifiers.tradeFileIdentifiers",
        description="Name of target column containing all trade file identifiers in the required"
        " schema format",
    )
    target_unique_ids_col: str = Field(
        "uniqueIds", description="Name of target column containing uniqueIds"
    )
    target_desks_col: str = Field(
        default="structure.desks",
        description="Column name for structure desks.",
    )

    def input_columns(self):
        """Returns a list of required source columns.

        In case of the 'prefix' columns, only 1 'required' column is
        created. For example, EMAIL is created for emails, and not
        EMAIL.1 etc.
        """
        return [
            self.source_phonenumber_work_col,
            self.source_phonenumber_mobile_col,
            self.source_phonenumber_other_col,
            self.source_phonenumber_extension_col,
            self.source_chatid_bbg_col,
            self.source_chatid_msteams_col,
            self.source_chatid_other_col_prefix,
            self.source_email_col_prefix,
            self.source_trade_file_id_col_prefix,
            self.source_desk_col_prefix,
        ]


logger = logging.getLogger(__name__)


class ComplexPersonIdentifiers(IntegrationTask):
    """This task populates a number of person identifiers from the source
    frame. These identifiers include phone numbers, trade file identifiers, im
    accounts and other complex fields (lists or list of dicts). Some of these
    columns might be present multiple times in the source frame. These columns
    are renamed to (for e.g.) Email.1, Email.2 by pandas when it reads the CSV
    file upstream It also populates uniqueIds from the phone numbers, email
    ids, im accounts and trade file identifiers.

    The following list contains all the target columns that this task populates:
    * communications.emails
    * communications.imAccounts
    * communications.phoneNumbers
    * sinkIdentifiers.tradeFileIdentifiers
    * params.target_unique_ids_col
    * params.target_unique_props_col
    * structure.desks
    """

    def _run(
        self,
        source_frame: pd.DataFrame,
        params: Params,
        **kwargs,
    ) -> pd.DataFrame:
        """Process class method which populates all the communication and trade
        file identifiers, as well as uniqueIds."""

        if source_frame.empty:
            logger.warning("Source frame empty, returning empty data frame")
            return source_frame

        df = source_frame.copy()
        # Add required columns if not present
        for col in params.input_columns():
            if col not in df.columns:
                df[col] = pd.NA

        # Populate Phone numbers column after E164 normalising all phone numbers. This also
        # populates temp columns to be used in uniqueIds population
        df = self._populate_phone_numbers(df=df, params=params)

        # Populate email column
        email_columns = list(
            filter(lambda x: x.startswith(params.source_email_col_prefix), df.columns)
        )
        email_columns_mask = df.columns.isin(email_columns)
        df.loc[:, params.target_emails_col] = (
            df.loc[:, email_columns_mask].apply(self._concatenate_email_ids, axis=1).fillna(pd.NA)  # type: ignore
        )

        # Populate im accounts
        im_account_cols = [
            params.source_chatid_bbg_col,
            params.source_chatid_msteams_col,
        ]
        live_chat_cols = list(
            filter(
                lambda x: x.startswith(params.source_chatid_other_col_prefix),
                df.columns,
            )
        )
        im_account_cols.extend(live_chat_cols)
        im_account_columns_mask = df.columns.isin(im_account_cols)
        df.loc[:, params.target_imaccounts_col] = (
            df.loc[:, im_account_columns_mask]  # type: ignore
            .apply(
                lambda x: self._concatenate_im_accounts(
                    im_accounts=x, live_chat_cols=live_chat_cols, params=params
                ),
                axis=1,
            )
            .fillna(pd.NA)
        )

        # Populate Trade file ids column
        trade_file_ids_columns = list(
            filter(
                lambda x: x.startswith(params.source_trade_file_id_col_prefix),
                df.columns,
            )
        )
        trade_file_ids_columns_mask = df.columns.isin(trade_file_ids_columns)
        df.loc[:, params.target_trade_file_ids_col] = (
            df.loc[:, trade_file_ids_columns_mask]  # type: ignore
            .apply(self._populate_trade_file_ids, axis=1)
            .fillna(pd.NA)
        )

        # Populate temp concatenated (list) trade file ids column to be used in
        # populating uniqueIds. If no trade file ids are present for a row,
        # [] is populated
        # Col Mask needs to be re-created because new columns have been added to df
        trade_file_ids_columns_mask = df.columns.isin(trade_file_ids_columns)
        df.loc[:, TempColumns.TRADE_FILE_IDS_UNIQUE] = df.loc[:, trade_file_ids_columns_mask].apply(
            self._concatenate_unique_trade_file_ids, axis=1
        )

        # Populate temp concatenated (list) communication ids column to be used
        # in populating uniqueIds
        communication_cols = list(
            chain(
                *[
                    im_account_cols,
                    email_columns,
                    [
                        TempColumns.WORK_PHONE,
                        TempColumns.MOBILE_PHONE,
                        TempColumns.OTHER_PHONE,
                        TempColumns.EXTENSION,
                    ],
                ]
            )
        )

        # Communication IDs in uniqueIds should NOT have spaces. Note that trade file ids in
        # uniqueIds can have spaces
        communication_cols_mask = df.columns.isin(communication_cols)
        df[TempColumns.COMMUNICATION_IDS_UNIQUE] = df.loc[:, communication_cols_mask].apply(
            lambda x: [
                element.replace(" ", "").lower() for element in list(x) if not pd.isna(element)
            ],
            axis=1,
        )

        # Populate uniqueIds
        unique_ids_cols_mask = df.columns.isin(
            [TempColumns.COMMUNICATION_IDS_UNIQUE, TempColumns.TRADE_FILE_IDS_UNIQUE]
        )
        df[params.target_unique_ids_col] = df.loc[:, unique_ids_cols_mask].apply(
            self._populate_unique_ids, axis=1
        )

        # Populate structure desks column setting id and name values.
        df = self._populate_structure_desk(df=df, params=params)

        target_columns = [
            params.target_imaccounts_col,
            params.target_emails_col,
            params.target_phone_numbers_col,
            params.target_trade_file_ids_col,
            params.target_unique_ids_col,
            params.target_desks_col,
        ]

        target_cols_mask = df.columns.isin(target_columns)
        return df.loc[:, target_cols_mask].fillna(pd.NA)

    @staticmethod
    def _concatenate_email_ids(email_ids: pd.Series) -> List[str] | None:
        """Appends all the email IDs in the input Series into a list, and
        returns it.

        If there are no email IDs, it returns None
        :param: email_ids: Series containing the values for all email columns for 1 row.
                The index of the series comes from the column names of the email
                columns in the original df
        :returns: Concatenated list of email ids, or None
        """
        concat_list = [email_id for email_id in email_ids if not pd.isna(email_id)]
        return None if not concat_list else concat_list

    @staticmethod
    def _populate_trade_file_ids(trade_file_ids: pd.Series) -> List[dict] | None:
        """
        :param: trade_file_ids: Series containing the values for all trade file id
                columns for 1 row.
                The index of the series comes from the column names of the trade
                file id columns in the original df
        :returns: List of trade file ids in the required format
        """
        concat_list = [
            [
                {ColumnKeys.ID: trade_file_id, ColumnKeys.LABEL: Prefixes.ID},
                {ColumnKeys.ID: trade_file_id, ColumnKeys.LABEL: Prefixes.ACCOUNT},
            ]
            for trade_file_id in trade_file_ids
            if not pd.isna(trade_file_id)
        ]
        return None if not concat_list else list(chain(*concat_list))

    @staticmethod
    def _concatenate_unique_trade_file_ids(
        trade_file_ids: pd.Series,
    ) -> List[str]:
        """Returns a list of concatenated trade file ids which are required for
        populating uniqueIds.

        These IDs include the prefix (id/account). If there
        are no trade file ids in the source, it returns an empty list
        :param: trade_file_ids: Series containing the values for all trade_file_ids columns for
                1 row. The index of the series comes from the column names of the trade
                file id columns in the original df
        :returns: List of concatenated trade file ids, to be used in uniqueIds population
        """
        concat_list = [
            [f"{Prefixes.ID}:{trade_file_id}", f"{Prefixes.ACCOUNT}:{trade_file_id}"]
            for trade_file_id in trade_file_ids
            if not pd.isna(trade_file_id)
        ]
        return [] if not concat_list else list(chain(*concat_list))

    @classmethod
    def _populate_phone_numbers(cls, df: pd.DataFrame, params: Params) -> pd.DataFrame:
        """This method takes as input a source df containing all the source
        columns required for populating phone numbers.

        It E164-normalises each of these phone
        numbers (other than the extension, which is left as it is). It returns four
        temp columns containing e164-normalised mobile, work and other phone numbers and
        the extension. It also populates the target communications.phoneNumbers column
        as per the schema.
        :param: df: source df containing phone numbers columns
        :param: params: Params instance
        :returns: DataFrame with 3 normalised temp phone number columns, and a
                  consolidated phonenumbers column as per the schema
        """
        # TODO: move E164 conversion into schema, and populate the raw numbers here
        #
        # Map Extension
        extension_present_mask = df.loc[:, params.source_phonenumber_extension_col].notnull()
        df.loc[extension_present_mask, TempColumns.EXTENSION] = df.loc[
            extension_present_mask, params.source_phonenumber_extension_col
        ]

        # Handle MOBILE phone number with temporary columns
        df = cls._convert_and_populate_phone_and_code(
            df=df,
            source_phone_number_col=params.source_phonenumber_mobile_col,
            phone_temp_col=TempColumns.MOBILE_PHONE,
            dialing_code_temp_col=TempColumns.MOBILE_PHONE_DIALING_CODE,
        )
        # Handle WORK phone number with temporary columns
        df = cls._convert_and_populate_phone_and_code(
            df=df,
            source_phone_number_col=params.source_phonenumber_work_col,
            phone_temp_col=TempColumns.WORK_PHONE,
            dialing_code_temp_col=TempColumns.WORK_PHONE_DIALING_CODE,
        )
        # Handle OTHER phone number with temporary columns
        df = cls._convert_and_populate_phone_and_code(
            df=df,
            source_phone_number_col=params.source_phonenumber_other_col,
            phone_temp_col=TempColumns.OTHER_PHONE,
            dialing_code_temp_col=TempColumns.OTHER_PHONE_DIALING_CODE,
        )

        normalised_phone_number_cols = [
            TempColumns.MOBILE_PHONE,
            TempColumns.MOBILE_PHONE_DIALING_CODE,
            TempColumns.WORK_PHONE,
            TempColumns.WORK_PHONE_DIALING_CODE,
            TempColumns.OTHER_PHONE,
            TempColumns.OTHER_PHONE_DIALING_CODE,
            TempColumns.EXTENSION,
        ]

        phone_no_columns_mask = df.columns.isin(normalised_phone_number_cols)
        df[params.target_phone_numbers_col] = (
            df.loc[:, phone_no_columns_mask]  # type: ignore
            .apply(cls._concatenate_phone_numbers, axis=1)
            .fillna(pd.NA)
        )
        # Drop dialling code and converter columns
        columns_to_drop = [
            TempColumns.MOBILE_PHONE_DIALING_CODE,
            TempColumns.WORK_PHONE_DIALING_CODE,
            TempColumns.OTHER_PHONE_DIALING_CODE,
        ]
        df = df.drop(columns_to_drop, axis=1)
        return df

    @staticmethod
    def _convert_and_populate_phone_and_code(
        df: pd.DataFrame,
        source_phone_number_col,
        phone_temp_col,
        dialing_code_temp_col,
    ):
        _e164_result_temp_col = "__e164_result__"
        present_mask = df.loc[:, source_phone_number_col].notnull()
        df.loc[present_mask, _e164_result_temp_col] = df.loc[
            present_mask, source_phone_number_col
        ].apply(lambda x: converter.convert(x))

        df.loc[present_mask, phone_temp_col] = df.loc[present_mask, _e164_result_temp_col].apply(
            lambda e164_result: e164_result.transformed
            if e164_result.e164_derived
            else e164_result.raw
        )

        # Note: the dialing code is obtained from the country code field.
        df.loc[present_mask, dialing_code_temp_col] = df.loc[
            present_mask, _e164_result_temp_col
        ].apply(lambda e164_result: e164_result.country_code if e164_result.e164_derived else pd.NA)
        return df.drop(_e164_result_temp_col, axis=1)

    @staticmethod
    def _concatenate_phone_numbers(phone_numbers: pd.Series) -> List[dict] | None:
        """Concatenates all the phone numbers in the im_accounts Series into a
        list of dictionaries.

        The work, mobile and other phone dictionaries have a label and
        id, while the extension dictionary has a single key 'extension'.
        :param: phone_numbers: Series containing the values for all phone no. columns for
                1 row. The index of the series comes from the column names of the temp
                phone number columns created in the calling function
        :returns: List of concatenated phone numbers in the required format
        """
        concat_list = []
        phone_and_code = {
            (
                TempColumns.MOBILE_PHONE,
                TempColumns.MOBILE_PHONE_DIALING_CODE,
                ColumnLabels.MOBILE,
            ),
            (
                TempColumns.WORK_PHONE,
                TempColumns.WORK_PHONE_DIALING_CODE,
                ColumnLabels.WORK,
            ),
            (
                TempColumns.OTHER_PHONE,
                TempColumns.OTHER_PHONE_DIALING_CODE,
                ColumnLabels.OTHER,
            ),
        }
        for phone, dialog_code, phone_label in phone_and_code:
            if not pd.isna(phone_numbers[phone]):
                phone_dict = {
                    ColumnKeys.LABEL: phone_label,
                    ColumnKeys.NUMBER: phone_numbers[phone],
                }
                # Add dialling code if not null
                if not pd.isna(phone_numbers[dialog_code]):
                    phone_dict[ColumnKeys.DIALING_CODE] = phone_numbers[dialog_code]

                concat_list.append(phone_dict)

        if not pd.isna(phone_numbers[TempColumns.EXTENSION]):
            concat_list.append({ColumnKeys.EXTENSION: phone_numbers[TempColumns.EXTENSION]})
        return None if not concat_list else concat_list

    @staticmethod
    def _concatenate_im_accounts(
        im_accounts: pd.Series, live_chat_cols: List[str], params: Params
    ) -> List[dict] | None:
        """Concatenates all the im accounts in the im_accounts Series into a
        list of dictionaries.

        Each of the dictionaries has a label and id as per
        the schema. There are multiple live chat columns in the source series,
        identified by the live_chat_cols argument.
        :param: im_accounts: Series containing the values for all im account columns
                for 1 row. The index of the series comes from the im account column
                names in the original df
        :returns: List of concatenated im_accounts in the required schema format
        """
        concat_list: list[dict] = []
        if not pd.isna(im_accounts[params.source_chatid_bbg_col]):
            concat_list.append(
                {
                    ColumnKeys.LABEL: ImAccountType.BBG.value,
                    ColumnKeys.ID: im_accounts[params.source_chatid_bbg_col],
                }
            )
        # TODO: The label for MS Teams has temporarily been set to 'Live chat'
        # because of schema issues. This needs to be set back to MS Teams later
        if not pd.isna(im_accounts[params.source_chatid_msteams_col]):
            concat_list.append(
                {
                    ColumnKeys.LABEL: ImAccountType.LIVE_CHAT.value,
                    ColumnKeys.ID: im_accounts[params.source_chatid_msteams_col],
                }
            )
        concat_live_chat_aux = [
            [
                {
                    ColumnKeys.LABEL: ImAccountType.LIVE_CHAT.value,
                    ColumnKeys.ID: im_accounts[live_chat_col],
                },
            ]
            for live_chat_col in live_chat_cols
            if not pd.isna(im_accounts[live_chat_col])
        ]
        # Flatten live chat list of list of dicts to list of dicts
        concat_live_chat_list: list[dict] = list(chain(*concat_live_chat_aux))
        for live_chat_dict in concat_live_chat_list:
            concat_list.append(live_chat_dict)
        return None if not concat_list else concat_list

    @staticmethod
    def _populate_unique_ids(identifiers: pd.Series) -> List[str] | None:
        """Takes a Series containing 2 lists -- list 1 contains trade file
        identifiers and list 2 contains communication identifiers.

        It creates one unified list
        containing identifiers from both lists after removing duplicates. If both lists
         are empty, it returns None. All ids are converted to lowercase.
        :param: identifiers: Series containing 2 lists (trade file identifiers and
                communication identifiers) for 1 row.
                The index of the series comes from the column names created in the
                calling function
        :returns: Concatenated list of unique ids, or None
        """
        concat_list = [
            identifier_list for identifier_list in identifiers if not all(pd.isna(identifier_list))
        ]
        # Flatten and remove duplicates
        concat_list = list(set(chain(*concat_list)))
        # Make all ids lower case
        concat_list = [element.lower() for element in concat_list]
        return None if not concat_list else concat_list

    @classmethod
    def _populate_structure_desk(cls, df: pd.DataFrame, params: Params) -> pd.DataFrame:
        desk_columns = list(
            filter(lambda x: x.startswith(params.source_desk_col_prefix), df.columns)
        )
        desk_columns_mask = df.columns.isin(desk_columns)

        df[params.target_desks_col] = df.loc[:, desk_columns_mask].apply(
            cls._validate_and_create_structure_desk,
            axis=1,
        )
        return df

    @classmethod
    def _validate_and_create_structure_desk(cls, row) -> List[dict] | object:
        desks = []
        for item in row:
            if not pd.isna(item):
                desks.append({"id": item, "name": item})

        return desks if desks else pd.NA


def run_complex_person_identifiers(
    source_frame: pd.DataFrame,
    params: Params,
    app_metrics_path: str | None = None,
    audit_path: str | None = None,
    **kwargs,
):
    task = ComplexPersonIdentifiers(app_metrics_path=app_metrics_path, audit_path=audit_path)
    return task.run(source_frame=source_frame, params=params, **kwargs)
