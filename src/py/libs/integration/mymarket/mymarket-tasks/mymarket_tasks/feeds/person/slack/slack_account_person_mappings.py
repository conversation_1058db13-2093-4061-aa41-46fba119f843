import pandas as pd
from mymarket_tasks.feeds.person.helper_functions import generate_unique_ids
from mymarket_tasks.feeds.person.slack.static import (
    ImAccountKeys,
    MymarketSlackSourceColumns,
    get_phone_numbers,
)
from se_core_tasks.abstractions.transformations.abstract_person_transformations import (
    AbstractPersonTransformations,
)
from se_elastic_schema.static.reference import ImAccountType


class SlackPersonAccountPersonMappings(AbstractPersonTransformations):
    def process(self) -> pd.DataFrame:
        self.communications_emails()
        self.communications_im_accounts()
        self.communications_phone_numbers()
        self.name()
        self.personal_details_first_name()
        self.personal_details_last_name()
        self.structure_role()
        self.unique_ids()

        return self.target_df

    def _communications_emails(self) -> pd.Series:
        # The email key is always present in the input,
        # but it may not contain a value, then it will be pd.NA
        result = pd.Series(pd.NA, index=self.source_frame.index)
        source_emails_not_null_mask = self.source_frame.loc[
            :, MymarketSlackSourceColumns.EMAIL
        ].notnull()
        result[source_emails_not_null_mask] = (
            self.source_frame.loc[source_emails_not_null_mask, MymarketSlackSourceColumns.EMAIL]
            .str.lower()
            .apply(lambda x: [x])
        )
        return result

    def _communications_im_accounts(self) -> pd.Series:
        return (
            self.source_frame.loc[:, MymarketSlackSourceColumns.SLACK_ID]
            .str.lower()
            .apply(
                lambda x: [
                    {
                        ImAccountKeys.ID: x,
                        ImAccountKeys.LABEL: ImAccountType.SLACK.value,
                    }
                ]
            )
        )

    def _communications_phone_numbers(self) -> pd.Series:
        """
        Takes the number in `phoneNumbers number`, converts to e164
        Creates a dict with the structure:
            {
                "number": e164.transformed if e164.e164_derived is True else e164.raw,
                "dialingCode": e164.country_code if e164.e164_derived is True else do not create,
                "label": `phoneNumbers label`
            }
        """
        return self.source_frame.loc[:, MymarketSlackSourceColumns.PHONE].apply(
            lambda phone_number: get_phone_numbers(phone_number=str(phone_number))
            if not pd.isna(phone_number)
            else pd.NA
        )

    def _meta_model(self) -> pd.Series:
        pass

    def _name(self) -> pd.Series:
        return pd.Series(
            data=self.source_frame[MymarketSlackSourceColumns.NAME],
            index=self.source_frame.index,
        )

    def _personal_details_first_name(self) -> pd.Series:
        return self.source_frame[MymarketSlackSourceColumns.NAME].apply(
            lambda x: x.split(" ")[0] if isinstance(x, str) else pd.NA
        )

    def _personal_details_middle_name(self) -> pd.Series:
        pass

    def _personal_details_last_name(self) -> pd.Series:
        return self.source_frame[MymarketSlackSourceColumns.NAME].apply(
            lambda x: x.split(" ")[-1] if isinstance(x, str) and " " in x else pd.NA
        )

    def _structure_role(self) -> pd.Series:
        return pd.Series(
            data=self.source_frame[MymarketSlackSourceColumns.TITLE],
            index=self.source_frame.index,
        )

    def _unique_ids(self) -> pd.Series:
        return self.target_df.apply(generate_unique_ids, axis=1)

    def _post_process(self):
        pass

    def _pre_process(self):
        pass

    def _counterparty_name(self) -> pd.Series:
        pass

    def _details_client_mandate(self) -> pd.Series:
        pass

    def _location_home_address_address(self) -> pd.Series:
        pass

    def _location_home_address_city(self) -> pd.Series:
        pass

    def _location_home_address_country(self) -> pd.Series:
        pass

    def _location_home_address_postal_code(self) -> pd.Series:
        pass

    def _location_office_address_address(self) -> pd.Series:
        pass

    def _location_office_address_city(self) -> pd.Series:
        pass

    def _location_office_address_country(self) -> pd.Series:
        pass

    def _location_office_address_postal_code(self) -> pd.Series:
        pass

    def _monitoring_custom_1(self) -> pd.Series:
        pass

    def _monitoring_custom_2(self) -> pd.Series:
        pass

    def _monitoring_custom_3(self) -> pd.Series:
        pass

    def _monitoring_is_monitored(self) -> pd.Series:
        pass

    def _monitoring_is_monitored_for(self) -> pd.Series:
        pass

    def _monitoring_jurisdiction_business_line(self) -> pd.Series:
        pass

    def _monitoring_jurisdiction_country(self) -> pd.Series:
        pass

    def _monitoring_jurisdiction_legal_entity(self) -> pd.Series:
        pass

    def _monitoring_jurisdiction_region(self) -> pd.Series:
        pass

    def _monitoring_risk(self) -> pd.Series:
        pass

    def _official_identifiers_branch_country(self) -> pd.Series:
        pass

    def _official_identifiers_client_mandate(self) -> pd.Series:
        pass

    def _official_identifiers_concat_id(self) -> pd.Series:
        pass

    def _official_identifiers_employee_id(self) -> pd.Series:
        pass

    def _official_identifiers_mifir_id(self) -> pd.Series:
        pass

    def _official_identifiers_mifir_id_sub_type(self) -> pd.Series:
        pass

    def _official_identifiers_mifir_id_type(self) -> pd.Series:
        pass

    def _official_identifiers_national_ids(self) -> pd.Series:
        pass

    def _official_identifiers_passports(self) -> pd.Series:
        pass

    def _official_identifiers_trader_ids(self) -> pd.Series:
        pass

    def _personal_details_dob(self) -> pd.Series:
        pass

    def _personal_details_nationality(self) -> pd.Series:
        pass

    def _retail_or_professional(self) -> pd.Series:
        pass

    def _sink_file_identifiers_order_file_identifiers(self) -> pd.Series:
        pass

    def _sink_file_identifiers_trade_file_identifiers(self) -> pd.Series:
        pass

    def _source_index(self) -> pd.Series:
        pass

    def _source_key(self) -> pd.Series:
        pass

    def _structure_client_date_account_opened(self) -> pd.Series:
        pass

    def _structure_department(self) -> pd.Series:
        pass

    def _structure_desks(self) -> pd.Series:
        pass

    def _structure_type(self) -> pd.Series:
        pass
