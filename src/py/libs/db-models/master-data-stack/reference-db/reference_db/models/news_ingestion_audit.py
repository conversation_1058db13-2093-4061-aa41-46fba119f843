from reference_db.declarative_base import Base
from se_db_utils.psql_utils import utcnow
from sqlalchemy import Column
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.sql import text
from sqlalchemy.sql.sqltypes import <PERSON>olean, DateTime, Integer, String, Text


class NewsIngestionAudit(Base):
    __tablename__ = "NewsIngestionAudit"
    __table_args__ = {"schema": "RefinitivNews"}
    id = Column(
        UUID(as_uuid=True),
        primary_key=True,
        nullable=False,
        server_default=text("gen_random_uuid()"),
    )
    messageId = Column(String(64), nullable=False)
    traceId = Column(String(64), nullable=False)
    isSuccessful = Column(
        Boolean,
        nullable=False,
    )
    failedReason = Column(Text, nullable=True)

    startDateTime = Column(DateTime, nullable=False)
    finishDateTime = Column(DateTime, nullable=False)

    sqsQueueSize = Column(Integer)
    conductorQueueSize = Column(Integer)

    createdDateTime = Column(DateTime, nullable=False, server_default=utcnow())
    createdBy = Column(Text, nullable=False, server_default=text("'UNKNOWN'"))
    updatedDateTime = Column(DateTime, nullable=True, onupdate=utcnow())
    updatedBy = Column(Text, nullable=True, onupdate=text("'UNKNOWN'"))
