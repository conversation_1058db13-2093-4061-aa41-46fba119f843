from reference_db.declarative_base import Base
from se_db_utils.psql_utils import utcnow
from sqlalchemy import Column
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.sql import text
from sqlalchemy.sql.sqltypes import DateTime, Integer, String, Text


class LoanCdsExtraction(Base):
    __tablename__ = "LoanCdsExtraction"
    __table_args__ = ({"schema": "MarketDataMonitoring"},)

    id = Column(
        UUID(as_uuid=True),
        primary_key=True,
        nullable=False,
        server_default=text("gen_random_uuid()"),
    )
    status = Column(
        String(7),
        nullable=False,
        server_default=text("'RUNNING'"),
        comment='["RUNNING", "SUCCESS", "FAILED"]',
    )
    type_ = Column(String(64), nullable=False, name="type")  # type: ignore
    traceId = Column(String(128), nullable=False)
    fileName = Column(Text, nullable=False)
    rawTotalInstrument = Column(Integer, nullable=True)
    parquetUpdated = Column(Integer, nullable=True)
    startDateTime = Column(DateTime, nullable=True)  # TODO: is this needed?
    endDateTime = Column(DateTime, nullable=True)  # TODO: is this needed?
    createdDateTime = Column(DateTime, nullable=False, server_default=utcnow())
    createdBy = Column(Text, nullable=False, server_default=text("'system'"))
    updatedDateTime = Column(DateTime, nullable=True, onupdate=utcnow())
    updatedBy = Column(Text, nullable=True, onupdate=text("'system'"))
