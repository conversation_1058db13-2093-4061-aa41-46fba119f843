from se_db_utils.psql_utils import utcnow
from sqlalchemy import Column, DateTime, String
from sqlalchemy.dialects.postgresql import ARRAY
from tenant_db.declarative_base import Base


class Topic(Base):
    __tablename__ = "Topic"

    # Lexica data
    topic = Column(String, nullable=False, primary_key=True)
    language = Column(String, nullable=False, primary_key=True)
    terms = Column(ARRAY(String), nullable=False)

    # Metadata
    version = Column(String, nullable=False, primary_key=True)
    createdBy = Column(String, nullable=False)
    createdDateTime = Column(DateTime(timezone=True), nullable=False, server_default=utcnow())
