BEGIN;

-- Running upgrade 1728329878_iNS5pSYY -> 1728329953_2J9YN9s4

CREATE TABLE "Permission" (
    id UUID NOT NULL, 
    name VARCHAR NOT NULL, 
    description VARCHAR, 
    "createdDateTime" TIM<PERSON><PERSON><PERSON> WITHOUT TIME ZONE DEFAULT TIMEZONE('utc', CURRENT_TIMESTAMP) NOT NULL, 
    "updatedDateTime" TIMESTAMP WITHOUT TIME ZONE, 
    "createdBy" TEXT, 
    "updatedBy" TEXT, 
    CONSTRAINT "pk_Permission" PRIMARY KEY (id), 
    CONSTRAINT "uq_Permission_name" UNIQUE (name)
);

CREATE TABLE "Role" (
    id UUID NOT NULL, 
    name VARCHAR NOT NULL, 
    module VARCHAR NOT NULL, 
    description VARCHAR, 
    "isDefault" BOOLEAN NOT NULL, 
    "createdDateTime" TIMESTAMP WITHOUT TIME ZONE DEFAULT TIMEZONE('utc', CURRENT_TIMESTAMP) NOT NULL, 
    "updatedDateTime" TIMESTAMP WITHOUT TIME ZONE, 
    "createdBy" TEXT, 
    "updatedBy" TEXT, 
    CONSTRAINT "pk_Role" PRIMARY KEY (id, module), 
    CONSTRAINT "uq_Role_name" UNIQUE (name, module)
);

CREATE UNIQUE INDEX ix_module ON "Role" (module, "isDefault") WHERE "isDefault" IS true;

CREATE TABLE "ModulePermission" (
    "permissionId" UUID NOT NULL, 
    module VARCHAR NOT NULL, 
    "subModule" VARCHAR NOT NULL, 
    "createdDateTime" TIMESTAMP WITHOUT TIME ZONE DEFAULT TIMEZONE('utc', CURRENT_TIMESTAMP) NOT NULL, 
    "updatedDateTime" TIMESTAMP WITHOUT TIME ZONE, 
    "createdBy" TEXT, 
    "updatedBy" TEXT, 
    CONSTRAINT "pk_ModulePermission" PRIMARY KEY ("permissionId", module, "subModule"), 
    CONSTRAINT "fk_ModulePermission_permissionId_Permission" FOREIGN KEY("permissionId") REFERENCES "Permission" (id)
);

CREATE TABLE "UserRole" (
    id UUID NOT NULL, 
    "roleId" UUID NOT NULL, 
    "userId" VARCHAR NOT NULL, 
    module VARCHAR NOT NULL, 
    "createdBy" TEXT, 
    "updatedBy" TEXT, 
    "createdDateTime" TIMESTAMP WITHOUT TIME ZONE DEFAULT TIMEZONE('utc', CURRENT_TIMESTAMP) NOT NULL, 
    "updatedDateTime" TIMESTAMP WITHOUT TIME ZONE, 
    CONSTRAINT "pk_UserRole" PRIMARY KEY (id), 
    CONSTRAINT "fk_UserRole_roleId_Role" FOREIGN KEY("roleId", module) REFERENCES "Role" (id, module), 
    CONSTRAINT "uq_UserRole_roleId" UNIQUE ("roleId", "userId"), 
    CONSTRAINT "uq_UserRole_userId" UNIQUE ("userId", module)
);

CREATE TABLE "PendingRequest" (
    id UUID NOT NULL, 
    "userId" VARCHAR NOT NULL, 
    request JSONB, 
    status VARCHAR DEFAULT 'PENDING' NOT NULL, 
    "permissionId" UUID NOT NULL, 
    module VARCHAR NOT NULL, 
    "subModule" VARCHAR NOT NULL, 
    "createdDateTime" TIMESTAMP WITHOUT TIME ZONE DEFAULT TIMEZONE('utc', CURRENT_TIMESTAMP) NOT NULL, 
    "updatedDateTime" TIMESTAMP WITHOUT TIME ZONE, 
    "createdBy" TEXT, 
    "updatedBy" TEXT, 
    CONSTRAINT "pk_PendingRequest" PRIMARY KEY (id), 
    CONSTRAINT "fk_PendingRequest_permissionId_ModulePermission" FOREIGN KEY("permissionId", module, "subModule") REFERENCES "ModulePermission" ("permissionId", module, "subModule")
);

CREATE TABLE "RolePermission" (
    id UUID NOT NULL, 
    "roleId" UUID NOT NULL, 
    "permissionId" UUID NOT NULL, 
    module VARCHAR NOT NULL, 
    "subModule" VARCHAR NOT NULL, 
    allowed VARCHAR NOT NULL, 
    "createdBy" VARCHAR, 
    "updatedBy" VARCHAR, 
    "createdDateTime" TIMESTAMP WITHOUT TIME ZONE DEFAULT TIMEZONE('utc', CURRENT_TIMESTAMP) NOT NULL, 
    "updatedDateTime" TIMESTAMP WITHOUT TIME ZONE, 
    CONSTRAINT "pk_RolePermission" PRIMARY KEY (id), 
    CONSTRAINT "fk_RolePermission_permissionId_ModulePermission" FOREIGN KEY("permissionId", module, "subModule") REFERENCES "ModulePermission" ("permissionId", module, "subModule"), 
    CONSTRAINT "fk_RolePermission_permissionId_Permission" FOREIGN KEY("permissionId") REFERENCES "Permission" (id), 
    CONSTRAINT "fk_RolePermission_roleId_Role" FOREIGN KEY("roleId", module) REFERENCES "Role" (id, module) ON DELETE CASCADE, 
    CONSTRAINT "uq_RolePermission_roleId" UNIQUE ("roleId", "permissionId", module, "subModule")
);

UPDATE alembic_version SET version_num='1728329953_2J9YN9s4' WHERE alembic_version.version_num = '1728329878_iNS5pSYY';

COMMIT;

