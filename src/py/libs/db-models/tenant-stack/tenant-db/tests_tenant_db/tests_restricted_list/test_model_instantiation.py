import pytest
import uuid
from datetime import datetime
from tenant_db.models.restricted_list.restricted_list import (  # type: ignore
    Party,
    RestrictedList,
    Restriction,
    RestrictionAttachment,
    RestrictionComment,
    RestrictionValidationError,
    WatchReason,
)
from tenant_db.schema.restricted_list import MinimumSizeType, Permanency  # type: ignore


@pytest.fixture
def validation_data():
    return dict(
        id=str(uuid.uuid4()),
        fieldPath="",
        code="",
        message="",
        category="Prices",
        modulesAffected=["Watch / Restricted List"],
        severity="HIGH",
        source="steeleye",
    )


@pytest.fixture
def restriction_data():
    return dict(
        alternateId="ABC123",
        provenance="User Created",
        changeReason="Sample change reason",
        dataSource="Sample data source",
        accessToRestrictedInformationDateTime=datetime.strptime(
            "2023-01-01T00:00:00", "%Y-%m-%dT%H:%M:%S"
        ),
        fromDateTime=datetime.strptime("2023-01-01T00:00:00", "%Y-%m-%dT%H:%M:%S"),
        toDateTime=datetime.strptime("2023-01-10T00:00:00", "%Y-%m-%dT%H:%M:%S"),
        delayAlertsCalendarDays=2,
        identifier="ID001",
        identifierType="ISIN",
        label=["Label1", "Label2"],
        minimumSize=100.0,
        minimumSizeType=MinimumSizeType.ORDER_NOTIONAL.value,
        minimumSizeCurrency="USD",
        permanency=Permanency.TEMPORARY.value,
        sourceKey="SRC001",
        createdBy="User",
        createdDateTime=int(
            datetime.strptime("2023-01-10T00:00:00", "%Y-%m-%dT%H:%M:%S").timestamp()
        ),
    )


@pytest.fixture
def restricted_list_data():
    return dict(
        id=str(uuid.uuid4()),
        alternateId="ABC",
        description="Sample restricted list",
        isEditableWithinApp=True,
        name="Sample List",
        owner="John Doe",
        listType="Restriction",
        listTypeCustom="Custom",
        createdDateTime=int(
            datetime.strptime("2023-01-01T00:00:00", "%Y-%m-%dT%H:%M:%S").timestamp()
        ),
        createdBy="User",
    )


def test_restricted_list_data_valid_instantiation(restricted_list_data):
    restricted_list = RestrictedList(**restricted_list_data)
    party1 = Party(name="Party1")
    party2 = Party(name="Party2")

    watch_reason = WatchReason(
        category="Sample category",
        categorySub="Sample sub-category",
        code="001",
        event="Sample event",
        eventDateTime=datetime.strptime("2023-01-01T00:00:00", "%Y-%m-%dT%H:%M:%S"),
        eventType="Meeting",
        eventTypeCustom="Custom event type",
        eventId="EVT001",
        description="Sample description",
    )

    restriction = Restriction(
        alternateId="ABC123",
        provenance="User Created",
        changeReason="Sample change reason",
        dataSource="Sample data source",
        accessToRestrictedInformationDateTime=datetime.strptime(
            "2023-01-01T00:00:00", "%Y-%m-%dT%H:%M:%S"
        ),
        fromDateTime=datetime.strptime("2023-01-01T00:00:00", "%Y-%m-%dT%H:%M:%S"),
        toDateTime=datetime.strptime("2023-01-10T00:00:00", "%Y-%m-%dT%H:%M:%S"),
        delayAlertsCalendarDays=2,
        identifier="ID001",
        identifierType="ISIN",
        label=["Label1", "Label2"],
        minimumSize=100.0,
        minimumSizeType=MinimumSizeType.ORDER_NOTIONAL.value,
        minimumSizeCurrency="USD",
        party=[party1, party2],
        permanency=Permanency.TEMPORARY.value,
        sourceKey="SRC001",
        restrictedListId=restricted_list.id,
        watchReason=watch_reason,
        createdBy="User",
        createdDateTime=int(
            datetime.strptime("2023-01-10T00:00:00", "%Y-%m-%dT%H:%M:%S").timestamp()
        ),
    )

    comment = RestrictionComment(restriction=restriction)

    validation_data = RestrictionValidationError(
        id=str(uuid.uuid4()),
        fieldPath="ABC",
        code="ABC",
        message="ABC",
        category="Prices",
        modulesAffected=["Watch / Restricted List"],
        severity="HIGH",
        source="steeleye",
    )

    comment_attachment = RestrictionAttachment(
        restrictedListId=None,
        commentId=comment.id,
        fileName="ABC",
        sizeInBytes=10,
        bucket="ABC",
        key="ABC",
        fileType="ABC",
    )
    comment.attachment.append(comment_attachment)

    restricted_list_attachment = RestrictionAttachment(
        restrictedListId=restricted_list.id,
        commentId=None,
        fileName="ABC",
        sizeInBytes=10,
        bucket="ABC",
        key="ABC",
        fileType="ABC",
    )
    restricted_list.attachment.append(restricted_list_attachment)

    restriction.comment.append(comment)
    restriction.validationError.append(validation_data)
    restricted_list.restriction.append(restriction)

    assert isinstance(restricted_list, RestrictedList)


def test_restricted_list_data_invalid_instantiation():
    # Restricted List Enum tests
    with pytest.raises(ValueError):
        RestrictedList(
            id=str(uuid.uuid4()),
            alternateId="ABC",
            description="Sample restricted list",
            isEditableWithinApp=True,
            name="Sample List",
            owner="John Doe",
            listType="Error",
            listTypeCustom="Custom",
            createdDateTime=datetime.strptime("2023-01-01T00:00:00", "%Y-%m-%dT%H:%M:%S"),
            createdBy="User",
        )


def test_watch_reason_data_invalid_instantiation():
    # Watch Reason Enum tests
    with pytest.raises(ValueError):
        WatchReason(
            category="Sample category",
            categorySub="Sample sub-category",
            code="001",
            event="Sample event",
            eventDateTime=datetime.strptime("2023-01-01T00:00:00", "%Y-%m-%dT%H:%M:%S"),
            eventType="Error",
            eventTypeCustom="Custom event type",
            eventId="EVT001",
            description="Sample description",
        )


# Validation Error Enum tests
def test_validation_error_data_invalid_instantiation_1(validation_data):
    with pytest.raises(ValueError):
        temp_data = validation_data
        temp_data["category"] = ""
        RestrictionValidationError(**temp_data)


def test_validation_error_data_invalid_instantiation_2(validation_data):
    with pytest.raises(ValueError):
        temp_data = validation_data
        temp_data["modulesAffected"] = [""]
        RestrictionValidationError(**temp_data)


def test_validation_error_data_invalid_instantiation_3(validation_data):
    with pytest.raises(ValueError):
        temp_data = validation_data
        temp_data["severity"] = ""
        RestrictionValidationError(**temp_data)


def test_validation_error_data_invalid_instantiation_4(validation_data):
    with pytest.raises(ValueError):
        temp_data = validation_data
        temp_data["source"] = ""
        RestrictionValidationError(**temp_data)


# Restriction Enum tests
def test_restriction_data_invalid_instantiation_1(restriction_data):
    with pytest.raises(ValueError):
        temp_data = restriction_data
        temp_data["permanency"] = ""
        Restriction(**temp_data)


def test_restriction_data_invalid_instantiation_2(restriction_data):
    with pytest.raises(ValueError):
        temp_data = restriction_data
        temp_data["provenance"] = ""
        Restriction(**temp_data)


def test_restriction_data_invalid_instantiation_3(restriction_data):
    with pytest.raises(ValueError):
        temp_data = restriction_data
        temp_data["identifierType"] = ""
        Restriction(**temp_data)


def test_restriction_data_invalid_instantiation_4(restriction_data):
    with pytest.raises(ValueError):
        temp_data = restriction_data
        temp_data["minimumSizeType"] = ""
        Restriction(**temp_data)


def test_restriction_data_invalid_instantiation_5(restriction_data):
    with pytest.raises(ValueError):
        temp_data = restriction_data
        temp_data["minimumSizeCurrency"] = "USDE"
        Restriction(**temp_data)


def test_check_id_generation_restricted_list(
    restricted_list_data, restriction_data, validation_data
):
    temp_data = restricted_list_data
    temp_data["alternateId"] = None
    temp_data["id"] = None
    validation_data["id"] = None
    restriction_data["validationError"] = validation_data
    restriction_data["comment"] = RestrictionComment(
        description="Test Comment",
    )
    temp_data["restriction"] = restriction_data
    obj = RestrictedList(**temp_data)

    assert obj.alternateId
    assert obj.id
    assert obj.restriction[0].id
    assert obj.restriction[0].restrictedListId
    assert obj.restriction[0].restrictedListAlternateId
    assert obj.restriction[0].validationError[0].id
    assert obj.restriction[0].validationError[0].restrictionId
    assert obj.restriction[0].comment[0].id
    assert obj.restriction[0].comment[0].restrictionId
