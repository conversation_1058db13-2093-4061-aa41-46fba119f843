# type: ignore
import pydantic
from api_sdk.exceptions import BadInput
from api_sdk.models.request_params import NonPaginatedDatedListParams
from api_sdk.schemas.base import Field
from api_sdk.utils.utils import StringEnum
from enum import auto
from pydantic import root_validator
from se_elastic_schema.static.surveillance import (
    BehaviourQueryKind,
)
from typing import Dict, List, Optional


class BulkImportType(StringEnum):
    COMMS = auto()
    ORDERS = auto()


class CaseBulkFilter(pydantic.BaseModel):
    type: BulkImportType
    start: Optional[int]
    end: Optional[int]
    f: str
    search: Optional[str] = None

    def to_params(self) -> NonPaginatedDatedListParams:
        return NonPaginatedDatedListParams(
            request=None, f=self.f, start=self.start, end=self.end, search=self.search
        )


class RecordIn(pydantic.BaseModel):
    """Input model only requiring an input ID and ignoring all else."""

    id_: Optional[str] = Field(default=None)
    alert_id: Optional[str] = Field(default=None)
    scenario_id: Optional[str] = Field(default=None)
    # Following field is just to do some operations on specific type of alerts.
    query_kind: Optional[BehaviourQueryKind] = Field(None)

    class Config:
        extra = pydantic.Extra.forbid

    @root_validator(pre=True)
    def check_id_or_key(cls, values):
        has_id = "id_" in values and values["id_"] is not None
        has_key = "key_" in values and values["key_"] is not None
        assert has_id or has_key
        return values


class RecordInIdOrKey(RecordIn):
    key_: Optional[str] = Field(default=None)
    data: Optional[Dict] = Field(default=None)

    @root_validator(pre=True)
    def check_id_or_key(cls, values):
        has_id = "id_" in values and values["id_"] is not None
        has_key = "key_" in values and values["key_"] is not None
        assert has_id or has_key
        return values

    @classmethod
    def from_record_in(cls, record: RecordIn):
        return cls(**record.dict(by_alias=True))

    @property
    def identifier(self):
        if self.id_:
            return self.id_
        return self.key_

    def uses_key(self):
        return self.id_ is None and self.key_ is not None


class CaseBulkRecordIn(pydantic.BaseModel):
    case_id: str
    filter: Optional[CaseBulkFilter]
    records: List[RecordInIdOrKey]
    use_filter: bool
    type: Optional[BulkImportType]

    @pydantic.root_validator(pre=True)
    def validate(cls, values):
        if values.get("use_filter", None) and not values.get("filter", None):
            raise BadInput(msg="Filter cannot be empty when use_filter is enabled.")

        return values


class CaseBulkOutput(pydantic.BaseModel):
    case_records: Dict = {}
    alert_records: Dict = {}
