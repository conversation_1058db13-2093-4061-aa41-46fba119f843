# type: ignore
import logging
from api_sdk.models.elasticsearch import RawResult
from api_sdk.repository.syncronous.request_bound import RequestBoundRepository
from case_bulk_workflow.schemas.cases import (
    CaseCall,
    CaseEmail,
    CaseMeeting,
    CaseMessage,
    CaseOrder,
    CaseRecord,
    CaseRecordSearch,
    CaseText,
)
from se_api_svc.schemas.cases.cases import Case, CaseByIdOrSlugSearch

log = logging.getLogger(__name__)


class NoRecordsFoundError(Exception):
    pass


class CaseRecordRepository(RequestBoundRepository):
    model = CaseRecord

    def get_many_for_case_and_original_id(self, case_id: str, original_id: str) -> RawResult:
        search = CaseRecordSearch(model=self.model, case_id=case_id, original_id=original_id)
        return self.get_many(self.model, search_model=search)

    def get_many_for_case(self, case_id: str, **kwargs) -> RawResult:
        return self.get_many(
            self.model, search_model=CaseRecordSearch(model=self.model, case_id=case_id, **kwargs)
        )

    def get_many_for_cases(self, case_id_list: list, **kwargs) -> RawResult:
        return self.get_many(
            self.model,
            search_model=CaseRecordSearch(model=self.model, case_id=case_id_list, **kwargs),
        )

    def get_one_by_id_or_slug(self, id_or_slug):
        return self.get_one(Case, search_model=CaseByIdOrSlugSearch(identifier=id_or_slug))


class CaseOrdersRepository(CaseRecordRepository):
    model = CaseOrder


class CaseCallsRepository(CaseRecordRepository):
    model = CaseCall


class CaseMeetingsRepository(CaseRecordRepository):
    model = CaseMeeting


class CaseMessagesRepository(CaseRecordRepository):
    model = CaseMessage


class CaseTextsRepository(CaseRecordRepository):
    model = CaseText


class CaseEmailsRepository(CaseRecordRepository):
    model = CaseEmail


class CaseCommsRepository(CaseRecordRepository):
    model = [CaseCall, CaseEmail, CaseMeeting, CaseMessage, CaseText]
