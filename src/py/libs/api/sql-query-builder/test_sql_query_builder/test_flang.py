import pytest
from sql_query_builder.flang.base import FlangFeature  # type: ignore
from sql_query_builder.params import SearchModelParams
from test_sql_query_builder.conftest import Base  # type: ignore
from typing import Optional


class Params(SearchModelParams):
    f: Optional[str]


def filter_to_str(filter):
    return str(filter.compile(compile_kwargs={"literal_binds": True}))


def format_filter_map(filter_map):
    formatted_map = {}
    for table_name, filters in filter_map.items():
        formatted_filters = [filter_to_str(f) for f in filters]
        formatted_map[table_name] = formatted_filters

    return formatted_map


@pytest.mark.parametrize(
    "f, expected",
    [
        ("pet in ['Fish']", {"test_model_2": ["test_model_2.pet IN ('Fish')"]}),
        (
            "pet in ['Fish'] and name endswith 'cK'",
            {
                "test_model_1": ["test_model_1.name LIKE '%cK'"],
                "test_model_2": ["test_model_2.pet IN ('Fish')"],
            },
        ),
        (
            "pet in ['Fish'] or name endswith 'cK'",
            {
                "or": ["test_model_2.pet IN ('Fish') OR test_model_1.name LIKE '%cK'"],
                "test_model_2": [],
                "test_model_1": [],
            },
        ),
        (
            "pet in ['Fish'] and name endswith 'cK' and (pet notin ['Fish'] or name endswith 'Kc')",
            {
                "test_model_1": ["test_model_1.name LIKE '%cK'"],
                "test_model_2": ["test_model_2.pet IN ('Fish')"],
                "or": ["(test_model_2.pet NOT IN ('Fish')) OR test_model_1.name LIKE '%Kc'"],
            },
        ),
        (
            "((pet in ['Fish'] and name endswith 'cK') or (pet notin ['Fish'] and name endswith 'Kc'))",  # noqa: E501
            {
                "or": [
                    "(test_model_2.pet IN ('Fish') AND test_model_1.name LIKE '%cK') OR ((test_model_2.pet NOT IN ('Fish')) AND test_model_1.name LIKE '%Kc')"  # noqa: E501
                ],
                "test_model_2": [],
                "test_model_1": [],
            },
        ),
    ],
)
def test_flang_filter_map(f, expected):
    flang_feature = FlangFeature.simple(Base, "f", _search_tables=["test_model_1", "test_model_2"])
    flang_feature.build_subquery(meta=Base, params=Params(**{"f": f}))

    assert format_filter_map(flang_feature.config.gen._f_filter_map) == expected


@pytest.mark.parametrize(
    "f, expected",
    [
        (
            "((pet in ['Fish'] and name endswith 'cK') or (pet notin ['Fish'] and name endswith 'Kc'))",  # noqa: E501
            {
                "or": [
                    '("M2"."pet" IN (\'Fish\') AND "M1"."name" LIKE \'%cK\') OR (("M2"."pet" NOT IN (\'Fish\')) AND "M1"."name" LIKE \'%Kc\')'  # noqa: E501
                ],
                "test_model_2": [],
                "test_model_1": [],
            },
        ),
    ],
)
def test_flang_filter_map_with_table_aliases(f, expected):
    flang_feature = FlangFeature.simple(
        Base,
        "f",
        _search_tables=["test_model_1", "test_model_2"],
        table_aliases={"test_model_1": "M1", "test_model_2": "M2"},
    )
    flang_feature.build_subquery(meta=Base, params=Params(**{"f": f}))

    assert format_filter_map(flang_feature.config.gen._f_filter_map) == expected
