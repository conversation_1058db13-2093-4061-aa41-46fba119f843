# type: ignore
import dataclasses
import flang  # pants: no-infer-dep
from sql_query_builder.base import SearchFeature, SearchFeatureConfig
from sql_query_builder.flang.flang_generator import BadFilterValue, Generator, SimpleQueryGenerator
from typing import Dict, List, Union


def build_query(
    meta,
    expr_str: str,
    flang_filter_obj,
    generator: Union[Generator, SimpleQueryGenerator] = None,
    _search_tables: List = [],
):
    # If base Generator add base meta and create new Generator
    gen = Generator(meta=meta, funcs={}) if generator == Generator else generator
    if generator == Generator:
        flang_filter_obj.config.gen = gen

    try:
        parse_tree = flang.parse(expr_str)
    except AttributeError:
        raise BadFilterValue("Invalid String, cannot parse")

    return gen.generate(parse_tree)


class FlangFeature(SearchFeature):
    @dataclasses.dataclass
    class Config(SearchFeatureConfig):
        # assign base generator by default
        gen: Generator = Generator
        param: str = "f"

    config: Config

    def check_in_params(self, params):
        if self.config.param in params or self.config.param in getattr(params, "__fields__", []):
            return True

        return False

    def value_in_params(self, params):
        if isinstance(params, dict):
            return params.get(self.config.param)

        return params.dict().get(self.config.param)

    def build_subquery(self, meta, params):
        # check it params hold any value for the field
        if not self.check_in_params(params):
            return

        value = self.value_in_params(params)

        if not value:
            return

        return build_query(
            meta=meta, expr_str=value, flang_filter_obj=self, generator=self.config.gen
        )

    @classmethod
    def simple(
        cls,
        meta,
        param: str = "f",
        _special_fields: Dict = {},
        _search_tables: List = [],
        **gen_options,
    ):
        """The Declaration of SimpleFlangFeature in model feature:
        FlangFeature.simple(Base, "f", _special_fields, _search_tables)

        meta, param -> Required
        _special_fields and _search_tables -> Optional
          - To use _special_fields and _search_tables define a file in repository
          - and pass it in simple function
        """
        return cls(
            param=param,
            gen=SimpleQueryGenerator(
                meta=meta,
                funcs={},
                _special_fields=_special_fields,
                _search_tables=_search_tables,
                **gen_options,
            ),
        )
