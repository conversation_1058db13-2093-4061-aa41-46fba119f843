# type: ignore

import addict
import logging
from se_es_utils.slim_record_handler import <PERSON><PERSON><PERSON>ord<PERSON><PERSON><PERSON>
from typing import Optional

logger = logging.getLogger(__name__)


class Repository:
    _default_batch_size = 500
    _default_scroll_time = "5m"
    _repo = None

    def __init__(self, repo: <PERSON><PERSON><PERSON>ord<PERSON><PERSON><PERSON>, index: str):
        self._repo = repo
        self._index = index
        self._client = repo.es_repo.client

    def scroll_query(self, query: dict, index: str = None):
        response = self._client.search(
            index=index or self._index,
            scroll=self._default_scroll_time,
            size=self._default_batch_size,
            body=query,
        )

        results = [hit.get("_source") for hit in response.body.get("hits", {}).get("hits", {})]

        total_hits = response.body["hits"]["total"]["value"]

        scrolled = len(response.body["hits"]["hits"])

        while scrolled < total_hits:
            response = self._client.scroll(
                body={"scroll_id": response.body["_scroll_id"]},
                scroll=self._default_scroll_time,
            )
            result = [hit.get("_source") for hit in response.body.get("hits", {}).get("hits", {})]

            results += result

            scrolled += len(response.body["hits"]["hits"])

        return results

    def search(self, query: dict, index: Optional[str] = None, **kwargs):
        if not query:
            raise NotImplementedError

        logger.info(f"Executing Query {query} @ {index or self._index}")

        response = self._client.search(index=index or self._index, body=query, **kwargs)

        response = addict.Dict(response.body)

        return response
