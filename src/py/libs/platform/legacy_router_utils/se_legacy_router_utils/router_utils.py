import backoff
import httpx
import logging
import random
from data_platform_config_api_client.stack import StackAPI
from se_schema.tools.schema_generator.static import Environment
from typing import Any

log = logging.getLogger(__name__)


def notify_slack(
    webhook_url: str,
    queue_url: str,
    error: Any,
    service_name: str,
    icon: str = ":alert:",
    raise_on_request_error: bool = False,
) -> None:
    """Sends the error message in Slack channel using webhook.

    :param webhook_url: Webhook URL to use to send the message
    :param queue_url: Queue URL for which the error occured
    :param error: Exception
    :param service_name: Service from which the message is being sent
    :param icon:
    :param raise_on_request_error: Raise exception if the notification send fails, default False
    """

    @backoff.on_exception(backoff.expo, httpx.RequestError, max_tries=3)
    def send_slack_message() -> None:
        msg = "```"
        msg += f"OBJECT : {queue_url}\n"
        msg += f"ERROR  : {error}\n"
        msg += "```"
        response = httpx.post(
            url=webhook_url,
            json=dict(text=msg, icon_emoji=icon, username=service_name),
            headers={"Content-Type": "application/json"},
            timeout=30,
        )
        response.raise_for_status()

    try:
        send_slack_message()
    except httpx.RequestError as e:
        log.exception("Failed to send slack notification. Error: %s", e)

        if raise_on_request_error:
            raise


def derive_queue_names_from_stack_config_api(
    stack_api_client: StackAPI,
    stack_name: str,
    environment: str,
    queue_suffix: str | None = "",
    stack_level_queue_name: str | None = None,
) -> list[str]:
    """Derives the queues name by calling the stack level config api.

    :param stack_api_client: StackAPI client
    :param stack_name: Stack name to derive the queues names for
    :param environment: Environment for which the queus to be described
    :param queue_suffix: The suffix to add in the last of queue name
    :param stack_level_queue_name: Stack level queue name to append in the list of queues
    """
    data = stack_api_client.get_by_name(stack_name=stack_name, tenant_paused=False)
    if data.content["paused"]:
        log.warning("Stack level pause is on! No queues to discover...")
        return []

    if not data.content["tenants"] and stack_level_queue_name is None:
        log.warning("No tenants found in stack level config api. Response: %s", data)
        return []

    match environment.upper():
        case Environment.PROD:
            queue_names_map = map(
                lambda x: f"{x['name']}_steeleye_co{queue_suffix}",
                data.content["tenants"],
            )
        case Environment.UAT:
            queue_names_map = map(
                lambda x: f"{x['name']}_uat_steeleye_co{queue_suffix}",
                data.content["tenants"],
            )
        case Environment.SIT:
            queue_names_map = map(
                lambda x: f"{x['name']}_sit_steeleye_co{queue_suffix}",
                data.content["tenants"],
            )
        case Environment.DEV:
            queue_names_map = map(
                lambda x: f"{x['name']}_dev_steeleye_co{queue_suffix}",
                data.content["tenants"],
            )
        case _:
            raise RuntimeError(f"Unexecpected environement: {environment}")

    queue_names = list(queue_names_map)
    if stack_level_queue_name:
        queue_names.append(stack_level_queue_name)

    random.shuffle(queue_names)
    return queue_names
