"""This module provide class for cached AriesApiClient class object to the
calling references."""

import os
from aries_se_api_client.client import AriesApiClient


class AriesConfigCachedApiClient:
    _config_api_client = None

    @classmethod
    def get_config_api_client(cls) -> AriesApiClient:
        """Returns the instance of the AriesApiClient.

        :return: The singleton instance of AriesApiClient.
        :rtype: AriesApiClient
        """
        if cls._config_api_client is None:
            cls._config_api_client = AriesApiClient(host=os.environ["DATA_PLATFORM_CONFIG_API_URL"])
        return cls._config_api_client
