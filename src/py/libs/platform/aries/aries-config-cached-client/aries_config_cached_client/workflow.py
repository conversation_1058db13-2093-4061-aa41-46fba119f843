"""This module provides class for cached WorkflowAPI platform config API client
methods."""

from addict import Dict
from aries_config_cached_client.api_client import AriesConfigCachedApiClient
from aries_config_cached_client.static import DEFAULT_MAX_SIZE, DEFAULT_TTL
from cachetools import TTLCache, cached
from data_platform_config_api_client.workflow import WorkflowAPI


class CachedWorkflowAPIClient:
    """CachedWorkflowAPIClient class contains class methods to cache the
    responses from WorkflowAPI platform config API client objects.

    The class methods have scope within the imported module and running
    worker and provides caching with size and ttl.
    """

    _config_api_client = AriesConfigCachedApiClient.get_config_api_client()
    _workflow_api = WorkflowAPI(client=_config_api_client)

    @classmethod
    @cached(cache=TTLCache(maxsize=DEFAULT_MAX_SIZE, ttl=DEFAULT_TTL))
    def get_all(cls, storage_prefix: str | None = None) -> Dict:
        """get_all method retrieves the cached workflow by key.

        :param storage_prefix: storage prefix
        :return: dictionary response
        :rtype: Dict
        """
        return cls._workflow_api.get_all(storage_prefix=storage_prefix).content
