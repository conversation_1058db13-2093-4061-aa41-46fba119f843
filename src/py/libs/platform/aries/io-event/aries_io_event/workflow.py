import datetime
import nanoid
from aries_io_event.base_model import IOEventFieldSetModel
from pydantic import Field


class WorkflowFieldSet(IOEventFieldSetModel):
    trace_id: str = Field(
        default_factory=nanoid.generate,
        description="A unique identifier for each workflow execution",
        copyToAll=False,
    )
    start_timestamp: datetime.datetime = Field(
        ...,
        description="Workflow execution start time, it contains the first timestamp when this"
        "event is created",
        copyToAll=False,
    )
    name: str = Field(
        ..., description="Workflow name as per Platform's config DB table Workflow", copyToAll=False
    )
    stack: str = Field(
        ...,
        description="Tenant-stack name as per Platform's config DB table Stack",
        copyToAll=False,
    )
    tenant: str = Field(
        ...,
        description="Tenant's name as per Platform's config DB table Tenant",
        copyToAll=False,
    )
