import datetime
from pydantic import BaseModel, Field
from typing import Set


class MSTeamsSubscriptionBase(BaseModel):
    last_polled: datetime.datetime | None


class MSTeamsSubscriptionUpdate(MSTeamsSubscriptionBase):
    subscription_name: str | None
    skip_poll: bool | None
    deleted: bool | None


class MSTeamsSubscriptionUpdated(MSTeamsSubscriptionBase):
    pass


class MSTeamsSubscriptionResourceBase(MSTeamsSubscriptionUpdate):
    resource: str


class MSTeamsConfigUpdate(BaseModel):
    microsoft_tenant_id: str
    license_model: str | None


class MSTeamsSubscriptionResourceUpdate(BaseModel):
    resources: list[MSTeamsSubscriptionResourceBase] = Field(..., min_items=1)


class MSTeamsSubscriptionResourceDelete(BaseModel):
    resources: Set[str] = Field(..., min_items=1)
