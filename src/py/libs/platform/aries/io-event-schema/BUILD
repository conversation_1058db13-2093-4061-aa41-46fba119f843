resources(name="spec", sources=["spec/oma/elasticsearch/*.json"])
python_sources(
    name="src",
    sources=["**/*.py"],
    dependencies=[
        "//:se_libs#schema-sdk",
        "//:3rdparty#pytz",
        "//:3rdparty#libjson2csv",
        ":spec",
    ],
    tags=["build_amd"],
)
se_image(
    image_name="aries-io-event-schema-publisher",
    pex_entry_point="publish_schema.py",
    tags=["build_amd"],
)
