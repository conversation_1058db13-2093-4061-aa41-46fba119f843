from aries_io_event.model import IOEvent
from aries_io_event_schema.base import AriesIOEventDeltaModel
from schema_sdk.steeleye_model.base.schema import SchemaExtra


class IOEventSchema(IOEvent, AriesIOEventDeltaModel):
    """ES Schema model for IOEvent."""

    class Config:
        # env is used as placeholder to create different index names for different environments
        schema_extra = {
            SchemaExtra.ALIAS: "{env}_io_event_schema",
            SchemaExtra.SETTINGS: {
                "refresh_interval": "30s",
                "number_of_shards": 1,
                "number_of_replicas": 0,
                "mapping.total_fields.limit": 10000,  # filebeat uses 10k, so its not atypical
                "index.lifecycle.name": "{env}_io_event_schema-ilmpolicy",
                # Apply the ILM policy
                "index.lifecycle.rollover_alias": "{env}_io_event_schema",
                # Required for rollover to work
            },
            SchemaExtra.HASH_PROPERTIES: [],
            SchemaExtra.ID_PROPERTIES: ["workflow.trace_id", "task.id", "task.timestamp"],
            SchemaExtra.IS_MANAGED: False,
        }
