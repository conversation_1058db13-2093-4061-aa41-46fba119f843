import click
import json
import logging
import pathlib
from aries_io_event_schema.io_event.model import IOEventSchema
from elasticsearch7 import Elasticsearch
from schema_sdk.steeleye_model.base.schema import <PERSON>hemaEx<PERSON>
from static import (
    ILM_DEFAULT_DELETE_AFTER_DAYS,
    ILM_DELETE_PHASE_DAYS_BUFFER,
    ILM_POLICY_JSON_PATH,
    INDEX_TEMPLATE_JSON_PATH,
)

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


@click.command()
@click.option("--env", required=True, type=click.STRING, default=None)
@click.option("--elastic-url", required=True, type=click.STRING)
@click.option("--add-ilm-delete-phase", required=False, type=click.BOOL, default=False)
@click.option("--ilm-delete-after-days", required=False, type=click.INT)
@click.option("--ilm-delete-wait-for-snapshot-policy-name", required=False, type=click.STRING)
@click.option("--verify-certs", required=False, type=click.BOOL, default=True)
def main(
    env: str,
    ilm_delete_after_days: int | None,
    elastic_url: str,
    add_ilm_delete_phase: bool,
    ilm_delete_wait_for_snapshot_policy_name: str | None,
    verify_certs: bool,
):
    es = Elasticsearch(elastic_url, verify_certs=verify_certs)
    model_settings = IOEventSchema.Config.schema_extra[SchemaExtra.SETTINGS]
    ilmpolicy_name = model_settings["index.lifecycle.name"].format(env=env)  # type: ignore
    ilmpolicy_alias = model_settings["index.lifecycle.rollover_alias"].format(  # type: ignore
        env=env
    )
    # publish ilm policy
    ilm_policy_path = pathlib.Path(__file__).parent.joinpath(ILM_POLICY_JSON_PATH)
    with open(ilm_policy_path, "r") as f:
        ilm_policy = json.load(f)

    # apply the add_ilm_delete_phase if applicable
    if add_ilm_delete_phase:
        if ilm_delete_after_days is None:
            ilm_delete_after_days = ILM_DEFAULT_DELETE_AFTER_DAYS
        # add 30 days to the delete_after_days, to account of hot phase
        ilm_delete_after_days += ILM_DELETE_PHASE_DAYS_BUFFER
        ilm_policy["policy"]["phases"]["delete"] = {
            "min_age": f"{ilm_delete_after_days}d",
            "actions": {"delete": {}},
        }
        # apply the delete_wait_for_snapshot_policy_name if applicable.
        if ilm_delete_wait_for_snapshot_policy_name:
            ilm_policy["policy"]["phases"]["delete"]["actions"]["wait_for_snapshot"] = {
                "policy": ilm_delete_wait_for_snapshot_policy_name
            }

    logger.info(f"Publishing ILM policy for env {env} ... with policy {ilm_policy}")
    resp = es.ilm.put_lifecycle(policy=ilmpolicy_name, body=ilm_policy)
    logger.info(f"Response: {resp}")

    # publish index template
    logger.info(f"Publishing schema for env {env} ...")
    schema_path = pathlib.Path(__file__).parent.joinpath(INDEX_TEMPLATE_JSON_PATH)
    # Load the JSON schema from the file
    with open(schema_path, "r") as file:
        schema_str = file.read()
        schema_str = schema_str.replace("{env}", env)
        schema = json.loads(schema_str)

    # Use the put_template method of the Elasticsearch client to publish the schema as an
    # index template
    resp = es.indices.put_index_template(name=f"{env}_io_event_schema-indextemplate", body=schema)
    logger.info(f"Response: {resp}")

    # update mappings in existing indices
    index_patterns: list[str] = schema["index_patterns"]
    for index_pattern in index_patterns:
        # Use the get method of the Elasticsearch client to retrieve the index template
        existing_indices = es.indices.get(index=index_pattern)
        if existing_indices:
            # update mapping of existing indices
            for an_idx in existing_indices:
                logger.info(f"Updating index {an_idx} ...")
                # update mapping of existing indices
                resp = es.indices.put_mapping(index=an_idx, body=schema["template"]["mappings"])
                logger.info(f"Response: {resp}")
        else:
            # Use the put_mapping method of the Elasticsearch client to publish the first index
            # ilm requires the first index to be created manually, and assigned write alias
            first_index_body = {"aliases": {ilmpolicy_alias: {"is_write_index": True}}}
            # creates index with date math, so that rolled over index contains date in the name
            # https://www.elastic.co/guide/en/elasticsearch/reference/current/indices-rollover-index.html#roll-over-index-alias-with-write-index
            index_name = "<" + index_pattern.replace("*", "{now/d}-000001") + ">"
            logger.info(f"Creating index {index_name} ... with body {first_index_body}")
            resp = es.indices.create(index=index_name, body=first_index_body)
            logger.info(f"Response: {resp}")


if __name__ == "__main__":
    main()
