import backoff
import boto3
import botocore.exceptions
import json
import logging
from datetime import datetime
from typing import Any

log = logging.getLogger(__name__)


def get_lambda_client():  # pragma: no cover
    return boto3.client("lambda")


@backoff.on_exception(
    backoff.expo,
    botocore.exceptions.ClientError,
    max_tries=3,
)
def invoke_lambda(
    lambda_client,
    stack: str,
    s3_bucket: str,
    s3_key: str,
    sqs_message_body: dict[str, Any],
    queue_url: str,
    invoked_by: str,
) -> None:
    """Invokes the lambda function based on sqs message and raises if response
    code is not in range of successful response.

    :param lambda_client: Boto3 lambda client
    :param stack: Stack for which the lambda should be invoked
    :param s3_bucket: Source s3 bucket
    :param s3_key: Source s3 key
    :param sqs_message_body: SQS message for which lambda is going to be invoked
    :param queue_url: SQS message source queue URL
    :param invoked_by: Service name which is invoking the lambda
    """
    lambda_name = derive_lambda_name(stack=stack, source_key=s3_key)
    log.info(f"Invoking {lambda_name}")
    payload = json.dumps(
        dict(
            source=invoked_by,
            bucket_name=s3_bucket,
            key_name=s3_key,
            receipt_handle=sqs_message_body["ReceiptHandle"],
            queued_at=str(
                datetime.utcfromtimestamp(
                    float(sqs_message_body["Attributes"]["SentTimestamp"]) / 1000.0
                )
            ),
            retry_msg={"MSG_BODY": json.dumps(sqs_message_body), "SQS_QUEUE_URL": queue_url},
            source_queue_url=queue_url,
        )
    )

    response = lambda_client.invoke(
        FunctionName=lambda_name,
        InvocationType="Event",
        Payload=payload,
    )

    status_code = response.get("StatusCode")
    if status_code < 200 or status_code >= 300:
        function_error = response.get("FunctionError")
        raise RuntimeError(function_error)
    log.info(f"Succesfully invoked {lambda_name} for sqs message: {sqs_message_body}")


def get_feed_type(s3_key: str):
    """Dervies the feed name from the s3 key and returns the value.

    This method is copied over from the legacy feeds-notifications-
    router

    :param s3_key: S3 key to be used to get feed type
    """
    if s3_key.split("/")[1].lower() in {"mgmt", "comms"}:
        return s3_key.split("/")[2]
    return s3_key.split("/")[1]


def derive_lambda_name(stack: str, source_key: str | None, model: str | None = None):
    """Derives the lambda name based on stack, source key and model.

    This method is copied over from commons-sink legacy repo.

    :param stack: Stack name where code is running
    :param source_key: S3 source key
    :param model: Model type for the message
    """
    if source_key is not None:
        feed_type = get_feed_type(s3_key=source_key)
        return f"{stack}-feeds-{feed_type}-handler"

    if (model is not None) and ("fix" in model.lower()):
        return f"{stack}-platform-fix2mifid-handler"

    raise ValueError(f"No handler. Model: {model or 'Null'}, Key: {stack or 'Null'}")
