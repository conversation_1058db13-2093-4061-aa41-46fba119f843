import logging
from aries_se_api_client.base import EndPoint
from aries_se_api_client.client import AriesAbstractApiClient
from aries_se_api_client.response_parsers import AddictResponseParser
from efdh_api_client.api.base import EFDHAPI
from typing import Any, ClassVar, Dict, List, Optional

log = logging.getLogger(__name__)


class NewsAPI(EFDHAPI):
    PREFIX: ClassVar[str] = "/news"
    SOURCES: ClassVar[str] = "/sources"
    GROUPINGS: ClassVar[str] = "/groupings"
    STORIES: ClassVar[str] = "/stories"
    GET_STORIES: ClassVar[str] = "/stories/{story_id}"
    SUBJECTS: ClassVar[str] = "/stories/subjects/{story_id}"
    BACKLOAD: ClassVar[str] = "/backload"

    def __init__(self, client: AriesAbstractApiClient):
        self._client = client
        self._sources_endpoint: EndPoint[dict] = EndPoint(
            path=NewsAPI.SOURCES,
            http_verb="GET",
            response_parser=AddictResponseParser(),
        )
        self._groupings_endpoint: EndPoint[dict] = EndPoint(
            path=NewsAPI.GROUPINGS,
            http_verb="GET",
            response_parser=AddictResponseParser(),
        )
        self._get_stories_endpoint: EndPoint[dict] = EndPoint(
            path=NewsAPI.GET_STORIES,
            http_verb="GET",
            response_parser=AddictResponseParser(),
        )
        self._subjects_endpoint: EndPoint[dict] = EndPoint(
            path=NewsAPI.SUBJECTS,
            http_verb="GET",
            response_parser=AddictResponseParser(),
        )
        self._bulk_fetch_stories_endpoint: EndPoint[dict] = EndPoint(
            path=NewsAPI.STORIES,
            http_verb="POST",
            response_parser=AddictResponseParser(),
        )
        self._backload_endpoint: EndPoint[dict] = EndPoint(
            path=NewsAPI.BACKLOAD,
            http_verb="POST",
            response_parser=AddictResponseParser(),
        )

    def get_prefix(self) -> str:
        return super().get_prefix() + NewsAPI.PREFIX

    def get_sources(self):
        return self._client.call_api(
            api=self,
            endpoint=self._sources_endpoint,
        )

    def get_groupings(self):
        return self._client.call_api(
            api=self,
            endpoint=self._groupings_endpoint,
        )

    def get_stories(
        self, story_id: str, version: Optional[int] = None, latest: Optional[bool] = None
    ):
        return self._client.call_api(
            api=self,
            endpoint=self._get_stories_endpoint,
            path_params={"story_id": story_id},
            query_param=dict(latest=latest, version=version),
        )

    def get_subjects(
        self, story_id: str, version: Optional[int] = None, latest: Optional[bool] = None
    ):
        return self._client.call_api(
            api=self,
            endpoint=self._subjects_endpoint,
            path_params={"story_id": story_id},
            query_param=dict(latest=latest, version=version),
        )

    def bulk_fetch_stories(self, json_body: Dict[str, Any]):
        return self._client.call_api(
            api=self,
            endpoint=self._bulk_fetch_stories_endpoint,
            json_body=json_body,
        )

    def trigger_backload(self, json_body: List[str]):
        return self._client.call_api(
            api=self,
            endpoint=self._backload_endpoint,
            json_body=json_body,
        )
