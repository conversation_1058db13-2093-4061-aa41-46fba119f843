import logging
from aries_se_api_client.base import EndPoint
from aries_se_api_client.client import AriesAbstractApiClient
from aries_se_api_client.response_parsers import AddictResponseParser
from efdh_api_client.api.base import EFDHAPI
from typing import ClassVar, Dict, List

log = logging.getLogger(__name__)


class PotamAPI(EFDHAPI):
    PREFIX: ClassVar[str] = "/potam"
    ISINS: ClassVar[str] = "/isins"
    CASES: ClassVar[str] = "/cases"

    def __init__(self, client: AriesAbstractApiClient):
        self._client = client
        self._isins_endpoint: EndPoint[Dict] = EndPoint(
            path=PotamAPI.ISINS,
            http_verb="GET",
            response_parser=AddictResponseParser(),
        )
        self._cases_endpoint: EndPoint[Dict] = EndPoint(
            path=PotamAPI.CASES,
            http_verb="POST",
            response_parser=AddictResponseParser(),
        )

    def get_prefix(self) -> str:
        return super().get_prefix() + PotamAPI.PREFIX

    def get_isins_from_cases(self):
        return self._client.call_api(
            api=self,
            endpoint=self._isins_endpoint,
        )

    def get_cases_from_isins(self, json_body: List[str]):
        return self._client.call_api(
            api=self,
            endpoint=self._cases_endpoint,
            json_body=json_body,
        )
