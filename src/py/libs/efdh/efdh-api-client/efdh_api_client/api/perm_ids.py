import logging
from aries_se_api_client.base import EndPoint
from aries_se_api_client.client import AriesAbstractApiClient
from aries_se_api_client.response_parsers import AddictResponseParser
from efdh_api_client.api.base import <PERSON>FD<PERSON><PERSON>
from typing import Any, ClassVar, Dict

log = logging.getLogger(__name__)


class PermIDsAPI(EFDHAPI):
    PREFIX: ClassVar[str] = "/perm_ids"
    GET_PERM_ID: ClassVar[str] = "/{id_type}/{instrument_id}"
    BULK_FETCH_PERM_IDS: ClassVar[str] = ""

    def __init__(self, client: AriesAbstractApiClient):
        self._client = client
        self._get_perm_id_endpoint: EndPoint[dict] = EndPoint(
            path=PermIDsAPI.GET_PERM_ID,
            http_verb="GET",
            response_parser=AddictResponseParser(),
        )
        self._bulk_fetch_perm_ids_endpoint: EndPoint[dict] = EndPoint(
            path=PermIDsAPI.BULK_FETCH_PERM_IDS,
            http_verb="POST",
            response_parser=AddictResponseParser(),
        )

    def get_prefix(self) -> str:
        return super().get_prefix() + PermIDsAPI.PREFIX

    def get_perm_id(self, id_type: str, instrument_id: str):
        return self._client.call_api(
            api=self,
            endpoint=self._get_perm_id_endpoint,
            path_params={"id_type": id_type, "instrument_id": instrument_id},
        )

    def bulk_fetch_perm_ids(self, json_body: Dict[str, Any]):
        return self._client.call_api(
            api=self,
            endpoint=self._bulk_fetch_perm_ids_endpoint,
            json_body=json_body,
        )
