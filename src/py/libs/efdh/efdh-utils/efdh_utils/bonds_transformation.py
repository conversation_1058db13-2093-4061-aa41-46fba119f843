import logging
import pandas as pd
from efdh_utils.schema.parquet import EoDStatsColumns
from se_elasticsearch.repository.elasticsearch6 import ElasticsearchRepository
from typing import Literal


def get_bonds_ric(ric: str, es_client: ElasticsearchRepository) -> pd.DataFrame:
    """Method to return all Bonds RICs.

    RIC priority
    Phase 1: Preferred -> Primary -> Ric -> Composite Ric
    :param ric: RIC to search *.ricLookup*
    :param es_client: ElasticsearchClient, ElasticSearch client
    :return: pd.DataFrame
    """

    ric_query = {
        "query": {
            "bool": {
                "must_not": [{"exists": {"field": "&expiry"}}],
                "filter": [
                    {"term": {"preferredRic": ric}},
                    {"term": {"cfi.category": "Debt"}},
                    {"term": {"cfi.group": "Bonds"}},
                ],
            }
        },
        "_source": {
            "includes": [
                "preferredRic",
                "preferredRicCurrency",
                "instrumentUniqueIdentifier",
            ]
        },
    }

    response = es_client.search(
        query=ric_query,
        index=".ricLookup",
    )

    data = [source.get("_source", {}) for source in response.get("hits", {}).get("hits", {})]

    if not data:
        return pd.DataFrame()

    result = pd.json_normalize(data)

    return result


def bonds_transformation(
    frame: pd.DataFrame,
    base_ccy: str,
    cols: list,
    es_client: ElasticsearchRepository,
    logger: logging.Logger,
    market_data_type: Literal["EOD", "OBD", "TICK"],
) -> pd.DataFrame:
    """Function to transform price of bonds to percentage. It can transform the
    prices from EOD data, where it only transforms the values for the same
    currency as the base_ccy. It also is capable of transforming OBD data
    however, it does not need to match the currency.

    :param frame: pd.DataFrame, datagrame with the data to transform
    :param base_ccy: str, currency
    :param cols: list, list of columns to transform
    :param es_client: ElasticsearchClient, ElasticSearch client
    :param logger:
    :return: pd.DataFrame, copy of the initial dataframe but with the values transformed
    """
    if market_data_type not in ["TICK", "OBD", "EOD"]:
        raise ValueError("Market data type not supported")

    ric = frame[EoDStatsColumns.RIC].dropna().unique().tolist()[0]
    bonds_df = get_bonds_ric(ric=ric, es_client=es_client)

    if bonds_df.empty:
        return frame

    if (
        (bonds_df["preferredRicCurrency"] == base_ccy)
        & bonds_df["instrumentUniqueIdentifier"].str.startswith(("KR", "BRS"))
    ).all():
        logger.info(
            f"Transforming bonds (korean/brazilian) for RIC '{ric}' and currency '{base_ccy}'"
        )
        mask = frame[cols].ge(200).any(axis=1)
        if market_data_type == "EOD":
            mask = mask & (frame[EoDStatsColumns.CURRENCY].isin([base_ccy]))

        # we believe these bonds will be prices in their market
        # value, and we assume the face value is 10k.
        # Therefore, we convert from market value to percentage.
        frame.loc[mask, cols] = frame.loc[mask, cols] / 100

    return frame
