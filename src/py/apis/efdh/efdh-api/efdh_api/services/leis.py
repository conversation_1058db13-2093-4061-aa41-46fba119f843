import elasticsearch_dsl
from efdh_api.utils.es_dsl.base import NotExpiredFilter, QueryModelBase, QueryString
from efdh_api.utils.es_dsl.features import SearchAfterScrolling
from efdh_api.utils.es_dsl.flang import FlangFilter
from efdh_api.utils.es_dsl.params import QueryModelParams
from efdh_api.utils.repo import EFDHRepository
from efdh_api.utils.results import EFDHSearchResult, Sort
from se_elastic_schema.models.efdh.lei_record import LeiRecord
from typing import List, Optional

LEI_SCROLL_THRESHOLD = 10


class LEIRecordCols:
    ENTITY_LEGAL_NAME = "entity.legalName"
    LEI = "lei"


class LeiQueryModel(QueryModelBase):
    class Params(QueryModelParams):
        f: Optional[str] = None
        search: Optional[str] = None
        modified_since: Optional[int] = None
        search_after: Optional[str] = None
        skip: int = 0
        take: int = 1000
        sorts: Optional[List[Sort]] = None
        leis: List[str]
        source: List[str] = [LEIRecordCols.LEI, LEIRecordCols.ENTITY_LEGAL_NAME]

    params: Params

    features = [
        NotExpiredFilter,
        FlangFilter.simple(
            param="f",
        ),
        QueryString(param="model_qs", fields=["*.text"]),
    ]

    def build(self, *args, **kwargs) -> elasticsearch_dsl.Search:
        if self.params.leis:
            search = super(LeiQueryModel, self).build(*args, **kwargs)
            search = search.filter("terms", lei=self.params.leis)
        else:
            search = super(LeiQueryModel, self).build(*args, **kwargs)

        search = search.source(
            self.params.source,
        )
        search = SearchAfterScrolling(
            config=SearchAfterScrolling.Config(scroll_threshold=LEI_SCROLL_THRESHOLD)
        ).build(model=self, search=search)

        return search


class LeiRepository(EFDHRepository):
    def get_leis(self, leis, **params) -> EFDHSearchResult:
        query_model = LeiQueryModel(leis=leis, sorts=[Sort(field="lei")], **params)
        # disable pagination as its done by SearchAfterScrolling feature
        query_dsl = query_model.build(paginate=True)

        raw_results = self.search(
            query_body=query_dsl.to_dict(count=False), se_models=[LeiRecord], parse_to_model=False
        )
        return EFDHSearchResult.from_raw_result(raw_results)
