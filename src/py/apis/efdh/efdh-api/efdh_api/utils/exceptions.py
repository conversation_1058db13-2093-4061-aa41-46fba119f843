import enum
import pydantic
from typing import Any, Dict, List, Optional, Sequence, Type, Union


class BulkProcessingError(Exception):
    def __init__(self, errors: List[dict]):
        self._errors = errors

    def errors(self):
        return self._errors

    def __str__(self):
        return str(self._errors)


class ValidationError(Exception):
    pass


class InvalidDocumentIdError(ValidationError):
    pass


class RecordHandlerError(Exception):
    def __init__(self, message: str):
        super().__init__(message)


class ModelIntegrityError(Exception):
    def __init__(
        self,
        model: Union[str, Type] = None,
        id: Any = None,
        message: str = None,
        **extra: Any,
    ) -> None:
        super().__init__()
        self.model = None
        if model:
            self.model = model.__name__ if isinstance(model, type) else str(model)
        self.id = id
        self.extra = extra
        self.message = message

    def __str__(self) -> str:
        if self.message:
            return self.message
        return "Unspecified"


class NotFound(ModelIntegrityError):
    """Usually raised when requesting a sub-resource of an inexistent resource,
    resulting in a bad request."""

    def __str__(self) -> str:
        return self.message or (f"{self.model} {self.id} does not exist")


class BadRequest(Exception):
    """Raised when the referenced Model is not found."""

    def __init__(self, message: str = "") -> None:
        self.message = message

    def __str__(self) -> str:
        if self.message:
            return self.message
        return "Bad request"


class BackendError(Exception):
    """A generic server-side error when clients don't need details of the
    failure."""

    def __str__(self) -> str:
        return "Server Error"


class AlreadyExists(ModelIntegrityError):
    """Raised when the entity being created already exists."""

    def __str__(self) -> str:
        return self.message or (f"{self.model} {self.id or self.extra} already exists")


class EditConflict(ModelIntegrityError):
    """Raised when:

    1) an edit refers to a record using a stale or incorrect timestamp.
    2) attempting to edit something that is not editable.
    """

    def __str__(self) -> str:
        return (
            self.message
            or "Incorrect timestamp, the record might have been updated by someone else"
        )


class BackendException(Exception):
    """Raised when the back-end did not like our request."""

    def __init__(self, message: str, ctx: Dict = None):
        super().__init__()
        self.message = str(message) if message else ""
        self.ctx = ctx

    def __str__(self) -> str:
        return self.message

    def __repr__(self) -> str:
        return f"{self.__class__.__name__}(message={self.message!r}, ctx={self.ctx!r})"


class BadInput(Exception):
    """Use this only when you are throwing an exception that is safe to make it
    up to the user in full detail.

    This is needed only in special cases when the request is
    effectively validated after FastAPI has already deserialized it,
    for example, there seems to be no way to use FastAPI's built-in
    error handling mechanism to provide detailed information of
    the location of the error in SearchParams validation (and
    FastAPI doesn't support using pydantic.BaseModel for query string based
    parameters).

    The structure mimics that of individual pydantic validation errors.
    """

    def __init__(self, msg: str, loc: List[str] = None, ctx: Dict = None) -> None:
        super().__init__(msg)
        self.msg = msg
        self.loc = loc
        self.ctx = ctx

    @classmethod
    def from_pydantic_validation_error(
        cls, exc: pydantic.ValidationError, loc_prefix: List[str] = None
    ) -> "BadInput":
        error = exc.errors()[0]
        return cls(
            msg=error.get("msg", None),
            loc=(loc_prefix or []) + list(error.get("loc") or []),
            ctx=error.get("ctx"),
        )

    def to_dict(self) -> Dict:
        return {
            "msg": self.msg,
            "loc": self.loc,
            "ctx": self.ctx,
        }


class PermissionsException(Exception):
    def __init__(self, permissions: Sequence, missing_permissions=None):
        super().__init__()
        self.permissions = permissions or ()
        self.missing_permissions = missing_permissions

    def __str__(self) -> str:
        return f"Missing permissions: {', '.join(str(p) for p in self.permissions)})"


class WorkflowPermissionsException(Exception):
    def __init__(self, policy: Any = None, comment: str = None) -> None:
        super().__init__()
        self.policy = policy
        self.comment = comment

    def __str__(self) -> str:
        comment_str = f" ({self.comment})" if self.comment else ""
        if self.policy and self.policy.isSubjectAdmin:
            return (
                "Workflow setting on "
                f"({self.policy.settingType}, {self.policy.subSetting}) "
                f"forbids this action for subject admin{comment_str}."
            )
        else:
            if self.policy:
                return (
                    "Workflow setting on "
                    f"({self.policy.settingType}, {self.policy.subSetting}) "
                    f"forbids this action{comment_str}."
                )
            else:
                return f"Workflow setting forbids this action{comment_str}"


class MoreThanOneRecordError(RuntimeError):
    pass


class TooManyActiveTenantConfigurations(MoreThanOneRecordError):
    def __init__(self, realm_name: str = None) -> None:
        self.realm_name = realm_name

    def __str__(self) -> str:
        if self.realm_name:
            return f"More than one active tenant configurations found for {self.realm_name}"
        else:
            return "More than one active tenant configurations in current realm"


class MfaException(Exception):
    def __init__(self, message: str = "", ctx: Optional[Dict] = None):
        super().__init__()
        self.message = str(message)

    def __str__(self) -> str:
        return self.message

    def __repr__(self) -> str:
        return f"{self.__class__.__name__}(message={self.message!r}"


class APIResponseStatus(enum.StrEnum):
    SUCCESS = "success"
    UNKNOWN_ERROR = "unknown_error"
    INVALID_DATES = "invalid_dates"


class MarketDataError(Exception):
    def __init__(self, status: APIResponseStatus, exception: Optional[Exception] = None):
        self.status = status
        self.exception = exception


class MasterDataNewsServiceError(Exception):
    pass


class RolePermissionNotFound(Exception):
    def __init__(self, info: Dict, message: Optional[str] = None):
        super().__init__(message)
        self.info = info


class NeedApproval(Exception):
    def __init__(self, info: Dict, message: Optional[str] = None):
        super().__init__(message)
        self.info = info


class InvalidModulePermission(Exception):
    def __init__(self, info: Dict, message: Optional[str] = None):
        super().__init__(message)
        self.info = info


class PendingRequestNotFound(Exception):
    def __init__(self, info: Dict, message: Optional[str] = None):
        super().__init__(message)
        self.info = info


class PendingRequestNotPending(Exception):
    def __init__(self, info: Dict, message: Optional[str] = None):
        super().__init__(message)
        self.info = info


class ApprovalPermissionNotFound(Exception):
    def __init__(self, info: Dict, message: Optional[str] = None):
        super().__init__(message)
        self.info = info


class KafkaResponseErrorException(Exception):
    # custom retry-able exception on kafka errors
    pass
