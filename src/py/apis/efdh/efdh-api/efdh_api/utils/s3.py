# ruff: noqa: E501

import boto3
import botocore.exceptions
import collections
import logging
from botocore.client import Config
from efdh_api.utils.exceptions import BackendError, BadInput, NotFound
from efdh_api.utils.not_set import DELETED
from fastapi import File
from pydantic import AnyUrl
from urllib.parse import urlparse

log = logging.getLogger(__name__)


class AbstractS3Client:
    def upload_fileobj(self, **kwargs):
        raise NotImplementedError()

    def put_object(self, **kwargs):
        raise NotImplementedError()

    def head_object(self, **kwargs):
        raise NotImplementedError()

    def delete_object(self, **kwargs):
        raise NotImplementedError()

    def get_object(self, **kwargs):
        raise NotImplementedError()

    def get_object_metadata(self, **kwargs):
        raise NotImplementedError()

    def generate_presigned_url(self, **kwargs):
        raise NotImplementedError()


class S3Client(AbstractS3Client):
    def __new__(cls, **kwargs):
        return boto3.client("s3", config=Config(signature_version="s3v4"), **kwargs)


class FakeS3Client(AbstractS3Client):
    def __init__(self):
        self.buckets = collections.defaultdict(dict)

    def upload_fileobj(self, **kwargs):
        self.buckets[kwargs["Bucket"]][kwargs["Key"]] = kwargs["Fileobj"].read()

    def put_object(self, **kwargs):
        self.buckets[kwargs["Bucket"]][kwargs["Key"]] = kwargs

    def head_object(self, **kwargs):
        if not self.buckets[kwargs["Bucket"]] or not self.buckets[kwargs["Bucket"]][kwargs["Key"]]:
            raise botocore.exceptions.ClientError(
                {"Error": {"Code": "404", "Message": "Not Found"}}, {...}
            )
        return self.buckets[kwargs["Bucket"]][kwargs["Key"]]

    def delete_object(self, **kwargs):
        self.buckets[kwargs["Bucket"]][kwargs["Key"]] = DELETED

    def get_object(self, **kwargs):
        return self.buckets[kwargs["Bucket"]][kwargs["Key"]]

    def get_object_metadata(self, **kwargs):
        return None

    def generate_presigned_url(self, **kwargs):
        return kwargs


class S3Uri(AnyUrl):
    allowed_schemes = {"s3"}


DEFAULT_PRESIGNED_URL_EXPIRY_SECONDS = 900  # 15 mins


class S3DownloadService:
    def __init__(self):
        self._boto3_client = boto3.client("s3", config=Config(signature_version="s3v4"))

    def get_presigned_url(
        self, s3_uri: str, expires_in: int = DEFAULT_PRESIGNED_URL_EXPIRY_SECONDS
    ):
        parsed_url = urlparse(s3_uri)
        bucket = parsed_url.netloc
        key = parsed_url.path[1:]
        file_name = key.split("/")[-1]
        try:
            # check if file exists
            self._boto3_client.head_object(Bucket=bucket, Key=key)
            resp = self._boto3_client.generate_presigned_url(
                ClientMethod="get_object",
                Params={
                    "Bucket": bucket,
                    "Key": key,
                    # required to ensure that browser downloads the file instead of displaying it
                    "ResponseContentDisposition": "attachment",
                },
                ExpiresIn=expires_in,
            )
            return resp
        except botocore.exceptions.ClientError as ce:
            if ce.response["Error"]["Code"] == "404":
                log.warning(f"No file found for s3 uri {s3_uri}")
                raise NotFound("File", file_name)
            else:
                log.exception(ce)
                raise BackendError()


class S3Service:
    def __init__(self, s3_client: AbstractS3Client):
        self.s3_client = s3_client

    def get_presigned_url(
        self,
        s3_uri: str,
        expires_in: int = DEFAULT_PRESIGNED_URL_EXPIRY_SECONDS,
        content_type: str = None,
    ):
        parsed_url = urlparse(s3_uri)
        bucket = parsed_url.netloc
        key = parsed_url.path[1:]
        file_name = key.split("/")[-1]

        if self.check_file_exist(bucket, key):
            params = {
                "Bucket": bucket,
                "Key": key,
                # required to ensure that browser downloads the file instead of displaying it
                "ResponseContentDisposition": "attachment",
            }

            # if content_type is provided, add it to the params, default is binary/octet-stream set by s3
            if content_type:
                params["ResponseContentType"] = content_type

            resp = self.s3_client.generate_presigned_url(
                ClientMethod="get_object",
                Params=params,
                ExpiresIn=expires_in,
            )
            return resp

        raise NotFound("File", file_name)

    def check_file_exist(self, bucket: str, key: str):
        try:
            self.s3_client.head_object(Bucket=bucket, Key=key)
        except botocore.exceptions.ClientError as ce:
            if ce.response["Error"]["Code"] == "404":
                return False
            else:
                log.exception(ce)
                raise BackendError()
        else:
            return True

    def upload_file(self, file: File, bucket: str, key: str, overwrite: bool = False):
        try:
            if not overwrite and self.check_file_exist(bucket, key):
                raise BadInput(msg=f"File exists in {key}")
            file.file.seek(0)
            self.s3_client.upload_fileobj(
                Fileobj=file.file,
                Bucket=bucket,
                Key=key,
            )
            return True
        except Exception as e:
            log.exception(msg=str(e))
            raise BackendError(str(e))
        finally:
            file.file.close()
