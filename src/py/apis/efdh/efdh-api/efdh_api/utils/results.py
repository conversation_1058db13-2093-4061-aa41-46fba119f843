# ruff: noqa: E501

import dataclasses
import flang
import logging
import pydantic
import se_schema_meta
from efdh_api.utils.exceptions import BadInput
from efdh_api.utils.not_set import NotSet
from efdh_api.utils.sorting import Comparable
from efdh_api.utils.utils import StringEnum, encode_search_after, nested_dict_get
from enum import auto
from fastapi import Query
from pydantic import BaseModel, Extra, Field, ValidationError, validator
from se_elastic_schema.elastic_schema.core.steeleye_schema_model import SteelEyeSchemaBaseModelES8
from typing import Any, Dict, Generator, List, Optional, Type, Union

log = logging.getLogger(__name__)


class RawHitSource(BaseModel):
    # "&"-prefixed fields are suffixed with "_" in Python so "&model" becomes "model_"

    class Config:
        extra = Extra.allow

    id_: str = Field(None, alias="&id")
    fqn_: str = Field(None, alias="&fqn")
    parent_: str = Field(None, alias="&parent")
    key_: str = Field(None, alias="&key")
    model_: str = Field(None, alias="&model")
    timestamp_: str = Field(None, alias="&timestamp")
    user_: str = Field(None, alias="&user")

    hit: Dict = Field(None)
    hitModel: str = Field(None)


class RawHit(BaseModel):
    index: str = Field(None, alias="_index")
    type: str = Field(None, alias="_type")
    id: str = Field(None, alias="_id")
    score: Any = Field(None, alias="_score")
    sort: Any = Field(None)
    routing: str = Field(None, alias="_routing")
    parent: Optional[str] = Field(None, alias="_parent")
    source: RawHitSource = Field(None, alias="_source")

    class Config:
        extra = Extra.allow


class RawResultTotal(BaseModel):
    value: int
    relation: str


class RawResultHits(BaseModel):
    max_score: Optional[float] = None
    hits: List[
        Any
    ] = []  # Not a List[Dict] because sometimes we could replace the dicts with model instances
    total: RawResultTotal


class RawResult(BaseModel):
    _shards: Dict
    aggregations: Any
    hits: RawResultHits
    hits: RawResultHits
    last_sort: Any = None
    timed_out: bool
    took: int

    def as_list(self) -> List[Dict]:
        return self.hits.hits

    def iter_bucket_agg(
        self,
        dotted_key: str,
        key_str: str = "key",
        key_as: str = "key",
        doc_count_as: str = "count",
        count_path: str = "doc_count",
    ) -> Generator[Dict, None, None]:
        for item in nested_dict_get(self.aggregations, dotted_key + ".buckets") or ():
            yield {key_as: item[key_str], doc_count_as: nested_dict_get(item, count_path)}

    def iter_raw_bucket_agg(self, dotted_key: str) -> Generator[Dict, None, None]:
        for item in nested_dict_get(self.aggregations, dotted_key + ".buckets") or ():
            yield item

    def set_last_sort(self) -> None:
        try:
            self.last_sort = self.hits.hits[-1]["sort"]
        except (KeyError, IndexError):
            pass

    def parse_hits_to_response(
        self,
        response_model_map: dict[str, type[SteelEyeSchemaBaseModelES8]],
        parse_to_model: bool = True,
    ) -> None:
        # Handle special case when there is only one response model, the hits could be deserialized directly using that model
        single_response_model = None
        if len(response_model_map) == 1:
            single_response_model = list(response_model_map.values())[0]

        for i in range(len(self.hits.hits)):
            hit_source = self.hits.hits[i]["_source"]
            if parse_to_model:
                model_name = hit_source.get(se_schema_meta.MODEL)
                if model_name is not None and model_name in response_model_map:
                    # we need the have the actual model name in the model map to deserialize the hit
                    self.hits.hits[i] = response_model_map[model_name].construct(**hit_source)
                elif single_response_model:
                    self.hits.hits[i] = single_response_model.construct(**hit_source)
                else:
                    # keep the hit source unchanged
                    self.hits.hits[i] = hit_source
            else:
                # If parse_to_model is False, we just keep the hit source as is
                self.hits.hits[i] = hit_source


class EFDHSearchResultHeader(BaseModel):
    returnedHits: int = Field(
        default=0,
        description="The count of records returned from the total hits based on the take param",
    )
    totalHits: Optional[int] = Field(
        default=0,
        description="The count of records that match the search criteria",
    )
    skippedHits: Optional[int] = Field(
        default=None,
        description=(
            "The count of records matching the search criteria that "
            "were skipped before the returned records. It is not available on all endpoints."
        ),
    )
    previousSearchBefore: Optional[str] = Field(default=None)
    nextSearchAfter: Optional[str] = Field(default=None)
    offset: Optional[int] = Field(default=None)


class EFDHSearchResult(BaseModel):
    class Config:
        # See LooseSearchResult for extras allowed
        extra = Extra.forbid

    header: EFDHSearchResultHeader = Field(default_factory=EFDHSearchResultHeader)
    results: List = Field(
        default_factory=list,
        description="The list of records matching the search criteria and take amount",
    )

    @classmethod
    def from_raw_result(
        cls, result: RawResult, skipped_hits: int = None, take: int = None, offset: int = None
    ) -> "EFDHSearchResult":
        """If "take" is passed, we can tell whether nextSearchAfter needs to be
        set."""
        returned_hits = len(result.hits.hits)
        return cls(
            header=EFDHSearchResultHeader(
                returnedHits=returned_hits,
                totalHits=result.hits.total.value,
                skippedHits=skipped_hits,
                nextSearchAfter=(
                    encode_search_after(result.last_sort) if result.last_sort else None
                ),
                offset=offset,
            ),
            results=result.hits.hits,
        )

    @classmethod
    def from_resp(cls, resp: Dict, record_model: Type = None):
        """Create SearchResult instance from a response body (dictionary) and
        optionally deserialize the individual records."""
        result = cls(**resp)
        if record_model:
            result.results = [record_model(**item) for item in result.results]
        return result


class LooseSearchResult(EFDHSearchResultHeader):
    """Unlike SearchResult, this allows any fields."""

    class Config:
        extra = Extra.allow


def check_nested_path(field_, sort: "Sort"):
    from efdh_api.utils.es_dsl.flang import SimpleQueryGenerator

    special_fields = SimpleQueryGenerator().special_fields

    nested_path = next(
        (sf.nested_path for sf in special_fields.values() if sf.field == field_), None
    )
    if nested_path:
        setattr(sort, "nested_path", nested_path)


def parse_sort_str(sort: str, allowed_fields=None) -> Optional[List["Sort"]]:
    if not sort:
        return

    sorts: List[Sort] = []

    items = sort.strip().split(",")
    for i, item in enumerate(items):
        try:
            reverse = False
            if item.startswith("-"):
                reverse = True
                item = item[1:]
            parts = item.split(":")
            if len(parts) == 1:
                sort = Sort(field=parts[0], order=Sort.Order.desc if reverse else Sort.Order.asc)
            elif len(parts) == 2:
                sort = Sort(field=parts[0], order=parts[1])
            elif len(parts) == 3:
                sort = Sort(field=parts[0], order=parts[1], missing=parts[2])
            else:
                raise ValueError("Too many sort parts")

            if len(parts):
                check_nested_path(parts[0], sort)
        except ValidationError as ve:
            raise BadInput.from_pydantic_validation_error(
                ve, loc_prefix=["query", "sort", i]
            ) from ve
        except Exception as e:
            raise BadInput(msg="Invalid value", loc=["query", "sort", i]) from e
        if allowed_fields and sort.field not in allowed_fields:
            raise BadInput(msg="Unsupported sort field", loc=["query", "sort", i])
        sorts.append(sort)

    return sorts


def apply_sorts(items, sorts: Optional[List["Sort"]], is_dict=True):
    """Apply parsed sorts on a list of dictionaries or objects.

    LIMITATIONS:
        asc/desc only works for the first field

    A proper solution would be to sort by each field individually first and then
    merge the results.
    """

    if not sorts:
        return items

    if is_dict:

        def key(item):
            return tuple(
                Comparable(item.get(s.field), nones_last=sorts[0].order == Sort.Order.asc)
                for s in sorts
            )

    else:

        def key(item):
            return tuple(Comparable(getattr(item, s.field)) for s in sorts)

    return sorted(items, key=key, reverse=sorts[0].order == Sort.Order.desc)


# Has to be a dataclass, FastAPI doesn't seem to unpack BaseModel for query string arguments.
@dataclasses.dataclass(frozen=True)
class SearchParams:
    """DEPRECATED.

    Use PageParams and specify search params separately. Search is not
    universal, some endpoints have no search, some have multiple,
    putting it all under one parameter just creates confusion.
    """

    search: Optional[str] = None
    skip: int = 0
    take: int = 50
    sort: Optional[str] = None
    f: Optional[str] = None

    def __post_init__(self):
        # Attempt to convert to Search model in order to catch validation errors during
        # the request processing. Unfortunately cannot cache the result on a frozen dataclass
        # instance.
        self.to_search()

    def to_search(self) -> "Search":
        # See also to pagination
        sorts = parse_sort_str(self.sort)
        try:
            return Search(
                query=self.search,
                skip=self.skip,
                take=self.take,
                sorts=sorts,
                f=self.f,
            )
        except ValidationError as e:
            raise BadInput.from_pydantic_validation_error(exc=e, loc_prefix=["query"]) from e

    def to_dict(self) -> Dict:
        return dataclasses.asdict(self)


@dataclasses.dataclass(frozen=True)
class PageParams:
    """Represents pagination params.

    Does not and should not include any search parameters.
    """

    skip: int = 0
    take: int = 50
    sort: Optional[str] = None

    def __post_init__(self):
        # Attempt to convert to Pagination model in order to catch validation errors during
        # the request processing. Unfortunately cannot cache the result on a frozen dataclass
        # instance.
        self.to_pagination()

    def to_pagination(self) -> "Pagination":
        sorts = parse_sort_str(self.sort)
        try:
            return Pagination(
                skip=self.skip,
                take=self.take,
                sorts=sorts,
            )
        except ValidationError as e:
            raise BadInput.from_pydantic_validation_error(exc=e, loc_prefix=["query"]) from e

    def to_dict(self) -> Dict:
        return dataclasses.asdict(self)


@dataclasses.dataclass(frozen=True)
class CustomPageParams:
    """Represents pagination params.

    Does not and should not include any search parameters.
    """

    skip: int = 0
    take: int = 50
    sort: Optional[str] = None

    def __post_init__(self):
        # Attempt to convert to Pagination model in order to catch validation errors during
        # the request processing. Unfortunately cannot cache the result on a frozen dataclass
        # instance.
        self.to_pagination()

    def to_pagination(self) -> "Pagination":
        sorts = parse_sort_str(self.sort)
        try:
            return CustomPagination(
                skip=self.skip,
                take=self.take,
                sorts=sorts,
            )
        except ValidationError as e:
            raise BadInput.from_pydantic_validation_error(exc=e, loc_prefix=["query"]) from e

    def to_dict(self) -> Dict:
        return dataclasses.asdict(self)


class Sort(BaseModel):
    class Order(StringEnum):
        asc = auto()
        desc = auto()

    class Missing(StringEnum):
        last = "_last"
        first = "_first"

    class Config:
        # to allow using user type NotSet
        arbitrary_types_allowed = True

    field: str
    order: Order = Order.asc
    missing: Union[NotSet, Missing] = Missing.last
    "Set to NOT_SET to skip setting this field"
    unmapped_type: Union[NotSet, str] = "long"
    "Set to NOT_SET to skip setting this field"
    nested_path: Optional[str]

    def to_dict(self) -> Dict:
        field_config = {"order": self.order}
        if self.missing:
            field_config["missing"] = self.missing
        if self.unmapped_type:
            field_config["unmapped_type"] = self.unmapped_type
        if self.nested_path:
            field_config["nested"] = {"path": self.nested_path}

        return {self.field: field_config}


class Search(BaseModel):
    """DEPRECATED.

    Search params passed around in the repo layer.

    Search() is SAFE to use in function declarations as a default value
    because Search is immutable.
    """

    class Config:
        # This is to allow using Search() as a default value
        allow_mutation = False
        extra = pydantic.Extra.forbid

    query: Optional[str] = None
    skip: int = 0
    take: int = 50
    sorts: Optional[List[Sort]] = None
    f: Any = None

    @pydantic.validator("f")
    def parse_filter_expr(cls, v):
        if v:
            if len(v) > 1000:
                raise ValueError("Filter expression too long")
            return flang.parse(v)
        return None

    def to_dict(self) -> Dict:
        return self.dict()


class Pagination(BaseModel):
    """Plain pagination."""

    class Config:
        # This is to allow using Search() as a default value
        allow_mutation = False
        extra = pydantic.Extra.forbid

    skip: int = 0
    take: int = 50
    sorts: Optional[List[Sort]] = None

    @validator("take")
    def _validate_take(cls, value):
        if value < 0:
            raise ValueError("should be between 0 and 250")
        elif value > 250:
            log.info(f"Received invalid take= value {value}")
            value = 250
        return value

    @validator("skip")
    def _validate_skip(cls, value):
        if not (0 <= value < 9999):
            raise ValueError("should be between 0 and 9999")
        return value

    def to_dict(self) -> Dict:
        return self.dict()


class CustomPagination(Pagination):
    """Plain pagination."""

    class Config:
        # This is to allow using Search() as a default value
        allow_mutation = False
        extra = pydantic.Extra.forbid

    skip: int = 0
    take: int = 50
    sorts: Optional[List[Sort]] = None

    @validator("take")
    def _validate_take(cls, value):
        if value < 0:
            raise ValueError("should be between 0 and 10000")
        elif value > 10000:
            log.info(f"Received invalid take= value {value}")
            value = 10000
        return value

    @validator("skip")
    def _validate_skip(cls, value):
        if not (0 <= value < 9999):
            raise ValueError("should be between 0 and 9999")
        return value

    def to_dict(self) -> Dict:
        return self.dict()


@pydantic.dataclasses.dataclass(frozen=True)
class ScrollParams:
    """Scrollable results based on elastic search search_after feature."""

    search_after: Optional[str] = Query(
        None,
        description="Key to scroll for results after the current results. This is taken from nextSearchAfter of previous API call response",
    )
    skip: int = Query(
        0,
        ge=0,
        description="No. of results to skip, this should not be set when using search_after",
    )
    take: int = Query(
        50, ge=0, le=1000, description="No. of results to fetch. Enables scrolling when >500"
    )
    sort: Optional[str] = Query(
        None, description="Required when take>500 or when search_after is set"
    )

    def to_search_after_params(self) -> Dict:
        return dict(
            search_after=self.search_after,
            sorts=parse_sort_str(self.sort),
            skip=self.skip,
            take=self.take,
        )
