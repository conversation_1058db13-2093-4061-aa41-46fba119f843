import logging
from fastapi import Request

log = logging.getLogger(__name__)


class LogStackTenantMiddleware:
    def __init__(
        self,
    ):
        return

    async def __call__(self, request: Request, call_next):
        stack = request.headers.get("STACK")
        tenant = request.headers.get("TENANT")
        log.info(f"request received for Stack: {stack} | Tenant: {tenant} | Request: {request.url}")
        response = await call_next(request)
        return response
