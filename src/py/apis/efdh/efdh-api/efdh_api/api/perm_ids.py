from dependency_injector.wiring import Provide, inject
from efdh_api.schemas.perm_ids import PermIDsInput, PermIDsQuery
from efdh_api.services.perm_ids import PermIDsRepo
from efdh_api.utils.containers import Container
from fastapi import APIRouter, Depends

router = APIRouter()


# owner: needed for news
@router.get("/{id_type}/{instrument_id}")
@inject
def get_perm_id(
    instrument_id: str, id_type: str, repo: PermIDsRepo = Depends(Provide[Container.perm_ids_repo])
):
    model = PermIDsInput(id=instrument_id, id_type=id_type)
    return repo.get_perm_id(model=model)


@router.post("")
@inject
def bulk_fetch_perm_ids(
    query: PermIDsQuery, repo: PermIDsRepo = Depends(Provide[Container.perm_ids_repo])
):
    return repo.bulk_fetch_perm_ids(query=query)
