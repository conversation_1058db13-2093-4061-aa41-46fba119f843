from dependency_injector.wiring import Provide, inject
from efdh_api.api.request_params import DatedListParams
from efdh_api.services.ecb_euro_ref_rates import ECBEuroRefRateRepository
from efdh_api.utils.containers import Container
from efdh_api.utils.exceptions import BadInput
from efdh_api.utils.results import EFDHSearchResult
from fastapi import APIRouter, Depends, Query
from typing import Optional

router = APIRouter()


DOWNLOAD_SIZE_LIMIT = 5_000


# owner: DE confirmed
@router.get("", name="ecb_ref_euro_rates:get-all")
@inject
async def get_ecb_euro_ref_rates(
    date_params: DatedListParams = Depends(),
    take: Optional[int] = Query(DOWNLOAD_SIZE_LIMIT, ge=0, le=DOWNLOAD_SIZE_LIMIT),
    repo: ECBEuroRefRateRepository = Depends(Provide[Container.ecb_euro_ref_rate_repository]),
) -> EFDHSearchResult:
    """Gets the non paginated list of EURO Ref Rates for the given date
    range."""
    if not (date_params.start or date_params.end):
        raise BadInput("start and end params is required.")

    return EFDHSearchResult.from_raw_result(
        repo.get_ecb_euro_ref_rates(take=take, **date_params.as_search_kwargs())
    )
