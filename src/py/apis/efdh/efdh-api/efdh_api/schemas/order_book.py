import polars as pl
import pyarrow as pa
from efdh_utils.schema.refinitiv import RefinitivOrderBookDepthColumns
from enum import Enum
from pydantic import BaseModel
from typing import Any, Generator, List, Optional, Tuple

MAX_OBD_LEVELS = 10
MAX_ROWS = 10000


class OrderBookLevel(BaseModel):
    level: Optional[int] = None
    volume: Optional[int] = None
    price: Optional[float] = None


class OrderBook(BaseModel):
    """Order book representation.

    Order book data is immutable and should be created from a pandas
    dataframe extracted from the RIC parquet files (see ticket EP-332).
    """

    class Config:
        allow_mutation = False

    dateTime: str
    price: float
    buyLevels: List[OrderBookLevel]
    sellLevels: List[OrderBookLevel]


class OrderBookWrapper(BaseModel):
    ric: str
    maxDepth: int
    data: List[OrderBook]


class OrderBookVolume(BaseModel):
    """Cumulative buy/sell volume representation for OBD data."""

    class Config:
        allow_mutation = False

    dateTime: str
    buyVolume: int
    sellVolume: int


class OrderBookDataFrame:
    TIME_COL = "Date-Time"

    class LevelColumns(str, Enum):
        BID_PRICE = "L{}-BidPrice"
        BID_SIZE = "L{}-BidSize"
        # BUY_NO = "L{}-BuyNo"  # Not in use for OBD, but leaving for reference
        ASK_PRICE = "L{}-AskPrice"
        ASK_SIZE = "L{}-AskSize"

        # SELL_NO = "L{}-SellNo" # Not in use for OBD, but leaving for reference

        def lvl(self, level: int):
            return self.value.format(level)

        def extract(self, level: int, data: dict) -> Any:
            val = data[self.lvl(level)]
            return None if val is None else val

    def __init__(self, pa_df: pa.Table, interval_ns: int):
        self._interval_ns = interval_ns
        self._pa_df = pa_df

    def as_orderbooks(self) -> Generator[OrderBook, None, None]:
        """Generator sampling data into OrderBook using Polars."""
        # Ensure the data is sorted by the Date-Time column.
        df_sorted = self._pa_df.sort(RefinitivOrderBookDepthColumns.DATE_TIME)
        # Use the first timestamp as the reference.
        start_time = df_sorted[0, RefinitivOrderBookDepthColumns.DATE_TIME]
        # Compute an interval bucket for each row.
        df_bucketed = df_sorted.with_columns(
            (
                (pl.col(RefinitivOrderBookDepthColumns.DATE_TIME) - start_time) // self._interval_ns
            ).alias("interval_bucket")
        )
        # Group by the interval bucket and take the first row from each group.
        downsampled_df = df_bucketed.group_by("interval_bucket").first().drop("interval_bucket")
        # Yield an OrderBook for each downsampled row.
        for row in downsampled_df.iter_rows(named=True):
            yield self._row_to_orderbook(row)

    def _row_to_orderbook(self, row: dict) -> OrderBook:
        buy_levels = []
        sell_levels = []
        cols = self.LevelColumns

        for level in range(1, MAX_OBD_LEVELS + 1):
            if cols.ASK_PRICE.lvl(level) not in row:
                # if a level is not present, no data is available for any levels beyond this
                break
            buy_levels.append(
                OrderBookLevel.parse_obj(
                    {
                        "level": level,
                        "price": cols.BID_PRICE.extract(level, row),
                        "volume": cols.BID_SIZE.extract(level, row),
                    }
                )
            )

            sell_levels.append(
                OrderBookLevel.parse_obj(
                    {
                        "level": level,
                        "price": cols.ASK_PRICE.extract(level, row),
                        "volume": cols.ASK_SIZE.extract(level, row),
                    }
                )
            )

        l1_buy_price = buy_levels[0].price
        l1_sell_price = sell_levels[0].price
        if l1_buy_price is None and l1_sell_price is not None:
            mid_price = l1_sell_price
        elif l1_sell_price is None and l1_buy_price is not None:
            mid_price = l1_buy_price
        else:
            mid_price = (l1_buy_price + l1_sell_price) / 2

        return OrderBook(
            dateTime=str(row.get(RefinitivOrderBookDepthColumns.DATE_TIME)),
            price=mid_price,
            buyLevels=buy_levels,
            sellLevels=sell_levels,
        )

    def as_cumulative_volumes(
        self, depth: int, num_records: int = 0
    ) -> Generator[Tuple[OrderBookVolume, int], None, None]:
        buy_vol_columns = [self.LevelColumns.BID_SIZE.lvl(level) for level in range(1, depth + 1)]
        sell_vol_columns = [self.LevelColumns.ASK_SIZE.lvl(level) for level in range(1, depth + 1)]

        # Fill null values with 0 and compute row-wise volume sums.
        df = self._pa_df.fill_null(0).with_columns(
            [
                pl.concat_list([pl.col(c) for c in buy_vol_columns]).arr.sum().alias("buyVolume"),
                pl.concat_list([pl.col(c) for c in sell_vol_columns]).arr.sum().alias("sellVolume"),
            ]
        )
        # Add a row count for computing cumulative means.
        df = df.with_row_count("row_nr")
        # Compute cumulative sums.
        df = df.with_columns(
            [
                pl.col("buyVolume").cumsum().alias("cum_buy"),
                pl.col("sellVolume").cumsum().alias("cum_sell"),
            ]
        )
        # Compute cumulative means (expanding mean) and cast to int.
        df = df.with_columns(
            [
                (pl.col("cum_buy") / (pl.col("row_nr") + 1)).cast(pl.Int64).alias("buyVolume_mean"),
                (pl.col("cum_sell") / (pl.col("row_nr") + 1))
                .cast(pl.Int64)
                .alias("sellVolume_mean"),
            ]
        )
        # Use the Date-Time column (self.TIME_COL) for bucketing.
        start_time = df.select(pl.col(self.TIME_COL).min()).item()
        df = df.with_columns(
            [((pl.col(self.TIME_COL) - start_time) // self._interval_ns).alias("interval_bucket")]
        )
        # Group by the interval bucket and take the first row from each group.
        downsampled_df = df.group_by("interval_bucket").first().drop("interval_bucket")
        # Yield each cumulative volume along with the record count.
        for row in downsampled_df.iter_rows(named=True):
            yield (
                OrderBookVolume(
                    dateTime=str(row[self.TIME_COL]),
                    buyVolume=row["buyVolume_mean"],
                    sellVolume=row["sellVolume_mean"],
                ),
                row["row_nr"] + 1,
            )

    @classmethod
    def level_columns_for_depth(cls, depth: int = MAX_OBD_LEVELS):
        columns = []
        for level in range(1, depth + 1):
            columns.extend([c.value.format(level) for c in cls.LevelColumns])

        return columns
