"""Tests for Zoom Phone Service."""

import pytest
from aries_config_api_httpschema.zoom import ZoomMeetingsConfigUpdate, ZoomMeetingsConfigUpdated
from data_platform_config.exceptions import DataNotFound
from data_platform_config.models.system.tenant import Tenant
from data_platform_config.models.zoom.zoom_meetings_config import ZoomMeetingsConfig
from data_platform_config.services.zoom_meetings_config import ZoomMeetingsService

tenant = Tenant(id=1, stack_id=1, name="test_tenant_1")
tenant_2 = Tenant(id=2, stack_id=1, name="test_tenant_2")

config = ZoomMeetingsConfig(
    id=1,
    tenant_id=1,  # type: ignore
    last_polled=None,
)
config_2 = ZoomMeetingsConfig(
    id=2,
    tenant_id=2,  # type: ignore
    last_polled=None,
    account_id="test_account",
)


@pytest.fixture
def zoom_meetings_service(mock_session):
    return ZoomMeetingsService(session_factory=mock_session)


def test_get_config(mock_session, zoom_meetings_service):
    """Test for get_config."""
    mock_session().__enter__().query().join().filter().filter().one_or_none.return_value = config
    return_val = zoom_meetings_service.get_config(
        stack_name="mock-stack",
        tenant_name="test_tenant",
    )
    assert return_val == config


def test_get_config_tenant_not_found(mock_session, zoom_meetings_service):
    """Test for delete config."""
    mock_session().__enter__().query().join().filter().filter().one_or_none.return_value = None
    with pytest.raises(DataNotFound):
        _ = zoom_meetings_service.get_config(
            stack_name="mock-stack",
            tenant_name="test_tenant_2",
        )


def test_upsert_config_update_existing_config(mock_session, zoom_meetings_service):
    """Test for update_config."""
    mock_session().__enter__().query().join().filter().filter().one_or_none.return_value = config
    return_val = zoom_meetings_service.upsert_config(
        stack_name="mock-stack",
        tenant_name="test_tenant",
        zoom_meetings_config_update=ZoomMeetingsConfigUpdate(last_polled=None),
    )
    assert isinstance(return_val, ZoomMeetingsConfigUpdated)
    assert return_val == ZoomMeetingsConfigUpdated(last_polled=None)


def test_upsert_config_new_config_tenant_not_found(mock_session, zoom_meetings_service):
    """Test for update_config."""
    mock_session().__enter__().query().join().filter().filter().one_or_none.return_value = None
    with pytest.raises(DataNotFound):
        _ = zoom_meetings_service.upsert_config(
            stack_name="mock-stack",
            tenant_name="test_tenant_1",
            zoom_meetings_config_update=ZoomMeetingsConfigUpdate(last_polled=None),
        )


def test_upsert_config_new_config_tenant_found(mock_session, zoom_meetings_service):
    """Test for update_config."""
    mock_session().__enter__().query().join().filter().filter().one_or_none.side_effect = [
        None,
        tenant,
    ]
    return_val = zoom_meetings_service.upsert_config(
        stack_name="mock-stack",
        tenant_name="test_tenant_1",
        zoom_meetings_config_update=ZoomMeetingsConfigUpdate(last_polled=None),
    )
    assert isinstance(return_val, ZoomMeetingsConfigUpdated)
    assert return_val == ZoomMeetingsConfigUpdated(last_polled=None)


def test_search_config_found(mock_session, zoom_meetings_service):
    """Test for search_config."""
    mock_session().__enter__().query().options().filter().one_or_none.return_value = config_2
    return_val = zoom_meetings_service.search_config(account_id="test_account")
    assert return_val == config_2


def test_search_config_not_found(mock_session, zoom_meetings_service):
    """Test for search_config."""
    mock_session().__enter__().query().options().filter().one_or_none.return_value = None
    with pytest.raises(DataNotFound):
        _ = zoom_meetings_service.search_config(account_id="test_account123")
