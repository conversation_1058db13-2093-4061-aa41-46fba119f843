from aries_config_api_httpschema.ms_teams import MSTeamsSubscriptionResourceUpdate
from data_platform_config.models.ms_teams.ms_teams_subscription import MSTeamsSubscription
from data_platform_config.models.system.tenant import Tenant
from mock.mock import DEFAULT, MagicMock, patch


@patch.multiple("data_platform_config.services.ms_teams", insert=DEFAULT, datetime=DEFAULT)
def test_upsert(ms_teams_service, mock_session, **kwargs):
    # input

    subscription_update = MSTeamsSubscriptionResourceUpdate(
        resources=[
            {
                "subscription_name": "string",
                "resource": "string00",
                "skip_poll": True,
                "last_polled": None,
            }  # type: ignore
        ]
    )

    mock_tenant = MagicMock()
    mocked_msteams_config = (
        mock_session().__enter__().query().join().filter().filter().filter().one.return_value
    )
    ms_teams_service.bulk_upsert_subscriptions_by_microsoft_tenant_id(
        stack_name="mock-stack",
        tenant_name=mock_tenant,
        microsoft_tenant_id=MagicMock(),
        subscription_update=subscription_update,
    )

    values = {
        "skip_poll": True,
        "last_polled": None,
        "ms_teams_config_id": mocked_msteams_config.id,
        "time_updated": kwargs["datetime"].datetime.utcnow(),
        "subscription_name": "string",
        "resource": "string00",
    }

    mock_session().__enter__().query().join().filter().filter.assert_any_call(
        Tenant.name == mock_tenant
    )
    kwargs["insert"].return_value.values.assert_any_call(**values)
    del values["ms_teams_config_id"]
    del values["resource"]

    kwargs["insert"].return_value.values().on_conflict_do_update.assert_any_call(
        index_elements=(MSTeamsSubscription.ms_teams_config_id, MSTeamsSubscription.resource),
        set_=values,
    )
    assert kwargs["insert"].return_value.values().on_conflict_do_update.call_count == 1
    mock_session().__enter__().execute.assert_called()
    assert mock_session().__enter__().execute.call_count == 1
