import datetime
from aries_config_api_httpschema.ms_teams import MSTeamsSubscriptionUpdate
from mock.mock import Magic<PERSON><PERSON>


def test_upsert_with_nothing(ms_teams_service, mock_session):
    # input

    existing_subscription = MagicMock(
        last_polled=datetime.datetime(year=2010, month=1, day=1, hour=1, minute=1, second=1),
        deleted=False,
    )

    # existing flow not found
    mock_session().__enter__().query().join().filter().filter().filter().filter().filter().filter().one.return_value = (  # noqa: E501
        existing_subscription
    )

    # mock the return value of creating model object
    subscription_update = MSTeamsSubscriptionUpdate()

    return_val = ms_teams_service.update_subscription_by_subscription_id(
        stack_name=MagicMock(),
        tenant_name=MagicMock(),
        subscription_id=MagicMock(),
        microsoft_tenant_id=MagicMock(),
        subscription_update=subscription_update,
    )

    assert return_val.last_polled == existing_subscription.last_polled


def test_upsert_with_none(ms_teams_service, mock_session):
    # input

    existing_subscription = MagicMock(
        last_polled=datetime.datetime(year=2010, month=1, day=1, hour=1, minute=1, second=1),
    )

    # existing flow not found
    mock_session().__enter__().query().join().filter().filter().filter().filter().filter().filter().one.return_value = (  # noqa: E501
        existing_subscription
    )

    # mock the return value of creating model object
    subscription_update = MSTeamsSubscriptionUpdate(last_polled=None)

    return_val = ms_teams_service.update_subscription_by_subscription_id(
        stack_name=MagicMock(),
        tenant_name=MagicMock(),
        subscription_id=MagicMock(),
        microsoft_tenant_id=MagicMock(),
        subscription_update=subscription_update,
    )

    assert return_val.last_polled == existing_subscription.last_polled
