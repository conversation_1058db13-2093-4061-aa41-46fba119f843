from data_platform_config.models.ms_teams.ms_teams_subscription import MSTeamsSubscription
from mock.mock import Magic<PERSON><PERSON>


def test_delete_subscription_by_id(ms_teams_service, mock_session):
    susbcription = (
        mock_session()
        .__enter__()
        .query()
        .join()
        .filter()
        .filter()
        .filter()
        .filter()
        .filter()
        .one.return_value
    )
    tenant_name = MagicMock()
    microsoft_tenant_id = MagicMock()
    mocked_subscription = MagicMock()
    ms_teams_service.delete_subscription_by_id(
        stack_name="mock-stack",
        tenant_name=tenant_name,
        subscription_id=mocked_subscription,
        microsoft_tenant_id=microsoft_tenant_id,
    )

    mock_session().__enter__().query().filter(
        MSTeamsSubscription.id == susbcription.id
    ).update.assert_called_once_with({"deleted": True})
