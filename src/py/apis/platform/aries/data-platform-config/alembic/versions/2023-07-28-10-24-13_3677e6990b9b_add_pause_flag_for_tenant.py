"""add pause flag for tenant

Revision ID: 3677e6990b9b
Revises: 5d462d692472
Create Date: 2023-07-28 10:24:13.396356

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '3677e6990b9b'
down_revision = '5d462d692472'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('tenant', sa.Column('paused', sa.<PERSON>(), server_default=sa.text('false'), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('tenant', 'paused')
    # ### end Alembic commands ###
