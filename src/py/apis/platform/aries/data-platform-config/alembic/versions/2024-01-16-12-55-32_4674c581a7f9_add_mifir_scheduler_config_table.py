"""add_mifir_scheduler_config_table

Revision ID: 4674c581a7f9
Revises: 8b0af28794a0
Create Date: 2024-01-16 12:55:32.465116

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '4674c581a7f9'
down_revision = '8b0af28794a0'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('MifirSchedulerConfig',
    sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('tenantId', sa.Integer(), nullable=False),
    sa.Column('scheduleType', sa.String(length=50), nullable=False),
    sa.Column('workflowName', sa.String(length=50), nullable=True),
    sa.Column('enabled', sa.<PERSON>(), server_default=sa.text('false'), nullable=False),
    sa.Column('recipients', postgresql.ARRAY(sa.String(length=50)), nullable=True),
    sa.Column('scheduleISOTimeZone', sa.String(length=50), nullable=True),
    sa.Column('createdDateTime', sa.DateTime(), server_default=sa.text("TIMEZONE('utc', CURRENT_TIMESTAMP)"), nullable=False),
    sa.Column('updatedDateTime', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['tenantId'], ['tenant.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('tenantId', 'scheduleType')
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('MifirSchedulerConfig')
    # ### end Alembic commands ###
