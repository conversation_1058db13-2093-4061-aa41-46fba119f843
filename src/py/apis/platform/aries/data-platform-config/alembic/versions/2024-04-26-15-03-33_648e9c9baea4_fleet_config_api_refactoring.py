"""fleet config-api refactoring

Revision ID: 648e9c9baea4
Revises: 30fcb5227536
Create Date: 2024-04-26 15:03:33.634265

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '648e9c9baea4'
down_revision = '30fcb5227536'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('ilm_config')
    op.alter_column('tenant', 'lake_prefix',
               existing_type=sa.VARCHAR(),
               nullable=False)
    op.alter_column('tenant', 'cloud',
               existing_type=sa.VARCHAR(),
               nullable=False,
               existing_server_default=sa.text("'aws'::character varying"))
    op.drop_constraint('tenant_name_key', 'tenant', type_='unique')
    op.create_unique_constraint('stack_id_name_key', 'tenant', ['stack_id', 'name'])
    op.drop_column('workflow', 's3_feed_prefix')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('workflow', sa.Column('s3_feed_prefix', sa.VARCHAR(), autoincrement=False, nullable=True))
    op.drop_constraint('stack_id_name_key', 'tenant', type_='unique')
    op.create_unique_constraint('tenant_name_key', 'tenant', ['name'])
    op.alter_column('tenant', 'cloud',
               existing_type=sa.VARCHAR(),
               nullable=True,
               existing_server_default=sa.text("'aws'::character varying"))
    op.alter_column('tenant', 'lake_prefix',
               existing_type=sa.VARCHAR(),
               nullable=True)
    op.create_table('ilm_config',
    sa.Column('id', sa.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('stack_id', sa.INTEGER(), autoincrement=False, nullable=False),
    sa.Column('alias_pattern', sa.VARCHAR(), autoincrement=False, nullable=False),
    sa.Column('shard_threshold_gib', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('time_threshold_month', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('time_created', postgresql.TIMESTAMP(), server_default=sa.text("timezone('utc'::text, CURRENT_TIMESTAMP)"), autoincrement=False, nullable=False),
    sa.Column('time_updated', postgresql.TIMESTAMP(), autoincrement=False, nullable=True),
    sa.ForeignKeyConstraint(['stack_id'], ['stack.id'], name='ilm_config_stack_id_fkey'),
    sa.PrimaryKeyConstraint('id', name='ilm_config_pkey'),
    sa.UniqueConstraint('stack_id', 'alias_pattern', name='ilm_config_stack_id_alias_pattern_key')
    )
    # ### end Alembic commands ###
