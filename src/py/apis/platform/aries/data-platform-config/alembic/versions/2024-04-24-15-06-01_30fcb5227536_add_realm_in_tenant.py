"""Add Realm In Tenant

Revision ID: 30fcb5227536
Revises: 67f72557f055
Create Date: 2024-04-24 15:06:01.563429

"""

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = "30fcb5227536"
down_revision = "67f72557f055"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("tenant", sa.Column("realm", sa.String(length=128), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("tenant", "realm")
    # ### end Alembic commands ###
