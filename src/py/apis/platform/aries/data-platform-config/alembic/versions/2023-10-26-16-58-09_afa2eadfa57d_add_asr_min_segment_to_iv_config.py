"""add_asr_min_segment_to_iv_config

Revision ID: afa2eadfa57d
Revises: 74e5cdf3ff9c
Create Date: 2023-10-26 16:58:09.640100

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'afa2eadfa57d'
down_revision = '74e5cdf3ff9c'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('intelligent_voice_config', sa.Column('asr_min_segment', sa.Integer(), server_default='12', nullable=False))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('intelligent_voice_config', 'asr_min_segment')
    # ### end Alembic commands ###
