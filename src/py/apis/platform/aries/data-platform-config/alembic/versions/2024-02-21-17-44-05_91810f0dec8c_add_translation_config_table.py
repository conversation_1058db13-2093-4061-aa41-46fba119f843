"""add_translation_config_table

Revision ID: 91810f0dec8c
Revises: 4674c581a7f9
Create Date: 2024-02-21 17:44:05.843551

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '91810f0dec8c'
down_revision = '4674c581a7f9'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('TranslationConfig',
    sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('tenantId', sa.Integer(), nullable=False),
    sa.Column('workflowId', sa.Integer(), nullable=False),
    sa.Column('translationEnabled', sa.Boolean(), server_default=sa.text('false'), nullable=False),
    sa.Column('translationSourceLanguage', sa.String(length=50), nullable=True),
    sa.Column('translationTargetLanguage', sa.String(length=50), nullable=True),
    sa.Column('translationProvider', sa.String(length=50), nullable=True),
    sa.Column('createdDateTime', sa.DateTime(), server_default=sa.text("TIMEZONE('utc', CURRENT_TIMESTAMP)"), nullable=False),
    sa.Column('updatedDateTime', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['tenantId'], ['tenant.id'], ),
    sa.ForeignKeyConstraint(['workflowId'], ['workflow.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('tenantId', 'workflowId')
    )
    op.drop_column('intelligent_voice_config', 'translation_provider')
    op.drop_column('intelligent_voice_config', 'translation_language')
    op.drop_column('intelligent_voice_config', 'translation_enabled')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('intelligent_voice_config', sa.Column('translation_enabled', sa.BOOLEAN(), autoincrement=False, nullable=True))
    op.add_column('intelligent_voice_config', sa.Column('translation_language', sa.VARCHAR(), autoincrement=False, nullable=True))
    op.add_column('intelligent_voice_config', sa.Column('translation_provider', sa.VARCHAR(), autoincrement=False, nullable=True))
    op.drop_table('TranslationConfig')
    # ### end Alembic commands ###
