"""add conductor-workflow-schedule table.

Revision ID: 3d4f39355258
Revises: 9ca1b2312d83
Create Date: 2023-06-08 18:00:48.899984
"""
import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = "3d4f39355258"
down_revision = "9ca1b2312d83"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "conductor_workflow_schedule",
        sa.Column("id", sa.Integer(), autoincrement=True, nullable=False),
        sa.Column("tenant_id", sa.Integer(), nullable=False),
        sa.Column("workflow_name", sa.String(length=128), nullable=False),
        sa.Column("schedule_id", sa.String(length=128), nullable=False),
        sa.Column(
            "schedule_type",
            postgresql.ENUM("cron", "interval", name="conductor_workflow_schedule_type_enum"),
            nullable=False,
        ),
        sa.Column("workflow_input", postgresql.JSONB(astext_type=sa.Text()), nullable=False),
        sa.Column("paused", sa.Boolean(), server_default=sa.text("false"), nullable=False),
        sa.Column("deleted", sa.Boolean(), server_default=sa.text("false"), nullable=False),
        sa.Column("cron", sa.String(), nullable=True),
        sa.Column("interval", postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column(
            "time_created",
            sa.DateTime(),
            server_default=sa.text("TIMEZONE('utc', CURRENT_TIMESTAMP)"),
            nullable=False,
        ),
        sa.Column("time_updated", sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(["tenant_id"], ["tenant.id"], ondelete="CASCADE"),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("schedule_id"),
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table("conductor_workflow_schedule")
    # ### end Alembic commands ###
