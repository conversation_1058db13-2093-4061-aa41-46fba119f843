from aries_config_api_httpschema.translation_config import TranslationConfigUpdate
from data_platform_config.containers import Container
from data_platform_config.services.translation_config import TranslationConfigService
from dependency_injector.wiring import Provide, inject
from fastapi import APIRouter, Depends, status

translation_config_router = APIRouter()


@translation_config_router.get("/{workflow_name}/translation/config")
@inject
def get_config_by_tenant_and_workflow(
    stack_name: str,
    tenant_name: str,
    workflow_name: str,
    service: TranslationConfigService = Depends(Provide[Container.translation_config_service]),
):
    """GETs the details from TranslationConfig table for a given tenant.

    :param stack_name:
    :param tenant_name:
    :param workflow_name:
    :param service:
    :return:
    """
    return service.get_config(
        stack_name=stack_name,
        tenant_name=tenant_name,
        workflow_name=workflow_name,
    )


@translation_config_router.put(
    "/{workflow_name}/translation/config", status_code=status.HTTP_201_CREATED
)
@inject
def upsert_config_by_tenant_and_workflow(
    stack_name: str,
    tenant_name: str,
    workflow_name: str,
    config_update: TranslationConfigUpdate,
    service: TranslationConfigService = Depends(Provide[Container.translation_config_service]),
):
    """PUTs the given details for a given tenant.

    :param stack_name:
    :param tenant_name:
    :param workflow_name:
    :param config_update: TranslationConfigUpdate,
    :param service:
    :return:
    """
    return service.upsert_config(
        stack_name=stack_name,
        tenant_name=tenant_name,
        config_to_upsert=config_update,
        workflow_name=workflow_name,
    )


@translation_config_router.delete("/{workflow_name}/translation/config")
@inject
def delete_config_by_tenant_and_workflow(
    stack_name: str,
    tenant_name: str,
    workflow_name: str,
    service: TranslationConfigService = Depends(Provide[Container.translation_config_service]),
):
    """delete config for a given tenant.

    :param stack_name:
    :param tenant_name:
    :param workflow_name:
    :param service:
    :return:
    """
    return service.delete_config(
        stack_name=stack_name,
        tenant_name=tenant_name,
        workflow_name=workflow_name,
    )
