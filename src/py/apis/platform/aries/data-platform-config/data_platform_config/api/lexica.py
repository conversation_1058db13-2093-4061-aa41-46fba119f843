from aries_config_api_httpschema.lexica import LexicaSeVersion, LexicaType
from data_platform_config.containers import Container
from data_platform_config.services.lexica import LexicaService
from dependency_injector.wiring import Provide, inject
from fastapi import APIRouter, Depends, status

lexica_router = APIRouter()


@lexica_router.get("")
@inject
def get_se_lexica_version(
    stack_name: str,
    tenant_name: str,
    lexica_type: LexicaType = LexicaType.tenant,
    service: LexicaService = Depends(Provide[Container.lexica_service]),
):
    """GETs the SE lexica version in use for given tenant.

    :param stack_name:
    :param tenant_name:
    :param lexica_type:
    :param service:
    :return:
    """
    return service.get_se_lexica_version(
        stack_name=stack_name, tenant_name=tenant_name, lexica_type=lexica_type
    )


@lexica_router.put("", status_code=status.HTTP_201_CREATED)
@inject
def put_se_lexica_version(
    stack_name: str,
    tenant_name: str,
    se_lexica_version: LexicaSeVersion,
    lexica_type: LexicaType = LexicaType.tenant,
    service: LexicaService = Depends(Provide[Container.lexica_service]),
):
    """PUTs the given se_lexica_version in the given tenant.

    :param stack_name:
    :param tenant_name:
    :param se_lexica_version:
    :param lexica_type:
    :param service:
    :return:
    """
    return service.put_se_lexica_version(
        stack_name=stack_name,
        tenant_name=tenant_name,
        se_lexica_version=se_lexica_version,
        lexica_type=lexica_type,
    )
