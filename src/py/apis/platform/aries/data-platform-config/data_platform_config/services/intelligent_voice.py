from aries_config_api_httpschema.intelligent_voice import IntelligentVoiceConfigUpdate
from data_platform_config.exceptions import DataNotFound
from data_platform_config.models.intelligent_voice.intelligent_voice_config import (
    IntelligentVoiceConfig,
)
from data_platform_config.models.system.stack import Stack
from data_platform_config.models.system.tenant import Tenant
from datetime import datetime
from sqlalchemy.dialects.postgresql import insert


class IntelligentVoiceService:
    def __init__(self, session_factory) -> None:
        self.session_factory = session_factory

    def get_config(self, stack_name: str, tenant_name: str) -> IntelligentVoiceConfig:
        with self.session_factory() as session:
            config: IntelligentVoiceConfig = (
                session.query(IntelligentVoiceConfig)
                .join(Tenant, Stack)
                .filter(Stack.name == stack_name)
                .filter(Tenant.name == tenant_name)
                .one_or_none()
            )
            if not config:
                raise DataNotFound(f"Intelligent Voice Config not found for Tenant: {tenant_name}")
            return config

    def upsert_config(
        self, stack_name: str, tenant_name: str, config_to_upsert: IntelligentVoiceConfigUpdate
    ) -> None:
        # check whether the tenant exists or not
        with self.session_factory() as session:
            tenant = (
                session.query(Tenant)
                .join(Stack)
                .filter(Stack.name == stack_name)
                .filter(Tenant.name == tenant_name)
                .one_or_none()
            )
            if not tenant:
                raise DataNotFound(f"Tenant: {tenant_name} does not exist")

            current_time_updated = datetime.utcnow()
            config_data = config_to_upsert.dict(exclude_unset=True)
            config_data["time_updated"] = current_time_updated
            insert_stmt = insert(IntelligentVoiceConfig).values(tenant_id=tenant.id, **config_data)
            on_conflict_update_stmt = insert_stmt.on_conflict_do_update(
                index_elements=["tenant_id"], set_=config_data
            )
            session.execute(on_conflict_update_stmt)
            session.commit()
