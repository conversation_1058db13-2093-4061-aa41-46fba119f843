import logging
from aries_config_api_httpschema.tenant_workflow import (
    TenantWorkflowCreate,
    TenantWorkflowCreated,
    TenantWorkflowDynamicConfigUpdate,
    TenantWorkflowDynamicConfigUpdated,
    TenantWorkflowSearchByStaticConfig,
    TenantWorkflowUpdate,
    TenantWorkflowUpdated,
)
from data_platform_config.exceptions import DataNotFound
from data_platform_config.models.system.stack import Stack
from data_platform_config.models.system.tenant import Tenant
from data_platform_config.models.system.tenant_workflow import TenantWorkflow
from data_platform_config.models.system.workflow import Workflow
from operator import and_
from sqlalchemy import cast, true
from sqlalchemy.dialects.postgresql import JSONB
from sqlalchemy.orm import contains_eager, joinedload
from typing import Iterator

logger = logging.getLogger(__name__)


class TenantWorkflowService:
    def __init__(self, session_factory) -> None:
        self.session_factory = session_factory

    def get(self, stack_name: str, tenant_name: str, workflow_name: str) -> TenantWorkflow:
        with self.session_factory() as session:
            result: TenantWorkflow = (
                session.query(TenantWorkflow)
                .join(Tenant, Stack, Workflow)
                .options(joinedload(TenantWorkflow.tenant).joinedload(Tenant.stack))  # type: ignore
                .options(joinedload(TenantWorkflow.workflow))  # type: ignore
                .filter(Workflow.name == workflow_name)
                .filter(Stack.name == stack_name)
                .filter(Tenant.name == tenant_name)
                .one()
            )
            return result

    def get_all_by_tenant(
        self,
        stack_name: str,
        tenant_name: str,
        tenant_paused: bool | None = None,
        tenant_workflow_paused: bool | None = None,
    ) -> list[TenantWorkflow]:
        with self.session_factory() as session:
            result: list[TenantWorkflow] = (
                session.query(Tenant)
                .join(Stack)
                .outerjoin(
                    TenantWorkflow,
                    and_(
                        TenantWorkflow.tenant_id == Tenant.id,
                        (
                            (TenantWorkflow.paused == tenant_workflow_paused)
                            if tenant_workflow_paused is not None
                            else true()
                        ),
                    ),
                )
                .options(contains_eager(Tenant.tenant_workflows))
                .filter(Stack.name == stack_name)
                .filter(Tenant.name == tenant_name)
                .filter((Tenant.paused == tenant_paused) if tenant_paused is not None else true())
                .one()
            ).tenant_workflows

            return result

    def add(
        self,
        stack_name: str,
        tenant_name: str,
        tenant_workflow_create: TenantWorkflowCreate,
    ) -> TenantWorkflowCreated:
        with self.session_factory() as session:
            tenant_workflow = (
                session.query(TenantWorkflow)
                .join(Tenant, Stack, Workflow)
                .filter(Workflow.name == tenant_workflow_create.name)
                .filter(Stack.name == stack_name)
                .filter(Tenant.name == tenant_name)
                .one_or_none()
            )

            workflow = (
                session.query(Workflow)
                .filter(Workflow.name == tenant_workflow_create.name)
                .one_or_none()
            )
            if workflow is None:
                raise DataNotFound(f"Workflow {tenant_workflow_create.name} not found")

            tenant_workflow_keys = tenant_workflow_create.dict(exclude_unset=True)
            # Remove name from the dict because the column doesn't exist on the
            # TenantWorkflow table, it's a foreign key link to the Workflow table
            del tenant_workflow_keys["name"]

            if tenant_workflow:
                # update if exists
                for key, value in tenant_workflow_keys.items():
                    setattr(tenant_workflow, key, value)
                session.commit()
                return TenantWorkflowCreated(
                    batch_timeout_s=tenant_workflow.batch_timeout_s,
                    io_topic=tenant_workflow.io_topic,
                    max_batch_size=tenant_workflow.max_batch_size,
                    name=tenant_workflow_create.name,
                    paused=tenant_workflow.paused,
                )

            new_tenant_workflow = TenantWorkflow(**tenant_workflow_keys)
            tenant = (
                session.query(Tenant)
                .join(Stack)
                .filter(Stack.name == stack_name)
                .filter(Tenant.name == tenant_name)
                .one()
            )
            tenant.tenant_workflows.append(new_tenant_workflow)
            workflow.tenant_workflows.append(new_tenant_workflow)
            session.commit()

            return TenantWorkflowCreated(
                name=tenant_workflow_create.name,
                batch_timeout_s=new_tenant_workflow.batch_timeout_s,
                io_topic=new_tenant_workflow.io_topic,
                max_batch_size=new_tenant_workflow.max_batch_size,
                paused=new_tenant_workflow.paused,
            )

    def get_all(self) -> Iterator[TenantWorkflow]:
        with self.session_factory() as session:
            result: Iterator[TenantWorkflow] = session.query(TenantWorkflow).all()
            return result

    def get_all_by_stack(
        self,
        stack_name: str,
        stack_paused: bool | None = None,
        tenant_paused: bool | None = None,
        tenant_workflow_paused: bool | None = None,
    ) -> list[TenantWorkflow]:
        with self.session_factory() as session:
            result: list[TenantWorkflow] = (
                session.query(TenantWorkflow)
                .join(Tenant, Stack)
                .options(joinedload(TenantWorkflow.workflow))  # type: ignore
                .options(joinedload(TenantWorkflow.tenant))  # type: ignore
                .filter(Stack.name == stack_name)
                .filter((Stack.paused == stack_paused) if (stack_paused is not None) else true())
                .filter((Tenant.paused == tenant_paused) if (tenant_paused is not None) else true())
                .filter(
                    (TenantWorkflow.paused == tenant_workflow_paused)
                    if (tenant_workflow_paused is not None)
                    else true()
                )
                .all()
            )
            return result

    def update_patch(
        self,
        stack_name: str,
        tenant_name: str,
        workflow_name: str,
        tenant_workflow_update: TenantWorkflowUpdate,
    ) -> TenantWorkflowUpdated:
        with self.session_factory() as session:
            session.connection(execution_options={"isolation_level": "SERIALIZABLE"})
            tenant_workflow = (
                session.query(TenantWorkflow)
                .join(Tenant, Stack, Workflow)
                .filter(Workflow.name == workflow_name)
                .filter(Stack.name == stack_name)
                .filter(Tenant.name == tenant_name)
                .one()
            )
            # update if exists
            config_dict = tenant_workflow_update.dict(exclude_unset=True)
            if tenant_workflow_update:
                for key, value in config_dict.items():
                    # TODO: Remove backward compatibility for updating workflow_execution_ref
                    if key == "workflow_execution_ref":
                        workflow_execution_ref = (
                            dict(**tenant_workflow.workflow_execution_ref)
                            if tenant_workflow.workflow_execution_ref
                            else {}
                        )
                        workflow_execution_ref.update(value)
                        setattr(tenant_workflow, key, workflow_execution_ref)
                    else:
                        setattr(tenant_workflow, key, value)
                session.commit()

            return TenantWorkflowUpdated(
                io_topic=tenant_workflow.io_topic,
                max_batch_size=tenant_workflow.max_batch_size,
                batch_timeout_s=tenant_workflow.batch_timeout_s,
                workflow_last_executed=tenant_workflow.workflow_last_executed,
                static_config=tenant_workflow.static_config,
                workflow_execution_ref=tenant_workflow.workflow_execution_ref,
            )

    def update_dynamic_config(
        self,
        stack_name: str,
        tenant_name: str,
        tenant_workflow_dynamic_config_update: TenantWorkflowDynamicConfigUpdate,
        workflow_name: str,
    ) -> TenantWorkflowDynamicConfigUpdated:
        with self.session_factory() as session:
            session.connection(execution_options={"isolation_level": "SERIALIZABLE"})
            tenant_workflow = (
                session.query(TenantWorkflow)
                .join(Tenant, Stack, Workflow)
                .filter(Workflow.name == workflow_name)
                .filter(Stack.name == stack_name)
                .filter(Tenant.name == tenant_name)
                .one()
            )
            existing_ref = tenant_workflow.workflow_execution_ref or {}
            updated_ref = {
                **existing_ref,
                **tenant_workflow_dynamic_config_update.workflow_execution_ref,
            }
            tenant_workflow.workflow_execution_ref = updated_ref

            session.commit()
            return TenantWorkflowDynamicConfigUpdated(
                workflow_execution_ref=tenant_workflow.workflow_execution_ref
            )

    def search_tenant_workflows_by_config(
        self,
        tenant_workflow_search_by_static_config: TenantWorkflowSearchByStaticConfig,
        stack_name: str,
    ) -> list[TenantWorkflow]:
        with self.session_factory() as session:
            conditions = []
            query = (
                session.query(TenantWorkflow)
                .join(Tenant)
                .join(Stack)
                .options(joinedload(TenantWorkflow.workflow))
                .options(joinedload(TenantWorkflow.tenant))
                .filter(Stack.name == stack_name)
            )

            # Example: When -
            # config.static_config = {
            #     "enabled": true,
            #     "transcription.languages": ["en", "fr", "zh", "ms"],
            #     "transcription.settings.languageMixing": "single"
            # }
            for key, value in tenant_workflow_search_by_static_config.static_config_filter.items():
                path = key.split(".")
                # key = transcription.languages
                # path = ['transcription', 'languages']
                # column = tenant_workflow.static_config[transcription][languages]
                # value = ['en', 'fr', 'zh', 'ms']
                column = TenantWorkflow.static_config[path]

                if value is None:  # null value
                    conditions.append(column.is_(None))

                # List or Dict — match full structure
                elif isinstance(value, (list, dict)):
                    conditions.append(cast(column, JSONB) == value)

                elif isinstance(value, bool):
                    # For booleans
                    conditions.append(column.astext == str(value).lower())
                elif isinstance(value, str):
                    conditions.append(column.astext == value)
                else:
                    logger.warning("Invalid filter value")

            return query.filter(*conditions).all()
