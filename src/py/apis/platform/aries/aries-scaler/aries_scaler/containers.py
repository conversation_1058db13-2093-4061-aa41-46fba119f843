import httpx
import pathlib
import se_boltons.log_config
from aries_scaler.services.scaler import ScalerService
from dependency_injector import containers, providers


class Container(containers.DeclarativeContainer):
    """Main container for all dependencies and configuration."""

    # Entry point of all dependency injection
    wiring_config = containers.WiringConfiguration(packages=["aries_scaler.api"])

    # add config to container
    yaml_path = pathlib.Path(__file__).parent.parent.joinpath("aries-scaler-config.yml")
    if not yaml_path.is_file():
        raise ValueError(f"{yaml_path} does not exist")
    config = providers.Configuration(yaml_files=[str(yaml_path)])

    # configure logging
    logging = providers.Resource(se_boltons.log_config.configure_logging)  # type: ignore

    conductor_api_client = providers.Resource(
        httpx.Client,
        base_url=config.conductor_api_url,
        timeout=30,
    )
    scaler_service = providers.ThreadSafeSingleton(
        ScalerService,
        conductor_api_client=conductor_api_client,
        config=config,
    )
