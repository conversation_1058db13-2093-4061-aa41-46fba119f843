ARG SE_MONO_PY11_BASE
FROM $SE_MONO_PY11_BASE

EXPOSE 5000

# switch to non-root user before copying pex
USER app
# ensure that following ENTRYPOINT, COPY, ARG, and ENV statements are always added to the end of dockerfile in this order for optimal caching
ENTRYPOINT ["/bin/app/__main__.py"]
COPY --chown=app:app src.py.apis.platform.aries.listener-api/bin.pex /bin/app
ARG IMAGE_TAG
ENV SE_VERSION=$IMAGE_TAG