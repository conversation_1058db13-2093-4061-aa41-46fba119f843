# type: ignore
import pytest
from api_sdk.models.elasticsearch import <PERSON><PERSON><PERSON>ult
from mock import AsyncMock
from se_api_svc.repository.comms_surveillance.neural.legacy import LegacyNeuralCallRepository
from se_api_svc.schemas.surveillance.comms_surveillance.comm_surveillance.commsurveillance import (
    FutureQuery,
)
from se_api_svc.schemas.surveillance.comms_surveillance.neural import (
    NeuralBacktestSummary,
    NeuralHitsByRisk,
    NeuralHitsByRiskScore,
    NeuralRiskScoreList,
)
from se_schema.all_components import (
    BehaviourChunkedFlangFilter,
    FalsePositiveReduction,
    FlangFilterChunk,
)
from se_schema.all_enums import Behaviour<PERSON><PERSON>yKind


@pytest.fixture()
def neural_comm_repo():
    obj = LegacyNeuralCallRepository.__new__(LegacyNeuralCallRepository)
    obj.get_aggs = AsyncMock(
        return_value=RawResult(
            **{
                "took": 24,
                "timed_out": False,
                "_shards": {"total": 1, "successful": 1, "skipped": 0, "failed": 0},
                "hits": {"total": 0, "max_score": 0.0, "hits": []},
                "aggregations": {
                    "RISK": {
                        "doc_count_error_upper_bound": 0,
                        "sum_other_doc_count": 0,
                        "buckets": [
                            {
                                "key": "COMMUNICATION_AND_MISREPRESENTATION",
                                "doc_count": 2,
                                "SCORE": {
                                    "doc_count_error_upper_bound": 0,
                                    "sum_other_doc_count": 1,
                                    "buckets": [
                                        {"key": 3, "doc_count": 1},
                                        {"key": 6, "doc_count": 1},
                                    ],
                                },
                            },
                            {
                                "key": "CONDUCT_RISK",
                                "doc_count": 2,
                                "SCORE": {
                                    "doc_count_error_upper_bound": 0,
                                    "sum_other_doc_count": 0,
                                    "buckets": [
                                        {"key": 7, "doc_count": 2},
                                        {"key": 3, "doc_count": 1},
                                    ],
                                },
                            },
                            {
                                "key": "INFORMATION_HANDLING",
                                "doc_count": 2,
                                "SCORE": {
                                    "doc_count_error_upper_bound": 0,
                                    "sum_other_doc_count": 2,
                                    "buckets": [
                                        {"key": 3, "doc_count": 1},
                                        {"key": 4, "doc_count": 1},
                                    ],
                                },
                            },
                            {
                                "key": "OPERATIONAL_AND_PROCEDURAL",
                                "doc_count": 2,
                                "SCORE": {
                                    "doc_count_error_upper_bound": 0,
                                    "sum_other_doc_count": 0,
                                    "buckets": [
                                        {"key": 7, "doc_count": 2},
                                        {"key": 6, "doc_count": 1},
                                    ],
                                },
                            },
                            {
                                "key": "DATA_PRIVACY",
                                "doc_count": 1,
                                "SCORE": {
                                    "doc_count_error_upper_bound": 0,
                                    "sum_other_doc_count": 0,
                                    "buckets": [{"key": 5, "doc_count": 1}],
                                },
                            },
                            {
                                "key": "INSIDER_INFORMATION",
                                "doc_count": 1,
                                "SCORE": {
                                    "doc_count_error_upper_bound": 0,
                                    "sum_other_doc_count": 0,
                                    "buckets": [{"key": 7, "doc_count": 1}],
                                },
                            },
                            {
                                "key": "MARKET_MANIPULATION",
                                "doc_count": 1,
                                "SCORE": {
                                    "doc_count_error_upper_bound": 0,
                                    "sum_other_doc_count": 0,
                                    "buckets": [
                                        {"key": 4, "doc_count": 1},
                                        {"key": 6, "doc_count": 1},
                                    ],
                                },
                            },
                        ],
                    },
                    "end_date": {
                        "value": 1.694534162105e12,
                        "value_as_string": "2023-09-12T15:56:02.105Z",
                    },
                    "start_date": {
                        "value": 1.692177340622e12,
                        "value_as_string": "2023-08-16T09:15:40.622Z",
                    },
                    "aggs": {
                        "doc_count": 334,
                        "COUNT": {
                            "doc_count_error_upper_bound": 0,
                            "sum_other_doc_count": 324,
                            "buckets": [
                                {"key": "blah", "doc_count": 1},
                            ],
                        },
                    },
                },
            }
        )
    )
    obj.get_many = AsyncMock(
        return_value=RawResult(
            **{
                "took": 4,
                "timed_out": False,
                "_shards": {"total": 1, "successful": 1, "skipped": 0, "failed": 0},
                "hits": {"total": 711, "max_score": 0.0, "hits": []},
            }
        )
    )

    return obj


@pytest.mark.asyncio
async def test_get_neural_backtest_summary(neural_comm_repo):
    assert await neural_comm_repo.get_neural_backtest_summary(
        query_in=FutureQuery(
            name="None",
            description="backtest",
            kind=BehaviourQueryKind.NEURAL,
            lexicaBehaviour=None,
            falsePositiveReduction=FalsePositiveReduction(
                excludedClassifications=None, excludedZones=None
            ),
            filter=BehaviourChunkedFlangFilter(
                flangVersion="1.2",
                chunks=[
                    FlangFilterChunk(
                        f="greaterThanEqualValue('score',2)",
                        category="minimum severity score",
                        id="03894a50-83cf-4e9c-ae29-0a0522d25e75",
                    ),
                    FlangFilterChunk(
                        f="involvedField('name',['Pedro  Moliner Royo'])",
                        category="people/involving/name",
                        id="3355bf4a-f1d0-4de1-99ef-614c6a19dfa0",
                    ),
                ],
            ),
            template=None,
        ),
    ) == NeuralBacktestSummary(totalHits=334, percentageOfComms=0.4698, hitPerDay=13.0)


@pytest.mark.asyncio
async def test_get_word_cloud(neural_comm_repo):
    assert await neural_comm_repo.get_word_cloud(
        query_in=FutureQuery(
            name="None",
            description="backtest",
            kind=BehaviourQueryKind.NEURAL,
            lexicaBehaviour=None,
            falsePositiveReduction=FalsePositiveReduction(
                excludedClassifications=None, excludedZones=None
            ),
            filter=BehaviourChunkedFlangFilter(
                flangVersion="1.2",
                chunks=[
                    FlangFilterChunk(
                        f="greaterThanEqualValue('score',2)",
                        category="minimum severity score",
                        id="03894a50-83cf-4e9c-ae29-0a0522d25e75",
                    ),
                    FlangFilterChunk(
                        f="involvedField('name',['Pedro  Moliner Royo'])",
                        category="people/involving/name",
                        id="3355bf4a-f1d0-4de1-99ef-614c6a19dfa0",
                    ),
                ],
            ),
            template=None,
        ),
    ) == [
        NeuralHitsByRisk(riskCategory="COMMUNICATION_AND_MISREPRESENTATION", hits=2),
        NeuralHitsByRisk(riskCategory="CONDUCT_RISK", hits=2),
        NeuralHitsByRisk(riskCategory="INFORMATION_HANDLING", hits=2),
        NeuralHitsByRisk(riskCategory="OPERATIONAL_AND_PROCEDURAL", hits=2),
        NeuralHitsByRisk(riskCategory="DATA_PRIVACY", hits=1),
        NeuralHitsByRisk(riskCategory="INSIDER_INFORMATION", hits=1),
        NeuralHitsByRisk(riskCategory="MARKET_MANIPULATION", hits=1),
    ]


@pytest.mark.asyncio
async def test_get_risk_score(neural_comm_repo):
    assert await neural_comm_repo.get_risk_score(
        query_in=FutureQuery(
            name="None",
            description="backtest",
            kind=BehaviourQueryKind.NEURAL,
            lexicaBehaviour=None,
            falsePositiveReduction=FalsePositiveReduction(
                excludedClassifications=None, excludedZones=None
            ),
            filter=BehaviourChunkedFlangFilter(
                flangVersion="1.2",
                chunks=[
                    FlangFilterChunk(
                        f="greaterThanEqualValue('score',2)",
                        category="minimum severity score",
                        id="03894a50-83cf-4e9c-ae29-0a0522d25e75",
                    ),
                    FlangFilterChunk(
                        f="involvedField('name',['Pedro  Moliner Royo'])",
                        category="people/involving/name",
                        id="3355bf4a-f1d0-4de1-99ef-614c6a19dfa0",
                    ),
                ],
            ),
            template=None,
        ),
    ) == [
        NeuralHitsByRiskScore(
            riskCategory="COMMUNICATION_AND_MISREPRESENTATION",
            riskScores=[
                NeuralRiskScoreList(riskSeverity="3", riskScoreHits="1"),
                NeuralRiskScoreList(riskSeverity="6", riskScoreHits="1"),
            ],
        ),
        NeuralHitsByRiskScore(
            riskCategory="CONDUCT_RISK",
            riskScores=[NeuralRiskScoreList(riskSeverity="7", riskScoreHits="2")],
        ),
        NeuralHitsByRiskScore(
            riskCategory="INFORMATION_HANDLING",
            riskScores=[
                NeuralRiskScoreList(riskSeverity="3", riskScoreHits="1"),
                NeuralRiskScoreList(riskSeverity="4", riskScoreHits="1"),
            ],
        ),
        NeuralHitsByRiskScore(
            riskCategory="OPERATIONAL_AND_PROCEDURAL",
            riskScores=[NeuralRiskScoreList(riskSeverity="7", riskScoreHits="2")],
        ),
        NeuralHitsByRiskScore(
            riskCategory="DATA_PRIVACY",
            riskScores=[NeuralRiskScoreList(riskSeverity="5", riskScoreHits="1")],
        ),
        NeuralHitsByRiskScore(
            riskCategory="INSIDER_INFORMATION",
            riskScores=[NeuralRiskScoreList(riskSeverity="7", riskScoreHits="1")],
        ),
        NeuralHitsByRiskScore(
            riskCategory="MARKET_MANIPULATION",
            riskScores=[NeuralRiskScoreList(riskSeverity="4", riskScoreHits="1")],
        ),
    ]
