import logging
import pytest
from api_sdk.config_base import ApiConfig
from dataclasses import dataclass
from schema_sdk.steeleye_model.gamma import GammaMetaFields
from se_api_svc.repository.data_provenance.records import SinkFileAuditSearch
from se_elastic_schema.static.provenance import TaskStatus

logger = logging.getLogger(__name__)


@dataclass
class FakeTenancy:
    realm: str = "test"
    userId: str = "<EMAIL>"


@dataclass
class FakeRepo:
    api_config: ApiConfig
    tenancy: FakeTenancy


def test_query():
    sf = SinkFileAuditSearch().to_dict(meta_fields=GammaMetaFields)
    assert sf.get("query")
    assert sf.get("query").get("bool").get("filter")


def test_status_exists():
    sf = SinkFileAuditSearch(status=[TaskStatus.QUEUED, TaskStatus.PROCESSING]).to_dict(
        meta_fields=GammaMetaFields
    )

    filter = sf.get("query").get("bool").get("filter")
    keys = [list(x.keys())[0] for x in filter]

    assert len(keys) > 0

    idx = keys.index("terms")
    terms = filter[idx].get("terms")

    assert terms.get("status") == [TaskStatus.QUEUED, TaskStatus.PROCESSING]


def test_status_empty():
    sf = SinkFileAuditSearch()

    sf.params.status = None
    build = sf.to_dict(meta_fields=GammaMetaFields)
    filter = build.get("query").get("bool").get("filter")
    keys = [list(x.keys())[0] for x in filter]

    with pytest.raises(ValueError):
        keys.index("terms")
