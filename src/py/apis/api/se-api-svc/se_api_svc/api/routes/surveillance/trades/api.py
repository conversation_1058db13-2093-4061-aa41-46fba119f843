from fastapi import APIRouter
from se_api_svc.api.routes.surveillance.settings import create_notification_settings_router
from se_api_svc.api.routes.surveillance.trades import alerts, on_demand, queries, users, watches
from se_api_svc.schemas.settings import SettingsModule

router = APIRouter()

router.include_router(alerts.router, prefix="/alerts", tags=["surveillance/trades/alerts"])

router.include_router(
    create_notification_settings_router(
        module=SettingsModule.TRADES_SURVEILLANCE, name_prefix="surveillance:trades"
    ),
    prefix="/settings/notifications",
    tags=["surveillance/trades/settings/notifications"],
)
router.include_router(queries.router, prefix="/queries", tags=["surveillance/trades/queries"])
router.include_router(
    users.router,
    prefix="/users",
    tags=["surveillance/trades/users"],
)
router.include_router(
    watches.router,
    prefix="/watches",
    tags=["surveillance/trades/watches"],
)
router.include_router(on_demand.router, prefix="/on-demand", tags=["surveillance/trades/on-demand"])
