# type: ignore
from api_sdk.middleware.module_permission_checker import Mo<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from api_sdk.schemas.module_permission_checker import All, Any
from api_sdk.schemas.static import Module
from fastapi import APIRouter, Depends
from se_api_svc.api.routes import version
from se_api_svc.api.routes.account import api as account
from se_api_svc.api.routes.admin import api as admin
from se_api_svc.api.routes.ai import api as api
from se_api_svc.api.routes.audits import router as audits_router
from se_api_svc.api.routes.auth import api as auth
from se_api_svc.api.routes.bookmarks import api as bookmarks
from se_api_svc.api.routes.case_manager.api import router as case_manager
from se_api_svc.api.routes.comms.api import router as comms_router
from se_api_svc.api.routes.comms_surveillance.api import router as comms_surveillance
from se_api_svc.api.routes.data_provenance.api import router as data_provenance
from se_api_svc.api.routes.executions import router as executions
from se_api_svc.api.routes.insights.api import router as insights_router
from se_api_svc.api.routes.mar.api import router as mar_router
from se_api_svc.api.routes.market import router as market
from se_api_svc.api.routes.market_data import router as market_router
from se_api_svc.api.routes.neural.api import router as neural_router
from se_api_svc.api.routes.news import router as news
from se_api_svc.api.routes.orders import api as orders
from se_api_svc.api.routes.position import router as position
from se_api_svc.api.routes.rts22 import api as rts22
from se_api_svc.api.routes.schema.api import router as schema_router
from se_api_svc.api.routes.server_sent_events.api import router as server_sent_events
from se_api_svc.api.routes.slack import router as slack
from se_api_svc.api.routes.surveillance.api import router as surveillance
from se_api_svc.api.routes.tenancy import router as tenancy_router
from se_api_svc.api.routes.tr import router as tr
from se_api_svc.api.routes.track import router as track
from se_api_svc.api.routes.trade_surveillance.api import router as trade_surveillance
from se_api_svc.api.routes.translate import router as translate_router
from se_api_svc.api.routes.upload import router as upload
from se_api_svc.api.routes.user_audit import router as user_audit
from se_api_svc.api.routes.user_comment.api import router as user_comment
from se_api_svc.api.routes.validate import router as validate_router
from se_api_svc.api.routes.zoom import router as zoom

router = APIRouter()

router.include_router(version.router, tags=["version"])

# Sorted list of routes
router.include_router(admin.router, prefix="/admin", tags=["admin"])
router.include_router(account.router, prefix="/account", tags=["account"])
router.include_router(auth.router, prefix="/auth")
router.include_router(bookmarks.router, tags=["bookmarks"])
# ruff: noqa: E501
router.include_router(
    case_manager,
    prefix="/case-manager",
    tags=["case-manager"],
    dependencies=[Depends(ModulePermissionChecker(All(Module.CASE_MANAGER)))],
)
router.include_router(case_manager, prefix="/case-manager", tags=["case-manager"])
router.include_router(comms_surveillance, prefix="/comms-surveillance", tags=["comms-surveillance"])
router.include_router(
    rts22.router,
    prefix="/rts22",
    tags=["rts22"],
    dependencies=[Depends(ModulePermissionChecker(All(Module.TRANSACTION_REPORTING)))],
)
router.include_router(comms_router, prefix="/comms")
router.include_router(trade_surveillance, prefix="/trade-surveillance", tags=["trade-surveillance"])
router.include_router(api.router, prefix="/ai", tags=["ai"])
router.include_router(
    market_router,
    prefix="/market-data",
    dependencies=[
        Depends(
            ModulePermissionChecker(Any(Module.MARKET, Module.TRADE_SURVEILLANCE, Module.ORDERS))
        )
    ],
)
router.include_router(schema_router, prefix="/schema")
router.include_router(translate_router, prefix="/translate")
router.include_router(audits_router, prefix="/audits", tags=["audits"])
router.include_router(validate_router, prefix="/validate", tags=["validation"])
router.include_router(tenancy_router, tags=["tenancy"])
router.include_router(
    orders.router,
    dependencies=[Depends(ModulePermissionChecker(All(Module.ORDERS)))],
)

router.include_router(insights_router, tags=["insights"])
router.include_router(
    data_provenance,
    prefix="/data-provenance",
    dependencies=[Depends(ModulePermissionChecker(All(Module.DATA_PROVENANCE)))],
)
router.include_router(
    mar_router,
    prefix="/mar",
    dependencies=[Depends(ModulePermissionChecker(Any(Module.TRADE_SURVEILLANCE)))],
)
router.include_router(user_comment, tags=["user-comment"])
router.include_router(neural_router, prefix="/neural", tags=["neural reviews"])
router.include_router(
    tr,
    prefix="/transaction-reporting",
    tags=["transaction-reporting"],
    dependencies=[Depends(ModulePermissionChecker(All(Module.TRANSACTION_REPORTING)))],
)
router.include_router(surveillance, prefix="/surveillance")
router.include_router(track, prefix="/_track", tags=["_track"])

# ADMIN ONLY
router.include_router(
    user_audit,
    prefix="/user-audits",
    tags=["user-audit"],
    dependencies=[Depends(ModulePermissionChecker(All(Module.ADMIN)))],
)

router.include_router(zoom, prefix="/zoom", tags=["zoom"])
router.include_router(slack, prefix="/slack", tags=["zoom"])
router.include_router(server_sent_events, prefix="/server-sent-events", tags=["server-sent-events"])
router.include_router(upload, prefix="/upload", tags=["upload"])
router.include_router(position, prefix="/position", tags=["position"])
router.include_router(
    executions,
    prefix="/executions",
    tags=["executions"],
    dependencies=[Depends(ModulePermissionChecker(Any(Module.ORDERS, Module.BEST_EXECUTION)))],
)
router.include_router(news, tags=["get-news-content"])
router.include_router(
    market,
    prefix="/market",
    tags=["market"],
    dependencies=[Depends(ModulePermissionChecker(Any(Module.MARKET)))],
)
