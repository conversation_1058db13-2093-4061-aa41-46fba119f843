import logging
from api_sdk.di.request import ReqDep
from api_sdk.exception_handlers import master_data_exception_handler
from api_sdk.exceptions import NotFound
from api_sdk.messages.fastapi_message_bus import FastApiMessageBus
from api_sdk.models.permissions import Permission
from api_sdk.models.request_params import PaginatedDatedListParams
from api_sdk.models.search import SearchResult
from api_sdk.services.master_data import MasterDataClient
from api_sdk.services.master_data_news import MasterDataNewsService
from api_sdk.utils.utils import nested_get
from fastapi import APIRouter, Depends, HTTPException
from se_api_svc.api.routes.orders.orders_base import GetOrdersOrExecutionsOut
from se_api_svc.messages.mar.events import RefinitivNewsSearched, ScenarioViewed
from se_api_svc.models.request_params import NewsParams
from se_api_svc.repository.mar.scenarios import ScenariosRepository
from se_api_svc.repository.order.orders import OrdersRepository
from se_api_svc.schemas.mar.scenarios import MarketAbuseScenario
from se_api_svc.services.news_service import NewsEventQuery, NewsQuery
from se_api_svc.utils.order import apply_aggregated_order_data, apply_day_trading_volume
from typing import List

log = logging.getLogger(__name__)

router = APIRouter()


@router.get(
    "/{identifier}",
    name="mar:scenarios:get-one",
    response_model=MarketAbuseScenario,
)
async def get_scenario(
    identifier: str,
    repo: ScenariosRepository = ReqDep(ScenariosRepository),
    mb: FastApiMessageBus = Depends(),
):
    repo.tenancy.require_permissions(Permission.MARKET_ABUSE)

    scenario = await repo.get_scenario_by_id(identifier=identifier)

    await mb.publish(ScenarioViewed(scenario=scenario))

    return scenario


@router.get(
    "/{identifier}/news",
    name="mar:scenarios:get-news",
)
@master_data_exception_handler
async def get_scenario_news(
    identifier: str,
    news_params: NewsParams = Depends(),
    repo: ScenariosRepository = ReqDep(ScenariosRepository),
    master_data_news_service: MasterDataNewsService = ReqDep(MasterDataNewsService),
    mb: FastApiMessageBus = Depends(),
):
    repo.tenancy.require_permissions(Permission.MARKET_ABUSE)

    scenario_result = await repo.get_scenario_by_id(identifier=identifier)

    """
    sources param can be assigned an empty string
    ex -
    www.example.com?sources
    www.example.com?sources=
    This makes sources a non empty list containing empty string. To avoid this, sources are filtered
    """
    sources = (
        [x for x in news_params.sources if x and len(x.strip()) != 0] if news_params.sources else []
    )

    event_queries: List[NewsEventQuery] = list()

    news_query = NewsQuery(
        sources=sources,
        min_relevance=news_params.min_relevance.value,
        number_of_articles=news_params.number_of_articles,
        queries=event_queries,
    )

    orders = scenario_result.collect_scenario_orders()

    for order in orders:
        query = NewsEventQuery.build_query_from_order_or_execution(
            order=order,
            minutes_after=news_params.minutes_after,
            minutes_before=news_params.minutes_before,
        )

        if not query:
            continue

        news_query.queries.append(query)

    body = news_params.as_refinitiv_kwargs()
    instrumentIdCodesWithRange = news_query.timestamp_before_after
    articles = await master_data_news_service.get_stories(
        body=body, instrumentIdCodesWithRange=instrumentIdCodesWithRange
    )
    await mb.publish(RefinitivNewsSearched(scenario=scenario_result))
    return {"articles": articles, "scenario": scenario_result.to_dict(exclude_none=True)}


@router.get(
    "/{identifier}/related-records",
    name="mar:scenarios:get-related-records",
    response_model=GetOrdersOrExecutionsOut,
)
@master_data_exception_handler
async def get_scenario_related_records(
    identifier: str,
    params: PaginatedDatedListParams = Depends(),
    repo: ScenariosRepository = ReqDep(ScenariosRepository),
    market_data_client: MasterDataClient = ReqDep(MasterDataClient),
    orders_repo: OrdersRepository = ReqDep(OrdersRepository),
    include_day_trading_volume: bool = False,
):
    repo.tenancy.require_permissions(Permission.MARKET_ABUSE, Permission.ORDERS)

    scenario = await repo.get_scenario_by_id(identifier=identifier)

    related_records = nested_get(scenario, "additional_fields.topLevel.relatedRecords")
    if not isinstance(related_records, list):
        raise NotFound(message="scenario does not have related records")

    related_records_map = {record["&id"]: record for record in related_records}

    # Get orders by ids present in related record
    orders = await orders_repo.get_orders_by_order_only_filters(
        ids=list(related_records_map.keys()),
        hit_deserializer=lambda x: x["_source"],
        **params.as_search_kwargs(),
    )

    # Enrich order records
    await apply_aggregated_order_data(repo=orders_repo, orders=orders.as_list())
    if include_day_trading_volume:
        apply_day_trading_volume(market_data_client=market_data_client, orders=orders.as_list())

    # Add related record info to orders
    for order in orders.hits.hits:
        related_record_info = related_records_map.get(order["&id"])
        if related_record_info:
            setattr(order, "relatedRecordInfo", related_record_info)

    return SearchResult.from_raw_result(orders, skipped_hits=params.page_params.skip)


@router.get(
    "/{identifier}/related-records/instruments",
    name="mar:scenarios:get-related-records-instruments",
    response_model=GetOrdersOrExecutionsOut,
    description="Returned instruments of orders in related records.",
)
async def get_scenario_related_records_instruments(
    identifier: str,
    repo: ScenariosRepository = ReqDep(ScenariosRepository),
    orders_repo: OrdersRepository = ReqDep(OrdersRepository),
):
    repo.tenancy.require_permissions(Permission.MARKET_ABUSE, Permission.ORDERS)

    scenario = await repo.get_scenario_by_id(identifier=identifier)

    related_records = nested_get(scenario, "additional_fields.topLevel.relatedRecords")
    if not isinstance(related_records, list):
        raise HTTPException(status_code=404, detail="scenario does not have related records")

    related_records_map = {record["&id"]: record for record in related_records}

    # Get orders by ids present in related record
    orders = await orders_repo.get_orders_instruments_by_order_only_filters(
        ids=list(related_records_map.keys()), hit_deserializer=lambda x: x["_source"]
    )

    for order in orders.hits.hits:
        related_record_info = related_records_map.get(order["&id"])
        if related_record_info:
            setattr(order, "relatedRecordInfo", related_record_info)

    return SearchResult.from_raw_result(orders)
