domains = [
    "126.com",
    "amazon.co.uk",
    "amazon.com",
    "automationforjira.com",
    "circleci.com",
    "cityandfinancial.com",
    "docusign.net",
    "dowjones.com",
    "dtcc.com",
    "e.economist.com",
    "email.mrporter.com",
    "email.mrporter.com",
    "emails.bookatable.com",
    "facebookmail.com",
    "fitchlearning.com",
    "ftfnews.com",
    "github.com",
    "google.com",
    "hfm-email.com",
    "hubspot.com",
    "industrialinfo.com",
    "interactive.wsj.com",
    "investmentweek.co.uk",
    "isda.org",
    "jira.com",
    "linkedin.com",
    "lseg.com",
    "mail.adobe.com",
    "marketing.refinitiv.com",
    "mg.weatherdelta.com",
    "newsletters.ft.com",
    "ocado.com",
    "onthemarket-mail.com",
    "publisher-news.com",
    "qq.com",
    "r.grouponmail.co.uk",
    "ragingbullinsider.com",
    "rightmove.com",
    "slack.com",
    "steel-eye.com",
    "thetradenews.com",
    "twitter.com",
    "washingtonpost.com",
    "yahoo.com",
    "zoom.us",
    "magicbricks.com",
    "wordsmith.org",
    "shoptalk.com",
    "mail.bloombergview.com",
    "t.deliveroo.com",
    "deliveroo.com",
]
local_parts = [
    "qlik",
    "abercrombie",
    "account",
    "admin",
    "administrator",
    "alert",
    "alerts",
    "alerts-noreply",
    "amazon-offers",
    "announcements",
    "app",
    "app-notification",
    "asp.notifications",
    "audit",
    "autoresponder",
    "azure-noreply",
    "booking",
    "broadcast",
    "bulletins",
    "calendar-notification",
    "citivelocitymarketing",
    "clientes",
    "comms",
    "comunicacao",
    "conferences",
    "contact",
    "contact",
    "content",
    "customer",
    "customercare",
    "customerservice",
    "customerservices",
    "cv",
    "daily",
    "dailynews",
    "datalicense",
    "do.not.respond",
    "do_not_reply",
    "donotreply",
    "do-not-reply",
    "donotreply-eu",
    "e-alerts",
    "ebay",
    "editor",
    "editorial",
    "editors",
    "education",
    "email",
    "email",
    "email.campaign",
    "enquiries",
    "events",
    "feedback",
    "feedsglobalsupport",
    "firstword",
    "ft",
    "googlealerts-noreply",
    "gov.uk.email",
    "gtrcommunications",
    "hello",
    "help",
    "helpdesk",
    "hermes.reply",
    "hfmglobal",
    "hi",
    "hit-reply",
    "ice",
    "info",
    "info",
    "information",
    "invitations",
    "iressalert",
    "it.helpdesk",
    "italert",
    "job-apps",
    "maccount",
    "mail",
    "mailer-daemon",
    "mailrobot",
    "marketing",
    "messages-noreply",
    "messaging-digest-noreply",
    "miningjournal",
    "monitor",
    "msonlineservicesteam",
    "myft",
    "newaccounts.uk",
    "news",
    "news-alt",
    "newsdesk",
    "newsletter",
    "newsletters",
    "nlrt",
    "no.reply",
    "no_reply",
    "noreply",
    "no-reply",
    "noreply.notifications",
    "noreply_conferme.etd",
    "notification",
    "notifications",
    "notifications-noreply",
    "notifier",
    "notify",
    "nytdirect",
    "nytimes",
    "o365mc",
    "offers",
    "ongoingmonitoring",
    "post",
    "postmaster",
    "postmaster",
    "publications_team",
    "qlikwebinars",
    "quarantine",
    "reception",
    "recommendations",
    "reply",
    "resellers",
    "sales",
    "salesdesk",
    "service",
    "solutions",
    "sqlalert",
    "subscription",
    "subscriptions",
    "support",
    "support.secondline",
    "supportcase",
    "support-reply",
    "system",
    "tech",
    "todaysnews",
    "unavista_donotreply",
    "unavistahelp",
    "update",
    "update",
    "updates",
    "website",
    "jira",
    "bbalert",
    "bbrief",
    "bialert",
    "daybreak1",
]
