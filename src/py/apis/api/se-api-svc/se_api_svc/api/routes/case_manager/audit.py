# type: ignore
from api_sdk.auth import Tenancy
from api_sdk.di.request import ReqDep
from api_sdk.models.permissions import Permission
from api_sdk.models.request_params import NonPaginatedDatedListParams, PaginatedDatedListParams
from api_sdk.models.search import SearchResult
from api_sdk.utils.intervals import TimeInterval
from fastapi import APIRouter, Depends, Query
from se_api_svc.repository.audit import AuditRepository
from se_api_svc.repository.cases import CasesRepository
from se_api_svc.schemas.track import ModuleTitle
from typing import Optional

router = APIRouter()


@router.get("/{identifier}", name="cases:audit:get-case-user-audits")
async def get_user_audits_for_case(
    identifier: str,
    params: PaginatedDatedListParams = Depends(),
    case_repo: CasesRepository = ReqDep(CasesRepository),
    repo: AuditRepository = ReqDep(AuditRepository),
    tenancy: Tenancy = ReqDep(Tenancy),
):
    tenancy.require_permissions(Permission.CASE_MANAGER)

    case = await case_repo.get_case_by_identifier(identifier)

    result = await repo.get_audits(
        record_id=case.id_,
        module=ModuleTitle.CASE_MANAGER,
        **params.as_search_kwargs(),
    )
    return SearchResult.from_raw_result(result, skipped_hits=params.page_params.skip)


@router.get("/{identifier}/by-time")
async def get_user_audits_timeline_for_case(
    identifier: str,
    params: NonPaginatedDatedListParams = Depends(),
    interval: Optional[TimeInterval] = None,
    buckets: Optional[int] = Query(None, ge=1, le=500),
    case_repo: CasesRepository = ReqDep(CasesRepository),
    repo: AuditRepository = ReqDep(AuditRepository),
    tenancy: Tenancy = ReqDep(Tenancy),
):
    tenancy.require_permissions(Permission.CASE_MANAGER)

    case = await case_repo.get_case_by_identifier(identifier)

    return await repo.get_audits_timeline(
        record_id=case.id_,
        module=ModuleTitle.CASE_MANAGER,
        interval=interval,
        buckets=buckets,
        **params.as_search_kwargs(),
    )
