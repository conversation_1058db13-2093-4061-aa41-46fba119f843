# type: ignore
import logging
from api_sdk.config_base import ApiConfig
from api_sdk.di.request import ReqDep
from api_sdk.messages.fastapi_message_bus import FastApiMessageBus
from api_sdk.repository.asyncronous.request_bound import TenantConfiguration
from api_sdk.utils.utils import nested_get
from fastapi import APIRouter, Depends, HTTPException, Query, Request
from se_api_svc.core.config import ApiServiceConfig
from se_api_svc.messages.comms.events import CommAnalysed
from se_api_svc.repository.tenancy import TenantConfigurationRepository
from se_api_svc.schemas.ai import Chat<PERSON><PERSON>romptResponse, ChatGPTResponse
from se_api_svc.services.cache import RedisCacheService
from se_api_svc.services.chat_gpt import (
    BadRequestException,
    ChatGPTChatCompletionService,
    InsufficientQuotaException,
    TooManyTokensException,
)
from se_api_svc.services.comms import CommsService
from se_api_svc.static.prompts import (
    PROMPT_TEMPLATE_VERSION,
    build_cache_key,
    generate_chat_prompts,
    generate_legacy_prompts,
)
from typing import Dict, List, Optional, Union

router = APIRouter()

log = logging.getLogger(__name__)


class CommsRecord:
    def __init__(self, record: Optional[Dict]):
        self.record = record

    @staticmethod
    def _list_to_string(input_list: Optional[Union[str, List[str]]]) -> str:
        if input_list is None:
            return ""

        if not isinstance(input_list, list):
            input_list = [input_list]

        return ", ".join(input_list)

    @staticmethod
    def get_participant_names(record: Optional[Dict], participant_type: str) -> Optional[List[str]]:
        if record is None:
            return None

        return (
            [
                nested_get(part, "value.name")
                for part in record.get("participants", [])
                if participant_type in part.get("types")
            ]
            if record.get("participants")
            else None
        )

    def _get_participant_names(self, participant_type: str) -> Optional[List[str]]:
        return self.get_participant_names(self.record, participant_type)

    @property
    def from_names(self) -> Optional[List[str]]:
        return self._get_participant_names("FROM")

    @property
    def to_names(self) -> Optional[List[str]]:
        return self._get_participant_names("TO")

    @property
    def comms_type(self) -> Optional[str]:
        return self.record.get("&model") if self.record else None

    @property
    def id(self) -> Optional[str]:
        return self.record.get("&id") if self.record else None

    def _generate_call_chat_gpt_payload(self) -> str:
        template = "From: {fromId} ({fromName}), To: {toIds} ({toNames}) at {at}. Body: {body}"
        template_args = {
            "fromId": self._list_to_string(nested_get(self.record, "identifiers.fromId")),
            "fromName": self._list_to_string(self.from_names),
            "toIds": self._list_to_string(nested_get(self.record, "identifiers.toIds")),
            "toNames": self._list_to_string(self.to_names),
            "at": nested_get(self.record, "timestamps.timestampStart"),
            "body": nested_get(self.record, "body.text"),
        }
        return template.format(**template_args)

    def _generate_email_chat_gpt_payload(self) -> str:
        template = "From: {from}, To: {to} at {at}, Subject: {subject}. Body: {body}"
        template_args = {
            "from": nested_get(self.record, "identifiers.fromId"),
            "to": ", ".join(nested_get(self.record, "identifiers.toIds")),
            "at": nested_get(self.record, "timestamps.timestampStart"),
            "subject": nested_get(self.record, "subject"),
            "body": nested_get(self.record, "body.text"),
        }
        return template.format(**template_args)

    def generate_chat_gpt_prompts(self) -> Optional[List[Dict]]:
        if self.comms_type == "Email":
            payload = self._generate_email_chat_gpt_payload()
            return generate_chat_prompts(payload=payload)
        elif self.comms_type == "Call":
            payload = self._generate_call_chat_gpt_payload()
            return generate_chat_prompts(payload=payload)
        return None

    def generate_legacy_chat_gpt_prompts(self) -> Optional[Dict[str, str]]:
        if self.comms_type == "Email":
            payload = self._generate_email_chat_gpt_payload()
            return generate_legacy_prompts(type_="email", payload=payload)
        elif self.comms_type == "Call":
            payload = self._generate_call_chat_gpt_payload()
            return generate_legacy_prompts(type_="call transcript", payload=payload)
        return None


class ChatRoomRecords:
    def __init__(self, messages_around: List[Dict]):
        self._messages_around = messages_around

    @staticmethod
    def _generate_chat_message_payload(message: Dict) -> str:
        template = "{fromId} {at} {body}"
        return template.format(
            **{
                "fromId": nested_get(message, "identifiers.fromId"),
                "at": nested_get(message, "timestamps.timestampStart"),
                "body": nested_get(message, "body.text"),
            }
        )

    def generate_chat_room_prompts(self) -> Optional[List[Dict]]:
        chat_message_payloads = [
            self._generate_chat_message_payload(message) for message in self._messages_around
        ]
        payload = "\n".join(chat_message_payloads)
        return generate_chat_prompts(payload=payload)

    def generate_legacy_chat_room_prompts(self):
        chat_message_payloads = [
            self._generate_chat_message_payload(message) for message in self._messages_around
        ]
        payload = "\n".join(chat_message_payloads)
        return generate_legacy_prompts(type_="chat transcript", payload=payload)


@router.get("/analysis/{id}", response_model=ChatGPTResponse)
async def get_comms_record_analysis(
    id: str,
    request: Request,
    bust: bool = Query(
        default=False,
        description="Set to true to 'bust' the cached response "
        "(i.e. force a fresh response to be generated)",
    ),
    cache_service: RedisCacheService = ReqDep(RedisCacheService),
    comms_service: CommsService = ReqDep(CommsService),
    chat_gpt_service: ChatGPTChatCompletionService = ReqDep(ChatGPTChatCompletionService),
    tenant_config_repo: TenantConfigurationRepository = ReqDep(TenantConfigurationRepository),
    config: ApiServiceConfig = ReqDep(ApiConfig),
    mb: FastApiMessageBus = Depends(),
) -> Optional[ChatGPTResponse]:
    tenant_config: TenantConfiguration = await tenant_config_repo.get_tenant_configuration()

    if tenant_config.featureFlags and config.AI_ANALYSIS_FEATURE not in tenant_config.featureFlags:
        raise HTTPException(status_code=403, detail="You are not authorized to use this feature.")

    cache_key = build_cache_key(id_=id, tenant=comms_service.repo.tenant)

    if cache_service.cache_available:
        cache_hit = await cache_service.get(cache_key)
        if cache_hit and not bust:
            log.info(f"Cache hit for {id}")
            cache_hit["fromCache"] = True
            return cache_hit

    record = comms_service.get_comms_by_id(id)

    if record is None:
        raise HTTPException(
            status_code=404,
            detail=f"Communication record with id '{id}' not found",
        )

    comms_record = CommsRecord(record)

    if comms_record.comms_type == "Message":
        messages_around = comms_service.get_messages_around(record)
        chat_records = ChatRoomRecords(messages_around)
        prompts = chat_records.generate_chat_room_prompts()
    else:
        prompts = comms_record.generate_chat_gpt_prompts()

    try:
        chat_gpt_responses = await chat_gpt_service.retrieve_keyed_chat_gpt_response(
            prompts=prompts, tenant=comms_service.repo.tenant
        )

        if chat_gpt_responses is None:
            return None

        response: ChatGPTResponse = ChatGPTResponse.construct(
            **{
                "cacheKey": cache_key,
                "commsRecord": comms_record.record,
                "chatGPTResponses": {
                    gpt_responses.prompt_key: ChatGPTPromptResponse(
                        **{"prompt": gpt_responses.prompt, "response": gpt_responses.response}
                    )
                    for gpt_responses in chat_gpt_responses
                },
                "promptTemplateVersion": PROMPT_TEMPLATE_VERSION,
            }
        )

        if cache_service.cache_available:
            await cache_service.set(key=cache_key, value=response.dict())

        await mb.publish(CommAnalysed(communication_record=comms_record, request=request))
        return response

    except InsufficientQuotaException as e:
        log.error("Quota reached.", e)
        raise HTTPException(
            status_code=500,
            detail="Capacity reached; <NAME_EMAIL>.",
        ) from e
    except TooManyTokensException as e:
        log.error("Too many tokens.", e)
        raise HTTPException(
            status_code=413,
            detail="The comms record is too large to analyze at this point in time.",
        ) from e
    except BadRequestException as e:
        log.error("Invalid request.", e)
        raise HTTPException(
            status_code=400,
            detail=f"There is an error while attempting to process this {comms_record.comms_type}. Please contact our support team for further assistance.",  # noqa:E501
        ) from e
    except Exception as e:
        log.error("An unexpected error occurred while making OpenAI request.", e)
        raise HTTPException(
            status_code=500,
            detail="An unexpected error occurred while making OpenAI request.",
        ) from e
