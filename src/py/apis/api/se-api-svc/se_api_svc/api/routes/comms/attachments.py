import base64
import binascii
import io
import logging
from api_sdk.di.request import ReqDep
from api_sdk.exceptions import NotFound
from api_sdk.models.request_params import PaginatedListParams
from api_sdk.utils.utils import b64decode_urlsafe_id
from fastapi import APIRouter, Depends, Query
from se_api_svc.repository.comms.common import CommsRepository
from se_api_svc.schemas.attachment import Attachment
from se_api_svc.services.attachments.attachments import (
    AbstractAttachmentsService,
    MissingAttachmentError,
)
from starlette.responses import StreamingResponse

log = logging.getLogger(__name__)

router = APIRouter()


def _fetch_content_type(attachment) -> str:
    file_name = attachment.fileName
    if attachment.mimeTag:
        return attachment.mimeTag
    elif file_name.endswith(".mp3"):
        return "audio/mpeg"
    elif file_name.endswith(".wav"):
        return "audio/wav"
    elif file_name.endswith(".json"):
        return "application/json"

    return "application/octet-stream"


def _retrieve_attachment(
    service: AbstractAttachmentsService, attachment: Attachment, base_64: bool = True
) -> StreamingResponse:
    # TODO This IS loading the entire file in memory!
    # TODO Need to find a solution to read the stream and base64-decode it.

    if not base_64:
        return _stream_binary_response(service=AbstractAttachmentsService, attachment=attachment)

    try:
        stream = service.get_attachment_content(attachment=attachment)

        return StreamingResponse(
            io.BytesIO(base64.decodebytes(stream.read())),
            media_type=attachment.fileType,
            headers={
                "Content-Disposition": f'attachment; filename="{attachment.fileName}"',
            },
        )
    except binascii.Error:
        # Should really be able to identify the attachments that are NOT base-64 multiline encoded
        # and are therefore to be served more efficiently
        # separately, rather than relay on try...except

        # If, for some reason, base_64 was False, but the stream could not be decoded
        # try sending it as binary
        return _stream_binary_response(service=service, attachment=attachment)


def _stream_binary_response(service: AbstractAttachmentsService, attachment) -> StreamingResponse:
    # Unfortunately we have to fetch the file again, because once you use stream.read(), you
    # can NOT read the bytes again. This is pretty inefficient :-(

    stream = service.get_attachment_content(attachment)

    # noinspection PyBroadException
    try:
        return StreamingResponse(
            io.BytesIO(stream.read()),
            media_type=_fetch_content_type(attachment),
            headers={
                "Content-Disposition": f'attachment; filename="{attachment.fileName}"',
            },
        )
    except Exception as e:
        log.warning(
            f"Failed to base-64 decode attachment "
            f"{attachment.id_} {attachment.fileInfo} with error: {e}."
            "Will try serving as-is..."
        )
        # last-ditch effort, try sending the boto3 StreamBody _raw_stream :shrug:
        return StreamingResponse(
            stream._raw_stream,
            media_type=_fetch_content_type(attachment),
            headers={
                "Content-Disposition": f'attachment; filename="{attachment.fileName}"',
            },
        )


@router.get(
    path="/data",
    response_model=dict,
    name="comms:attachments:get-aggregated-attachment-info",
    summary="Returns aggregated attachment information for all Communications.",
    description="Returns aggregated attachment information for all Communications.",
)
async def get_comms_attachments_data(
    params: PaginatedListParams = Depends(), repo: CommsRepository = ReqDep(CommsRepository)
):
    return await repo.get_attachment_data(**params.as_search_kwargs())


@router.get("/{encoded_attachment_id}", name="comms:attachments:get-one")
def get_one_attachment(
    encoded_attachment_id: str,
    service: AbstractAttachmentsService = ReqDep(AbstractAttachmentsService),
):
    attachment_id = b64decode_urlsafe_id(encoded_attachment_id, fallback=True)
    try:
        return service.get_attachment(attachment_id)
    except MissingAttachmentError as e:
        log.error(f"Attachment {encoded_attachment_id}({attachment_id}) is missing or invalid")
        raise NotFound from e


@router.get("/{encoded_attachment_id}/fetch", name="comms:attachments:fetch")
async def fetch_one_attachment(
    encoded_attachment_id: str,
    service: AbstractAttachmentsService = ReqDep(AbstractAttachmentsService),
    base_64: bool = Query(default=True, alias="base64"),
):
    """Fetch a single comms attachment.

    This cannot be used to fetch other kinds of attachments. If the
    attachment file cannot be found in the storage, a 404 will be
    returned.
    """

    attachment_id = b64decode_urlsafe_id(encoded_attachment_id, fallback=True)

    try:
        attachment = await service.get_attachment(attachment_id)
        return _retrieve_attachment(service, attachment, base_64)
    except MissingAttachmentError as e:
        log.error(f"Attachment {encoded_attachment_id}({attachment_id}) is missing or invalid")
        raise NotFound("Attachment", encoded_attachment_id) from e
