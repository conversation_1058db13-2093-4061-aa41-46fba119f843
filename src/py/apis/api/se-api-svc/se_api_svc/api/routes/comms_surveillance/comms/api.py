# type: ignore
import logging
from api_sdk.di.request import ReqDep
from fastapi import APIRouter, Query
from se_api_svc.api.routes.comms_surveillance.comms.search_terms import domains, local_parts
from se_api_svc.repository.comms_surveillance.noise_remove import (
    NoiseRemovalRepository,
    NoiseRequestTypes,
)

router = APIRouter()
log = logging.getLogger(__name__)


@router.get("/noise-removal-list", name="comms:noise-removal-list")
async def get_noise_summary(
    type: str,
    behaviour_id: str = Query(alias="behaviourId", default=None),
    repo: NoiseRemovalRepository = ReqDep(NoiseRemovalRepository),
):
    try:
        if type == NoiseRequestTypes.LOCAL_PARTS.value:
            repo.type = NoiseRequestTypes.LOCAL_PARTS
        params = {"behaviour_id": behaviour_id} if behaviour_id else {}
        result = await repo.get_noise_summary(domains=domains, local_parts=local_parts, **params)

        return result
    except Exception as e:
        log.error(f"An error occurred during noise remove endpoint: {e}")
