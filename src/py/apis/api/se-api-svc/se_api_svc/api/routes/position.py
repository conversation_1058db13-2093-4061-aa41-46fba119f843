import logging
from api_sdk.di.request import ReqDep
from api_sdk.models.permissions import Permission
from api_sdk.models.request_params import PaginatedDatedListParams
from api_sdk.models.search import SearchResult
from fastapi import APIRouter, Depends
from se_api_svc.repository.position import PositionRepository
from se_api_svc.utils.perms import require_mar_reports
from se_elastic_schema.static.surveillance import MarketAbuseReportType

log = logging.getLogger(__name__)
router = APIRouter()


@router.get("", name="position:get-postion")
@require_mar_reports(MarketAbuseReportType.INSIDER_TRADING_V3.value)
async def get_positions(
    params: PaginatedDatedListParams = Depends(),
    model_repo: PositionRepository = ReqDep(PositionRepository),
):
    model_repo.tenancy.require_permissions(
        Permission.TRADE_SURVEILLANCE_ADMIN | Permission.TRADE_SURVEILLANCE
    )

    result = await model_repo.get_positions(**params.as_search_kwargs())
    return SearchResult.from_raw_result(result, skipped_hits=params.page_params.skip)
