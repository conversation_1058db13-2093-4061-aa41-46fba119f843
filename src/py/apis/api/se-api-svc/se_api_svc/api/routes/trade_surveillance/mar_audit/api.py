# type: ignore
from fastapi import APIRouter
from se_api_svc.api.routes.trade_surveillance.mar_audit import (
    execution,
    step_audit,
    summary_by_type,
)

router = APIRouter()
router.include_router(execution.router, prefix="/execution", tags=["mar-audit/execution"])
router.include_router(step_audit.router, prefix="/steps", tags=["mar-audit/step-audit"])
router.include_router(
    summary_by_type.router, prefix="/summary-by-type", tags=["mar-audit/summary-by-type"]
)
