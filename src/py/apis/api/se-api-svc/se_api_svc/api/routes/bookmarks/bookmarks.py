# type: ignore
import logging
from api_sdk.database import Database
from api_sdk.di.request import ReqDep
from api_sdk.exceptions import PermissionsException, RolePermissionNotFound
from api_sdk.middleware.permission_checker import Permission<PERSON>hecker
from api_sdk.models.permissions import Permission
from api_sdk.models.request_params import PaginatedListParams
from api_sdk.models.search import SearchResult
from api_sdk.responses import OkResponse
from fastapi import APIRouter, Body, Depends, Request, status
from fastapi.responses import JSONResponse
from se_api_svc.core.route_permissions import (
    comms_save_search_permission,
    csurv_save_search_permission,
    orders_save_search_permission,
    tsurv_save_search_permission,
)
from se_api_svc.repository.bookmarks import BookmarksRepisotory
from se_api_svc.schemas.bookmarks import BookmarkFilterModels, BookmarkIn, BookmarkUpdateIn
from se_elastic_schema.static.reference import Module

router = APIRouter()

log = logging.getLogger(__name__)

# Backwards compatibility methods
# TODO: remove once the database is updated to records having their Models populated
MODEL_TO_PATH_MAPPING = {
    BookmarkFilterModels.COMMUNICATION_ALERT: ["/comms-surveillance/dashboard"],
    BookmarkFilterModels.COMMUNICATION_TYPES_ALL: [
        "/communications/dashboard",
        "/communications/all",
    ],
}


def check_if_path_available(model):
    if model in MODEL_TO_PATH_MAPPING.keys():
        return MODEL_TO_PATH_MAPPING[model]
    else:
        pass


bookmark_module_permission_map = {
    Module.COMMUNICATIONS: comms_save_search_permission,
    Module.ORDERS: orders_save_search_permission,
    Module.TRADE_SURVEILLANCE: tsurv_save_search_permission,
    Module.COMMS_SURVEILLANCE: csurv_save_search_permission,
}


@router.post("", name="platform:bookmarks:post-one")
async def save_bookmark(
    request: Request,
    body: BookmarkIn = Body(...),
    repo: BookmarksRepisotory = ReqDep(BookmarksRepisotory),
    db: Database = ReqDep(Database),
):
    # ToDo(sumeet): Move this to dependency
    required_origin_permission = bookmark_module_permission_map.get(body.module)
    if required_origin_permission:
        try:
            await PermissionChecker.validate_permissions(request, required_origin_permission, db)
        except (AttributeError, RolePermissionNotFound) as e:
            logging.error(e)
            return JSONResponse(
                content=getattr(e, "info", None) or {"msg": "could not fetch user id"},
                status_code=status.HTTP_403_FORBIDDEN,
            )
        except RolePermissionNotFound as e:
            logging.debug(e)
            return JSONResponse(
                content={
                    "msg": "permission not found",
                    "info": {
                        "userId": e.info.get("user_id"),
                        "requiredPermission": e.info.get("required_permission"),
                    },
                },
                status_code=status.HTTP_403_FORBIDDEN,
            )
        # This won't need approval, so we won't catch NeedApproval

    await repo.save_bookmark(bookmark_in=body)
    return OkResponse()


@router.get("/my-bookmarks/{filter_model}", name="platform:bookmarks:user-get-all")
async def get_my_bookmark(
    filter_model: str,
    repo: BookmarksRepisotory = ReqDep(BookmarksRepisotory),
    params: PaginatedListParams = Depends(),
):
    paths = check_if_path_available(filter_model)
    res = await repo.get_my_bookmark(
        filter_model=filter_model,
        paths=paths,
        default_sort=["created:desc"],
        **params.as_search_kwargs(as_model_qs=False),
    )
    return SearchResult.from_raw_result(res)


@router.get("/global/{filter_model}", name="platform:bookmarks:global-get-all")
async def get_global_bookmarks(
    filter_model: str,
    repo: BookmarksRepisotory = ReqDep(BookmarksRepisotory),
    params: PaginatedListParams = Depends(),
):
    paths = check_if_path_available(filter_model)
    res = await repo.get_global_bookmarks(
        filter_model=(filter_model),
        paths=paths,
        default_sort=["created:desc"],
        **params.as_search_kwargs(as_model_qs=False),
    )
    return SearchResult.from_raw_result(res)


@router.get("/my-favorites/{filter_model}", name="platform:bookmarks:favorites-get-all")
async def get_my_favorite_bookmarks(
    filter_model: str,
    repo: BookmarksRepisotory = ReqDep(BookmarksRepisotory),
    params: PaginatedListParams = Depends(),
):
    paths = check_if_path_available(filter_model)
    res = await repo.get_my_favorite_bookmarks(
        filter_model=(filter_model),
        paths=paths,
        default_sort=["created:desc"],
        **params.as_search_kwargs(as_model_qs=False),
    )
    return SearchResult.from_raw_result(res)


@router.delete("/{bookmark_id}", name="platform:bookmarks:delete-one")
async def delete_bookmark(
    bookmark_id: str, repo: BookmarksRepisotory = ReqDep(BookmarksRepisotory)
):
    is_admin = repo.tenancy.has_permissions(Permission.ADMIN)
    bookmark = await repo.get_bookmark(bookmark_id=bookmark_id)

    if bookmark.createdBy == repo.tenancy.userId:
        await repo.delete_bookmark(bookmark_id=bookmark_id)
        return OkResponse()
    elif is_admin:
        if bookmark.shared is True:
            bookmark.shared = False
            await repo.edit_bookmark(bookmark_id=bookmark_id, update=bookmark)
            return OkResponse()
        # this would not happen with the current logic, but its a fallback if somehow
        # other users private saved searches ever exposed, it cannot be deleted by someone else
        else:
            raise PermissionsException(permissions=[Permission.ADMIN])
    else:
        raise PermissionsException(permissions=[Permission.ADMIN])


@router.put("/favorite/{bookmark_id}", name="platform:bookmarks:favorite-one")
async def favorite_bookmark(
    bookmark_id: str,
    repo: BookmarksRepisotory = ReqDep(BookmarksRepisotory),
):
    await repo.favorite_bookmark(bookmark_id=bookmark_id)
    return OkResponse()


@router.put("/{bookmark_id}", name="platform:bookmarks:update-one")
async def edit_bookmark(
    bookmark_id: str,
    body: BookmarkUpdateIn = Body(...),
    repo: BookmarksRepisotory = ReqDep(BookmarksRepisotory),
):
    await repo.edit_bookmark(bookmark_id=bookmark_id, update=body)
    return OkResponse()
