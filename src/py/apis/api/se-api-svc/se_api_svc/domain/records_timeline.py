# type: ignore
import datetime as dt
from api_sdk.utils.utils import nested_dict_get as rget
from api_sdk.utils.utils import parse_datetime
from pydantic import BaseModel, Field
from se_api_svc.repository.cases import CaseRecordSummaryRepository, NoRecordsFoundError
from se_elastic_schema.static.mifid2 import BuySellIndicator
from typing import List, Optional


class TimeSpans:
    DAYS_PER_YEAR = 365
    DAYS_PER_MONTH = 30
    DAYS_PER_WEEK = 7


class Bucket(BaseModel):
    id: str
    model: str

    original_record_id: Optional[str] = Field(default=None, alias="originalRecordId")

    comm_start: Optional[dt.datetime] = Field(default=None, alias="commStart")
    order_time: Optional[dt.datetime] = Field(default=None, alias="orderTime")
    trading_time: Optional[dt.datetime] = Field(default=None, alias="tradingTime")

    sender: Optional[str] = Field(default=None)
    recipient: Optional[str] = Field(default=None)

    order_code: Optional[str] = Field(default=None, alias="orderCode")
    order_status: Optional[str] = Field(default=None, alias="orderStatus")

    instrument: Optional[str] = Field(default=None)
    buy_sell: Optional[BuySellIndicator] = Field(default=None, alias="buySellIndicator")

    traded_quantity: Optional[int] = Field(default=None, alias="tradedQuantity")

    @classmethod
    def from_timeline_bucket(cls, src):
        bkt = cls(id=src["&id"], model=src["&model"])

        if rget(src, "timestamps.orderSubmitted"):
            bkt.order_time = parse_datetime(src["timestamps"]["orderSubmitted"])
        if rget(src, "transactionDetails.tradingDateTime"):
            bkt.trading_time = parse_datetime(src["transactionDetails"]["tradingDateTime"])
        if rget(src, "timestamps.timestampStart"):
            bkt.comm_start = parse_datetime(src["timestamps"]["timestampStart"])
        if src.get("orderIdCode"):
            bkt.order_code = src["orderIdCode"]
        if rget(src, "instrumentDetails.instrument.instrumentFullName"):
            bkt.instrument = src["instrumentDetails"]["instrument"]["instrumentFullName"]
        if rget(src, "executionDetails.buySellIndicator"):
            bkt.buy_sell = src["executionDetails"]["buySellIndicator"]

        if rget(src, "transactionDetails.quantity"):
            bkt.traded_quantity = src["transactionDetails"]["quantity"]
        elif rget(src, "priceFormingData.tradedQuantity"):
            bkt.traded_quantity = src["priceFormingData"]["tradedQuantity"]

        if rget(src, "identifiers.fromId"):
            bkt.sender = src["identifiers"]["fromId"]
        if rget(src, "executionDetails.orderStatus"):
            bkt.order_status = src["executionDetails"]["orderStatus"]

        if src.get("originalRecordId"):
            bkt.original_record_id = src.get("originalRecordId")
        else:
            bkt.original_record_id = src["&id"]

        recipients = rget(src, "identifiers.toIds")
        if type(recipients) is list and len(recipients) > 0:  # noqa: E721
            bkt.recipient = recipients[0]

        return bkt


def _format_bucket(bucket):
    return {
        "datetime": bucket["key_as_string"],
        "count": bucket["doc_count"],
        "data": [
            Bucket.from_timeline_bucket(h["_source"]) for h in bucket["records"]["hits"]["hits"]
        ],
    }


async def get_records_timeline(
    repo: CaseRecordSummaryRepository, case_id, start, end, **kwargs
) -> List[Bucket]:
    """Retrieves the list of records ordered and bucketed by event date."""
    # Figure out the time span for the records

    if start is None or end is None:
        try:
            records_info = await repo.get_timeline_info(case_id)
        except NoRecordsFoundError:
            return []

        if start is None:
            start = records_info.min.replace(tzinfo=dt.timezone.utc)
        if end is None:
            end = records_info.max.replace(tzinfo=dt.timezone.utc)

    # Everything needs to be a datetime so date arithmetic can be performed safely
    if type(start) is dt.date:
        start = dt.datetime.combine(start, dt.datetime.min.time())
    if type(start) is dt.date:
        end = dt.datetime.combine(end, dt.datetime.min.time())

    span = end.replace(tzinfo=dt.timezone.utc) - start.replace(tzinfo=dt.timezone.utc)

    interval = "1h"
    if span.days > TimeSpans.DAYS_PER_YEAR * 10:
        # More than 10 years
        interval = "365d"
    elif span.days > TimeSpans.DAYS_PER_YEAR:
        # More than 1 year
        interval = "30d"
    elif span.days > TimeSpans.DAYS_PER_YEAR / 2:
        interval = "15d"
    elif span.days > TimeSpans.DAYS_PER_MONTH:
        # More than 1 month
        interval = "2d"
    elif span.days > TimeSpans.DAYS_PER_WEEK * 2:
        interval = "1d"
    elif span.days > TimeSpans.DAYS_PER_WEEK:
        interval = "6h"
    elif span.days > 1:
        interval = "2h"

    buckets = await repo.get_timeline(case_id, interval, start=start, end=end, **kwargs)
    return [_format_bucket(b) for b in buckets]
