from api_sdk.utils.utils import StringEnum
from enum import auto
from pydantic.fields import Field
from pydantic.main import BaseModel
from typing import List, Optional


class Error(BaseModel):
    column: Optional[str] = None
    msg: Optional[str] = None


class RowError(BaseModel):
    number: Optional[int] = None
    errors: Optional[List[Error]] = None


class RestrictedListUploadResponse(BaseModel):
    file_error: Optional[str] = Field(None, alias="fileError")
    row_errors: Optional[List[RowError]] = Field(None, alias="rowErrors")
    uploaded_key: Optional[str] = Field(None, alias="uploadedKey")


class FileExtension(StringEnum):  # type: ignore
    csv = auto()
    xlsx = auto()
