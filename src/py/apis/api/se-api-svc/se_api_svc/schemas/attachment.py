import datetime as dt
import pydantic
from api_sdk.schemas.base import APIModel, RecordModel
from typing import Dict, Optional


class FileInfo(APIModel):
    location: Optional[Dict]
    processed: Optional[dt.datetime]
    metadata: Optional[Dict]
    lastModified: Optional[dt.datetime]
    versionId: Optional[str]
    contentMD5: Optional[str]
    contentLength: Optional[int]


class Attachment(RecordModel):
    class Config:
        model_name = "Attachment"
        index_suffix = "attachment"

        extra = pydantic.Extra.allow

    fileName: Optional[str]
    sizeInBytes: Optional[int]
    fileType: Optional[str]
    fileInfo: Optional[FileInfo]
    indexed: Optional[Dict]
    content: Optional[str]  # base-64 encoded content
    mimeTag: Optional[str]


class S3Attachment(RecordModel):
    bucket: str
    key: str
