# type: ignore
import abc
import pydantic
from api_sdk.repository.asyncronous.request_bound import TenantConfiguration
from api_sdk.schemas.base import APIModel, Field, RecordModel
from api_sdk.utils.utils import StringEnum, nested_dict_get
from enum import auto
from pydantic import validator
from se_elastic_schema.static.reference import ImAccountType, ItuE164, PhoneLabel
from typing import ClassVar, Dict, Generator, List, Optional, Union


class Identifier(APIModel):
    STR_REGEX: ClassVar[str] = "^[^:]+:[^:]+$"

    id: Optional[str]
    label: Optional[str]

    @property
    def str(self):
        return f"{self.label}:{self.id}"

    @classmethod
    def from_str(cls, identifier_str: str):
        label, id = identifier_str.split(":", 1)
        return cls(label=label, id=id)


class PersonIdentifierType(StringEnum):
    TRADE_FILE_IDENTIFIER = auto()
    ORDER_FILE_IDENTIFIER = auto()
    EMAIL = auto()
    IM_ACCOUNT = auto()
    PHONE_NUMBER = auto()


class PersonIdentifierIn(APIModel):
    class Config:
        extra = pydantic.Extra.allow

    identifierType: PersonIdentifierType
    id: Optional[str] = None
    value: Optional[str] = None
    dialingCode: Optional[ItuE164] = None
    extension: Optional[str] = None
    label: Optional[str] = None
    number: Optional[str] = None


class PhoneNumberIn(APIModel):
    identifierType: PersonIdentifierType
    number: Optional[str] = None
    label: Optional[PhoneLabel] = None
    dialingCode: Optional[ItuE164] = None
    extension: Optional[str] = None


class EmailIn(APIModel):
    identifierType: PersonIdentifierType
    email: str


class ImAccountIn(APIModel):
    identifierType: PersonIdentifierType
    id: str
    label: ImAccountType


class SinkIdentifierIn(APIModel):
    identifierType: PersonIdentifierType
    id: str
    label: str


class PersonIdentifiersIn(APIModel):
    identifiers: List[Union[EmailIn, PhoneNumberIn, ImAccountIn, SinkIdentifierIn]]

    @validator("identifiers")
    def change_type(cls, identifiers):
        # this code is needed as ingested file in communication set `metadata.source.clien` value as
        # MS Teams Chat, which is not a valid ImAccountType now
        # data team is working to accept `MS Teams Chat` as valid option till that
        # this is just a work - around. More details can be found on EU-1732
        nw_identifiers = []
        for identity in identifiers:
            if isinstance(identity, SinkIdentifierIn) and identity.label == "MS Teams Chat":
                identity = ImAccountIn(
                    id=identity.id,
                    identifierType=PersonIdentifierType.IM_ACCOUNT,
                    label=ImAccountType.LIVE_CHAT,
                )
            nw_identifiers.append(identity)

        return nw_identifiers


class IdentifierIn(APIModel):
    identifier: str = Field(..., regex=Identifier.STR_REGEX)


class SinkIdentifiers(APIModel):
    tradeFileIdentifiers: Optional[List[Identifier]]
    orderFileIdentifiers: Optional[List[Identifier]]


class Identifiable(RecordModel, abc.ABC):
    """Mixin for RecordModel that declares the identifiers attributes so we can
    work with them through attribute access reliably."""

    class Config:
        save_hooks_require_tenant_configuration: bool = True

    sinkIdentifiers: Optional[SinkIdentifiers] = None
    uniqueIds: Optional[List[str]] = None

    def before_save_new(self, **kwargs):
        self.uniqueIds = self.uniqueProps_ = self.build_unique_ids(**kwargs)

    def before_save_existing(self, **kwargs):
        self.uniqueIds = self.uniqueProps_ = self.build_unique_ids(**kwargs)

    @abc.abstractmethod
    def unique_ids_paths(self, tenant_configuration: Optional[TenantConfiguration]) -> List[str]:
        return

    @abc.abstractmethod
    def identifiers_paths(self, tenant_configuration: Optional[TenantConfiguration]) -> List[str]:
        return

    @abc.abstractmethod
    def build_ids(self) -> None:
        return

    def build_unique_ids(
        self, tenant_configuration: Optional[TenantConfiguration], **kwargs
    ) -> Optional[List[str]]:
        self.build_ids()
        dct = self.dict()
        ids = [
            *collect_unique_ids(dct, self.unique_ids_paths(tenant_configuration)),
            *collect_identifiers(dct, self.identifiers_paths(tenant_configuration)),
        ] or None

        return list(set(ids)) if ids is not None else None


def collect_unique_ids(dct: Dict, keys: List, use_lower: bool = True) -> Generator[str, None, None]:
    for k in keys:
        # Do not use nested_get because wildcard keys won't work with non-dicts!
        v = nested_dict_get(dct, k)
        if v:
            if not isinstance(v, list):
                v = [v]
            for vv in v:
                if vv is None:
                    continue
                svv = str(vv).replace(" ", "")
                if svv:
                    yield svv.lower() if use_lower else svv


def collect_identifiers(dct: Dict, keys) -> Generator[str, None, None]:
    for k in keys:
        for identifier in nested_dict_get(dct, k) or ():
            if identifier and "label" in identifier and "id" in identifier:
                id = f"{identifier['label'] or ''}:{identifier['id'] or ''}".lower()
                if id == ":":
                    continue
                yield id
