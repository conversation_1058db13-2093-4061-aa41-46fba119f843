# type: ignore
from api_sdk.schemas.base import APIModel, Field
from se_api_svc.schemas.surveillance.notification_settings import NotificationSetting
from typing import List


class CreateNotificationSettingIn(APIModel):
    settingType: NotificationSetting.SettingType
    option: NotificationSetting.Option
    notificationEmails: List[str] = Field(default_factory=list)
    enabled: bool = Field(True)


class UpdateNotificationSettingIn(APIModel):
    notificationEmails: List[str] = Field(default_factory=list)
    enabled: bool = Field(True)
