from api_sdk.schemas.static import Module
from typing import Dict, Tuple

csurv_exeuction_audit_permissions = ("VIEW_EXECUTION_AUDIT", "COMMS_SURVEILLANCE", "AUDIT")
tsurv_exeuction_audit_permissions = ("VIEW_EXECUTION_AUDIT", "TRADE_SURVEILLANCE", "AUDIT")

case_manager_insights_reporting = (
    "INSIGHT_REPORT_ACCESS",
    "CASE_MANAGER",
    "REPORTING_AND_DOWNLOADS",
)
comms_insights_reporting = ("INSIGHT_REPORT_ACCESS", "COMMUNICATIONS", "REPORTING_AND_DOWNLOADS")
csurv_insights_reporting = (
    "INSIGHT_REPORT_ACCESS",
    "COMMS_SURVEILLANCE",
    "REPORTING_AND_DOWNLOADS",
)
orders_insights_reporting = ("INSIGHT_REPORT_ACCESS", "ORDERS", "REPORTING_AND_DOWNLOADS")
tsurv_insights_reporting = (
    "INSIGHT_REPORT_ACCESS",
    "TRADE_SURVEILLANCE",
    "REPORTING_AND_DOWNLOADS",
)
best_execution_insights_reporting = (
    "INSIGHT_REPORT_ACCESS",
    "BEST_EXECUTION",
    "REPORTING_AND_DOWNLOADS",
)

case_manager_insights_building = ("INSIGHT_BUILDING", "CASE_MANAGER", "REPORTING_AND_DOWNLOADS")
comms_insights_building = ("INSIGHT_BUILDING", "COMMUNICATIONS", "REPORTING_AND_DOWNLOADS")
csurv_insights_building = ("INSIGHT_BUILDING", "COMMS_SURVEILLANCE", "REPORTING_AND_DOWNLOADS")
orders_insights_building = ("INSIGHT_BUILDING", "ORDERS", "REPORTING_AND_DOWNLOADS")
tsurv_insights_building = ("INSIGHT_BUILDING", "TRADE_SURVEILLANCE", "REPORTING_AND_DOWNLOADS")
best_execution_insights_building = ("INSIGHT_BUILDING", "BEST_EXECUTION", "REPORTING_AND_DOWNLOADS")


# Constants
APPROVE_REJECT_PERMISSION = "APPROVE_PENDING_REQUESTS"
APPROVE_REJECT_SUBMODULE = "ROLES_AND_PERMISSIONS"
VIEW_ROLE_PERMISSION = "VIEW_ROLES_AND_PERMISSIONS"
VIEW_ROLE_PERMISSION_SUBMODULE = "ROLES_AND_PERMISSIONS"
EDIT_ROLES_AND_PERMISSIONS = "EDIT_ROLES_AND_PERMISSIONS"
EDIT_ROLES_AND_PERMISSIONS_SUBMODULE = "ROLES_AND_PERMISSIONS"
ASSIGN_ROLES = "ASSIGN_ROLES"
ASSIGN_DATA_ACCESS_POLICY = "ASSIGN_DATA_ACCESS_POLICY"
VIEW_DATA_ACCESS_POLICY = "VIEW_DATA_ACCESS_POLICY"
EDIT_DATA_ACCESS_POLICY = "EDIT_DATA_ACCESS_POLICY"
DATA_ACCESS_POLICY_SUBMODULE = "DATA_ACCESS_POLICY"

# Bookmark permissions
orders_save_search_permission = ("SAVE_SEARCH", "ORDERS", "SEARCH")
comms_save_search_permission = ("SAVE_SEARCH", "COMMUNICATIONS", "SEARCH")
csurv_save_search_permission = ("SAVE_SEARCH", "COMMS_SURVEILLANCE", "SEARCH")
tsurv_save_search_permission = ("SAVE_SEARCH", "TRADE_SURVEILLANCE", "SEARCH")


# Permissions used throughout app
comms_case_create_permission = ("CASE_CREATION", "COMMUNICATIONS", "CASE")
csurv_case_create_permission = ("CASE_CREATION", "COMMS_SURVEILLANCE", "ALERT")
orders_case_create_permission = ("CASE_CREATION", "ORDERS", "CASE")
tsurv_case_create_permission = ("CASE_CREATION", "TRADE_SURVEILLANCE", "ALERT")

view_roles_permission = {
    Module.CASE_MANAGER: (VIEW_ROLE_PERMISSION, "CASE_MANAGER", VIEW_ROLE_PERMISSION_SUBMODULE),
    Module.COMMUNICATIONS: (VIEW_ROLE_PERMISSION, "COMMUNICATIONS", VIEW_ROLE_PERMISSION_SUBMODULE),
    Module.COMMS_SURVEILLANCE: (
        VIEW_ROLE_PERMISSION,
        "COMMS_SURVEILLANCE",
        VIEW_ROLE_PERMISSION_SUBMODULE,
    ),
    Module.ORDERS: (VIEW_ROLE_PERMISSION, "ORDERS", VIEW_ROLE_PERMISSION_SUBMODULE),
    Module.TRADE_SURVEILLANCE: (
        VIEW_ROLE_PERMISSION,
        "TRADE_SURVEILLANCE",
        VIEW_ROLE_PERMISSION_SUBMODULE,
    ),
    Module.TRANSACTION_REPORTING: (
        VIEW_ROLE_PERMISSION,
        "TRANSACTION_REPORTING",
        VIEW_ROLE_PERMISSION_SUBMODULE,
    ),
}

edit_roles_permission = {
    Module.CASE_MANAGER: (
        EDIT_ROLES_AND_PERMISSIONS,
        "CASE_MANAGER",
        EDIT_ROLES_AND_PERMISSIONS_SUBMODULE,
    ),
    Module.COMMUNICATIONS: (
        EDIT_ROLES_AND_PERMISSIONS,
        "COMMUNICATIONS",
        EDIT_ROLES_AND_PERMISSIONS_SUBMODULE,
    ),
    Module.COMMS_SURVEILLANCE: (
        EDIT_ROLES_AND_PERMISSIONS,
        "COMMS_SURVEILLANCE",
        EDIT_ROLES_AND_PERMISSIONS_SUBMODULE,
    ),
    Module.ORDERS: (EDIT_ROLES_AND_PERMISSIONS, "ORDERS", EDIT_ROLES_AND_PERMISSIONS_SUBMODULE),
    Module.TRADE_SURVEILLANCE: (
        EDIT_ROLES_AND_PERMISSIONS,
        "TRADE_SURVEILLANCE",
        EDIT_ROLES_AND_PERMISSIONS_SUBMODULE,
    ),
    Module.TRANSACTION_REPORTING: (
        EDIT_ROLES_AND_PERMISSIONS,
        "TRANSACTION_REPORTING",
        EDIT_ROLES_AND_PERMISSIONS_SUBMODULE,
    ),
}
assign_roles_permission = {
    Module.CASE_MANAGER: (ASSIGN_ROLES, "CASE_MANAGER", EDIT_ROLES_AND_PERMISSIONS_SUBMODULE),
    Module.COMMUNICATIONS: (ASSIGN_ROLES, "COMMUNICATIONS", EDIT_ROLES_AND_PERMISSIONS_SUBMODULE),
    Module.COMMS_SURVEILLANCE: (
        ASSIGN_ROLES,
        "COMMS_SURVEILLANCE",
        EDIT_ROLES_AND_PERMISSIONS_SUBMODULE,
    ),
    Module.ORDERS: (ASSIGN_ROLES, "ORDERS", EDIT_ROLES_AND_PERMISSIONS_SUBMODULE),
    Module.TRADE_SURVEILLANCE: (
        ASSIGN_ROLES,
        "TRADE_SURVEILLANCE",
        EDIT_ROLES_AND_PERMISSIONS_SUBMODULE,
    ),
    Module.TRANSACTION_REPORTING: (
        ASSIGN_ROLES,
        "TRANSACTION_REPORTING",
        EDIT_ROLES_AND_PERMISSIONS_SUBMODULE,
    ),
}

view_workflow_permission = {
    Module.COMMS_SURVEILLANCE: (
        "VIEW_WORKFLOW",
        "COMMS_SURVEILLANCE",
        "WORKFLOW",
    ),
    Module.TRADE_SURVEILLANCE: (
        "VIEW_WORKFLOW",
        "TRADE_SURVEILLANCE",
        "WORKFLOW",
    ),
}

edit_workflow_permission = {
    Module.COMMS_SURVEILLANCE: (
        "EDIT_WORKFLOW",
        "COMMS_SURVEILLANCE",
        "WORKFLOW",
    ),
    Module.TRADE_SURVEILLANCE: (
        "EDIT_WORKFLOW",
        "TRADE_SURVEILLANCE",
        "WORKFLOW",
    ),
}

assign_workflow_permission = {
    Module.COMMS_SURVEILLANCE: (
        "ASSIGN_WORKFLOW",
        "COMMS_SURVEILLANCE",
        "WORKFLOW",
    ),
    Module.TRADE_SURVEILLANCE: (
        "ASSIGN_WORKFLOW",
        "TRADE_SURVEILLANCE",
        "WORKFLOW",
    ),
}


csurv_watch_create_permission = (
    "WATCH_CREATION",
    "COMMS_SURVEILLANCE",
    "WATCH_AND_SCHEDULE",
)
csurv_watch_schedule_create_permission = (
    "SCHEDULE_CREATION",
    "COMMS_SURVEILLANCE",
    "WATCH_AND_SCHEDULE",
)

comms_watch_update_without_schedule_permission = (
    "WATCH_EDIT",
    "COMMS_SURVEILLANCE",
    "WATCH_AND_SCHEDULE",
)
comms_watch_update_with_schedule_permission = (
    "SCHEDULE_EDIT",
    "COMMS_SURVEILLANCE",
    "WATCH_AND_SCHEDULE",
)
tsurv_watch_create_permission = (
    "WATCH_CREATION",
    "TRADE_SURVEILLANCE",
    "WATCH_AND_SCHEDULE",
)
tsurv_watch_backtest_permission = (
    "BACKTEST",
    "TRADE_SURVEILLANCE",
    "WATCH_AND_SCHEDULE",
)
tsurv_watch_schedule_permission = (
    "SCHEDULE_CREATION",
    "TRADE_SURVEILLANCE",
    "WATCH_AND_SCHEDULE",
)
tsurv_watch_update_without_schedule_permission = (
    "WATCH_EDIT",
    "TRADE_SURVEILLANCE",
    "WATCH_AND_SCHEDULE",
)
tsurv_watch_update_with_schedule_permission = (
    "SCHEDULE_EDIT",
    "TRADE_SURVEILLANCE",
    "WATCH_AND_SCHEDULE",
)


# Permission config
route_permissions: Dict[str, Tuple[str, str, str]] = {
    # Case manager
    "case_manager_audits": ("VIEW_USER_AUDIT", "CASE_MANAGER", "AUDIT"),
    "case_manager_audits_timeline": ("VIEW_USER_AUDIT", "CASE_MANAGER", "AUDIT"),
    "add_case_comment": ("COMMENT", "CASE_MANAGER", "WORKFLOW"),
    "update_legal_hold": ("LEGAL_HOLD", "CASE_MANAGER", "WORKFLOW"),
    "attach_case_records": ("WORK_ON", "CASE_MANAGER", "WORKFLOW"),
    "update_case": ("EDIT", "CASE_MANAGER", "WORKFLOW"),
    "reopen_case": ("CASE_WORKFLOW", "CASE_MANAGER", "WORKFLOW"),
    "resolve_case": ("CASE_WORKFLOW", "CASE_MANAGER", "WORKFLOW"),
    # Comms
    "comms_audits": ("VIEW_USER_AUDIT", "COMMUNICATIONS", "AUDIT"),
    # CSurv
    "csurv_audits": ("VIEW_USER_AUDIT", "COMMS_SURVEILLANCE", "AUDIT"),
    # CSurv.Lexica
    "create_term": ("CREATION", "COMMS_SURVEILLANCE", "LEXICA"),
    "comms_watch_backtest": ("CUSTOM_BACKTEST", "COMMS_SURVEILLANCE", "LEXICA"),
    "delete_se_lexica": ("DELETION_STEELEYE_LEXICA", "COMMS_SURVEILLANCE", "LEXICA"),
    "delete_custom_lexica": ("DELETION_CUSTOM_LEXICA", "COMMS_SURVEILLANCE", "LEXICA"),
    "bulk_delete_se_lexica": ("DELETION_STEELEYE_LEXICA", "COMMS_SURVEILLANCE", "LEXICA"),
    "bulk_delete_custom_lexica": ("DELETION_CUSTOM_LEXICA", "COMMS_SURVEILLANCE", "LEXICA"),
    "update_se_lexica": ("EDIT_STEELEYE_LEXICA", "COMMS_SURVEILLANCE", "LEXICA"),
    "update_custom_lexica": ("EDIT_CUSTOM_LEXICA", "COMMS_SURVEILLANCE", "LEXICA"),
    "lexica_backtest": ("CUSTOM_BACKTEST", "COMMS_SURVEILLANCE", "LEXICA"),
    "post_apply_upgrades": ("UPGRADE", "COMMS_SURVEILLANCE", "LEXICA"),
    "upload_lexica_file": ("UPLOAD", "COMMS_SURVEILLANCE", "LEXICA"),
    "get_se_behaviours": ("VIEW_STEELEYE_LEXICA", "COMMS_SURVEILLANCE", "LEXICA"),
    "get_custom_behaviours": ("VIEW_CUSTOM_LEXICA", "COMMS_SURVEILLANCE", "LEXICA"),
    # CSurv.Workflow
    "create_user_alert_workflow": ("EDIT_WORKFLOW", "COMMS_SURVEILLANCE", "WORKFLOW"),
    "update_workflow_details": ("EDIT_WORKFLOW", "COMMS_SURVEILLANCE", "WORKFLOW"),
    # Tsurv
    "tsurv_audits": ("VIEW_USER_AUDIT", "TRADE_SURVEILLANCE", "AUDIT"),
    "get_execution_audit": ("VIEW_EXECUTION_AUDIT", "TRADE_SURVEILLANCE", "AUDIT"),
    "get_summary_by_type": ("VIEW_EXECUTION_AUDIT", "TRADE_SURVEILLANCE", "AUDIT"),
    "pause_trades_watch": tsurv_watch_update_with_schedule_permission,
    "activate_trades_watch": tsurv_watch_update_with_schedule_permission,
    "restore_trades_watch": tsurv_watch_update_without_schedule_permission,
    "archive_trades_watch": tsurv_watch_update_without_schedule_permission,
    # Tsurv.Workflow
    # "create_user_alert_workflow": ("EDIT_WORKFLOW", "TRADE_SURVEILLANCE", "WORKFLOW"),
    # "update_workflow_details": ("EDIT_WORKFLOW", "TRADE_SURVEILLANCE", "WORKFLOW"),
    # Tsurv.RL
    "get_restricted_lists": ("LIST_VIEW", "TRADE_SURVEILLANCE", "WATCH_AND_RL"),
    "get_restricted_list": ("LIST_VIEW", "TRADE_SURVEILLANCE", "WATCH_AND_RL"),
    "create_restricted_list": ("LIST_CREATION", "TRADE_SURVEILLANCE", "WATCH_AND_RL"),
    "ingest_restricted_list": ("LIST_CREATION", "TRADE_SURVEILLANCE", "WATCH_AND_RL"),
    "update_restricted_list": ("LIST_EDIT", "TRADE_SURVEILLANCE", "WATCH_AND_RL"),
    "delete_restricted_list": ("LIST_DELETE", "TRADE_SURVEILLANCE", "WATCH_AND_RL"),
    "create_restriction": ("CREATE", "TRADE_SURVEILLANCE", "WATCH_AND_RL"),
    "delete_restriction": ("DELETE", "TRADE_SURVEILLANCE", "WATCH_AND_RL"),
    "update_restriction": ("EDIT", "TRADE_SURVEILLANCE", "WATCH_AND_RL"),
    # TR
    "tr_audits": ("VIEW_USER_AUDIT", "TRANSACTION_REPORTING", "AUDIT"),
    "delete_transaction": ("ARCHIVE_TRANSACTION", "TRANSACTION_REPORTING", "WORKFLOW"),
    "update_transaction": ("EDIT_TRANSACTION", "TRANSACTION_REPORTING", "WORKFLOW"),
    "update_schedule": (
        "AUTOMATIC_SUBMISSION",
        "TRANSACTION_REPORTING",
        "SUBMISSIONS_AND_NOTIFICATIONS",
    ),
    "submit_report_for_action": (
        "SUBMIT_REPORT",
        "TRANSACTION_REPORTING",
        "SUBMISSIONS_AND_NOTIFICATIONS",
    ),
    # Approve Reject
    "case_manager_pending_requests": (
        APPROVE_REJECT_PERMISSION,
        "CASE_MANAGER",
        APPROVE_REJECT_SUBMODULE,
    ),
    "communications_surveillance_pending_requests": (
        APPROVE_REJECT_PERMISSION,
        "COMMS_SURVEILLANCE",
        APPROVE_REJECT_SUBMODULE,
    ),
    "trades_surveillance_pending_requests": (
        APPROVE_REJECT_PERMISSION,
        "TRADE_SURVEILLANCE",
        APPROVE_REJECT_SUBMODULE,
    ),
    "case_manager_pending_request_reject": (
        APPROVE_REJECT_PERMISSION,
        "CASE_MANAGER",
        APPROVE_REJECT_SUBMODULE,
    ),
    "communications_surveillance_pending_request_reject": (
        APPROVE_REJECT_PERMISSION,
        "COMMS_SURVEILLANCE",
        APPROVE_REJECT_SUBMODULE,
    ),
    "trades_surveillance_pending_request_reject": (
        APPROVE_REJECT_PERMISSION,
        "TRADE_SURVEILLANCE",
        APPROVE_REJECT_SUBMODULE,
    ),
    "pause_watch": tsurv_watch_update_with_schedule_permission,
    # Insights Report
    "get_case_manager_insights_report": case_manager_insights_reporting,
    "get_communications_insights_report": comms_insights_reporting,
    "get_comms_surveillance_insights_report": csurv_insights_reporting,
    "get_orders_insights_report": orders_insights_reporting,
    "get_trade_surveillance_insights_report": tsurv_insights_reporting,
    "get_best_execution_insights_report": best_execution_insights_reporting,
    # Create insgihts report
    "post_case_manager_insights_reports": case_manager_insights_reporting,
    "post_comms_surveillance_insights_reports": comms_insights_reporting,
    "post_communications_insights_reports": csurv_insights_reporting,
    "post_orders_insights_reports": orders_insights_reporting,
    "post_best_execution_insights_reports": tsurv_insights_reporting,
    "post_trade_surveillance_insights_reports": best_execution_insights_reporting,
    # Insights Report.Delete
    "delete_case_manager_insights_report": case_manager_insights_reporting,
    "delete_comms_surveillance_insights_report": comms_insights_reporting,
    "delete_communications_insights_report": csurv_insights_reporting,
    "delete_orders_insights_report": orders_insights_reporting,
    "delete_best_execution_insights_report": best_execution_insights_reporting,
    "delete_trade_surveillance_insights_report": tsurv_insights_reporting,
    # Insights Report.Edit
    "edit_case_manager_insights_report": case_manager_insights_reporting,
    "edit_comms_surveillance_insights_report": comms_insights_reporting,
    "edit_communications_insights_report": csurv_insights_reporting,
    "edit_orders_insights_report": orders_insights_reporting,
    "edit_best_execution_insights_report": best_execution_insights_reporting,
    "edit_trade_surveillance_insights_report": tsurv_insights_reporting,
    # Insights Report.Pause
    "pause_case_manager_insights_report": case_manager_insights_reporting,
    "pause_comms_surveillance_insights_report": comms_insights_reporting,
    "pause_communications_insights_report": csurv_insights_reporting,
    "pause_orders_insights_report": orders_insights_reporting,
    "pause_best_execution_insights_report": best_execution_insights_reporting,
    "pause_trade_surveillance_insights_report": tsurv_insights_reporting,
    # Insights Report.Resume
    "resume_case_manager_insights_report": case_manager_insights_building,
    "resume_comms_surveillance_insights_report": comms_insights_reporting,
    "resume_communications_insights_report": csurv_insights_reporting,
    "resume_orders_insights_report": orders_insights_reporting,
    "resume_best_execution_insights_report": best_execution_insights_reporting,
    "resume_trade_surveillance_insights_report": tsurv_insights_reporting,
    # Insights Report.Update Schedule
    "update_schedule_case_manager_insights_report": case_manager_insights_building,
    "update_schedule_comms_surveillance_insights_report": comms_insights_reporting,
    "update_schedule_communications_insights_report": csurv_insights_reporting,
    "update_schedule_orders_insights_report": orders_insights_reporting,
    "update_schedule_best_execution_insights_report": best_execution_insights_reporting,
    "update_schedule_trade_surveillance_insights_report": tsurv_insights_reporting,
}
