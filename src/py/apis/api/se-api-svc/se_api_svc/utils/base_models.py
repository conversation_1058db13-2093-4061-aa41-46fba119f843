# type: ignore
from front_running_v2_apply_strategy.models import Thresholds as FrontRunningV2Thresholds
from market_abuse_algorithms.strategy.abstract_marking_the_price.models import (
    Thresholds as MarkingThePriceThreshold,
)
from market_abuse_algorithms.strategy.forcing_the_market_against_its_will.models import (
    Thresholds as ForcingTheMarketAgainstItsWillThresholds,
)
from market_abuse_algorithms.strategy.front_running.models import (
    Thresholds as FrontRunningThresholds,
)
from market_abuse_algorithms.strategy.front_running_client_vs_client.models import (
    Thresholds as FrontRunningClientVsClientThresholds,
)
from market_abuse_algorithms.strategy.insider_trading_v2.models import (
    Thresholds as InsiderTradingV2Thresholds,
)
from market_abuse_algorithms.strategy.inter_trading_venue_manipulation.models import (
    Thresholds as InterTradingVenueManipulationThresholds,
)
from market_abuse_algorithms.strategy.layering.models import Thresholds as LayeringThresholds
from market_abuse_algorithms.strategy.message_volume.models import (
    Thresholds as MessageVolumeThresholds,
)
from market_abuse_algorithms.strategy.painting_the_tape.models import (
    Thresholds as PaintingTheTapeThresholds,
)
from market_abuse_algorithms.strategy.parking.models import Thresholds as ParkingThresholds
from market_abuse_algorithms.strategy.phishing.models import Thresholds as PhishingThresholds
from market_abuse_algorithms.strategy.phishing_v2.models import Thresholds as PhishingV2Thresholds
from market_abuse_algorithms.strategy.potam.models import Thresholds as PotamThresholds
from market_abuse_algorithms.strategy.price_outliers.models import (
    Thresholds as PriceOutliersThresholds,
)
from market_abuse_algorithms.strategy.pump_and_dump.models import (
    Thresholds as PumpAndDumpThresholds,
)
from market_abuse_algorithms.strategy.quote_stuffing.models import (
    Thresholds as QuoteStuffingThresholds,
)
from market_abuse_algorithms.strategy.spoofing.models import Thresholds as SpoofingThresholds
from market_abuse_algorithms.strategy.suspicious_large_order_volume.models import (
    Thresholds as SuspiciousLargeOrderVolumeThresholds,
)
from market_abuse_algorithms.strategy.suspicious_large_order_volume_v2.models import (
    Thresholds as SuspiciousLargeOrderVolumeV2Thresholds,
)
from market_abuse_algorithms.strategy.trash_and_cash.models import (
    Thresholds as TrashAndCashThresholds,
)
from marking_open_close_intraday_v2_apply_strategy.models import (
    Thresholds as MarkingOpenCloseIntradayV2Thresholds,
)
from se_elastic_schema.components.mar.strategy.insider_trading_v3.thresholds import (
    InsiderTradingV3Thresholds,
)
from se_elastic_schema.components.mar.strategy.insider_trading_v3.thresholds_refinitiv import (
    InsiderTradingV3RefinitivThresholds,
)
from se_elastic_schema.components.mar.strategy.painting_the_tape_v2.thresholds import (
    PaintingTheTapeV2Thresholds,
)
from se_elastic_schema.static.surveillance import MarketAbuseReportType
from wash_trading_apply_strategy.models import Thresholds as WashTradingThresholds

MARKET_ABUSE_REPORT_TYPE_THRESHOLD_MAP = {
    MarketAbuseReportType.LAYERING: LayeringThresholds,
    MarketAbuseReportType.FRONT_RUNNING_CLIENT_VS_CLIENT: FrontRunningClientVsClientThresholds,
    MarketAbuseReportType.FORCING_THE_MARKET_AGAINST_ITS_WILL: ForcingTheMarketAgainstItsWillThresholds,  # noqa: E501
    MarketAbuseReportType.FRONT_RUNNING: FrontRunningThresholds,
    MarketAbuseReportType.FRONT_RUNNING_V2: FrontRunningV2Thresholds,
    MarketAbuseReportType.INSIDER_TRADING_V2: InsiderTradingV2Thresholds,
    MarketAbuseReportType.WASH_TRADING: WashTradingThresholds,
    MarketAbuseReportType.TRASH_AND_CASH: TrashAndCashThresholds,
    MarketAbuseReportType.SUSPICIOUS_LARGE_ORDER_VOLUME_V2: SuspiciousLargeOrderVolumeV2Thresholds,
    MarketAbuseReportType.SUSPICIOUS_LARGE_ORDER_VOLUME: SuspiciousLargeOrderVolumeThresholds,
    MarketAbuseReportType.SPOOFING: SpoofingThresholds,
    MarketAbuseReportType.QUOTE_STUFFING: QuoteStuffingThresholds,
    MarketAbuseReportType.PUMP_AND_DUMP: PumpAndDumpThresholds,
    MarketAbuseReportType.POTAM: PotamThresholds,
    MarketAbuseReportType.PHISHING_V2: PhishingV2Thresholds,
    MarketAbuseReportType.PHISHING: PhishingThresholds,
    # MarketAbuseReportType.AbstractMarkingThePriceThresholds: AbstractMarkingThePriceThresholds,
    MarketAbuseReportType.PARKING: ParkingThresholds,
    MarketAbuseReportType.MESSAGE_VOLUME: MessageVolumeThresholds,
    MarketAbuseReportType.INTER_TRADING_VENUE_MANIPULATION: InterTradingVenueManipulationThresholds,
    MarketAbuseReportType.MARKING_OPEN_CLOSE_INTRADAY_V2: MarkingOpenCloseIntradayV2Thresholds,
    MarketAbuseReportType.PRICE_OUTLIERS: PriceOutliersThresholds,
    MarketAbuseReportType.PAINTING_THE_TAPE: PaintingTheTapeThresholds,
    MarketAbuseReportType.INSIDER_TRADING_V3: InsiderTradingV3Thresholds,
    MarketAbuseReportType.INSIDER_TRADING_V3_REFINITIV: InsiderTradingV3RefinitivThresholds,
    MarketAbuseReportType.MARKING_THE_CLOSE: MarkingThePriceThreshold,
    MarketAbuseReportType.MARKING_THE_OPEN: MarkingThePriceThreshold,
    MarketAbuseReportType.PAINTING_THE_TAPE_V2: PaintingTheTapeV2Thresholds,
}

MODEL_NAME_MAP = dict(**MARKET_ABUSE_REPORT_TYPE_THRESHOLD_MAP)
