# type: ignore
from api_sdk.messages.base import DomainEvent
from api_sdk.schemas.track import ModuleTitle
from api_sdk.utils.attributes import get_attr
from dataclasses import dataclass
from se_api_svc.messages.audit.events import (
    RecordCreationEvent,
    RecordDeletionEvent,
    RecordModificationEvent,
    RecordViewEvent,
    SearchEvent,
)
from typing import Any, List, NamedTuple, Optional


@dataclass
class RestrictedListManagementEvent(DomainEvent):
    audit_module = ModuleTitle.TRADE_SURVEILLANCE
    id_fields = ["restrictedListId", "restrictionId", "id"]

    record_obj: Optional[Any] = None

    @property
    def record(self):
        if isinstance(self.record_obj, list) and self.record_obj:
            return self.record_obj[0]

        return self.record_obj


@dataclass
class WatchEvent(DomainEvent):
    audit_module = ModuleTitle.TRADE_SURVEILLANCE

    restricted_list: Any = None

    @property
    def record(self):
        return self.restricted_list


class UserChange(NamedTuple):
    field: str
    model_name: str
    old_value: Any
    new_value: Any


@dataclass
class RestrictedListsSearched(RestrictedListManagementEvent, SearchEvent):
    pass


@dataclass
class RestrictedListViewed(RestrictedListManagementEvent, RecordViewEvent):
    audit_description = "User viewed a restricted list"


@dataclass
class RestrictedListCreated(RestrictedListManagementEvent, RecordCreationEvent):
    @property
    def audit_description(self):
        return f"User added a restrictedList '{get_attr(self.record_obj, 'id')}'"


@dataclass
class RestrictedListDeleted(RestrictedListManagementEvent, RecordDeletionEvent):
    @property
    def audit_description(self):
        return f"User deleted the restrictedList '{get_attr(self.record_obj, 'id')}'"


@dataclass
class RestrictedListUpdated(RestrictedListManagementEvent, RecordModificationEvent):
    changes: List[UserChange] = None
    event_description = "User updated {model_name}'s {field_id} from {old_value} to {new_value}"

    def generate_audit_records(self, **kwargs):
        for change in self.changes or []:
            yield self.new_audit_record(
                description=self.event_description.format(
                    field_id=change.field,
                    old_value=change.old_value,
                    new_value=change.new_value,
                    model_name=change.model_name,
                ),
            )


@dataclass
class RestrictedListWatchesSearched(RestrictedListManagementEvent, RecordCreationEvent):
    restricted_list_id: str = None

    @property
    def audit_description(self):
        return f"User viewed watches of a restricted list {self.restricted_list_id}"


@dataclass
class BacktestWatchCreated(WatchEvent, RecordCreationEvent):
    event_description = "User created a backtest watch for restricted list '{restricted_list_name}'"

    @property
    def audit_description(self):
        return self.event_description.format(
            restricted_list_name=get_attr(self.restricted_list, "name", "")
        )
