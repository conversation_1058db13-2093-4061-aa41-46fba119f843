from api_sdk.repository.asyncronous.request_bound import TenantConfiguration
from se_elastic_schema.models.tenant.surveillance.communication_alert import CommunicationAlert
from se_elastic_schema.models.tenant.surveillance.market_abuse_alert import MarketAbuseAlert
from se_elastic_schema.models.tenant.surveillance.market_abuse_scenario_tag import (
    MarketAbuseScenarioTag,
)
from se_elastic_schema.models.tenant.surveillance.order_alert import OrderAlert
from se_elastic_schema.static.reference import Module


def get_surveillance_record_models(tenant_config: TenantConfiguration):
    record_models = []
    if Module.COMMS_SURVEILLANCE in tenant_config.subscribedModules:
        record_models.append(CommunicationAlert)
    if Module.TRADE_SURVEILLANCE in tenant_config.subscribedModules:
        record_models.extend([Order<PERSON><PERSON>t, MarketAbuseAlert, MarketAbuseScenarioTag])

    return record_models
