# type: ignore
from api_sdk.messages.base import DomainEvent
from dataclasses import dataclass
from se_api_svc.messages.admin.events import AdminEvent
from se_api_svc.messages.audit.events import (
    AuditedEvent,
    RecordCreationEvent,
    RecordDeletionEvent,
    RecordDownloadEvent,
    RecordModificationEvent,
    RecordViewEvent,
    SearchEvent,
)
from se_api_svc.schemas.track import SubModuleTitle
from tenant_db.models.workflow.workflow_transitions import WorkflowTransition
from tenant_db.models.workflow.workflows import Workflow
from typing import List, Optional


@dataclass
class WorkflowAuditEvent(AdminEvent, AuditedEvent):
    audit_sub_module = SubModuleTitle.WORKFLOW


@dataclass
class WorkflowEvent(WorkflowAuditEvent, DomainEvent):
    workflow: Workflow = None
    field: Optional[str] = None
    value: Optional[str] = None
    old_transition: Optional[WorkflowTransition] = None
    new_transition: Optional[WorkflowTransition] = None

    @property
    def record(self):
        return self.workflow


@dataclass
class WorkflowDownloadEvent(WorkflowEvent, RecordDownloadEvent):
    audit_description = "The user exported the Excel file containing a matrix and information related to all workflows."  # noqa: E501

    def generate_audit_records(self, **kwargs):
        yield self.new_audit_record(description=self.audit_description)


@dataclass
class WorkflowViewEvent(WorkflowEvent, RecordViewEvent):
    audit_description = "User viewed Workflow - {name}"

    def generate_audit_records(self, **kwargs):
        yield self.new_audit_record(
            description=self.audit_description.format(name=self.workflow.workflowName)
        )


@dataclass
class WorkflowCreationEvent(WorkflowEvent, RecordCreationEvent):
    audit_description = "New workflow {name} created."

    def generate_audit_records(self, **kwargs):
        yield self.new_audit_record(
            description=self.audit_description.format(
                name=self.workflow.workflowName,
            ),
        )


@dataclass
class WorkflowAppliedEvent(WorkflowEvent, RecordModificationEvent):
    audit_description = "Workflow: {workflow_name} was applied to the users {users}"
    user_ids: List[str] = None

    def generate_audit_records(self, **kwargs):
        yield self.new_audit_record(
            description=self.audit_description.format(
                workflow_name=self.workflow.workflowName, users=self.user_ids
            )
        )


@dataclass
class WorkflowSearchEvent(WorkflowEvent, SearchEvent):
    audit_description = "User searched for '{search_text}' workflows"

    def generate_audit_records(self, **kwargs):
        yield self.new_audit_record(
            description=(
                self.audit_description.format(search_text=self.search_text)
                if self.search_text
                else "User viewed all workflows in the dashboard"
            )
        )


@dataclass
class WorkflowUpdateEvent(WorkflowEvent, RecordModificationEvent):
    audit_description = "User updated workflow {name}"
    audit_description_with_field_value = (
        "User updated {field} for {name} workflow with value: {value}"
    )

    def generate_audit_records(self, **kwargs):
        if self.field:
            yield self.new_audit_record(
                description=self.audit_description_with_field_value.format(
                    name=self.workflow.workflowName,
                    field=self.field,
                    value=self.value,
                )
            )
        else:
            yield self.new_audit_record(
                description=self.audit_description.format(id=self.workflow.id)
            )


class WorkflowTransitionUpdateEvent(WorkflowEvent, RecordModificationEvent):
    audit_description_with_field = "User updated {field} for {workflow_name} workflow's from {from_status}:{to_status} transition"  # noqa
    audit_description = (
        "User updated for {workflow_name} workflow's from {from_status}:{to_status} transition"
    )

    def generate_audit_records(self, **kwargs):
        if self.field:
            yield self.new_audit_record(
                description=self.audit_description_with_field.format(
                    field=self.field,
                    workflow_name=self.workflow.workflowName,
                    from_status=self.old_transition.fromStatus,
                    to_status=self.new_transition.toStatus,
                )
            )

        else:
            yield self.new_audit_record(
                description=self.audit_description.format(
                    workflow_name=self.workflow.workflowName,
                    from_status=self.old_transition.fromStatus,
                    to_status=self.new_transition.toStatus,
                )
            )


@dataclass
class WorkflowDeleteEvent(WorkflowEvent, RecordDeletionEvent):
    audit_description = "User deleted {name} workflow"

    def generate_audit_records(self, **kwargs):
        yield self.new_audit_record(
            description=self.audit_description.format(name=self.workflow.workflowName)
        )
