from email.mime.text import MIMEText
from generator.alert_assignee_email_generator import AlertAssigneeEmailGenerator
from generator.watch_alert_resolution_email_generator import WatchAlertResolutionEmailGenerator


def generate_watch_resolution_updated_message(
    realm: str,
    subject: str,
    header: str,
    watch_name: str,
    alert_number: int,
    resolution_state: str,
    module: str,
    watch_id: str,
) -> MIMEText:
    generator: WatchAlertResolutionEmailGenerator = WatchAlertResolutionEmailGenerator(realm=realm)

    return generator.generate_email(
        subject=subject,
        header=header,
        watch_name=watch_name,
        alert_number=alert_number,
        resolution_state=resolution_state,
        module=module,
        watch_id=watch_id,
    )


def generate_alert_assignee_message(
    subject: str,
    header: str,
    assigner: str,
    date: str,
    reason: str,
    alert_info: list,
    realm: str,
    assigner_comment: str,
) -> MIMEText:
    generator: AlertAssigneeEmailGenerator = AlertAssigneeEmailGenerator(realm=realm)

    return generator.generate_email(
        subject=subject,
        header=header,
        assigner=assigner,
        date=date,
        reason=reason,
        alert_info=alert_info,
        assigner_comment=assigner_comment,
    )
