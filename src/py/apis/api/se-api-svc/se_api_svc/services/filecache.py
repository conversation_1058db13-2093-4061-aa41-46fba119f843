# type: ignore
import hashlib
import json
import logging
import os
from aiofile import async_open  # pants: no-infer-dep
from async_lru import alru_cache  # pants: no-infer-dep
from json.decoder import <PERSON><PERSON><PERSON>ecodeError
from se_api_svc.core.config import ApiServiceConfig as config

log = logging.getLogger("filecache")


def cache_file(key):
    if isinstance(key, list):
        key = "_".join(key)
    return "se-api-svc_{}.cache".format(hashlib.md5(key.encode()).hexdigest())


class ExpiredCache(Exception):
    pass


class FileCache:
    """A super-simple and fully-async file-based cache without lifetime.

    This cache system DOES NOT implement a cache lifetime. Therefore
    all cached data will be valid forever. This is best used with a
    container-based system where containers will be re-created when
    a new version of the code is released (and therefore the cache
    purged).

    Requires the following config options:

    * ``FILECACHE_ENABLED``: whether the cache should actually operate
    * ``FILECACHE_PATH``: the path to the directory where the cache files
       are stored

    Note: the ``FILECACHE_PATH` folder will not be auto-created.

    Example usage::

        cache = FileCache()

        async def foo(param):
            if cache.has_cache([__name__, param]):
                return await cache.load_cache([__name__, param])
            result = 12345
            return await cache.write_cache([__name__, param], result)
    """

    def __init__(self):
        self.cache_path = config.FILECACHE_PATH.init_value

    @property
    def cache_enabled(self):
        if not config.FILECACHE_ENABLED.init_value:
            log.warning("Filecache Disabled")
            return False
        return True

    def has_cache(self, key):
        """Checks for the existence of the coumpound key in the cache store.

        :param key: is a string or list of strings making a unique cacheable key
        :return: True or False depending on the existence of the requested key in store
        """
        if not self.cache_enabled:
            return False
        return os.path.isfile(os.path.join(self.cache_path, cache_file(key)))

    async def write_cache(self, key, data):
        """Writes data into the cache store and associates it with the provided
        ``key``.

        :param key: str or list of str
        :param data: any data structure capable of being serialized to JSON.
        :return: ``data``

        This is designed to be used in a single line, example::

            return await cache.write_cache("key", "data")
        """
        if not self.cache_enabled:
            return data
        async with async_open(os.path.join(self.cache_path, cache_file(key)), "w") as cache_fh:
            await cache_fh.write(json.dumps(data))
            return data

    # Caching one Order schema takes 700KB, so aiming for roughly 100MB cache max.
    @alru_cache(maxsize=150)
    async def load_cache(self, key):
        """Loads and returns the cached data associated with ``key``.

        :param key: a str or list of str
        :return: the data cached under ``key``
        """
        if not self.cache_enabled:
            return None
        cache_path = os.path.join(self.cache_path, cache_file(key))
        try:
            async with async_open(cache_path, "r") as cache_fh:
                return json.loads(await cache_fh.read())
        except JSONDecodeError:
            # The cache is likely corrupted. Let's clean up the file.
            try:
                os.unlink(cache_path)
            except Exception:
                # Don't stop main code execution because of this.
                pass
            raise ExpiredCache()
        except Exception:
            raise ExpiredCache()
