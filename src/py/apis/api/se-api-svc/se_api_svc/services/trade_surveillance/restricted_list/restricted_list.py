# type: ignore
import logging
from api_sdk.di.request import ReqDep
from api_sdk.exceptions import NotFound
from collections import defaultdict
from fastapi import HTTPException
from se_api_svc.repository.trade_surveillance.restricted_list.attachments import (
    AttachmentRepository,
)
from se_api_svc.repository.trade_surveillance.restricted_list.restricted_list import (
    RestrictedListRepository,
    RestrictedListRepositoryEs,
)
from se_api_svc.schemas.trade_surveillance.restricted_list.common import AttachmentIn
from se_api_svc.schemas.trade_surveillance.restricted_list.restricted_list_schema import (
    OptionalRestrictedListIn,
    RestrictedListIn,
)
from se_api_svc.services.trade_surveillance.restricted_list.restricted_list_upload import (
    RestrictedListUploadService as RLUploadService,
)
from se_schema.static.surveillance import WatchStatusType
from tenant_db.models.restricted_list.restricted_list import RestrictionAttachment as Attachment
from uuid import UUID

log = logging.getLogger(__name__)


class RestrictedListService:
    def __init__(
        self,
        es_repository: RestrictedListRepositoryEs,
        attachment_repo: AttachmentRepository,
        repository: RestrictedListRepository = None,
        upload_service: RLUploadService = ReqDep(RLUploadService),
    ):
        self.upload_service = upload_service
        self.__repository = repository
        self.__attachment_repo = attachment_repo
        self.__es_repository = es_repository

    async def create_restricted_list(self, restricted_list_in: RestrictedListIn):
        attachments = restricted_list_in.attachment
        restricted_list_in = restricted_list_in.dict(exclude_none=True)

        # removing attachment
        restricted_list_in.pop("attachment")

        new_restricted_list = self.__repository.create_restricted_list(
            **{**restricted_list_in, "createdBy": self.__es_repository.tenancy.userId}
        )

        new_attachments = self.add_attachment_to_restricted_list(
            restricted_list_id=new_restricted_list["id"],
            attachments=attachments,
        )
        new_restricted_list["attachment"] = new_attachments
        return new_restricted_list

    async def get_restricted_lists_with_active_watches(self) -> dict:
        log.info("Searching for active watches of restricted lists")
        watches = await self.__es_repository.get_restricted_list_watches(
            status=WatchStatusType.ACTIVE
        )

        restricted_list_id_counts = defaultdict(int)
        for w in watches["results"]:
            restricted_list_id_counts[w["query"]["restrictedListId"]] += 1

        return restricted_list_id_counts

    async def get_restricted_lists(self, **kwargs):
        restricted_list_id_counts = await self.get_restricted_lists_with_active_watches()

        rl_ids = list(restricted_list_id_counts.keys())
        if not rl_ids and kwargs.get("with_active_watches"):
            return {"header": [], "results": []}

        header, search_result = self.__repository.get_restricted_lists(ids=rl_ids, **kwargs)

        # Adding activeWatches count to restricted lists with active watches
        if kwargs.get("with_active_watches"):
            for rl in search_result:
                rl["activeWatches"] = restricted_list_id_counts[str(rl["id"])]

        return {"header": header, "results": search_result}

    def get_restricted_list(self, restricted_list_id: UUID):
        try:
            restricted_list = self.__repository.get_restricted_list(
                restricted_list_id=str(restricted_list_id)
            )
            restricted_list = restricted_list[0]

        except (AttributeError, IndexError):
            raise NotFound(message=f"Restricted list for id '{restricted_list_id}' not found")

        return restricted_list

    async def get_restriction_summary(self, restricted_list_id: str = None, **kwargs):
        try:
            rl_ids = None
            if not restricted_list_id:
                restricted_list_id_counts = await self.get_restricted_lists_with_active_watches()

                rl_ids = list(restricted_list_id_counts.keys())
                if not rl_ids and kwargs.get("with_active_watches"):
                    return []

            summary = self.__repository.get_restriction_summary(
                ids=rl_ids, restricted_list_id=restricted_list_id, **kwargs
            )
            if restricted_list_id:
                summary = summary[0]

        except IndexError:
            return {}

        return summary

    async def update_restricted_list(
        self, restricted_list_id: UUID, restricted_list_in: OptionalRestrictedListIn
    ):
        changes, restricted_list = self.__repository.update_restricted_list(
            restricted_list_id=restricted_list_id,
            **{
                **restricted_list_in.dict(by_alias=True, exclude_unset=True),
                "updatedBy": self.__repository.tenancy.userId,
            },
        )

        if restricted_list is None:
            raise NotFound(message=f"Restricted list with id '{restricted_list_id}' not found")

        return changes, restricted_list

    async def delete_restricted_list(self, restricted_list_id: UUID):
        watches = await self.__es_repository.get_restricted_list_watches(
            restricted_list_id=str(restricted_list_id),
            status=WatchStatusType.ACTIVE,
        )
        if watches.results:
            raise HTTPException(
                status_code=403, detail="Restricted list have active watches linked to it"
            )

        restricted_list = self.__repository.delete_restricted_list(
            restricted_list_id=restricted_list_id
        )
        if not restricted_list:
            raise NotFound(message=f"Restricted list for id '{restricted_list_id}' not found")

        return restricted_list

    def add_attachment_to_restricted_list(
        self, restricted_list_id: UUID, attachments: list[AttachmentIn]
    ):
        attachment_list = []
        for attachment in attachments:
            bucket, key = self.upload_service.upload_restricted_list_attachment(
                restricted_list_id=restricted_list_id,
                attachment_in=attachment,
            )

            attachment_data = attachment.dict()
            del attachment_data["content"]
            new_attachment = self.__attachment_repo.create_restricted_list_attachment(
                restricted_list_id=restricted_list_id,
                attachment=Attachment(bucket=bucket, key=key, **attachment_data),
            )
            attachment_list.append(new_attachment)

        return attachment_list

    async def get_restricted_list_watches(self, **kwargs):
        return await self.__es_repository.get_restricted_list_watches(**kwargs)

    def get_validation_errors_summary(self, **kwargs):
        return self.__repository.get_validation_errors_summary(**kwargs)
