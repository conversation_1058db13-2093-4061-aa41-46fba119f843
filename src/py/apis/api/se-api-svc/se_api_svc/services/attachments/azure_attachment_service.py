import logging
import os
from api_sdk.auth import Tenancy
from api_sdk.cloud.abstractions_sync.abstract_storage_client import AbstractStorageClient
from api_sdk.exceptions import BackendError
from api_sdk.repository.asyncronous.request_bound import RequestBoundRepository
from api_sdk.schemas.common import Attachment
from azure.core.exceptions import ClientAuthenticationError, HttpResponseError
from azure.identity import DefaultAzureCredential
from azure.storage.blob import BlobServiceClient
from se_api_svc.core.config import ApiServiceConfig
from se_api_svc.services.attachments.attachments import AbstractAttachmentsService

log = logging.getLogger(__name__)


class AzureAttachmentsService(AbstractAttachmentsService):
    def __init__(
        self,
        tenancy: Tenancy,
        repo: RequestBoundRepository,
        api_config: ApiServiceConfig,
        storage_client: AbstractStorageClient,
    ):
        super().__init__(
            tenancy=tenancy, repo=repo, api_config=api_config, storage_client=storage_client
        )
        try:
            self.container_name = tenancy.tenant
            account_name = os.environ.get("AZURE_STORAGE_ACCOUNT_NAME")
            account_url = f"https://{account_name}.blob.core.windows.net"
            self.service_client = BlobServiceClient(
                account_url=account_url,
                credential=DefaultAzureCredential(),
            )
            self.container_client = self.service_client.get_container_client(
                container=self.container_name
            )
        except ClientAuthenticationError as e:
            msg = f"Failed to Connect with Azure Client : {e}"
            raise BackendError(msg)

    @property
    def bucket(self):
        return self.tenancy.tenant

    def get_attachment_content(self, attachment: Attachment, obj: bool = False):
        location = self._get_location(attachment)
        bucket = location["bucket"]
        key = location["key"]
        log.debug(f"Attempting to retrieve attachment at container - {bucket} and key - {key}")
        try:
            blob_client = self.service_client.get_blob_client(
                container=self.container_name, blob=key
            )
            resp = blob_client.download_blob()
        except HttpResponseError as ce:
            msg = f"Error downloading attachment - {location} due to error - {ce}"
            log.error(msg)
            raise BackendError(msg)
        return resp.readall()
