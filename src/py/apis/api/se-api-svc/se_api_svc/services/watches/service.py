# type: ignore
"""Service handling watch execution and scheduling."""

import logging
import pprint
import pytz
from api_sdk.auth import Tenancy
from api_sdk.exceptions import AlreadyExists, BackendError, BadInput
from api_sdk.utils.not_set import NOT_SET, NotSet
from datetime import datetime
from dateutil import tz
from se_api_svc.core.config import ApiServiceConfig
from se_api_svc.repository.surveillance.watches import WatchesRepository
from se_api_svc.schemas.surveillance.watches import Watch
from se_elastic_schema.components.surveillance.watch_schedule_details import WatchScheduleDetails
from se_elastic_schema.static.surveillance import (
    BaseStrEnum,
    MarketAbuseReportType,
    WatchPriorityType,
    WatchScheduleMonthlyRecurrence,
    WatchScheduleRecurrence,
)
from typing import Union


class WatchScheduleDaysOfWeek(BaseStrEnum):
    SUNDAY = "SUNDAY"
    MONDAY = "MONDAY"
    TUESDAY = "TUESDAY"
    WEDNESDAY = "WEDNESDAY"
    THURSDAY = "THURSDAY"
    FRIDAY = "FRIDAY"
    SATURDAY = "SATURDAY"


DAY_OF_WEEK_ABBREV = {
    WatchScheduleDaysOfWeek.SUNDAY.value: "SUN",
    WatchScheduleDaysOfWeek.MONDAY.value: "MON",
    WatchScheduleDaysOfWeek.TUESDAY.value: "TUE",
    WatchScheduleDaysOfWeek.WEDNESDAY.value: "WED",
    WatchScheduleDaysOfWeek.THURSDAY.value: "THU",
    WatchScheduleDaysOfWeek.FRIDAY.value: "FRI",
    WatchScheduleDaysOfWeek.SATURDAY.value: "SAT",
}


log = logging.getLogger(__name__)


class WatchSchedulingException(Exception):
    pass


AnyWatch = Union[Watch]


class WatchService:
    _config: ApiServiceConfig
    _tenancy: Tenancy
    _repo: Union[WatchesRepository]

    def __init__(self, tenancy: Tenancy, config: ApiServiceConfig, repo: WatchesRepository):
        self._tenancy = tenancy
        self._config = config
        self._repo = repo

    @staticmethod
    def _get_flow_module(report_type: MarketAbuseReportType):
        name = report_type.lower()
        return f"flow_detect_{name}"

    async def delete_watch_and_schedule(self, watch: Watch):
        try:
            await self._repo.delete_watch(watch)
        except WatchSchedulingException as e:
            raise BackendError from e

    @staticmethod
    def _is_schedulable(schedule_details: WatchScheduleDetails):
        if not schedule_details or not schedule_details.recurrence:
            return False

        if not schedule_details.recurrence == WatchScheduleRecurrence.CONTINUOUS:
            if schedule_details.timeOfDay is None:
                return False

            if len(schedule_details.timeOfDay.split(":")) != 2:
                return False

            hour = schedule_details.timeOfDay.split(":")[0]
            minute = schedule_details.timeOfDay.split(":")[1]

            if not hour.isnumeric() or not minute.isnumeric():
                return False
        elif not isinstance(schedule_details.interval, int):
            return False

        return True

    async def upsert_watch_schedule(
        self, watch: Watch, watch_schedule: WatchScheduleDetails
    ) -> Watch:
        # disallow storing invalid schedule
        log.debug(
            "Validating watch_id %s with schedule %s",
            watch.id_,
            str(watch_schedule.dict()),
        )

        self.validate_schedule_details(watch_schedule)
        # throws error on not found
        log.debug("Attempting to get existing watch which will throw an error if not found")

        log.debug("Existing watch %s", str(watch.dict()))
        original_schedule = watch.schedule_copy()
        log.debug("Original schedule %s", str(original_schedule))

        # new schedule
        watch.scheduleDetails = watch_schedule
        try:
            log.info("Updating watch schedule for watch %s", watch.id_)
            updated_watch = await self._repo.save_watch(watch)
            if not updated_watch:
                raise BackendError(f"Failed to update watch {watch.id_}")
            return updated_watch
        except AlreadyExists:
            log.debug("Watched already scheduled without changes")
        except WatchSchedulingException as e:
            # restore the original schedule on the watch in ES index
            log.exception(f"Unable to update watch due failure in updating schedule - {e}")
            log.debug("Attempting revert update using original watch")
            watch.scheduleDetails = original_schedule
            await self._repo.save_watch(watch)
            log.debug("Update successful, raising backtest error")
            raise BackendError from e

    async def update_watch_schedule_status(self, watch: Watch) -> Watch:
        log.debug("Updating watch %s with status %s", watch, watch.status.value)
        try:
            log.info("Updating status for watch %s", watch.id_)
            updated_watch = await self._repo.save_watch(watch)

            if not updated_watch:
                raise BackendError(f"Failed to update status for watch {watch.id_}")

            return updated_watch
        except AlreadyExists:
            log.debug("Updating watch failed due to already existing, returning existing watch")
            return watch

        except WatchSchedulingException as e:
            # restore the watch in ES index
            log.exception(f"Unable to update watch due failure in updating schedule - {e}")
            log.debug("Attempting revert update using original watch")
            await self._repo.save_watch(watch)
            log.debug("Update successful, raising backtest error")
            raise BackendError from e

    async def update_watch_priority(self, watch: Watch, priority: WatchPriorityType) -> Watch:
        log.debug("Updating watch %s with priority %s", watch, priority)
        try:
            watch.priority = priority
            updated_watch = await self._repo.save_watch(watch)

            if not updated_watch:
                raise BackendError(f"Failed to update priority for watch {watch.id_}")
            return updated_watch

        except WatchSchedulingException as e:
            log.debug("Updating watch priority was not successful.")
            raise BackendError from e

    async def update_watch(self, watch: Watch, new_watch: Watch) -> Watch:
        log.debug("Updating watch %s with new watch %s", watch, new_watch)
        try:
            if watch.name != new_watch.name:
                watch.name = new_watch.name

            if watch.filter_changed(new_watch):
                watch.query.filter = new_watch.query.filter

            if watch.defaultAssigneeId != new_watch.defaultAssigneeId:
                watch.defaultAssigneeId = new_watch.defaultAssigneeId
                watch.defaultAssigneeName = new_watch.defaultAssigneeName

            _watch = watch.copy()
            _watch.update_from(new_watch)

            log.info("Updating status for watch %s", watch.id_)
            updated_watch = await self._repo.save_watch(_watch)

            if not updated_watch:
                raise BackendError(f"Failed to update status for watch {watch.id_}")

            return new_watch

        except AlreadyExists:
            log.debug("Updating watch failed due to already existing, returning existing watch")
            return watch

    @staticmethod
    def validate_schedule_details(schedule_details: WatchScheduleDetails):
        """Check if watch is schedulable and valid Validation is optimistic,
        which means validation is only done for required fields based on
        schedule_detail's recurrence type.

        In case of any error, an exception is thrown
        """
        if not WatchService._is_schedulable(schedule_details):
            return

        if (
            schedule_details.recurrence == WatchScheduleRecurrence.WEEKLY
            and not schedule_details.daysOfWeek
        ):
            raise BadInput(
                msg="Days of week is required for WEEKLY schedule type",
                loc=["body", "schedule_details", "daysOfWeek"],
            )

        if schedule_details.recurrence == WatchScheduleRecurrence.MONTHLY:
            if not schedule_details.monthlyRecurrence:
                raise BadInput(
                    msg="monthly recurrence type is required for MONTHLY schedule type",
                    loc=["body", "schedule_details", "monthlyRecurrence"],
                )
            if (
                schedule_details.monthlyRecurrence == WatchScheduleMonthlyRecurrence.DAILY
                and not schedule_details.dayOfMonth
            ):
                raise BadInput(
                    msg="Day of month is required for MONTHLY schedule type with DAILY recurrence",
                    loc=["body", "schedule_details", "dayOfMonth"],
                )

        if schedule_details.recurrence == WatchScheduleRecurrence.CONTINUOUS:
            if not schedule_details.interval:
                raise BadInput(
                    msg="interval is required for CONTINUOUS schedule type.",
                    loc=["body", "schedule_details", "interval"],
                )

            if not schedule_details.interval >= 5:
                raise BadInput(
                    msg="interval must be greater than or equal to 5 minutes.",
                    loc=["body", "schedule_details", "interval"],
                )

            if any(
                [
                    schedule_details.dayOfMonth,
                    schedule_details.daysOfWeek,
                    schedule_details.timeOfDay,
                    schedule_details.monthlyRecurrence,
                ]
            ):
                raise BadInput(
                    msg="dayOfMonth, daysOfWeek, hour, minute, and monthlyRecurrence must be null in CONTINUOUS "  # noqa: E501
                    "schedule type.",
                    loc=["body", "schedule_details"],
                )

    @staticmethod
    def create_cron(schedule_details: WatchScheduleDetails) -> Union[str, NotSet]:
        if not WatchService._is_schedulable(schedule_details):
            return NOT_SET

        hour, minute = "0", "0"
        day_of_month = "*"
        month = "*"
        day_of_week = "*"

        time_zone = schedule_details.timeZone if schedule_details.timeZone else "UTC"

        if schedule_details.timeOfDay is not None:
            local_tz = pytz.timezone(time_zone)
            local_time = datetime.now(tz=local_tz)
            naive_time = datetime.strptime(schedule_details.timeOfDay, "%H:%M")
            local_time = local_time.replace(
                hour=naive_time.hour, minute=naive_time.minute, second=0, microsecond=0
            )
            utc_time = local_time.astimezone(tz.UTC)
            hour, minute = str(utc_time.hour), str(utc_time.minute)

        if schedule_details.daysOfWeek:
            daysOfWeek = schedule_details.daysOfWeek.split(",")

            day_name_to_number = {
                "MONDAY": 0,
                "TUESDAY": 1,
                "WEDNESDAY": 2,
                "THURSDAY": 3,
                "FRIDAY": 4,
                "SATURDAY": 5,
                "SUNDAY": 6,
            }
            number_to_day_abbrev = {
                0: "MON",
                1: "TUE",
                2: "WED",
                3: "THU",
                4: "FRI",
                5: "SAT",
                6: "SUN",
            }

            utc_day_of_week = utc_time.weekday()

            # If the day of the week in UTC is different from the local time, adjust the daysOfWeek
            if utc_day_of_week != local_time.weekday():
                if utc_day_of_week > local_time.weekday():
                    daysOfWeek = [
                        number_to_day_abbrev[(day_name_to_number[day] + 1) % 7]
                        for day in daysOfWeek
                    ]
                else:
                    daysOfWeek = [
                        number_to_day_abbrev[(day_name_to_number[day] - 1) % 7]
                        for day in daysOfWeek
                    ]
            else:
                daysOfWeek = [number_to_day_abbrev[day_name_to_number[day]] for day in daysOfWeek]

            day_of_week = ",".join(daysOfWeek)

        if schedule_details.recurrence is WatchScheduleRecurrence.CONTINUOUS:
            if schedule_details.interval < 60:
                hour = "*"
                minute = f"*/{schedule_details.interval}"
            else:
                hour = f"*/{int(schedule_details.interval / 60)}"
                minute = "0"

        if (
            schedule_details.recurrence == WatchScheduleRecurrence.DAILY
            and schedule_details.interval
        ):
            day_of_month = f"*/{schedule_details.interval}"

        if schedule_details.recurrence == WatchScheduleRecurrence.MONTHLY:
            monthly_recurrence = schedule_details.monthlyRecurrence
            if monthly_recurrence == WatchScheduleMonthlyRecurrence.ON_DAY:
                day_of_month = schedule_details.dayOfMonth
            elif monthly_recurrence == WatchScheduleMonthlyRecurrence.FIRST_DAY:
                day_of_month = "1"
            elif monthly_recurrence == WatchScheduleMonthlyRecurrence.LAST_DAY:
                day_of_month = "L"
        cron_string = f"{minute} {hour} {day_of_month} {month} {day_of_week}"
        log.debug(
            f"Generated cron string {cron_string} for {pprint.pformat(schedule_details.dict())}"
        )
        return cron_string
