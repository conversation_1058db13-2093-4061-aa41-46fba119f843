import logging
import pytz
from api_sdk.auth import Tenancy
from api_sdk.config_base import ApiConfig
from api_sdk.exceptions import NotFound
from api_sdk.models.session import Session
from api_sdk.schemas.kc import JWTPayload
from api_sdk.schemas.static import Module
from api_sdk.services.kc.kc import KeyCloakService
from fastapi import BackgroundTasks
from se_api_svc.repository.account.users import UserRepository
from se_api_svc.repository.admin.data_access_policy import DataAccessPolicyRepo
from se_api_svc.repository.admin.roles import RolesRepo
from se_api_svc.repository.admin.workflow import UserAlertWorkflowRepo
from se_api_svc.schemas.account import AccountUser
from se_api_svc.services.kc_sync.constants import (
    dap_modules,
    idp_attribute_supported_models,
    rp_modules,
    workflow_modules,
)
from se_api_svc.services.kc_sync.utils import get_jurisdiction_object_from_csv
from se_api_svc.services.kc_sync.validators import (
    country_validator,
    jurisdiction_validator,
    language_validator,
)
from se_api_svc.services.user_manager import UserManagerService
from se_elastic_schema.components.tenant.idp_attributes import IdpAttributeMapping
from se_elastic_schema.models.tenant.security.user_session import UserSession
from se_elastic_schema.static.security import AuthScheme
from tenant_db.models.data_access_policy.constant import Module as DapModules
from tenant_db.models.workflow.constant import Module as WorkflowModules
from typing import Dict, List, Optional

logger = logging.getLogger(__name__)


async def sync_account_user(
    jwt_payload: JWTPayload,
    user_id,
    modules: List[str],
    idpAttributes: Optional[List[IdpAttributeMapping]],
    users_repo: UserRepository,
    user_manager: UserManagerService,
):
    jwt_dict = jwt_payload.dict()

    # Reset modules
    original_user: AccountUser = await users_repo.get_one(id=user_id, record_model=AccountUser)

    logger.debug("Updating modules in account user")
    original_user.permissions = [Module(module) for module in modules if module in Module.as_list()]

    # Set modules/permissions
    original_user.permissions = modules

    if idpAttributes:
        # Since jurisdiction is a list, if it's present in the idpAttributes,
        # we will set it to None first.
        for attr in idpAttributes:
            if attr.model == AccountUser.Config.model_name and attr.field == str(
                AccountUser.__fields__["jurisdiction"].name
            ):
                # We would have raised error already if the attribute is present in
                # idpAttributes and not in jwt_payload, but still checking here.
                original_user.jurisdiction = None

        for attr in idpAttributes:
            if attr.model == AccountUser.Config.model_name:
                if attr.field == str(AccountUser.__fields__["jurisdiction"].name):
                    # We would have raised error already if the attribute is present in
                    # idpAttributes and not in jwt_payload, but still checking here.
                    jurisdiction_attr = jwt_dict.get(attr.attribute)
                    if isinstance(jurisdiction_attr, str):
                        try:
                            jurisdiction = get_jurisdiction_object_from_csv(jurisdiction_attr)
                            original_user.jurisdiction = (
                                [*(original_user.jurisdiction or []), jurisdiction]
                                if original_user.jurisdiction
                                else [jurisdiction]
                            )
                        except ValueError as e:
                            logger.error("Error parsing jurisdiction attributes: " + str(e))
                else:
                    setattr(original_user, attr.field, getattr(jwt_payload, attr.attribute, None))

    user_manager.update_user_without_permission_check(
        user=original_user.dict(by_alias=True),
        tenant=users_repo.tenancy.tenant,
    )
    logger.debug("Updated user:", original_user.dict(by_alias=True))


idp_module_attr_mapper = {AccountUser.Config.model_name: sync_account_user}
idp_module_attr_validator = {
    (
        AccountUser.Config.model_name,
        str(AccountUser.__fields__["jurisdiction"].name),
    ): jurisdiction_validator,
    (
        AccountUser.Config.model_name,
        str(AccountUser.__fields__["language"].name),
    ): language_validator,
    (
        AccountUser.Config.model_name,
        str(AccountUser.__fields__["countryCode"].name),
    ): country_validator,
    (
        AccountUser.Config.model_name,
        str(AccountUser.__fields__["timeZone"].name),
    ): lambda value: None if value in pytz.all_timezones else f"Invalid timezone '{value}'",
}


class KcSyncService:
    def __init__(
        self,
        config: ApiConfig,
        kc: KeyCloakService,
        roles: RolesRepo,
        dap: DataAccessPolicyRepo,
        workflow: UserAlertWorkflowRepo,
        users_repo: UserRepository,
        user_manager: UserManagerService,
        tenancy: Tenancy,
    ):
        self.config = config
        self.kc = kc
        self.roles = roles
        self.workflow = workflow
        self.dap = dap
        self.users_repo = users_repo
        self.user_manager = user_manager
        self.tenancy = tenancy

    async def validate_sync_input(
        self,
        modules,
        roles: Dict[str, str],
        workflows: Dict[str, str],
        daps: Dict[str, str],
        jwt_payload: JWTPayload,
    ):
        errors = []

        is_admin = Module.ADMIN in modules

        # If user has access to any of the workflow module
        # (module for which workflow can be assigned),
        # that module should have workflow assigned
        for module in modules:
            if module in workflow_modules and (
                module not in workflows or workflows[module] is None
            ):
                logger.error(f"Module '{module}' has no workflow assigned")
                errors.append(f"Module '{module}' has no workflow assigned")

        # If user has access to any of the DAP module
        # (module for which DAP can be assigned),
        # that module should have DAP assigned
        for module in modules:
            if module in dap_modules and (module not in daps or daps[module] is None):
                logger.error(f"Module '{module}' has no DAP assigned")
                errors.append(f"Module '{module}' has no DAP assigned")

        # If user is admin, no role should be assigned
        if is_admin:
            if roles:
                for module, role in roles.items():
                    if role:
                        logger.error(
                            f"User is admin, but has role '{role}' assigned for module '{module}'"
                        )
                        errors.append(
                            f"User is admin, but has role '{role}' assigned for module '{module}'"
                        )
        else:
            # If user has access to any of the r&p module
            # (module for which role can be assigned),
            # that module should have role assigned
            for module in modules:
                if module not in rp_modules:
                    logger.error(f"Module '{module}' has no roles and permission support")
                    errors.append(f"Module '{module}' has no roles and permission support")
                    continue

                if module in rp_modules and (module not in roles or roles[module] is None):
                    logger.error(f"Module '{module}' has no role assigned")
                    errors.append(f"Module '{module}' has no role assigned")

        # Validate that roles, workflows, daps exists in PG
        for module, role in roles.items():
            if role is None:
                continue
            try:
                _ = self.roles.get_module_role_by_name(module, role)
            except NotFound:
                logger.error(f"Role '{role}' for module '{module}' does not exist")
                errors.append(f"Role '{role}' for module '{module}' does not exist")

        for module, workflow in workflows.items():
            if module not in workflow_modules:
                logger.error(f"Module '{module}' has no workflow support")
                errors.append(f"Module '{module}' has no workflow support")
                continue

            db_workflow = await self.workflow.get_workflow(
                module=WorkflowModules[module], name=workflow
            )
            if not db_workflow:
                logger.error(f"Workflow '{workflow}' for module '{module}' does not exist")
                errors.append(f"Workflow '{workflow}' for module '{module}' does not exist")

        for module, dap in daps.items():
            if module not in dap_modules:
                logger.error(f"Module '{module}' has no dap support")
                errors.append(f"Module '{module}' has no dap support")
                continue

            db_dap = await self.dap.get_data_access_policy(module=DapModules[module], name=dap)
            if not db_dap:
                logger.error(f"DAP '{dap}' for module '{module}' does not exist")
                errors.append(f"DAP '{dap}' for module '{module}' does not exist")

        # Validate extra attributes from JWT payload
        if self.tenancy.tenant_config.idpAttributes:
            used_attr_names = set()

            for attr in self.tenancy.tenant_config.idpAttributes:
                if not hasattr(jwt_payload, attr.attribute):
                    logger.error(f"Attribute '{attr.attribute}' is missing in JWT payload")
                    errors.append(f"Attribute '{attr.attribute}' is missing in JWT payload")
                else:
                    # We're not using elif from here because we want to catch all errors at once

                    # Check if model is valid
                    if attr.model not in idp_attribute_supported_models:
                        logger.error(f"Model '{attr.model}' is not supported for IDP attributes")
                        errors.append(f"Model '{attr.model}' is not supported for IDP attributes")

                    # Check if field is valid for the model
                    if attr.field not in idp_attribute_supported_models[attr.model]:
                        logger.error(
                            f"Field '{attr.field}' is not supported for model '{attr.model}'"
                        )
                        errors.append(
                            f"Field '{attr.field}' is not supported for model '{attr.model}'"
                        )

                    if attr.attribute in used_attr_names:
                        logger.error(f"Duplicate attribute '{attr.attribute}' in IDP attributes")
                        errors.append(f"Duplicate attribute '{attr.attribute}' in IDP attributes")

                    # If any (model, field) specific validator
                    if (attr.model, attr.field) in idp_module_attr_validator:
                        validator = idp_module_attr_validator[(attr.model, attr.field)]
                        res = validator(getattr(jwt_payload, attr.attribute))
                        if isinstance(res, str):
                            logger.error(
                                f"Validation error for attribute '{attr.attribute}': {res}"
                            )
                            errors.append(res)

                    used_attr_names.add(attr.attribute)

        if errors:
            raise ValueError(errors)

    async def sync_data(
        self, session: Session | UserSession, auth_header: str, background_tasks: BackgroundTasks
    ) -> None:
        """
        Sync roles from Keycloak to SteelEye features like module, roles and permissions, DAP, etc.

        :param session: User session object of type Session
        :param auth_header: Authorization header containing the Keycloak token
        :return: None
        """

        try:
            scheme, _ = auth_header.split(" ", 1)
        except ValueError as e:
            logger.error(f"Count not sync keycloak attributes: {str(e)}")
            return

        if scheme != AuthScheme.CODE:
            logger.debug(f"Auth scheme is '{scheme}', and not 'CODE', not syncing keycloak data")
            return

        if not isinstance(session, Session):
            logger.error("session is not of type Session")
            return

        modules = session.token.jwt_payload.get_modules()
        roles = session.token.jwt_payload.get_roles()
        workflows = session.token.jwt_payload.get_workflows()
        daps = session.token.jwt_payload.get_daps()

        await self.validate_sync_input(modules, roles, workflows, daps, session.token.jwt_payload)

        await idp_module_attr_mapper[AccountUser.Config.model_name](
            session.token.jwt_payload,
            session.user_id,
            modules,
            self.tenancy.tenant_config.idpAttributes,
            self.users_repo,
            self.user_manager,
        )

        # Reset roles
        se_module_roles = [
            (module, role) for module, role in roles.items() if module in Module.as_list()
        ]
        self.roles.reset_roles(session.user_id, se_module_roles) if se_module_roles else None

        # Reset workflow
        se_module_workflow = [
            (module, workflow)
            for module, workflow in workflows.items()
            if module in Module.as_list()
        ]

        await self.workflow.reset_workflow(
            session.user_id, se_module_workflow
        ) if se_module_workflow else None

        # Reset Daps
        se_module_dap = [
            (module, dap) for module, dap in daps.items() if module in Module.as_list()
        ]
        await self.dap.reset_dap(
            session.user_id, se_module_dap, background_tasks
        ) if se_module_dap else None
