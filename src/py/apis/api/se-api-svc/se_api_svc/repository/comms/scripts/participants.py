# type: ignore
# flake8: noqa
from se_api_svc.schemas.account import Account<PERSON>erson
from se_api_svc.schemas.comms.common import (
    ParticipantCategorizations,
    ParticipantCategorizationsDetailed,
    ParticipantCategorizationTrends,
)
from se_api_svc.schemas.market import MarketPerson

ACCOUNT_PERSON_MODEL_NAME = AccountPerson.__config__.model_name
MARKET_PERSON_MODEL_NAME = MarketPerson.__config__.model_name

# Agg Script for `Categorizations` trend type.
# IT has the common `init_script`, `combine_script` and `reduce_script`
# to be used by `CATEGORIZATION` and `CATEGORIZATION_DETAILED` trends.
CATEGORIZATION_AGG_SCRIPT = {
    "init_script": "state.participant_classifications = []",
    "combine_script": """
        Map m = [:];
        for (c in state.participant_classifications) {
          if (!m.containsKey(c)) {
            m[c] = 1;
          } else {
            m[c]++;
          }
        }
        return m;
    """,
    "reduce_script": """
        Map m = [:];
        for (c in states) {
          if (c.isEmpty()) {
            continue;
          }
          for (k in c.keySet()) {
            if (!m.containsKey(k)) {
              m[k] = c[k];
            } else {
              m[k] += c[k];
            }
          }
        }
        return m;
    """,
}

# First part of the Agg Map Script that calculates the
# internal and external counts of the participants.
_CATEGORIZATION_AGG_INTERNAL_EXTERNAL_SCRIPT = f"""
    int internal = 0;
    int external = 0;
    for (p in params._source.participants) {{
      def key = p.value['&key'];
      if (key.contains('{ACCOUNT_PERSON_MODEL_NAME}')) {{ internal++; }}
      else if (key.contains('{MARKET_PERSON_MODEL_NAME}')) {{ external++; }}
    }}
"""

# Creates classification for `CATEGORIZATION` Trends.
_CATEGORIZATION_AGG_MAP_SCRIPT = f"""
    if (params == null || params._source == null || params._source.participants == null) {{
      state.participant_classifications.add('{ParticipantCategorizations.NO_PARTICIPANTS.value}');
    }} else {{
      {_CATEGORIZATION_AGG_INTERNAL_EXTERNAL_SCRIPT}
      if (internal != 0 && external != 0) {{
        state.participant_classifications.add('{ParticipantCategorizations.INTERNAL_AND_EXTERNAL.value}');
      }} else if (internal != 0) {{
        state.participant_classifications.add('{ParticipantCategorizations.INTERNAL_ONLY.value}');
      }} else if (external != 0) {{
        state.participant_classifications.add('{ParticipantCategorizations.EXTERNAL_ONLY.value}');
      }} else {{
        state.participant_classifications.add('{ParticipantCategorizations.NO_RECORDS.value}');
      }}
    }}
"""

# Creates classification for `CATEGORIZATION_DETAILED` Trends. It's being used by both CategoriationDetailedFilter
# and CategorizationDetailed Agg.
_CATEGORIZATION_DETAILED_AGG_CATEGORY_SCRIPT = f"""
    def category = 'Unknown';
    if (internal == 0 && external == 0) {{ category = '{ParticipantCategorizationsDetailed.NO_RECORDS.value}'; }}
    else if (internal > 1 && external > 1) {{
      category = '{ParticipantCategorizationsDetailed.INTERNAL_MULTIPLE_AND_EXTERNAL_MULTIPLE.value}';
    }}
    else if (internal == 1 && external > 1) {{
      category = '{ParticipantCategorizationsDetailed.INTERNAL_SINGLE_AND_EXTERNAL_MULTIPLE.value}';
    }}
    else if (internal > 1 && external == 1) {{
      category = '{ParticipantCategorizationsDetailed.INTERNAL_MULTIPLE_AND_EXTERNAL_SINGLE.value}';
    }}
    else if (internal == 1 && external == 1) {{
      category = '{ParticipantCategorizationsDetailed.INTERNAL_SINGLE_AND_EXTERNAL_SINGLE.value}';
    }}
    else if (internal > 1 && external == 0) {{
      category = '{ParticipantCategorizationsDetailed.INTERNAL_MULTIPLE.value}';
    }}
    else if (internal == 1 && external == 0) {{
      category = '{ParticipantCategorizationsDetailed.INTERNAL_SINGLE.value}';
    }}
    else if (external > 1 && internal == 0) {{
      category = '{ParticipantCategorizationsDetailed.EXTERNAL_MULTIPLE.value}';
    }}
    else if (external == 1 && internal == 0) {{
      category = '{ParticipantCategorizationsDetailed.EXTERNAL_SINGLE.value}';
    }}
"""

# Creates the classification agg script for `CATEGORIZATION_DETAILED` trend.
_CATEGORIZATION_DETAILED_AGG_MAP_SCRIPT = f"""
    if (params == null || params._source == null || params._source.participants == null) {{
      state.participant_classifications.add('{ParticipantCategorizations.NO_PARTICIPANTS.value}');
    }} else {{
      {_CATEGORIZATION_AGG_INTERNAL_EXTERNAL_SCRIPT}
      {_CATEGORIZATION_DETAILED_AGG_CATEGORY_SCRIPT}
      if (category != 'Unknown') {{
        state.participant_classifications.add(category);
      }}
    }}
"""

CATEGORIZATIONS_AGG_MAP_SCRIPTS = {
    ParticipantCategorizationTrends.CATEGORIZATION: _CATEGORIZATION_AGG_MAP_SCRIPT,
    ParticipantCategorizationTrends.CATEGORIZATION_DETAILED: _CATEGORIZATION_DETAILED_AGG_MAP_SCRIPT,
}

# Creates filter script for `CATEGORIZATION_DETAILED` trend type.
CATEGORIZATION_DETAILED_FILTER_SCRIPT = f"""
    {_CATEGORIZATION_AGG_INTERNAL_EXTERNAL_SCRIPT}
    {_CATEGORIZATION_DETAILED_AGG_CATEGORY_SCRIPT}
    return params.ids.contains(category);
"""
