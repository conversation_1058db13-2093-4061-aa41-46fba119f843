import datetime
import datetime as dt
import json
import logging
import math
from api_sdk.cloud.abstractions_sync.abstract_storage_client import AbstractStorageClient
from api_sdk.cloud.utils import extract_bucket_key_filename
from api_sdk.es_dsl.base import ModelFilter, Not, NotExpired, SearchModel, TermFilter
from api_sdk.es_dsl.features import Nested, RangeFilter
from api_sdk.es_dsl.flang import FlangFilter
from api_sdk.es_dsl.params import SearchModelParams
from api_sdk.models.elasticsearch import RawResult
from api_sdk.repository.asyncronous.request_bound import BoundRepository, RepoHelpersMixin
from api_sdk.utils.utils import nested_get
from glom import glom
from ordered_set import OrderedSet  # pants: no-infer-dep
from schema_sdk.steeleye_model.gamma import GammaMetaFields
from se_api_svc.core.constants import ES_MAX_AGG_SIZE
from se_api_svc.repository.comms.utils.calendar_flang import CALENDAR_FLANG_FUNCS
from se_api_svc.repository.comms.utils.calendar_search_filter import (
    AGGS_TERM_SCRIPT,
    ExternalFilter,
    InternalFilter,
)
from se_api_svc.repository.comms.utils.participants import (
    DEPTH_1_NODE_COUNT,
    DEPTH_2_NODE_COUNT,
    NetworkNode,
    get_initials_from_name,
    get_network_map_nodes_and_links,
)
from se_api_svc.schemas.account import AccountPerson
from se_api_svc.schemas.comms import Call, Email, Message, Text
from se_api_svc.schemas.comms.meeting import Meeting
from se_api_svc.schemas.market import MarketPerson
from typing import Any, Dict, List, Optional, Union

log = logging.getLogger(__name__)


class CommsSearchBase(SearchModel):
    class Params(SearchModelParams):
        f: Optional[str]
        start: Union[dt.datetime, dt.date] = None
        end: Union[dt.datetime, dt.date] = None
        participant_ids: Optional[List[str]] = None
        recurring_id: Optional[str] = None
        internal: Optional[List[str]] = None
        external: Optional[List[str]] = None

    params: Params
    default_sort_order = ["timestamps.timestampStart:asc"]

    features = [
        ModelFilter(model=[Meeting]),
        NotExpired,
        Not(TermFilter(name="isAllDay", value=True)),
        TermFilter(name="meetingDetails.isCancelled", value=False),
        Nested(
            TermFilter(name="participants.value.&id", param="participant_ids"), path="participants"
        ),
        TermFilter(name="meetingIdentifier.recurringId", param="recurring_id"),
        RangeFilter(field="timestamps.timestampStart"),
        InternalFilter(param="internal"),
        ExternalFilter(param="external"),
        FlangFilter.simple(
            param="f",
            nested_paths=["participants", "analytics.classifier.predictions"],
            funcs=CALENDAR_FLANG_FUNCS,
        ),
    ]


class CommsSourcesAgg(CommsSearchBase):
    features = [
        ModelFilter(model=[Call, Email, Meeting, Message, Text]),
        *CommsSearchBase.features[1:],
    ]

    def build_aggs(self) -> Optional[Dict]:
        return {
            "MODEL": {
                "terms": {"field": "&model"},
                "aggs": {"SOURCE": {"terms": {"field": "metadata.source.client", "missing": "-"}}},
            }
        }


class CommsMeetingAggs(CommsSearchBase):
    class Params(CommsSearchBase.Params):
        timezone: Optional[str]

    params: Params

    def build_aggs(self) -> Dict:
        return {
            "TIMELINE": {
                "date_histogram": {
                    "field": "timestamps.timestampStart",
                    "fixed_interval": "1d",
                    "format": "yyyy-MM-dd",
                    "time_zone": self.params.timezone,
                },
                "aggs": {
                    "SUM": {
                        "sum": {
                            "script": "(doc['timestamps.timestampEnd'].value.toInstant()."
                            "toEpochMilli()"
                            " - doc['timestamps.timestampStart'].value.toInstant()."
                            "toEpochMilli()) / 1000"
                        }
                    }
                },
            }
        }


class CommsMeetingHeatMapAggs(CommsSearchBase):
    class Params(CommsSearchBase.Params):
        timezone: Optional[str]

    params: Params

    def build_aggs(self) -> Optional[Dict]:
        return {
            "days_of_week": {
                "terms": {
                    "script": {
                        "source": f"def ts = doc['timestamps.timestampStart'].value.toInstant()."
                        f"toEpochMilli(); def localTs = LocalDateTime."
                        f"ofInstant(Instant.ofEpochMilli(ts), ZoneId."
                        f"of('{self.params.timezone}')); return localTs.getDayOfWeek();"
                    }
                },
                "aggs": {
                    "hours_of_day": {
                        "terms": {
                            "script": {
                                "source": f"def ts = doc['timestamps.timestampStart'].value."
                                f"toInstant().toEpochMilli(); def localTs "
                                f"= LocalDateTime.ofInstant(Instant.ofEpochMilli(ts),"
                                f" ZoneId.of('{self.params.timezone}'));"
                                f" return localTs.getHour();"
                            }
                        },
                        "aggs": {
                            "SOURCE": {"terms": {"field": "metadata.source.client", "missing": "-"}}
                        },
                    }
                },
            }
        }


class CommsMeetingProfileAggs(CommsSearchBase):
    class Params(CommsSearchBase.Params):
        domains: Optional[List[str]] = []

    params: Params

    def build_aggs(self) -> Optional[Dict]:
        return {
            "MEETING_SECONDS": {
                "stats": {
                    "script": {
                        "source": "doc['timestamps.timestampEnd'].value.toInstant().toEpochMilli() "
                        "- doc['timestamps.timestampStart'].value.toInstant().toEpochMilli()"
                    }
                }
            },
            "MIN": {"min": {"field": "timestamps.timestampStart"}},
            "MAX": {"max": {"field": "timestamps.timestampStart"}},
            "INTERNAL_EXTERNAL": {
                "terms": {
                    "script": {
                        "lang": "painless",
                        "params": {"domains": self.params.domains},
                        "inline": AGGS_TERM_SCRIPT,
                    }
                }
            },
            "TOP_DOMAIN": {
                "terms": {
                    "script": {
                        "source": f"def allIds = doc['identifiers.allDomains']; "
                        f"def domains = {self.params.domains}; def result = [];"
                        f"for(id in allIds){{ "
                        f"def is_external = [false];"
                        f"for(d in domains){{ if(id == d){{ is_external.add(true);}}"
                        f" else{{is_external.add(false);}} }}"
                        f"if(!is_external.contains(true)){{result.add(id);}}"
                        f" }} "
                        f"return result;"
                    }
                }
            },
            "TOP_PEOPLES": {
                "terms": {
                    "script": {
                        "source": f"def allIds = doc['identifiers.allIds']; "
                        f"def domains = {self.params.domains}; def result = [];"
                        f"for(id in allIds){{ "
                        f"for(d in domains){{ if(id.contains(d)){{ result.add(id);}} }}"
                        f" }} "
                        f"return result;"
                    }
                },
            },
        }


class CommsMeetingsPersonShare(CommsSearchBase):
    def build_aggs(self) -> Optional[Dict]:
        return {
            "PERSON": {"terms": {"field": "identifiers.allIds", "size": ES_MAX_AGG_SIZE}},
            "DOMAIN": {"terms": {"field": "identifiers.allDomains", "size": ES_MAX_AGG_SIZE}},
        }


class PersonNetworkMapAggs(CommsSearchBase):
    class Params(CommsSearchBase.Params):
        depth_node_count: int
        exclude_participant_ids: Optional[List[str]] = []

    params: Params

    def build_aggs(self):
        def build_nested_participant_agg(include_ids: List[str], exclude_ids: List[str]):
            return {
                "nested": {"path": "participants"},
                "aggs": {
                    "ONLY_ID": {
                        "filter": {"filter": {"terms": {"participants.value.&id": include_ids}}},
                        "aggs": {
                            "REVERSE": {
                                "aggs": {
                                    "PARTICIPANT": {
                                        "nested": {"path": "participants"},
                                        "aggs": {
                                            "FILTER_PARTICIPANTS": {
                                                "filter": {
                                                    "bool": {
                                                        "must_not": [
                                                            {
                                                                "terms": {
                                                                    "participants.value.&id": include_ids  # noqa
                                                                    + exclude_ids
                                                                }
                                                            }
                                                        ]
                                                    }
                                                },
                                                "aggs": {
                                                    "LINKS": {
                                                        "terms": {
                                                            "size": self.params.depth_node_count,
                                                            "script": {
                                                                "inline": "doc['participants.value."
                                                                "&id'].value + '|' "
                                                                "+ doc['participants.value.name']."
                                                                "value + '|' "
                                                                "+ doc['participants.value.&key']."
                                                                "value.substring(0,3)"
                                                            },
                                                        }
                                                    }
                                                },
                                            }
                                        },
                                    }
                                },
                                "reverse_nested": {},
                            }
                        },
                    }
                },
            }

        """
        For each included participant create an agg to find the nodes and links associated with
         the same.
        """
        aggs = {}
        for participant_id in self.params.participant_ids:
            aggs[participant_id] = build_nested_participant_agg(
                include_ids=[participant_id], exclude_ids=self.params.exclude_participant_ids
            )

        return aggs


class CalendarRepository(RepoHelpersMixin):
    def __init__(self, repo: BoundRepository, cloud_client: AbstractStorageClient):
        super().__init__(repo)
        self.cloud_client = cloud_client

    @staticmethod
    def get_slice_idx(size, around, idx):
        # considering idx will start from 0
        size = size - 1

        if idx > size:
            return 0, 0

        start = 0
        end = size
        mid = int(around / 2)

        before = mid
        after = mid

        # to place IDx at mid-position
        if around % 2 == 0:
            after = mid - 1

        if idx - before > 0:
            start = idx - before

            if idx + after > size:
                start = idx - before - (idx + after - size)
        else:
            after = idx + before

        if start < 0:
            start = 0

        if after + idx < size - 1:
            end = idx + after

        return start, end

    @staticmethod
    def get_value_for_key(items: List[Any], key: str) -> int:
        for item in items:
            if item["key"] == key:
                return item["doc_count"]

        return 0

    @staticmethod
    def get_weeks(result):
        start = (
            datetime.date.fromtimestamp(glom(result, "MIN.value") / 1000).isocalendar()[1]
            if glom(result, "MIN.value") is not None
            else 0
        )
        end = (
            datetime.date.fromtimestamp(glom(result, "MAX.value") / 1000).isocalendar()[1]
            if glom(result, "MAX.value") is not None
            else 1
        )

        if end < start:
            end = 52 + end

        weeks = end - start if (end - start) != 0 else 1

        return weeks

    async def get_meeting_details_of_person(self, **params):
        return await self.repo.get_many(
            search_model=CommsSearchBase(**params),
        )

    async def get_source_aggs(self, **params):
        result = []
        raw_result = await self.repo.get_aggs(
            search_model=CommsSourcesAgg(**params),
        )

        for item in raw_result.iter_raw_bucket_agg("MODEL"):
            result.append({"key": item["key"], "data": glom(item, "SOURCE.buckets")})

        return result

    async def get_meetings_summary(self, **params):
        result = []
        raw_result = await self.repo.get_aggs(
            search_model=CommsMeetingAggs(**params),
        )

        for item in raw_result.iter_raw_bucket_agg("TIMELINE"):
            result.append(
                {
                    "date": item["key_as_string"],
                    "total_meetings": item["doc_count"],
                    "meeting_seconds": math.ceil(glom(item, "SUM.value") / 1000),
                }
            )

        return result

    async def get_comms_heatmap(self, **params):
        results = (await self.get_aggs(search_model=CommsMeetingHeatMapAggs(**params))).aggregations
        day_buckets = results["days_of_week"]["buckets"]

        output = []
        days = ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday"]
        WEEK_DAY_MAP = {
            "Monday": "MONDAY",
            "Tuesday": "TUESDAY",
            "Wednesday": "WEDNESDAY",
            "Thursday": "THURSDAY",
            "Friday": "FRIDAY",
            "Saturday": "SATURDAY",
            "Sunday": "SUNDAY",
        }

        hours = list(range(24))
        day_buckets_keys = [data["key"] for data in day_buckets]

        for day in days:
            day_data = {
                "key": day,
                "value": 0,
                "hours_of_day": {"buckets": []},
            }

            try:
                idx = day_buckets_keys.index(WEEK_DAY_MAP[day])
                data = day_buckets[idx]
            except ValueError:
                data = {"doc_count": 0, "hours_of_day": {"buckets": []}}

            day_data["value"] = data["doc_count"]
            for hour in hours:
                hour_data = {"key": hour, "value": 0, "source": {"buckets": []}}
                for hour_bucket in data["hours_of_day"]["buckets"]:
                    if int(hour_bucket["key"]) == hour:
                        hour_data["value"] = hour_bucket["doc_count"]
                        hour_data["source"]["buckets"] = hour_bucket["SOURCE"]["buckets"]
                day_data["hours_of_day"]["buckets"].append(hour_data)

            output.append(day_data)

        return output

    async def get_person_meeting_profile_details(self, **params):
        result = (await self.get_aggs(search_model=CommsMeetingProfileAggs(**params))).aggregations

        weeks = self.get_weeks(result)
        return {
            "totalNumberOfMeetingsPerWeek": glom(result, "MEETING_SECONDS.count") / weeks,
            "totalSecondsOfMeetingsPerWeek": glom(result, "MEETING_SECONDS.sum") / (weeks * 1000)
            if glom(result, "MEETING_SECONDS.sum")
            else 0,
            "totalInternalMeetings": self.get_value_for_key(
                glom(result, "INTERNAL_EXTERNAL.buckets"), "INTERNAL"
            ),
            "totalExternalMeetings": self.get_value_for_key(
                glom(result, "INTERNAL_EXTERNAL.buckets"), "EXTERNAL"
            ),
            "topExternalDomains": glom(result, "TOP_DOMAIN.buckets"),
            "topInternalPeople": glom(result, "TOP_PEOPLES.buckets"),
        }

    async def get_person_meeting_share(self, **params):
        user = await self.get_one(
            record_model=[MarketPerson, AccountPerson], id=params.get("participant_ids")[0]
        )
        result = (await self.get_aggs(search_model=CommsMeetingsPersonShare(**params))).aggregations
        resp = {"domain": glom(result, "DOMAIN.buckets"), "person": glom(result, "PERSON.buckets")}

        for email in user.communications.emails or []:
            resp["person"] = [d for d in resp["person"] if d.get("key") != email]

        return resp

    async def get_attendees_status(self, calendar_id: str):
        calendar: Meeting = await self.get_one(record_model=Meeting, id=calendar_id)
        data = {}
        result = []

        try:
            bucket, key, file_name = extract_bucket_key_filename(calendar.sourceKey)
            meeting_ingested_file = self.cloud_client.get_object(Bucket=bucket, Key=key, obj=True)
            file_content = meeting_ingested_file.decode("utf-8")
            data = json.loads(file_content)

        except Exception as e:
            log.info(f"Meeting Ingestion file could not be loaded: {e}")

        try:
            meetings = data["value"]
            meetings_id = [x["id"] for x in meetings]
            idx = meetings_id.index(calendar.meetingId)

            meeting = meetings[idx]
            attendees = meeting["attendees"]

            for attendee in attendees:
                result.append(
                    {
                        "name": attendee["emailAddress"]["name"],
                        "email_id": attendee["emailAddress"]["address"],
                        "type": attendee["type"],
                        "response": attendee["status"]["response"],
                    }
                )

        except (ValueError, KeyError) as e:
            log.info(f"Data can not be processed : {e}")

        return result

    async def get_network_map(self, participant_ids: List[str], **search_params):
        # Find participant and create root node
        primary_participant = await self.get_one(
            record_model=[AccountPerson, MarketPerson], id=participant_ids[0]
        )
        root_node = NetworkNode(
            id=primary_participant.id_,
            name=primary_participant.name,
            initials=get_initials_from_name(primary_participant.name),
            type=primary_participant.model_,
        )

        nodes_by_id = {}
        links_by_id = {}
        data_nodes = []
        data_links = []

        data_nodes.append(root_node)
        nodes_by_id[root_node.id] = root_node

        # Delete participant_ids param if present as we'll use this param to search for
        # included participants in the
        # query
        search_params.pop("participant_ids", None)

        # Get the nodes for the primary participant. We'll find a max of
        # `DEPTH_1_NODE_COUNT` nodes.
        depth_1_result = await self.get_aggs(
            search_model_cls=PersonNetworkMapAggs,
            depth_node_count=DEPTH_1_NODE_COUNT,
            participant_ids=[root_node.id],
            **search_params,
        )
        depth_1_nodes, depth_1_links = get_network_map_nodes_and_links(
            depth_1_result, [root_node], nodes_by_id, links_by_id
        )
        data_nodes += depth_1_nodes
        data_links += depth_1_links

        # If nodes are present, now for these node as
        # root node, find the nodes and links for each included node
        if data_nodes:
            depth_2_result = await self.get_aggs(
                search_model_cls=PersonNetworkMapAggs,
                depth_node_count=DEPTH_2_NODE_COUNT,
                participant_ids=[node.id for node in depth_1_nodes],
                exclude_participant_ids=[root_node.id],
                **search_params,
            )
            depth_2_nodes, depth_2_links = get_network_map_nodes_and_links(
                depth_2_result, depth_1_nodes, nodes_by_id, links_by_id
            )
            data_nodes += depth_2_nodes
            data_links += depth_2_links

        # Get the maximum comms link
        max_links_comms = max([link.comms for link in depth_1_links], default=0)

        # Calculate the new normalised weight.
        for link in data_links:
            link.normalised_weight = link.comms / max_links_comms

        return {"links": data_links, "nodes": data_nodes}

    async def get_around_summary(
        self,
        calendar_id,
        **search_params,
    ) -> Dict:
        calendar = await self.repo.get_one(record_model=Meeting, id=calendar_id)
        recurring_id = nested_get(calendar, "meetingIdentifier.recurringId")

        ids = OrderedSet()
        dates = []
        total = 1
        # If there is no recurring_id or recurring meetings
        # are cancelled return the summary of original calendar
        if recurring_id:
            query = CommsSearchBase(recurring_id=recurring_id, **search_params).to_dict(
                meta_fields=GammaMetaFields
            )
            query["size"] = 1000
            query["_source"] = ["&id", "timestamps"]

            index = self.repo.index_for_record_model(Meeting)
            results = self.es_client.search(body=query, index=index)
            ids = OrderedSet([x["_source"]["&id"] for x in results["hits"]["hits"]])
            dates: List[str] = [
                x["_source"]["timestamps"]["timestampStart"] for x in results["hits"]["hits"]
            ]
            total = max(total, results["hits"]["total"].get("value", 0))

        idx = ids.get_indexer(calendar.id_) if calendar.id_ in ids else 0
        if not dates:
            dates.extend([calendar.timestamps.timestampStart, calendar.timestamps.timestampEnd])

        return {"position": idx + 1, "total": total, "start": dates[0], "end": dates[-1]}

    async def get_recurring_list(self, **params) -> RawResult:
        return await self.repo.get_many(
            search_model=CommsSearchBase(**params),
        )
