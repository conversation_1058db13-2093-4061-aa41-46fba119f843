# type: ignore
from api_sdk.es_dsl.base import NoExpirySearchModel
from api_sdk.repository.asyncronous.request_bound import RepoHelpersMixin
from se_api_svc.schemas.comms import Call, Email, Meeting, Message, Text
from se_elastic_schema.models.tenant.communication.transcript import Transcript


class SchemaBoundRepo(RepoHelpersMixin):
    schema = None

    async def get_many(self, **params):
        return await super().get_many(self.schema, **params)

    async def get_one(self, record_id):
        return await super().get_one(self.schema, record_id)

    async def get_one_by_key(self, record_key):
        return await super().get_one(
            self.schema,
            search_model_cls=NoExpirySearchModel,
            terms={"&key": record_key},
        )


class MessagesRepository(SchemaBoundRepo):
    schema = Message

    @staticmethod
    def deserializer(hit):
        return Message(**hit)


class MeetingsRepository(SchemaBoundRepo):
    schema = Meeting

    @staticmethod
    def deserializer(hit):
        return Meeting(**hit)


class CallsRepository(SchemaBoundRepo):
    schema = Call

    @staticmethod
    def deserializer(hit):
        return Call(**hit)


class TranscriptsRepository(SchemaBoundRepo):
    schema = Transcript

    async def get_by_file_id(self, id: str) -> Transcript:
        """Retrieve a transcript by ID.

        Interestingly Transcript has both a `&id` and a `id`. This is
        for the transcript file `id`...
        """
        return await RepoHelpersMixin.get_one(self, self.schema, terms={"id": id})


class EmailsRepository(SchemaBoundRepo):
    schema = Email

    @staticmethod
    def deserializer(hit):
        return Email(**hit)


class TextsRepository(SchemaBoundRepo):
    schema = Text

    @staticmethod
    def deserializer(hit):
        return Text(**hit)
