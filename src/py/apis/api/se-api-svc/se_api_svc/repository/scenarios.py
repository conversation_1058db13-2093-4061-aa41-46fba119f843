# type: ignore
import datetime as dt
import logging
from api_sdk.es_dsl.base import <PERSON><PERSON>ilter, SearchModel, TermFilter
from api_sdk.es_dsl.flang import FlangFilter
from api_sdk.es_dsl.params import SearchModelParams
from api_sdk.models.elasticsearch import RawResult
from api_sdk.repository.asyncronous.request_bound import RepoHelpersMixin
from se_api_svc.schemas.surveillance.alerts import SurveillanceAlert
from se_elastic_schema.models.tenant.mifid2.order import Order
from typing import List, Optional, Union

log = logging.getLogger(__name__)


ORDERS_NESTED_PATHS = [
    "parties.buyer",
    "parties.buyer",
    "parties.client",
    "parties.clientAgent",
    "parties.orderReceiver",
    "parties.seller",
    "parties.trader",
]


class OrdersSearch(SearchModel):
    class Params(SearchModelParams):
        f: Optional[str] = None
        keys: Optional[List[str]] = None
        start: Optional[Union[dt.datetime, dt.date]] = None
        end: Optional[Union[dt.datetime, dt.date]] = None

    params: Params

    features = [
        ModelFilter(model=Order),
        TermFilter(param="keys", name="_meta.key"),
        FlangFilter.simple(
            param="f",
            nested_paths=ORDERS_NESTED_PATHS,
        ),
    ]


def get_nested_order_ids(alert: SurveillanceAlert) -> Union[List[str], None]:
    if not alert.results:
        return None
    alert_result_records = [i.records for i in alert.results]
    meta_keys = [i.metaKeys for row in alert_result_records for i in row]
    return sorted(list(set([i.key for row in meta_keys for i in row])))


class ScenariosRepository(RepoHelpersMixin):
    async def _get_orders_by_keys(self, keys: List[str], **params) -> RawResult:
        return await self.get_many(
            record_model=Order, search_model_cls=OrdersSearch, keys=keys, **params
        )

    async def get_orders_by_scenario(
        self, scenario_id: str, search_params: dict
    ) -> Union[RawResult, None]:
        alert = await self.get_one(record_model=SurveillanceAlert, id=scenario_id)
        order_keys = get_nested_order_ids(alert)

        if not order_keys:
            log.debug("Alert returned no results, and therefore no metaKeys")
            return None

        orders = await self._get_orders_by_keys(keys=order_keys, **search_params)
        return orders
