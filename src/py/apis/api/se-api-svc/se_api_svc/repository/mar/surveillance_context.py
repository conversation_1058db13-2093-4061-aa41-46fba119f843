import datetime as dt
from api_sdk.es_dsl.base import <PERSON><PERSON>ilter, NotExpiredFilter, Or, SearchModel, TermFilter
from api_sdk.es_dsl.features import RangeFilter
from api_sdk.es_dsl.params import SearchModelParams
from api_sdk.models.elasticsearch import RawR<PERSON>ult
from api_sdk.repository.asyncronous.request_bound import RepoHelpersMixin
from api_sdk.utils.utils import nested_dict_get
from se_api_svc.core.constants import ES_MAX_AGG_SIZE
from se_api_svc.schemas.surveillance.alerts import <PERSON><PERSON><PERSON>t, OrderAlert
from se_api_svc.schemas.surveillance.market_abuse import MarketAbuseAlert
from se_api_svc.schemas.surveillance.watches import Watch
from se_elastic_schema.static.surveillance import AlertHitStatus, WatchStatusType
from typing import Any, Dict, List, Optional, Union


class WatchesIdAggs(SearchModel):
    features = [
        ModelFilter(model=Watch),
        NotExpiredFilter,
        TermFilter(name="status", value=WatchStatusType.ACTIVE),
    ]

    def build_aggs(self) -> Optional[Dict]:
        return {"WATCH_IDS": {"terms": {"field": "&id", "size": ES_MAX_AGG_SIZE}}}


class SurveillanceContextSearchBase(SearchModel):
    class Params(SearchModelParams):
        start: Optional[Union[dt.datetime, dt.date]] = None
        end: Optional[Union[dt.datetime, dt.date]] = None
        trader: Optional[str]
        client: Optional[str]
        portfolio_manager: Optional[str]
        portfolio_manager_desk: Optional[str]
        participant: Optional[str]
        report_type: Optional[str]
        instrument_id: Optional[str]
        watch_ids: Optional[Union[str, List[str]]]

    params: Params

    features = [
        ModelFilter(model=[OrderAlert, CommunicationAlert, MarketAbuseAlert]),
        NotExpiredFilter,
        TermFilter(name="detail.watchId", param="watch_ids"),
        RangeFilter(field="detected"),
        Or(
            TermFilter(name="hit.trader.&id", param="trader"),
            TermFilter(name="hit.clientIdentifiers.client.&id", param="client"),
            TermFilter(
                name="hit.tradersAlgosWaiversIndicators.investmentDecisionWithinFirm.&id",
                param="portfolio_manager",
            ),
            TermFilter(
                name="hit.tradersAlgosWaiversIndicators.investmentDecisionWithinFirm.structure.desks.name",
                param="portfolio_manager_desk",
            ),
            TermFilter(name="hit.participants.value.&id", param="participant"),
        ),
    ]


class AlertSummaryAgg(SurveillanceContextSearchBase):
    def build_aggs(self) -> Optional[Dict]:
        return {
            "COMM_ALERTS": {
                "filter": {"bool": {"must": {"term": {"&model": "CommunicationAlert"}}}},
                "aggs": {
                    "STATUS": {
                        "terms": {"field": "workflow.status"},
                    }
                },
            },
            "TRADE_ALERTS": {
                "filter": {"bool": {"must": {"term": {"&model": "OrderAlert"}}}},
                "aggs": {
                    "STATUS": {
                        "terms": {"field": "workflow.status"},
                    }
                },
            },
            "TRADE_MAR_ALERTS": {
                "filter": {"bool": {"must": {"term": {"&model": "MarketAbuseAlert"}}}},
                "aggs": {
                    "STATUS": {
                        "terms": {"field": "workflow.status"},
                        "aggs": {"SCENARIO": {"cardinality": {"field": "scenarioId"}}},
                    },
                },
            },
            "WATCH_ALERTS": {
                "filter": {
                    "bool": {
                        "must": [
                            {"term": {"&model": "MarketAbuseAlert"}},
                            {"term": {"detail.marketAbuseReportType": self.params.report_type}},
                        ]
                    }
                },
                "aggs": {
                    "STATUS": {
                        "terms": {"field": "workflow.status"},
                        "aggs": {"SCENARIO": {"cardinality": {"field": "scenarioId"}}},
                    }
                },
            },
        }


class TimeSummaryAggs(SurveillanceContextSearchBase):
    def build_aggs(self):
        """Builds aggregations Participant Comms by hour, by weekday and then
        by data sources.

        1. HOUR_OF_DAY: histogram aggregation by hour of day
        1.1. DAY_OF_WEEK: histogram aggregation histogram by day of week
        1.1.1. DATA_SOURCE: term aggregation by client data source
        """
        return {
            "ALERTS_OVER_TIME": {
                "date_histogram": {
                    "field": "detected",
                    "fixed_interval": "1d",
                    "format": "yyyy-MM-dd",
                },
                "aggs": {
                    "COMM_ALERTS": {
                        "filter": {"bool": {"must": {"term": {"&model": "CommunicationAlert"}}}},
                        "aggs": {"STATUS": {"terms": {"field": "workflow.status"}}},
                    },
                    "TRADE_ALERTS": {
                        "filter": {"bool": {"must": {"term": {"&model": "OrderAlert"}}}},
                        "aggs": {"STATUS": {"terms": {"field": "workflow.status"}}},
                    },
                    "TRADE_MAR_ALERTS": {
                        "filter": {"bool": {"must": {"term": {"&model": "MarketAbuseAlert"}}}},
                        "aggs": {
                            "STATUS": {
                                "terms": {"field": "workflow.status"},
                                "aggs": {
                                    "SCENARIO": {"cardinality": {"field": "scenarioId"}},
                                    "TOP_ALERT_TYPE": {
                                        "terms": {
                                            "field": "detail.marketAbuseReportType",
                                            "size": 3,
                                        },
                                        "aggs": {
                                            "SCENARIO": {"cardinality": {"field": "scenarioId"}},
                                            "SAME_INSTRUMENT": {
                                                "filter": {
                                                    "bool": {
                                                        "must": {
                                                            "term": {
                                                                "hit.instrumentDetails.instrument."
                                                                "instrumentIdCode": self.params.instrument_id  # noqa: E501
                                                            }
                                                        }
                                                    }
                                                },
                                                "aggs": {
                                                    "ALERT_TYPE": {
                                                        "terms": {
                                                            "field": "detail.marketAbuseReportType"
                                                        },
                                                        "aggs": {
                                                            "SCENARIO": {
                                                                "cardinality": {
                                                                    "field": "scenarioId"
                                                                }
                                                            },
                                                        },
                                                    }
                                                },
                                            },
                                        },
                                    },
                                },
                            },
                        },
                    },
                    "WATCH_ALERTS": {
                        "filter": {
                            "bool": {
                                "must": [
                                    {"term": {"&model": "MarketAbuseAlert"}},
                                    {
                                        "term": {
                                            "detail.marketAbuseReportType": self.params.report_type
                                        }
                                    },
                                ]
                            }
                        },
                        "aggs": {
                            "STATUS": {
                                "terms": {"field": "workflow.status"},
                                "aggs": {"SCENARIO": {"cardinality": {"field": "scenarioId"}}},
                            }
                        },
                    },
                },
            }
        }


class SurveillanceRepository(RepoHelpersMixin):
    @classmethod
    def get_single_value(cls, items: dict) -> int:
        value = 0
        for item in nested_dict_get(items, "SAME_INSTRUMENT.COUNT.buckets") or ():
            value += item.get("SCENARIO", {}).get("value", 0)

        return value

    @classmethod
    def build_bucket_for_top_abuse(cls, items: [Any]) -> List[Any]:
        result = []
        for item in items:
            result.append(
                {
                    "key": item["key"],
                    "value": item.get("SCENARIO", {}).get("value", 0),
                    "same_instrument": cls.get_single_value(item),
                }
            )

        return result

    @classmethod
    def build_trade_specific_bucket(cls, items: dict) -> dict:
        counts = {status: 0 for status in AlertHitStatus.__members__}
        details = {status: [] for status in AlertHitStatus.__members__}

        for item in nested_dict_get(items, "TRADE_ALERTS.STATUS.buckets") or ():
            counts[item["key"]] += item["doc_count"]
            details[item["key"]] = [{"key": "tradeNonAbuseAlerts", "value": item["doc_count"]}]

        for item in nested_dict_get(items, "TRADE_MAR_ALERTS.STATUS.buckets") or ():
            counts[item["key"]] += item.get("SCENARIO", {}).get("value", 0)
            if len(details[item["key"]]) == 0:
                details[item["key"]] = [{"key": "tradeNonAbuseAlerts", "value": 0}]

            details[item["key"]] = details[item["key"]] + cls.build_bucket_for_top_abuse(
                item.get("TOP_ALERT_TYPE", {}).get("buckets", [])
            )

        return {
            "details": cls.aggregate_status_detail_build(details),
            **cls.aggregate_status_counts(counts),
        }

    @classmethod
    def build_bucket_for_market_abuse_alert(cls, items: dict, path: str) -> dict:
        counts = {status: 0 for status in AlertHitStatus.__members__}

        for item in nested_dict_get(items, path + ".buckets") or ():
            counts[item["key"]] += item.get("SCENARIO", {}).get("value", 0)

        return cls.aggregate_status_counts(counts)

    @classmethod
    def build_bucket(cls, items: dict, path: str) -> dict:
        counts = {status: 0 for status in AlertHitStatus.__members__}

        for item in nested_dict_get(items, path + ".buckets") or ():
            counts[item["key"]] += item["doc_count"]

        return cls.aggregate_status_counts(counts)

    @classmethod
    def build_aggregate_bucket_for_market_abuse_alert(
        cls, aggs_result: RawResult, bucket: str
    ) -> dict:
        counts = {status: 0 for status in AlertHitStatus.__members__}

        for item in aggs_result.iter_raw_bucket_agg(bucket):
            counts[item["key"]] += item.get("SCENARIO", {}).get("value", 0)

        return cls.aggregate_status_counts(counts)

    @classmethod
    def build_aggregate_bucket(cls, aggs_result: RawResult, bucket: str) -> dict:
        counts = {status: 0 for status in AlertHitStatus.__members__}

        for item in aggs_result.iter_raw_bucket_agg(bucket):
            counts[item["key"]] += item["doc_count"]

        return cls.aggregate_status_counts(counts)

    @classmethod
    def aggregate_status_detail_merge(cls, items: List[Any]) -> List[Any]:
        result = {item["key"]: {"value": 0, "same_instrument": 0} for item in items}
        for item in items:
            result[item["key"]]["value"] += item.get("value", 0)
            result[item["key"]]["same_instrument"] += item.get("same_instrument", 0)

        final_result = []
        for key, value in result.items():
            temp = {
                "key": key,
                "value": value.get("value", 0),
                "same_instrument": value.get("same_instrument", 0),
            }
            final_result.append(temp)

        return final_result

    @classmethod
    def aggregate_status_detail_build(cls, details: dict) -> dict:
        return {
            "unresolved": cls.aggregate_status_detail_merge(
                details.get(AlertHitStatus.UNRESOLVED.value, [])
            ),
            "underInvestigation": cls.aggregate_status_detail_merge(
                details.get(AlertHitStatus.UNDER_INVESTIGATION.value, [])
                + details.get(AlertHitStatus.IN_PROGRESS.value, [])
                + details.get(AlertHitStatus.IN_REVIEW.value, [])
                + details.get(AlertHitStatus.ESCALATED.value, [])
            ),
            "resolved": cls.aggregate_status_detail_merge(
                details.get(AlertHitStatus.RESOLVED.value, [])
                + details.get(AlertHitStatus.RESOLVED_WITH_INVESTIGATION.value, [])
                + details.get(AlertHitStatus.RESOLVED_WITH_DISMISSAL.value, [])
                + details.get(AlertHitStatus.RESOLVED_WITH_BREACH.value, [])
                + details.get(AlertHitStatus.RESOLVED_WITH_INVESTIGATION_WITH_BREACH.value, [])
            ),
        }

    @classmethod
    def aggregate_status_counts(cls, counts: dict) -> dict:
        return {
            "unresolved": counts.get(AlertHitStatus.UNRESOLVED.value, 0),
            "underInvestigation": (
                counts.get(AlertHitStatus.UNDER_INVESTIGATION.value, 0)
                + counts.get(AlertHitStatus.IN_PROGRESS.value, 0)
                + counts.get(AlertHitStatus.IN_REVIEW.value, 0)
                + counts.get(AlertHitStatus.ESCALATED.value, 0)
            ),
            "resolved": (
                counts.get(AlertHitStatus.RESOLVED.value, 0)
                + counts.get(AlertHitStatus.RESOLVED_WITH_INVESTIGATION.value, 0)
                + counts.get(AlertHitStatus.RESOLVED_WITH_DISMISSAL.value, 0)
                + counts.get(AlertHitStatus.RESOLVED_WITH_BREACH.value, 0)
                + counts.get(AlertHitStatus.RESOLVED_WITH_INVESTIGATION_WITH_BREACH.value, 0)
            ),
        }

    async def get_active_watch_ids(self) -> List[str]:
        agg_result = await self.repo.get_aggs(search_model_cls=WatchesIdAggs)

        return [item["key"] for item in agg_result.iter_raw_bucket_agg("WATCH_IDS")]

    async def get_alert_summary_for_entity(self, params: dict) -> dict:
        params["watch_ids"] = await self.get_active_watch_ids()
        aggs_result = await self.repo.get_aggs(search_model=AlertSummaryAgg(**params))
        tradeSurveillance = self.build_trade_specific_bucket(aggs_result.aggregations)
        tradeSurveillance.pop("details")

        return {
            "watch": self.build_aggregate_bucket_for_market_abuse_alert(
                aggs_result, "WATCH_ALERTS.STATUS"
            ),
            "commSurveillance": self.build_aggregate_bucket(aggs_result, "COMM_ALERTS.STATUS"),
            "tradeSurveillance": tradeSurveillance,
        }

    async def get_time_summary_for_entity(self, params: dict) -> List[Any]:
        params["watch_ids"] = await self.get_active_watch_ids()
        agg_result = await self.repo.get_aggs(search_model=TimeSummaryAggs(**params))

        result = []
        for item in agg_result.iter_raw_bucket_agg("ALERTS_OVER_TIME"):
            result.append(
                {
                    "key": item["key_as_string"],
                    "watch": self.build_bucket_for_market_abuse_alert(item, "WATCH_ALERTS.STATUS"),
                    "commSurveillance": self.build_bucket(item, "COMM_ALERTS.STATUS"),
                    "tradeSurveillance": self.build_trade_specific_bucket(item),
                }
            )

        return result
