# type: ignore
class TableNotFound(Exception):
    def __init__(self, message):
        # Call the base class constructor with the parameters it needs
        super().__init__(message)


class InvalidInput(Exception):
    def __init__(self, message):
        # Call the base class constructor with the parameters it needs
        super().__init__(message)


class WorkflowScheduleError(Exception):
    """raise when error sending schedule to conductor."""

    pass
