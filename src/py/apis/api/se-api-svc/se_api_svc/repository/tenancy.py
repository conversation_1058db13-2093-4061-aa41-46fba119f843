from api_sdk.exceptions import MoreThanOneRecordError, TooManyActiveTenantConfigurations
from api_sdk.repository.asyncronous.request_bound import RepoHelpersMixin, TenantConfiguration


class TenantConfigurationRepository(RepoHelpersMixin):  # type: ignore[misc]
    async def get(self):
        try:
            return await self.repo.get_one(TenantConfiguration)
        except MoreThanOneRecordError:
            raise TooManyActiveTenantConfigurations()
