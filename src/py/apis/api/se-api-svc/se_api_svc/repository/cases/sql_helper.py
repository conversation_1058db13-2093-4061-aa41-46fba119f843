# type: ignore
import logging
import sqlalchemy
from fastapi import <PERSON><PERSON>P<PERSON>x<PERSON>
from psycopg2.errors import UndefinedTable
from se_api_svc.repository.errors import InvalidInput, TableNotFound
from sqlalchemy import and_, asc, desc
from sqlalchemy.orm import class_mapper
from typing import Dict, List, Optional

log = logging.getLogger(__name__)


def model_to_dict(model):
    columns = [c.key for c in class_mapper(model.__class__).columns]
    return {c: getattr(model, c) for c in columns}


def convert_filter_to_sqlalchemy(filter_dict):
    field = filter_dict["field"]
    operation = filter_dict["op"]
    value = filter_dict["value"]

    if operation == "eq":
        return field == value
    elif operation == "gt":
        return field > value
    elif operation == "lt":
        return field < value
    elif operation == "in":
        return field.in_(value)
    elif operation == "notin":
        return field.not_in(value)


class SQLHelpersMixin:
    def get_sort(self, sort: str = None):
        sort_by = None
        if sort:
            sort_info = sort.split(":")
            sort_field_name, sort_order_name = (
                sort_info[0],
                (sort_info[1] if len(sort_info) > 1 else (sort_info, None)),
            )
            if sort_order_name not in ["asc", "desc"]:
                sort_order = asc
            else:
                sort_order = asc if sort_order_name == "asc" else desc

            sort_field = getattr(self.relation, sort_field_name, None)
            if sort_field:
                sort_by = sort_order(sort_field)
            return sort_by
        return None

    @staticmethod
    def get_filters(filters):
        sql_filters = and_()

        if filters:
            for f in filters:
                sql_filters = and_(sql_filters, convert_filter_to_sqlalchemy(f))

        return sql_filters

    def get_many(
        self,
        filters: List[Dict] = None,
        limit: Optional[int] = None,
        offset: Optional[int] = 0,
        options: List = [],
        sort_by=None,
    ):
        if not options:
            options = []

        try:
            with self.db.session(self.schema) as db:
                sql_filters = self.get_filters(filters)
                query = (
                    db.query(self.relation)
                    .options(*options)
                    .filter(sql_filters)
                    .order_by(sort_by)
                    .limit(limit)
                    .offset(offset)
                )

                return list(query)
        except UndefinedTable:
            raise Exception("Table not found")
        except sqlalchemy.exc.ProgrammingError as e:
            if "UndefinedTable" in str(e):
                str_err = str(e.orig)
                str_err = str_err[: str_err.find("\n")].replace('"', "'")
                raise TableNotFound(str_err)
            else:
                raise e

    def get_one(self, id: str):
        try:
            with self.db.session(self.schema) as db:
                return db.query(self.relation).filter(self.relation.id == id).one()
        except UndefinedTable:
            raise Exception("Table not found")
        except sqlalchemy.exc.ProgrammingError as e:
            if "UndefinedTable" in str(e):
                str_err = str(e.orig)
                str_err = str_err[: str_err.find("\n")].replace('"', "'")
                raise TableNotFound(str_err)
            else:
                raise e
        except sqlalchemy.exc.DataError as e:
            params = [v for k, v in e.params.items()]
            raise InvalidInput(f"Invalid input {params}")

    def add_one(self, record):
        try:
            with self.db.session(self.schema) as db:
                db.add(record)
                db.flush()
                new_record_id = record.id
                db.commit()
            return new_record_id
        except UndefinedTable:
            raise Exception("Table not found")
        except sqlalchemy.exc.ProgrammingError as e:
            if "UndefinedTable" in str(e):
                str_err = str(e.orig)
                str_err = str_err[: str_err.find("\n")].replace('"', "'")
                raise TableNotFound(str_err)
            else:
                raise e
        except sqlalchemy.exc.DataError as e:
            params = [v for k, v in e.params.items()]
            raise InvalidInput(f"Invalid input {params}")

    def bulk_add(self, records: List):
        try:
            with self.db.session(self.schema) as db:
                db.bulk_save_objects(records, return_defaults=True)
                db.commit()
                db.flush()
                return records
        except UndefinedTable:
            raise Exception("Table not found")
        except sqlalchemy.exc.ProgrammingError as e:
            if "UndefinedTable" in str(e):
                str_err = str(e.orig)
                str_err = str_err[: str_err.find("\n")].replace('"', "'")
                raise TableNotFound(str_err)
            else:
                raise e
        except sqlalchemy.exc.DataError as e:
            params = [v for k, v in e.params.items()]
            raise InvalidInput(f"Invalid input {params}")

    def get_count(
        self,
        filters: List[Dict] = None,
        limit: Optional[int] = None,
        offset: Optional[int] = None,
    ):
        try:
            with self.db.session(self.schema) as db:
                sql_filters = self.get_filters(filters)
                query = db.query(self.relation).filter(sql_filters).limit(limit).offset(offset)
                return query.count()
        except UndefinedTable:
            raise Exception("Table not found")
        except sqlalchemy.exc.ProgrammingError as e:
            if "UndefinedTable" in str(e):
                str_err = str(e.orig)
                str_err = str_err[: str_err.find("\n")].replace('"', "'")
                raise TableNotFound(str_err)
            else:
                raise e

    def get_many_with_counts(
        self,
        filters: List[Dict] = None,
        limit: Optional[int] = None,
        offset: Optional[int] = 0,
        options: List = None,
        sort: str = None,
    ):
        sort_by = self.get_sort(sort)
        try:
            records = self.get_many(
                limit=limit,
                offset=offset,
                filters=filters,
                options=options,
                sort_by=sort_by,
            )
            total_count = self.get_count(filters=filters)
            return records, total_count, offset
        except TableNotFound as e:
            raise HTTPException(404, detail=f"tenant or table not found: {e}")
        except InvalidInput as e:
            raise HTTPException(404, detail=str(e))
        except Exception as e:
            log.error(str(e))
            raise HTTPException(500, detail="record not found")

    def remove_record(self, id):
        try:
            with self.db.session(self.schema) as db:
                db.query(self.relation).filter(self.relation.id == id).delete()
                db.commit()
        except UndefinedTable:
            raise Exception("Table not found")
        except sqlalchemy.exc.ProgrammingError as e:
            if "UndefinedTable" in str(e):
                str_err = str(e.orig)
                str_err = str_err[: str_err.find("\n")].replace('"', "'")
                raise TableNotFound(str_err)
            else:
                raise e
        except sqlalchemy.exc.DataError as e:
            params = [v for k, v in e.params.items()]
            raise InvalidInput(f"Invalid input {params}")
