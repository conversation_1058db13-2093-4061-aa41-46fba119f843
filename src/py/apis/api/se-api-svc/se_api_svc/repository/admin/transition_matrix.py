import openpyxl
import pandas as pd
from openpyxl.styles import Alignment, Border, Font, PatternFill, Side
from typing import List


def get_status(restriction):
    """Function to get the status based on the restriction type.

    Args:
        restriction (str): The restriction type.
    Returns:
        str: The status corresponding to the restriction type.
    """
    if restriction == "UNRESTRICTED":
        return "Allowed"
    elif restriction == "PROHIBITED":
        return "Not Allowed"
    elif restriction == "CONDITIONAL":
        return "Conditionally Allowed"


def create_matrix_workbook(existing_excel):
    """Function to create a workbook and a worksheet titled "Workflows Matrix".

    Args:
        existing_excel (file): The existing excel file.
    Returns:
        tuple: The workbook and the worksheet.
    """
    existing_excel.seek(0)
    wb = openpyxl.load_workbook(existing_excel)
    ws = wb.create_sheet(title="Workflows Matrix")
    return wb, ws


def format_headers(ws, headers):
    """Function to format the headers of the worksheet.

    Args:
        ws (Worksheet): The worksheet.
        headers (list): The headers to be formatted.
    """
    header_fill = PatternFill(start_color="333333", end_color="333333", fill_type="solid")
    font_white = Font(color="FFFFFF", bold=True)
    center_align = Alignment(horizontal="center", vertical="center")

    ws.append(headers)
    for col in range(1, len(headers) + 1):
        cell = ws.cell(row=1, column=col)
        cell.fill = header_fill
        cell.font = font_white
        cell.alignment = center_align


def add_matrix_data_to_worksheet(ws, matrix_data):
    """Function to add matrix data to the worksheet.

    Args:
        ws (Worksheet): The worksheet.
        matrix_data (dict): The matrix data to be added.
    """
    allowed_fill = PatternFill(start_color="90EE90", end_color="00FF00", fill_type="solid")
    not_allowed_fill = PatternFill(start_color="FF7F7F", end_color="FF0000", fill_type="solid")
    conditional_allowed_fill = PatternFill(
        start_color="FFFF99", end_color="FFFF99", fill_type="solid"
    )
    font_black = Font(color="000000", bold=True)
    center_align = Alignment(horizontal="center", vertical="center")
    thin_border = Border(
        left=Side(style="thin"),
        right=Side(style="thin"),
        top=Side(style="thin"),
        bottom=Side(style="thin"),
    )

    row_num = 2
    for from_transition, to_transitions in matrix_data.items():
        start_row = row_num
        ws.cell(row=row_num, column=1, value=from_transition)

        for index, transition in enumerate(to_transitions.index):
            ws.cell(row=row_num + index, column=2, value=transition)

        col_num = 3
        for to_transition, value in to_transitions.items():
            temp_row_num = row_num
            ws.cell(row=temp_row_num, column=col_num, value=to_transition)
            for i, v in enumerate(list(value)):
                if v == "Allowed":
                    fill_type = allowed_fill
                elif v == "Conditionally Allowed":
                    fill_type = conditional_allowed_fill
                else:
                    fill_type = not_allowed_fill

                ws.cell(row=temp_row_num, column=col_num, value=v).font = font_black
                ws.cell(row=temp_row_num, column=col_num, value=v).fill = fill_type
                ws.cell(row=temp_row_num, column=col_num, value=v).border = thin_border
                temp_row_num += 1
            col_num += 1

        end_row = row_num + len(to_transitions.index)
        ws.merge_cells(start_row=start_row, end_row=end_row, start_column=1, end_column=1)
        merged_cell = ws.cell(row=start_row, column=1)
        merged_cell.alignment = center_align
        row_num += len(to_transitions.index) + 1


def adjust_column_widths(ws):
    """Function to adjust the widths of the columns in the worksheet.

    Args:
        ws (Worksheet): The worksheet.
    """
    for col in ws.columns:
        max_length = 0
        column = col[0].column_letter
        for cell in col:
            try:
                if len(str(cell.value)) > max_length:
                    max_length = len(cell.value)
            except Exception:
                pass
        adjusted_width = max_length + 2
        ws.column_dimensions[column].width = adjusted_width


def save_workbook(wb, existing_excel):
    """Function to save the workbook.

    Args:
        wb (Workbook): The workbook.
        existing_excel (file): The existing excel file.
    Returns:
        file: The saved excel file.
    """
    existing_excel.seek(0)
    wb.save(existing_excel)
    existing_excel.seek(0)
    return existing_excel


def append_workflow_matrix_sheet(existing_excel, matrix_data):
    """Function to append a workflow matrix sheet to the existing excel file.

    Args:
        existing_excel (file): The existing excel file.
        matrix_data (dict): The matrix data to be added.
    Returns:
        file: The updated excel file.
    """
    wb, ws = create_matrix_workbook(existing_excel)
    workflow_names = next(iter(matrix_data.values())).keys() if matrix_data else []
    headers = ["From Transition", "To Transition"] + list(workflow_names)
    format_headers(ws, headers)
    add_matrix_data_to_worksheet(ws, matrix_data)
    adjust_column_widths(ws)
    return save_workbook(wb, existing_excel)


def format_selectable_sheets(workbook, sheet_names=None):
    """Function to apply formatting to selectable sheets in a workbook.

    Args:
        workbook (Workbook): The workbook.
        sheet_names (list, optional): The names of the sheets to be formatted. If not provided, all sheets will be formatted.
    """  # noqa: E501

    workbook.seek(0)
    wb = openpyxl.load_workbook(workbook)
    header_fill = PatternFill(start_color="333333", end_color="333333", fill_type="solid")
    font_white = Font(color="FFFFFF", bold=True)
    center_align = Alignment(horizontal="center", vertical="center")

    for sheet in wb.worksheets:
        if sheet_names is None or sheet.title in sheet_names:
            headers = [cell.value for cell in sheet[1]]
            format_headers(sheet, headers)

            for cell in sheet[1]:
                cell.fill = header_fill
                cell.font = font_white
                cell.alignment = center_align


def transform_matrix_data_to_df(matrix_data: List):
    """Function to transform matrix data to a pandas DataFrame.

    Args:
        matrix_data (list): The matrix data.
    Returns:
        DataFrame: The transformed matrix data.
    """
    workflow_names = sorted(set([row[0] for row in matrix_data]))
    transitions = sorted(set([row[2] for row in matrix_data]))

    df = pd.DataFrame(index=transitions, columns=workflow_names)

    for row in matrix_data:
        workflow, _, transition, restriction = row
        df.at[transition, workflow] = get_status(restriction)

    df = df.fillna("Not Allowed")

    return df
