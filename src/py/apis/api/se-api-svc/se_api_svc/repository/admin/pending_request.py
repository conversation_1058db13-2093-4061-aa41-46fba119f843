import logging
from api_sdk.auth import Tenancy
from api_sdk.models.search import Search<PERSON><PERSON><PERSON>, SearchResultHeader
from api_sdk.schemas.pending_request import PendingRequestStatus
from se_api_svc.repository.database import DatabaseRepoMixin  # type: ignore[attr-defined]
from sql_query_builder.repository.mixin import RepoHelpersMixin
from sql_query_builder.repository.request_bound import BoundRepository
from sqlalchemy import Column, func, select, update
from tenant_db.models.roles_permissions.pending_request import PendingRequest
from tenant_db.models.roles_permissions.permission import Permission  # type: ignore
from typing import Any, Optional

log = logging.getLogger(__name__)


class PendingRequestRepo(RepoHelpersMixin, DatabaseRepoMixin):  # type: ignore[misc]
    def __init__(self, repo: BoundRepository, tenancy: Tenancy):
        super().__init__(repo=repo, tenancy=tenancy)
        self.sql_client = self.repo.sql_client
        self.session_factory = self.repo.session_factory

    def get_requests(
        self,
        skip: int,
        take: int,
        sort: Optional[str] = None,
        module: Optional[str] = None,
        status: Optional[PendingRequestStatus] = None,
        user_id: Optional[str] = None,
    ):
        # ToDo: Change this schema style
        with self.sql_client.db.session(tenant_schema=self.tenancy.tenant) as session:
            query = select(
                [*list(PendingRequest.__table__.c), Permission.name.label("permission")]
            ).join(Permission, Permission.id == PendingRequest.permissionId)
            query = query.where(PendingRequest.module == module) if module else query  # type: ignore
            query = query.where(PendingRequest.status == status.name) if status else query  # type: ignore
            query = query.where(PendingRequest.userId == user_id) if user_id else query  # type: ignore
            count = session.execute(select(func.count()).select_from(query.subquery())).scalar()  # type: ignore

            if sort and ":" in sort:
                sort_field, sort_order = sort.split(":")
                sort_field_col: Column | Any = getattr(PendingRequest, sort_field, None)
                if sort_field:
                    query = (
                        query.order_by(sort_field_col.asc())  # type: ignore
                        if sort_order == "asc"
                        else query.order_by(sort_field_col.desc())  # type: ignore
                    )

            query = query.order_by(PendingRequest.createdDateTime.desc())  # type: ignore
            query = query.offset(skip).limit(take)  # type: ignore
            result = list(session.execute(query))

            return SearchResult(
                header=SearchResultHeader(returnedHits=len(result), totalHits=count, offset=skip),
                results=[dict(row._mapping) for row in result],
            )

    def reject_request(self, request_id: str):
        # ToDo: Change this schema style
        with self.sql_client.db.session(tenant_schema=self.tenancy.tenant) as session:
            query = (
                update(PendingRequest)  # type: ignore
                .where(
                    PendingRequest.id == request_id,
                    PendingRequest.status == PendingRequestStatus.PENDING.name,
                )
                .values(status=PendingRequestStatus.REJECTED.name, updatedBy=self.tenancy.userId)
            )
            session.execute(query)
            session.commit()
