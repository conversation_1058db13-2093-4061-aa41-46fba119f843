# Quarantine feature

For certain models, when a record with the same ID is ingested repeatedly, it may be that it is an exact replica,
it could be an update, or it could be a problem upstream.

Quarantine feature allows to flag the problematic cases by creating special records that store these duplicates
without overwriting the existing records.

Quarantine feature creates a matching `<QuarantinedModel>` record and a `SinkRecordAudit` record.
The name of the `<QuarantinedModel>` class is the name of the duplicated record model class prepended with
`Quarantined`. So, for `Order` model, it is `QuarantinedOrder`.

The persisted `<QuarantinedModel>` record stores the values found in the new record which was not ingested.
`SinkRecordAudit` links the instances of `<QuarantinedModel>` with their corresponding `<Model>` instances.

```
    SinkRecordAudit
        .matchedKey --> points to the &key of the matched record (the current source of truth)
        .recordKey --> points to the quarantined record (of <QuarantinedModel>) that duplicates the matched one
        .&id -- does NOT match &id of the matched record or the quarantined record 
```

Quarantine handler allows doing two things with a `SinkRecordAudit` record: *process it* or *archive it*.

*To archive* the record means to:

 1. delete the `<QuarantinedModel>` record.
 2. update `<SinkRecordAudit>` record:
    1. create a copy of the record, set `status`, `userProcessed`, `processed`, index it
    2. create a parent-child relationship between this new record and the previous `SinkRecordAudit` record

*To process* the record means to:

 1. Update the matched record from the quarantined record
 2. archive quarantine record
 3. update `<SinkRecordAudit>` record.
