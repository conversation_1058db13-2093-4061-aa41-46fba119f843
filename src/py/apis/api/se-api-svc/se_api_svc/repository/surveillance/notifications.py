# type: ignore
import logging
from api_sdk.auth import Tenancy
from api_sdk.exceptions import NotFound
from api_sdk.services.email import AbstractEmailService
from generator.notification_email_generator import NotificationEmailGenerator
from se_api_svc.repository.surveillance.settings import NotificationSettingsRepository
from se_api_svc.repository.surveillance.watches import WatchesRepository
from se_api_svc.schemas.surveillance.notification_settings import NotificationSetting
from se_api_svc.schemas.surveillance.watches import Watch
from typing import List

log = logging.getLogger(__name__)


class Notifications:
    def __init__(
        self,
        tenancy: Tenancy,
        watches: WatchesRepository,
        settings: NotificationSettingsRepository,
        email: AbstractEmailService,
    ):
        self.tenancy = tenancy
        self.watches = watches
        self.settings = settings
        self.email = email
        self.generator = NotificationEmailGenerator(tenancy.realm)

    async def notify_watch_event(self, watch: Watch, settingType: NotificationSetting.SettingType):
        settings = await self.get_relevant_settings(watch, settingType)
        if not settings:
            log.info("No relevant notification settings found")
            return

        recipients = set()
        for setting in settings:
            if setting.notificationEmails:
                recipients.update(setting.notificationEmails)

        if not recipients:
            log.info("No recipients found in notification settings")
            return

        message = self.generate_watch_event_message(watch, settingType)
        self.email.send_raw(message, to=sorted(recipients))
        log.info(f"Notification Sent to {recipients} for {settingType.value} {watch.name}")

    async def get_relevant_settings(
        self, watch: Watch, settingType: NotificationSetting.SettingType
    ) -> List[NotificationSetting]:
        module = watch.get_settings_module()
        settings = []

        if not module:
            return settings

        try:
            settings.append(
                await self.settings.get_module_setting(
                    module=module,
                    id=NotificationSettingsRepository.id_for(
                        module,
                        settingType,
                        NotificationSetting.Option.BY_ANYONE,
                    ),
                )
            )
        except NotFound:
            pass

        try:
            settings.append(
                await self.settings.get_module_setting(
                    module=module,
                    id=NotificationSettingsRepository.id_for(
                        module,
                        settingType,
                        (
                            NotificationSetting.Option.BY_ADMIN
                            if watch.createdByAdmin
                            else NotificationSetting.Option.BY_NON_ADMIN
                        ),
                    ),
                )
            )
        except NotFound:
            pass

        return settings

    def generate_watch_event_message(
        self, watch: Watch, settingType: NotificationSetting.SettingType
    ):
        key_message = settingType.value.replace("_", " ").lower().title()
        return self.generator.generate_email(
            message=f"<p>{key_message}</p>",
            subject=f"SteelEye Surveillance {key_message}",
            header="SteelEye Surveillance Watch Notification",
            link_txt="View Watch",
            link_url=self.watches.get_iris_url_for(watch),
        )
