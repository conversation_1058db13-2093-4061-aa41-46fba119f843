import datetime as dt
from api_sdk.es_dsl.base import <PERSON><PERSON><PERSON><PERSON>, NotExpired, SearchModel, TermFilter
from api_sdk.es_dsl.flang import FlangFilter
from api_sdk.es_dsl.params import SearchModelParams
from api_sdk.repository.asyncronous.request_bound import RepoHelpersMixin
from se_api_svc.core.constants import ES_MAX_AGG_SIZE
from se_elastic_schema.components.surveillance.surveillance_lexicon import SurveillanceLexicon
from typing import List, Optional, Union

LEXICON_CATEGORY_AGG_KEY = "LEXICON_CATEGORY"


class LexiconSearchBase(SearchModel):
    class Params(SearchModelParams):
        f: Optional[str]
        start: Union[dt.datetime, dt.date] = None
        end: Union[dt.datetime, dt.date] = None
        search: Optional[str]
        categories: Optional[List[str]] = None

    params: Params

    features = [
        ModelFilter(model=SurveillanceLexicon),
        NotExpired,
        FlangFilter.simple(param="f"),
        Term<PERSON>ilter(name="category", param="categories"),
    ]


class LexiconCategoryAggs(LexiconSearchBase):
    """Builds aggregation query to get Lexicon categories sorted by ascending
    order of name."""

    def build_aggs(self):
        return {
            LEXICON_CATEGORY_AGG_KEY: {
                "terms": {"field": "category", "order": {"_key": "asc"}, "size": ES_MAX_AGG_SIZE}
            }
        }


class LexiconRepository(RepoHelpersMixin):
    async def get_lexicon(self, id):
        return await self.get_one(SurveillanceLexicon, id)

    async def get_lexicons(self, **search_params):
        return await self.get_many(search_model_cls=LexiconSearchBase, **search_params)

    async def get_categories(self, **search_params):
        aggs_result = await self.get_aggs(search_model_cls=LexiconCategoryAggs, **search_params)

        return aggs_result.iter_bucket_agg(LEXICON_CATEGORY_AGG_KEY)
