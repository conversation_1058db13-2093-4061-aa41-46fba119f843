resources(
    name="rscs",
    sources=["config.yml"],
)

python_sources(name="lib", sources=["reference_api_svc/**/*.py"], dependencies=[":rscs"])

python_sources(
    name="reference-api-svc0",
    sources=["*.py"],
)

python_test_utils(
    name="conftest", sources=["tests_reference_api_svc/conftest.py"], dependencies=[":rscs"]
)

python_tests(
    name="tests",
    sources=["tests_reference_api_svc/**/test_*.py"],
    dependencies=[":conftest", ":rscs"],
)

se_image(
    image_name="intelligence-reference-api-svc",
    pex_entry_point="reference_api_svc/main.py",
)
