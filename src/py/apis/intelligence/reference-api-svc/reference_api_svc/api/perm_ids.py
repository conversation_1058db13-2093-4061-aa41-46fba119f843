from dependency_injector.wiring import Provide, inject
from fastapi import APIRouter, Depends
from reference_api_svc.containers import Container
from reference_api_svc.repository.perm_ids import PermIDsRepo
from reference_api_svc.schemas.perm_ids import PermIDsInput, PermIDsQuery

perm_ids_router = APIRouter()


@perm_ids_router.get("/{id_type}/{instrument_id}")
@inject
def get_perm_id(
    instrument_id: str, id_type: str, repo: PermIDsRepo = Depends(Provide[Container.perm_ids])
):
    model = PermIDsInput(id=instrument_id, id_type=id_type)
    return repo.get_perm_id(model=model)


@perm_ids_router.post("")
@inject
def bulk_fetch_perm_ids(
    query: PermIDsQuery, repo: PermIDsRepo = Depends(Provide[Container.perm_ids])
):
    return repo.bulk_fetch_perm_ids(query=query)
