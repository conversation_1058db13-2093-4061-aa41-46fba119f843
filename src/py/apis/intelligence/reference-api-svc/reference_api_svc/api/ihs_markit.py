from dependency_injector.wiring import Provide, inject
from fastapi import APIRouter, Depends
from reference_api_svc.containers import Container
from reference_api_svc.repository.ihs_markit import IHSMarkitRepo
from typing import List

ihs_markit_router = APIRouter()


@ihs_markit_router.get("/redcodes/{iuid}")
@inject
def get_redcodes(
    iuid: str,
    repo: IHSMarkitRepo = Depends(Provide[Container.ihs_markit]),
):
    return repo.get_redcodes(iuid=iuid)


@ihs_markit_router.get("/lxids/{iuid}")
@inject
def get_lxids(
    iuid: str,
    repo: IHSMarkitRepo = Depends(Provide[Container.ihs_markit]),
):
    return repo.get_lxids(iuid=iuid)


@ihs_markit_router.post("/redcodes")
@inject
def bulk_redcodes(
    iuids: List[str],
    repo: IHSMarkitRepo = Depends(Provide[Container.ihs_markit]),
):
    return repo.bulk_redcodes(iuids=iuids)


@ihs_markit_router.post("/lxids")
@inject
def bulk_lxids(
    iuids: List[str],
    repo: IHSMarkitRepo = Depends(Provide[Container.ihs_markit]),
):
    return repo.bulk_lxids(iuids=iuids)
