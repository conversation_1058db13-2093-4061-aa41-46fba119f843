import boto3
import pytest
import sqlalchemy.orm
from unittest.mock import <PERSON><PERSON>ock


@pytest.fixture
def mock_db(monkeypatch):
    m = MagicMock()

    def get_sesh(*args, **kwargs):
        return m

    monkeypatch.setattr(sqlalchemy.orm, "scoped_session", get_sesh)
    monkeypatch.setattr(sqlalchemy.orm, "sessionmaker", get_sesh)
    return m


@pytest.fixture
def mock_config():
    conf = {"kafka": {"topic": "mock_master_data_events"}}
    return conf


@pytest.fixture(autouse=True)
def mock_boto(monkeypatch):
    f = MagicMock()
    f.get_parameter.return_value = {
        "Parameter": {"Value": ("postgresql://username:<EMAIL>/db_name")}
    }

    def get_fake_client(*args, **kwargs):
        return f

    monkeypatch.setattr(
        boto3,
        "client",
        get_fake_client,
    )
    return f


@pytest.fixture(autouse=True)
def mock_boto_session(monkeypatch):
    f = MagicMock()

    def get_fake_session(*args, **kwargs):
        return f

    monkeypatch.setattr(
        boto3,
        "Session",
        get_fake_session,
    )
    return f
