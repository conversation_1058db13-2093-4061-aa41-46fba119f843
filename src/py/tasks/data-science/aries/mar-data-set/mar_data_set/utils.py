import logging
from copy import deepcopy
from hashlib import sha256
from mar_data_set.static import (
    _ANON_FIELDS,
    _SKIP_FIELDS,
    BASE_SOURCE_FIELDS,
)
from parquet_data_anonymization.encryption import generate_deterministic_id
from urllib.parse import urlparse


# -------------------------------
# Utility Functions
# -------------------------------
def hash_string(val: str) -> str:
    return sha256(val.encode()).hexdigest() if isinstance(val, str) and val else None


def get_nested(data, keys):
    for key in keys:
        if not isinstance(data, dict) or key not in data:
            return None
        data = data[key]
    return data


def set_nested(data, keys, value):
    for key in keys[:-1]:
        data = data.setdefault(key, {})
    data[keys[-1]] = value


# -------------------------------
# Anonymization Functions
# -------------------------------


def anonymise_data(d) -> dict:
    if not d:
        return d
    elif isinstance(d, str):
        return generate_deterministic_id(d)
    elif isinstance(d, bool):
        return d
    elif isinstance(d, dict):
        return {k: anonymise_data(v) if k not in _SKIP_FIELDS else v for k, v in d.items()}
    elif isinstance(d, list):
        return [anonymise_data(v) if v else None for v in d]
    return d


def anonymize_workflow_fields(workflow: dict) -> dict:
    updated_workflow = deepcopy(workflow)
    for field in ["assigneeId", "assigneeName", "resolvedById", "resolvedByName", "updatedBy"]:
        if field in updated_workflow:
            updated_workflow[field] = anonymise_data(updated_workflow[field])
    return updated_workflow


def anonymize_record(record: dict):
    """Applies anonymization to relevant nested fields."""
    if "hit" in record:
        record["hit"] = anonymize_hit_fields(record["hit"])
    if "workflow" in record:
        record["workflow"] = anonymize_workflow_fields(record["workflow"])
    return record


def anonymize_participant_list(participants):
    if isinstance(participants, str):
        return generate_deterministic_id(participants)
    elif isinstance(participants, dict):
        return anonymise_data(participants)
    elif isinstance(participants, list):
        return [
            {**p, **{f: anonymise_data(p.get(f)) for f in _ANON_FIELDS if f in p}}
            for p in participants
        ]
    return participants


ANONYMIZATION_MAP = {
    "seller": anonymize_participant_list,
    "buyer": anonymize_participant_list,
    "counterparty": anonymize_participant_list,
    "buyerDecisionMaker": anonymize_participant_list,
    "sellerDecisionMaker": anonymize_participant_list,
    "trader": anonymize_participant_list,
    "clientIdentifiers": anonymize_participant_list,
    "marketIdentifiers": anonymize_participant_list,
    "tradersAlgosWaiversIndicators.executionWithinFirm": anonymize_participant_list,
    "tradersAlgosWaiversIndicators.investmentDecisionWithinFirm": anonymize_participant_list,
    "tradersAlgosWaiversIndicators.investmentDecisionWithinFirmFileIdentifier": anonymize_participant_list,  # noqa: E501
    "tradersAlgosWaiversIndicators.executionWithinFirmFileIdentifier": anonymize_participant_list,
    "clientFileIdentifier": anonymize_participant_list,
    "traderFileIdentifier": anonymize_participant_list,
    "counterpartyFileIdentifier": anonymize_participant_list,
    "sellerFileIdentifier": anonymize_participant_list,
    "buyerFileIdentifier": anonymize_participant_list,
    "buyerDecisionMakerFileIdentifier": anonymize_participant_list,
    "sellerDecisionMakerFileIdentifier": anonymize_participant_list,
    "reportDetails.executingEntity": anonymize_participant_list,
}


def anonymize_hit_fields(hit_row: dict) -> dict:
    updated_hit = deepcopy(hit_row)
    for field_path, anon_fn in ANONYMIZATION_MAP.items():
        keys = field_path.split(".")
        original_value = get_nested(updated_hit, keys)
        if original_value is not None:
            try:
                anonymized_value = anon_fn(original_value)
                set_nested(updated_hit, keys, anonymized_value)
            except Exception as e:
                logging.warning(f"Failed to anonymize field '{field_path}': {e}")
                set_nested(updated_hit, keys, None)
    return updated_hit


def filter_fields(record: dict, fields_to_keep: list) -> dict:
    result = {}

    for field_path in fields_to_keep:
        keys = field_path.split(".")
        value = record
        for key in keys:
            if not isinstance(value, dict) or key not in value:
                value = None
                break
            value = value[key]

        if value is not None:
            current = result
            for key in keys[:-1]:
                current = current.setdefault(key, {})
            current[keys[-1]] = value

    return result


def normalize_record(record, all_fields, field_types):
    """
    Ensure record has all fields in all_fields,
    coerce value to match inferred type.
    Recursively handles nested structs & lists.
    """

    def coerce_value(value, typ):
        if value is None:
            return None
        if isinstance(typ, dict):
            if not isinstance(value, dict):
                return None
            return {k: coerce_value(value.get(k), v) for k, v in typ.items()}
        if isinstance(typ, list):
            if not isinstance(value, list):
                return None
            subtype = typ[1]
            return [coerce_value(v, subtype) for v in value]
        if typ == "int64":
            try:
                return int(value)
            except Exception:
                return None
        if typ == "float64":
            try:
                return float(value)
            except Exception:
                return None
        if typ == "bool":
            return bool(value)
        return str(value)

    return {f: coerce_value(record.get(f), field_types.get(f, "string")) for f in all_fields}


def infer_field_types(records):
    """
    Recursively infer field types for a batch of dicts.
    Supports int, float, bool, string, nested dict (struct), list.
    """
    field_types = {}

    def infer_value_type(value):
        if value is None:
            return None
        elif isinstance(value, bool):
            return "bool"
        elif isinstance(value, int):
            return "int64"
        elif isinstance(value, float):
            return "float64"
        elif isinstance(value, dict):
            if not value:  # Empty dict → treat as string
                return "string"
            return {k: infer_value_type(v) for k, v in value.items()}
        elif isinstance(value, list):
            # Assume homogeneous lists: infer first non-None
            for item in value:
                t = infer_value_type(item)
                if t is not None:
                    return ["list", t]
            return ["list", "string"]
        else:
            return "string"

    for r in records:
        for k, v in r.items():
            t = infer_value_type(v)
            if t is None:
                continue

            old = field_types.setdefault(k, t)
            if old == t:
                continue

            if isinstance(old, dict) and isinstance(t, dict):
                if not old and not t:
                    field_types[k] = "string"
                elif not old:
                    field_types[k] = t
                elif not t:
                    field_types[k] = old
                else:
                    merged = {}
                    for key in set(old) | set(t):
                        o = old.get(key)
                        n = t.get(key)
                        if o is None:
                            merged[key] = n
                        elif n is None:
                            merged[key] = o
                        elif o == n:
                            merged[key] = o
                        else:
                            merged[key] = "string"
                    field_types[k] = merged
            else:
                # Type conflict → fallback to string
                field_types[k] = "string"

    return field_types


def replace_tenant(file_uri: str, new_tenant: str) -> str:
    parsed = urlparse(file_uri)
    scheme = parsed.scheme
    netloc = parsed.netloc
    path = parsed.path

    # Replace only the first segment of the netloc (the tenant)
    parts = netloc.split(".")
    parts[0] = new_tenant
    new_netloc = ".".join(parts)

    # Reconstruct the URI
    return f"{scheme}://{new_netloc}{path}"


def get_final_source_fields(model: str, order_fields) -> list[str]:
    if model == "MarketAbuseAlert":
        hit_fields = [f"hit.{field}" for field in order_fields]
        return BASE_SOURCE_FIELDS + hit_fields
    else:
        return BASE_SOURCE_FIELDS


def ensure_field_type(record, field_path, cast_type=int, default=None):
    """
    Safely cast a nested field to the given type.
    Supports dot-separated nested keys.
    Example: ensure_field_type(r, "hit.buySell", int)
    """
    keys = field_path.split(".")
    d = record
    try:
        for k in keys[:-1]:
            d = d.get(k, {})
            if not isinstance(d, dict):
                return  # path broken
        last_key = keys[-1]
        val = d.get(last_key)
        if val is not None:
            try:
                if cast_type is bool:
                    # For bool, interpret common string representations
                    if isinstance(val, str):
                        val_lower = val.strip().lower()
                        if val_lower in ("true", "1", "yes"):
                            d[last_key] = True
                        elif val_lower in ("false", "0", "no"):
                            d[last_key] = False
                        else:
                            d[last_key] = bool(val)
                    else:
                        d[last_key] = bool(val)
                else:
                    d[last_key] = cast_type(val)
            except (ValueError, TypeError):
                d[last_key] = default
    except AttributeError:
        pass
