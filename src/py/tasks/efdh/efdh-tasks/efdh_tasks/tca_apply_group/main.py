from aries_io_event.io_param import IOParamFieldSet
from aries_io_event.task import TaskFieldSet
from aries_io_event.workflow import WorkflowFieldSet
from aries_task_link.models import AriesTaskInput, AriesTaskResult
from datetime import datetime
from efdh_tasks.tca_apply_group.tca_apply_group_task import TCAApplyGroup


def run_tca_apply_group(aries_task_input: AriesTaskInput) -> AriesTaskResult:
    """Aries task accepts a json of s3 uri's of structured refinitiv data,
    grouped by RIC per file to normalise to SE Parquet Schema.

    :param aries_task_input: file path which contains the parquet csvs paths to normalise
    """
    tca_apply_group = TCAApplyGroup(aries_task_input=aries_task_input)
    result: AriesTaskResult = tca_apply_group.execute()

    return result


if __name__ == "__main__":
    params = {
        "date_from": "2023-08-01T00:00:00",
        "date_to": "2023-08-02T00:00:00",
        "all_orders": True,
    }
    workflow = WorkflowFieldSet(
        trace_id="-fcqJMAA709jsNaDvp_i7",
        name="localtest",
        stack="uat-shared-steeleye",
        tenant="uat-shared-steeleye",
        start_timestamp=datetime(2024, 10, 25, 13, 27, 18, 806741),
    )
    input_param = IOParamFieldSet(params=params)
    input = AriesTaskInput(
        workflow=workflow,
        input_param=input_param,
        task=TaskFieldSet(
            id="foo",
            name="tca-metrics",
            version="1",
            success=True,
            previous_id=None,
        ),
    )
    run_tca_apply_group(aries_task_input=input)
