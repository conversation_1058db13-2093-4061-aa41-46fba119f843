import datetime
import logging
import polars as pl
from datetime import date
from market_data_utils.schema.parquet import EoDStatsColumns, QuoteTickColumns, TradeTickColumns
from polars.lazyframe.frame import Lazy<PERSON>rame
from se_market_data_utils.client import MarketDataAPI
from se_market_data_utils.parquet_handlers import (
    get_eod_stats,
    get_tick_parquet,
)
from se_market_data_utils.schema.refinitiv import RefinitivEventType
from tca_utils.schema.tca_market_data import TickColumns, TickDtypes
from tca_utils.schema.tca_metrics import Alias, TCAMarketData
from typing import Any, List, Optional

logger = logging.getLogger(__name__)


QUOTE_COLUMNS = [
    QuoteTickColumns.DATE_TIME,
    QuoteTickColumns.BID_PRICE,
    QuoteTickColumns.BID_SIZE,
    QuoteTickColumns.ASK_SIZE,
    QuoteTickColumns.ASK_PRICE,
    QuoteTickColumns.MID_PRICE,
]
TRADE_COLUMNS = [
    TradeTickColumns.DATE_TIME,
    TradeTickColumns.PRICE,
    TradeTickColumns.VOLUME,
    TradeTickColumns.RIC,
    TradeTickColumns.TRADE_PRICE_CURRENCY,
]

STATS_COLUMNS = [
    EoDStatsColumns.CLOSE_PRICE,
    EoDStatsColumns.OPEN_PRICE,
    EoDStatsColumns.DATE,
    EoDStatsColumns.RIC,
    EoDStatsColumns.VWAP,
    # EoDStatsColumns.PRICE_VOLATILITY,
    # EoDStatsColumns.VOLUME_VOLATILITY,
    EoDStatsColumns.VOLUME_EMA,
    EoDStatsColumns.CURRENCY,
    EoDStatsColumns.TRADE_VOLUME,
]


class Currencies:
    USD = "USD"
    INVERSE_CURRENCIES = ["AUD", "EUR", "GBP", "NZD"]


class MarketData:
    def __init__(self):
        self.md_client = MarketDataAPI()

    def execute(self, frame: pl.DataFrame, ric: str, venue: Optional[str]) -> TCAMarketData:
        dates = (
            frame.select(pl.col(Alias.DATE).filter(pl.col(Alias.DATE).is_not_null()))
            .get_column(Alias.DATE)
            .unique()
            .to_list()
        )
        order_received_dates = (
            frame.select(
                pl.col(Alias.ORDER_RECEIVED_DATE).filter(
                    pl.col(Alias.ORDER_RECEIVED_DATE).is_not_null()
                )
            )
            .get_column(Alias.ORDER_RECEIVED_DATE)
            .unique()
            .to_list()
        )
        dates.extend(order_received_dates)
        dates = list(set(dates))
        market_data = self._get_market_data(ric=ric, dates=dates, frame=frame, ric_venue=venue)

        quotes_empty = False
        trades_empty = False
        stats_empty = False
        if market_data.quotes.collect().is_empty():
            logger.info(f"QUOTES EMPTY FOR RIC {market_data.ric}")
            quotes_empty = True

        if market_data.trades.collect().is_empty():
            logger.info(f"TRADES EMPTY FOR RIC {market_data.ric}")
            trades_empty = True

        if market_data.stats.collect().is_empty():
            logger.info(f"STATS EMPTY FOR RIC {market_data.ric}")
            stats_empty = True

        market_data.no_market_data = (
            True if all([trades_empty, quotes_empty, stats_empty]) else False
        )

        return market_data

    def _get_market_data(
        self, ric: str, dates: List[date], frame: pl.DataFrame, ric_venue: Optional[str]
    ) -> TCAMarketData:
        """

        :param ric:
        :param dates:
        :return:
        """
        dates_series = pl.Series(dates)
        date_from = (dates_series.min() - datetime.timedelta(30)).strftime("%Y-%m-%d")  # type: ignore
        date_to = dates_series.max().strftime("%Y-%m-%d")  # type: ignore
        stats = self.fetch_stats(ric=ric, venue=ric_venue, date_from=date_from, date_to=date_to)

        trades = self.get_level_1_data(
            dates=dates,
            ric=ric,
            venue=ric_venue,
            event_type=RefinitivEventType.TRADE,
            columns=TRADE_COLUMNS,
        )

        quotes = self.get_level_1_data(
            dates=dates,
            ric=ric,
            event_type=RefinitivEventType.QUOTE,
            venue=ric_venue,
            columns=QUOTE_COLUMNS,
        )

        ric_ccy = self._get_refinitiv_currencies(
            orders=frame, trades=trades, eod_stats=stats, ric=ric
        )

        return TCAMarketData(trades=trades, quotes=quotes, stats=stats, ric=ric, currency=ric_ccy)

    def _get_refinitiv_currencies(
        self,
        orders: pl.DataFrame,
        trades: pl.LazyFrame,
        eod_stats: pl.LazyFrame,
        ric: str,
    ) -> Optional[Any]:
        """Refinitiv currency priority:

        1st: TCAMarket Data - If ccy is present in TCAMarket data update RIC_CURRENCY
        in orders and return
        2nd: Order's - `RIC_CCY` (taken from `.ricLookup` upstream)
        If more than one ric ccy then return None and the orders will be flagged with
         Invalid Currency
        :param orders:
        :param trades:
        :return:
        """
        ccy = []

        if (
            not eod_stats.collect().is_empty()
            and EoDStatsColumns.CURRENCY in eod_stats.collect_schema().names()
        ):
            ccy = (
                eod_stats.collect()
                .select(
                    [
                        pl.col(EoDStatsColumns.CURRENCY).filter(
                            pl.col(EoDStatsColumns.CURRENCY).is_not_null()
                        )
                    ]
                )
                .get_column(EoDStatsColumns.CURRENCY)
                .unique()
                .to_list()
            )
            # If more than one RIC ccy we return None and it is flagged as Invalid
            if len(ccy) > 1:
                logger.warning(f"Multiple currencies found in EoD Stats fileRIC: {ric}")
                return None
        if (
            not ccy
            and not trades.collect().is_empty()
            and TradeTickColumns.TRADE_PRICE_CURRENCY in trades.columns
        ):
            ccy = (
                trades.collect()
                .select(
                    [
                        pl.col(TradeTickColumns.TRADE_PRICE_CURRENCY).filter(
                            pl.col(TradeTickColumns.TRADE_PRICE_CURRENCY).is_not_null()
                        )
                    ]
                )
                .get_column(TradeTickColumns.TRADE_PRICE_CURRENCY)
                .unique()
                .to_list()
            )
            if len(ccy) > 1:
                logger.warning(f"Multiple currencies found in trade tick fileRIC: {ric}")
                return None

        if not ccy:
            ccy = (
                orders.select([pl.col(Alias.RIC_CCY).filter(pl.col(Alias.RIC_CCY).is_not_null())])
                .get_column(Alias.RIC_CCY)
                .unique()
                .to_list()
            )

        if len(ccy) == 1:
            return ccy[0]

        return None

    def get_level_1_data(
        self,
        dates: List[datetime.date],
        ric: str,
        event_type: RefinitivEventType,
        columns: List[str],
        venue: Optional[str],
    ) -> pl.LazyFrame:
        """

        :param dates:
        :param ric:
        :param event_type:
        :param columns:
        :param venue:
        :return:
        """
        results = []
        try:
            for market_data_frame in get_tick_parquet(
                ric=ric,
                market_client=self.md_client,
                event_type=event_type,
                dates=dates,
                columns=columns,
                enforce_schema=True,
            ):
                results.append(pl.from_arrow(market_data_frame).lazy())  # type: ignore
            result = pl.concat(results)
        except Exception:
            logger.warning(f"Error fetch tick parquets for {dates} and {ric}")
            return pl.LazyFrame()

        result = result.with_columns(
            pl.from_epoch(column=TradeTickColumns.DATE_TIME, time_unit="ns")
            .cast(pl.Date, strict=True)
            .alias(TickColumns.DATE),
            pl.lit(venue).alias(TickColumns.VENUE),
        )
        result = self.set_tick_dtypes(frame=result, event_type=event_type)
        result = result.sort("Date-Time")
        return result

    def fetch_stats(
        self, ric: str, venue: Optional[str], date_from: str, date_to: str
    ) -> LazyFrame:
        """

        :param ric:
        :param venue:
        :param date_from:
        :param date_to:
        :return:
        """
        data = get_eod_stats(
            ric=ric,
            date_from=date_from,
            date_to=date_to,
            market_client=self.md_client,
            columns=STATS_COLUMNS,
        )
        data = pl.from_arrow(data)
        if data.is_empty():
            return pl.LazyFrame()

        data = data.lazy()

        data = data.with_columns(pl.lit(venue).alias(EoDStatsColumns.VENUE))

        data = data.unique(subset=[EoDStatsColumns.DATE])
        data = data.with_columns(
            pl.col(EoDStatsColumns.DATE).str.strptime(pl.Date, strict=False).cast(pl.Date())
        )

        if EoDStatsColumns.CURRENCY not in data.collect_schema().names():
            data = data.with_columns(pl.lit(None).alias(EoDStatsColumns.CURRENCY))
            data = data.with_columns(pl.col(EoDStatsColumns.CURRENCY).cast(pl.Utf8))

        if EoDStatsColumns.TRADE_VOLUME not in data.collect_schema().names():
            data = data.with_columns(pl.lit(None).alias(EoDStatsColumns.TRADE_VOLUME))
            data = data.with_columns(pl.col(EoDStatsColumns.TRADE_VOLUME).cast(pl.Float64))

        data = data.with_columns(
            pl.when(pl.col(EoDStatsColumns.CURRENCY).is_in(["nan"]))
            .then(pl.lit(None))
            .otherwise(pl.col(EoDStatsColumns.CURRENCY))
            .alias(EoDStatsColumns.CURRENCY)
        )

        if EoDStatsColumns.VWAP not in data.collect_schema().names():
            data = data.with_columns(pl.lit(None).alias(EoDStatsColumns.VWAP))
        else:
            data = data.with_columns(
                pl.col(EoDStatsColumns.VWAP).cast(pl.Utf8).str.replace(",", "").cast(pl.Float64)
            )
        if EoDStatsColumns.VOLUME_EMA not in data.collect_schema().names():
            data = data.with_columns(
                pl.col(EoDStatsColumns.TRADE_VOLUME)
                .ewm_mean(com=20, alpha=0.7, min_periods=1)
                .alias(EoDStatsColumns.VOLUME_EMA)
            )
        # TODO: Change window_size back to 10
        if EoDStatsColumns.VOLUME_VOLATILITY not in data.collect_schema().names():
            data = data.with_columns(
                pl.col(EoDStatsColumns.TRADE_VOLUME)
                .rolling_std(window_size=5)
                .alias(EoDStatsColumns.VOLUME_VOLATILITY)
            )
        if EoDStatsColumns.PRICE_VOLATILITY not in data.collect_schema().names():
            data = data.with_columns(
                pl.col(EoDStatsColumns.CLOSE_PRICE)
                .rolling_std(window_size=5)
                .alias(EoDStatsColumns.PRICE_VOLATILITY)
            )

        return data  # type: ignore

    @staticmethod
    def set_tick_dtypes(frame: pl.LazyFrame, event_type: RefinitivEventType):
        """

        :param event_type:
        :param frame:
        :return:
        """
        if event_type not in [RefinitivEventType.TRADE, RefinitivEventType.QUOTE]:
            raise AttributeError(f"Event type must be quote or trade. Type: {event_type}")

        price_columns = {
            RefinitivEventType.TRADE: TickDtypes.TRADES,
            RefinitivEventType.QUOTE: TickDtypes.QUOTES,
        }
        frame = frame.with_columns(
            [
                pl.col(col).cast(dtype, strict=False)
                for col, dtype in price_columns[event_type].items()
                if col in frame.collect_schema().names()
            ]
        )
        return frame
