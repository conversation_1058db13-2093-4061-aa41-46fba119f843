import logging
import pytest
from aries_io_event.io_param import IOParamFieldSet
from aries_io_event.task import TaskFieldSet
from aries_io_event.workflow import WorkflowFieldSet
from aries_task_link.models import AriesTaskInput, AriesTaskResult
from collections import namedtuple
from datetime import datetime
from efdh_tasks.map_ric.map_ric_task import MapRicTask
from efdh_tasks.map_ric.update_instrument_list import UpdateLevel2Instruments
from mock_alchemy.mocking import AlchemyMagicMock
from pathlib import Path
from tests_efdh_tasks.tests_map_ric.map_ric_mock_data import (
    mock_add_ric_to_il,
    mock_get_instrument_list_id_by_name,
    mock_get_ric_code,
    mock_get_rics_in_instrument_list,
    mock_perm_id,
)

logger = logging.getLogger(__name__)

TEST_PATH = Path(__file__).parent.joinpath("data")


@pytest.fixture
def mock_session_map_ric():
    return AlchemyMagicMock()


@pytest.fixture(scope="function", autouse=True)
def mock_data_sources_map_ric(monkeypatch):
    mock_get_ric_code(monkeypatch)
    mock_get_rics_in_instrument_list(monkeypatch)
    mock_get_instrument_list_id_by_name(monkeypatch)
    mock_add_ric_to_il(monkeypatch)
    mock_perm_id(monkeypatch)


def test_map_ric(monkeypatch, mock_session_map_ric):
    params = {
        "instrument_file_uri": str(TEST_PATH.joinpath("200021__55G1DPWZ.csv")),
    }
    workflow = WorkflowFieldSet(
        name="localtest",
        stack="dev-master-data",
        tenant="dev-master-data",
        start_timestamp=str(datetime.now()),
    )
    input_param = IOParamFieldSet(params=params)
    input = AriesTaskInput(
        workflow=workflow,
        input_param=input_param,
        task=TaskFieldSet(
            id="foo",
            name="map-ric",
            version="1",
            success=True,
            previous_id=None,
        ),
    )

    expected_output = AriesTaskResult(
        output_param=IOParamFieldSet(
            params={
                "instrument_file_uri": str(TEST_PATH.joinpath("200021__55G1DPWZ.csv")),
                "input_instruments_count": 1,
                "unmapped_instruments_count": None,
                "rics": ["SHL.NEO"],
            }
        ),
        app_metric=None,
    )

    update_object = UpdateLevel2Instruments()
    update_object.db.session = mock_session_map_ric  # type: ignore

    L2Row = namedtuple("L2Row", ["id", "key"])
    row = L2Row(id="1b1ff9ce-9551-46c9-8768-e1cc2d19d7a4", key="NEOE:CA43740Q1072")
    mock_session_map_ric().__enter__().query().filter().all.side_effect = [[row], []]

    map_ric = MapRicTask(aries_task_input=input)
    result = map_ric.execute()

    assert result == expected_output
