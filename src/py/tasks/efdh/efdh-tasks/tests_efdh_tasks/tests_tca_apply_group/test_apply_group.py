import fsspec
import httpx
import polars as pl
import pyarrow.parquet as pq
from fsspec.implementations.local import LocalFileSystem
from pathlib import Path
from unittest.mock import MagicMock

LOCAL_RESULT_FP = Path(__file__).parent / "data" / "result.parquet"


class MockFileSystem(LocalFileSystem):  # type: ignore
    def download(self, *args, **kwargs):
        p = Path(args[0]).parent
        if not p.exists():
            p.mkdir(parents=True)
        return self.copy(*args, **kwargs)


def mock_file_system(*args):
    return MockFileSystem()


def mock_httpx_client(*args, **kwargs):
    return MagicMock()


def mock_write_parquet(data, filepath, use_pyarrow=True):
    arrow_table = data.to_arrow()
    pq.write_table(arrow_table, LOCAL_RESULT_FP)


def test_tca_apply_group(monkeypatch, tca_group_input_event, mock_es_tca_group):
    from efdh_tasks.tca_apply_group.tca_apply_group_task import TCAApplyGroup

    monkeypatch.setattr(fsspec, "filesystem", mock_file_system)
    monkeypatch.setattr(httpx, "Client", MagicMock)
    monkeypatch.setattr(pl.DataFrame, "write_parquet", mock_write_parquet)

    tca_apply_group = TCAApplyGroup(aries_task_input=tca_group_input_event)
    result = tca_apply_group.execute()

    assert len(result.output_param.params["io_params_list"]) == 625  # type: ignore
