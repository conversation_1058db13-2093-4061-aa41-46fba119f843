import fsspec
import os
import polars
import polars as pl
import pytest
import re
import reference_api_client.ihs_monitoring
import se_fsspec_utils.sftp_utils
import shutil
from efdh_tasks.loans_cds_converter.loans_cds_input_schema import LoansCDSInput
from efdh_tasks.loans_cds_converter.static import IHSType
from fsspec.implementations.local import LocalFileSystem
from fsspec.implementations.sftp import SFTPFileSystem
from pathlib import Path
from tests_efdh_tasks.tests_loans_cds_converter.conftest import MockRicsClient
from unittest.mock import MagicMock, patch

os.environ["COGNITO_CLIENT_SECRET"] = ""
os.environ["COGNITO_CLIENT_ID"] = ""
os.environ["COGNITO_AUTH_URL"] = ""
os.environ["MP_WORKER_COUNT"] = "1"
os.environ["EOD_STATS_COLUMNS_LIST"] = (
    '["Close Ask Price", "Close Bid Price", "Close Price", "Currency", "Date", '
    '"Exchange Code", "High Ask Price", "High Bid Price", "High Price", '
    '"Low Price", "Low Ask Price", "Low Bid Price", "Open Ask Price", '
    '"Open Bid Price", "Open Interest", "Open Price", "#RIC", "Trade Volume", '
    '"VWAP", "Traded Volume 20 Day EMA", "Daily Traded Notional (JPY)", '
    '"Daily Traded Notional (EUR)", "Daily Traded Notional (GBP)", "Daily Traded '
    'Notional (CHF)", "Daily Traded Notional (USD)", "Daily Traded Notional ('
    'AUD)","Daily Traded Notional (SGD)", "Daily Traded Notional"] '
)


TEST_DATA = Path(__file__).parent


class MockFileSystem(LocalFileSystem):  # type: ignore
    def put_object(self, Bucket, Key, Body, ACL, callback=None, **kwargs):
        pass


class FileSystemA(SFTPFileSystem):  # type: ignore
    def __init__(self):
        self._file_instance = MagicMock()

    def listdir(self, path):
        # test for prod path:
        if path == "/Loans":
            return [{"name": "/Loans/LoanXMarks_NY_2024-01-18.csv"}]

        # test for mock sftp path:
        elif path == "/output":
            return [
                {"name": "/output/Credit_Index_Tranche_Composites_20240119.zip"},
                {"name": "/output/Credit_Index_Pricing_20240119.zip"},
                {"name": "/output/Composite_CDS_Pricing_20240119.zip"},
            ]

        else:
            raise ValueError("Only /output and /Loans supported")

    def cp_file(self, path1, path2, **kwargs):
        pass

    def created(self, path):
        pass

    def modified(self, path):
        pass

    def sign(self, path, expiration=100, **kwargs):
        pass

    def put_file(self, lpath, rpath, callback=None, **kwargs):
        pass

    def open(
        self,
        path,
        mode="rb",
        block_size=None,
        cache_options=None,
        compression=None,
        **kwargs,
    ):
        return self._file_instance

    def get(self, path1, path2):
        if "cds_single" in path2:
            shutil.copyfile(
                TEST_DATA.joinpath(
                    "tests_downloads/lake/ingress/ric/raw/cds_single/Composite_CDS_Pricing_20240119.zip"
                ),
                path2,
            )

        elif "cds_index" in path2:
            shutil.copyfile(
                TEST_DATA.joinpath(
                    "tests_downloads/lake/ingress/ric/raw/cds_index/Credit_Index_Pricing_20240119.zip"
                ),
                path2,
            )

        elif "cds_tranche" in path2:
            shutil.copyfile(
                TEST_DATA.joinpath(
                    "tests_downloads/lake/ingress/ric/raw/cds_tranche/Credit_Index_Tranche_Composites_20240119.zip"
                ),
                path2,
            )

        elif "loans" in path2:
            shutil.copyfile(
                TEST_DATA.joinpath(
                    "tests_downloads/lake/ingress/ric/raw/loans/LoanXMarks_NY_2024-01-18.csv"
                ),
                path2,
            )


def mock_file_system(*args, **kwargs):
    return MockFileSystem()


def mock_read_parquet(filepath, *args, **kwargs):
    if filepath.startswith("s3"):
        raise FileNotFoundError


def new_read_parquet(*args, **kwargs):
    return pl.read_parquet(*args, **kwargs)


def mock_sftp_client(*args, **kwargs):
    return FileSystemA()


def mock_httpx_client(*args, **kwargs):
    return MagicMock()


def mock_ihsm(*args, **kwargs):
    return MagicMock()


def test_convert_to_loans_parquet(monkeypatch, loans_input_event):
    monkeypatch.setattr(se_fsspec_utils.sftp_utils, "get_sftp_fs", mock_sftp_client)

    monkeypatch.setattr(fsspec, "filesystem", mock_file_system)
    monkeypatch.setattr(polars, "read_parquet", mock_read_parquet)
    monkeypatch.setattr(reference_api_client.ihs_monitoring, "IHSM", mock_ihsm)
    with patch("boto3.client") as mock_boto:
        mock_s3 = MagicMock()

        mock_boto.return_value = mock_s3
        from efdh_tasks.loans_cds_converter.loans_cds_converter_task import LoansCDSConverter

        loans_cds_converter = LoansCDSConverter(aries_task_input=loans_input_event)
        loans_cds_converter.rics_client = MockRicsClient
        output = loans_cds_converter.execute()
        # Extract the batch file URI
        # eg: '/lake/ingress/loans_cds/structured/2025/04/16/batch_0_20250416050109.parquet'
        batch_file_uri = output.output_param.params["io_params_list"][0]["io_param"]["params"][
            "batch_file_uri"
        ]

        assert re.match(
            r"^/lake/ingress/loans_cds/structured/\d{4}/\d{2}/\d{2}/batch_0_\d{14}\.parquet$",
            batch_file_uri,
        ), f"Filename {batch_file_uri} does not match expected pattern"
        mock_s3.put_object.assert_called()


def test_loans_cds_input_only_from_date():
    with pytest.raises(ValueError, match="Both from_date and to_date must be provided together"):
        LoansCDSInput(type=IHSType.LOANS, from_date="2025-01-10")


def test_convert_with_date_range(monkeypatch, loans_input_event):
    monkeypatch.setattr(se_fsspec_utils.sftp_utils, "get_sftp_fs", mock_sftp_client)
    monkeypatch.setattr(fsspec, "filesystem", mock_file_system)
    monkeypatch.setattr(polars, "read_parquet", mock_read_parquet)
    monkeypatch.setattr(reference_api_client.ihs_monitoring, "IHSM", mock_ihsm)

    # Update loans_input_event to include from_date/to_date
    loans_input_event.input_param.params["from_date"] = "2024-01-18"
    loans_input_event.input_param.params["to_date"] = "2024-01-20"

    with patch("boto3.client") as mock_boto:
        mock_s3 = MagicMock()
        mock_boto.return_value = mock_s3
        from efdh_tasks.loans_cds_converter.loans_cds_converter_task import LoansCDSConverter

        loans_cds_converter = LoansCDSConverter(aries_task_input=loans_input_event)
        loans_cds_converter.rics_client = MockRicsClient
        output = loans_cds_converter.execute()
        batch_file_uri = output.output_param.params["io_params_list"][0]["io_param"]["params"][
            "batch_file_uri"
        ]

        assert re.match(
            r"^/lake/ingress/loans_cds/structured/\d{4}/\d{2}/\d{2}/batch_0_\d{14}\.parquet$",
            batch_file_uri,
        ), f"Filename {batch_file_uri} does not match expected pattern"

        mock_s3.put_object.assert_called()
