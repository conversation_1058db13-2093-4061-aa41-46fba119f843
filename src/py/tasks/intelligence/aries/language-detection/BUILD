python_sources(
    name="task",
    tags=["amd_only"],
    sources=["language_detection/**/*.py"],
    dependencies=[
        ":model",
        "//:3rdparty#adlfs",  # adlfs is used to upload ndjson files
        "//:machine_learning#fasttext-wheel",
        "//:machine_learning#ftfy",
        "//:machine_learning#langid",
        "//:machine_learning#transformers",
        "//:machine_learning#cmake",
        "//:machine_learning#lit",
        "//:machine_learning#torch",
        "//:machine_learning#triton",
    ],
)

se_image(
    image_name="aries-language-detection",
    tags=["amd_only"],
    pex_entry_point="language_detection/main.py:run_language_detection",
)

resources(
    name="model",
    tags=["amd_only"],
    sources=[
        "language_detection/model/**/*.json",
        "language_detection/model/**/*.bin",
        "language_detection/model/**/*.bin",
    ],
    description="The models for language detection",
)

resource(
    name="test_data",
    source="tests_language_detection/data/conductor_test_emails.ndjson",
    description="Test data for language detection",
)

python_test_utils(
    name="conftest", tags=["amd_only"], sources=["tests_language_detection/**/conftest.py"]
)

python_tests(
    name="tests",
    tags=["amd_only"],
    sources=["tests_language_detection/**/test_*.py"],
    dependencies=[":conftest", ":test_data"],
)

# not required to ship these files in docker image
python_sources(tags=["amd_only"], sources=["*.py"])
