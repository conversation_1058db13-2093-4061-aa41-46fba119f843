# type: ignore
import addict
import copy
import emoji
import json
import logging
import pandas as pd
import spacy
import time
from aries_config_cached_client.tenant_workflow import CachedTenantWorkflowAPIClient
from aries_io_event.app_metric import AppMetricFieldSet
from aries_task_link.models import AriesTaskInput, AriesTaskR<PERSON>ult
from aries_task_link.task import aries_task
from integration_audit.auditor import (
    AuditorStaticFields,
    get_record_identifier,
    upload_audit,
    upsert_audit,
)
from language_detection.classifier import detect_languages
from language_detection.config import TASK_CONFIG
from language_detection.schemas.internal import ClassifierInnerMetrics
from language_detection.static import (
    DEVICE,
    EMAIL_REGEX,
    SPECIAL_CHARACTER_REGEX,
    TASK_NAME,
    URL_REGEX,
    LanguageDetectionSchemaColumns,
    SourceColumns,
    TempColumns,
)
from se_elastic_schema.components.communication.common_analytics import CommonAnalytics
from se_elastic_schema.elastic_schema.core.steeleye_schema_model import SteelEyeSchemaModelReference
from se_enums.cloud import CloudProviderEnum
from se_io_utils.batching_utils import batching_generator
from se_io_utils.json_utils import append_to_ndjson, write_named_temporary_json
from spacy.lang.en import English
from spacy.tokens import Span
from surveillance_utils.utils import create_output_path
from typing import Any, Dict, List

# TODO: Replace with a shared logger
logger = logging.getLogger(__name__)

# Note: Use GPUs if it's available
if DEVICE >= 0:
    spacy.prefer_gpu()
NLP = English()
NLP.add_pipe("sentencizer")


def parse_comms_data(comms_list: List[Dict[str, Any]]) -> pd.DataFrame:
    """Fetch the textual data from the comms record.

    :param comms_list: List of dict containing the comms data
    :return: Dataframe containing the textual data of the comms field.
    """
    text_list = list()
    id_list = list()
    for index, comms_data in enumerate(comms_list):
        text_list.append(comms_data.get(SourceColumns.BODY, {}).get(SourceColumns.TEXT, ""))
        id_list.append(index)

    return pd.DataFrame({SourceColumns.ID: id_list, SourceColumns.TEXT: text_list})


def tokenize_to_sentence(data: pd.Series) -> list[Span]:
    """Tokenize the comms text to sentences using spacy.

    :param data: Data containing the text within the comms record
    :return: List of sentences
    """
    try:
        text = data["text"]
        doc = NLP(text)

        return list(doc.sents)
    except Exception:
        return list()


def pre_process_data(source_frame: pd.DataFrame) -> pd.DataFrame:
    """Preprocess the comms text data by doing the following:

        - Remove special characters
        - Remove emojis
        - Tokenize to sentences
        - Filtering out blank text after preprocessing

    :param source_frame: Source dataframe
    :return: Cleaned and tokenized dataframe
    """
    target = pd.DataFrame(index=source_frame.index)
    target.loc[:, SourceColumns.TEXT] = source_frame.loc[:, SourceColumns.TEXT]
    target.loc[:, TempColumns.ORIGINAL_TEXT] = source_frame.loc[:, SourceColumns.TEXT]
    target.loc[:, SourceColumns.ID] = source_frame.loc[:, SourceColumns.ID]

    # Remove all urls and emails from text
    target[SourceColumns.TEXT] = source_frame[SourceColumns.TEXT].str.replace(
        URL_REGEX, "", regex=True
    )
    target[SourceColumns.TEXT] = target[SourceColumns.TEXT].str.replace(EMAIL_REGEX, "", regex=True)

    # Remove all special characters
    target[SourceColumns.TEXT] = target[SourceColumns.TEXT].str.replace(
        SPECIAL_CHARACTER_REGEX, "", regex=True
    )

    # Remove repeating periods (.) and spaces along with new lines
    target[SourceColumns.TEXT] = target[SourceColumns.TEXT].str.replace(r"\.\.+", ".", regex=True)
    target[SourceColumns.TEXT] = target[SourceColumns.TEXT].str.replace(r"\n+", " ", regex=True)
    target[SourceColumns.TEXT] = target[SourceColumns.TEXT].str.replace(r"\s\s+", " ", regex=True)
    target[SourceColumns.TEXT] = target[SourceColumns.TEXT].fillna("")

    logger.info(f"Shape before text filter: {target.shape}")
    target = target[target[SourceColumns.TEXT] != ""]
    logger.info(f"Shape after text filter: {target.shape}")

    start_time = time.perf_counter()
    target[TempColumns.SENTENCE] = target.apply(tokenize_to_sentence, axis=1)
    logger.info(f"Sentence tokenization took {time.perf_counter() - start_time:.2f} seconds")

    # Transforming view to sentences
    sentence_df = target.explode(TempColumns.SENTENCE)
    sentence_df[TempColumns.SENTENCE] = sentence_df[TempColumns.SENTENCE].astype(str)
    sentence_df[TempColumns.ORIGINAL_SENTENCE] = sentence_df[TempColumns.SENTENCE]

    # Remove periods from sentences
    regex_str_sentence = SPECIAL_CHARACTER_REGEX + r"|\."
    sentence_df[TempColumns.SENTENCE] = sentence_df[TempColumns.SENTENCE].str.replace(
        regex_str_sentence, "", regex=True
    )

    # Remove emojis
    sentence_df[TempColumns.SENTENCE] = sentence_df[TempColumns.SENTENCE].apply(
        lambda x: emoji.replace_emoji(x, replace="")
    )

    sentence_df[TempColumns.SENTENCE] = sentence_df[TempColumns.SENTENCE].str.strip()
    sentence_df[TempColumns.SENTENCE] = sentence_df[TempColumns.SENTENCE].fillna("")

    logger.info(f"Shape before sentence filter: {sentence_df.shape}")
    sentence_df = sentence_df[sentence_df[TempColumns.SENTENCE] != ""]
    logger.info(f"Shape after sentence filter: {sentence_df.shape}")

    return sentence_df


def format_classifier_analytics(classification: Dict) -> CommonAnalytics | None:
    """Format the language prediction results to our required schema.

    :param classification: Language detection predictions
    :return: Validated CommonAnalytics record
    """
    if not classification:
        logger.info("Analytics record is empty")
        return None

    analytics = addict.Dict()
    analytics.languageDetection.languagesDetected = classification.get(
        LanguageDetectionSchemaColumns.LANGUAGES_DETECTED
    )
    analytics.languageDetection.numberOfLanguages = classification.get(
        LanguageDetectionSchemaColumns.NO_OF_LANGUAGES
    )
    analytics.languageDetection.isMultiLingual = classification.get(
        LanguageDetectionSchemaColumns.IS_MULTI_LINGUAL
    )
    analytics.totalCharacters = classification.get(LanguageDetectionSchemaColumns.TOTAL_CHARACTERS)
    analytics.languageDetection.languages = classification.get(
        LanguageDetectionSchemaColumns.LANGUAGES
    )

    try:
        validated_record = CommonAnalytics(**analytics).dict(exclude_none=True)
    except Exception:
        logger.exception("Analytics record does not conform to `CommonAnalytics` schema")
        validated_record = None

    return validated_record


def process_event(aries_task_input: AriesTaskInput) -> AriesTaskResult:
    task_name = aries_task_input.task.name
    input_params = aries_task_input.input_param
    input_path: str = input_params.params["file_uri"]
    # Get tenant workflow tenant config from postgres
    cached_tenant_workflow_config = CachedTenantWorkflowAPIClient.get(
        stack_name=aries_task_input.workflow.stack,
        tenant_name=aries_task_input.workflow.tenant,
        workflow_name=aries_task_input.workflow.name,
    )

    cloud_provider = CloudProviderEnum(cached_tenant_workflow_config.tenant.cloud)
    streamed: bool = cached_tenant_workflow_config.workflow.streamed

    output_params = copy.deepcopy(input_params)
    output_path = create_output_path(
        aries_task_input,
        cloud_provider_enum=cloud_provider,
        file_uri=aries_task_input.input_param.params["file_uri"],
    )
    output_params.params["file_uri"] = output_path

    audit_filepath = write_named_temporary_json(content={}, output_filename="audit.json")
    inner_metrics = ClassifierInnerMetrics()
    pre_flight_time = 0.0

    input_count, skipped_count = 0, 0
    for temp_file_path, comms_list_str in batching_generator(
        [input_path], output_path, TASK_CONFIG.BATCH_SIZE, TASK_CONFIG.BATCH_SIZE_CHARS
    ):
        batch_start = input_count
        input_count += len(comms_list_str)
        logger.info(f"Processing records {batch_start} through {input_count}")

        comms_list = [json.loads(line) for line in comms_list_str]

        try:
            # Extract fields from comms
            start = time.time()
            parsed_df = parse_comms_data(comms_list)
            batch_df = pre_process_data(parsed_df)
            pre_flight_time += time.time() - start

            # Pass through models
            prediction_df, inner_metrics = detect_languages(batch_df, inner_metrics)

            # Add analytics to comms in-place
            for index, comms_data in enumerate(comms_list):
                classification_list = prediction_df[
                    prediction_df[TempColumns.LINE_NUMBER] == index
                ].to_dict(orient="records")
                classification = next(iter(classification_list)) if classification_list else {}
                analytics = format_classifier_analytics(classification)
                if analytics:
                    comms_data[SourceColumns.ANALYTICS] = analytics

        except Exception as e:
            logger.error(f"FAILURE IN BATCH {batch_start} through {input_count} - {type(e)}, {e}")
            skipped_count += len(comms_list)

            # Add audit
            audit_result: Dict[str, dict] = {}
            model = SteelEyeSchemaModelReference.from_qualified_reference(
                reference=input_params.params["data_model"]
            ).import_class()
            for comms_data in comms_list:
                rec_id = (
                    get_record_identifier(
                        streamed=streamed,
                        record=comms_data,
                        data_model=model,
                        cloud_provider=cloud_provider,
                        already_unflattened=True,
                    )
                    or "unknown"
                )
                audit_result[rec_id] = {
                    AuditorStaticFields.STATUS: ["Language Detection failed"],
                }
            upsert_audit(
                audit_path=audit_filepath,
                streamed=streamed,
                input_data=audit_result,
                models=[model],
            )

        # Write to output file
        append_to_ndjson(temp_file_path, comms_list)

    # Upload Ingestion Audit
    upload_audit(
        aries_task_input=aries_task_input,
        audit_filepath=audit_filepath,
        cloud_provider=cloud_provider,
    )

    # Create Metrics
    app_metric = update_app_metrics(
        aries_task_input_name=task_name,
        input_count=input_count,
        skipped_count=skipped_count,
        pre_flight_time=pre_flight_time,
        inner_metrics=inner_metrics,
    )

    return AriesTaskResult(output_param=output_params, app_metric=app_metric)


def update_app_metrics(
    aries_task_input_name: str,
    input_count: int,
    skipped_count: int,
    pre_flight_time: float,
    inner_metrics: ClassifierInnerMetrics,
) -> AppMetricFieldSet:
    predict_time = round(
        +inner_metrics.xlm_bert_time + inner_metrics.fast_text_time + inner_metrics.lang_id,
        5,
    )
    app_metrics = AppMetricFieldSet(
        metrics={
            "custom": {
                TASK_NAME: {
                    "input_count": input_count,
                    "skipped_count": skipped_count,
                    "classified_count": input_count - skipped_count,
                    "pre_flight_time": round(pre_flight_time, 5),
                    "xlm_bert_time": round(inner_metrics.xlm_bert_time, 5),
                    "fast_text_time": round(inner_metrics.fast_text_time, 5),
                    "lang_id": round(inner_metrics.lang_id, 5),
                    "predict_time": predict_time,
                }
            }
        }
    )
    logger.info(json.dumps(app_metrics.dict()))
    return app_metrics


@aries_task()
def run_language_detection(aries_task_input: AriesTaskInput) -> AriesTaskResult:
    # Validate aries_task_input
    assert aries_task_input.input_param.params["file_uri"]
    assert aries_task_input.task.name

    return process_event(aries_task_input)
