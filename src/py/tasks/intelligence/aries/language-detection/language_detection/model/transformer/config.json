{"_name_or_path": "xlm-roberta-base", "architectures": ["XLMRobertaForSequenceClassification"], "attention_probs_dropout_prob": 0.1, "bos_token_id": 0, "classifier_dropout": null, "eos_token_id": 2, "hidden_act": "gelu", "hidden_dropout_prob": 0.1, "hidden_size": 768, "id2label": {"0": "Arabic", "1": "Catalan", "2": "Chinese_China", "3": "Chinese_Hongkong", "4": "Chinese_Taiwan", "5": "Czech", "6": "Dutch", "7": "English", "8": "Estonian", "9": "French", "10": "German", "11": "Greek", "12": "Indonesian", "13": "Italian", "14": "Japanese", "15": "Latvian", "16": "Persian", "17": "Polish", "18": "Portuguese", "19": "Romanian", "20": "Russian", "21": "Slovenian", "22": "Spanish", "23": "Swedish", "24": "Tamil", "25": "Turkish", "26": "<PERSON><PERSON><PERSON>", "27": "Bulgaria", "28": "Hindi", "29": "Thai", "30": "Vietnamese"}, "initializer_range": 0.02, "intermediate_size": 3072, "label2id": {"Arabic": 0, "Bulgaria": 27, "Catalan": 1, "Chinese_China": 2, "Chinese_Hongkong": 3, "Chinese_Taiwan": 4, "Czech": 5, "Dutch": 6, "English": 7, "Estonian": 8, "French": 9, "German": 10, "Greek": 11, "Hindi": 28, "Indonesian": 12, "Italian": 13, "Japanese": 14, "Latvian": 15, "Persian": 16, "Polish": 17, "Portuguese": 18, "Romanian": 19, "Russian": 20, "Slovenian": 21, "Spanish": 22, "Swedish": 23, "Tamil": 24, "Thai": 29, "Turkish": 25, "Ukranian": 26, "Vietnamese": 30}, "layer_norm_eps": 1e-05, "max_position_embeddings": 514, "model_type": "xlm-roberta", "num_attention_heads": 12, "num_hidden_layers": 12, "output_past": true, "pad_token_id": 1, "position_embedding_type": "absolute", "problem_type": "single_label_classification", "torch_dtype": "float32", "transformers_version": "4.30.2", "type_vocab_size": 1, "use_cache": true, "vocab_size": 250002}