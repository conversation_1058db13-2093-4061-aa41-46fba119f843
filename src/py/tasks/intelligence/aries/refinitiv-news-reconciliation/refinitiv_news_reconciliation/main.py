from aries_task_link.models import AriesTaskInput, AriesTaskResult
from aries_task_link.task import aries_task
from refinitiv_news_reconciliation.rn_annotator import Annotator
from refinitiv_news_reconciliation.task import RefinitivNewsReconciliationTask


@aries_task()
def refinitiv_news_reconciliation(aries_task_input: AriesTaskInput) -> AriesTaskResult:
    task = RefinitivNewsReconciliationTask(aries_task_input=aries_task_input)
    result: AriesTaskResult = task.execute(annotator=Annotator)
    return result
