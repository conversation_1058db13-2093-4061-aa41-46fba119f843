from apply_strategy.apply_strategy_task import ITv3ApplyStrategyTask
from aries_task_link.models import AriesTaskInput, AriesTaskResult
from aries_task_link.task import aries_task


@aries_task()
def apply_strategy(aries_task_input: AriesTaskInput) -> AriesTaskResult:
    mar_create_group_task = ITv3ApplyStrategyTask(aries_task_input=aries_task_input)
    # I have to revist this in https://steeleye.atlassian.net/browse/ENG-3037
    # when alerts will be generated and written directly to s3 for ESConnector task
    return mar_create_group_task.apply_strategy()  # type: ignore # See comment above
