resource(
    name="config",
    source="apply-strategy-config.yml",
)

python_sources(
    name="task",
    sources=["apply_strategy/**/*.py"],
    dependencies=[":config", "//:se_libs#se-schema"],
)

python_test_utils(name="conftest", sources=["tests_apply_strategy/**/conftest.py"])

python_tests(
    name="tests",
    sources=["tests_apply_strategy/**/test_*.py"],
    dependencies=[":conftest"],
)

python_sources()
