from aries_task_link.models import AriesTaskInput, AriesTaskR<PERSON>ult
from aries_task_link.task import aries_task
from mar_create_group_deprecated.mar_create_group_task import MarCreateGroupTask


@aries_task()
def mar_create_group(aries_task_input: AriesTaskInput) -> AriesTaskResult:
    mar_create_group_task = MarCreateGroupTask(aries_task_input=aries_task_input)
    return mar_create_group_task.execute()
