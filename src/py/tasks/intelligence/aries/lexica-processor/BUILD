python_sources(
    name="task",
    tags=["amd_only"],
    sources=[
        "lexica_processor/**/*.py",
    ],
    dependencies=[
        "//:3rdparty#s3fs",  # s3fs is used to upload ndjson files
        "//:3rdparty#adlfs",  # adlfs is used to upload ndjson files
    ],
)

se_image(
    image_name="aries-lexica-processor",
    tags=["amd_only"],
    pex_entry_point="lexica_processor/main.py:lexica_processor",
)

python_test_utils(
    name="utils",
    tags=["amd_only"],
    sources=[
        "tests_lexica_processor/comms_records_fixtures.py",
        "tests_lexica_processor/comms_records_helpers.py",
        "tests_lexica_processor/mock_tika_client.py",
        "tests_lexica_processor/conftest.py",
    ],
)

python_tests(
    name="tests",
    tags=["amd_only"],
    sources=["tests_lexica_processor/**/test_*.py"],
    dependencies=[":utils", ":lexica_processor_test_resources"],
)

resources(
    name="lexica_processor_test_resources",
    tags=["amd_only"],
    sources=[
        "tests_lexica_processor/**/*.ndjson",
        "tests_lexica_processor/**/*.json",
        "tests_lexica_processor/**/*.yaml",
        "tests_lexica_processor/**/*.pkl",
    ],
)

# not required to ship these files in docker image
python_sources(
    tags=["amd_only"],
    sources=["*.py"],
)
# Needed python source for benchmarking files
python_sources(
    name="benchmarking_forced_source",
    tags=["amd_only"],
    sources=[
        "benchmarking/**/*.py",
    ],
)
