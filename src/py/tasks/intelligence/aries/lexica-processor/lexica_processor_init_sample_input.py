import datetime
from aries_io_event.io_param import IOParamFieldSet
from aries_io_event.task import TaskFieldSet
from aries_io_event.workflow import WorkflowFieldSet
from aries_task_link.models import AriesTaskInput


def sample_input() -> AriesTaskInput:
    workflow = WorkflowFieldSet(
        name="email",
        stack="dev-shared-2",
        tenant="irises8",
        start_timestamp=datetime.datetime.utcnow(),
    )
    input_param = IOParamFieldSet(
        params=dict(
            file_uri="s3://irises8.dev.steeleye.co/aries/ingest/email/2024/04/04/9ifBn4iYPMUpHaJrwm-PP/deduplicate_by_meta_id/139715cda824833935cd8e059b6e98410d285436de538a3031636237e041f9d2___email___transformed.ndjson",
            data_model="se_elastic_schema.models.tenant.communication.email:Email",
        )
    )
    task = TaskFieldSet(name="lexica_processor", version="latest", success=False)
    return AriesTaskInput(workflow=workflow, input_param=input_param, task=task)
