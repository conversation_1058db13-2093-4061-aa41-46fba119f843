from typing import List

CSV_COLUMNS: List[str] = [
    "&id",
    "instrumentListId",
    "refinitivExchangeCode",
    "primaryTradingRic",
    "preferredRicVenueRefinitiv",
    "preferredRicCurrencyRaw",
    "&key",
    "ric",
    "&model",
    "&version",
    "instrumentUniqueIdentifier",
    "instrumentModel",
    "preferredRic",
    "&hash",
    "preferredRicVenue",
    "&timestamp",
    "&user",
    "preferredRicCurrency",
    "timestamps.coverageTo",
    "timestamps.coverageFrom",
    "cfi.code",
    "cfi.attribute4",
    "cfi.attribute1",
    "cfi.attribute3",
    "cfi.attribute2",
    "cfi.category",
    "cfi.group",
    "compositeRic",
    "query.bool.must_not",
    "query.bool.filter",
    "doc.instrumentUniqueIdentifier",
    "doc.cfi.code",
    "doc.&id",
    "doc.&timestamp",
    "doc.preferredRic",
    "doc.primaryTradingRic",
    "doc.preferredRicCurrencyRaw",
    "doc.preferredRicCurrency",
    "doc.preferredRicVenueRefinitiv",
    "doc.instrumentListId",
    "&ancestor",
]

SEARCH_AFTER_FIELD: str = "&id"
