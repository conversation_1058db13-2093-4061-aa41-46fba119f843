from datetime import date
from pydantic import BaseModel, validator
from typing import List


class BackloadTaskInput(BaseModel):
    """Custom wrapper of Pydantic Base Model for S3 Upload Task."""

    rics: List[str]
    date_from: date
    date_to: date

    @validator("date_to")
    def validate_end_date(cls, value: date, values) -> date:
        start_date = values.get("date_from")
        if value <= start_date:
            raise ValueError("End date must be greater than the start date.")
        return value
