from aries_task_link.models import AriesTaskInput, AriesTaskResult
from aries_task_link.task import aries_task
from refinitiv_news_backload.task import RefinitivNewsBackloadTask


@aries_task()
def refinitiv_news_backload(aries_task_input: AriesTaskInput) -> AriesTaskResult:
    task = RefinitivNewsBackloadTask(aries_task_input=aries_task_input)
    result: AriesTaskResult = task.execute()
    return result


if __name__ == "__main__":
    task_in = {
        "workflow": {
            "trace_id": "Yi9zvmGLhRZUhP6cZW-r6",
            "start_timestamp": "2023-11-28T21:39:38.928234",
            "name": "refinitiv_news_backload",
            "stack": "dev-master-data",
            "tenant": "dev-master-data",
        },
        "task": {
            "id": "tm3DZmEu-GV6Bi1k5YOe-",
            "name": "aries-conductor-trigger",
            "version": "2023.10.0011-dev.1696332866.58b8142",
            "timestamp": "2023-11-29T03:34:38.418386",
            "success": True,
            "previous_id": "OSyh1ybFLgeTZ70gE-4Oy",
        },
        "input_param": {
            "params": {"rics": ["TSLA.OQ"], "date_from": "2023-01-01", "date_to": "2023-01-02"}
        },
    }
    refinitiv_news_backload(AriesTaskInput.parse_obj(task_in))
