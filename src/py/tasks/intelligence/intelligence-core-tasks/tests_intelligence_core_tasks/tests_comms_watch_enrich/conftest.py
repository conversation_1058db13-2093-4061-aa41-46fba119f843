import os
import pytest
from pathlib import Path
from surveillance_utils.test_mock_helpers import (
    FakeEsRepo,
    FakeSequenceService,
    FakeSlimRecordHandler,
)

os.environ["DATA_PLATFORM_CONFIG_API_URL"] = "https://test-enterprise.steeleye.co"
os.environ["WATCH_COPILOT_TENANTS"] = "['pinafore']"
os.environ["ELASTIC_HOST"] = "fake_host"
os.environ["ELASTIC_API_KEY"] = "fake_key"
os.environ["MAX_INFO_BARRIER_ALERTS_SIZE"] = "60"

TEST_DATA_PATH = Path(__file__).parent.joinpath("data")

DEFAULT_OPENAI_VALUES = {
    "OPENAI_API_BASE": "",
    "OPENAI_API_KEY": "",
    "OPENAI_API_MODEL": "gpt-4o",
    "OPENAI_API_VERSION": "2023-03-15-preview",
}


@pytest.fixture
def fake_es_repo_instance(request):
    return FakeEsRepo(
        version=8,
        client_get_path=str(TEST_DATA_PATH.joinpath(request.param)),
    )


@pytest.fixture()
def fake_es_repo_instance_info_barrier():
    return FakeEsRepo(
        version=8,
        search_repo_path=[
            str(TEST_DATA_PATH.joinpath("info_barrier_es.json")),
            str(TEST_DATA_PATH.joinpath("info_barrier.json")),
        ],
        client_get_path=str(TEST_DATA_PATH.joinpath("watch_execution_info_barrier.json")),
        count=1,
    )


@pytest.fixture()
def fake_slim_record_handler():
    return FakeSlimRecordHandler(
        version=8,
        search_repo_path=[str(TEST_DATA_PATH.joinpath("watch_execution.json"))],
        client_get_path=str(TEST_DATA_PATH.joinpath("watch_execution.json")),
    )


@pytest.fixture()
def fake_sequence_service():
    return FakeSequenceService
