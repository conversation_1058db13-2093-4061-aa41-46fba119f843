{"document": {"&model": "MessageGroup", "analytics": {"languageDetection": {"languagesDetected": [{"code": "en", "name": "English"}], "numberOfLanguages": 1, "isMultiLingual": false, "languages": [{"start": 0, "end": 0}]}}, "ids": ["16ec850e5d4c2049b1e934603edad0af38ce96def2acb59f5efa77e8af73b6512f2ae8ed223a2b84f409acf3a42170efe179297f076c4713db25c94e9b244178", "16ec850e5d4c2049b1e934603edad0af38ce96def2acb59f5efa77e8af73b6512f2ae8ed223a2b84f409acf3a42170efe179297f076c4713db25c94e9b244178"], "metadata": {"source": {"client": "WhatsApp (Telemessage)"}}, "participants": [{"types": ["TO"], "value": {"&id": "299d7eb6-1eca-43d5-a7fb-fde74ee45293", "&key": "AccountPerson:299d7eb6-1eca-43d5-a7fb-fde74ee45293:*************", "communications": {"emails": ["<EMAIL>"], "phoneNumbers": [{"dialingCode": "ZA", "label": "MOBILE", "number": "+***********"}]}, "counterparty": {"name": "South Africa"}, "name": "<PERSON><PERSON>", "personalDetails": {"firstName": "<PERSON><PERSON>", "lastName": "<PERSON>"}, "retailOrProfessional": "N/A", "sourceIndex": "107", "sourceKey": "s3://x.uat.steeleye.co/flows/mymarket-universal-steeleye-person/x.csv", "structure": {"department": "x", "role": "x"}, "uniqueIds": ["<EMAIL>", "+***********"]}}], "roomId": "+*********** ***********", "roomName": "+*********** ***********", "stats": {"participantsCount": 1}, "timestamps": {"timestampEnd": "2024-09-29T12:52:31Z", "timestampStart": "2024-09-29T12:52:31Z"}}, "assessments": {"l1": {"id": "a774e313", "dateTime": "2024-10-15T12:00:01Z", "modelID": "", "threadID": "defcc4b1", "sourceKeyPrompt": "sample_path", "sourceKeyPromptResponse": "sample_path", "assessment": "Sensitive data.", "behaviour": "Risk", "recommendations": "Report to IT.", "severityScore": 6.0, "severityScoreWeighted": 6.0, "triggers": [{"trigger": "", "startChar": 0, "endChar": 1}]}, "l2": {"id": "a774e313", "dateTime": "2024-10-15T12:00:01Z", "modelID": "", "threadID": "defcc4b1", "sourceKeyPrompt": "sample_path", "sourceKeyPromptResponse": "sample_path", "assessment": "Sensitive data.", "behaviour": "Risk", "recommendations": "Report to IT.", "severityScore": 6.0, "severityScoreWeighted": 5.65, "triggers": [{"trigger": "", "startChar": 0, "endChar": 1}]}}}