{"hits": [{"&id": "ab12699f0762fea2f919f4431e603c9c4b755f6de49660a86c5f4cb24795b198f8e0fbfdf00bddbc09a44899c4ccad5382abce7186d8279d169f3aae79ce344a", "&uniqueProps": ["<EMAIL>", "<EMAIL>"], "subject": "cSurv Unit Test | Breach Awareness | zh.cn", "timestamps": {"timestampEnd": "2023-04-29T12:08:35Z", "timestampStart": "2023-04-29T12:08:28Z"}, "messageId": "82f1ade116f2c96c22ea6eceacdb4d39@qlik-1", "&key": "Email:ab12699f0762fea2f919f4431e603c9c4b755f6de49660a86c5f4cb24795b198f8e0fbfdf00bddbc09a44899c4ccad5382abce7186d8279d169f3aae79ce344a:1682770759864", "body": {"displayText": "empty ", "text": "我们被发现", "type": "HTML"}, "&model": "Email", "analytics": {"lexica": [{"behaviour": "Breach Awareness", "behaviourId": "732b6889-ee12-477a-94c1-ed492d4376f5", "subBehaviour": "Awareness Of Consequence Of Discovery", "subBehaviourId": "abd23376-b85a-4494-86b0-06965609aa99", "termId": "192d1d4a-c24d-4837-b3ee-1279678c50d4", "source": "body_text", "term": "我们被发现", "termBase": "发现", "termLanguage": "zh.cn", "termPaired": "我们", "termType": "COLLOCATED_TERM", "triggers": [{"trigger": "我们被发现", "triggerStartChar": 0, "triggerEndChar": 5}]}], "lexicaTopics": [{"source": "body_text", "termCount": 1, "topic": "USUK"}]}, "hasAttachment": false, "&version": 1, "&cascadeId": "7fb11269-e78a-436f-93d7-0e3d2f4d22f0", "&hash": "ddb61057c6c67dd96af8e3e2ac82fb3f29fe6d8464ca839c8d551b6cd718f8e2", "&timestamp": 1682770759864, "&user": "system"}, {"&id": "d854ed10ff0316f25423093928e84506be5f8c91a376bf5bdc0e0fcc035cdbdd8a78dd31fc75b387b5574a26554755302d186e753a92af102b7ee8ffc621f973", "&uniqueProps": ["<EMAIL>", "<EMAIL>"], "metadata": {"header": {"Authentication-Results": "amazonses.com;\r\n spf=pass (spfCheck: domain of steel-eye.com designates ************* as permitted sender) client-ip=*************; envelope-from=<EMAIL>; helo=eu-smtp-delivery-134.mimecast.com;\r\n dmarc=none header.from=steel-eye.com;", "Authentication-Results-Original": "dkim=none (message not signed)\r\n header.d=none;dmarc=none action=none header.from=steel-eye.com", "Content-Type": "multipart/related;\r\n\tboundary=\"_f499280f-dcb8-4b13-8b2e-57892e1fff7d_\"", "Date": "Sat, 29 Apr 2023 12:08:25 +0000", "From": "<<EMAIL>>"}, "messageId": "4cfc90f38a52bac0f7aef08396d5b0d9@qlik-1", "source": {"client": "Microsoft Exchange", "fileInfo": {"contentLength": 327466, "lastModified": "2023-04-29T12:08:34Z", "location": {"bucket": "pinafore.dev.steeleye.co", "key": "feeds/eml/steel-eye.com/pugtt58pq46btlp4t42hrnd94r3nofou7seorco1"}, "versionId": "sRa5bSaMVFU580zg36zKhwNCNqwMbL5z"}}, "threadId": "cSurv Unit Test | Breach Awareness | nl"}, "subject": "cSurv Unit Test | Breach Awareness | nl", "timestamps": {"timestampEnd": "2023-04-29T12:08:32Z", "timestampStart": "2023-04-29T12:08:25Z"}, "messageId": "4cfc90f38a52bac0f7aef08396d5b0d9@qlik-1", "&key": "Email:d854ed10ff0316f25423093928e84506be5f8c91a376bf5bdc0e0fcc035cdbdd8a78dd31fc75b387b5574a26554755302d186e753a92af102b7ee8ffc621f973:1682770759765", "body": {"displayText": " empty ", "text": "er is gdpr ", "type": "HTML"}, "&model": "Email", "analytics": {"lexica": [{"behaviour": "Breach Awareness", "behaviourId": "732b6889-ee12-477a-94c1-ed492d4376f5", "subBehaviour": "Awareness Of Breach Rules", "subBehaviourId": "374ac543-b973-4e95-9922-b341ee5431bd", "termId": "8a6bde0b-5997-46e8-b20d-309fcbc3a85c", "source": "body_text", "term": "er is gdpr", "termBase": "gdpr", "termLanguage": "nl", "termPaired": "er is", "termType": "COLLOCATED_TERM", "triggers": [{"trigger": "er is gdpr", "triggerStartChar": 0, "triggerEndChar": 10}]}, {"behaviour": "Breach Awareness", "behaviourId": "732b6889-ee12-477a-94c1-ed492d4376f5", "subBehaviour": "Awareness Of Breach Rules", "subBehaviourId": "374ac543-b973-4e95-9922-b341ee5431bd", "termId": "173af544-ec41-4f28-8218-8f39cc255d8d", "source": "body_text", "term": "er is gdpr", "termBase": "gdpr", "termLanguage": "nl", "termPaired": "er is", "termType": "COLLOCATED_TERM", "triggers": [{"trigger": "er is gdpr", "triggerStartChar": 0, "triggerEndChar": 10}]}], "lexicaTopics": [{"source": "body_text", "termCount": 1, "topic": "USUK"}, {"source": "body_text", "termCount": 1, "topic": "SOCIALMEDIA"}, {"source": "body_text", "termCount": 1, "topic": "SOCIALMEDIA"}]}, "hasAttachment": false, "&version": 1, "referenceMessageIds": ["<EMAIL>"], "&cascadeId": "7fb11269-e78a-436f-93d7-0e3d2f4d22f0", "&hash": "19cd83d1c01f980e2317b65d6b54d574215afef4c5d1e0cdf935a9fc152659ff", "&timestamp": 1682770759765, "&user": "system"}, {"&hash": null, "&id": "1a11aa111aaa1111aaaa11111aaaaa111111aaaaaa1", "&traitFqn": null, "&parent": null, "&key": "Message:1a11aa111aaa1111aaaa11111aaaaa111111aaaaaa1:1699000000000", "&model": "Message", "&timestamp": "1699000000000", "&user": "bbg-sink", "&validationErrors": null, "&cascadeId": null, "&version": null, "&link": null, "&uniqueProps": ["<EMAIL>"], "timestamps": {"created": null, "duration": null, "localTimestampEnd": null, "localTimestampEndTimeZone": null, "localTimestampStart": "2023-12-12T12:12:12", "localTimestampStartTimeZone": null, "timestampConnected": null, "timestampEnd": null, "timestampStart": "2023-12-12T12:12:12"}, "roomId": "PINK WAR ROOM", "metadata": {"source": {"client": "Instant Bloomberg", "fileInfo": {"location": {"bucket": "pinafore.dev.steeleye.co", "key": "feeds/task/bloomberg-shredder/commsMessage.ib.irs.BBG/20231212/commsMessage.ib.irs.BBG.20231212.xml"}}}}, "participants": [], "body": {"displayText": "Pink is the new black", "text": "Pink is the new black", "type": "PLAIN"}, "identifiers": {"fromId": "<EMAIL>", "domains": [{"value": "steel-eye.com", "types": ["FROM"]}], "localParts": [{"value": "test.tester", "types": ["FROM"]}]}}]}