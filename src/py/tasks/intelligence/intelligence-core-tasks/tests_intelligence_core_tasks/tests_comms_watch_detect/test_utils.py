import pytest
from intelligence_core_tasks.comms_watch_detect.utils import decide_flow_path
from se_elastic_schema.components.surveillance.behaviour_query import SurveillanceBehaviourQuery
from se_elastic_schema.components.surveillance.false_positive_reduction import (
    FalsePositiveReduction,
)
from se_elastic_schema.models.tenant.surveillance.surveillance_watch import SurveillanceWatch


class TestDecideFlowPath:
    surv_watch = SurveillanceWatch.validate({"name": "a_watch", "query": {"name": "a_query"}})

    def create_watch(self):
        return SurveillanceWatch.validate(
            {
                "name": "a_watch",
                "query": {
                    "name": "a_query",
                    "description": "a_description",
                    "kind": "BEHAVIOUR",
                    "lexicaBehaviour": {"name": "a_behaviour", "languages": ["pt"]},
                },
            }
        )

    @pytest.mark.parametrize(
        "hits_count, email_hits_count, path",
        [
            (2, 1, "comms_false_positive_reduction"),
            (1, 0, "hits_found"),
            (0, 0, "no_hits"),
        ],
    )
    def test_all_false_positive_reduction(self, hits_count, email_hits_count, path):
        surv_watch = self.create_watch()
        surv_watch.query.falsePositiveReduction = FalsePositiveReduction(
            excludedZones=[], excludedClassifications=[]
        )
        assert decide_flow_path(surv_watch, hits_count, email_hits_count, []) == path

    @pytest.mark.parametrize(
        "hits_count, email_hits_count, path",
        [
            (2, 1, "email_zoner"),
            (1, 0, "hits_found"),
            (0, 0, "no_hits"),
        ],
    )
    def test_zoner_only(self, hits_count, email_hits_count, path):
        surv_watch = self.create_watch()
        surv_watch.query.falsePositiveReduction = FalsePositiveReduction(excludedZones=[])
        assert decide_flow_path(surv_watch, hits_count, email_hits_count, []) == path

    @pytest.mark.parametrize(
        "hits_count, email_hits_count, path",
        [
            (2, 1, "email_classifier"),
            (1, 0, "hits_found"),
            (0, 0, "no_hits"),
        ],
    )
    def test_classifier_only(self, hits_count, email_hits_count, path):
        surv_watch = self.create_watch()
        surv_watch.query.falsePositiveReduction = FalsePositiveReduction(excludedClassifications=[])
        assert decide_flow_path(surv_watch, hits_count, email_hits_count, []) == path

    @pytest.mark.parametrize(
        "hits_count, email_hits_count, path",
        [
            (2, 1, "hits_found"),
            (1, 0, "hits_found"),
            (0, 0, "no_hits"),
        ],
    )
    def test_no_false_positive_reduction(self, hits_count, email_hits_count, path):
        surv_watch = self.create_watch()
        assert decide_flow_path(surv_watch, hits_count, email_hits_count, []) == path

    @pytest.mark.parametrize(
        "hits_count, email_hits_count, path",
        [
            (2, 1, "email_classifier"),
            (1, 0, "hits_found"),
            (0, 0, "no_hits"),
        ],
    )
    def test_bespoke_watch(self, hits_count, email_hits_count, path):
        surv_watch = self.create_watch()
        query = {
            "name": "a_query",
            "description": "a",
            "kind": "BESPOKE",
            "filter": {"flangVersion": "1", "chunks": []},
            "falsePositiveReduction": {"excludedZones": [], "excludedClassifications": []},
        }
        surv_watch.query = SurveillanceBehaviourQuery.validate(query)
        assert decide_flow_path(surv_watch, hits_count, email_hits_count, []) == path

    @pytest.mark.parametrize(
        "hits_count, email_hits_count, path",
        [
            (2, 1, "comms_false_positive_reduction"),
            (1, 0, "hits_found"),
            (0, 0, "no_hits"),
        ],
    )
    def test_infobarrier_watch(self, hits_count, email_hits_count, path):
        surv_watch = self.create_watch()
        query = {
            "name": "a_query",
            "description": "a",
            "kind": "TEMPLATE",
            "template": {"templateType": "DATA_LEAKAGE_INFO_BARRIER"},
            "infoBarrier": {"involvingProjectMembers": True},
            "filter": {"flangVersion": "1", "chunks": []},
            "falsePositiveReduction": {"excludedZones": [], "excludedClassifications": []},
        }
        surv_watch.query = SurveillanceBehaviourQuery.validate(query)
        assert decide_flow_path(surv_watch, hits_count, email_hits_count, ["project alpha"]) == path
