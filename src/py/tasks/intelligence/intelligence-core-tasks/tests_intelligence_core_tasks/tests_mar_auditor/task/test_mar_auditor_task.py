import datetime
import json
import pytest
from aries_io_event.io_param import IOParamFieldSet
from aries_io_event.task import TaskFieldSet
from aries_io_event.workflow import WorkflowFieldSet
from aries_task_link.models import AriesTaskInput
from intelligence_core_tasks.mar_auditor import enrichment
from pathlib import Path
from se_db_utils import database
from se_enums.cloud import CloudProviderEnum
from tenant_db.models.mar_audit.aggregated_step_audit import AggregatedStepAudit
from tenant_db.models.mar_audit.market_abuse_audit import MarketAbuseAudit
from tenant_db.models.mar_audit.step_audit import StepAudit
from tests_intelligence_core_tasks.tests_mar_auditor.task.helpers.mock_sqlalchemy import (
    FakeDatabase,
    FakeSession,
)
from tests_intelligence_core_tasks.tests_mar_auditor.task.helpers.mock_utils import (
    fake_get_tenant_workflow_auditor,
)

TEST_DATA = Path(__file__).parent.joinpath("test_data")


@pytest.fixture
def mar_auditor_task(monkeypatch, set_env_vars):
    from aries_config_cached_client.tenant_workflow import CachedTenantWorkflowAPIClient

    monkeypatch.setattr(CachedTenantWorkflowAPIClient, "get", fake_get_tenant_workflow_auditor)

    from intelligence_core_tasks.mar_auditor.mar_auditor_task import MarAuditorTask

    return MarAuditorTask


@pytest.fixture(autouse=True)
def mocks_for_auditor_task(monkeypatch, set_env_vars):
    monkeypatch.setattr(database, "Database", FakeDatabase)
    monkeypatch.setattr(enrichment.StepAuditEnrichment, "get_es_repo", lambda x: None)


def sample_input() -> AriesTaskInput:
    workflow = WorkflowFieldSet(
        trace_id="tests",
        name="mar_auditor",
        stack="foo",
        tenant="bar",
        start_timestamp=datetime.datetime.utcnow(),
    )
    params = {
        "mar_audit_url": str(
            TEST_DATA.joinpath("mar_audit_execution_files/AUDIT__date__time.json")
        ),
        "se_realm": "mares8.dev.steeleye.co",
        "step_audit_url": str(
            TEST_DATA.joinpath("mar_audit_execution_files/STEP/STEP__date__time.json")
        ),
        "aggregated_audit_url": str(
            TEST_DATA.joinpath("mar_audit_execution_files/AGGR/AGGR__date__time.json")
        ),
    }

    input_param = IOParamFieldSet(params=params)

    task = TaskFieldSet(name="mar_auditor", version="latest", success=False)
    return AriesTaskInput(workflow=workflow, input_param=input_param, task=task)


class TestMarAuditor:
    @pytest.mark.parametrize(
        "cloud_provider, folder, expected_folder_path",
        [
            [
                CloudProviderEnum.LOCAL,
                "AGGR",
                str(TEST_DATA.joinpath("mar_audit_execution_files/AGGR/AGGR__date__time.json")),
            ],
            [
                CloudProviderEnum.LOCAL,
                "STEP",
                str(TEST_DATA.joinpath("mar_audit_execution_files/STEP/STEP__date__time.json")),
            ],
        ],
    )
    def test_glob_files_from_folder(
        self, cloud_provider, folder, expected_folder_path, mar_auditor_task
    ):
        """Test if glob_files_from_folder is calculating the values inside the
        method as expected.

        This is checked by asserting the arguments the function
        cloud_list_files receives
        """
        aries_task_input = sample_input()

        task = mar_auditor_task(aries_task_input=aries_task_input)
        task.cloud_provider = CloudProviderEnum.LOCAL

        result = task.glob_files_from_folder(folder_name=folder)

        assert result[0] == expected_folder_path

    def test_mar_auditor_task(self, monkeypatch, mocks_for_auditor_task, mar_auditor_task):
        """The asserts in this test are done in the mock method for
        execute_db_operations We can validate the number of rows that were to
        be added to the DB."""

        def fake_bulk_save_objects(self_object, list_of_objects):
            if isinstance(list_of_objects[0], MarketAbuseAudit):
                no_market_abuse = 0
                no_aggregated = 0
                for entry in list_of_objects:
                    if isinstance(entry, MarketAbuseAudit):
                        no_market_abuse += 1
                    if isinstance(entry, AggregatedStepAudit):
                        no_aggregated += 1

                assert no_market_abuse == 1
                assert no_aggregated == 45

            if isinstance(list_of_objects[0], StepAudit):
                assert len(list_of_objects) == 819

        def fake_search_after(*args, **kwargs):
            with open(str(TEST_DATA.joinpath("enriched_steps.json")), "r") as file:
                enrichment_data = json.load(file)
            return enrichment_data

        aries_task_input = sample_input()
        task = mar_auditor_task(aries_task_input=aries_task_input)
        task.cloud_provider = CloudProviderEnum.LOCAL

        monkeypatch.setattr(enrichment, "search_after_query", fake_search_after)
        monkeypatch.setattr(FakeSession, "bulk_save_objects", fake_bulk_save_objects)

        task.execute()
