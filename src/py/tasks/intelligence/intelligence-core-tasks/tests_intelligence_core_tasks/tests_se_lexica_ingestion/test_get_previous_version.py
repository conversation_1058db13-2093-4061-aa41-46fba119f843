import os
import pytest
from datetime import datetime
from fsspec.core import get_fs_token_paths
from intelligence_core_tasks.se_lexica_ingestion.get_previous_version import get_previous_version
from intelligence_core_tasks.se_lexica_ingestion.static import (
    FILENAME_SEPARATOR_PATTERN,
    FILENAME_TIMESTAMP_PATTERN,
)
from pathlib import Path
from surveillance_utils.test_mock_helpers import data_lake

LEXICA_FOLDER_PATH = "./lexica/storage/ready_to_ingest_lexica/"


@pytest.fixture
def valid_lexica_terms_path():
    terms_csv_path = str(Path(__file__).parent.joinpath("data", "steeleye_lexica_small_v1.csv"))

    return terms_csv_path


@pytest.fixture
def valid_lexica_topics_path():
    topics_csv_path = str(Path(__file__).parent.joinpath("data", "topic_definitions_small_v1.csv"))

    return topics_csv_path


def test_get_previous_version(
    valid_lexica_terms_path, valid_lexica_topics_path, mock_s3_bucket, monkeypatch
):
    with data_lake():
        timestamp = datetime.now().strftime(FILENAME_TIMESTAMP_PATTERN)
        lexica_folder = f"{LEXICA_FOLDER_PATH}/{timestamp}{FILENAME_SEPARATOR_PATTERN}v1"
        # copy files into temp lake
        os.makedirs(lexica_folder)
        fs, _, fs_folder = get_fs_token_paths(os.getcwd())
        terms_dest = f"{lexica_folder}/se_lexica_terms___v1.csv"
        topics_dest = f"{lexica_folder}/se_lexica_topics___v1.csv"
        behaviour_dest = f"{lexica_folder}/se_lexica_behaviours___v1.csv"
        sub_behaviour_dest = f"{lexica_folder}/se_lexica_sub_behaviours___v1.csv"
        fs.cp(valid_lexica_terms_path, terms_dest)
        fs.cp(valid_lexica_topics_path, topics_dest)
        fs.cp(valid_lexica_terms_path, behaviour_dest)
        fs.cp(valid_lexica_terms_path, sub_behaviour_dest)

        previous_version = get_previous_version()

        assert previous_version == 1


def test_no_previous_folder(
    valid_lexica_terms_path, valid_lexica_topics_path, mock_s3_bucket, monkeypatch
):
    with data_lake():
        # copy files into temp lake
        os.makedirs(LEXICA_FOLDER_PATH)
        fs, _, fs_folder = get_fs_token_paths(os.getcwd())

        previous_version = get_previous_version()

        assert previous_version == 0
