import pandas as pd
import pytest
import tempfile
from intelligence_core_tasks.se_lexica_ingestion.csv_file_io import read_csv
from tests_intelligence_core_tasks.tests_se_lexica_ingestion.conftest import TERMS_URL


def test_checks_filepath_is_csv():
    terms = TERMS_URL.split(".")[0] + ".pdf"

    with pytest.raises(Exception) as e:
        read_csv(terms)

    assert "filepath is not an csv" in str(e)


def tests_check_path_is_directory():
    temp_dir = tempfile.mkdtemp(suffix="a_dir.csv")

    with pytest.raises(Exception) as e:
        read_csv(temp_dir)

    assert "path is missing file name" in str(e)


def test_handles_other_exceptions():
    terms = ".csv"

    with pytest.raises(Exception) as e:
        read_csv(terms)

    assert "an error occurred while fetching" in str(e)


def test_all_good():
    csv_path = f"{tempfile.mkdtemp()}/a_file.csv"

    lexica_dataframe = pd.DataFrame(
        {
            "category": ["a_cat", "a_cat", "b_cat", "c_cat"],
            "categorySub": ["a_sub", "a_sub", "b_sub", "b_sub"],
            "term": ["a_term", "a_term", "b_term", "b_term"],
        }
    )
    lexica_dataframe.to_csv(csv_path, index=False)

    resp_df = read_csv(csv_path)

    assert all(resp_df == lexica_dataframe)
