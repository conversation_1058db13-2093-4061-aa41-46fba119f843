import datetime
import logging
from aries_io_event.io_param import IOParamFieldSet
from aries_io_event.task import TaskFieldSet
from aries_io_event.workflow import WorkflowFieldSet
from aries_task_link.models import AriesTaskInput
from intelligence_core_tasks.mar_auditor.main import run_mar_auditor

logger = logging.getLogger("mar_auditor")


def sample_input() -> AriesTaskInput:
    workflow = WorkflowFieldSet(
        trace_id="mares8_itv3_algo_1",
        name="mar_itv3_refinitiv",
        stack="dev-shared-2",
        tenant="mares8",
        start_timestamp=datetime.datetime.utcnow(),
    )
    input_param = IOParamFieldSet(
        # params={
        #     "mar_audit_url": "s3://mares8.dev.steeleye.co/lake/ingress/mar_audit/structured/2024/05/10/INSIDER_TRADING_V3_REFINITIV__3a281b5d-6875-46d6-a871-0525f17d515e/AUDIT__20240510__101954.json",
        #     "se_realm": "mares8.dev.steeleye.co",
        #     "step_audit_url": "s3://mares8.dev.steeleye.co/lake/ingress/mar_audit/structured/2024/05/10/INSIDER_TRADING_V3_REFINITIV__3a281b5d-6875-46d6-a871-0525f17d515e/STEP/STEP__20240510__101954.json",
        #     "aggregated_audit_url": "s3://mares8.dev.steeleye.co/lake/ingress/mar_audit/structured/2024/05/10/INSIDER_TRADING_V3_REFINITIV__3a281b5d-6875-46d6-a871-0525f17d515e/AGGR/AGGR__20240510__101954.json",
        #     "step_audit_lines": 6143,
        # }
        params={
            "total_scenarios": 0,
            "watch_metadata_file_uri": "s3://mares8.dev.steeleye.co/aries/ingest/mar_itv3_refinitiv/2024/04/24/mares8_itv3_algo_1/mar-create-group/9e22266af540f651e77eb3f30890c1acb0eb5b2b0bc6eb07e2107e334614933c___watch_metadata.json",
            "upper_bound": datetime.datetime(2025, 2, 28, 2, 50, 0, 96255),
        }
    )

    task = TaskFieldSet(name="mar_auditor", version="latest", success=False)
    return AriesTaskInput(workflow=workflow, input_param=input_param, task=task)


# local runs are only possible if env_vars are added
if __name__ == "__main__":
    logger.info("Starting execution...")
    debug_aries_task_input = sample_input()
    output = run_mar_auditor(debug_aries_task_input)
    logger.info(f"Finished executing with output {output}")
