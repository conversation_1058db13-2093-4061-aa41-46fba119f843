import datetime
import logging
from aries_io_event.io_param import IOParamFieldSet
from aries_io_event.task import TaskFieldSet
from aries_io_event.workflow import WorkflowFieldSet
from aries_task_link.models import AriesTaskInput
from intelligence_core_tasks.trade_watch_enrich.main import run_trade_watch_enrich

logger = logging.getLogger("run_trade_watch_detect")

workflow = WorkflowFieldSet(
    name="run_trade_watch",
    stack="dev-shared-1",
    tenant="mar",
    start_timestamp=datetime.datetime.utcnow(),
)
input_param = IOParamFieldSet(
    params=dict(
        watch_id="476d0764-b8e1-4489-9caa-8bf1c8b80549",
        watch_execution_id="ff67e9c5-0fe2-4dc4-a40f-df3933564969",
        order_hits_uri="az://mar/aries/ingest/case_bulk/2024/04/05/34c7c9bf-53a6-47d5-bc9a-c871f5efce8f/trade_watch_detect/d97c02cdd923dd0cb8050f04e60b5683e8880cba731ad61de84dd2b768f4fb3a___.ndjson",
    )
)
task = TaskFieldSet(name="run_trade_watch", version="latest", success=False)
sample_input = AriesTaskInput(workflow=workflow, input_param=input_param, task=task)

if __name__ == "__main__":
    logger.debug("Starting execution...")
    output = run_trade_watch_enrich(sample_input)
    logger.debug(f"Finished executing with output {output}")
