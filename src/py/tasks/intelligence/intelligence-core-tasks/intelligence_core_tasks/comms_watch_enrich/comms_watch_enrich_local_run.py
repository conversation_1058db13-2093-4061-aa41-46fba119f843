import logging
from aries_io_event.io_param import IOParamFieldSet
from aries_io_event.task import TaskFieldSet
from aries_io_event.workflow import WorkflowFieldSet
from aries_task_link.models import AriesTaskInput
from intelligence_core_tasks.comms_watch_enrich.main import run_comms_watch_enrich

logger = logging.getLogger("run_comms_watch_enrich")


def sample_input() -> AriesTaskInput:
    input = {
        "task": None,
        "workflow": {
            "trace_id": "1234567",
            "name": "run_comms_watch",
            "tenant": "irises8",
            "stack": "dev-shared-2",
            "start_timestamp": "2023-09-19T14:00:00.000000+00:00",
        },
        "io_param": {
            "params": {
                "upper_bound_date": "2025-02-24T16:14:00.281508Z",
                "watch_id": "6e4b1f18-16c8-4f1f-a6f4-ab50c3845033",
                "watch_execution_id": "27ce76f6-ae36-4898-822a-ce77405ca7ef",
                "flow_path": "hits_found",
                "non_email_hits_uri": "s3://irises8.dev.steeleye.co/aries/ingest/run_comms_watch/2023/09/19/1234567/comms_watch_detect/d36121b5d57e67b09ee40dd4d36ceb5adfefcef2b3bd2a65cbf574732e79a0c4___.ndjson",
                "file_uri": "s3://irises8.dev.steeleye.co/aries/ingest/run_comms_watch/2023/09/19/1234567/comms_watch_detect/6a33aba963b954a3fd959e143e86b2a3c6bd00438e90835f22b9c5088a8d1ac4___.ndjson",
            }
        },
    }

    workflow = WorkflowFieldSet.validate(input["workflow"])
    input_param = IOParamFieldSet(params=input["io_param"]["params"])
    task = TaskFieldSet(name="comms_watch_enrich", version="latest", success=False)
    return AriesTaskInput(workflow=workflow, input_param=input_param, task=task)


# local runs are only possible if env_vars are added
if __name__ == "__main__":
    logger.info("Starting execution...")
    output = run_comms_watch_enrich(sample_input())
    logger.info(f"Finished executing with output {output}")
