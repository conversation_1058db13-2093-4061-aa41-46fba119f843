from aries_io_event.io_param import IOParamFieldSet
from aries_io_event.task import TaskFieldSet
from aries_io_event.workflow import WorkflowFieldSet
from aries_task_link.models import AriesTaskInput
from datetime import datetime


def sample_input() -> AriesTaskInput:
    workflow = WorkflowFieldSet(
        name="kerv_voice_poll",
        stack="dev-blue",
        tenant="pinafore",
        start_timestamp=datetime.now(),
    )
    input_param = IOParamFieldSet(
        params=dict(
            custom_lake_path="test/",
            look_back_days=3,  # optional
            from_date="2023-04-01",  # optional
            to_date="2023-04-05",  # optional
            force_pull=True,  # optional
            remote_directory_type="recordings",  # optional: "recordings" or "root"
        )
    )
    task = TaskFieldSet(name="test", version="latest", success=False)
    return AriesTaskInput(workflow=workflow, input_param=input_param, task=task)
