import logging
from benchmark_mode import benchmark
from integration_poller_tasks.cloud9_voice_poller.cloud9_voice_poller_sample_input import (
    sample_input,
)
from integration_poller_tasks.cloud9_voice_poller.static import POLLER_NAME
from integration_poller_tasks.main import integration_poller_tasks_run

logger = logging.getLogger(POLLER_NAME)


@benchmark
def main():
    logger.info("Starting execution...")
    output = integration_poller_tasks_run(aries_task_input=sample_input())
    logger.info(f"Finished executing with output {output}")


if __name__ == "__main__":
    main()
