import logging
from aries_se_comms_tasks.abstractions.pollers.abstract_sftp_poller import (
    AbstractSFTPPoller,
    BaseSFTPPollerInputParams,
)
from aries_task_link.models import AriesTaskInput
from omegaconf import DictConfig, ListConfig
from typing import Any, Dict, List

logger = logging.getLogger(__name__)


class EnmaccPoller(AbstractSFTPPoller):
    """
    For this poller, the files in SFTP are fetched from the folder
    specified by the input param 'sftp_folder'

    """

    def __init__(
        self,
        aries_task_input: AriesTaskInput,
        config: DictConfig | ListConfig,
    ):
        super().__init__(
            aries_task_input=aries_task_input,
            config=config,
            input_params_class=BaseSFTPPollerInputParams,
            is_sftp_server_date_partitioned=False,
        )

    def get_remote_dirs(self, *args, **kwargs) -> List[str]:
        """Enmacc implementation for getting remote directories.
        Note that the format of the directories in the SFTP Server is specified
        as an input param. For e.g., it is as
        follows for BP:
        /enmacc-chat-conversation-archive/BP/production/csv/<xyz>.csv
        and
        /enmacc-chat-conversation-archive/BP/production/json/<xyz>.json

        """
        return [self._sftp_folder_path]

    def post_filter(
        self, target_files: Dict[str, List[Dict[str, Any]]], *args, **kwargs
    ) -> Dict[str, List[Dict[str, Any]]]:
        """No specific post-filter logic needed."""
        return target_files
