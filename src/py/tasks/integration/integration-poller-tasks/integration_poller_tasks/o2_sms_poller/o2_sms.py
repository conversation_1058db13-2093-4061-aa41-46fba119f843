import hashlib
import logging
import os
import re
from aries_task_link.models import AriesTaskInput, AriesTaskResult
from datetime import datetime, timedelta
from integration_poller_tasks.o2_sms_poller.static import (
    O2_SMS_POLLER_BATCH_SIZE,
    O2_SMS_POLLER_RAW_PATH_PREFIX,
    O2_SMS_POLLER_REMOTE_DIR,
    O2_SMS_TRANSFORM_WORKFLOW_NAME,
)
from pydantic import BaseModel, Field
from se_comms_ingress_utils.abstractions.o2_poller import AbstractO2Poller
from se_fsspec_utils.file_utils import get_file, is_exist


class InputParams(BaseModel):
    force_pull: bool = False
    custom_lake_path: str | None = Field(None, min_length=1, max_length=255)
    should_event: bool = False


class O2SMSPoller(AbstractO2Poller):
    """O2SMSPoller polls sms data."""

    def _clean_up_local_paths(self, path) -> None:
        if os.path.exists(path):
            os.remove(path)

    def _should_write(self, target_file_path, file_name, source_file_path):
        # For o2 sms, we need the checksum logic.
        if is_exist(self._destination_fs, target_file_path):
            # download both files, compare checksums
            # if checksum is same, continue back to the loop
            timestamp = str(datetime.utcnow().timestamp()).replace(".", "_")

            local_sftp_path = f"{timestamp}_sftp_{file_name}"
            logging.info(f"[DOWNLOADING] from sftp to {local_sftp_path}")
            get_file(
                fs=self._sftp_fs,
                remote_file_path=source_file_path,
                local_file_path=local_sftp_path,
            )
            logging.info(f"[DOWNLOADED] from sftp to {local_sftp_path}")

            local_s3_path = f"{timestamp}_s3_{file_name}"
            logging.info(f"[DOWNLOADING] from s3 to {local_s3_path}")
            get_file(
                fs=self._destination_fs,
                remote_file_path=target_file_path,
                local_file_path=local_s3_path,
            )
            logging.info(f"[DOWNLOADED] from s3 to {local_s3_path}")

            sha256_checksum_sftp = hashlib.sha256()
            sha256_checksum_s3 = hashlib.sha256()

            with open(local_sftp_path, "rb") as f:
                sha256_checksum_sftp.update(f.read())
            with open(local_s3_path, "rb") as f:
                sha256_checksum_s3.update(f.read())

            # clean local paths
            self._clean_up_local_paths(local_sftp_path)
            self._clean_up_local_paths(local_s3_path)

            if sha256_checksum_sftp.hexdigest() == sha256_checksum_s3.hexdigest():
                logging.info(
                    f"[NOT PROCESSING] {file_name} as the checksum is same as {target_file_path}"
                )
                return False

        logging.info(
            f"[PROCESSING] {file_name} as the checksum is different from {target_file_path}"
        )
        return True

    def _get_date_to_filter(self, date_):
        # date_ = '2023-09'
        return date_.split("-")[0]

    def _initialize_input_params(self, aries_task_input):
        input_params = InputParams.validate(aries_task_input.input_param.params)

        # Access the validated input parameters
        self._input_params = input_params

    def _validate_filename_date(self, date, filename):
        """Verifies if the filename matches the expected pattern with the
        specified date.

        The expected filename pattern is '<digits>_SMSMessages_<year>_<month>.csv'.
        The function checks if the extracted year and month from the
        filename match the provided date.

        Parameters:
        - date (str): The expected date in the format 'YYYY-MM'.
        - filename (str): The filename to validate.

        Returns:
        bool: True if the filename matches the pattern and has the specified date, False otherwise.
        """
        pattern = r"^\d+_SMSMessages_(\d{4})_(\d{2})\.csv$"
        file_parts = re.match(pattern, filename)
        if file_parts:
            file_date = "-".join(file_parts.groups())  # 2023-12
            if file_date == date:
                return True
        return False

    def __init__(self, aries_task_input: AriesTaskInput, config):
        super().__init__(
            aries_task_input=aries_task_input,
            config=config,
            transform_flow_name=O2_SMS_TRANSFORM_WORKFLOW_NAME,
            raw_path_prefix=O2_SMS_POLLER_RAW_PATH_PREFIX,
            remote_directory=O2_SMS_POLLER_REMOTE_DIR,
            batch_size=O2_SMS_POLLER_BATCH_SIZE,
        )

    def _no_trigger_event(self) -> bool:
        if self._input_params.custom_lake_path and not self._input_params.should_event:
            return True
        return False

    def _is_custom_path(self) -> str | None:
        if self._input_params.custom_lake_path:
            return self._input_params.custom_lake_path.strip("/")
        return None

    def run_poller(self) -> AriesTaskResult:
        """run_poller exceutes the poller steps.

        :return: task result
        :rtype: AriesTaskResult
        """
        self._workflow_start_timestamp = datetime.utcnow()

        if "backfill_months" in self._aries_task_input.input_param.params:
            all_dates = self._aries_task_input.input_param.params["backfill_months"]
            # back-fill month example: [2023-09, 2023-06, 2022-07] (YYYY-MM)
        else:
            # normal run
            # get yesterday's date
            yesterday = datetime.utcnow().date() - timedelta(days=1)
            this_month = yesterday.strftime("%Y-%m")
            if 1 <= yesterday.day <= 5:
                last_month = (yesterday.replace(day=1) - timedelta(days=1)).strftime("%Y-%m")
                all_dates = [this_month, last_month]
            else:
                all_dates = [this_month]

        logging.info(f"Processing files from sftp for the following dates: `{all_dates}`")

        self._process_batch(all_dates=all_dates)

        return AriesTaskResult(output_param=None, app_metric=self._app_metric)
