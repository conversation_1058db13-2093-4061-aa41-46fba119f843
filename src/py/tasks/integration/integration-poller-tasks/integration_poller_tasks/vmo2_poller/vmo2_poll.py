import httpx
import json
import logging
import requests
from aries_se_comms_tasks.feeds.voice.vmo2_voice.static import Vmo2SourceColumns
from aries_task_link.models import AriesTaskInput, AriesTaskResult
from integration_poller_tasks.vmo2_poller.static import Vmo2Static
from se_comms_ingress_utils.abstractions.api_voice_poller import AbstractAPIVoicePoller
from se_comms_ingress_utils.common_util import (
    get_poll_from_date_and_backfill,
    update_poller_last_execution_time,
)
from se_data_lake.lake_path import get_attachment_file_path, get_streamed_poller_file_path
from se_fsspec_utils.file_utils import retriable_call
from typing import Callable

logger = logging.getLogger(Vmo2Static.POLLER_NAME)

ATTACHMENT_FAILURES: list = []


class SomeAttachmentsNotDownloadedException(Exception):
    pass


class MetadataDownloadFailureException(Exception):
    pass


class Vmo2Poll(AbstractAPIVoicePoller):
    def __init__(self, aries_task_input: AriesTaskInput, config):
        super().__init__(aries_task_input, config)
        self._jwt_token = self._get_jwt_token()

    def _get_jwt_token(self):
        """Authenticate with Vmo2 API and get JWT token."""
        logger.info("[RETRIEVING] JWT token from Vmo2 API")

        url = (
            f"{self._secrets.url.strip('/')}/apigateway/acp/api/auth/v1/"
            f"{self._secrets.account_id}/auth-token"
        )

        request_body = {"username": self._secrets.username, "password": self._secrets.password}

        response = self._api_request(
            requests.post,
            url=url,
            json=request_body,
            headers={
                "Accept": "application/json",
                "Content-Type": "application/json",
                "User-Agent": Vmo2Static.USER_AGENT,  # noqa E501
            },
            timeout=Vmo2Static.REQUEST_TIMEOUT,
            auth_required=False,
            retry_auth=False,
        )

        token = response.json()["jwt"]
        if not token:
            raise Exception("Unable to retrieve JWT token")

        logger.info("[RETRIEVED] JWT token from Vmo2 API")
        return token

    def _get_initial_cursor(self, call_end_time_inclusive_utc: str):
        """Get initial cursor for API calls."""

        # Add callEndTimeInclusiveUtc to set the from_date for the data fetch from the api
        # It will get messages from that time, until now
        url = (
            f"{self._secrets.url.strip('/')}/apigateway/acp/api/interaction/v2/"
            f"{self._secrets.account_id}/{self._secrets.solution_id}/cursor/"
            f"initialcursor?callEndTimeInclusiveUtc={call_end_time_inclusive_utc}"
        )

        response = self._api_request(
            requests.get,
            url=url,
            headers={
                "accept": "*/*",
                "User-Agent": Vmo2Static.USER_AGENT,  # noqa E501
            },
            timeout=Vmo2Static.REQUEST_TIMEOUT,
        )

        return response.json()[Vmo2Static.NEXT_CURSOR]

    def _get_next_records(self, cursor: str, limit: int = 100, requested_file_format: str = "wav"):
        """
        Get next batch of records using cursor.

        :returns: the complete response which includes:
        - metadata.nextCursor: cursor for the next page of results
        - interactions: list of interaction records
        """
        url = (
            f"{self._secrets.url.strip('/')}/apigateway/acp/api/interaction/v2/"
            f"{self._secrets.account_id}/{self._secrets.solution_id}/cursor/nextrecords"
        )
        params = {"cursor": cursor, "limit": limit, "requestedFileFormat": requested_file_format}

        response = self._api_request(
            requests.get,
            url=url,
            headers={
                "accept": "*/*",
                "User-Agent": Vmo2Static.USER_AGENT,  # noqa E501
            },
            params=params,
            timeout=Vmo2Static.REQUEST_TIMEOUT,
        )

        return response.json()

    def _get_attachment(self, media_url: str):
        response = self._api_request(
            requests.get,
            url=media_url,
            headers={
                "accept": "*/*",
                "User-Agent": Vmo2Static.USER_AGENT,  # noqa E501
            },
            timeout=Vmo2Static.REQUEST_TIMEOUT,
        )
        return response.content

    def _download_sms_media(self, interaction: dict) -> tuple[bytes, str]:
        raise NotImplementedError

    def _download_call_media(self, interaction: dict) -> tuple[bytes, str]:
        """Download CALL Type media content for an interaction."""
        media_url = interaction["callRecordingUrl"]
        file_extension = ".wav"

        logger.info(
            f"[DOWNLOADING] media for interaction ID: "
            f"{interaction[Vmo2SourceColumns.INTERACTION_ID]}"
        )
        response = self._get_attachment(media_url=media_url)

        return response, file_extension

    def _process_interaction(self, interaction: dict, attachment_object_path_prefix: str) -> dict:
        """Process a single interaction - download media and add metadata."""
        interaction_id = interaction[Vmo2SourceColumns.INTERACTION_ID]
        try:
            if interaction[Vmo2Static.INTERACTION_TYPE] == "Call":
                media_content, file_extension = self._download_call_media(interaction)
                file_name = f"{interaction_id}{file_extension}"
                attachment_object_path = f"{attachment_object_path_prefix.rstrip('/')}/{file_name}"
                attachment_file_uri = (
                    f"{self._poller_tenant_workflow_config.tenant.lake_prefix.rstrip('/')}/"
                    f"{attachment_object_path}"
                )

                logger.info(f"[UPLOADING] attachment to `{attachment_file_uri}`")

                with retriable_call(self._destination_fs.open, attachment_file_uri, mode="wb") as f:
                    f.write(media_content)

                logger.info(f"[UPLOADED] attachment to `{attachment_file_uri}`")

                interaction["steeleye_meta"] = {"attachment_object_path": attachment_object_path}
            else:
                logger.info(
                    f"[SKIPPING] attachment upload for interactionType: "
                    f"{interaction[Vmo2Static.INTERACTION_TYPE]}"
                )

        except Exception as e:
            error_message = f"Failed to process attachment {interaction_id}: {str(e)}"
            logger.error(f"[ERROR] {error_message}")
            self._app_metric.metrics["generic"]["errored_count"] += 1
            ATTACHMENT_FAILURES.append(error_message)
            interaction["steeleye_meta"] = {"attachment_object_path": ""}

        return interaction

    def _get_metadata(self, interactions, path_prefix):
        """
        Write metadata to cloud storage for each interaction record.

        :param args:
        - interactions: List of interaction dictionaries
        - path_prefix cloud storage path prefix for storing the metadata
        """
        for idx, interaction in enumerate(interactions):
            if interaction[Vmo2Static.INTERACTION_TYPE] == "Call":
                try:
                    metadata_json = interaction.get("metadataJson")
                    metadata = {
                        Vmo2SourceColumns.SUBSCRIBER_PHONE_NUMBER: interaction.get(
                            "subscriberPhoneNumber"
                        ),
                        Vmo2SourceColumns.THIRD_PARTY_PHONE_NUMBER: interaction.get(
                            "thirdPartyPhoneNumber"
                        ),
                        Vmo2SourceColumns.DIRECTION: interaction.get("direction"),
                        Vmo2SourceColumns.START_DATE_TIME: interaction.get("startDateTimeUtc"),
                        Vmo2SourceColumns.END_DATE_TIME: interaction.get("endDateTimeUtc"),
                        Vmo2SourceColumns.DURATION: interaction.get("durationInSeconds"),
                        Vmo2SourceColumns.INTERACTION_ID: interaction.get("interactionId"),
                        Vmo2SourceColumns.SIP_CALL_ID: (
                            metadata_json.get("SipCallId")
                            if isinstance(metadata_json, dict)
                            else None
                        ),
                        Vmo2SourceColumns.RECORDING_STATUS: interaction.get("recordingStatus"),
                        Vmo2SourceColumns.STEELEYE_META_ATTACHMENT_PATH: interaction.get(
                            "steeleye_meta", {}
                        ).get("attachment_object_path"),
                    }

                    # Create a unique file path for each metadata record
                    interaction_id = interaction[Vmo2SourceColumns.INTERACTION_ID]
                    metadata_path = f"{path_prefix}/metadata_{interaction_id}.json"

                    logger.info(
                        f"[UPLOADING] metadata for interaction ID "
                        f"{interaction_id} to {metadata_path}"
                    )

                    # Write the metadata to cloud storage
                    with retriable_call(self._destination_fs.open, metadata_path, mode="w") as f:
                        json.dump(metadata, f)

                    logger.info(f"[UPLOADED] metadata for interaction ID {interaction_id}")

                except Exception as e:
                    error_message = (
                        f"Failed to upload metadata for interaction ID "
                        f"{interaction.get(Vmo2SourceColumns.INTERACTION_ID, 'unknown')}: {str(e)}"
                    )
                    logger.error(f"[ERROR] {error_message}")
                    self._app_metric.metrics["generic"]["errored_count"] += 1
            else:
                logger.info(
                    f"[SKIPPING] metadata download for interactionType: "
                    f"{interaction[Vmo2Static.INTERACTION_TYPE]}"
                )

    def run_poller(self) -> AriesTaskResult:
        # Using get_poll_from_date_and_backfill() here because the API only supports a from_date,
        # and not a to_date, so the original get_poll_interval_and_backfill() would not work.
        poll_interval_and_backfill_info = get_poll_from_date_and_backfill(
            aries_task_input=self._aries_task_input,
            workflow_last_executed=self._poller_tenant_workflow_config.workflow_last_executed,
            timestamp_now=self._timestamp_now,
        )
        from_date = poll_interval_and_backfill_info.from_date
        end_date = poll_interval_and_backfill_info.to_date

        path = get_streamed_poller_file_path(
            workflow_name=self._workflow_name,
            date=from_date.strftime("%Y-%m-%d"),
            custom_path=self._custom_lake_path,
            is_evented=True,
        )
        path_prefix = f"{self._poller_tenant_workflow_config.tenant.lake_prefix.rstrip('/')}/{path}"
        attachment_object_path_prefix = get_attachment_file_path(
            workflow_name=self._workflow_name,
            workflow_start_timestamp=self._timestamp_now,
            workflow_trace_id=self._aries_task_input.workflow.trace_id,
            custom_path=self._custom_lake_path,
        )

        current_cursor = self._get_initial_cursor(
            call_end_time_inclusive_utc=from_date.strftime("%Y-%m-%dT%H:%M:%S.%fZ")
        )
        logger.info(f"Retrieved initial cursor: {current_cursor}")

        processed_count = 0

        while True:
            logger.info(f"[FETCHING] records with cursor: {current_cursor}")
            response = self._get_next_records(current_cursor)

            interactions = response.get(Vmo2Static.INTERACTIONS, [])
            if not interactions:
                logger.info("[NO MORE INTERACTIONS]")
                break

            logger.info(f"[FETCHED] {len(interactions)} interactions")

            # Get next cursor from the response metadata
            next_cursor = response[Vmo2Static.METADATA][Vmo2Static.NEXT_CURSOR]

            batch_interactions = []
            for interaction in interactions:
                processed_interaction = self._process_interaction(
                    interaction, attachment_object_path_prefix
                )
                batch_interactions.append(processed_interaction)

            if batch_interactions:
                try:
                    self._get_metadata(batch_interactions, path_prefix)
                    logger.info(
                        f"[UPLOADED] batch of {len(batch_interactions)} interactions metadata"
                    )
                    processed_count += len(batch_interactions)
                except Exception as e:
                    logger.error(f"[ERROR] Failed to fetch Vmo2 metadata: {str(e)}")
                    raise MetadataDownloadFailureException(
                        "Metadata could not be fetched from the API"
                    )

            current_cursor = next_cursor

        update_poller_last_execution_time(
            last_execution_time=end_date.strftime("%Y-%m-%dT%H:%M:%S"),
            tenant_name=self._tenant_name,
            workflow_name=self._workflow_name,
            flow_env_vars=self._config,
        )

        if ATTACHMENT_FAILURES:
            logger.error(f"Failed to download attachments: {ATTACHMENT_FAILURES}")
            raise SomeAttachmentsNotDownloadedException(
                "Some of the attachments were not downloaded"
            )

        return AriesTaskResult(output_param=None, app_metric=self._app_metric)

    def _api_request(
        self,
        method: Callable,
        url: str,
        auth_required: bool = True,
        retry_auth: bool = True,
        **kwargs,
    ) -> requests.Response:
        if auth_required:
            headers = kwargs.get("headers", {})
            headers["Authorization"] = f"Bearer {self._jwt_token}"
            kwargs["headers"] = headers

        response = self._requests_retriable_call(method, url=url, **kwargs)

        # Handle 401 Unauthorized or 403 Forbidden by refreshing token and retrying
        if retry_auth and (
            response.status_code == httpx.codes.UNAUTHORIZED
            or response.status_code == httpx.codes.FORBIDDEN
        ):
            logger.info("[REFRESHING] JWT token due to 401 response")
            self._jwt_token = self._get_jwt_token()

            if auth_required:
                kwargs["headers"]["Authorization"] = f"Bearer {self._jwt_token}"

            response = self._requests_retriable_call(method, url=url, **kwargs)

        return response
