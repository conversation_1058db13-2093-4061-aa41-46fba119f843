from aries_io_event.io_param import IOParamFieldSet
from aries_io_event.task import TaskFieldSet
from aries_io_event.workflow import WorkflowFieldSet
from aries_task_link.models import AriesTaskInput
from datetime import datetime


def sample_input() -> AriesTaskInput:
    workflow = WorkflowFieldSet(
        name="zoom_phone_poll", stack="dev-blue", tenant="pinafore", start_timestamp=datetime.now()
    )
    input_param = IOParamFieldSet(
        params=dict(
            from_date="2023-08-01",  # optional
            to_date="2023-12-14",  # optional
        )
    )
    task = TaskFieldSet(name="test", version="latest", success=False)
    return AriesTaskInput(workflow=workflow, input_param=input_param, task=task)
