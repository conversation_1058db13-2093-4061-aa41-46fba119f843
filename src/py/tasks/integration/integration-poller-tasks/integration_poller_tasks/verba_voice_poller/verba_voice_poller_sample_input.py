from aries_io_event.io_param import IOParamFieldSet
from aries_io_event.task import TaskFieldSet
from aries_io_event.workflow import WorkflowFieldSet
from aries_task_link.models import AriesTaskInput
from datetime import datetime


def sample_input() -> AriesTaskInput:
    workflow = WorkflowFieldSet(
        name="verba_voice_poll",
        stack="dev-blue",
        tenant="suresh",
        start_timestamp=datetime.now(),
    )
    input_param = IOParamFieldSet(
        params=dict(
            from_date="2024-03-21",  # optional
            to_date="2024-03-22",  # optional
            use_mock_sftp=True,  # optional
            force_pull=True,  # optional
            look_back_days=7,
            custom_lake_path="test/",  # optional
            should_event=True,  # optional
        )
    )
    task = TaskFieldSet(name="test", version="latest", success=False)
    return AriesTaskInput(workflow=workflow, input_param=input_param, task=task)
