`kerv-text-poll` is a flow that runs on schedule and gets zip files from SFTP for the t-1 date
and uploads to S3

It also gives us the flexibility to pass `from_date` and `to_date` arguments in case we need to back-fill the data for a time period.

The raw files are downloaded into `s3://$bucket/aries/ingress/streamed/evented/kerv_text_poll/2023/04/06/1106-bec15430-c316-11ed-b1ad-0954178b5e10-447442778398-447484261701-1678873798-ee-mt-.zip` if not downloaded already. This is in sync with the sftp folders.


SFTP File Structure:
```
['/recordings/1146-0-447418999995-1721817199-ee-sms.zip', '/recordings/1146-0-447771610997-1721817214-ee-sms.zip',
  '/recordings/1146-f83f43a2-44e3-11ef-a676-898b941f2fc1-447977433728-447710160177-1721293138-ee-mt-.zip',
  '/recordings/1146-f89a6ef4-451e-11ef-9ff8-898b941f2fc1-441428751233-447816278093-1721318479-ee-mo-.zip', 
  '/recordings/1146-f8f2963c-46c3-11ef-83f7-73ab67c0c49e-447771610997-447799373091-1721499298-ee-mt-.zip', 
  '/recordings/1146-fa1228f6-4e3f-11ef-8aee-c12023b18673-442075234874-447796714500-1722322216-ee-mo-.zip',
  '/recordings/1146-fa56f994-483c-11ef-9e94-c12023b18673-447796714500-447557232053-1721661221-ee-mt-.zip', 
  '/recordings/1146-facb24ba-4bfa-11ef-97e0-898b941f2fc1-447799434488-447977433705-1722072679-ee-mo-.zip',
  '/recordings/1146-fe02be72-4434-11ef-808b-73ab67c0c49e-447977433721-447799566869-1721217986-ee-mt-.zip', 
  '/recordings/1146-ff09d7da-45a9-11ef-981a-898b941f2fc1-443330064555-447977433772-1721378190-ee-mo-.zip']
```


Sample events:

When we need to do a normal run,

```
workflow=WorkflowFieldSet(trace_id='3bKEtJ7pHjEm_IRkVrMlK', start_timestamp=datetime.datetime(2023, 8, 9, 10, 38, 12, 361821), name='kerv_text_poll', stack='dev-blue', tenant='pinafore') task=TaskFieldSet(id='2rvSyyTZvfrCKr_wt_3PG', name='test', version='latest', timestamp=datetime.datetime(2023, 8, 9, 5, 8, 12, 362494), success=False, previous_id=None) input_param=IOParamFieldSet(params={})
```

When we need to back-fill, we use `from_date` and `to_date`

```
workflow=WorkflowFieldSet(trace_id='3bKEtJ7pHjEm_IRkVrMlK', start_timestamp=datetime.datetime(2023, 8, 9, 10, 38, 12, 361821), name='kerv_text_poll', stack='dev-blue', tenant='pinafore') task=TaskFieldSet(id='2rvSyyTZvfrCKr_wt_3PG', name='test', version='latest', timestamp=datetime.datetime(2023, 8, 9, 5, 8, 12, 362494), success=False, previous_id=None) input_param=IOParamFieldSet(params={'from_date': '2023-03-15', 'to_date': '2023-03-25'})
```

When we need to use mock_sftp, use `use_mock_sftp=True`:

```
workflow=WorkflowFieldSet(trace_id='3bKEtJ7pHjEm_IRkVrMlK', start_timestamp=datetime.datetime(2023, 8, 9, 10, 38, 12, 361821), name='kerv_text_poll', stack='dev-blue', tenant='pinafore') task=TaskFieldSet(id='2rvSyyTZvfrCKr_wt_3PG', name='test', version='latest', timestamp=datetime.datetime(2023, 8, 9, 5, 8, 12, 362494), success=False, previous_id=None) input_param=IOParamFieldSet(params={'use_mock_sftp': True})
```
This is mostly done to test using mock sftp in uat env.

In dev/local, we have the check for stack, so it automatically assumes sftp to be the mock one.

PROXY:

The proxy_host and proxy_port is set up as environment variables. If not passed, it is assumed to be None.

FORCE PULL:

- if force_pull is set: (force_pull is a boolean flag)
    - sync the files from sftp even if they were already downloaded and produce ingress
  - if force_pull is not set: 
    - download to s3 only if they were not downloaded.


DEFAULT LOOK-BACK: input_param=IOParamFieldSet(params={'look_back_days': 5})

- look_back_days should be positive integer and should be >=1 and <= 7

Poller will fail fast if the validation fails.
If look_back_days is set to 1 day

Poller will process only the files that fall between today and today - 1 dates and skip the others.
If look_back_days is not set

Poller will process only the files that fall between today and today - 7 dates and skip the others.

Default IO-Params:
1. `force_pull`: `False`
2. `custom_path`: `None`
3. `should_event`: `False`
4. `look_back_days`: `7`

App metrics are added when there is a failure event.
* app_metric.metrics["generic"]["errored_count"] += 1