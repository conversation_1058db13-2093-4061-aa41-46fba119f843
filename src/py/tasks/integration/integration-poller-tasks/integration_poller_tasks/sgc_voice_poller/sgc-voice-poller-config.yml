data_platform_config_api_url: ${oc.env:DATA_PLATFORM_CONFIG_API_URL,localhost}
stack: ${oc.env:STACK}
kafka_rest_proxy_url: ${oc.env:KAFKA_REST_PROXY_URL,test-url}
version: ${oc.env:SE_VERSION,"latest"}
vault:
  url: ${oc.env:VAULT_URL}
  mount_point: ${oc.env:VAULT_MOUNT_POINT}
  auth_method: ${oc.env:VAULT_AUTH_METHOD}
  token: ${oc.env:VAULT_TOKEN}
  k8s_role: ${oc.env:VAULT_K8S_ROLE}
  k8s_jwt_path: ${oc.env:VAULT_K8S_JWT_PATH}
  k8s_auth_mount_point: ${oc.env:VAULT_K8S_AUTH_MOUNT_POINT}

