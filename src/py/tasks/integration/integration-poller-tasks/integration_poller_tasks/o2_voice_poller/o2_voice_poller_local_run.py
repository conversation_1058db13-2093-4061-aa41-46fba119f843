import logging
from benchmark_mode import benchmark
from integration_poller_tasks.main import integration_poller_tasks_run
from integration_poller_tasks.o2_voice_poller.o2_voice_poller_sample_input import sample_input

logger = logging.getLogger("o2_voice_poller")


@benchmark
def main():
    logger.info("Starting execution...")
    output = integration_poller_tasks_run(aries_task_input=sample_input())
    logger.info(f"Finished executing with output {output}")


if __name__ == "__main__":
    main()
