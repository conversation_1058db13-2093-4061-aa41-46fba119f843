import logging
from aries_task_link.models import AriesTaskInput, AriesTaskResult
from integration_poller_tasks.numonix_ix_cloud_voice_poller.numonix_ix_cloud_voice_poll import (
    NumonixIXCloudVoicePoll,
)
from integration_poller_tasks.numonix_ix_cloud_voice_poller.static import Static
from omegaconf import OmegaConf
from pathlib import Path

logger = logging.getLogger(Static.POLLER_NAME)


def numonix_ix_cloud_voice_poll_run(aries_task_input: AriesTaskInput) -> AriesTaskResult:
    config = OmegaConf.load(
        Path(__file__).parent.joinpath("numonix-ix-cloud-voice-poller-config.yml")
    )
    poller = NumonixIXCloudVoicePoll(aries_task_input=aries_task_input, config=config)
    return poller.run_poller()
