from aries_io_event.io_param import IOParamFieldSet
from aries_io_event.task import TaskFieldSet
from aries_io_event.workflow import WorkflowFieldSet
from aries_task_link.models import AriesTaskInput
from datetime import datetime


def sample_input() -> AriesTaskInput:
    workflow = WorkflowFieldSet(
        name="bloomberg_poll",
        stack="dev-shared-2",
        tenant="irises8",
        start_timestamp=datetime.now(),
    )
    input_param = IOParamFieldSet(
        params=dict(
            bbg_username="test_username",
            # custom_lake_path="test/",  # optional
            # should_event=True,  # optional
            look_back_days=7,  # optional and max value allowed is 30
            # force_pull={
            #     "2024-06-19": [".ib19"],
            #     "2024-06-18": [".ib19"],
            # },  # optional
            # fetch_from_object_store=True,  # optional
            # ignore_mdc_error=False, # optional
        )
    )
    task = TaskFieldSet(name="test", version="latest", success=False)
    return AriesTaskInput(workflow=workflow, input_param=input_param, task=task)
