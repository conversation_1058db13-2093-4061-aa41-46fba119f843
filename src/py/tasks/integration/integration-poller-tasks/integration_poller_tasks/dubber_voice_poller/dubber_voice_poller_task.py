import logging
from aries_task_link.models import AriesTaskInput, AriesTaskResult
from integration_poller_tasks.dubber_voice_poller.dubber_voice_poll import DubberVoicePoll
from integration_poller_tasks.dubber_voice_poller.static import Static
from omegaconf import OmegaConf
from pathlib import Path

logger = logging.getLogger(Static.POLLER_NAME)


def dubber_voice_poll_run(aries_task_input: AriesTaskInput) -> AriesTaskResult:
    config = OmegaConf.load(Path(__file__).parent.joinpath("dubber-voice-poller-config.yml"))
    poller = DubberVoicePoll(aries_task_input=aries_task_input, config=config)
    return poller.run_poller()
