"""The module contain S<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> class."""

import backoff
import glob
import json
import logging
import nanoid
import ndjson
import os
import shutil
import time
import uuid
from aries_config_api_compatible_client.tenant_workflow import CompatibleTenantWorkflowAPIClient
from aries_io_event.app_metric import AppMetricFieldSet
from aries_se_api_client.client import AriesApiClient
from aries_task_link.models import AriesTaskInput, AriesTaskResult
from aries_utils.kafka_rest import KafkaRestClient
from azure.core.exceptions import AzureError
from botocore.exceptions import ClientError
from collections import defaultdict
from concurrent.futures import ThreadPoolExecutor, as_completed
from data_platform_config_api_client.slack_chat import SlackChatAPI
from data_platform_config_api_client.tenant_workflow import Tenant<PERSON>ork<PERSON>API
from datetime import date, datetime, timedelta, timezone
from dateutil import parser
from httpx import RequestError
from integration_poller_tasks.slack_chat_poller.static import (
    CONVERSATIONS_EDITS,
    CONVERSATIONS_HISTORY,
    CONVERSATIONS_INFO,
    CONVERSATIONS_MEMBERS,
    DEFAULT_LOOKBACK_DAYS,
    RECENT_CONVERSATIONS,
    SLACK_CHAT_SECRET_PATH,
    SLACK_CHAT_TENANT_SECRET_PATH,
    SLACK_CHAT_TRANSFORM_WORKFLOW_NAME,
    FileAttributes,
    QueryParams,
    SlackSourceColumns,
)
from pydantic import BaseModel, Field, validator
from se_comms_ingress_utils.common_util import (
    ingestion_trigger_event,
    update_poller_last_execution_time,
)
from se_comms_slack_api_utils.client import SlackApiClient
from se_data_lake.lake_path import get_non_streamed_poller_file_path
from se_fsspec_utils.file_system import get_filesystem
from se_redis_utils.redis_utils import get_redis_client
from se_secrets_client.utils import secrets_client
from threading import Lock
from typing import Any, Dict, List, Union


class SlackChatPollParams(BaseModel):
    should_event: bool = Field(default=False, description="should event")
    custom_lake_path: str | None = Field(default=None, description="custom path")
    from_date: date | None = Field(default=None, description="from date timestamp")
    to_date: date | None = Field(default=None, description="to date timestamp")
    ignore_private_channels: bool = Field(default=False, description="ignore private channels")
    use_redis: bool = Field(default=False, description="flag to use redis client")

    @validator("from_date", "to_date", pre=True, always=True)
    def parse_dates(cls, value):
        if value is not None and not isinstance(value, datetime):
            return parser.parse(value)
        return value


class SlackChatPoll:
    """class SlackChatPoll polls for chat data."""

    def __init__(self, aries_task_input: AriesTaskInput, config):
        """__init__ initializes the class."""
        self._batch_count = 0
        self._lock = Lock()
        self._aries_task_input = aries_task_input
        self._params = SlackChatPollParams.validate(self._aries_task_input.input_param.params)
        self._config = config
        self._max_workers = int(
            aries_task_input.input_param.params.get(
                "max_worker_pool", int(self._config.max_worker_pool)
            )
        )
        self._page_size = int(self._config.page_size)
        self._max_batch_size = int(self._config.max_batch_size)
        # get the secrets from the vault
        self._secrets_client = secrets_client(self._config.vault, self._config)

        self._app_metric = AppMetricFieldSet(metrics={"generic": {"errored_count": 0}})
        self._failures = defaultdict(list)  # type: ignore

        # get tenant workflow configuration for destination workflow
        self._config_api_client = AriesApiClient(host=config.data_platform_config_api_url)
        self._tenant_workflow_api = TenantWorkflowAPI(self._config_api_client)
        self._transform_workflow_tw_config = CompatibleTenantWorkflowAPIClient.get(
            tenant_workflow_api=self._tenant_workflow_api,
            tenant_name=self._aries_task_input.workflow.tenant,
            workflow_name=SLACK_CHAT_TRANSFORM_WORKFLOW_NAME,
        )

        # get tenant workflow configuration for destination workflow
        self._poller_tenant_workflow_config = CompatibleTenantWorkflowAPIClient.get(
            tenant_workflow_api=self._tenant_workflow_api,
            tenant_name=self._aries_task_input.workflow.tenant,
            workflow_name=self._aries_task_input.workflow.name,
        )

        # get filesystem
        self._destination_fs = get_filesystem(
            cloud=self._transform_workflow_tw_config.tenant.cloud,
            lake_prefix=self._transform_workflow_tw_config.tenant.lake_prefix,
        )
        self._lake_prefix = self._transform_workflow_tw_config.tenant.lake_prefix.rstrip("/")
        self._batch_size = (
            self._transform_workflow_tw_config.max_batch_size
            if self._transform_workflow_tw_config.max_batch_size
            else self._max_batch_size
        )

        # kafka client
        self._kafka_rest_proxy_client = KafkaRestClient(
            kafka_rest_proxy_url=self._config.kafka_rest_proxy_url,
        )

        self._local_dir = str(uuid.uuid4())

        self._slack_api_client = SlackApiClient(
            tenant=self._aries_task_input.workflow.tenant,
            secrets_client=self._secrets_client,
            app_secret_path=SLACK_CHAT_SECRET_PATH,
            tenant_secret_path=SLACK_CHAT_TENANT_SECRET_PATH,
            timeout=int(self._config.slack_api_timeout_s),
        )

        self._slack_chat_api = SlackChatAPI(
            AriesApiClient(host=self._config.data_platform_config_api_url)
        )

        self._backfill = False
        # set the from and to timestamp based on backfill/ normal run
        if not self._params.from_date and not self._params.to_date:
            # regular poller run
            last_polled_db = self._poller_tenant_workflow_config.workflow_last_executed
            if not last_polled_db:
                self._poll_from_timestamp = datetime.timestamp(
                    datetime.utcnow() - timedelta(days=float(DEFAULT_LOOKBACK_DAYS))
                )
            else:
                self._poll_from_timestamp = datetime.fromisoformat(last_polled_db).timestamp()

            self._poll_to_timestamp = datetime.utcnow().timestamp()
        else:
            self._poll_from_timestamp = (
                datetime.combine(self._params.from_date, datetime.min.time())  # type: ignore
                .replace(tzinfo=timezone.utc)
                .timestamp()
            )
            self._poll_to_timestamp = (
                datetime.combine(self._params.to_date, datetime.min.time())  # type: ignore
                .replace(tzinfo=timezone.utc)
                .timestamp()
            )
            self._backfill = True

        # set use redis flag only if use redis is True and normal run
        self._use_redis = self._params.use_redis and not self._backfill
        self._redis_client = (
            get_redis_client(
                host=self._config.redis.host,
                port=self._config.redis.port,
                ssl=self._config.redis.ssl,
                password=self._config.redis.password,
            )
            if self._use_redis
            else None
        )

    def _set_up_local_dirs(self) -> None:
        """_set_up_local_dirs creates local dir."""
        self._clean_up_local_dirs()
        if not os.path.exists(self._local_dir):
            os.makedirs(self._local_dir)
            os.makedirs(os.path.join(self._local_dir, "channels"))
            os.makedirs(os.path.join(self._local_dir, "batch_message_files"))
            os.makedirs(os.path.join(self._local_dir, "edits_messages"))

    def _clean_up_local_dirs(self) -> None:
        """_clean_up_local_dirs clears the local dir."""
        if os.path.exists(self._local_dir):
            shutil.rmtree(self._local_dir)

    def _read_local_file(self, file_path: str) -> Dict[str, Any]:
        """_read_local_file reads the local json file and returns data.

        :param file_path: file path
        :type file_path: str
        :return: dictionary object
        :rtype: Dict[str, Any]
        """
        result_dict = {}
        with open(os.path.join(self._local_dir, file_path), "r") as fp:
            # read local file
            result_dict = json.load(fp)

        return result_dict

    def _initialize_batch(self, channel_id: str) -> Dict[str, Any]:
        """_initialize_batch initializes batch.

        :param channel_id: channel_id
        :type channel_id: str
        :return: batch dictionary object
        :rtype: Dict[str,Any]
        """
        batch_dict = {}
        batch_dict = {
            "messages": [],
            "members": self._read_local_file(
                file_path=os.path.join("channels", f"{channel_id}___members.json")
            ),
            "info": self._read_local_file(
                file_path=os.path.join("channels", f"{channel_id}___channel_info.json")
            ),
            "edits": [],
        }
        return batch_dict

    def _trigger_event(self, batch_file_path: str) -> None:
        """_trigger_event triggers ingestion event.

        :param batch_file_path: path to batch file
        :type batch_file_path: str
        """
        # for each batch data produce new system event with new trace id
        # Producing ingestion trigger event
        event = ingestion_trigger_event(
            target_path=batch_file_path,
            workflow_name=SLACK_CHAT_TRANSFORM_WORKFLOW_NAME,
            tenant_name=self._aries_task_input.workflow.tenant,
            stack_name=self._config.stack,
            task_name=self._aries_task_input.workflow.name,
            streamed=False,
            version=self._config.version,
            aries_task_to_domain_dict=dict(),
        )
        logging.info(f"[CREATED] Ingestion Trigger Event: {event}")
        self._kafka_rest_proxy_client.send(
            io_event=event,
            topic=self._transform_workflow_tw_config.io_topic,
            raise_on_connection_error=True,
            raise_on_serder_error=True,
        )

    @backoff.on_exception(wait_gen=backoff.expo, exception=RequestError, max_tries=3)
    def _transfer_streamed_data(self, url: str, destination_path: str) -> None:
        """_transfer_streamed_data streams and stores the data.

        :param url: download url
        :type url: str
        :param destination_path: destination path
        :type destination_path: str
        """
        with self._destination_fs.open(destination_path, mode="wb") as f:
            for data in self._slack_api_client.get_streamed_data(url=url):
                f.write(data)

    def _transfer_channel_attachments(
        self, message: Dict[str, Any], destination: str
    ) -> List[Dict[Any, Any]]:
        """_transfer_channel_attachments downloads/uploads attachments.

        :param message: list of channel messages
        :type message: Dict[str,Any]
        :param destination: destination directory for messages
        :type destination: str
        :return: list of uploaded files
        :rtype: List[Dict,Any]
        """
        files = []
        download_url = ""
        # write attachments by iterating over messages_list
        # iterate over files list
        try:
            for file in message.get("files", []):
                download_url = file.get("url_private_download")
                if download_url:
                    logging.info(f"Downloading file from url {download_url}")
                    file_id = file.get("id")
                    file_name = file.get("name")
                    final_file_name = f"{file_id}_{file_name}"
                    destination_path = f"{destination}/{final_file_name}"
                    self._transfer_streamed_data(
                        url=download_url, destination_path=destination_path
                    )
                    logging.info(
                        f"File from url {download_url} downloaded at location {destination_path}"
                    )

                    files.append(
                        {
                            "id": file_id,
                            "name": file_name,
                            "title": file.get(FileAttributes.TITLE),
                            "mimetype": file.get(FileAttributes.MIMETYPE),
                            "user": file.get(FileAttributes.USER),
                            "s3_path": destination_path,
                            "filetype": file.get(FileAttributes.FILETYPE),
                            "pretty_type": file.get(FileAttributes.PRETTY_TYPE),
                        }
                    )
                else:
                    logging.info(
                        f"No attachment download url found for file id: `{file.get('id')}`"
                    )
        except (ClientError, AzureError) as exc:
            raise exc
        except Exception as exc:
            error_message = f"Failed to download attachment from url {download_url} - {exc}"
            self._failures["attachments"].append(error_message)
            logging.exception(error_message)

        return files

    @backoff.on_exception(wait_gen=backoff.expo, exception=RequestError, max_tries=3)
    def _upload_chat_log(self, destination_dir: str, batch: Dict[str, Dict[str, Any]]) -> str:
        """_upload_chat_log uploads the chat logs.

        :param destination_dir: destination directory path
        :type destination_dir: str
        :param batch: dictionary of channels information
        :type batch: Dict[str, Dict[str, Any]]
        """
        # write metadata
        # <timestamp_ns>_batch_<count>.ndjson
        destination_file = f"{destination_dir}/{time.time_ns()}_batch_{self._batch_count}.ndjson"
        with self._destination_fs.open(destination_file, mode="w") as f:
            for channel_id in batch:
                channel_log = batch[channel_id]
                logging.info(
                    f" Channel info is_private: {channel_log['info'].get('is_private')},"
                    f" is_dm: {channel_log['info'].get('is_dm')}, "
                    f"is_mpim: {channel_log['info'].get('is_mpim')}"
                )
                # Download attachments and modify message key
                uploaded_files = []
                # download the attachment and update the key
                for index, message in enumerate(channel_log.get("messages", [])):
                    uploaded_files = self._transfer_channel_attachments(
                        message=message, destination=f"{destination_dir}/{channel_id}"
                    )
                    # if files were uploaded
                    if uploaded_files:
                        channel_log["messages"][index]["file_uploads"] = uploaded_files
                # write to s3 bucket
                writer = ndjson.writer(f)
                writer.writerow(channel_log)
                logging.info(
                    f"Stored metadata for channel_id: {channel_id} at location:{destination_file}"
                )
        return destination_file

    def _dump_batch_and_event(self, batch_data: Dict[str, Dict[str, Any]], file_path: str) -> None:
        """_dump_batch_and_event dumps the batch and triggers event.

        :param batch_data: batch data
        :type batch_data: List[str]
        """
        self._batch_count += 1
        try:
            uploaded_file_path = self._upload_chat_log(destination_dir=file_path, batch=batch_data)

            if self._params.custom_lake_path and not self._params.should_event:
                logging.info(
                    "Skipping ingress io event as custom_lake_path"
                    " is set and should_event is not set"
                )
                return
        except (ClientError, AzureError) as exc:
            raise exc
        except Exception as e:
            error_message = f"Failed to write batch no {self._batch_count} at {file_path} - {e}"
            self._failures["batch"].append(error_message)
            logging.exception(error_message)

        logging.info("sending ingress IO event")
        if uploaded_file_path:
            self._trigger_event(uploaded_file_path)

    def _read_all_edits(
        self, edits: List[str], message_id: str, channel_id: str
    ) -> List[Dict[str, Any]]:
        """_read_all_edits reads all the edits from the disk.

        :param edits: list of editids__subtype
        :type edits: List[str]
        :param message_id: message id
        :type message_id: str
        :param channel_id: channel id
        :type channel_id: str
        :return: edits list
        :rtype: List[Dict[str,Any]]
        """
        edits_list = []
        for edit in edits:
            edit_id, subtype = edit.split("___")
            edits_list.append(
                self._read_local_file(
                    file_path=os.path.join(
                        "edits_messages",
                        f"{message_id}___{edit_id}___{channel_id}___{subtype}.json",
                    )
                )
            )
        return edits_list

    def _build_batches(self, group_edits_dict: Dict[str, Dict[str, List[str]]]) -> None:
        """_build_batches builds batches from local files and dump to s3.

        :param group_edits_dict: dictionary{"message_id":["edit1", "edit2"]}
        :type group_edits_dict: Dict[str, List[str]]
        """
        target_ingress_file_path = get_non_streamed_poller_file_path(
            workflow_name=self._aries_task_input.workflow.name,
            workflow_trace_id=self._aries_task_input.workflow.trace_id,
            workflow_start_timestamp=self._folder_date,
            lake_prefix=self._lake_prefix,
            custom_path=self._params.custom_lake_path,
        )
        batch = {}
        batch_counter = 0
        processed_edits = {}
        unprocessed_edits_ndjson_batch = {}
        unprocessed_batch_counter = 0
        unprocessed_edits = []  # type: ignore
        # iterate over the sorted batched messages files grouped together based on channel id
        batched_message_files_paths = sorted(
            glob.glob(os.path.join(self._local_dir, "batch_message_files", "*.ndjson"))
        )
        for index in range(len(batched_message_files_paths)):
            # for path in batched_message_files_paths:
            path = batched_message_files_paths[index]
            with open(path, "r") as rp:
                # eg download/batch_message_files/channel_id1___page1___random_id.ndjson
                channel_id = os.path.split(path)[1].split("___")[0]
                # initialize the batch for channel id
                # if channel_id not in batch:
                #     # initialize batch
                #     batch[channel_id] = self._initialize_batch(channel_id=channel_id)
                # Read each line of the file and parse it as JSON
                # each line corresponds to a message
                lines = ndjson.reader(rp)
                for message in lines:
                    # message = ndjson.loads(line)
                    if channel_id not in batch:
                        # initialize batch
                        batch[channel_id] = self._initialize_batch(channel_id=channel_id)

                    batch[channel_id]["messages"].append(message)
                    batch_counter += 1
                    # find the edits
                    message_id = message.get(SlackSourceColumns.TS)
                    # read edits if present
                    # eg: {'channelId': {'message_id': [edits1____subtype, edit2____subtype]}}
                    edits = group_edits_dict.get(channel_id, {}).get(message_id)
                    if edits:
                        batch[channel_id]["edits"].extend(
                            self._read_all_edits(
                                edits=edits, message_id=message_id, channel_id=channel_id
                            )
                        )
                        batch_counter += len(edits)
                        for edit in edits:
                            processed_edits[edit] = 1

                    if batch_counter >= self._max_batch_size:
                        self._dump_batch_and_event(
                            file_path=target_ingress_file_path, batch_data=batch
                        )
                        # reset the batch and its counter
                        batch_counter, batch = 0, {}

            # if we have reached reading last element of the list
            if index + 1 == len(batched_message_files_paths):
                next_channel = None
            else:
                # get the next channel from the file path list
                next_channel = os.path.split(batched_message_files_paths[index + 1])[1].split(
                    "___"
                )[0]
            # if it is last file or its the last channel page within the list
            if not next_channel or (next_channel != channel_id):
                # build the unprocessed edits
                unprocessed_edits = []
                # the list would contain
                # [(message_id, edit_id, channel_id),(),...]
                for message_id in group_edits_dict.get(channel_id, {}):
                    for edit in group_edits_dict[channel_id][message_id]:
                        # check the id in processed dictionary
                        if edit not in processed_edits:
                            unprocessed_edits.append((message_id, edit, channel_id))

                # process only if unprocessed edits per channel
                if unprocessed_edits:
                    # build the unprocessed edits batch
                    for messages_id, edit, channel in unprocessed_edits:
                        # initialize the unprocessed edits ndjson batch
                        if channel_id not in unprocessed_edits_ndjson_batch:
                            unprocessed_edits_ndjson_batch[channel_id] = self._initialize_batch(
                                channel_id=channel_id
                            )
                        # add the edits
                        edit_id, subtype = edit.split("___")
                        unprocessed_edits_ndjson_batch[channel_id]["edits"].append(
                            self._read_local_file(
                                file_path=os.path.join(
                                    "edits_messages",
                                    f"{messages_id}___{edit_id}___{channel}___{subtype}.json",
                                )
                            )
                        )
                        unprocessed_batch_counter += 1

                        if unprocessed_batch_counter >= self._max_batch_size:
                            self._dump_batch_and_event(
                                file_path=target_ingress_file_path,
                                batch_data=unprocessed_edits_ndjson_batch,
                            )
                            unprocessed_batch_counter, unprocessed_edits_ndjson_batch = 0, {}

        # if unstored batch is available
        if batch:
            # store the batches to s3
            self._dump_batch_and_event(file_path=target_ingress_file_path, batch_data=batch)
            batch, batch_counter = {}, 0
        # if unstored batch is available
        if unprocessed_edits_ndjson_batch:
            # store the batches to s3
            self._dump_batch_and_event(
                file_path=target_ingress_file_path, batch_data=unprocessed_edits_ndjson_batch
            )
            unprocessed_edits_ndjson_batch, unprocessed_batch_counter = {}, 0

    def _write_local_file(
        self,
        file_path: str,
        data: Union[Dict[str, Any], List[Dict[str, Any]], List[str]],
        format: str,
    ) -> None:
        """_write_local_file writes the files locally.

        :param file_path: absolute path of the files
        :type file_path: str
        :param data: data to be written on disk
        :type data: Union[Dict[str, Any], List[Dict[str, Any]], List[str]]
        """
        with open(os.path.join(self._local_dir, file_path), "w") as fp:
            if format == "json":
                # write using dump and read using json.load
                json.dump(data, fp)
            elif format == "ndjson":
                # create ndjson
                writer = ndjson.writer(fp)
                for line in data:
                    writer.writerow(line)

    def _get_channel_members(self, channel_id: str, team_id: str) -> None:
        """_get_channel_members retrieves the channel members.

        :param channel_id: channel id
        :type channel_id: str
        :param team_id: team id
        :type team_id: str
        :return: None
        :rtype: None
        """
        members_list = []
        try:
            logging.info(f"channel_id:{channel_id} team_id: {team_id}")
            # get paginated response and create members list, no latest
            # param available, offset to paginate
            params = {
                QueryParams.LIMIT: self._page_size,
                QueryParams.CHANNEL: channel_id,
                QueryParams.TEAM: team_id,
            }
            for response in self._slack_api_client.get_paginated_response(
                api_url=CONVERSATIONS_MEMBERS,
                params=params,
                response_key="members",
                pagination_key="offset",
            ):
                if response.get("members", []):
                    members_list.extend([member["id"] for member in response["members"]])
            # channels/channel_id1___members.json
            self._write_local_file(
                file_path=os.path.join("channels", f"{channel_id}___members.json"),
                data=members_list,
                format="json",
            )
            logging.info(
                f"Retrieved {len(members_list)} channel members "
                f"for channel_id:{channel_id} team_id: {team_id}"
            )
        except (ClientError, AzureError) as exc:
            raise exc
        except Exception as exc:
            error_message = (
                f"Failed to retrieve channel members for "
                f"channel_id {channel_id} team_id {team_id} - {exc}"
            )
            logging.exception(error_message)
            with self._lock:
                self._failures["conversations_members"].append(error_message)

    def _get_chat_history(self, channel_id: str, team_id: str) -> bool:
        """_get_chat_history retrieves the chat history.

        :param channel_id: channel id
        :type channel_id: str
        :param team_id: team id
        :type team_id: str
        :return: has edits flag
        :rtype: bool
        """
        has_edits = False
        page_count = 0
        messages_count = 0
        try:
            logging.info(f"Fetching chat history for channel_id:{channel_id}")
            # get paginated response and create participant list
            params = {
                QueryParams.LIMIT: self._page_size,
                QueryParams.CHANNEL: channel_id,
                QueryParams.REACTIONS: 1,
                QueryParams.OLDEST: self._poll_from_timestamp,
                QueryParams.LATEST: self._poll_to_timestamp,
                QueryParams.TEAM: team_id,
            }
            for response in self._slack_api_client.get_paginated_response(
                api_url=CONVERSATIONS_HISTORY,
                params=params,
                response_key="messages",
                pagination_key="latest",
            ):
                # create message list
                if response.get("messages", []):
                    messages_count += len(response["messages"])
                    has_edits = response.get("has_edits", False)
                    page_count += 1
                    # batch_message_files/channel_id1___page1___random_id.ndjson
                    self._write_local_file(
                        file_path=os.path.join(
                            "batch_message_files",
                            f"{channel_id}___{page_count}___{nanoid.generate(size=5)}.ndjson",
                        ),
                        data=response["messages"],
                        format="ndjson",
                    )
            logging.info(f"Retrieved {messages_count} chat history for channel_id:{channel_id}")
        except (ClientError, AzureError) as exc:
            raise exc
        except Exception as exc:
            error_message = f"Failed to retrieve chat history for channel_id {channel_id} - {exc}"
            logging.exception(error_message)
            with self._lock:
                self._failures["conversations_history"].append(error_message)
        finally:
            return has_edits

    def _get_chat_edits(self, channel_id: str, team_id: str) -> Dict[str, List[str]]:
        """_get_chat_edits retrieves the chat edits history.

        :param channel_id: channel id
        :type channel_id: str
        :param team_id: team id
        :type team_id: str
        :return: group_edits_dict
        :rtype: Dict[str,List[str]]
        """
        group_edits_dict = {}  # type: ignore
        edit_messages_count = 0
        try:
            logging.info(f"Fetching chat edits for channel_id:{channel_id}")
            # get paginated response and create participant list
            params = {
                QueryParams.LIMIT: self._page_size,
                QueryParams.CHANNEL: channel_id,
                QueryParams.OLDEST: self._poll_from_timestamp,
                QueryParams.LATEST: self._poll_to_timestamp,
                QueryParams.TEAM: team_id,
            }
            for response in self._slack_api_client.get_paginated_response(
                api_url=CONVERSATIONS_EDITS,
                params=params,
                response_key="edits",
                pagination_key="latest",
            ):
                if response.get("edits", []):
                    for edit_message in response["edits"]:
                        edit_messages_count += 1
                        # building group_edits_dict
                        # {original_message_id: [edit_message_id1___subtype
                        # ,…,edit_message_idn___subtype],…}
                        if edit_message[SlackSourceColumns.ORIGINAL_TS] in group_edits_dict:
                            group_edits_dict[edit_message[SlackSourceColumns.ORIGINAL_TS]].append(
                                f"{edit_message[SlackSourceColumns.TS]}___{edit_message[SlackSourceColumns.SUBTYPE]}"
                            )
                        else:
                            group_edits_dict[edit_message[SlackSourceColumns.ORIGINAL_TS]] = [
                                f"{edit_message[SlackSourceColumns.TS]}___{edit_message[SlackSourceColumns.SUBTYPE]}"
                            ]
                        # edits_messages/message_id___edit_id___channel_id.json
                        self._write_local_file(
                            file_path=os.path.join(
                                "edits_messages",
                                f"{edit_message[SlackSourceColumns.ORIGINAL_TS]}___{edit_message[SlackSourceColumns.TS]}___{channel_id}___{edit_message[SlackSourceColumns.SUBTYPE]}.json",
                            ),
                            data=edit_message,
                            format="json",
                        )
            logging.info(f"Retrieved {edit_messages_count} chat edits for channel_id:{channel_id}")
        except (ClientError, AzureError) as exc:
            raise exc
        except Exception as exc:
            error_message = f"Failed to retrieve chat edits for channel_id {channel_id} - {exc}"
            logging.exception(error_message)
            with self._lock:
                self._failures["conversations_edits"].append(error_message)

        finally:
            return group_edits_dict

    def _write_channel_data_local_disk(
        self, channel_id: str, team_id: str, channel_info: Dict[str, Any]
    ) -> Dict[str, Dict[str, List[str]]]:
        """_write_channel_data_local_disk writes data locally.

        :param channel_id: channel_id
        :type channel_id: str
        :param team_id: team_id
        :type team_id: str
        :param channel_info: channel information dictionary
        :type channel_info: Dict[str, Any]
        :return: chats edit dictionary for all channels
        :rtype: Dict[str, Dict[str,List[str]]]
        """
        chat_edits_dict = {}
        # store the channel info locally
        # channels/channel_id1___channel_info.json
        self._write_local_file(
            file_path=os.path.join("channels", f"{channel_id}___channel_info.json"),
            data=channel_info,
            format="json",
        )

        self._get_channel_members(channel_id=channel_id, team_id=team_id)
        # gets the messages list
        # get the chat edits if flag is set to True
        if self._get_chat_history(channel_id=channel_id, team_id=team_id):
            # build the group_edits_dict for all channels
            # {"channel_1":{original_message_id: [edit_message_id1,…,edit_message_idn],…}}
            chat_edits_dict[channel_id] = self._get_chat_edits(
                channel_id=channel_id, team_id=team_id
            )

        return chat_edits_dict

    def _download_channel_data(self, channel: Dict[str, str]) -> Dict[str, Dict[str, List[str]]]:
        """_download_channel_data uses multithreads to download channel data.

        :param channel: channel dictionary
        :type channel: Dict[str,str]
        :return: chats edit dictionary
        :rtype: Dict[str,Dict[str,List[str]]]
        """
        result = {}
        channel_metadata = self._slack_api_client.get_endpoint_response_data(
            api_end_point=CONVERSATIONS_INFO,
            params={QueryParams.CHANNEL: channel["id"], QueryParams.TEAM: channel["team"]},
        )
        #  Check for the public / private channels and store the public channel data
        # skip the private conversation downloads in case of debug mode
        # reponse {"ok": True, "info": []}
        channel_info = (
            channel_metadata.get("info", [])[0] if channel_metadata.get("info", []) else {}
        )
        if channel_info and not (
            self._params.ignore_private_channels
            and (
                channel_info.get("is_private")
                or channel_info.get("is_dm")
                or channel_info.get("is_mpim")
            )
        ):
            # {original_message_id: [edit_message_id1,…,edit_message_idn],…}
            result = self._write_channel_data_local_disk(
                channel_id=channel["id"], team_id=channel["team"], channel_info=channel_info
            )

        return result

    def _get_channels_data(self) -> dict:
        all_channels = []
        group_edits_dict = {}
        params = {
            QueryParams.LIMIT: self._page_size,
            QueryParams.LATEST: self._poll_from_timestamp,
        }
        # get the channels array through pagination
        for response in self._slack_api_client.get_paginated_response(
            api_url=RECENT_CONVERSATIONS,
            params=params,
            response_key="channels",
            pagination_key="latest",
        ):
            # check if response contains channels
            channels = response.get("channels", [])
            all_channels.extend(channels)

        logging.info(f"No of discovered channels : {len(all_channels)}")
        if all_channels:
            futures = []

            with ThreadPoolExecutor(max_workers=self._max_workers) as executor:
                # iterate over the channel list
                futures = [
                    executor.submit(self._download_channel_data, channel)
                    for channel in all_channels
                ]
                # timeout of 4 hours for the iterator
                for future in as_completed(futures, timeout=21600):
                    result = future.result()
                    group_edits_dict.update(result if result else {})

        return group_edits_dict

    def _get_existing_workflow_execution_id(self, key: str) -> str | None:
        """_get_existing_workflow_execution_id checks cache for workflow
        execution.

        :return: cache key
        :rtype: str | None
        """
        existing_workflow_execution_id = None
        if self._use_redis:
            existing_workflow_execution_id = self._redis_client.get(name=key)  # type: ignore
            logging.info(
                f"Existing workflow exceution id in redis : {existing_workflow_execution_id}"
            )
        return existing_workflow_execution_id  # type: ignore

    def run_poller(self) -> AriesTaskResult:
        """run_poller executes the poller steps.

        :return: task result
        :rtype: AriesTaskResult
        """
        try:
            logging.info(
                f"poll_from_timestamp {self._poll_from_timestamp}, "
                f"poll_to_timestamp: {self._poll_to_timestamp}"
            )
            # check if existing workflow is running for a tenant
            workflow_execution_key = (
                f"{self._config.stack}__{self._aries_task_input.workflow.tenant}"
                f"__{self._aries_task_input.workflow.name}__concurrency_lock"
            )
            existing_workflow_execution_id = self._get_existing_workflow_execution_id(
                workflow_execution_key
            )
            if existing_workflow_execution_id:
                logging.warn(
                    f"Poller is already running for the tenant"
                    f" with execution id: {existing_workflow_execution_id}."
                )
                return AriesTaskResult(output_param=None, app_metric=self._app_metric)

            # set the redis key with ttl
            if self._use_redis:
                self._redis_client.set(  # type: ignore
                    name=workflow_execution_key,
                    value=self._aries_task_input.input_param.params["orchestrator_metadata"][
                        "workflow_execution_id"
                    ],
                    ex=int(self._config.redis.concurrency_lock_ttl),
                )
            # folder date is used to create folder dates
            self._folder_date = datetime.now()

            self._set_up_local_dirs()

            group_edits_dict = self._get_channels_data()
            # build the batches from local files and dump to s3
            self._build_batches(group_edits_dict=group_edits_dict)

            if not self._backfill:
                logging.info(f"Last polled date timestamp: {self._poll_from_timestamp}")
                update_poller_last_execution_time(
                    last_execution_time=datetime.fromtimestamp(self._poll_to_timestamp).strftime(
                        "%Y-%m-%dT%H:%M:%S.%f"
                    ),
                    tenant_name=self._aries_task_input.workflow.tenant,
                    workflow_name=self._aries_task_input.workflow.name,
                    flow_env_vars=self._config,
                )
        finally:
            if self._use_redis:
                # delete the key from redis
                self._redis_client.delete(name=workflow_execution_key)  # type: ignore
            # clean the directories
            self._clean_up_local_dirs()
            # raise exception when failures
            if self._failures:
                raise Exception(f"Poller failed due to failures: {self._failures}")

        return AriesTaskResult(output_param=None, app_metric=self._app_metric)
