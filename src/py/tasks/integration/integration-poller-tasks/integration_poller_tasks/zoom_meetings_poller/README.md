**Zoommeetings Poller**


`zoom_meetings_poller` is poller flow that runs on schedule, gets chat messages and attachments via APIs, and uploads it to S3.

The API takes in the timestamp parameter and returns the data from that timestamp till now. 

It then triggers the integration flow with the metadata path.

IOParamFieldSet can have `from_date` and `to_date` added to its free dictionary. This helps us in backfill.

# Sample path for batch files:
's3://pinafore.dev.steeleye.co/aries/ingress/nonstreamed/polled/zoom_meetings_poll/2023/10/17/<workflow_trace_id>/<timestamp_ns>_batch_<count>.json'

metadata:
's3://pinafore.dev.steeleye.co/aries/ingress/nonstreamed/polled/zoom_meetings_poll/2023/10/17/<workflow_trace_id>/attachments/metadata/<meeting_uuid>/<meeting_uuid>_metadata_1.json'

's3://pinafore.dev.steeleye.co/aries/ingress/nonstreamed/polled/zoom_meetings_poll/2023/10/17/<workflow_trace_id>/attachments/metadata/<meeting_uuid>/<meeting_uuid>_metadata_2.json'

recordings:
's3://pinafore.dev.steeleye.co/aries/ingress/nonstreamed/polled/zoom_meetings_poll/2023/10/17/<workflow_trace_id>/attachments/recordings/<meeting_uuid>/<meeting_uuid>_recording.json'

's3://pinafore.dev.steeleye.co/aries/ingress/nonstreamed/polled/zoom_meetings_poll/2023/10/17/<workflow_trace_id>/attachments/recordings/<meeting_uuid>/<file_name>.<extension>'

participants:
's3://pinafore.dev.steeleye.co/aries/ingress/nonstreamed/polled/zoom_meetings_poll/2023/10/17/<workflow_trace_id>/attachments/participants/<meeting_uuid>/<meeting_uuid>_participants.json'

's3://pinafore.dev.steeleye.co/aries/ingress/nonstreamed/polled/zoom_meetings_poll/2023/10/17/<workflow_trace_id>/attachments/participants/<meeting_uuid>/<meeting_uuid>_participants_report.json'

Sample Backfill Input:

```
workflow = WorkflowFieldSet(
    name="zoom_meetings_poll",
    stack="dev-blue",
    tenant="pinafore",
    start_timestamp=datetime.now(),
)
input_param = IOParamFieldSet(
    params=dict(
        from_date="2023-08-01",  # optional
        to_date="2023-12-07",  # optional
)
)
task = TaskFieldSet(name="test", version="latest", success=False)
return AriesTaskInput(workflow=workflow, input_param=input_param, task=task)
```


Sample Normal Run Input:

```
workflow = WorkflowFieldSet(
    name="zoom_meetings_poll",
    stack="dev-blue",
    tenant="pinafore",
    start_timestamp=datetime.now(),
)
input_param = IOParamFieldSet(
    params=dict(
)
)
task = TaskFieldSet(name="test", version="latest", success=False)
return AriesTaskInput(workflow=workflow, input_param=input_param, task=task)
```

The poller also has the support for polling data into onboarding/sftp path.
If `custom_lake_path` is passed then use custom_lake_path + aries/ingress/… as the landing path. 
By default, it is set to None.
We check for `should_event` param when `custom_lake_path` is passed. If it is set (True), then we send io-event.
By default, it is set to False.

This is supported via IOParamFieldSet.

Sample:

```
workflow = WorkflowFieldSet(
        name="zoom_meetings_poll",
        stack="dev-blue",
        tenant="pinafore",
        start_timestamp=datetime.datetime.now(),
    )
    input_param = IOParamFieldSet(
        params=dict(
            from_date="2022-12-07",  # optional
            to_date="2022-12-11",  # optional
            custom_lake_path='onboarding/',  # optional
            should_event=False  # optional
        )
    )
    task = TaskFieldSet(name="test", version="latest", success=False)
    return AriesTaskInput(workflow=workflow, input_param=input_param, task=task)
```





