from aries_task_link.models import AriesTaskInput, AriesTaskResult
from integration_poller_tasks.slack_users_poller.slack_users import Slack<PERSON><PERSON>sPoll
from omegaconf import OmegaConf
from pathlib import Path


def slack_users_poll_run(aries_task_input: AriesTaskInput) -> AriesTaskResult:
    config = OmegaConf.load(Path(__file__).parent.joinpath("slack-users-poller-config.yml"))
    poller = SlackUsersPoll(aries_task_input=aries_task_input, config=config)
    return poller.run_poller()
