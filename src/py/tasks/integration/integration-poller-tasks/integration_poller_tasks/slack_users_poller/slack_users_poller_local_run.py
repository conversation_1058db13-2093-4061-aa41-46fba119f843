import logging
from benchmark_mode import benchmark
from integration_poller_tasks.main import integration_poller_tasks_run
from integration_poller_tasks.slack_users_poller.slack_users_poller_sample_input import sample_input

logger = logging.getLogger("slack_users_poller")


@benchmark
def main():
    logger.info("Starting execution...")
    output = integration_poller_tasks_run(aries_task_input=sample_input())
    logger.info(f"Finished executing with output {output}")


if __name__ == "__main__":
    main()
