# flake8: noqa: E402
import os

os.environ["DATA_PLATFORM_CONFIG_API_URL"] = "test"
os.environ["STACK"] = "local"
import addict
import pytest
from aries_io_event.io_param import IOParamFieldSet
from aries_io_event.task import TaskFieldSet
from aries_io_event.workflow import WorkflowFieldSet
from aries_task_link.models import AriesTaskInput
from datetime import datetime
from integration_poller_tasks.ms_teams_poll_v2.ms_teams_poll_v2 import MsTeamsPollV2
from mock.mock import DEFAULT, patch
from se_comms_ingress_utils.custom_exceptions import (
    EitherStartOrEndDateNotExists,
    FromDateGreaterThanToDate,
)


@pytest.fixture()
def sample_input_no_date() -> AriesTaskInput:
    workflow = WorkflowFieldSet(
        name="ms_teams_poll_v2",
        stack="dev-blue",
        tenant="pinafore",
        start_timestamp=datetime.now(),
    )
    input_param = IOParamFieldSet(
        params=dict(
            microsoft_tenant_id="tenant_microsoft_id",
            from_date="2023-08-11",
            to_date="",
            max_worker_pool="15",
            custom_lake_path="custom/path",
            should_event=True,
        )
    )
    task = TaskFieldSet(name="test", version="latest", success=False)
    return AriesTaskInput(workflow=workflow, input_param=input_param, task=task)


@pytest.fixture()
def sample_input_to_date_greater() -> AriesTaskInput:
    workflow = WorkflowFieldSet(
        name="ms_teams_poll_v2",
        stack="dev-blue",
        tenant="pinafore",
        start_timestamp=datetime.now(),
    )
    input_param = IOParamFieldSet(
        params=dict(
            microsoft_tenant_id="tenant_microsoft_id",
            from_date="2023-09-11",
            to_date="2023-08-11",
            max_worker_pool="15",
            custom_path="custom/path",
            should_event=True,
        )
    )
    task = TaskFieldSet(name="test", version="latest", success=False)
    return AriesTaskInput(workflow=workflow, input_param=input_param, task=task)


@pytest.fixture()
def config():
    return addict.Dict(
        {
            "data_platform_config_api_url": "localhost:8080",
            "stack": "local",
            "version": "version",
            "vault": {
                "url": "dev-vault-url",
                "mount_point": "data-engineer",
                "auth_method": "token",
                "token": "",
                "k8s_role": "",
                "k8s_jwt_path": "",
                "k8s_auth_mount_point": "",
            },
        }
    )


@patch.multiple(
    "integration_poller_tasks.ms_teams_poll_v2.ms_teams_poll_v2",
    secrets_client=DEFAULT,
    GraphApiClient=DEFAULT,
    CompatibleTenantWorkflowAPIClient=DEFAULT,
    AriesApiClient=DEFAULT,
    TenantWorkflowAPI=DEFAULT,
    KafkaRestClient=DEFAULT,
)
def test_one_date_is_none(sample_input_no_date, config, **kwargs):
    ms_teams_poll_v2 = MsTeamsPollV2(sample_input_no_date, config)
    with pytest.raises(EitherStartOrEndDateNotExists):
        ms_teams_poll_v2.run()


@patch.multiple(
    "integration_poller_tasks.ms_teams_poll_v2.ms_teams_poll_v2",
    secrets_client=DEFAULT,
    GraphApiClient=DEFAULT,
    CompatibleTenantWorkflowAPIClient=DEFAULT,
    AriesApiClient=DEFAULT,
    TenantWorkflowAPI=DEFAULT,
    KafkaRestClient=DEFAULT,
)
def test_end_date_greater_from_date(sample_input_to_date_greater, config, **kwargs):
    ms_teams_poll_v2 = MsTeamsPollV2(sample_input_to_date_greater, config)
    with pytest.raises(FromDateGreaterThanToDate):
        ms_teams_poll_v2.run()
