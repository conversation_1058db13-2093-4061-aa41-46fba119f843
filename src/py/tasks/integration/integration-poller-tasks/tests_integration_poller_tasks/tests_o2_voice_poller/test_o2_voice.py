# ruff: noqa: E402
# type: ignore
import os
from datetime import datetime

os.environ["DATA_PLATFORM_CONFIG_API_URL"] = "https://test-enterprise.steeleye.co"
os.environ["STACK"] = "local"

import addict
import botocore.exceptions
import pytest
from aries_io_event.app_metric import AppMetricFieldSet
from aries_io_event.io_param import IOParamFieldSet
from aries_io_event.model import IOEvent
from aries_io_event.task import TaskFieldSet
from aries_io_event.workflow import WorkflowFieldSet
from aries_task_link.models import AriesTaskInput, AriesTaskResult
from fsspec.implementations.sftp import SFTPFileSystem
from integration_poller_tasks.o2_voice_poller.o2_voice import O2VoicePoller
from mock import mock
from mock.mock import DEFAULT, MagicMock, patch

workflow = WorkflowFieldSet(
    name="o2_voice_poller",
    stack="dev-blue",
    tenant="pinafore",
    start_timestamp=datetime.now(),
)
workflow_batch = WorkflowFieldSet(
    name="o2_voice_poller",
    stack="uat-blue",
    tenant="pinafore",
    start_timestamp=datetime.now(),
)

input_param = IOParamFieldSet(
    params=dict(
        from_date="2023-01-01",  # optional
        to_date="2023-01-03",  # optional
    )
)
task = TaskFieldSet(name="test", version="latest", success=False)

SAMPLE_EVENT = AriesTaskInput(workflow=workflow, input_param=input_param, task=task)
SAMPLE_EVENT_BATCH = AriesTaskInput(workflow=workflow_batch, input_param=input_param, task=task)

MOCK_SFTP_SECRETS = {
    "password": "mockpassword",
    "port": "22",
    "private_key_ascii": "",
    "username": "mockuser",
}
MOCK_RESPONSE = {"tenant": {"lake_prefix": "s3://pinafore.dev.steeleye.co/"}}


@pytest.fixture()
def config():
    return addict.Dict(
        {
            "cloud": "aws",
            "aws_region": None,
            "data_platform_config_api_url": "localhost:8080",
            "stack": "local",
            "kafka_rest_proxy_url": "test-url",
            "proxy": {"host": "", "port": 8080},
            "version": "version",
            "vault": {
                "url": "dev-vault-url",
                "mount_point": "data-engineer",
                "auth_method": "token",
                "token": "",
                "k8s_role": "",
                "k8s_jwt_path": "",
                "k8s_auth_mount_point": "",
            },
        }
    )


@pytest.fixture()
def config_without_proxy():
    return addict.Dict(
        {
            "cloud": "aws",
            "aws_region": None,
            "data_platform_config_api_url": "localhost:8080",
            "stack": "uat",
            "kafka_rest_proxy_url": "test-url",
            "version": "version",
            "vault": {
                "url": "dev-vault-url",
                "mount_point": "data-engineer",
                "auth_method": "token",
                "token": "",
                "k8s_role": "",
                "k8s_jwt_path": "",
                "k8s_auth_mount_point": "",
            },
        }
    )


EXPECTED_EVENT = IOEvent(
    workflow=WorkflowFieldSet(
        name="o2_voice",
        stack="local",
        tenant="pinafore",
        start_timestamp=datetime.now(),
    ),
    task=TaskFieldSet(name="o2_voice_poller", version="version", success=True),
    io_param=IOParamFieldSet(
        params=dict(
            streamed=False,
            aries_task_to_domain={},
        ),
    ),
)
EXPECTED_EVENT.workflow.trace_id = mock.ANY
EXPECTED_EVENT.task.id = mock.ANY
EXPECTED_EVENT.task.timestamp = mock.ANY
EXPECTED_EVENT.workflow.start_timestamp = mock.ANY
EXPECTED_EVENT.io_param.params["file_uri"] = mock.ANY

EXPECTED_EVENT_UAT = IOEvent(
    workflow=WorkflowFieldSet(
        name="o2_voice",
        stack="uat",
        tenant="pinafore",
        start_timestamp=datetime.now(),
    ),
    task=TaskFieldSet(name="o2_voice_poller", version="version", success=True),
    io_param=IOParamFieldSet(
        params=dict(
            streamed=False,
            aries_task_to_domain={},
        ),
    ),
)
EXPECTED_EVENT_UAT.workflow.trace_id = mock.ANY
EXPECTED_EVENT_UAT.task.id = mock.ANY
EXPECTED_EVENT_UAT.task.timestamp = mock.ANY
EXPECTED_EVENT_UAT.workflow.start_timestamp = mock.ANY
EXPECTED_EVENT_UAT.io_param.params["file_uri"] = mock.ANY


class FileSystemA(SFTPFileSystem):
    def __init__(self):
        self._file_instance = MagicMock()

    def listdir(self, path):
        # test for prod path:
        if (
            path == "/Product Suite/Product Suite/Recordings/CallRecordings/WAV_16_8000_S"
            "/Daily/2023/01/01"
        ):
            return [
                {
                    "name": "/Product Suite/Product Suite/Recordings/CallRecordings/"
                    "WAV_16_8000_S/Daily/2023/01/01/"
                    "20230131084247_447742447369_447354250160_4771290_169.wav",
                    "size": 37,
                    "type": "file",
                    "uid": 1001,
                    "gid": 100,
                },
                {
                    "name": "/Product Suite/Product Suite/Recordings/CallRecordings"
                    "/WAV_16_8000_S/Daily"
                    "/2023/01/01/20230131084247_447742447369_447354250160_4771290_189.wav",
                    "size": 37,
                    "type": "file",
                    "uid": 1001,
                    "gid": 100,
                },
            ]

        # test for mock sftp path:
        elif (
            path == "data/pinafore/o2_voice_poller/Product Suite/Product Suite/Recordings"
            "/CallRecordings/WAV_16_8000_S/Daily/2023/01/01"
        ):
            return [
                {
                    "name": "data/pinafore/o2_voice_poller/Product Suite"
                    "/Product Suite"
                    "/Recordings/CallRecordings/WAV_16_8000_S/Daily/2023/01/01/"
                    "20230131084247_447742447369_447354250160_4771290_169.wav",
                    "size": 37,
                    "type": "file",
                    "uid": 1001,
                    "gid": 100,
                },
                {
                    "name": "data/pinafore/o2_voice_poller/Product Suite/Product Suite"
                    "/Recordings/CallRecordings/WAV_16_8000_S/Daily/2023/01/01/"
                    "20230131084247_447742447369_447354250170_4771290_169.wav",
                    "size": 37,
                    "type": "file",
                    "uid": 1001,
                    "gid": 100,
                },
            ]
        elif (
            path == "/Product Suite/Product Suite/Recordings/CallRecordings/WAV_16_8000_S"
            "/Daily/2023/01/02"
        ):
            data = {
                "size": 37,
                "type": "file",
                "uid": 1001,
                "gid": 100,
            }
            list_of_sftp_files = []
            for i in range(0, 2001):
                data["name"] = f"name{i}"
                list_of_sftp_files.append(data)
            return list_of_sftp_files
        else:
            return []

    def cp_file(self, path1, path2, **kwargs):
        pass

    def created(self, path):
        pass

    def modified(self, path):
        pass

    def sign(self, path, expiration=100, **kwargs):
        pass

    def put_file(self, lpath, rpath, callback=None, **kwargs):
        pass

    def open(
        self,
        path,
        mode="rb",
        block_size=None,
        cache_options=None,
        compression=None,
        **kwargs,
    ):
        return self._file_instance

    def get(self, path1, path2):
        pass


@patch.multiple(
    "se_comms_ingress_utils.abstractions.o2_poller",
    KafkaRestClient=DEFAULT,
    get_filesystem=DEFAULT,
    CompatibleTenantWorkflowAPIClient=DEFAULT,
    AriesApiClient=DEFAULT,
    TenantWorkflowAPI=DEFAULT,
    secrets_client=DEFAULT,
    get_sftp_fs_from_addict=DEFAULT,
)
@patch.multiple(
    "se_fsspec_utils.sftp_utils",
    fsspec=DEFAULT,
)
def test_poller_success(config, **kwargs):
    kwargs["secrets_client"].return_value.get_secrets.return_value = MagicMock()
    kwargs["CompatibleTenantWorkflowAPIClient"].get.return_value = addict.Dict(MOCK_RESPONSE)
    kwargs["get_filesystem"].return_value.exists.return_value = False
    kwargs["fsspec"].filesystem.return_value = FileSystemA()
    kwargs["get_sftp_fs_from_addict"].return_value = FileSystemA()
    poller = O2VoicePoller(aries_task_input=SAMPLE_EVENT, config=config)
    expected_output = poller.run_poller()
    assert expected_output == AriesTaskResult(
        output_param=None,
        app_metric=AppMetricFieldSet(metrics={"generic": {"errored_count": 0}}),
    )
    kwargs["KafkaRestClient"].return_value.send.assert_called_once_with(
        io_event=EXPECTED_EVENT,
        topic={},
        raise_on_connection_error=True,
        raise_on_serder_error=True,
    )


@patch.multiple(
    "se_comms_ingress_utils.abstractions.o2_poller",
    KafkaRestClient=DEFAULT,
    get_filesystem=DEFAULT,
    CompatibleTenantWorkflowAPIClient=DEFAULT,
    AriesApiClient=DEFAULT,
    TenantWorkflowAPI=DEFAULT,
    secrets_client=DEFAULT,
    get_sftp_fs_from_addict=DEFAULT,
)
@patch.multiple(
    "se_fsspec_utils.sftp_utils",
    fsspec=DEFAULT,
)
def test_poller_success_with_batch(config, config_without_proxy, **kwargs):
    kwargs["secrets_client"].return_value.get_secrets.return_value = MagicMock()
    kwargs["CompatibleTenantWorkflowAPIClient"].get.return_value = addict.Dict(MOCK_RESPONSE)
    kwargs["get_filesystem"].return_value.exists.return_value = False
    kwargs["fsspec"].filesystem.return_value = FileSystemA()
    kwargs["get_sftp_fs_from_addict"].return_value = FileSystemA()
    poller = O2VoicePoller(aries_task_input=SAMPLE_EVENT_BATCH, config=config_without_proxy)
    expected_output = poller.run_poller()
    assert expected_output == AriesTaskResult(
        output_param=None,
        app_metric=AppMetricFieldSet(metrics={"generic": {"errored_count": 0}}),
    )
    kwargs["KafkaRestClient"].return_value.send.assert_called_with(
        io_event=EXPECTED_EVENT_UAT,
        topic={},
        raise_on_connection_error=True,
        raise_on_serder_error=True,
    )


@patch.multiple(
    "se_comms_ingress_utils.abstractions.o2_poller",
    KafkaRestClient=DEFAULT,
    get_filesystem=DEFAULT,
    CompatibleTenantWorkflowAPIClient=DEFAULT,
    AriesApiClient=DEFAULT,
    TenantWorkflowAPI=DEFAULT,
    secrets_client=DEFAULT,
    write=DEFAULT,
    get_sftp_fs_from_addict=DEFAULT,
)
@patch.multiple(
    "se_fsspec_utils.sftp_utils",
    fsspec=DEFAULT,
)
def test_poller_success_already_existing_files(config_without_proxy, **kwargs):
    kwargs["secrets_client"].return_value.get_secrets.return_value = MagicMock()
    kwargs["CompatibleTenantWorkflowAPIClient"].get.return_value = addict.Dict(MOCK_RESPONSE)
    kwargs["get_filesystem"].return_value.exists.return_value = True
    kwargs["fsspec"].filesystem.return_value = FileSystemA()
    kwargs["get_sftp_fs_from_addict"].return_value = FileSystemA()
    poller = O2VoicePoller(aries_task_input=SAMPLE_EVENT, config=config_without_proxy)
    expected_output = poller.run_poller()
    assert expected_output == AriesTaskResult(
        output_param=None,
        app_metric=AppMetricFieldSet(metrics={"generic": {"errored_count": 0}}),
    )
    kwargs["KafkaRestClient"].return_value.send.assert_not_called()


@patch.multiple(
    "se_comms_ingress_utils.abstractions.o2_poller",
    KafkaRestClient=DEFAULT,
    get_filesystem=DEFAULT,
    CompatibleTenantWorkflowAPIClient=DEFAULT,
    AriesApiClient=DEFAULT,
    TenantWorkflowAPI=DEFAULT,
    secrets_client=DEFAULT,
    write=DEFAULT,
    get_sftp_fs_from_addict=DEFAULT,
)
@patch.multiple(
    "se_fsspec_utils.sftp_utils",
    fsspec=DEFAULT,
)
def test_poller_failure_fatal(config, **kwargs):
    kwargs["secrets_client"].return_value.get_secrets.return_value = MagicMock()
    kwargs["CompatibleTenantWorkflowAPIClient"].get.return_value = addict.Dict(MOCK_RESPONSE)
    kwargs["get_filesystem"].return_value.exists.return_value = False
    kwargs["fsspec"].filesystem.return_value = FileSystemA()
    kwargs["get_sftp_fs_from_addict"].return_value = FileSystemA()
    kwargs["write"].side_effect = botocore.exceptions.ClientError({}, "PutObject")
    poller = O2VoicePoller(aries_task_input=SAMPLE_EVENT, config=config)
    with pytest.raises(botocore.exceptions.ClientError):
        poller.run_poller()
    kwargs["KafkaRestClient"].return_value.send.assert_not_called()


@patch.multiple(
    "se_comms_ingress_utils.abstractions.o2_poller",
    KafkaRestClient=DEFAULT,
    get_filesystem=DEFAULT,
    CompatibleTenantWorkflowAPIClient=DEFAULT,
    AriesApiClient=DEFAULT,
    TenantWorkflowAPI=DEFAULT,
    secrets_client=DEFAULT,
    get_sftp_fs_from_addict=DEFAULT,
)
@patch.multiple(
    "se_fsspec_utils.sftp_utils",
    fsspec=DEFAULT,
)
def test_poller_failure_fatal_wrong_sftp_path(config, **kwargs):
    kwargs["secrets_client"].return_value.get_secrets.return_value = MagicMock()
    kwargs["CompatibleTenantWorkflowAPIClient"].get.return_value = addict.Dict(MOCK_RESPONSE)
    kwargs["get_filesystem"].return_value.exists.return_value = False
    kwargs["fsspec"].filesystem.return_value.listdir.side_effect = IOError
    kwargs["get_sftp_fs_from_addict"].return_value = FileSystemA()
    poller = O2VoicePoller(aries_task_input=SAMPLE_EVENT, config=config)
    poller.run_poller()
    kwargs["KafkaRestClient"].return_value.send.assert_called()


@patch.multiple(
    "se_comms_ingress_utils.abstractions.o2_poller",
    KafkaRestClient=DEFAULT,
    get_filesystem=DEFAULT,
    CompatibleTenantWorkflowAPIClient=DEFAULT,
    AriesApiClient=DEFAULT,
    TenantWorkflowAPI=DEFAULT,
    secrets_client=DEFAULT,
    write=DEFAULT,
    get_sftp_fs_from_addict=DEFAULT,
)
@patch.multiple(
    "se_fsspec_utils.sftp_utils",
    fsspec=DEFAULT,
)
def test_poller_failure_non_fatal(config, **kwargs):
    kwargs["secrets_client"].return_value.get_secrets.return_value = MagicMock()
    kwargs["CompatibleTenantWorkflowAPIClient"].get.return_value = addict.Dict(MOCK_RESPONSE)
    kwargs["get_filesystem"].return_value.exists.return_value = False
    kwargs["fsspec"].filesystem.return_value = FileSystemA()
    kwargs["get_sftp_fs_from_addict"].return_value = FileSystemA()
    kwargs["write"].side_effect = Exception
    poller = O2VoicePoller(aries_task_input=SAMPLE_EVENT, config=config)
    expected_output = poller.run_poller()
    assert expected_output == AriesTaskResult(
        output_param=None,
        app_metric=AppMetricFieldSet(metrics={"generic": {"errored_count": 2}}),
    )
    kwargs["KafkaRestClient"].return_value.send.assert_not_called()


@patch.multiple(
    "se_comms_ingress_utils.abstractions.o2_poller",
    KafkaRestClient=DEFAULT,
    get_filesystem=DEFAULT,
    CompatibleTenantWorkflowAPIClient=DEFAULT,
    AriesApiClient=DEFAULT,
    TenantWorkflowAPI=DEFAULT,
    secrets_client=DEFAULT,
    get_sftp_fs_from_addict=DEFAULT,
)
@patch.multiple(
    "se_fsspec_utils.sftp_utils",
    fsspec=DEFAULT,
)
def test_poller_success_custom_path_no_event(config, **kwargs):
    custom_workflow = WorkflowFieldSet(
        name="o2_voice_poller",
        stack="dev-blue",
        tenant="pinafore",
        start_timestamp=datetime.now(),
    )
    custom_input_param = IOParamFieldSet(
        params=dict(
            from_date="2023-01-01",  # optional
            to_date="2023-01-03",  # optional
            custom_lake_path="onboarding/",
        )
    )
    custom_event = AriesTaskInput(
        workflow=custom_workflow, input_param=custom_input_param, task=task
    )
    kwargs["secrets_client"].return_value.get_secrets.return_value = MagicMock()
    kwargs["CompatibleTenantWorkflowAPIClient"].get.return_value = addict.Dict(MOCK_RESPONSE)
    kwargs["get_filesystem"].return_value.exists.return_value = False
    kwargs["fsspec"].filesystem.return_value = FileSystemA()
    kwargs["get_sftp_fs_from_addict"].return_value = FileSystemA()
    poller = O2VoicePoller(aries_task_input=custom_event, config=config)
    expected_output = poller.run_poller()
    assert expected_output == AriesTaskResult(
        output_param=None,
        app_metric=AppMetricFieldSet(metrics={"generic": {"errored_count": 0}}),
    )
    kwargs["KafkaRestClient"].return_value.send.assert_not_called()


@patch.multiple(
    "se_comms_ingress_utils.abstractions.o2_poller",
    KafkaRestClient=DEFAULT,
    get_filesystem=DEFAULT,
    CompatibleTenantWorkflowAPIClient=DEFAULT,
    AriesApiClient=DEFAULT,
    TenantWorkflowAPI=DEFAULT,
    secrets_client=DEFAULT,
    get_sftp_fs_from_addict=DEFAULT,
)
@patch.multiple(
    "se_fsspec_utils.sftp_utils",
    fsspec=DEFAULT,
)
def test_poller_success_custom_path_event(config, **kwargs):
    custom_workflow = WorkflowFieldSet(
        name="o2_voice_poller",
        stack="dev-blue",
        tenant="pinafore",
        start_timestamp=datetime.now(),
    )
    custom_input_param = IOParamFieldSet(
        params=dict(
            from_date="2023-01-01",  # optional
            to_date="2023-01-03",  # optional
            custom_lake_path="onboarding/",
            should_event=True,
        )
    )
    custom_event = AriesTaskInput(
        workflow=custom_workflow, input_param=custom_input_param, task=task
    )
    kwargs["secrets_client"].return_value.get_secrets.return_value = MagicMock()
    kwargs["CompatibleTenantWorkflowAPIClient"].get.return_value = addict.Dict(MOCK_RESPONSE)
    kwargs["get_filesystem"].return_value.exists.return_value = False
    kwargs["fsspec"].filesystem.return_value = FileSystemA()
    kwargs["get_sftp_fs_from_addict"].return_value = FileSystemA()
    poller = O2VoicePoller(aries_task_input=custom_event, config=config)
    expected_output = poller.run_poller()
    assert expected_output == AriesTaskResult(
        output_param=None,
        app_metric=AppMetricFieldSet(metrics={"generic": {"errored_count": 0}}),
    )
    kwargs["KafkaRestClient"].return_value.send.assert_called_once_with(
        io_event=EXPECTED_EVENT,
        topic={},
        raise_on_connection_error=True,
        raise_on_serder_error=True,
    )


@patch.multiple(
    "se_comms_ingress_utils.abstractions.o2_poller",
    KafkaRestClient=DEFAULT,
    get_filesystem=DEFAULT,
    CompatibleTenantWorkflowAPIClient=DEFAULT,
    AriesApiClient=DEFAULT,
    TenantWorkflowAPI=DEFAULT,
    secrets_client=DEFAULT,
    get_sftp_fs_from_addict=DEFAULT,
)
@patch.multiple(
    "se_fsspec_utils.sftp_utils",
    fsspec=DEFAULT,
)
def test_poller_success_with_lookback_days(config, **kwargs):
    """no files in the look back days so no kafka assertion."""
    custom_workflow = WorkflowFieldSet(
        name="o2_voice_poller",
        stack="dev-blue",
        tenant="pinafore",
        start_timestamp=datetime.now(),
    )
    custom_input_param = IOParamFieldSet(params=dict(look_back_days=3))
    custom_event = AriesTaskInput(
        workflow=custom_workflow, input_param=custom_input_param, task=task
    )
    kwargs["secrets_client"].return_value.get_secrets.return_value = MagicMock()
    kwargs["CompatibleTenantWorkflowAPIClient"].get.return_value = addict.Dict(MOCK_RESPONSE)
    kwargs["get_filesystem"].return_value.exists.return_value = False
    kwargs["fsspec"].filesystem.return_value = FileSystemA()
    kwargs["get_sftp_fs_from_addict"].return_value = FileSystemA()
    poller = O2VoicePoller(aries_task_input=custom_event, config=config)
    expected_output = poller.run_poller()
    assert expected_output == AriesTaskResult(
        output_param=None,
        app_metric=AppMetricFieldSet(metrics={"generic": {"errored_count": 0}}),
    )
    kwargs["KafkaRestClient"].return_value.send.assert_not_called()
