# flake8: noqa: E402
import os

os.environ["DATA_PLATFORM_CONFIG_API_URL"] = "test"
os.environ["STACK"] = "local"

import botocore.exceptions
import datetime
import pytest
from addict import addict
from aries_io_event.app_metric import AppMetricFieldSet
from aries_io_event.io_param import IOParamFieldSet
from aries_io_event.task import TaskFieldSet
from aries_io_event.workflow import WorkflowFieldSet
from aries_task_link.models import AriesTaskInput, AriesTaskResult
from integration_poller_tasks.eze_eclipse_poller.eze_eclipse import (
    EzeEclipsePoll,
    InvalidDataFlagsException,
)
from mock.mock import DEFAULT, patch


@pytest.fixture()
def sample_input() -> AriesTaskInput:
    workflow = WorkflowFieldSet(
        name="eze_eclipse",
        stack="dev-blue",
        tenant="pinafore",
        start_timestamp=datetime.datetime.now(),
    )
    input_param = IOParamFieldSet(params=dict())
    task = TaskFieldSet(name="test", version="latest", success=False)
    return AriesTaskInput(workflow=workflow, input_param=input_param, task=task)


@pytest.fixture()
def sample_input_with_custom_path() -> AriesTaskInput:
    workflow = WorkflowFieldSet(
        name="eze_eclipse",
        stack="dev-blue",
        tenant="pinafore",
        start_timestamp=datetime.datetime.now(),
    )
    input_param = IOParamFieldSet(params=dict(custom_lake_path="onboarding/"))
    task = TaskFieldSet(name="test", version="latest", success=False)
    return AriesTaskInput(workflow=workflow, input_param=input_param, task=task)


@pytest.fixture()
def sample_input_back_fill() -> AriesTaskInput:
    workflow = WorkflowFieldSet(
        name="eze_eclipse",
        stack="dev-blue",
        tenant="pinafore",
        start_timestamp=datetime.datetime.now(),
    )
    input_param = IOParamFieldSet(
        params=dict(
            from_date="2022-12-07",  # optional
            to_date="2022-12-11",  # optional
        )
    )
    task = TaskFieldSet(name="test", version="latest", success=False)
    return AriesTaskInput(workflow=workflow, input_param=input_param, task=task)


@pytest.fixture(
    params=[
        {"skip_positions": True, "skip_trades": False},  # process trades
        {"skip_positions": False, "skip_trades": True},  # process positions
        {"skip_positions": False, "skip_trades": False},  # process both
        {"skip_positions": True, "skip_trades": True},  # skip both
    ]
)
def sample_input_with_skip_data_logic(request) -> AriesTaskInput:
    # Extract the skip flags from the params passed by the fixture
    skip_positions = request.param["skip_positions"]
    skip_trades = request.param["skip_trades"]

    workflow = WorkflowFieldSet(
        name="eze_eclipse",
        stack="dev-blue",
        tenant="pinafore",
        start_timestamp=datetime.datetime.now(),
    )
    input_param = IOParamFieldSet(
        params=dict(skip_positions=skip_positions, skip_trades=skip_trades)
    )
    task = TaskFieldSet(name="test", version="latest", success=False)
    return AriesTaskInput(workflow=workflow, input_param=input_param, task=task)


@pytest.fixture()
def config():
    return addict.Dict(
        {
            "data_platform_config_api_url": "localhost:8080",
            "stack": "local",
            "vault": {
                "url": "dev-vault-url",
                "mount_point": "data-engineer",
                "auth_method": "token",
                "token": "",
                "k8s_role": "",
                "k8s_jwt_path": "",
                "k8s_auth_mount_point": "",
            },
        }
    )


MOCK_RESPONSE = {
    "paused": False,
    "id": 3,
    "max_batch_size": None,
    "workflow_last_executed": None,
    "time_created": "2023-06-09T10:06:14.733020",
    "io_topic": "mock_topic",
    "tenant_id": 1,
    "workflow_id": 3,
    "batch_timeout_s": None,
    "workflow_execution_ref": None,
    "time_updated": None,
    "tenant": {
        "id": 1,
        "lake_prefix": "s3://pinafore.dev.steeleye.co/",
        "time_updated": None,
        "stack_id": 1,
        "name": "pinafore",
        "time_created": "2023-06-09T10:00:22.860947",
        "stack": {
            "paused": False,
            "name": "dev-blue",
            "time_updated": None,
            "id": 1,
            "time_created": "2023-06-09T10:00:03.477200",
        },
    },
    "workflow": {
        "s3_feed_prefix": "test",
        "name": "eze_eclipse",
        "time_created": "2023-06-09T10:02:34.313252",
        "id": 3,
        "streamed": False,
        "time_updated": None,
    },
}

MOCK_SECRETS = {"client_id": "test", "client_secret": "test", "issuer": "test"}


@patch.multiple(
    "integration_poller_tasks.eze_eclipse_poller.eze_eclipse",
    secrets_client=DEFAULT,
    CompatibleTenantWorkflowAPIClient=DEFAULT,
    AriesApiClient=DEFAULT,
    TenantWorkflowAPI=DEFAULT,
    get_filesystem=DEFAULT,
    requests=DEFAULT,
)
def test_eze_eclipse_success(sample_input, config, **kwargs):
    kwargs["secrets_client"].return_value.get_secrets.return_value = addict.Dict(MOCK_SECRETS)
    mock_get_response = {
        "token_endpoint": "https://example.com/token",
    }
    kwargs["requests"].get.return_value.json.return_value = mock_get_response
    mock_post_response = {"access_token": "your_access_token"}
    kwargs["requests"].post.return_value.json.return_value = mock_post_response
    poller = EzeEclipsePoll(config=config, aries_task_input=sample_input)
    expected_output = poller.run_poller()
    assert expected_output == AriesTaskResult(
        output_param=None, app_metric=AppMetricFieldSet(metrics={"generic": {"errored_count": 0}})
    )


@patch.multiple(
    "integration_poller_tasks.eze_eclipse_poller.eze_eclipse",
    secrets_client=DEFAULT,
    CompatibleTenantWorkflowAPIClient=DEFAULT,
    AriesApiClient=DEFAULT,
    TenantWorkflowAPI=DEFAULT,
    get_filesystem=DEFAULT,
    requests=DEFAULT,
)
def test_eze_eclipse_success_with_custom_path(sample_input_with_custom_path, config, **kwargs):
    kwargs["secrets_client"].return_value.get_secrets.return_value = addict.Dict(MOCK_SECRETS)
    mock_get_response = {
        "token_endpoint": "https://example.com/token",
    }
    kwargs["requests"].get.return_value.json.return_value = mock_get_response
    mock_post_response = {"access_token": "your_access_token"}
    kwargs["requests"].post.return_value.json.return_value = mock_post_response
    poller = EzeEclipsePoll(config=config, aries_task_input=sample_input_with_custom_path)
    expected_output = poller.run_poller()
    assert expected_output == AriesTaskResult(
        output_param=None, app_metric=AppMetricFieldSet(metrics={"generic": {"errored_count": 0}})
    )


@patch.multiple(
    "integration_poller_tasks.eze_eclipse_poller.eze_eclipse",
    secrets_client=DEFAULT,
    CompatibleTenantWorkflowAPIClient=DEFAULT,
    AriesApiClient=DEFAULT,
    TenantWorkflowAPI=DEFAULT,
    get_filesystem=DEFAULT,
    requests=DEFAULT,
)
def test_eze_eclipse_success_backfill(sample_input_back_fill, config, **kwargs):
    kwargs["secrets_client"].return_value.get_secrets.return_value = addict.Dict(MOCK_SECRETS)
    mock_get_response = {
        "token_endpoint": "https://example.com/token",
    }
    kwargs["requests"].get.return_value.json.return_value = mock_get_response
    mock_post_response = {"access_token": "your_access_token"}
    kwargs["requests"].post.return_value.json.return_value = mock_post_response
    poller = EzeEclipsePoll(config=config, aries_task_input=sample_input_back_fill)
    expected_output = poller.run_poller()
    assert expected_output == AriesTaskResult(
        output_param=None, app_metric=AppMetricFieldSet(metrics={"generic": {"errored_count": 0}})
    )


@patch.multiple(
    "integration_poller_tasks.eze_eclipse_poller.eze_eclipse",
    secrets_client=DEFAULT,
    CompatibleTenantWorkflowAPIClient=DEFAULT,
    AriesApiClient=DEFAULT,
    TenantWorkflowAPI=DEFAULT,
    get_filesystem=DEFAULT,
    requests=DEFAULT,
)
def test_eze_eclipse_failure_no_access_token(sample_input, config, **kwargs):
    kwargs["secrets_client"].return_value.get_secrets.return_value = addict.Dict(MOCK_SECRETS)
    mock_get_response = {
        "token_endpoint": "https://example.com/token",
    }
    kwargs["requests"].get.return_value.json.return_value = mock_get_response
    mock_post_response = {"access_token": None}
    kwargs["requests"].post.return_value.json.return_value = mock_post_response
    with pytest.raises(Exception, match="Unable to retrieve access token"):
        EzeEclipsePoll(config=config, aries_task_input=sample_input)


@patch.multiple(
    "integration_poller_tasks.eze_eclipse_poller.eze_eclipse",
    secrets_client=DEFAULT,
    CompatibleTenantWorkflowAPIClient=DEFAULT,
    AriesApiClient=DEFAULT,
    TenantWorkflowAPI=DEFAULT,
    get_filesystem=DEFAULT,
    requests=DEFAULT,
    write=DEFAULT,
    datetime=DEFAULT,
)
def test_eze_eclipse_failure_non_fatal_exception(sample_input, config, **kwargs):
    kwargs["datetime"].now.return_value.timestamp.return_value = datetime.datetime.fromisoformat(
        "2023-03-21 22:41:18"
    ).timestamp()
    kwargs["secrets_client"].return_value.get_secrets.return_value = addict.Dict(MOCK_SECRETS)
    mock_get_response = {
        "token_endpoint": "https://example.com/token",
    }
    kwargs["requests"].get.return_value.json.return_value = mock_get_response
    mock_post_response = {"access_token": "test"}
    kwargs["requests"].post.return_value.json.return_value = mock_post_response
    kwargs["write"].side_effect = Exception
    poller = EzeEclipsePoll(config=config, aries_task_input=sample_input)
    poller.run_poller()
    assert kwargs["requests"].get.call_count == 3
    assert kwargs["requests"].post.call_count == 1


@patch.multiple(
    "integration_poller_tasks.eze_eclipse_poller.eze_eclipse",
    secrets_client=DEFAULT,
    CompatibleTenantWorkflowAPIClient=DEFAULT,
    AriesApiClient=DEFAULT,
    TenantWorkflowAPI=DEFAULT,
    get_filesystem=DEFAULT,
    requests=DEFAULT,
    write=DEFAULT,
)
def test_eze_eclipse_failure_fatal_exception(sample_input, config, **kwargs):
    kwargs["secrets_client"].return_value.get_secrets.return_value = addict.Dict(MOCK_SECRETS)
    mock_get_response = {
        "token_endpoint": "https://example.com/token",
    }
    kwargs["requests"].get.return_value.status_code = 200
    kwargs["requests"].get.return_value.json.return_value = mock_get_response
    mock_post_response = {"access_token": "test"}
    kwargs["requests"].post.return_value.json.return_value = mock_post_response
    kwargs["write"].side_effect = botocore.exceptions.ClientError({}, "PutObject")
    poller = EzeEclipsePoll(config=config, aries_task_input=sample_input)
    with pytest.raises(botocore.exceptions.ClientError):
        poller.run_poller()
    assert kwargs["requests"].get.call_count == 2
    assert kwargs["requests"].post.call_count == 1


@patch.multiple(
    "integration_poller_tasks.eze_eclipse_poller.eze_eclipse",
    secrets_client=DEFAULT,
    CompatibleTenantWorkflowAPIClient=DEFAULT,
    AriesApiClient=DEFAULT,
    TenantWorkflowAPI=DEFAULT,
    get_filesystem=DEFAULT,
    requests=DEFAULT,
    write=DEFAULT,
)
def test_eze_eclipse_skip_data_logic(sample_input_with_skip_data_logic, config, **kwargs):
    kwargs["secrets_client"].return_value.get_secrets.return_value = addict.Dict(MOCK_SECRETS)
    mock_get_response = {
        "token_endpoint": "https://example.com/token",
    }
    kwargs["requests"].get.return_value.json.return_value = mock_get_response
    mock_post_response = {"access_token": "your_access_token"}
    kwargs["requests"].post.return_value.json.return_value = mock_post_response
    poller = EzeEclipsePoll(config=config, aries_task_input=sample_input_with_skip_data_logic)
    if sample_input_with_skip_data_logic.input_param.params.get(
        "skip_positions", False
    ) and sample_input_with_skip_data_logic.input_param.params.get("skip_trades", False):
        # If both skip flags are True, check if the exception is raised
        with pytest.raises(InvalidDataFlagsException):
            poller.run_poller()

    else:
        # If not both flags are True, check for normal output
        expected_output = poller.run_poller()
        assert expected_output == AriesTaskResult(
            output_param=None,
            app_metric=AppMetricFieldSet(metrics={"generic": {"errored_count": 0}}),
        )
