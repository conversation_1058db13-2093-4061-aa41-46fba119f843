# flake8: noqa: E402
# flake8: noqa: E501
import os

os.environ["DATA_PLATFORM_CONFIG_API_URL"] = "test"
os.environ["STACK"] = "local"

import datetime
import pytest
from addict import addict
from aries_io_event.io_param import IOParamFieldSet
from aries_io_event.task import TaskFieldSet
from aries_io_event.workflow import WorkflowFieldSet
from aries_task_link.models import AriesTaskInput


@pytest.fixture()
def sample_input() -> AriesTaskInput:
    workflow = WorkflowFieldSet(
        name="vmo2_poll",
        stack="dev-blue",
        tenant="test",
        start_timestamp=datetime.datetime.now(),
    )
    input_param = IOParamFieldSet(params=dict())
    task = TaskFieldSet(name="test", version="latest", success=False)
    return AriesTaskInput(workflow=workflow, input_param=input_param, task=task)


@pytest.fixture()
def sample_input_with_custom_path() -> AriesTaskInput:
    workflow = WorkflowFieldSet(
        name="vmo2_poll",
        stack="dev-blue",
        tenant="test",
        start_timestamp=datetime.datetime.now(),
    )
    input_param = IOParamFieldSet(
        params=dict(
            from_date="2022-12-07",  # optional
            custom_lake_path="boarding/",  # optional
        )
    )
    task = TaskFieldSet(name="test", version="latest", success=False)
    return AriesTaskInput(workflow=workflow, input_param=input_param, task=task)


@pytest.fixture()
def config():
    return addict.Dict(
        {
            "cloud": "aws",
            "aws_region": None,
            "data_platform_config_api_url": "localhost:8080",
            "stack": "local",
            "version": "version",
            "vault": {
                "url": "dev-vault-url",
                "mount_point": "data-engineer",
                "auth_method": "token",
                "token": "",
                "k8s_role": "",
                "k8s_jwt_path": "",
                "k8s_auth_mount_point": "",
            },
        }
    )


@pytest.fixture()
def response_metadata_mock():
    return {
        "metadata": {"nextCursor": "test-next-cursor"},
        "interactions": [
            {
                "interactionId": 349,
                "interactionAccessTokenString": "VWFu",
                "thirdPartyInteractionId": "FA163E714B19-1a10-ca90a700-5400625-67a3791d-e126",
                "interactionType": "Call",
                "direction": "Outbound",
                "startDateTimeUtc": "2025-02-05T14:43:56.3833333Z",
                "userIsInteractionOwner": "false",
                "commentCount": 0,
                "metadataJson": {
                    "SipCallId": "B2B.442.6736410.1738766635.880461189",
                },
                "isCompliant": "true",
                "sourceType": "MobilePhone",
                "legalHoldReasons": [],
                "subscriberPhoneNumberId": 10,
                "subscriberPhoneNumber": "447849909788",
                "retainUntilDateTimeUtc": "2032-02-04T00:00:00.0000000Z",
                "isRetainedIndefinitely": "false",
                "isThirdPartyStaff": "false",
                "thirdPartyPhoneNumberId": 156,
                "thirdPartyPhoneNumber": "+41227325060",
                "isPrivate": "false",
                "isExpired": "false",
                "isMediaAvailable": "true",
                "isMediaAvailableMessage": "Ready",
                "isMediaAvailableReason": "Ready",
                "recordingStatus": "Playable",
                "callRecordingUrl": "https://unified.recording.virginmediao2.co.uk/api/legacycursor/interaction/v2/test-acc-id/test-solution-id/recordings/media/349.wav?retreivalToken=VWFu",
                "callRecordingWaveformUrl": "https://unified.recording.virginmediao2.co.uk/api/legacycursor/interaction/v2/test-acc-id/test-solution-id/recordings/waveforms/349.wav?r",
                "hashTags": [],
                "endDateTimeUtc": "2025-02-05T14:45:25.3633333Z",
                "durationInSeconds": 89,
                "fileSizeBytes": 484296,
                "playedStatusHasPlayed": "false",
                "playedStatusPercentagePlayed": 0.0,
                "hasTranscription": "false",
            },
            {
                "interactionId": 347,
                "interactionAccessTokenString": "VWFu",
                "thirdPartyInteractionId": "FA163E5C158D-19fc-99bba700-3ca7f79-67a22ef0-40705",
                "interactionType": "Call",
                "direction": "Outbound",
                "startDateTimeUtc": "2025-02-04T15:15:03.1966667Z",
                "userIsInteractionOwner": "false",
                "commentCount": 0,
                "metadataJson": {
                    "SipCallId": "B2B.442.5712880.1738682102.208202896",
                },
                "isCompliant": "true",
                "sourceType": "MobilePhone",
                "legalHoldReasons": [],
                "subscriberPhoneNumberId": 4,
                "subscriberPhoneNumber": "447849909787",
                "retainUntilDateTimeUtc": "2032-02-03T00:00:00.0000000Z",
                "isRetainedIndefinitely": "false",
                "isThirdPartyStaff": "false",
                "thirdPartyPhoneNumberId": 154,
                "thirdPartyPhoneNumber": "02070468528",
                "isPrivate": "false",
                "isExpired": "false",
                "isMediaAvailable": "true",
                "isMediaAvailableMessage": "Ready",
                "isMediaAvailableReason": "Ready",
                "recordingStatus": "Playable",
                "callRecordingUrl": "https://unified.recording.virginmediao2.co.uk/api/legacycursor/interaction/v2/test-acc-id/test-solution-id/recordings/media/347.wav?retreivalToken=VWFu",
                "callRecordingWaveformUrl": "https://unified.recording.virginmediao2.co.uk/api/legacycursor/interaction/v2/test-acc-id/test-solution-id/recordings/waveforms/347.wav?r",
                "hashTags": [],
                "endDateTimeUtc": "2025-02-04T15:19:46.1700000Z",
                "durationInSeconds": 283,
                "fileSizeBytes": 1543100,
                "playedStatusHasPlayed": "false",
                "playedStatusPercentagePlayed": 0.0,
                "hasTranscription": "false",
            },
        ],
    }


@pytest.fixture()
def response_metadata_mock_with_call_and_sms_type():
    return {
        "metadata": {"nextCursor": "test-next-cursor"},
        "interactions": [
            {
                "interactionId": 349,
                "interactionAccessTokenString": "VWFu",
                "thirdPartyInteractionId": "FA163E714B19-1a10-ca90a700-5400625-67a3791d-e126",
                "interactionType": "Sms",
                "direction": "Outbound",
                "startDateTimeUtc": "2025-02-05T14:43:56.3833333Z",
                "userIsInteractionOwner": "false",
                "commentCount": 0,
                "metadataJson": {
                    "SipCallId": "B2B.442.6736410.1738766635.880461189",
                },
                "isCompliant": "true",
                "sourceType": "MobilePhone",
                "legalHoldReasons": [],
                "subscriberPhoneNumberId": 10,
                "subscriberPhoneNumber": "447849909788",
                "retainUntilDateTimeUtc": "2032-02-04T00:00:00.0000000Z",
                "isRetainedIndefinitely": "false",
                "isThirdPartyStaff": "false",
                "thirdPartyPhoneNumberId": 156,
                "thirdPartyPhoneNumber": "+41227325060",
                "isPrivate": "false",
                "isExpired": "false",
                "isMediaAvailable": "true",
                "isMediaAvailableMessage": "Ready",
                "isMediaAvailableReason": "Ready",
                "recordingStatus": "Playable",
                "callRecordingUrl": "https://unified.recording.virginmediao2.co.uk/api/legacycursor/interaction/v2/test-acc-id/test-solution-id/recordings/media/349.wav?retreivalToken=VWFu",
                "callRecordingWaveformUrl": "https://unified.recording.virginmediao2.co.uk/api/legacycursor/interaction/v2/test-acc-id/test-solution-id/recordings/waveforms/349.wav?r",
                "hashTags": [],
                "endDateTimeUtc": "2025-02-05T14:45:25.3633333Z",
                "durationInSeconds": 89,
                "fileSizeBytes": 484296,
                "playedStatusHasPlayed": "false",
                "playedStatusPercentagePlayed": 0.0,
                "hasTranscription": "false",
            },
            {
                "interactionId": 347,
                "interactionAccessTokenString": "VWFu",
                "thirdPartyInteractionId": "FA163E5C158D-19fc-99bba700-3ca7f79-67a22ef0-40705",
                "interactionType": "Call",
                "direction": "Outbound",
                "startDateTimeUtc": "2025-02-04T15:15:03.1966667Z",
                "userIsInteractionOwner": "false",
                "commentCount": 0,
                "metadataJson": {
                    "SipCallId": "B2B.442.5712880.1738682102.208202896",
                },
                "isCompliant": "true",
                "sourceType": "MobilePhone",
                "legalHoldReasons": [],
                "subscriberPhoneNumberId": 4,
                "subscriberPhoneNumber": "447849909787",
                "retainUntilDateTimeUtc": "2032-02-03T00:00:00.0000000Z",
                "isRetainedIndefinitely": "false",
                "isThirdPartyStaff": "false",
                "thirdPartyPhoneNumberId": 154,
                "thirdPartyPhoneNumber": "02070468528",
                "isPrivate": "false",
                "isExpired": "false",
                "isMediaAvailable": "true",
                "isMediaAvailableMessage": "Ready",
                "isMediaAvailableReason": "Ready",
                "recordingStatus": "Playable",
                "callRecordingUrl": "https://unified.recording.virginmediao2.co.uk/api/legacycursor/interaction/v2/test-acc-id/test-solution-id/recordings/media/347.wav?retreivalToken=VWFu",
                "callRecordingWaveformUrl": "https://unified.recording.virginmediao2.co.uk/api/legacycursor/interaction/v2/test-acc-id/test-solution-id/recordings/waveforms/347.wav?r",
                "hashTags": [],
                "endDateTimeUtc": "2025-02-04T15:19:46.1700000Z",
                "durationInSeconds": 283,
                "fileSizeBytes": 1543100,
                "playedStatusHasPlayed": "false",
                "playedStatusPercentagePlayed": 0.0,
                "hasTranscription": "false",
            },
        ],
    }


@pytest.fixture()
def next_cursor_metadata_mock():
    return {"metadata": {"nextCursor": "test-next-cursor"}, "interactions": []}
