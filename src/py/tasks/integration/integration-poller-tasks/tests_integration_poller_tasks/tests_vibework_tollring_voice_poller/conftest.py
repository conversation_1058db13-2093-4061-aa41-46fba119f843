# flake8: noqa: E402
# flake8: noqa: E501
import os

os.environ["DATA_PLATFORM_CONFIG_API_URL"] = "test"
os.environ["STACK"] = "local"

import datetime
import pytest
from addict import addict
from aries_io_event.io_param import IOParamFieldSet
from aries_io_event.task import TaskFieldSet
from aries_io_event.workflow import WorkflowFieldSet
from aries_task_link.models import AriesTaskInput


@pytest.fixture()
def sample_input() -> AriesTaskInput:
    workflow = WorkflowFieldSet(
        name="vibework_tollring_voice_poll",
        stack="dev-blue",
        tenant="test",
        start_timestamp=datetime.datetime.now(),
    )
    input_param = IOParamFieldSet(params=dict())
    task = TaskFieldSet(name="test", version="latest", success=False)
    return AriesTaskInput(workflow=workflow, input_param=input_param, task=task)


@pytest.fixture()
def sample_input_with_custom_path() -> AriesTaskInput:
    workflow = WorkflowFieldSet(
        name="vibework_tollring_voice_poll",
        stack="dev-blue",
        tenant="test",
        start_timestamp=datetime.datetime.now(),
    )
    input_param = IOParamFieldSet(
        params=dict(
            from_date="2022-12-07",  # optional
            custom_lake_path="boarding/",  # optional
        )
    )
    task = TaskFieldSet(name="test", version="latest", success=False)
    return AriesTaskInput(workflow=workflow, input_param=input_param, task=task)


@pytest.fixture()
def config():
    return addict.Dict(
        {
            "cloud": "aws",
            "aws_region": None,
            "data_platform_config_api_url": "localhost:8080",
            "stack": "local",
            "version": "version",
            "vault": {
                "url": "dev-vault-url",
                "mount_point": "data-engineer",
                "auth_method": "token",
                "token": "",
                "k8s_role": "",
                "k8s_jwt_path": "",
                "k8s_auth_mount_point": "",
            },
        }
    )


@pytest.fixture()
def response_metadata_mock():
    return [
        {
            "RecordId": "123456",
            "Call_date": "2025-09-01T00:00:00",
            "Call_time": "17106",
            "Dest_cli": "",
            "Caller_cli": " 1111111111",
            "Duration": "186",
            "Direction": "O",
            "Call_tag": "",
            "Extension": "1032",
            "LookupResult": "0",
            "Lookup": "0",
            "LookupFlag": "0",
            "isEval": "0",
            "TenantId": "1",
            "StatusId": "1",
            "Date_expiry": "2125-09-01T00:00:00",
            "IsBuffer": "0",
            "IsPause": "false",
            "onDemand": "false",
            "DC_Id": "null",
        },
        {
            "RecordId": "2346314",
            "Call_date": "2025-09-01T00:00:00",
            "Call_time": "176",
            "Dest_cli": "",
            "Caller_cli": " 852299999",
            "Duration": "1826",
            "Direction": "O",
            "Call_tag": "",
            "Extension": "103",
            "LookupResult": "0",
            "Lookup": "0",
            "LookupFlag": "0",
            "isEval": "0",
            "TenantId": "3",
            "StatusId": "1",
            "Date_expiry": "2125-09-01T00:00:00",
            "IsBuffer": "0",
            "IsPause": "false",
            "onDemand": "false",
            "DC_Id": "null",
        },
    ]
