# type: ignore
import logging
from aries_config_cached_client.tenant_workflow import CachedTenantWorkflowAPIClient
from aries_se_comms_tasks.feeds.meeting.microsoft.outlook_calendar.process_attachments_outlook_calendar import (  # noqa: E501
    run_process_attachments_outlook_calendar,
)
from aries_se_comms_tasks.feeds.meeting.microsoft.outlook_calendar.static import (
    OUTLOOK_CALENDAR_FLOW_NAME,
)
from aries_se_comms_tasks.generic.participants.link_participants import (
    Params as LinkParticipantsParams,
)
from aries_se_comms_tasks.generic.participants.link_participants import run_link_participants
from aries_se_comms_tasks.meeting.static import MeetingModelFields
from aries_se_core_tasks.aries.utility_tasks.finish_flow import add_nested_params, finish_flow
from aries_se_core_tasks.aries.utility_tasks.get_tenant_bucket import get_tenant_bucket
from aries_se_core_tasks.aries.utility_tasks.unpack_aries_task_input import unpack_aries_task_input
from aries_se_core_tasks.frame.frame_concatenator import Params as ParamsFrameConcatenator
from aries_se_core_tasks.frame.frame_concatenator import run_frame_concatenator
from aries_se_core_tasks.generic.generate_record_identifiers_for_df import (
    Params as GenerateRecordFileIdentifiersForDfParams,
)
from aries_se_core_tasks.generic.generate_record_identifiers_for_df import (
    run_generate_record_identifiers_for_df,
)
from aries_se_core_tasks.get_primary_transformations import run_get_primary_transformations
from aries_se_core_tasks.io.create_ndjson_path import create_ndjson_path
from aries_se_core_tasks.io.read.cloud.download_file import run_download_file
from aries_se_core_tasks.io.write.write_ndjson import run_write_ndjson
from aries_se_core_tasks.static import MetaModel
from aries_task_link.models import AriesTaskInput
from integration_audio_comms_tasks.meeting.outlook_calendar_meeting_transform.input_schema import (
    OutlookCalendarMeetingTransformAriesTaskInput,
)
from se_core_tasks.frame.frame_concatenator import OrientEnum
from se_data_lake.cloud_utils import (
    get_bucket,
    get_cloud_provider_from_file_uri,
    get_cloud_provider_prefix,
)
from se_data_lake.lake_path import get_ingress_depository_lake_path_for_attachments
from se_elastic_schema.models import Meeting
from se_enums.cloud import CloudProviderEnum
from se_enums.elastic_search import EsActionEnum
from se_io_utils.json_utils import ndjson_to_flat_dataframe
from typing import Optional

logger = logging.getLogger(__name__)


def outlook_calendar_meeting_transform_flow(
    aries_task_input: AriesTaskInput,
    app_metrics_path: str,
    audit_path: str,
    result_path: str = "result.json",
):
    """
    -------------------------------------------------------------
    ||             OutlookCalendarMeetingTransform             ||
    -------------------------------------------------------------
    """
    outlook_calendar_meeting_transform_input = unpack_aries_task_input(
        aries_task_input=aries_task_input, model=OutlookCalendarMeetingTransformAriesTaskInput
    )

    cached_tenant_workflow_config = CachedTenantWorkflowAPIClient.get(
        tenant_name=aries_task_input.workflow.tenant,
        workflow_name=aries_task_input.workflow.name,
        stack_name=aries_task_input.workflow.stack,
    )

    # Get realm from input file path
    realm: str = get_bucket(file_uri=outlook_calendar_meeting_transform_input.file_uri)
    tenant: str = aries_task_input.workflow.tenant

    cloud_provider: CloudProviderEnum = get_cloud_provider_from_file_uri(
        file_uri=cached_tenant_workflow_config.tenant.lake_prefix
    )
    cloud_provider_prefix = get_cloud_provider_prefix(value=cloud_provider)

    streamed: bool = cached_tenant_workflow_config.workflow.streamed

    # Determine the Cloud Bucket of the tenant
    tenant_bucket_with_cloud_prefix = get_tenant_bucket(
        task_input=outlook_calendar_meeting_transform_input,
        cloud_provider_prefix=cloud_provider_prefix,
    )

    # Download remote file from Cloud Bucket
    local_file_path: str = run_download_file(
        file_url=outlook_calendar_meeting_transform_input.file_uri,
    )

    files_dataframe = ndjson_to_flat_dataframe(ndjson_file_path=local_file_path)

    primary_mappings_result = run_get_primary_transformations(
        source_frame=files_dataframe,
        flow=OUTLOOK_CALENDAR_FLOW_NAME,
        realm=realm,
        tenant=tenant,
        source_file_url=outlook_calendar_meeting_transform_input.file_uri,
    )

    # Get the path to upload transcripts and recordings
    attachments_path = get_ingress_depository_lake_path_for_attachments(
        workflow_name=aries_task_input.workflow.name,
        workflow_trace_id=aries_task_input.workflow.trace_id,
        task_io_params=aries_task_input.input_param.params,
        workflow_start_timestamp=aries_task_input.workflow.start_timestamp,
    ).lstrip("/")

    # Run Outlook Calendar Attachment mappings
    attachments_result = run_process_attachments_outlook_calendar(
        source_frame=primary_mappings_result,
        realm=realm,
        attachments_path=attachments_path,
        cloud_provider=cloud_provider,
        app_metrics_path=app_metrics_path,
        audit_path=audit_path,
    )

    # Concatenate voice and attachment data
    concatenated_calls_result = run_frame_concatenator(
        metadata_df=primary_mappings_result,
        attachment_df=attachments_result,
        params=ParamsFrameConcatenator(
            orient=OrientEnum.horizontal,
        ),
    )

    # Get the sourceKey, this is used for the audit keys
    call_frame_with_amp_id = run_generate_record_identifiers_for_df(
        source_frame=concatenated_calls_result,
        params=GenerateRecordFileIdentifiersForDfParams(
            data_model=MetaModel.MEETING, target_record_identifier_col="record_identifier"
        ),
        streamed=streamed,
        cloud_provider=cloud_provider,
    )

    # Enrichment: Link participants based on the create participant identifiers
    participants_result = run_link_participants(
        tenant=tenant,
        source_frame=call_frame_with_amp_id,
        params=LinkParticipantsParams(target_participants_column=MeetingModelFields.PARTICIPANTS),
        streamed=streamed,
        app_metrics_path=app_metrics_path,
        audit_path=audit_path,
        data_models=[Meeting],
        record_identifier_column="record_identifier",
    )

    # Concatenate the participants data with the entire call and attachment data
    final_result = run_frame_concatenator(
        call_and_attachment_df=concatenated_calls_result,
        participants_df=participants_result,
        params=ParamsFrameConcatenator(orient=OrientEnum.horizontal),
    )

    # Write the result to the result path
    final_records_length: int = final_result.shape[0]

    # Will store the generated output paths
    ndjson_path: Optional[str] = None

    if final_records_length > 0:
        # Create the appropriate path where the ndjson result is to be uploaded
        ndjson_path = create_ndjson_path(
            tenant_bucket=tenant_bucket_with_cloud_prefix,
            aries_task_input=aries_task_input,
            model=MetaModel.MEETING,
        )

        # Write the transformed_df data frame into a ndjson file to the generated ndjson path
        run_write_ndjson(
            source_serializer_result=final_result,
            output_filepath=ndjson_path,
            audit_output=True,
            app_metrics_path=app_metrics_path,
            audit_path=audit_path,
        )

    meeting_output = (
        add_nested_params(
            file_uri=ndjson_path,
            es_action=EsActionEnum.INDEX.value,
            data_model=Meeting.get_reference().get_qualified_reference(),
            ignore_empty_file_uri=True,
        )
        if ndjson_path
        else None
    )

    finish_flow(
        result_path=result_path,
        result_data={
            MetaModel.MEETING: meeting_output,
        },
    )
