ELASTIC_HOST=localhost
ELASTIC_PORT=9401
TASK_NAME=aries_iv_get_transcription
TASK_WORKER_DOMAIN=dev-blue
DEBUG=1
REDIS_HOST=localhost
REDIS_PORT=6379
BASE_INTELLIGENT_VOICE_URL='https://iv.dev-blue.steeleye.co:8443/vrxServlet/v2/'
VAULT_URL='https://vault.dev-enterprise.steeleye.co/'
VAULT_MOUNT_POINT="data-engineer"
VAULT_AUTH_METHOD="token"
VAULT_TOKEN="s.KHp99IsKk9ZaTixkEpctij72"
VAULT_K8S_JWT_PATH = ''
VAULT_K8S_ROLE = ''
VAULT_K8S_AUTH_MOUNT_POINT = ''
OMA_REST_PROXY_URL=https://kafka-rest.dev-oma.steeleye.co
OMA_ARIES_TOPIC=aries.oma.events
CONDUCTOR_API_URL=https://conductor.dev-blue.steeleye.co/api
STACK=dev-blue
AWS_PROFILE=nonprod_infra
DISABLE_REDIS=false
BENCHMARK=false
#BENCHMARK=true