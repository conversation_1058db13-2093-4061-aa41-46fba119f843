import logging
from aries_task_link.models import AriesTaskInput, AriesTaskResult
from integration_audio_comms_tasks.transcription.deepgram_feed.app_metrics_template import (
    APP_METRICS,
)
from integration_audio_comms_tasks.transcription.deepgram_feed.deepgram_feed_flow import (
    deepgram_feed_flow,
)
from integration_wrapper.integration_aries_task import IntegrationAriesTask

logger = logging.getLogger("deepgram_feed_flow")


def deepgram_feed_run(aries_task_input: AriesTaskInput) -> AriesTaskResult:
    integration = IntegrationAriesTask(
        aries_task_input=aries_task_input,
        app_metrics_template=APP_METRICS,
    )

    return integration.execute(flow=deepgram_feed_flow)
