from aries_io_event.io_param import IOParamFieldSet
from aries_io_event.task import TaskFieldSet
from aries_io_event.workflow import WorkflowFieldSet
from aries_task_link.models import AriesTaskInput
from datetime import datetime


def sample_input() -> AriesTaskInput:
    workflow = WorkflowFieldSet(
        trace_id="test_dubber_voice_transform_1",
        name="dubber_voice",
        stack="dev-blue",
        tenant="pinafore",
        start_timestamp=datetime.now(),
    )

    input_param = IOParamFieldSet(
        params=dict(
            file_uri="s3://jose.dev.steeleye.co/aries/ingress/nonstreamed/evented/dubber_voice_poll/2024/03/15/trace_id/batch_1.json",  # noqa E501
        )
    )

    task = TaskFieldSet(name="dubber_voice_transform", version="latest", success=False)

    return AriesTaskInput(workflow=workflow, input_param=input_param, task=task)
