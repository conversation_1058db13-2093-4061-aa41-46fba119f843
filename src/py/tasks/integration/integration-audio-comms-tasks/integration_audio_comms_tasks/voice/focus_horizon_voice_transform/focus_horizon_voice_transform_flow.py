# type: ignore
import logging
import numpy as np
import os
from aries_config_cached_client.tenant_workflow import CachedTenantWorkflowAPIClient
from aries_se_comms_tasks.audio_waveform.trigger_waveform_generator import (
    Params as ParamsTriggerWaveForm,
)
from aries_se_comms_tasks.audio_waveform.trigger_waveform_generator import (
    SkipIfFileNotUploaded,
    SkipIfMissingRequiredColumns,
    SkipIfSourceFrameEmpty,
    run_trigger_waveform_generator,
)
from aries_se_comms_tasks.feeds.voice.focus_horizon.static import (
    FOCUS_HORIZON_FILENAME_REGEX,
    FOCUS_HORIZON_VOICE_FLOW_NAME,
    FileUrlColumns,
)
from aries_se_comms_tasks.generic.attachment.get_attachment_metadata import (
    Params as ParamsGetAttachmentMetadata,
)
from aries_se_comms_tasks.generic.attachment.get_attachment_metadata import (
    run_get_attachment_metadata,
)
from aries_se_comms_tasks.generic.participants.link_participants import (
    Params as ParamsLinkParticipants,
)
from aries_se_comms_tasks.generic.participants.link_participants import run_link_participants
from aries_se_comms_tasks.voice.static import CallColumns, Prefixes
from aries_se_core_tasks.aries.utility_tasks.finish_flow import finish_flow
from aries_se_core_tasks.aries.utility_tasks.get_tenant_bucket import get_tenant_bucket
from aries_se_core_tasks.aries.utility_tasks.unpack_aries_task_input import unpack_aries_task_input
from aries_se_core_tasks.frame.frame_column_manipulator import (
    Params as ParamsFrameColumnManipulator,
)
from aries_se_core_tasks.frame.frame_column_manipulator import run_frame_column_manipulator
from aries_se_core_tasks.frame.frame_concatenator import Params as ParamsFrameConcatenator
from aries_se_core_tasks.frame.frame_concatenator import run_frame_concatenator
from aries_se_core_tasks.generic.generate_record_identifiers_for_df import (
    Params as ParamsGenerateRecordFileIdentifiersForDf,
)
from aries_se_core_tasks.generic.generate_record_identifiers_for_df import (
    run_generate_record_identifiers_for_df,
)
from aries_se_core_tasks.get_primary_transformations import run_get_primary_transformations
from aries_se_core_tasks.io.create_ndjson_path import create_ndjson_path
from aries_se_core_tasks.io.read.cloud.download_file import run_download_file
from aries_se_core_tasks.io.read.unzip import Params as ParamsUnzip
from aries_se_core_tasks.io.read.unzip import run_unzip
from aries_se_core_tasks.io.write.upload_file import run_upload_file
from aries_se_core_tasks.io.write.write_ndjson import run_write_ndjson
from aries_se_core_tasks.static import MetaModel
from aries_se_core_tasks.transform.dataframe.dataframe_from_cloud_file_list import (
    Params as ParamsDataFrameFromCloudFileList,
)
from aries_se_core_tasks.transform.dataframe.dataframe_from_cloud_file_list import (
    run_dataframe_from_cloud_file_list,
)
from aries_se_core_tasks.transform.extract_path.cloud_file_list_from_extract_path_result_list import (  # noqa: E501
    Params as ParamsCloudFileListFromExtractPathResultList,
)
from aries_se_core_tasks.transform.extract_path.cloud_file_list_from_extract_path_result_list import (  # noqa: E501
    run_cloud_file_list_from_extract_path_result_list,
)
from aries_se_core_tasks.transform.regex.regex_get_columns_from_groups import (
    Params as ParamsRegexGetColumnsFromGroups,
)
from aries_se_core_tasks.transform.regex.regex_get_columns_from_groups import (
    run_regex_get_columns_from_groups,
)
from aries_se_core_tasks.utilities.file_utils import delete_local_files
from aries_task_link.models import AriesTaskInput
from integration_audio_comms_tasks.voice.focus_horizon_voice_transform.input_schema import (
    FocusHorizonVoiceTransformAriesTaskInput,
)
from math import ceil
from pathlib import Path
from se_conductor_utils.task_output import DynamicTask, create_dynamic_tasks_list
from se_core_tasks.frame.frame_column_manipulator import Action
from se_core_tasks.frame.frame_concatenator import OrientEnum
from se_core_tasks.io.write.cloud.upload_file import Params as UploadFileParams
from se_data_lake.cloud_utils import (
    get_bucket,
    get_cloud_provider_from_file_uri,
    get_cloud_provider_prefix,
)
from se_data_lake.lake_path import (
    get_base_ingress_depository_lake_path_for_call_attachments,
    get_ingress_depository_lake_path_for_waveform,
)
from se_elastic_schema.models import Call
from se_enums.cloud import CloudProviderEnum
from se_enums.elastic_search import EsActionEnum
from typing import List, Optional

logger = logging.getLogger(__name__)


DEFAULT_BATCH_SIZE: int = os.environ.get("DEFAULT_BATCH_SIZE", 100)


def focus_horizon_voice_transform_flow(
    aries_task_input: AriesTaskInput,
    app_metrics_path: Optional[str] = None,
    audit_path: Optional[str] = None,
    result_path: str = "result.json",
):
    # Parse and validate AriesTaskInput parameters
    focus_horizon_voice_transform_input = unpack_aries_task_input(
        aries_task_input=aries_task_input, model=FocusHorizonVoiceTransformAriesTaskInput
    )

    # Get tenant workflow tenant config from postgres
    cached_tenant_workflow_config = CachedTenantWorkflowAPIClient.get(
        tenant_name=aries_task_input.workflow.tenant,
        workflow_name=aries_task_input.workflow.name,
        stack_name=aries_task_input.workflow.stack,
    )

    cloud_provider: CloudProviderEnum = get_cloud_provider_from_file_uri(
        file_uri=cached_tenant_workflow_config.tenant.lake_prefix
    )
    cloud_provider_prefix = get_cloud_provider_prefix(value=cloud_provider)

    streamed: bool = cached_tenant_workflow_config.workflow.streamed

    # Get all the dynamic task definitions from workflow input
    # in this case it is required, "call" and "waveform"
    call_output_dynamic_task_input: DynamicTask = (
        focus_horizon_voice_transform_input.dynamic_tasks.get("call")
    )

    waveform_output_dynamic_task_input: DynamicTask = (
        focus_horizon_voice_transform_input.dynamic_tasks.get("waveform")
    )

    # Determine the Cloud Bucket of the tenant
    tenant_bucket_with_cloud_prefix = get_tenant_bucket(
        task_input=focus_horizon_voice_transform_input, cloud_provider_prefix=cloud_provider_prefix
    )

    # Get realm from input file path
    realm: str = get_bucket(file_uri=focus_horizon_voice_transform_input.file_uri)
    tenant: str = aries_task_input.workflow.tenant

    # Download remote file from Cloud
    local_file_path: str = run_download_file(
        file_url=focus_horizon_voice_transform_input.file_uri,
    )

    # Unzip the zip file containing mp3 files
    recording_file_paths = run_unzip(
        extract_result=local_file_path, params=ParamsUnzip(multi_file=True)
    )

    # Create list of file Objects from ExtractPathResult List
    attachments_path = get_base_ingress_depository_lake_path_for_call_attachments(
        workflow_name=aries_task_input.workflow.name,
    ).lstrip("/")

    # Add a 'recordings' folder inside the attachments_path. The reason for this is
    # that we need to have a predictable path to use in the Regex to extract fields
    # from the file name.
    # Without recordings, the path looks something like this :
    # saries/ingress/depository/focus_horizon_voice/attachments/2023/06/22/
    # <TraceID>/<SHA1Hash>/, where traceID and Sha1 hash are dynamic
    # Appending recordings/<file name> to this makes the folder before the file name
    # predictable so it can be used in the regex
    recordings_path = f"{Path(attachments_path).joinpath('recordings').as_posix()}/"

    s3_recordings_list = run_cloud_file_list_from_extract_path_result_list(
        extract_result_list=recording_file_paths,
        realm=realm,
        params=ParamsCloudFileListFromExtractPathResultList(
            key_prefix=recordings_path, file_suffix=".mp3"
        ),
    )
    # Upload mp3 files to S3
    # run_upload_file(upload_target=)
    run_upload_file(
        upload_target=s3_recordings_list, cloud_provider=cloud_provider, params=UploadFileParams()
    )

    total_files: int = len(s3_recordings_list)
    total_batches = ceil(total_files / DEFAULT_BATCH_SIZE)

    ndjson_paths: List[str] = []
    waveform_paths: List[str] = []

    # This is sorted to get consistent result every time it runs
    s3_recordings_list = sorted(s3_recordings_list, key=lambda s3_file: s3_file.file_path)

    # Prevent loading an unknown amount of files at once,
    # by chunking the s3 recording files list
    for idx, recordings_list in enumerate(np.array_split(s3_recordings_list, total_batches)):
        recordings_list: List = list(recordings_list)

        # Get a dataframe containing a file url column from the S3FileList
        dataframe_from_s3_file_list = run_dataframe_from_cloud_file_list(
            cloud_file_list=recordings_list,
            params=ParamsDataFrameFromCloudFileList(
                output_file_url_column=FileUrlColumns.RECORDING_FILE_URL,
                output_local_filepath_column=FileUrlColumns.LOCAL_FILEPATH,
            ),
            cloud_provider_prefix=cloud_provider_prefix,
        )

        # Get the Attachment head object details from the recording files
        attachment_head_object_df = run_get_attachment_metadata(
            source_frame=dataframe_from_s3_file_list,
            cloud_provider=cloud_provider,
            params=ParamsGetAttachmentMetadata(attachment_column=FileUrlColumns.RECORDING_FILE_URL),
        )

        # Add the 'voiceFile' prefix to the attachment columns
        attachment_with_prefix_added_df = run_frame_column_manipulator(
            source_frame=attachment_head_object_df,
            params=ParamsFrameColumnManipulator(action=Action.add, prefix=Prefixes.VOICE_FILE),
        )

        # Extract call metadata from the filename
        call_metadata = run_regex_get_columns_from_groups(
            source_frame=dataframe_from_s3_file_list,
            params=ParamsRegexGetColumnsFromGroups(
                file_url_column=FileUrlColumns.RECORDING_FILE_URL,
                target_source_key_column=FileUrlColumns.SOURCE_KEY_COLUMN,
                pattern=FOCUS_HORIZON_FILENAME_REGEX,
            ),
        )

        # Concatenate metadata df with the df containing the path to the local
        # file, which will be used to get the call duration in the mappings task
        concatenated_mappings_source_frame = run_frame_concatenator(
            call_metadata_df=call_metadata,
            attachment_df=dataframe_from_s3_file_list,
            params=ParamsFrameConcatenator(orient=OrientEnum.horizontal),
        )

        focus_horizon_mappings_df = run_get_primary_transformations(
            source_frame=concatenated_mappings_source_frame,
            flow=FOCUS_HORIZON_VOICE_FLOW_NAME,
            realm=realm,
            tenant=tenant,
            file_url=focus_horizon_voice_transform_input.file_uri,
        )

        # Concatenate voice and attachment data
        concatenated_call_frame = run_frame_concatenator(
            focus_horizon_mappings_df=focus_horizon_mappings_df,
            attachment_with_prefix_added_df=attachment_with_prefix_added_df,
            params=ParamsFrameConcatenator(orient=OrientEnum.horizontal),
        )

        # Get the amp ID, this is used for the audit keys
        call_frame_with_amp_id = run_generate_record_identifiers_for_df(
            source_frame=concatenated_call_frame,
            params=ParamsGenerateRecordFileIdentifiersForDf(
                data_model=MetaModel.CALL, target_record_identifier_col="record_identifier"
            ),
            streamed=streamed,
            cloud_provider=cloud_provider,
        )

        # Enrichment: Link participants based on the participant identifiers
        participants_df = run_link_participants(
            source_frame=call_frame_with_amp_id,
            tenant=tenant,
            params=ParamsLinkParticipants(target_participants_column=CallColumns.PARTICIPANTS),
            streamed=streamed,
            app_metrics_path=app_metrics_path,
            audit_path=audit_path,
            data_models=[Call],
            record_identifier_column="record_identifier",
        )

        # Concatenate the participants' data with the entire call and attachment data
        final_df = run_frame_concatenator(
            call_and_attachment_df=concatenated_call_frame,
            participants_df=participants_df,
            params=ParamsFrameConcatenator(orient=OrientEnum.horizontal),
        )

        # Create the appropriate path where the ndjson result is to be uploaded
        ndjson_path = create_ndjson_path(
            tenant_bucket=tenant_bucket_with_cloud_prefix,
            aries_task_input=aries_task_input,
            model=MetaModel.CALL,
            suffix=idx,
        )

        # Write the transformed_df data frame into a ndjson file to the generated ndjson path
        run_write_ndjson(
            source_serializer_result=final_df,
            output_filepath=ndjson_path,
            audit_output=True,
            app_metrics_path=app_metrics_path,
            audit_path=audit_path,
        )

        ndjson_paths.append(ndjson_path)

        waveform_prefix = get_ingress_depository_lake_path_for_waveform(
            workflow_name=aries_task_input.workflow.name,
            workflow_start_timestamp=aries_task_input.workflow.start_timestamp,
            workflow_trace_id=aries_task_input.workflow.trace_id,
            task_io_params=aries_task_input.input_param.params,
        )

        try:
            waveform_path = run_trigger_waveform_generator(
                source_frame=final_df,
                realm=realm,
                waveform_path=waveform_prefix,
                params=ParamsTriggerWaveForm(
                    source_feed=FOCUS_HORIZON_VOICE_FLOW_NAME,
                    cloud_provider_prefix=cloud_provider_prefix,
                ),
            )
        except (SkipIfMissingRequiredColumns, SkipIfSourceFrameEmpty, SkipIfFileNotUploaded) as e:
            # Reset waveform_path to None
            logger.warning(
                "Setting waveform path to None for cases where there was an error"
                f"in trigger waveform. Exception: {e}"
            )
            waveform_path = None

        if waveform_path:
            waveform_paths.append(waveform_path)

    # Delete all temp recording files from the container

    temp_recording_paths = [extract_path.path for extract_path in recording_file_paths]
    delete_local_files(files_to_delete=temp_recording_paths)

    call_output = create_dynamic_tasks_list(
        lst=[{"file_uri": ndjson_path} for ndjson_path in ndjson_paths],
        task=call_output_dynamic_task_input,
        common_input_parameters={
            "es_action": EsActionEnum.INDEX.value,
            "data_model": Call.get_reference().get_qualified_reference(),
        },
        workflow=aries_task_input.workflow,
    )

    waveform_output = create_dynamic_tasks_list(
        lst=[{"file_uri": waveform_path} for waveform_path in waveform_paths],
        task=waveform_output_dynamic_task_input,
        common_input_parameters={},
        workflow=aries_task_input.workflow,
    )

    finish_flow(
        result_path=result_path,
        result_data={
            MetaModel.CALL: call_output,
            MetaModel.WAVEFORM: waveform_output,
        },
    )
