TASK_NAME=steeleye_universal_voice_transform
DEBUG=1
OMA_REST_PROXY_URL=https://kafka-rest.dev-oma.steeleye.co
OMA_ARIES_TOPIC=aries.oma.events
AWS_PROFILE=nonprod_infra
BATCH_SIZE=100
BENCHMARK=false
#BENCHMARK=true

# AWS
DATA_PLATFORM_CONFIG_API_URL=https://config-api.nonprod-eu-ie-1.steeleye.co/
CONDUCTOR_API_URL=https://conductor.uat-shared-steeleye.steeleye.co/api
ELASTIC_HOST=elasticsearch.uat-shared-steeleye.steeleye.co
ELASTIC_PORT=443
ELASTIC_SCHEME=https
ELASTIC_API_KEY=foobar
TASK_WORKER_DOMAIN=uat-shared-steeleye
STACK=uat-shared-steeleye
FFMPEG_BIN_PATH=/usr/local/bin/ffmpeg

# AZURE
# DATA_PLATFORM_CONFIG_API_URL=https://config-api.nonprod-uk-so-1.steeleye.io/
# CONDUCTOR_API_URL=https://conductor.dev-shared-1.steeleye.io/api
# ELASTIC_HOST=elasticsearch.dev-shared-1.steeleye.io
# ELASTIC_PORT=443
# ELASTIC_SCHEME=https
# ELASTIC_API_KEY=
# TASK_WORKER_DOMAIN=dev-shared-1
# STACK=dev-shared-1