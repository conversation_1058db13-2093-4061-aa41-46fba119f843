import nanoid
from aries_io_event.io_param import IOParamFieldSet
from aries_io_event.task import TaskFieldSet
from aries_io_event.workflow import WorkflowFieldSet
from aries_task_link.models import AriesTaskInput
from datetime import datetime


def sample_input() -> AriesTaskInput:
    workflow = WorkflowFieldSet(
        trace_id=nanoid.generate(),
        name="steeleye_universal_voice",
        stack="uat-shared-steeleye",
        tenant="dom",
        start_timestamp=datetime.now(),
    )

    input_param = IOParamFieldSet(
        params=dict(
            file_uri="s3://dom.uat.steeleye.co/aries/ingress/nonstreamed/evented/steeleye_universal_voice/Cantonese171024.zip",
            dynamic_tasks=dict(
                calls_to_transcribe=dict(
                    name="comms_voice_sub_workflow",
                    task_reference_name="steeleye_universal_voice_sub_workflow_ref",
                    type="SUB_WORKFLOW",
                    input_parameters={
                        "is_lexica_enabled": True,
                    },
                ),
                calls_with_transcript=dict(
                    name="comms_voice_with_transcript_subworkflow",
                    task_reference_name="steeleye_universal_voice_sub_workflow_ref2",
                    type="SUB_WORKFLOW",
                ),
                calls_without_transcript=dict(
                    name="elastic_ingestion",
                    task_reference_name="steeleye_universal_voice_sub_workflow_ref3",
                    type="SUB_WORKFLOW",
                ),
                waveform=dict(
                    name="waveform_transform",
                    task_reference_name="waveform_transform_ref",
                    type="SIMPLE",
                ),
            ),
        )
    )

    task = TaskFieldSet(name="steeleye_universal_voice_transform", version="latest", success=False)

    return AriesTaskInput(workflow=workflow, input_param=input_param, task=task)
