import logging
from aries_task_link.models import AriesTaskInput, Aries<PERSON>askResult
from integration_audio_comms_tasks.voice.the_comms_guys_voice_transform.app_metrics_template import (  # noqa E510
    APP_METRICS,
)
from integration_audio_comms_tasks.voice.three_cx_voice_transform.three_cx_voice_transform_flow import (  # noqa E501
    three_cx_voice_transform_flow,
)
from integration_wrapper.integration_aries_task import IntegrationAriesTask

logger = logging.getLogger("three_cx_voice_transform")


def three_cx_voice_transform_run(aries_task_input: AriesTaskInput) -> AriesTaskResult:
    integration = IntegrationAriesTask(
        aries_task_input=aries_task_input,
        app_metrics_template=APP_METRICS,
    )

    return integration.execute(flow=three_cx_voice_transform_flow)
