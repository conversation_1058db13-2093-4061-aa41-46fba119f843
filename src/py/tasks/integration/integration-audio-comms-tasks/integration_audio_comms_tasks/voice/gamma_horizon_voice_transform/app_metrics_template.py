from aries_se_comms_tasks.comms_app_metrics_enum import CommsAppMetricsEnum
from aries_se_comms_tasks.feeds.voice.app_metrics_enum import VoiceTransformAppMetricsEnum
from aries_se_core_tasks.core.generic_app_metrics_enum import GenericAppMetricsEnum
from integration_wrapper.app_metrics_enum import IntegrationAriesTaskAppMetricsEnum

METRICS_ENUMS = [
    VoiceTransformAppMetricsEnum,
    GenericAppMetricsEnum,
    CommsAppMetricsEnum,
    IntegrationAriesTaskAppMetricsEnum,
]

METRICS_LIST = [val for sub_list in [_enum.list() for _enum in METRICS_ENUMS] for val in sub_list]
APP_METRICS = {v: 0 for v in METRICS_LIST}
