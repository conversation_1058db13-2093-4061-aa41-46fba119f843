from aries_io_event.io_param import IOParamFieldSet
from aries_io_event.task import TaskFieldSet
from aries_io_event.workflow import WorkflowFieldSet
from aries_task_link.models import AriesTaskInput
from datetime import datetime


def sample_input() -> AriesTaskInput:
    workflow = WorkflowFieldSet(
        trace_id="test_gamma_horizon_voice_transform2",
        name="gamma_horizon_voice",
        stack="dev-blue",
        tenant="pinafore",
        start_timestamp=datetime.now(),
    )
    input_param = IOParamFieldSet(
        params=dict(
            file_uri="s3://pinafore.dev.steeleye.co/aries/ingress/nonstreamed/polled/gamma_horizon_voice_poll/2024/04/02/call-recordings-2024-02-20-to-2024-02-21.zip",
        )
    )
    task = TaskFieldSet(name="gamma_horizon_voice_transform", version="latest", success=False)
    return AriesTaskInput(workflow=workflow, input_param=input_param, task=task)
