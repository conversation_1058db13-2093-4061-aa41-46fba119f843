TASK_NAME=avaya_voice_transform
DEBUG=1
OMA_REST_PROXY_URL=https://kafka-rest.dev-oma.steeleye.co
OMA_ARIES_TOPIC=aries.oma.events
AWS_PROFILE=nonprod_infra
BENCHMARK=false
#BENCHMARK=true

# AWS
DATA_PLATFORM_CONFIG_API_URL=https://config-api.nonprod-eu-ie-1.steeleye.co/
CONDUCTOR_API_URL=https://conductor.dev-shared-2.steeleye.co/api
ELASTIC_HOST=elasticsearch.dev-shared-2.steeleye.co
ELASTIC_PORT=443
ELASTIC_SCHEME=https
ELASTIC_API_KEY=
TASK_WORKER_DOMAIN=dev-shared-2
STACK=dev-shared-2

# AZURE
# DATA_PLATFORM_CONFIG_API_URL=https://config-api.nonprod-uk-so-1.steeleye.io/
# CONDUCTOR_API_URL=https://conductor.dev-shared-1.steeleye.io/api
# ELASTIC_HOST=elasticsearch.dev-shared-1.steeleye.io
# ELASTIC_PORT=443
# ELASTIC_SCHEME=https
# ELASTIC_API_KEY=
# TASK_WORKER_DOMAIN=dev-shared-1
# STACK=dev-shared-1