from aries_task_link.models import AriesTaskInput, AriesTaskResult
from integration_audio_comms_tasks.voice.cloud9_voice_transform.app_metrics_template import (
    APP_METRICS,
)
from integration_audio_comms_tasks.voice.cloud9_voice_transform.cloud9_voice_transform_flow import (
    Cloud9VoiceTransformFlow,
    cloud9_voice_transform_flow,
)
from integration_audio_comms_tasks.voice.cloud9_voice_transform.flow_overrides.arrow import (
    ArrowCloud9VoiceTransformFlow,
)
from integration_audio_comms_tasks.voice.cloud9_voice_transform.flow_overrides.evolutionmarkets import (  # noqa E501
    EvolutionMarketsCloud9VoiceTransformFlow,
)
from integration_wrapper.integration_aries_task import IntegrationAriesTask


def cloud9_voice_transform_run(aries_task_input: AriesTaskInput) -> AriesTaskResult:
    integration = IntegrationAriesTask(
        aries_task_input=aries_task_input,
        app_metrics_template=APP_METRICS,
    )
    flow_overrides_map = {
        "default": Cloud9VoiceTransformFlow,
        "arrow": ArrowCloud9VoiceTransformFlow,
        "evolutionmarkets": EvolutionMarketsCloud9VoiceTransformFlow,
        "ben": EvolutionMarketsCloud9VoiceTransformFlow,
    }

    tenant = aries_task_input.workflow.tenant
    flow_override_class = flow_overrides_map.get(tenant, flow_overrides_map["default"])

    return integration.execute(
        flow=cloud9_voice_transform_flow, flow_override_class=flow_override_class
    )
