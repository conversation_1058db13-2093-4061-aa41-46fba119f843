import logging
import pandas as pd
import shutil
from aries_se_comms_tasks.feeds.voice.cloud9_voice.static import (
    Cloud9VoiceSourceColumns,
    Cloud9VoiceTempColumns,
)
from aries_se_comms_tasks.recording.extract_segment_from_recording import (
    InvalidDurationException,
    InvalidOffsetException,
    run_extract_segment_from_recording,
)
from aries_se_core_tasks.core.core_dataclasses import CloudAction, CloudFile
from aries_se_core_tasks.io.write.upload_file import (  # type: ignore[attr-defined] # noqa: E501
    Params as UploadFileParams,
)
from aries_se_core_tasks.io.write.upload_file import run_upload_file
from aries_se_core_tasks.utilities.serializer import serializer
from pathlib import Path
from se_core_tasks.utils.tempfile_utils import tmp_directory
from se_data_lake.cloud_utils import get_file_uri, get_key
from se_enums.cloud import CloudProviderEnum
from typing import Optional

logger = logging.getLogger(__name__)


def extract_and_upload_call_segment(
    bucket: str,
    cloud_provider: CloudProviderEnum,
    row: pd.Series,
    tmp_dir: Path,
) -> pd.Series:
    key = row[Cloud9VoiceSourceColumns.STEELEYE_META_ATTACHMENT_PATH]

    if pd.isna(key):
        return row

    audio_file_uri: str = get_file_uri(
        bucket=bucket,
        key=key,
        cloud_provider=cloud_provider,
    )

    duration = float(
        row[Cloud9VoiceSourceColumns.DURATION]
        if not pd.isna(row[Cloud9VoiceSourceColumns.DURATION])
        else 0
    )

    offset_is_na = pd.isna(row[Cloud9VoiceSourceColumns.RECORDING_FILE_RECORDING_FILE_OFFSET])

    if offset_is_na and not pd.isna(key):
        # In case of for example "DIALTONE" calls, the offset is not
        # provided ("null") in the source data it will simply point
        # to the original audio file
        row[Cloud9VoiceTempColumns.RECORDING_URI] = get_file_uri(
            bucket=bucket,
            key=key,
            cloud_provider=cloud_provider,
        )

        return row

    offset = float(
        row[Cloud9VoiceSourceColumns.RECORDING_FILE_RECORDING_FILE_OFFSET]
        if not offset_is_na
        else 0
    )

    audio_segment_local_path: Optional[Path] = None

    try:
        audio_segment_local_path = run_extract_segment_from_recording(
            path=audio_file_uri,
            duration=duration,
            offset=offset,
            tmp_dir=tmp_dir,
        )
    except (InvalidOffsetException, InvalidDurationException) as e:
        logger.warning(
            f"Skipping audio extraction for file: '{row[Cloud9VoiceSourceColumns.RECORDING_FILE_NAME]}' "  # noqa: E501
            f"with `uniqueCallID`: '{row[Cloud9VoiceSourceColumns.UNIQUE_CALL_ID]}' due to error: {e}"  # noqa: E501
        )

    if audio_segment_local_path:
        original_cloud_key: str = get_key(file_uri=key)
        original_cloud_key = original_cloud_key.replace(Path(original_cloud_key).name, "").strip(
            "/"
        )

        new_cloud_key: str = f"{original_cloud_key}/segments/{audio_segment_local_path.name}"

        row[Cloud9VoiceTempColumns.RECORDING_URI] = get_file_uri(
            bucket=bucket,
            key=new_cloud_key,
            cloud_provider=cloud_provider,
        )

        run_upload_file(
            upload_target=CloudFile(
                bucket_name=bucket,
                key_name=new_cloud_key,
                action=CloudAction.UPLOAD,
                file_path=audio_segment_local_path,
            ),
            cloud_provider=cloud_provider,
            params=UploadFileParams(),
        )

    return row


@serializer
def extract_and_upload_calls(
    df: pd.DataFrame, bucket: str, cloud_provider: CloudProviderEnum
) -> pd.DataFrame:
    """Extracts audio segments from the original audio files (when offset is
    provided, otherwise it will simply point to the original audio file) and
    uploads the extracted segments to the cloud storage.

    :param df: DataFrame containing the source data
    :param bucket: The bucket name where the audio segments will be uploaded
    :param cloud_provider: The cloud provider where the audio segments will be uploaded
    :return: DataFrame with the updated `RECORDING_URI` column
    """
    tmp_dir: Path = tmp_directory()

    df.loc[:, Cloud9VoiceTempColumns.RECORDING_URI] = pd.NA

    df = df.apply(
        lambda row: extract_and_upload_call_segment(
            bucket=bucket,
            cloud_provider=cloud_provider,
            row=row,
            tmp_dir=tmp_dir,
        ),
        axis=1,
    )

    shutil.rmtree(tmp_dir)

    return df
