DATA_PLATFORM_CONFIG_API_URL=https://config-api.nonprod-eu-ie-1.steeleye.co
ELASTIC_HOST=elasticsearch.uat-shared-steeleye.steeleye.co
ELASTIC_PORT=443
ELASTIC_API_KEY=
ELASTIC_SCHEME=https
TASK_NAME=bp_twilio_voice_transform
TASK_WORKER_DOMAIN=uat-shared-steeleye
DEBUG=1
OMA_REST_PROXY_URL=https://kafka-rest.dev-oma.steeleye.co
OMA_ARIES_TOPIC=aries.oma.events
CONDUCTOR_API_URL=https://conductor.dev-enterprise.steeleye.co/api
STACK=uat-shared-steeleye
AWS_PROFILE=nonprod_infra
BENCHMARK=false
#BENCHMARK=true