# mypy: disable-error-code="attr-defined"
import logging
import pandas as pd
from aries_config_cached_client.tenant_workflow import CachedTenantWorkflowAPIClient
from aries_se_comms_tasks.audio_waveform.trigger_waveform_generator import (
    Params as TriggerWaveFormGeneratorParams,
)
from aries_se_comms_tasks.audio_waveform.trigger_waveform_generator import (
    SkipIfFileNotUploaded,
    SkipIfMissingRequiredColumns,
    SkipIfSourceFrameEmpty,
    run_trigger_waveform_generator,
)
from aries_se_comms_tasks.feeds.voice.voip_voice.static import FLOW_NAME, SourceColumns
from aries_se_comms_tasks.generic.attachment.get_attachment_metadata import (
    Params as GetAttachmentMetadataParams,
)
from aries_se_comms_tasks.generic.attachment.get_attachment_metadata import (
    run_get_attachment_metadata,
)
from aries_se_comms_tasks.voice.generic.has_attachment import run_has_attachment
from aries_se_comms_tasks.voice.static import Prefixes
from aries_se_core_tasks.aries.utility_tasks.finish_flow import add_nested_params, finish_flow
from aries_se_core_tasks.aries.utility_tasks.get_tenant_bucket import get_tenant_bucket
from aries_se_core_tasks.aries.utility_tasks.unpack_aries_task_input import unpack_aries_task_input
from aries_se_core_tasks.frame.frame_column_manipulator import (
    Params as FrameColumnManipulatorParams,
)
from aries_se_core_tasks.frame.frame_column_manipulator import run_frame_column_manipulator
from aries_se_core_tasks.frame.frame_concatenator import Params as FrameConcatenatorParams
from aries_se_core_tasks.frame.frame_concatenator import run_frame_concatenator
from aries_se_core_tasks.get_primary_transformations import run_get_primary_transformations
from aries_se_core_tasks.io.create_ndjson_path import create_ndjson_path
from aries_se_core_tasks.io.read.cloud.download_file import run_download_file
from aries_se_core_tasks.io.read.json_file_to_dataframe_converter import (
    Params as JsonFileToDataframeParams,
)
from aries_se_core_tasks.io.read.json_file_to_dataframe_converter import (
    run_json_file_to_dataframe_converter,
)
from aries_se_core_tasks.io.write.write_ndjson import run_write_ndjson
from aries_se_core_tasks.static import MetaModel
from aries_se_core_tasks.utilities.frame_manipulation import add_missing_columns
from aries_se_core_tasks.utilities.serializer import serializer
from aries_task_link.models import AriesTaskInput
from integration_audio_comms_tasks.voice.voip_voice_transform.input_schema import (
    VoipVoiceTransformAriesTaskInput,
)
from pathlib import Path
from se_core_tasks.frame.frame_column_manipulator import Action
from se_core_tasks.frame.frame_concatenator import OrientEnum
from se_data_lake.cloud_utils import (
    get_bucket,
    get_cloud_provider_from_file_uri,
    get_cloud_provider_prefix,
    get_file_uri,
)
from se_data_lake.lake_path import get_ingress_depository_lake_path_for_waveform
from se_elastic_schema.models import Call
from se_enums.cloud import CloudProviderEnum
from se_enums.elastic_search import EsActionEnum
from typing import List, Optional

logger = logging.getLogger(__name__)


def voip_voice_transform_flow(
    aries_task_input: AriesTaskInput,
    app_metrics_path: Optional[str] = None,
    audit_path: Optional[str] = None,
    result_path: str = "result.json",
):
    """
    -------------------------------------------------------------
    ||                   VoipVoiceTransform                    ||
    -------------------------------------------------------------
    """
    # Parse and validate AriesTaskInput parameters
    task_transform_input: VoipVoiceTransformAriesTaskInput = unpack_aries_task_input(
        aries_task_input=aries_task_input, model=VoipVoiceTransformAriesTaskInput
    )

    # Get realm from input file path
    realm: str = get_bucket(file_uri=task_transform_input.file_uri)
    tenant: str = aries_task_input.workflow.tenant

    # Get tenant workflow tenant config from postgres
    cached_tenant_workflow_config = CachedTenantWorkflowAPIClient.get(
        stack_name=aries_task_input.workflow.stack,
        tenant_name=aries_task_input.workflow.tenant,
        workflow_name=aries_task_input.workflow.name,
    )

    cloud_provider: CloudProviderEnum = get_cloud_provider_from_file_uri(
        file_uri=cached_tenant_workflow_config.tenant.lake_prefix
    )
    streamed: bool = cached_tenant_workflow_config.workflow.streamed

    # Determine the Cloud Bucket of the tenant
    cloud_provider_prefix: str = get_cloud_provider_prefix(value=cloud_provider)
    cloud_bucket_with_prefix: str = get_tenant_bucket(
        task_input=task_transform_input,
        cloud_provider_prefix=cloud_provider_prefix,
    )

    local_file_path: str = run_download_file(file_url=task_transform_input.file_uri)

    # Extract metadata from json file
    input_data = run_json_file_to_dataframe_converter(
        path=Path(local_file_path),
        params=JsonFileToDataframeParams(
            normalize=True,
        ),
    )

    # Will store the generated output paths
    ndjson_path: Optional[str] = None
    waveform_path: Optional[str] = None

    if input_data.shape[0] > 0:
        selected_input_data = select_input_data(
            df=input_data, cloud_provider=cloud_provider, bucket=realm
        )

        primary_mappings_result = run_get_primary_transformations(
            source_frame=selected_input_data,
            flow=FLOW_NAME,
            realm=realm,
            tenant=tenant,
            source_file_uri=task_transform_input.file_uri,
            cloud_provider=cloud_provider,
            streamed=streamed,
        )

        # Get the Attachment head object details from the attachment url
        attachment_head_object_result = run_get_attachment_metadata(
            source_frame=selected_input_data,
            params=GetAttachmentMetadataParams(attachment_column=SourceColumns.ATTACHMENT_URL),
            cloud_provider=cloud_provider,
        )

        # Add the 'voiceFile' prefix to the attachment columns
        attachment_with_voice_file_prefix_result = run_frame_column_manipulator(
            source_frame=attachment_head_object_result,
            params=FrameColumnManipulatorParams(action=Action.add, prefix=Prefixes.VOICE_FILE),
        )

        # Concatenate voice and attachment data
        concatenated_calls_result = run_frame_concatenator(
            metadata_df=primary_mappings_result,
            attachment_df=attachment_with_voice_file_prefix_result,
            params=FrameConcatenatorParams(orient=OrientEnum.horizontal),
        )

        # Populate HasAttachment and connected based on whether an attachment was fetched or not
        final_result = run_has_attachment(source_frame=concatenated_calls_result)

        final_records_length: int = final_result.shape[0]

        if final_records_length > 0:
            # Create the appropriate path where the ndjson result is to be uploaded
            ndjson_path = create_ndjson_path(
                tenant_bucket=cloud_bucket_with_prefix,
                aries_task_input=aries_task_input,
                model=MetaModel.CALL,
            )

            # Write the transformed_df data frame into a ndjson file to the generated ndjson path
            run_write_ndjson(
                source_serializer_result=final_result,  # type: ignore
                output_filepath=ndjson_path,
                audit_output=True,
                app_metrics_path=app_metrics_path,
                audit_path=audit_path,
            )

            waveform_prefix = get_ingress_depository_lake_path_for_waveform(
                workflow_name=aries_task_input.workflow.name,
                workflow_start_timestamp=aries_task_input.workflow.start_timestamp,
                workflow_trace_id=aries_task_input.workflow.trace_id,
                task_io_params=aries_task_input.input_param.params,
            )

            try:
                waveform_path = run_trigger_waveform_generator(
                    source_frame=final_result,
                    realm=realm,
                    waveform_path=waveform_prefix,
                    params=TriggerWaveFormGeneratorParams(
                        source_feed=FLOW_NAME,
                        cloud_provider_prefix=cloud_provider_prefix,
                    ),
                )
            except (
                SkipIfMissingRequiredColumns,
                SkipIfSourceFrameEmpty,
                SkipIfFileNotUploaded,
            ) as e:
                # Reset waveform_path to None
                logger.warning(
                    "Setting waveform path to None for cases where there was an error"
                    f"in trigger waveform. Exception: {e}"
                )

    call_output = (
        add_nested_params(
            file_uri=ndjson_path,
            es_action=EsActionEnum.INDEX.value,
            data_model=Call.get_reference().get_qualified_reference(),
        )
        if ndjson_path
        else None
    )

    waveform_output = (
        add_nested_params(
            file_uri=waveform_path,
        )
        if waveform_path
        else None
    )

    finish_flow(
        result_path=result_path,
        result_data={
            MetaModel.CALL: call_output,
            MetaModel.WAVEFORM: waveform_output,
        },
    )


@serializer
def select_input_data(df: pd.DataFrame, cloud_provider: CloudProviderEnum, bucket: str):
    """Add missing required columns, and remove all other that are not
    necessary.

    :param df:
    :param cloud_provider: cloud provider
    :param bucket: bucket
    :return: dataframe with only required columns
    """
    required_columns: List[str] = SourceColumns.all()

    df = add_missing_columns(dataframe=df, columns=required_columns)

    # Get the complete attachment cloud URI
    df[SourceColumns.ATTACHMENT_URL] = df[SourceColumns.ATTACHMENT_URL].apply(
        lambda key: (
            (get_file_uri(cloud_provider=cloud_provider, bucket=bucket, key=key))
            if not pd.isna(key)
            else key
        )
    )

    return df[required_columns]
