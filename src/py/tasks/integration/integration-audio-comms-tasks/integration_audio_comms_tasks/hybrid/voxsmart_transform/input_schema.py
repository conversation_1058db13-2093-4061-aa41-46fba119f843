from integration_wrapper.integration_aries_task_input import IntegrationAriesTaskInput
from pydantic import Field
from se_conductor_utils.task_output import DynamicTask
from typing import Dict


class VoxsmartTransformAriesTaskInput(IntegrationAriesTaskInput):
    dynamic_tasks: Dict[str, DynamicTask] = Field(
        ...,
        description=(
            "Dict containing the definition for messages dynamic tasks."
            "e.g. input: { 'message': `DynamicTask` }"
        ),
    )
