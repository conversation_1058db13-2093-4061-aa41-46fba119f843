import boto3
import pandas as pd
from addict import addict
from aries_config_cached_client.tenant_workflow import CachedTenantWorkflowAPIClient
from aries_se_comms_tasks.generic.participants import link_participants
from aries_se_core_tasks.core.generic_app_metrics_enum import GenericAppMetricsEnum
from aries_se_core_tasks.io.read.cloud.download_file import run_download_file
from aries_se_core_tasks.utilities.helpers_for_tests import (  # type: ignore[attr-defined]  # noqa: E501
    sort_identifiers_columns,
)
from aries_task_link.models import AriesTaskInput
from freezegun import freeze_time
from integration_audio_comms_tasks.meeting.outlook_calendar_meeting_transform.outlook_calendar_meeting_transform_task import (  # noqa: E501
    outlook_calendar_meeting_transform_run,
)
from integration_test_utils.aws_helpers.patch_response import (
    mock_aiobotocore_convert_to_response_dict,
)
from integration_wrapper.static import StaticFields
from moto import mock_aws
from pathlib import Path
from se_elastic_schema.models import Meeting
from se_elasticsearch.repository.models import ResourceConfig
from se_elasticsearch.repository.static import MetaPrefix
from se_enums.elastic_search import EsActionEnum
from typing import List
from unittest.mock import patch

BUCKET_NAME: str = "test.dev.steeleye.co"

CURRENT_PATH = Path(__file__).parent
DATA_PATH = Path(__file__).parent.joinpath("data")

LOCAL_BUCKET_PATH = DATA_PATH.joinpath("buckets", BUCKET_NAME)
EXPECTED_RESULTS_PATH = DATA_PATH.joinpath("expected_results")

EXPECTED_OUTPUT_NDJSON_PATH = EXPECTED_RESULTS_PATH.joinpath("expected_output.ndjson")


mock_aiobotocore_convert_to_response_dict()


class TestOutlookCalendarMeetingTransform:
    """Test suite for Zoom Meeting Transform."""

    @mock_aws
    @freeze_time(time_to_freeze="2024-03-06 06:59:38.911459+00:00")
    def test_it_can_run_end_to_end(
        self,
        sample_aries_task_input: AriesTaskInput,
        link_participants_scroll_result: List[dict],
    ):
        # Create mock S3 bucket and objects to it
        create_and_add_objects_to_s3_bucket(bucket_name=BUCKET_NAME)

        aries_task_result = self._run_aries_task(  # type: ignore
            sample_aries_task_input=sample_aries_task_input,
            link_participants_scroll_result=link_participants_scroll_result,  # type: ignore
        )

        meeting_ndjson_s3_uri: str = (
            aries_task_result.output_param.params.get("Meeting").get("params").get("file_uri")
        )

        meeting_local_file_path: str = run_download_file(
            file_url=meeting_ndjson_s3_uri,
        )

        assert_result_is_expected(
            result_path=meeting_local_file_path,
            expected_path=EXPECTED_OUTPUT_NDJSON_PATH,
            sort_identifiers=True,
            columns_to_drop=[
                "sourceIndex",
                "body",
            ],  # Body is being dropped as html2text is giving different results
            sort_by="meetingUniqueId",
        )

        metrics = (
            aries_task_result.app_metric.metrics[StaticFields.DATA_INTEGRATION_METRICS_PREFIX][
                "outlook_calendar_meeting"
            ]["outlook_calendar_meetings_transform"]
            if aries_task_result.app_metric
            else {}
        )

        assert metrics[GenericAppMetricsEnum.OUTPUT_COUNT] == 5

        assert aries_task_result.output_param.params == {
            "Meeting": {
                "params": {
                    "file_uri": meeting_ndjson_s3_uri,
                    "es_action": EsActionEnum.INDEX.value,
                    "data_model": Meeting.get_reference().get_qualified_reference(),
                    "ignore_empty_file_uri": True,
                }
            },
        }

    @staticmethod
    @patch.object(link_participants, "get_repository_by_cluster_version")
    @patch.object(link_participants, "get_es_config")
    @patch.object(
        target=CachedTenantWorkflowAPIClient,
        attribute="get",
        return_value=addict.Dict(
            {
                "tenant": {
                    "lake_prefix": f"s3://{BUCKET_NAME}",
                },
                "workflow": {"streamed": False},
            },
        ),
    )
    def _run_aries_task(
        _mock_cached_tenant_workflow_api_client,
        mock_link_participants_get_es_config,
        mock_link_participants_elasticsearch_repository,
        link_participants_scroll_result: dict,
        sample_aries_task_input: AriesTaskInput,
    ):
        """Runs the flow after uploading the input files to mock S3 and mocking
        functions which read from Elasticsearch.

        :param mock_link_participants_get_es_config: Unittest patch object
        :param mock_link_participants_elasticsearch_repository: Unittest patch object
        :param link_participants_scroll_result: Mock LinkParticipants scroll result
        :return:
        """
        aries_task_input = sample_aries_task_input

        # Mocks for link_participants elastic config
        mock_link_participants_get_es_config.return_value = ResourceConfig(
            host="localhost",
            port=9200,
            scheme="http",
            meta_prefix=MetaPrefix.AMPERSAND,
        )

        # Mocks link participants elastic repository
        es_obj = mock_link_participants_elasticsearch_repository.return_value
        es_obj.scroll.return_value = link_participants_scroll_result
        es_obj.MAX_TERMS_SIZE = 1024
        es_obj.meta.prefix = MetaPrefix.AMPERSAND
        es_obj.meta.key = "&key"
        es_obj.meta.id = "&id"

        # Run flow
        return outlook_calendar_meeting_transform_run(aries_task_input=aries_task_input)


def assert_result_is_expected(
    result_path: str,
    expected_path: Path,
    sort_identifiers: bool,
    sort_by: str,
    columns_to_drop: List[str],
) -> None:
    final_result: pd.DataFrame = pd.read_json(result_path, lines=True)

    final_result_expected: pd.DataFrame = pd.read_json(expected_path.as_posix(), lines=True)

    final_result = final_result.sort_values(by=sort_by).reset_index(drop=True)

    final_result = (
        final_result.sort_values(by=sort_by).reset_index(drop=True).drop(columns_to_drop, axis=1)
    )

    final_result_expected = (
        final_result_expected.sort_values(by=sort_by)
        .reset_index(drop=True)
        .drop(columns_to_drop, axis=1)
    )

    if sort_identifiers:
        # Sort identifiers columns
        sort_identifiers_columns(result_df=final_result, expected_result_df=final_result_expected)

    pd.testing.assert_frame_equal(
        left=final_result,
        right=final_result_expected,
    )


def create_and_add_objects_to_s3_bucket(bucket_name: str):
    """Recreate the s3 bucket, and copy all the files from `LOCAL_BUCKET_PATH`
    to s3 mocked bucket.

    :param bucket_name: Bucket name of Mock S3 bucket
    :return: None
    """

    # Create bucket
    s3 = boto3.client("s3", region_name="us-east-1")
    s3.create_bucket(Bucket=bucket_name)

    for file_ in LOCAL_BUCKET_PATH.rglob("*"):
        if file_.is_file():
            _path = file_.as_posix().replace(f"{LOCAL_BUCKET_PATH}/", "")

            with open(file_, "rb") as f:
                s3.put_object(Bucket=bucket_name, Key=_path, Body=f.read())
