import os
import pandas as pd
import pytest
from aries_io_event.io_param import IOParamFieldSet
from aries_io_event.task import TaskFieldSet
from aries_io_event.workflow import WorkflowFieldSet
from aries_task_link.models import AriesTaskInput
from datetime import datetime
from integration_wrapper.static import IntegrationAriesTaskVariables

# This needs to be here since the conftest is loaded first
# than any import on the test files
os.environ[IntegrationAriesTaskVariables.DATA_PLATFORM_CONFIG_API_URL] = (
    "https://test-enterprise.steeleye.co"
)


@pytest.fixture()
def sample_aries_task_input() -> AriesTaskInput:
    workflow = WorkflowFieldSet(
        name="luna_advance_voice",
        stack="dev-shared2",
        tenant="test",
        start_timestamp=datetime(2022, 7, 6),
        trace_id="trace",
    )

    input_param = IOParamFieldSet(
        params=dict(
            file_uri="s3://test.dev.steeleye.co/aries/ingest_batching/luna_advance_voice/2023/11/07/file.ndjson",  # noqa E501
        )
    )

    task = TaskFieldSet(name="luna_advance_voice_transform", version="latest", success=False)

    return AriesTaskInput(workflow=workflow, input_param=input_param, task=task)


@pytest.fixture()
def link_participants_scroll_result() -> pd.DataFrame:
    list_dict = [
        {
            "&id": "142a3b49-6f89-4fb6-91da-6ca079aab5d6",
            "&key": "MarketPerson:142a3b49-6f89-4fb6-91da-6ca079aab5d6:1652689230569",
            "&timestamp": 1652689230569,
            "uniqueIds": ["+447768393840"],
            "&uniqueProps": ["+447768393840"],
            "&model": "MarketPerson",
            "name": "Anthony Hopkins",
            "communications.phoneNumbers": [
                {"dialingCode": "GB", "label": "HOME", "number": "+447768393840"}
            ],
            "personalDetails": {"firstName": "Anthony", "lastName": "Hopkins"},
        },
        {
            "&id": "142a3b49-6f89-4fb6-91da-6ca079aab5d7",
            "&key": "MarketPerson:142a3b49-6f89-4fb6-91da-6ca079aab5d7:1652689230569",
            "&timestamp": 1652689230569,
            "uniqueIds": ["+441615498341"],
            "&uniqueProps": ["+441615498341"],
            "&model": "MarketPerson",
            "name": "Robert de Niro",
            "communications.phoneNumbers": [
                {"dialingCode": "GB", "label": "HOME", "number": "+441615498341"}
            ],
            "personalDetails": {"firstName": "Robert", "lastName": "de Niro"},
        },
        {
            "&id": "142a3b49-6f89-4fb6-91da-6ca079aab5d8",
            "&key": "MarketPerson:142a3b49-6f89-4fb6-91da-6ca079aab5d8:1652689230569",
            "&timestamp": 1652689230569,
            "uniqueIds": ["+443456722999"],
            "&uniqueProps": ["+443456722999"],
            "&model": "MarketPerson",
            "name": "Josh Homme",
            "communications.phoneNumbers": [
                {"dialingCode": "GB", "label": "HOME", "number": "+443456722999"}
            ],
            "personalDetails": {"firstName": "Josh", "lastName": "Homme"},
        },
    ]

    return pd.DataFrame(list_dict)
