import os
import pandas as pd
import pytest
from aries_io_event.io_param import IOParamFieldSet
from aries_io_event.task import TaskFieldSet
from aries_io_event.workflow import WorkflowFieldSet
from aries_task_link.models import AriesTaskInput
from datetime import datetime
from integration_wrapper.static import IntegrationAriesTaskVariables

# This needs to be here since the conftest is loaded first
# than any import on the test files
os.environ[IntegrationAriesTaskVariables.DATA_PLATFORM_CONFIG_API_URL] = (
    "https://test-enterprise.steeleye.co"
)


@pytest.fixture()
def sample_aries_task_input() -> AriesTaskInput:
    workflow = WorkflowFieldSet(
        name="cloud9_voice",
        stack="dev-shared-2",
        tenant="test",
        start_timestamp=datetime(2022, 7, 6),
        trace_id="trace",
    )

    input_param = IOParamFieldSet(
        params=dict(
            file_uri="s3://test.dev.steeleye.co/aries/ingress/nonstreamed/evented/cloud9_voice_poll/2024/05/16/random_trace_id/cDt-********-100940-simaujla1-7242272-1.json",  # noqa E501
        )
    )

    task = TaskFieldSet(name="cloud9_voice_transform", version="latest", success=False)

    return AriesTaskInput(workflow=workflow, input_param=input_param, task=task)


@pytest.fixture()
def link_participants_scroll_result() -> pd.DataFrame:
    list_dict = [
        {
            "&id": "142a3b49-6f89-4fb6-91da-6ca079aab5d6",
            "&key": "AccountPerson:142a3b49-6f89-4fb6-91da-6ca079aab5d6:*************",
            "&timestamp": *************,
            "uniqueIds": ["+************", "simaujla1"],
            "&uniqueProps": ["+************", "simaujla1"],
            "&model": "MarketPerson",
            "name": "simaujla1",
            "communications.phoneNumbers": [
                {"dialingCode": "GB", "label": "HOME", "number": "+************"}
            ],
            "personalDetails": {"firstName": "simaujla1", "lastName": "robin"},
        }
    ]

    return pd.DataFrame(data=list_dict)
