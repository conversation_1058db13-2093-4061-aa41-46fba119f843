{"callDuration": "00:35:22", "connected": true, "hasAttachment": true, "id": 1307368780318097408, "identifiers": {"allIds": ["meeting", "willbartleet"], "fromId": "willbartleet", "fromIdAddlInfo": {"raw": "willbartleet"}, "toIds": ["meeting"], "toIdsAddlInfo": [{"raw": "meeting"}]}, "metadata": {"source": {"client": "Dubber", "fileInfo": {"location": {"bucket": "test.dev.steeleye.co", "key": "aries/ingress/nonstreamed/evented/dubber_voice_poll/2022/09/07/iuyt3k/file.json"}}}}, "sourceIndex": 0, "sourceKey": "s3://test.dev.steeleye.co/aries/ingress/nonstreamed/evented/dubber_voice_poll/2022/09/07/iuyt3k/file.json", "timestamps": {"localTimestampEnd": "2023-10-17T16:36:15.000000Z", "localTimestampStart": "2023-10-17T16:00:53.000000Z", "timestampConnected": "2023-10-17T16:00:53.000000Z", "timestampEnd": "2023-10-17T16:36:15.000000Z", "timestampStart": "2023-10-17T16:00:53.000000Z"}, "voiceFile": {"fileInfo": {"contentLength": 14498, "lastModified": "2022-07-06T06:59:38", "location": {"bucket": "test.dev.steeleye.co", "key": "aries/ingress/depository/attachments/dubber_voice_poll/2022/09/07/1307368780318097411.mp3"}}, "fileName": "1307368780318097411.mp3", "fileType": ".mp3", "sizeInBytes": 14498}, "waveform": {"location": {"bucket": "test.dev.steeleye.co", "key": "attachments/waveform/dubber_voice_transform/1307368780318097411/waveform.json"}}}
{"callDuration": "00:35:25", "connected": true, "hasAttachment": true, "id": 1307369116751204352, "identifiers": {"allIds": ["luciandeboinville", "meeting"], "fromId": "luciandeboinville", "fromIdAddlInfo": {"raw": "luciandeboinville"}, "toIds": ["meeting"], "toIdsAddlInfo": [{"raw": "meeting"}]}, "metadata": {"source": {"client": "Dubber", "fileInfo": {"location": {"bucket": "test.dev.steeleye.co", "key": "aries/ingress/nonstreamed/evented/dubber_voice_poll/2022/09/07/iuyt3k/file.json"}}}}, "sourceIndex": 1, "sourceKey": "s3://test.dev.steeleye.co/aries/ingress/nonstreamed/evented/dubber_voice_poll/2022/09/07/iuyt3k/file.json", "timestamps": {"localTimestampEnd": "2023-10-17T16:36:17.000000Z", "localTimestampStart": "2023-10-17T16:00:52.000000Z", "timestampConnected": "2023-10-17T16:00:52.000000Z", "timestampEnd": "2023-10-17T16:36:17.000000Z", "timestampStart": "2023-10-17T16:00:52.000000Z"}, "voiceFile": {"fileInfo": {"contentLength": 7353, "lastModified": "2022-07-06T06:59:38", "location": {"bucket": "test.dev.steeleye.co", "key": "aries/ingress/depository/attachments/dubber_voice_poll/2022/09/07/1307369116751204355.mp3"}}, "fileName": "1307369116751204355.mp3", "fileType": ".mp3", "sizeInBytes": 7353}, "waveform": {"location": {"bucket": "test.dev.steeleye.co", "key": "attachments/waveform/dubber_voice_transform/1307369116751204355/waveform.json"}}}
{"callDuration": "00:52:47", "connected": true, "hasAttachment": true, "id": 1307373387441852416, "identifiers": {"allCountryCodes": ["GB"], "allIds": ["+442038704500", "juliavaresko"], "fromId": "juliavaresko", "fromIdAddlInfo": {"raw": "juliavaresko"}, "toIds": ["+442038704500"], "toIdsAddlInfo": [{"countryCode": "GB", "raw": "+442038704500"}]}, "metadata": {"source": {"client": "Dubber", "fileInfo": {"location": {"bucket": "test.dev.steeleye.co", "key": "aries/ingress/nonstreamed/evented/dubber_voice_poll/2022/09/07/iuyt3k/file.json"}}}}, "sourceIndex": 2, "sourceKey": "s3://test.dev.steeleye.co/aries/ingress/nonstreamed/evented/dubber_voice_poll/2022/09/07/iuyt3k/file.json", "timestamps": {"localTimestampEnd": "2023-10-17T16:54:32.000000Z", "localTimestampStart": "2023-10-17T16:01:45.000000Z", "timestampConnected": "2023-10-17T16:01:45.000000Z", "timestampEnd": "2023-10-17T16:54:32.000000Z", "timestampStart": "2023-10-17T16:01:45.000000Z"}, "voiceFile": {"fileInfo": {"contentLength": 9747, "lastModified": "2022-07-06T06:59:38", "location": {"bucket": "test.dev.steeleye.co", "key": "aries/ingress/depository/attachments/dubber_voice_poll/2022/09/07/1307373387441852419.mp3"}}, "fileName": "1307373387441852419.mp3", "fileType": ".mp3", "sizeInBytes": 9747}, "waveform": {"location": {"bucket": "test.dev.steeleye.co", "key": "attachments/waveform/dubber_voice_transform/1307373387441852419/waveform.json"}}}
{"callDuration": "00:08:13", "connected": true, "hasAttachment": false, "id": 1307373492647997440, "identifiers": {"allCountryCodes": ["GB"], "allIds": ["+447794192443", "jonnoross"], "fromId": "jonnoross", "fromIdAddlInfo": {"raw": "jonnoross"}, "toIds": ["+447794192443"], "toIdsAddlInfo": [{"countryCode": "GB", "raw": "+447794192443"}]}, "metadata": {"source": {"client": "Dubber", "fileInfo": {"location": {"bucket": "test.dev.steeleye.co", "key": "aries/ingress/nonstreamed/evented/dubber_voice_poll/2022/09/07/iuyt3k/file.json"}}}}, "sourceIndex": 3, "sourceKey": "s3://test.dev.steeleye.co/aries/ingress/nonstreamed/evented/dubber_voice_poll/2022/09/07/iuyt3k/file.json", "timestamps": {"localTimestampEnd": "2023-10-17T16:55:14.000000Z", "localTimestampStart": "2023-10-17T16:47:01.000000Z", "timestampConnected": "2023-10-17T16:47:01.000000Z", "timestampEnd": "2023-10-17T16:55:14.000000Z", "timestampStart": "2023-10-17T16:47:01.000000Z"}, "voiceFile": NaN, "waveform": {"location": {"bucket": "test.dev.steeleye.co", "key": "attachments/waveform/dubber_voice_transform/1307373492647997443/waveform.json"}}}
{"connected":false,"hasAttachment":false,"id":"1410897545816812570","identifiers":{"allCountryCodes":["GB"],"allIds":["+447837018645","maximgreen"],"fromId":"maximgreen","fromIdAddlInfo":{"raw":"maximgreen"},"toIds":["+447837018645"],"toIdsAddlInfo":[{"countryCode":"GB","raw":"+447837018645"}]},"metadata":{"source":{"client":"Dubber","fileInfo":{"location":{"bucket":"test.dev.steeleye.co","key":"aries/ingress/nonstreamed/evented/dubber_voice_poll/2022/09/07/iuyt3k/file.json"}}}},"sourceIndex":0,"sourceKey":"s3://test.dev.steeleye.co/aries/ingress/nonstreamed/evented/dubber_voice_poll/2022/09/07/iuyt3k/file.json","timestamps":{"localTimestampStart":"2024-07-29T09:02:55.000000Z","localTimestampEnd":"2024-07-29T09:02:55.000000Z","timestampConnected":"2024-07-29T09:02:55.000000Z","timestampStart":"2024-07-29T09:02:55.000000Z","timestampEnd":"2024-07-29T09:02:55.000000Z"},"waveform":{"location":{"bucket":"test.dev.steeleye.co","key":"attachments/waveform/dubber_voice_transform/1410897545816812570/waveform.json"}}}