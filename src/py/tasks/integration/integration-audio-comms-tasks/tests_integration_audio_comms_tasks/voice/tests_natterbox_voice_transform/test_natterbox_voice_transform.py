import boto3
import fsspec
import json
import pandas as pd
import pytest
from addict import addict
from aries_config_cached_client.tenant_workflow import CachedTenantWorkflowAPIClient
from aries_se_comms_tasks.feeds.voice.app_metrics_enum import VoiceTransformAppMetricsEnum
from aries_se_comms_tasks.feeds.voice.natterbox_voice.api.get_natterbox_recording_files import (
    Params as GetNatterboxRecordingFilesParams,
)
from aries_se_comms_tasks.generic.participants import link_participants
from aries_se_core_tasks.core.generic_app_metrics_enum import GenericAppMetricsEnum
from aries_se_core_tasks.io.utils import check_file_exists
from aries_se_core_tasks.utilities.helpers_for_tests import sort_identifiers_columns  # type: ignore
from aries_task_link.models import AriesTaskInput
from freezegun import freeze_time
from integration_audio_comms_tasks.voice.natterbox_voice_transform.natterbox_voice_transform_task import (  # noqa E510
    natterbox_voice_transform_run,
)
from integration_test_utils.aws_helpers.patch_response import (
    mock_aiobotocore_convert_to_response_dict,
)
from integration_wrapper.static import St<PERSON><PERSON>ields
from moto import mock_aws
from pathlib import Path
from se_elasticsearch.repository.models import ResourceConfig
from se_elasticsearch.repository.static import MetaPrefix
from se_enums.elastic_search import EsActionEnum
from se_io_utils.json_utils import write_named_temporary_json
from se_io_utils.tempfile_utils import tmp_directory
from shutil import rmtree
from typing import List
from unittest.mock import Mock, patch

BUCKET_NAME: str = "test.dev.steeleye.co"

TEMP_DIR: Path = tmp_directory()
AUDIT_PATH: Path = TEMP_DIR.joinpath("audit.json")

CURRENT_PATH = Path(__file__).parent
LOCAL_BUCKET_PATH = CURRENT_PATH.joinpath("data/buckets", BUCKET_NAME)

DATA_PATH = Path(__file__).parent.joinpath("data")
EXPECTED_RESULTS_PATH = DATA_PATH.joinpath("expected_results")
EXPECTED_OUTPUT_NDJSON_PATH = EXPECTED_RESULTS_PATH.joinpath("expected_output.ndjson")
EXPECTED_OUTPUT_ALL_CASES_NDJSON_PATH = EXPECTED_RESULTS_PATH.joinpath(
    "expected_output_all_cases.ndjson"
)


@pytest.fixture(scope="session", autouse=True)
def cleanup(request):
    def _end():
        rmtree(TEMP_DIR)

    request.addfinalizer(_end)


mock_aiobotocore_convert_to_response_dict()


class TestNatterboxVoiceTransform:
    """Test suite for Natterbox Voice Transform."""

    @staticmethod
    def teardown_method():
        if AUDIT_PATH.exists():
            AUDIT_PATH.unlink()

    @mock_aws
    @freeze_time(time_to_freeze="2022-07-06 06:59:38.911459+00:00")
    @patch(
        "aries_se_comms_tasks.feeds.voice.natterbox_voice.api.get_natterbox_recording_files.requests"  # noqa E501
    )
    @patch(
        "integration_audio_comms_tasks.voice.natterbox_voice_transform.natterbox_voice_transform_flow.GetNatterboxRecordingFilesParams"  # noqa E501
    )
    def test_it_can_run_end_to_end(
        self,
        get_recordings_params_mock,
        get_recordings_mock_requests,
        sample_aries_task_input: AriesTaskInput,
        link_participants_scroll_result: List[dict],
    ):
        # Params is a pydantic model, so we need to mock it
        get_recordings_params_mock.side_effect = mock_get_recordings_params_side_effect

        get_recordings_mock_requests.post.side_effect = mock_post_requests_side_effect
        get_recordings_mock_requests.get.side_effect = mock_get_requests_side_effect

        # Create mock S3 bucket and objects to it
        create_and_add_objects_to_s3_bucket(bucket_name=BUCKET_NAME)

        aries_task_result = self._run_aries_task(
            sample_aries_task_input=sample_aries_task_input,
            link_participants_scroll_result=link_participants_scroll_result,
        )

        ndjon_s3_uri: str = (
            aries_task_result.output_param.params.get("Call").get("params").get("file_uri")
        )

        waveform_s3_uri: str = (
            aries_task_result.output_param.params.get("Waveform").get("params").get("file_uri")
        )

        final_result_expected: pd.DataFrame = pd.read_json(
            EXPECTED_OUTPUT_NDJSON_PATH.as_posix(), lines=True
        )

        final_result: pd.DataFrame = pd.read_json(ndjon_s3_uri, lines=True)

        # Sort identifiers columns
        sort_identifiers_columns(result_df=final_result, expected_result_df=final_result_expected)

        pd.testing.assert_frame_equal(
            left=final_result.sort_values(by="id")
            .drop(["sourceIndex"], axis=1)
            .reset_index(drop=True),
            right=final_result_expected.sort_values(by="id")
            .drop(["sourceIndex"], axis=1)
            .reset_index(drop=True),
            check_like=True,
        )

        metrics = (
            aries_task_result.app_metric.metrics[StaticFields.DATA_INTEGRATION_METRICS_PREFIX][
                "natterbox_voice"
            ]["natterbox_voice_transform"]
            if aries_task_result.app_metric
            else {}
        )

        assert metrics[GenericAppMetricsEnum.OUTPUT_COUNT] == 3
        assert metrics[VoiceTransformAppMetricsEnum.CALLS_WITHOUT_ATTACHMENT_COUNT] == 2

        assert aries_task_result.output_param.params == {
            "Call": {
                "params": {
                    "file_uri": ndjon_s3_uri,
                    "es_action": EsActionEnum.INDEX.value,
                    "data_model": "se_elastic_schema.models.tenant.communication.call:Call",
                }
            },
            "Waveform": {
                "params": {
                    "file_uri": waveform_s3_uri,
                }
            },
        }

        # Ensure recordings were uploaded
        assert (
            check_file_exists(
                path=f"s3://{BUCKET_NAME}/aries/ingress/depository/natterbox_voice/recordings/2022/07/06/230522-3431.mpeg",
            )
            is True
        )

        with fsspec.open(AUDIT_PATH.as_posix(), "r") as file:
            audit = json.load(file)

        assert audit == {
            "input_files": {
                "s3://test.dev.steeleye.co/aries/ingress/streamed/evented/masergy_voice/metadata/2022/09/07/003324-4808": {  # noqa: E501
                    "Call": {
                        "created": 0,
                        "errored": 0,
                        "skipped": 0,
                        "duplicate": 0,
                        "updated": 0,
                        "status": ["It was not possible to fetch the recording file"],
                    }
                },
                "s3://test.dev.steeleye.co/aries/ingress/streamed/evented/masergy_voice/metadata/2022/09/07/010613-2128": {  # noqa: E501
                    "Call": {
                        "created": 0,
                        "errored": 0,
                        "skipped": 0,
                        "duplicate": 0,
                        "updated": 0,
                        "status": [
                            "It was not possible to fetch the recording file",
                            "Participant data did not link with SteelEye records",
                        ],
                    }
                },
                "s3://test.dev.steeleye.co/aries/ingress/depository/natterbox_voice/recordings/2022/07/06/230522-3431.mpeg": {  # noqa: E501
                    "Call": {
                        "created": 0,
                        "errored": 0,
                        "skipped": 0,
                        "duplicate": 0,
                        "updated": 0,
                        "status": ["Participant data did not link with SteelEye records"],
                    }
                },
            },
            "workflow_status": [],
        }

    @staticmethod
    @patch.object(link_participants, "get_repository_by_cluster_version")
    @patch.object(link_participants, "get_es_config")
    @patch.object(
        target=CachedTenantWorkflowAPIClient,
        attribute="get",
        return_value=addict.Dict(
            {
                "tenant": {
                    "lake_prefix": f"s3://{BUCKET_NAME}",
                },
                "workflow": {"streamed": True},
            },
        ),
    )
    @patch("integration_wrapper.integration_aries_task.write_named_temporary_json")
    def _run_aries_task(
        write_named_temporary_json_mock,
        _mock_cached_tenant_workflow_api_client,
        mock_link_participants_get_es_config,
        mock_link_participants_elasticsearch_repository,
        link_participants_scroll_result: dict,
        sample_aries_task_input: AriesTaskInput,
    ):
        """Runs the flow after uploading the input files to mock S3 and mocking
        functions which read from Elasticsearch.

        :param mock_link_participants_get_es_config: Unittest patch object
        :param mock_link_participants_elasticsearch_repository: Unittest patch object
        :param link_participants_scroll_result: Mock LinkParticipants scroll result
        :return:
        """
        aries_task_input = sample_aries_task_input

        # replace write_named_temporary_json side effect
        write_named_temporary_json_mock.side_effect = write_named_temporary_json_side_effect

        # Mocks for link_participants elastic config
        mock_link_participants_get_es_config.return_value = ResourceConfig(
            host="localhost",
            port=9200,
            scheme="http",
            meta_prefix=MetaPrefix.AMPERSAND,
        )

        # Mocks link participants elastic repository
        es_obj = mock_link_participants_elasticsearch_repository.return_value
        es_obj.scroll.return_value = link_participants_scroll_result
        es_obj.MAX_TERMS_SIZE = 1024
        es_obj.meta.prefix = MetaPrefix.AMPERSAND
        es_obj.meta.key = "&key"
        es_obj.meta.id = "&id"

        # Run flow
        return natterbox_voice_transform_run(aries_task_input=aries_task_input)


def create_and_add_objects_to_s3_bucket(bucket_name: str):
    """Recreate the s3 bucket, and copy all the files from `LOCAL_BUCKET_PATH`
    to s3 mocked bucket.

    :param bucket_name: Bucket name of Mock S3 bucket
    :return: None
    """

    # Create bucket
    s3 = boto3.client("s3", region_name="us-east-1")
    s3.create_bucket(Bucket=bucket_name)

    for file_ in LOCAL_BUCKET_PATH.rglob("*"):
        if file_.is_file():
            _path = file_.as_posix().replace(f"{LOCAL_BUCKET_PATH}/", "")

            with open(file_, "rb") as f:
                s3.put_object(Bucket=bucket_name, Key=_path, Body=f.read())


def mock_post_requests_side_effect(url: str, **kwargs):
    if url == "https://sapien.redmatter.com/v1/auth/token":
        return Mock(
            status_code=200,
            json=Mock(
                return_value={
                    "access_token": "access_token_001",
                    "expires_in": 3600,
                    "token_type": "bearer",
                    "scope": "user admin",
                    "refresh_token": "refresh_token_002",
                }
            ),
        )

    return Mock(
        status_code=404,
        text="Not Found",
        ok=False,
    )


def mock_get_requests_side_effect(url: str, **kwargs):
    if (
        url
        == "https://sapien.redmatter.com/v1/organisation/1960/archive/recording/bf157bfe-f27f-11ec-ab35-573f8c32eeb3"
    ):
        # read bytes of DATA_PATH
        with open(DATA_PATH.joinpath("api", "recordings", "230522-3431.mpeg"), "rb") as f:
            return Mock(
                status_code=200,
                ok=True,
                content=f.read(),
                headers={
                    "Content-Type": "audio/mpeg",
                },
            )

    return Mock(
        status_code=404,
        text="Not Found",
        ok=False,
    )


def mock_get_recordings_params_side_effect(**kwargs):
    # no need to retry on tests
    return GetNatterboxRecordingFilesParams(
        **kwargs,
        max_retry=0,
    )


def write_named_temporary_json_side_effect(output_filename: str, **kwargs) -> str:
    if output_filename == "audit.json":
        with fsspec.open(AUDIT_PATH.as_posix(), "w") as file:
            json.dump({}, file)

        return AUDIT_PATH.as_posix()

    return write_named_temporary_json(output_filename=output_filename, **kwargs)
