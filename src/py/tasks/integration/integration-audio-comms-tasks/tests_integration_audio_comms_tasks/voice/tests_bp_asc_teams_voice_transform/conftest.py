import os
import pandas as pd
import pytest
from aries_io_event.io_param import IOParamFieldSet
from aries_io_event.task import TaskFieldSet
from aries_io_event.workflow import WorkflowFieldSet
from aries_task_link.models import AriesTaskInput
from datetime import datetime
from integration_wrapper.static import IntegrationAriesTaskVariables

# This needs to be here since the conftest is loaded first
# than any import on the test files
os.environ[IntegrationAriesTaskVariables.DATA_PLATFORM_CONFIG_API_URL] = (
    "https://test-enterprise.steeleye.co"
)


@pytest.fixture()
def aries_task_input_asc_teams_voice() -> AriesTaskInput:
    workflow = WorkflowFieldSet(
        name="bp_asc_teams_voice",
        stack="dev-shared-2",
        tenant="test",
        start_timestamp=datetime(2022, 7, 6),
        trace_id="trace",
    )

    input_param = IOParamFieldSet(
        params=dict(
            file_uri="s3://test.dev.steeleye.co/aries/ingest_batching/bp_asc_teams_voice/2022/09/07/jzLNl/test_batch.ndjson",  # noqa E501
        )
    )

    task = TaskFieldSet(name="bp_asc_teams_voice_transform", version="latest", success=False)

    return AriesTaskInput(workflow=workflow, input_param=input_param, task=task)


@pytest.fixture()
def link_participants_scroll_result() -> pd.DataFrame:
    list_dict = [
        {
            "&id": "142a3b49-6f89-4fb6-91da-6ca079aab5d6",
            "&key": "AccountPerson:142a3b49-6f89-4fb6-91da-6ca079aab5d6:*************",
            "&timestamp": *************,
            "uniqueIds": ["<EMAIL>", "+************"],
            "&uniqueProps": ["<EMAIL>", "+************"],
            "&model": "AccountPerson",
            "name": "MJ",
            "communications.phoneNumbers": [
                {"dialingCode": "GB", "label": "HOME", "number": "+************"}
            ],
            "personalDetails": {"firstName": "D", "lastName": "Laud"},
        }
    ]

    return pd.DataFrame(list_dict)
