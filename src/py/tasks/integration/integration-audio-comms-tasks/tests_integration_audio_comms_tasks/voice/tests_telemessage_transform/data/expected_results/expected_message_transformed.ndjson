{"attachments":[{"fileInfo":{"contentLength":148782,"location":{"bucket":"test.dev.steeleye.co","key":"aries/ingress/depository/telemessage/attachments/2022/07/06/trace/i_am_a_fake_hash/WhatsApp message from 447418342918 to 60122109577 (1).eml/image26.jpg"},"processed":"2025-10-10T06:59:38.911459","versionId":"9QfrKO0ou_1TjP6ACrf_h2GWiijQOu2W1"},"fileName":"image26.jpg","fileType":"jpg","mimeTag":"image/jpeg","sizeInBytes":148782}],"body":{"displayText":"It\u2019s regular WhatsApp <br/>Disclaimer: Messages in this chat are being recorded.","text":"It\u2019s regular WhatsApp\nDisclaimer: Messages in this chat are being recorded.\n","type":"HTML"},"hasAttachment":true,"identifiers":{"allIds":["+447418342918","+60122109577","447418342918","60122109577"],"fromId":"+447418342918","fromIdAddlInfo":{"countryCode":"GB","raw":"447418342918"},"toIds":["+60122109577"],"toIdsAddlInfo":[{"countryCode":"MY","raw":"60122109577"}]},"metadata":{"messageId":"11294339.77505.1596007819747@generic-smtp-storage-service-1678337916-l5lq4","source":{"client":"WhatsApp (Telemessage)","fileInfo":{"location":{"bucket":"test.dev.steeleye.co","key":"aries/ingress/streamed/evented/telemessage/2024/04/23/WhatsApp message from 447418342918 to 60122109577 (1).eml"}}},"threadId":"WhatsApp message from 447418342918 to 60122109577"},"participants":[{"types":["FROM"],"value":{"&id":"142a3b49-6f89-4fb6-91da-6ca079aab5d6","&key":"MarketPerson:142a3b49-6f89-4fb6-91da-6ca079aab5d6:foobar:1652689230569","communications":{"phoneNumbers":[{"dialingCode":"GB","label":"HOME","number":"+18885145790"}]},"name":"Top trader in Spain","personalDetails":{"firstName":"Person","lastName":"1"},"uniqueIds":["+447418342918"]}},{"types":["TO"],"value":{"&id":"142a3b49-6f89-4fb6-91da-6ca079aab5d6","&key":"MarketPerson:142a3b49-6f89-4fb6-91da-6ca079aab5d6:foobar:1652689230569","communications":{"phoneNumbers":[{"dialingCode":"GB","label":"HOME","number":"+18885145790"}]},"name":"Top trader in Spain","personalDetails":{"firstName":"Person","lastName":"2"},"uniqueIds":["+60122109577"]}}],"roomId":"+447418342918 +60122109577 447418342918 60122109577","roomName":"+447418342918 +60122109577 447418342918 60122109577","sourceKey":"s3://test.dev.steeleye.co/aries/ingest_batching/telemessage/2024/02/23/fx0j0QzbUzthaO5hgdJ6m/batch_file.ndjson","timestamps":{"localTimestampStart":"2020-07-29T07:30:16.000000Z","timestampStart":"2020-07-29T07:30:16.000000Z"}}
{"attachments":[{"fileInfo":{"contentLength":127707,"location":{"bucket":"test.dev.steeleye.co","key":"aries/ingress/depository/telemessage/attachments/2022/07/06/trace/i_am_a_fake_hash/WhatsApp message from 16179257869 to 41799087006.eml/untitled.jpeg"},"processed":"2025-10-10T06:59:38.911459","versionId":"9QfrKO0ou_1TjP6ACrf_h2GWiijQOu2W1"},"fileName":"untitled.jpeg","fileType":"jpeg","mimeTag":"image/jpeg","sizeInBytes":127707}],"body":{"displayText":"\ud83d\udc4b","text":"\ud83d\udc4b\n","type":"HTML"},"hasAttachment":true,"identifiers":{"allIds":["+16179257869","+41799087006","16179257869","41799087006"],"fromId":"+16179257869","fromIdAddlInfo":{"countryCode":"US","raw":"16179257869"},"toIds":["+41799087006"],"toIdsAddlInfo":[{"countryCode":"CH","raw":"41799087006"}]},"metadata":{"messageId":"883267435.549071.1595423911996@generic-smtp-storage-service-1678337916-1wcqv","source":{"client":"WhatsApp (Telemessage)","fileInfo":{"location":{"bucket":"test.dev.steeleye.co","key":"aries/ingress/streamed/evented/telemessage/2024/04/23/WhatsApp message from 16179257869 to 41799087006.eml"}}},"threadId":"WhatsApp message from 16179257869 to 41799087006"},"participants":[{"types":["FROM"],"value":{"&id":"142a3b49-6f89-4fb6-91da-6ca079aab5d6","&key":"MarketPerson:142a3b49-6f89-4fb6-91da-6ca079aab5d6:foobar:1652689230569","communications":{"phoneNumbers":[{"dialingCode":"GB","label":"HOME","number":"+18885145790"}]},"name":"Top trader in Spain","personalDetails":{"firstName":"Person","lastName":"3"},"uniqueIds":["+16179257869"]}},{"types":["TO"],"value":{"&id":"142a3b49-6f89-4fb6-91da-6ca079aab5d6","&key":"MarketPerson:142a3b49-6f89-4fb6-91da-6ca079aab5d6:foobar:1652689230569","communications":{"phoneNumbers":[{"dialingCode":"GB","label":"HOME","number":"+18885145790"}]},"name":"Top trader in Spain","personalDetails":{"firstName":"Person","lastName":"4"},"uniqueIds":["+41799087006"]}}],"roomId":"+16179257869 +41799087006 16179257869 41799087006","roomName":"+16179257869 +41799087006 16179257869 41799087006","sourceKey":"s3://test.dev.steeleye.co/aries/ingest_batching/telemessage/2024/02/23/fx0j0QzbUzthaO5hgdJ6m/batch_file.ndjson","timestamps":{"localTimestampStart":"2020-07-22T13:18:06.000000Z","timestampStart":"2020-07-22T13:18:06.000000Z"}}
{"body":{"displayText":"No. I\u2019m not - wrong phone","text":"No. I\u2019m not - wrong phone\n","type":"HTML"},"hasAttachment":false,"identifiers":{"allIds":["+16179257869","+447391351002","16179257869","447391351002"],"fromId":"+16179257869","fromIdAddlInfo":{"countryCode":"US","raw":"16179257869"},"toIds":["+447391351002"],"toIdsAddlInfo":[{"countryCode":"GB","raw":"447391351002"}]},"metadata":{"messageId":"1995624056.38973.1593619858612@generic-smtp-storage-service-1678337916-1wcqv","source":{"client":"SMS (Telemessage)","fileInfo":{"location":{"bucket":"test.dev.steeleye.co","key":"aries/ingress/streamed/evented/telemessage/2024/04/23/SMS from 16179257869 to 447391351002.eml"}}},"threadId":"SMS from 16179257869 to 447391351002"},"participants":[{"types":["FROM"],"value":{"&id":"142a3b49-6f89-4fb6-91da-6ca079aab5d6","&key":"MarketPerson:142a3b49-6f89-4fb6-91da-6ca079aab5d6:foobar:1652689230569","communications":{"phoneNumbers":[{"dialingCode":"GB","label":"HOME","number":"+18885145790"}]},"name":"Top trader in Spain","personalDetails":{"firstName":"Person","lastName":"3"},"uniqueIds":["+16179257869"]}},{"types":["TO"],"value":{"&id":"142a3b49-6f89-4fb6-91da-6ca079aab5d6","&key":"MarketPerson:142a3b49-6f89-4fb6-91da-6ca079aab5d6:foobar:1652689230569","communications":{"phoneNumbers":[{"dialingCode":"GB","label":"HOME","number":"+18885145790"}]},"name":"Top trader in Spain","personalDetails":{"firstName":"Person","lastName":"5"},"uniqueIds":["+447391351002"]}}],"roomId":"+16179257869 +447391351002 16179257869 447391351002","roomName":"+16179257869 +447391351002 16179257869 447391351002","sourceKey":"s3://test.dev.steeleye.co/aries/ingest_batching/telemessage/2024/02/23/fx0j0QzbUzthaO5hgdJ6m/batch_file.ndjson","timestamps":{"localTimestampStart":"2020-07-01T16:10:57.000000Z","timestampStart":"2020-07-01T16:10:57.000000Z"}}
{"attachments":[{"fileInfo":{"contentLength":306299,"location":{"bucket":"test.dev.steeleye.co","key":"aries/ingress/depository/telemessage/attachments/2022/07/06/trace/i_am_a_fake_hash/06unlvmq5vojh850p69thds2cu339msh2n050rg1.eml/EP\u4e0e\u5fb7\u6052_\u5173\u4e8e\u8bbe\u7acb\u80a1\u6743\u7c7b\u79c1\u52df\u57fa\u91d1\u7ba1\u7406\u4eba\u516c\u53f8\u7684\u7b2c\u4e09\u6b21\u4f1a\u8bae.pdf"},"processed":"2025-10-10T06:59:38.911459","versionId":"9QfrKO0ou_1TjP6ACrf_h2GWiijQOu2W1"},"fileName":"EP\u4e0e\u5fb7\u6052_\u5173\u4e8e\u8bbe\u7acb\u80a1\u6743\u7c7b\u79c1\u52df\u57fa\u91d1\u7ba1\u7406\u4eba\u516c\u53f8\u7684\u7b2c\u4e09\u6b21\u4f1a\u8bae.pdf","fileType":"pdf","mimeTag":"application/pdf","sizeInBytes":306299}],"body":{"type":"HTML"},"hasAttachment":true,"identifiers":{"allIds":["unknownwmm0efqgaaww7o5k1cfjxlwpsddfc9va"],"fromId":"unknownwmm0efqgaaww7o5k1cfjxlwpsddfc9va","fromIdAddlInfo":{"raw":"unknownwmm0efqgaaww7o5k1cfjxlwpsddfc9va"}},"metadata":{"messageId":"1960299431.277389.1711016087638@genericsmtp-storage-service-7bf8d95f8f-gp2h4","source":{"client":"WeChat (Telemessage)","fileInfo":{"location":{"bucket":"test.dev.steeleye.co","key":"aries/ingress/streamed/evented/telemessage/2024/04/23/06unlvmq5vojh850p69thds2cu339msh2n050rg1.eml"}}},"threadId":"WeChat message from Unknown wmM0eFQgAAWw7o5k1cfjxlwpsdDfc9VA to\n chat group"},"roomId":"wrM0eFQgAA-Oei8Rqa2dlU9YRd3y3o5Q","roomName":"unknownwmm0efqgaaww7o5k1cfjxlwpsddfc9va","sourceKey":"s3://test.dev.steeleye.co/aries/ingest_batching/telemessage/2024/02/23/fx0j0QzbUzthaO5hgdJ6m/batch_file.ndjson","timestamps":{"localTimestampStart":"2020-03-21T10:12:00.000000Z","timestampStart":"2020-03-21T10:12:00.000000Z"}}
{"body":{"displayText":"\u73b0\u5728\u662f13214421677","text":"\u73b0\u5728\u662f13214421677\n","type":"HTML"},"hasAttachment":false,"identifiers":{"allIds":["+8613214421677","+8613722508288","8613214421677","8613722508288"],"fromId":"+8613214421677","fromIdAddlInfo":{"countryCode":"CN","raw":"8613214421677"},"toIds":["+8613722508288"],"toIdsAddlInfo":[{"countryCode":"CN","raw":"8613722508288"}]},"metadata":{"messageId":"1024935928.442204.1710431999938@genericsmtp-storage-service-7bf8d95f8f-6g4zn","source":{"client":"WeChat (Telemessage)","fileInfo":{"location":{"bucket":"test.dev.steeleye.co","key":"aries/ingress/streamed/evented/telemessage/2024/04/23/0228ca9hf48qkojtltam97198uj199msqtnvvg81.eml"}}},"threadId":"WeChat  message from 8613214421677 to 8613722508288"},"roomId":"+8613214421677 +8613722508288 8613214421677 8613722508288","roomName":"+8613214421677 +8613722508288 8613214421677 8613722508288","sourceKey":"s3://test.dev.steeleye.co/aries/ingest_batching/telemessage/2024/02/23/fx0j0QzbUzthaO5hgdJ6m/batch_file.ndjson","timestamps":{"localTimestampStart":"2020-03-14T15:59:31.000000Z","timestampStart":"2020-03-14T15:59:31.000000Z"}}
{"body":{"type":"HTML"},"hasAttachment":false,"identifiers":{"allIds":["+8613722508288","8613722508288"],"fromId":"+8613722508288","fromIdAddlInfo":{"countryCode":"CN","raw":"8613722508288"}},"metadata":{"messageId":"793637293.32113.1596531507224@generic-smtp-storage-service-1868261245-z7wrc","source":{"client":"WeChat (Telemessage)","fileInfo":{"location":{"bucket":"test.dev.steeleye.co","key":"aries/ingress/streamed/evented/telemessage/2024/04/23/WeChat System Event - User 8613722508288 was removed from WeChat service.eml"}}},"threadId":"WeChat System Event - User 8613722508288 was removed from WeChat service"},"roomId":"+8613722508288 8613722508288","roomName":"+8613722508288 8613722508288","sourceKey":"s3://test.dev.steeleye.co/aries/ingest_batching/telemessage/2024/02/23/fx0j0QzbUzthaO5hgdJ6m/batch_file.ndjson","timestamps":{"localTimestampStart":"2020-08-04T08:57:49.000000Z","timestampStart":"2020-08-04T08:57:49.000000Z"}}