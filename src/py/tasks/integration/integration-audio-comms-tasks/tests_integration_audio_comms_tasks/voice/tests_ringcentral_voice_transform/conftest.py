import os
import pandas as pd
import pytest
from aries_io_event.io_param import IOParamFieldSet
from aries_io_event.task import TaskFieldSet
from aries_io_event.workflow import WorkflowFieldSet
from aries_task_link.models import AriesTaskInput
from datetime import datetime
from integration_wrapper.static import IntegrationAriesTaskVariables

# This needs to be here since the conftest is loaded first
# than any import on the test files
os.environ[IntegrationAriesTaskVariables.DATA_PLATFORM_CONFIG_API_URL] = (
    "https://test-enterprise.steeleye.co"
)


@pytest.fixture()
def sample_aries_task_input() -> AriesTaskInput:
    workflow = WorkflowFieldSet(
        name="ringcentral_voice",
        stack="dev-blue",
        tenant="test",
        start_timestamp=datetime(2022, 7, 6),
        trace_id="trace",
    )

    input_param = IOParamFieldSet(
        params=dict(
            file_uri="s3://test.dev.steeleye.co/aries/ingress/nonstreamed/evented/ringcentral_voice_poll/2022/09/07/iuyt3k/file.json",  # noqa E501
        )
    )

    task = TaskFieldSet(name="ringcentral_voice_transform", version="latest", success=False)

    return AriesTaskInput(workflow=workflow, input_param=input_param, task=task)


@pytest.fixture()
def link_participants_scroll_result() -> pd.DataFrame:
    list_dict = [
        {
            "&id": "142a3b49-6f89-4fb6-91da-6ca079aab5d6",
            "&timestamp": 1652689230569,
            "uniqueIds": ["+442739464512"],
            "&uniqueProps": ["+442739464512"],
            "&model": "MarketPerson",
            "name": "MJ",
            "communications.phoneNumbers": [
                {"dialingCode": "GB", "label": "HOME", "number": "+18885145790"}
            ],
            "personalDetails": {"firstName": "Mary", "lastName": "Jane"},
        }
    ]

    return pd.DataFrame(list_dict)
