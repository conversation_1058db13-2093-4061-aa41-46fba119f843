{"body": {"displayText": "Your Chefly parcel has been delivered today by Kausar your DPD Local driver and left safely www.dpdl", "text": "Your Chefly parcel has been delivered today by Kausar your DPD Local driver and left safely www.dpdl"}, "identifiers": {"allCountryCodes": ["GB"], "allIds": ["+447408819215", "+44chefly;"], "fromDeviceId": "+44chefly;", "fromId": "+44chefly;", "fromIdAddlInfo": {"raw": "+44chefly;"}, "toDeviceId": "+447408819215", "toIds": ["+447408819215"], "toIdsAddlInfo": [{"countryCode": "GB", "raw": "+447408819215"}]}, "metadata": {"source": {"client": "TruPhone", "fileInfo": {"location": {"bucket": "test.dev.steeleye.co", "key": "aries/ingress/nonstreamed/evented/truphone/2022/07/06/CJqtw/UE SMS Files 110322.csv"}}}}, "roomId": "18fad199ac403ab29c11011362608363", "sourceKey": "s3://test.dev.steeleye.co/aries/ingress/nonstreamed/evented/truphone/2022/07/06/CJqtw/UE SMS Files 110322.csv", "timestamps": {"localTimestampEnd": "2022-03-11T09:10:00.000000Z", "localTimestampStart": "2022-03-11T09:10:00.000000Z", "timestampEnd": "2022-03-11T09:10:00.000000Z", "timestampStart": "2022-03-11T09:10:00.000000Z"}, "participants": NaN}
{"body": {"displayText": "Your driver Kausar will deliver your parcel today between 09:00-10:00| you do have options if you're", "text": "Your driver Kausar will deliver your parcel today between 09:00-10:00| you do have options if you're"}, "identifiers": {"allCountryCodes": ["GB"], "allIds": ["+447408819215", "+44chefly;"], "fromDeviceId": "+44chefly;", "fromId": "+44chefly;", "fromIdAddlInfo": {"raw": "+44chefly;"}, "toDeviceId": "+447408819215", "toIds": ["+447408819215"], "toIdsAddlInfo": [{"countryCode": "GB", "raw": "+447408819215"}]}, "metadata": {"source": {"client": "TruPhone", "fileInfo": {"location": {"bucket": "test.dev.steeleye.co", "key": "aries/ingress/nonstreamed/evented/truphone/2022/07/06/CJqtw/UE SMS Files 110322.csv"}}}}, "roomId": "18fad199ac403ab29c11011362608363", "sourceKey": "s3://test.dev.steeleye.co/aries/ingress/nonstreamed/evented/truphone/2022/07/06/CJqtw/UE SMS Files 110322.csv", "timestamps": {"localTimestampEnd": "2022-03-11T07:02:00.000000Z", "localTimestampStart": "2022-03-11T07:02:00.000000Z", "timestampEnd": "2022-03-11T07:02:00.000000Z", "timestampStart": "2022-03-11T07:02:00.000000Z"}, "participants": NaN}
{"body": {"displayText": "Hi Ulf. You have been outbid on item number 2 - Two tickets to the BAFTA TV Awards 2022. Click here", "text": "Hi Ulf. You have been outbid on item number 2 - Two tickets to the BAFTA TV Awards 2022. Click here"}, "identifiers": {"allCountryCodes": ["GB"], "allIds": ["+447400370244", "+447408819215"], "fromDeviceId": "+447400370244", "fromId": "+447400370244", "fromIdAddlInfo": {"countryCode": "GB", "raw": "+447400370244"}, "toDeviceId": "+447408819215", "toIds": ["+447408819215"], "toIdsAddlInfo": [{"countryCode": "GB", "raw": "+447408819215"}]}, "metadata": {"source": {"client": "TruPhone", "fileInfo": {"location": {"bucket": "test.dev.steeleye.co", "key": "aries/ingress/nonstreamed/evented/truphone/2022/07/06/CJqtw/UE SMS Files 110322.csv"}}}}, "roomId": "f1c98df92756735948cc8646db32c618", "sourceKey": "s3://test.dev.steeleye.co/aries/ingress/nonstreamed/evented/truphone/2022/07/06/CJqtw/UE SMS Files 110322.csv", "timestamps": {"localTimestampEnd": "2022-03-10T13:24:00.000000Z", "localTimestampStart": "2022-03-10T13:24:00.000000Z", "timestampEnd": "2022-03-10T13:24:00.000000Z", "timestampStart": "2022-03-10T13:24:00.000000Z"}, "participants": [{"types": ["FROM"], "value": {"&id": "252a3b49-6f89-4fb6-87gt-6ca079aab5d7", "communications": {"phoneNumbers": [{"dialingCode": "GB", "label": "HOME", "number": "+16585145798"}]}, "name": "MJ", "personalDetails": {"firstName": "Corey", "lastName": "Roll"}, "uniqueIds": ["+447400370244"]}}]}
{"body": {"displayText": "Hi Ulf. You have been outbid on item number 1 - OMEGA '007' Commander's Watch Limited Edition. Click", "text": "Hi Ulf. You have been outbid on item number 1 - OMEGA '007' Commander's Watch Limited Edition. Click"}, "identifiers": {"allCountryCodes": ["GB"], "allIds": ["+447400370244", "+447408819215"], "fromDeviceId": "+447400370244", "fromId": "+447400370244", "fromIdAddlInfo": {"countryCode": "GB", "raw": "+447400370244"}, "toDeviceId": "+447408819215", "toIds": ["+447408819215"], "toIdsAddlInfo": [{"countryCode": "GB", "raw": "+447408819215"}]}, "metadata": {"source": {"client": "TruPhone", "fileInfo": {"location": {"bucket": "test.dev.steeleye.co", "key": "aries/ingress/nonstreamed/evented/truphone/2022/07/06/CJqtw/UE SMS Files 110322.csv"}}}}, "roomId": "f1c98df92756735948cc8646db32c618", "sourceKey": "s3://test.dev.steeleye.co/aries/ingress/nonstreamed/evented/truphone/2022/07/06/CJqtw/UE SMS Files 110322.csv", "timestamps": {"localTimestampEnd": "2022-03-10T12:02:00.000000Z", "localTimestampStart": "2022-03-10T12:02:00.000000Z", "timestampEnd": "2022-03-10T12:02:00.000000Z", "timestampStart": "2022-03-10T12:02:00.000000Z"}, "participants": [{"types": ["FROM"], "value": {"&id": "252a3b49-6f89-4fb6-87gt-6ca079aab5d7", "communications": {"phoneNumbers": [{"dialingCode": "GB", "label": "HOME", "number": "+16585145798"}]}, "name": "MJ", "personalDetails": {"firstName": "Corey", "lastName": "Roll"}, "uniqueIds": ["+447400370244"]}}]}