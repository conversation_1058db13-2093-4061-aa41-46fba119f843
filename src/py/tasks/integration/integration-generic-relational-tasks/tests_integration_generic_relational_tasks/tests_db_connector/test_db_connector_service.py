import datetime
import json
import logging
import pytest
import uuid
from integration_generic_relational_tasks.db_connector import db_connector_task
from integration_generic_relational_tasks.db_connector.db_connector_task import (
    SQLALCHEMY_MODEL_DICT,
)
from integration_generic_relational_tasks.db_connector.static import (
    AuditColumns,
    DatabaseModelGroupEnum,
)
from integration_generic_relational_tasks.db_connector.utils import (
    get_embedded_validation_error_record,
    remove_meta_fields,
)
from mock.mock import DEFAULT, MagicMock, mock_open, patch
from mock_alchemy.mocking import AlchemyMagicMock
from se_io_utils.json_utils import read_json, write_named_temporary_json


@patch.multiple(
    "integration_generic_relational_tasks.db_connector.db_connector_task",
    Resources=DEFAULT,
    fsspec=DEFAULT,
    Database=DEFAULT,
)
def test_db_create(config, mocker, restriction_test_data, mock_tenant_config_api_data, **kwargs):
    mock_msg = MagicMock()
    mock_msg.workflow.start_timestamp = datetime.datetime.utcnow()
    mock_msg.workflow.stack = "test"
    mock_msg.workflow.tenant = "test"
    mock_msg.input_param.params = dict(
        file_uri="file_uri", model_group=DatabaseModelGroupEnum.RESTRICTED_LIST
    )

    kwargs["Resources"].config.return_value = config
    kwargs["fsspec"].open = mock_open(
        read_data="\n".join(json.dumps(i) for i in restriction_test_data)
    )
    kwargs["fsspec"].get_fs_token_paths.return_value = (
        MagicMock(put=MagicMock(return_value=None)),
        0,
        0,
    )

    db_write_result_1 = MagicMock()
    db_write_result_1._mapping = remove_meta_fields(record=restriction_test_data[1])

    db_write_result_2 = MagicMock()
    db_write_result_2._mapping = remove_meta_fields(record=restriction_test_data[3])

    session_mock = AlchemyMagicMock()
    session_mock.execute.return_value.fetchall.return_value = [db_write_result_1, db_write_result_2]

    kwargs[
        "Resources"
    ].db_client.return_value.session.return_value.__enter__.return_value = session_mock

    audit_filepath = write_named_temporary_json(content={}, output_filename=None)
    mock_task = mocker.patch(
        "integration_generic_relational_tasks.db_connector.db_connector_task.write_named_temporary_json"
    )
    mock_task.return_value = audit_filepath
    mocker.patch("integration_audit.auditor.write_json")

    db_connector_task.db_connector_run(aries_task_input=mock_msg)

    audit_report = read_json(audit_filepath)
    expected_result = {
        "input_records": {
            "ad7d4b3f-41b1-488b-8560-12fc92102ac4": {
                "Restriction": {
                    "created": 1,
                    "errored": 0,
                    "skipped": 0,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [],
                }
            },
            "9e9ef784-f7c4-4db6-87b8-4d27fa52c444": {
                "Restriction": {
                    "created": 1,
                    "errored": 0,
                    "skipped": 0,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [],
                }
            },
        },
        "workflow_status": [],
    }
    assert audit_report == expected_result


@patch.multiple(
    "integration_generic_relational_tasks.db_connector.db_connector_task",
    Resources=DEFAULT,
    fsspec=DEFAULT,
    Database=DEFAULT,
)
def test_db_create_with_validation_errors(
    config, mocker, restriction_test_data, mock_tenant_config_api_data, **kwargs
):
    mock_msg = MagicMock()
    mock_msg.workflow.start_timestamp = datetime.datetime.utcnow()
    mock_msg.workflow.stack = "test"
    mock_msg.workflow.tenant = "test"
    mock_msg.input_param.params = dict(
        file_uri="file_uri", model_group=DatabaseModelGroupEnum.RESTRICTED_LIST
    )
    validation_error_uuid = str(uuid.uuid4())
    validation_error_data = dict(
        id=validation_error_uuid,
        field_path="ABC",
        code="Updated",
        message="ABC",
        category="Prices",
        modules_affected=["Watch / Restricted List"],
        severity="HIGH",
        source="steeleye",
        restrictionId=restriction_test_data[1]["id"],
    )
    restriction_test_data[1]["&validationErrors"] = [validation_error_data]

    kwargs["Resources"].config.return_value = config
    kwargs["fsspec"].open = mock_open(
        read_data="\n".join(json.dumps(i) for i in restriction_test_data)
    )
    kwargs["fsspec"].get_fs_token_paths.return_value = (
        MagicMock(put=MagicMock(return_value=None)),
        0,
        0,
    )

    db_write_result_1 = MagicMock()
    db_write_result_1._mapping = remove_meta_fields(record=restriction_test_data[1])

    db_write_result_2 = MagicMock()
    db_write_result_2._mapping = remove_meta_fields(record=restriction_test_data[3])

    validation_error_normalized = MagicMock()
    temp_data = dict()
    temp_data["&validationErrors"] = [remove_meta_fields(record=validation_error_data)]
    validation_error_normalized._mapping = get_embedded_validation_error_record(
        record=temp_data,
        record_model="Restriction",
        model_dict=SQLALCHEMY_MODEL_DICT.get(DatabaseModelGroupEnum.RESTRICTED_LIST, dict()),
    )[0]

    session_mock = AlchemyMagicMock()
    session_mock.execute.return_value.fetchall.side_effect = [
        [db_write_result_1, db_write_result_2],
        [validation_error_normalized],
    ]

    kwargs[
        "Resources"
    ].db_client.return_value.session.return_value.__enter__.return_value = session_mock

    audit_filepath = write_named_temporary_json(content={}, output_filename=None)
    mock_task = mocker.patch(
        "integration_generic_relational_tasks.db_connector.db_connector_task.write_named_temporary_json"
    )
    mock_task.return_value = audit_filepath
    mocker.patch("integration_audit.auditor.write_json")

    db_connector_task.db_connector_run(aries_task_input=mock_msg)

    audit_report = read_json(audit_filepath)
    expected_result = {
        "input_records": {
            "9e9ef784-f7c4-4db6-87b8-4d27fa52c444": {
                "Restriction": {
                    "created": 1,
                    "duplicate": 0,
                    "errored": 0,
                    "skipped": 0,
                    "status": [],
                    "updated": 0,
                }
            },
            "ad7d4b3f-41b1-488b-8560-12fc92102ac4": {
                "Restriction": {
                    "created": 1,
                    "duplicate": 0,
                    "errored": 0,
                    "skipped": 0,
                    "status": [],
                    "updated": 0,
                }
            },
            validation_error_uuid: {
                "ValidationError": {
                    "created": 1,
                    "duplicate": 0,
                    "errored": 0,
                    "skipped": 0,
                    "status": [],
                    "updated": 0,
                }
            },
        },
        "workflow_status": [],
    }
    assert audit_report == expected_result


@patch.multiple(
    "integration_generic_relational_tasks.db_connector.db_connector_task",
    Resources=DEFAULT,
    fsspec=DEFAULT,
    Database=DEFAULT,
)
def test_db_create_for_association_table(
    config, mocker, party_association_test_data, mock_tenant_config_api_data, **kwargs
):
    mock_msg = MagicMock()
    mock_msg.workflow.start_timestamp = datetime.datetime.utcnow()
    mock_msg.workflow.stack = "test"
    mock_msg.workflow.tenant = "test"
    mock_msg.input_param.params = dict(
        file_uri="file_uri", model_group=DatabaseModelGroupEnum.RESTRICTED_LIST
    )

    kwargs["Resources"].config.return_value = config
    kwargs["fsspec"].open = mock_open(
        read_data="\n".join(json.dumps(i) for i in party_association_test_data)
    )
    kwargs["fsspec"].get_fs_token_paths.return_value = (
        MagicMock(put=MagicMock(return_value=None)),
        0,
        0,
    )

    db_write_result_1 = MagicMock()
    db_write_result_1._mapping = remove_meta_fields(record=party_association_test_data[1])

    db_write_result_2 = MagicMock()
    db_write_result_2._mapping = remove_meta_fields(record=party_association_test_data[3])

    session_mock = AlchemyMagicMock()
    session_mock.execute.return_value.fetchall.return_value = [db_write_result_1, db_write_result_2]

    kwargs[
        "Resources"
    ].db_client.return_value.session.return_value.__enter__.return_value = session_mock

    audit_filepath = write_named_temporary_json(content={}, output_filename=None)
    mock_task = mocker.patch(
        "integration_generic_relational_tasks.db_connector.db_connector_task.write_named_temporary_json"
    )
    mock_task.return_value = audit_filepath
    mocker.patch("integration_audit.auditor.write_json")

    db_connector_task.db_connector_run(aries_task_input=mock_msg)

    audit_report = read_json(audit_filepath)
    expected_result = {
        "input_records": {
            "2876da9a-2f8e-4778-a1bb-7833b7708997|81dc9bdb-52d0-4dc2-8036-dbd8313ed055": {
                "RestrictionParty": {
                    "created": 1,
                    "duplicate": 0,
                    "errored": 0,
                    "skipped": 0,
                    "status": [],
                    "updated": 0,
                }
            },
            "9e9ef784-f7c4-4db6-87b8-4d27fa52c444|81dc9bdb-52d0-4dc2-8036-dbd8313ed055": {
                "RestrictionParty": {
                    "created": 1,
                    "duplicate": 0,
                    "errored": 0,
                    "skipped": 0,
                    "status": [],
                    "updated": 0,
                }
            },
        },
        "workflow_status": [],
    }
    assert audit_report == expected_result


@patch.multiple(
    "integration_generic_relational_tasks.db_connector.db_connector_task",
    Resources=DEFAULT,
    fsspec=DEFAULT,
    Database=DEFAULT,
)
def test_db_update(config, mocker, restriction_test_data, mock_tenant_config_api_data, **kwargs):
    mock_msg = MagicMock()
    mock_msg.workflow.start_timestamp = datetime.datetime.utcnow()
    mock_msg.workflow.stack = "test"
    mock_msg.workflow.tenant = "test"
    mock_msg.input_param.params = dict(
        file_uri="file_uri", model_group=DatabaseModelGroupEnum.RESTRICTED_LIST
    )

    kwargs["Resources"].config.return_value = config
    kwargs["fsspec"].open = mock_open(
        read_data="\n".join(json.dumps(i) for i in restriction_test_data)
    )
    kwargs["fsspec"].get_fs_token_paths.return_value = (
        MagicMock(put=MagicMock(return_value=None)),
        0,
        0,
    )

    db_write_result_1 = MagicMock()
    temp_data = remove_meta_fields(record=restriction_test_data[1])
    temp_data[AuditColumns.UPDATED_DATE_TIME] = "ABC"
    db_write_result_1._mapping = temp_data

    db_write_result_2 = MagicMock()
    temp_data = remove_meta_fields(record=restriction_test_data[3])
    temp_data[AuditColumns.UPDATED_DATE_TIME] = "ABC"
    db_write_result_2._mapping = temp_data

    session_mock = AlchemyMagicMock()
    session_mock.execute.return_value.fetchall.return_value = [db_write_result_1, db_write_result_2]

    kwargs[
        "Resources"
    ].db_client.return_value.session.return_value.__enter__.return_value = session_mock

    audit_filepath = write_named_temporary_json(content={}, output_filename=None)
    mock_task = mocker.patch(
        "integration_generic_relational_tasks.db_connector.db_connector_task.write_named_temporary_json"
    )
    mock_task.return_value = audit_filepath
    mocker.patch("integration_audit.auditor.write_json")

    db_connector_task.db_connector_run(aries_task_input=mock_msg)

    audit_report = read_json(audit_filepath)
    expected_result = {
        "input_records": {
            "ad7d4b3f-41b1-488b-8560-12fc92102ac4": {
                "Restriction": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 0,
                    "duplicate": 0,
                    "updated": 1,
                    "status": [],
                }
            },
            "9e9ef784-f7c4-4db6-87b8-4d27fa52c444": {
                "Restriction": {
                    "created": 0,
                    "errored": 0,
                    "skipped": 0,
                    "duplicate": 0,
                    "updated": 1,
                    "status": [],
                }
            },
        },
        "workflow_status": [],
    }
    assert audit_report == expected_result


@patch.multiple(
    "integration_generic_relational_tasks.db_connector.db_connector_task",
    Resources=DEFAULT,
    fsspec=DEFAULT,
    Database=DEFAULT,
)
def test_db_create_streamed(
    config, mocker, restriction_test_data, mock_tenant_config_api_data_streamed, **kwargs
):
    mock_msg = MagicMock()
    mock_msg.workflow.start_timestamp = datetime.datetime.utcnow()
    mock_msg.workflow.stack = "test"
    mock_msg.workflow.tenant = "test"
    mock_msg.input_param.params = dict(
        file_uri="file_uri", model_group=DatabaseModelGroupEnum.RESTRICTED_LIST
    )

    kwargs["Resources"].config.return_value = config
    kwargs["fsspec"].open = mock_open(
        read_data="\n".join(json.dumps(i) for i in restriction_test_data)
    )
    kwargs["fsspec"].get_fs_token_paths.return_value = (
        MagicMock(put=MagicMock(return_value=None)),
        0,
        0,
    )

    db_write_result_1 = MagicMock()
    db_write_result_1._mapping = remove_meta_fields(record=restriction_test_data[1])

    db_write_result_2 = MagicMock()
    db_write_result_2._mapping = remove_meta_fields(record=restriction_test_data[3])

    session_mock = AlchemyMagicMock()
    session_mock.execute.return_value.fetchall.return_value = [db_write_result_1, db_write_result_2]

    kwargs[
        "Resources"
    ].db_client.return_value.session.return_value.__enter__.return_value = session_mock

    audit_filepath = write_named_temporary_json(content={}, output_filename=None)
    mock_task = mocker.patch(
        "integration_generic_relational_tasks.db_connector.db_connector_task.write_named_temporary_json"
    )
    mock_task.return_value = audit_filepath
    mocker.patch("integration_audit.auditor.write_json")

    db_connector_task.db_connector_run(aries_task_input=mock_msg)

    audit_report = read_json(audit_filepath)
    expected_result = {
        "input_files": {
            "unknown_record_1": {
                "Restriction": {
                    "created": 1,
                    "errored": 0,
                    "skipped": 0,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [],
                }
            },
            "unknown_record_2": {
                "Restriction": {
                    "created": 1,
                    "errored": 0,
                    "skipped": 0,
                    "duplicate": 0,
                    "updated": 0,
                    "status": [],
                }
            },
        },
        "workflow_status": [],
    }
    assert audit_report == expected_result


@patch.multiple(
    "integration_generic_relational_tasks.db_connector.db_connector_task",
    Resources=DEFAULT,
    fsspec=DEFAULT,
    Database=DEFAULT,
)
def test_db_error(config, mocker, restriction_test_data, mock_tenant_config_api_data, **kwargs):
    """Test partial DB error."""
    mock_msg = MagicMock()
    mock_msg.workflow.start_timestamp = datetime.datetime.utcnow()
    mock_msg.workflow.stack = "test"
    mock_msg.workflow.tenant = "test"
    mock_msg.input_param.params = dict(
        file_uri="file_uri", model_group=DatabaseModelGroupEnum.RESTRICTED_LIST
    )

    kwargs["Resources"].config.return_value = config

    kwargs["fsspec"].open = mock_open(
        read_data="\n".join(json.dumps(i) for i in restriction_test_data)
    )
    kwargs["fsspec"].get_fs_token_paths.return_value = (
        MagicMock(put=MagicMock(return_value=None)),
        0,
        0,
    )

    session_mock = AlchemyMagicMock()
    session_mock.execute.return_value.fetchall.return_value = Exception("Inner Exception")

    kwargs[
        "Resources"
    ].db_client.return_value.session.return_value.__enter__.return_value = session_mock

    audit_filepath = write_named_temporary_json(content={}, output_filename=None)
    mock_task = mocker.patch(
        "integration_generic_relational_tasks.db_connector.db_connector_task.write_named_temporary_json"
    )
    mock_task.return_value = audit_filepath
    mocker.patch("integration_audit.auditor.write_json")

    db_connector_task.db_connector_run(aries_task_input=mock_msg)

    audit_report = read_json(audit_filepath)
    expected_result = {
        "input_records": {
            "9e9ef784-f7c4-4db6-87b8-4d27fa52c444": {
                "Restriction": {
                    "created": 0,
                    "duplicate": 0,
                    "errored": 1,
                    "skipped": 0,
                    "status": ["Failed to write to database: 'Exception' object is not iterable"],
                    "updated": 0,
                }
            },
            "ad7d4b3f-41b1-488b-8560-12fc92102ac4": {
                "Restriction": {
                    "created": 0,
                    "duplicate": 0,
                    "errored": 1,
                    "skipped": 0,
                    "status": ["Failed to write to database: 'Exception' object is not iterable"],
                    "updated": 0,
                }
            },
        },
        "workflow_status": [],
    }
    assert audit_report == expected_result


@patch.multiple(
    "integration_generic_relational_tasks.db_connector.db_connector_task",
    Resources=DEFAULT,
    fsspec=DEFAULT,
    Database=DEFAULT,
)
def test_record_parsing_error_but_db_success(
    config, mocker, restriction_test_data, mock_tenant_config_api_data, caplog, **kwargs
):
    """The whole batch fails when there is an error in any one of the records
    now."""
    caplog.set_level(logging.ERROR)

    mock_msg = MagicMock()
    mock_msg.workflow.start_timestamp = datetime.datetime.utcnow()
    mock_msg.workflow.stack = "test"
    mock_msg.workflow.tenant = "test"
    mock_msg.input_param.params = dict(
        file_uri="file_uri", model_group=DatabaseModelGroupEnum.RESTRICTED_LIST
    )

    kwargs["Resources"].config.return_value = config

    kwargs["fsspec"].open = mock_open(
        read_data="\n".join(json.dumps(i) for i in restriction_test_data)
    )
    kwargs["fsspec"].get_fs_token_paths.return_value = (
        MagicMock(put=MagicMock(return_value=None)),
        0,
        0,
    )

    restriction_test_data[1]["ABC"] = 123

    db_write_result_1 = MagicMock()
    db_write_result_1._mapping = remove_meta_fields(record=restriction_test_data[1])

    db_write_result_2 = MagicMock()
    db_write_result_2._mapping = remove_meta_fields(record=restriction_test_data[3])

    session_mock = AlchemyMagicMock()
    session_mock.execute.return_value.fetchall.return_value = [db_write_result_1, db_write_result_2]

    kwargs[
        "Resources"
    ].db_client.return_value.session.return_value.__enter__.return_value = session_mock

    audit_filepath = write_named_temporary_json(content={}, output_filename=None)
    mock_task = mocker.patch(
        "integration_generic_relational_tasks.db_connector.db_connector_task.write_named_temporary_json"
    )
    mock_task.return_value = audit_filepath
    mocker.patch("integration_audit.auditor.write_json")

    db_connector_task.db_connector_run(aries_task_input=mock_msg)

    audit_report = read_json(audit_filepath)
    expected_result = {
        "input_records": {
            "ad7d4b3f-41b1-488b-8560-12fc92102ac4": {
                "Restriction": {
                    "created": 1,
                    "duplicate": 0,
                    "errored": 0,
                    "skipped": 0,
                    "status": [],
                    "updated": 0,
                }
            },
            "9e9ef784-f7c4-4db6-87b8-4d27fa52c444": {
                "Restriction": {
                    "created": 1,
                    "duplicate": 0,
                    "errored": 0,
                    "skipped": 0,
                    "status": [],
                    "updated": 0,
                }
            },
        },
        "workflow_status": [],
    }
    assert audit_report == expected_result
    assert "Error parsing returning values from DB" in caplog.text


@patch.multiple(
    "integration_generic_relational_tasks.db_connector.db_connector_task",
    Resources=DEFAULT,
    fsspec=DEFAULT,
    Database=DEFAULT,
)
def test_db_error_partial(
    config, mocker, restriction_test_data, mock_tenant_config_api_data, **kwargs
):
    """Test partial DB error."""
    mock_msg = MagicMock()
    mock_msg.workflow.start_timestamp = datetime.datetime.utcnow()
    mock_msg.workflow.stack = "test"
    mock_msg.workflow.tenant = "test"
    mock_msg.input_param.params = dict(
        file_uri="file_uri", model_group=DatabaseModelGroupEnum.RESTRICTED_LIST
    )

    kwargs["Resources"].config.return_value = config

    restriction_test_data[1]["ABC"] = 123
    kwargs["fsspec"].open = mock_open(
        read_data="\n".join(json.dumps(i) for i in restriction_test_data)
    )
    kwargs["fsspec"].get_fs_token_paths.return_value = (
        MagicMock(put=MagicMock(return_value=None)),
        0,
        0,
    )

    db_write_result = MagicMock()
    db_write_result._mapping = remove_meta_fields(record=restriction_test_data[3])

    session_mock = AlchemyMagicMock()
    session_mock.execute.return_value.fetchall.return_value = [db_write_result]

    kwargs[
        "Resources"
    ].db_client.return_value.session.return_value.__enter__.return_value = session_mock

    audit_filepath = write_named_temporary_json(content={}, output_filename=None)
    mock_task = mocker.patch(
        "integration_generic_relational_tasks.db_connector.db_connector_task.write_named_temporary_json"
    )
    mock_task.return_value = audit_filepath
    mocker.patch("integration_audit.auditor.write_json")

    db_connector_task.db_connector_run(aries_task_input=mock_msg)

    audit_report = read_json(audit_filepath)
    expected_result = {
        "input_records": {
            "9e9ef784-f7c4-4db6-87b8-4d27fa52c444": {
                "Restriction": {
                    "created": 1,
                    "duplicate": 0,
                    "errored": 0,
                    "skipped": 0,
                    "status": [],
                    "updated": 0,
                }
            },
            "ad7d4b3f-41b1-488b-8560-12fc92102ac4": {
                "Restriction": {
                    "created": 0,
                    "duplicate": 0,
                    "errored": 1,
                    "skipped": 0,
                    "status": ["Failed to write to database: Record failed validation"],
                    "updated": 0,
                }
            },
        },
        "workflow_status": [],
    }
    assert audit_report == expected_result


@patch.multiple(
    "integration_generic_relational_tasks.db_connector.db_connector_task",
    Resources=DEFAULT,
    fsspec=DEFAULT,
    Database=DEFAULT,
)
def test_empty_file(config, mocker, mock_tenant_config_api_data, **kwargs):
    mock_msg = MagicMock()
    mock_msg.workflow.start_timestamp = datetime.datetime.utcnow()
    mock_msg.input_param.params = dict(
        file_uri="file_uri", model_group=DatabaseModelGroupEnum.RESTRICTED_LIST
    )
    mock_msg.workflow.trace_id = "trace"
    mock_msg.task.id = "task"
    # Mimicking empty file by setting data as empty string
    kwargs["fsspec"].open = mock_open(read_data="")
    kwargs["fsspec"].get_fs_token_paths.return_value = (
        MagicMock(put=MagicMock(return_value=None)),
        0,
        0,
    )

    audit_filepath = write_named_temporary_json(content={}, output_filename=None)
    mock_task = mocker.patch(
        "integration_generic_relational_tasks.db_connector.db_connector_task.write_named_temporary_json"
    )
    mock_task.return_value = audit_filepath

    db_connector_task.db_connector_run(aries_task_input=mock_msg)
    audit_report = read_json(audit_filepath)
    assert audit_report == {}


@patch.multiple(
    "integration_generic_relational_tasks.db_connector.db_connector_task",
    Resources=DEFAULT,
    fsspec=DEFAULT,
    Database=DEFAULT,
)
def test_empty_file_uri(config, mocker, mock_tenant_config_api_data, **kwargs):
    mock_msg = MagicMock()
    mock_msg.workflow.start_timestamp = datetime.datetime.utcnow()
    mock_msg.input_param.params = dict(
        file_uri=None, model_group=DatabaseModelGroupEnum.RESTRICTED_LIST
    )
    mock_msg.workflow.trace_id = "trace"
    mock_msg.task.id = "task"
    # Mimicking empty file by setting data as empty string
    kwargs["fsspec"].open = mock_open(read_data="")
    kwargs["fsspec"].get_fs_token_paths.return_value = (
        MagicMock(put=MagicMock(return_value=None)),
        0,
        0,
    )

    audit_filepath = write_named_temporary_json(content={}, output_filename=None)
    mock_task = mocker.patch(
        "integration_generic_relational_tasks.db_connector.db_connector_task.write_named_temporary_json"
    )
    mock_task.return_value = audit_filepath

    with pytest.raises(ValueError):
        db_connector_task.db_connector_run(aries_task_input=mock_msg)


@patch.multiple(
    "integration_generic_relational_tasks.db_connector.db_connector_task",
    Resources=DEFAULT,
    fsspec=DEFAULT,
    Database=DEFAULT,
)
def test_empty_file_uri_with_ignore_empty_file_uri(
    config, mocker, mock_tenant_config_api_data, **kwargs
):
    mock_msg = MagicMock()
    mock_msg.workflow.start_timestamp = datetime.datetime.utcnow()
    mock_msg.input_param.params = dict(
        file_uri=None,
        ignore_empty_file_uri=True,
        model_group=DatabaseModelGroupEnum.RESTRICTED_LIST,
    )
    mock_msg.workflow.trace_id = "trace"
    mock_msg.task.id = "task"
    # Mimicking empty file by setting data as empty string
    kwargs["fsspec"].open = mock_open(read_data="")
    kwargs["fsspec"].get_fs_token_paths.return_value = (
        MagicMock(put=MagicMock(return_value=None)),
        0,
        0,
    )

    audit_filepath = write_named_temporary_json(content={}, output_filename=None)
    mock_task = mocker.patch(
        "integration_generic_relational_tasks.db_connector.db_connector_task.write_named_temporary_json"
    )
    mock_task.return_value = audit_filepath

    db_connector_task.db_connector_run(aries_task_input=mock_msg)
    audit_report = read_json(audit_filepath)
    assert audit_report == {}
