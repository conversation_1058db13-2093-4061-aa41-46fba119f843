import datetime
import logging
from aries_io_event.io_param import IOParamFieldSet
from aries_io_event.task import TaskFieldSet
from aries_io_event.workflow import WorkflowFieldSet
from aries_task_link.models import AriesTaskInput
from benchmark_mode import benchmark
from integration_generic_relational_tasks.db_connector.db_connector_task import db_connector_run
from integration_generic_relational_tasks.db_connector.static import DatabaseModelGroupEnum

logger = logging.getLogger("db_connector")


@benchmark
def main():
    workflow = WorkflowFieldSet(
        trace_id="test_restricted_list3",
        name="restricted_list",
        stack="dev-blue",
        tenant="pinafore",
        start_timestamp=datetime.datetime.utcnow(),
    )
    input_param = IOParamFieldSet(
        params=dict(
            file_uri="s3://pinafore.dev.steeleye.co/aries/ingest/restricted_list/2023/10/05/L9aD_xGLg_fGXMVrGhHcd/apply_meta/b0a093e4e54210e00bf172f2cc3c50b46a12af84867d96d71e3cf4e93f64440a___metafied.ndjson",
            model_group=DatabaseModelGroupEnum.RESTRICTED_LIST,
        )
    )
    task = TaskFieldSet(name="db_connector", version="latest", success=False)
    task_input = AriesTaskInput(workflow=workflow, input_param=input_param, task=task)
    task_output = db_connector_run(task_input)
    logger.info(f"Finished executing with output {task_output}")


if __name__ == "__main__":
    main()
