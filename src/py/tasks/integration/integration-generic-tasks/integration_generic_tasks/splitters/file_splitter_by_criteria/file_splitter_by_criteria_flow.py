from addict import addict
from aries_config_cached_client.tenant_workflow import Cached<PERSON>enantWorkflowAPIClient
from aries_se_core_tasks.aries.utility_tasks.finish_flow import finish_flow
from aries_se_core_tasks.aries.utility_tasks.unpack_aries_task_input import unpack_aries_task_input
from aries_se_core_tasks.core.exception import TaskException
from aries_se_trades_tasks.order.static import OrderWorkflowNames
from aries_se_trades_tasks.orders_and_tr.transformations.feed.order_tr_fidessa_eod.static import (
    WORKFLOW_NAME as ORDER_TR_FIDESSA_EOD_FLOW_NAME,
)
from aries_se_trades_tasks.tr.transformations.bbg.emsi.orders.static import (
    WORKFLOW_NAME as TR_BBG_EMSI_ORDERS_FLOW_NAME,
)
from aries_task_link.models import AriesTaskInput
from integration_generic_tasks.splitters.file_splitter_by_criteria.criteria.mymarket_universal_steeleye_person.mymarket_universal_steeleye_person_criteria import (  # noqa: E501
    mymarket_universal_steeleye_person_criteria,
)
from integration_generic_tasks.splitters.file_splitter_by_criteria.criteria.order_aladdin_v2.order_aladdin_v2_controller_criteria import (  # noqa: E501
    order_aladdin_v2_controller_criteria,
)
from integration_generic_tasks.splitters.file_splitter_by_criteria.criteria.order_aladdin_v2.overrides.schroders_order_aladdin_v2_controller_criteria import (  # noqa: E501
    schroders_order_aladdin_v2_controller_criteria,
)
from integration_generic_tasks.splitters.file_splitter_by_criteria.criteria.order_eze_oms_soft_criteria import (
    order_eze_oms_soft_criteria,
)
from integration_generic_tasks.splitters.file_splitter_by_criteria.criteria.order_flextrade_bell_potter_fix_criteria import (  # noqa E501
    order_flextrade_bell_potter_fix_criteria,
)
from integration_generic_tasks.splitters.file_splitter_by_criteria.criteria.order_iress_bell_potter_fix_criteria import (  # noqa E501
    order_iress_bell_potter_fix_criteria,
)
from integration_generic_tasks.splitters.file_splitter_by_criteria.criteria.order_tr_fidessa_eod import (  # noqa E501
    order_tr_fidessa_eod_criteria,
)
from integration_generic_tasks.splitters.file_splitter_by_criteria.criteria.tr_bbg_emsi_orders import (  # noqa E501
    tr_bbg_emsi_orders_criteria,
)
from integration_generic_tasks.splitters.file_splitter_by_criteria.input_schema import (
    FileSplitterByCriteriaAriesTaskInput,
)
from mymarket_tasks.feeds.person.universal_steeleye_person.static import (
    MYMARKET_PERSON_FLOW_NAME,
)
from typing import Callable, Dict, Optional

criteria_per_workflow: Dict[str, Callable] = {
    ORDER_TR_FIDESSA_EOD_FLOW_NAME: order_tr_fidessa_eod_criteria,
    OrderWorkflowNames.ORDER_ALADDIN_V2: order_aladdin_v2_controller_criteria,
    OrderWorkflowNames.ORDER_FLEXTRADE_BELL_POTTER_FIX: order_flextrade_bell_potter_fix_criteria,
    OrderWorkflowNames.ORDER_IRESS_BELL_POTTER_FIX: order_iress_bell_potter_fix_criteria,
    TR_BBG_EMSI_ORDERS_FLOW_NAME: tr_bbg_emsi_orders_criteria,
    MYMARKET_PERSON_FLOW_NAME: mymarket_universal_steeleye_person_criteria,
    OrderWorkflowNames.ORDER_EZE_OMS_SOFT: order_eze_oms_soft_criteria,
}

tenant_override_criteria_per_workflow: Dict[str, Dict[str, Callable]] = {
    "schroders": {
        OrderWorkflowNames.ORDER_ALADDIN_V2: schroders_order_aladdin_v2_controller_criteria
    }
}


class FailIfNoCriteria(TaskException):
    """Exception raised when there is no criteria for the provided workflow."""


def file_splitter_by_criteria_flow(
    aries_task_input: AriesTaskInput,
    app_metrics_path: Optional[str] = None,
    audit_path: Optional[str] = None,
    result_path: str = "result.json",
):
    """
    ---------------------------------------------------------------
    ||                 File Splitter By Criteria                 ||
    ---------------------------------------------------------------
    """

    # Parse and validate AriesTaskInput parameters
    file_splitter_by_criteria_input = unpack_aries_task_input(
        aries_task_input=aries_task_input, model=FileSplitterByCriteriaAriesTaskInput
    )
    tenant = aries_task_input.workflow.tenant

    # Get tenant workflow config
    workflow_config: addict.Dict = CachedTenantWorkflowAPIClient.get(
        tenant_name=tenant,
        workflow_name=aries_task_input.workflow.name,
        stack_name=aries_task_input.workflow.stack,
    )

    tenant_overrides = tenant_override_criteria_per_workflow.get(tenant, {})
    workflow_criteria = tenant_overrides.get(workflow_config.workflow.name)

    # The tenant may have criteria overrides but not for the workflow being executed
    # OR, the tenant may not have any criteria overrides at all.
    if not workflow_criteria:
        # use the default criteria instead
        workflow_criteria = criteria_per_workflow.get(workflow_config.workflow.name)

    if not workflow_criteria:
        raise FailIfNoCriteria(f"No criteria found for: {workflow_config.workflow.name}")

    # Process splitting criteria according to workflow
    result_data = workflow_criteria(
        **{
            "aries_task_input": aries_task_input,
            "file_splitter_by_criteria_aries_task_input": file_splitter_by_criteria_input,
            "workflow_config": workflow_config,
            "app_metrics_path": app_metrics_path,
            "audit_path": audit_path,
        }
    )

    finish_flow(result_path=result_path, result_data=result_data)
