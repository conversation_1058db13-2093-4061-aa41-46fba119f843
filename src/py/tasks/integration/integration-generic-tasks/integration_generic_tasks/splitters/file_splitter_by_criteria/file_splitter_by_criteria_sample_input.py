import datetime
import uuid
from aries_io_event.io_param import IOParamFieldSet
from aries_io_event.task import TaskFieldSet
from aries_io_event.workflow import WorkflowFieldSet
from aries_task_link.models import AriesTaskInput


def sample_input() -> AriesTaskInput:
    workflow = WorkflowFieldSet(
        trace_id=uuid.uuid4().hex,
        name="order_eze_oms_soft",
        stack="dev-shared-2",
        tenant="mares8",
        start_timestamp=datetime.datetime(2024, 10, 29),
    )

    input_param = IOParamFieldSet(
        params=dict(
            file_uri=(
                # "s3://jose.dev.steeleye.co/aries/ingress/nonstreamed/evented/tr_bbg_emsi_orders/STEELEYE_EMSI_ORDERS_CHEL_30859241_20240705.csv"  # noqa: E501
                # "s3://jose.dev.steeleye.co/aries/ingress/nonstreamed/evented/tr_bbg_emsi_orders/STEELEYE_EMSI_ORDERS_CHEL_30859241_20240705_total_empty.csv"  # noqa: E501
                # "s3://jose.dev.steeleye.co/aries/ingress/nonstreamed/evented/tr_bbg_emsi_orders/STEELEYE_EMSI_ORDERS_CHEL_30859241_20240705_headers.csv"  # noqa: E501
                # "s3://jose.dev.steeleye.co/aries/ingress/nonstreamed/evented/tr_bbg_emsi_orders/STEELEYE_EMSI_ORDERS_CHEL_30859241_20240705_headers_empty_lines.csv"  # noqa: E501
                # "s3://jose.dev.steeleye.co/aries/ingress/nonstreamed/evented/tr_bbg_emsi_orders/STEELEYE_EMSI_GLTH_31744242_20240723.csv"  # noqa: E501
                # "s3://mares8.dev.steeleye.co/aries/ingress/nonstreamed/evented/order_tr_fidessa_eod/EXECUTION_LIST.20241029.psv"  # noqa: E501
                "s3://mares8.dev.steeleye.co/test/Foresight_SteelEye_Orders_050225.csv"
            ),
            dynamic_tasks={
                "dynamic_task": {
                    "task_reference_name": "tr_feed_random_subworkflow",
                    "name": "tr_feed_random_subworkflow",
                    "type": "SUB_WORKFLOW",
                }
            },
        )
    )

    # input_param = IOParamFieldSet(
    #     **{
    #         "params": {
    #             "file_uri": "s3://dom.uat.steeleye.co/aries/ingress/nonstreamed/evented/order_aladdin_v2/AladdinTCA.jam_EQ.Fill.20241023.csv",
    #             "dynamic_tasks": {
    #                 "client_side_orders": {
    #                     "task_reference_name": "client_side_orders_processing",
    #                     "input_parameters": {"flow_override": "client"},
    #                     "name": "order_aladdin_v2_subworkflow",
    #                     "type": "SUB_WORKFLOW",
    #                 },
    #                 "market_side_orders": {
    #                     "task_reference_name": "market_side_orders_processing",
    #                     "input_parameters": {"flow_override": "market"},
    #                     "name": "order_aladdin_v2_subworkflow",
    #                     "type": "SUB_WORKFLOW",
    #                 },
    #             },
    #         }
    #     }
    # )

    task = TaskFieldSet(name="file_splitter_by_criteria", version="latest", success=False)
    return AriesTaskInput(workflow=workflow, input_param=input_param, task=task)
