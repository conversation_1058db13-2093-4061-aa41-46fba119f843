CONDUCTOR_API_URL=https://conductor.dev-shared-2.steeleye.co/api
DATA_PLATFORM_CONFIG_API_URL=https://config-api.nonprod-eu-ie-1.steeleye.co
ELASTIC_HOST=elasticsearch.dev-shared-2.steeleye.co
ELASTIC_PORT=443
ELASTIC_SCHEME=https
ELASTIC_API_KEY=ZTU5OERvNEJRM0lZem1Yd3VnX2w6QWRuT3FLNllSUk9MQjB0c2k5a0IwQQ==
STACK=dev-shared-2
SRP_ELASTIC_HOST=localhost
SRP_ELASTIC_PORT=9801
SRP_ELASTIC_SCHEME=http
TASK_NAME=file_splitter_by_criteria
TASK_WORKER_DOMAIN=dev-shared-2
DEBUG=1
SRP_THROUGH_MASTER_DATA=False
OMA_REST_PROXY_URL=https://kafka-rest.uat-oma.steeleye.co
OMA_ARIES_TOPIC=aries.oma.events
AWS_PROFILE=nonprod_infra
COGNITO_AUTH_URL=https://dev-master-data.auth.eu-west-1.amazoncognito.com/oauth2/token
COGNITO_CLIENT_SECRET=
COGNITO_CLIENT_ID=
MARKET_DATA_API_URL=https://api.dev-market-data.steeleye.co
MASTER_DATA_API_HOST=https://api.dev-master-data.steeleye.co
MASTER_DATA_HOST=https://api.dev-master-data.steeleye.co
AZURE_STORAGE_CONNECTION_STRING=
BENCHMARK=false
#BENCHMARK=true