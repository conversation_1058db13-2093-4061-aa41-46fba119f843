from addict import addict
from aries_se_core_tasks.aries.utility_tasks.get_tenant_bucket import get_tenant_bucket
from aries_se_core_tasks.io.iter_file_memmap import iter_file_memmap
from aries_se_core_tasks.io.read.cloud.download_file import run_download_file
from aries_se_core_tasks.io.read.csv_file_splitter_by_column_values import (
    run_csv_file_splitter_by_column_values,
)
from aries_se_core_tasks.io.utils import FileInfo, get_file_info
from aries_se_core_tasks.io.write.upload_file import (  # type: ignore[attr-defined]
    Params as UploadFileParams,
)
from aries_se_core_tasks.io.write.upload_file import run_upload_file
from aries_se_core_tasks.transform.splitter_path.cloud_file_list_from_frame_splitter_result_list import (  # type: ignore[attr-defined] # noqa: E501
    Params as ParamsCloudFileListFileSplitterResultList,
)
from aries_se_core_tasks.transform.splitter_path.cloud_file_list_from_frame_splitter_result_list import (  # type: ignore[attr-defined] # noqa: E501
    run_cloud_file_list_from_file_splitter_result_list,
)
from aries_se_trades_tasks.tr.transformations.bbg.emsi.orders.static import (
    PreNormalisedSourceColumns,
)
from aries_task_link.models import AriesTaskInput
from integration_generic_tasks.splitters.file_splitter_by_criteria.input_schema import (
    FileSplitterByCriteriaAriesTaskInput,
)
from pathlib import Path
from se_conductor_utils.task_output import DynamicTask, create_dynamic_tasks_list
from se_data_lake.cloud_utils import (
    get_bucket,
    get_cloud_provider_from_file_uri,
    get_cloud_provider_prefix,
)
from se_data_lake.lake_path import get_ingest_lake_dir_for_task
from se_enums.cloud import CloudProviderEnum
from typing import Any, Dict


def tr_bbg_emsi_orders_criteria(
    aries_task_input: AriesTaskInput,
    file_splitter_by_criteria_aries_task_input: FileSplitterByCriteriaAriesTaskInput,
    workflow_config: addict.Dict,
    **kwargs,
) -> Dict[str, Any]:
    # Get cloud provider
    cloud_provider: CloudProviderEnum = get_cloud_provider_from_file_uri(
        file_uri=workflow_config.tenant.lake_prefix
    )
    cloud_provider_prefix = get_cloud_provider_prefix(value=cloud_provider)

    # Get realm from input file path
    realm: str = get_bucket(file_uri=file_splitter_by_criteria_aries_task_input.file_uri)

    # Determine the Cloud Bucket of the tenant
    tenant_bucket_with_cloud_prefix = get_tenant_bucket(
        task_input=file_splitter_by_criteria_aries_task_input,
        cloud_provider_prefix=cloud_provider_prefix,
    )

    # Dynamic Task Input
    dynamic_task_input: DynamicTask = file_splitter_by_criteria_aries_task_input.dynamic_tasks[
        "dynamic_task"
    ]

    # download source file
    local_file = run_download_file(file_url=file_splitter_by_criteria_aries_task_input.file_uri)

    if check_if_file_is_empty(local_file):
        dynamic_tasks_output = create_dynamic_tasks_list(
            lst=[],
            task=dynamic_task_input,
            workflow=aries_task_input.workflow,
            common_input_parameters={},
        )

        return dynamic_tasks_output

    # add fake columns at the end to deal with extra commas on Trader Notes column
    add_extra_columns(file_path=local_file)

    # split file into multiple according to Order Number
    split_csv_files = run_csv_file_splitter_by_column_values(
        csv_path=Path(local_file),
        target_columns=[PreNormalisedSourceColumns.ORDER_NUMBER],
        batch_size=workflow_config.max_batch_size,
        number_of_headers=3,
    )

    # Create the appropriate path where the batched files are to be uploaded
    split_file_upload_path_prefix = get_ingest_lake_dir_for_task(
        workflow_name=aries_task_input.workflow.name,
        task_name="file_splitter_by_criteria",
        workflow_start_timestamp=aries_task_input.workflow.start_timestamp,
        workflow_trace_id=aries_task_input.workflow.trace_id,
    )

    # Create cloud upload config for the file batches
    cloud_upload_config = run_cloud_file_list_from_file_splitter_result_list(
        params=ParamsCloudFileListFileSplitterResultList(
            cloud_key_prefix=split_file_upload_path_prefix,
            bucket_name=realm,
        ),
        file_splitter_result_list=split_csv_files,
    )

    # Upload the file batches to cloud
    cloud_file_targets = run_upload_file(
        upload_target=cloud_upload_config, cloud_provider=cloud_provider, params=UploadFileParams()
    )

    # Create the file URIs
    file_uri_list = [
        f"{tenant_bucket_with_cloud_prefix}/{data.key_name}" for data in cloud_file_targets.targets
    ]

    dynamic_tasks_output = create_dynamic_tasks_list(
        lst=[{"file_uri": ndjson_path} for ndjson_path in file_uri_list],
        task=dynamic_task_input,
        workflow=aries_task_input.workflow,
        common_input_parameters={
            "source_file_uri": file_splitter_by_criteria_aries_task_input.file_uri,
        },
    )

    return dynamic_tasks_output


def check_if_file_is_empty(local_file) -> bool:
    """checks if file is completely empty or only contains headers."""
    file_info: FileInfo = get_file_info(path=local_file)

    if file_info.size == 0:
        return True
    else:
        (
            num_newlines,
            file_only_contains_new_lines,
            file_only_contains_new_lines_after_first_newline,
        ) = iter_file_memmap(
            file_path=local_file,
            max_mem_chunk=1024 * 1024,  # Process 1 MB at a time.
            first_newline_index=2,
        )

        if (
            (num_newlines < 3)
            or file_only_contains_new_lines
            or file_only_contains_new_lines_after_first_newline
        ):
            return True

    return False


def add_extra_columns(file_path: str):
    """the vendor sends unquotes CSVs with comma as delimiter but sometimes
    values can have commas in them.

    we've only seen it so far in a column which has
    """
    with open(file_path, "r") as file:
        lines = file.readlines()

    text_to_append: str = ",".join([f"extra_{i}" for i in range(1, 20)])

    lines[0] = lines[0].rstrip("\n") + "," + text_to_append + "\n"

    with open(file_path, "w") as file:
        file.writelines(lines)
