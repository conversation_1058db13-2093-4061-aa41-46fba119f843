import addict
import logging
import pandas as pd
from aries_se_core_tasks.aries.utility_tasks.get_tenant_bucket import get_tenant_bucket
from aries_se_core_tasks.controllers.multiple_files_input_controller import (
    Params as MultipleFilesInputControllerParams,
)
from aries_se_core_tasks.controllers.multiple_files_input_controller import (
    run_multiple_files_input_controller,
)
from aries_se_core_tasks.io.read.cloud.download_file import run_download_file
from aries_se_core_tasks.io.read.csv_file_splitter import Params as ParamsCsvFileSplitter
from aries_se_core_tasks.io.read.csv_file_splitter import run_csv_file_splitter
from aries_se_core_tasks.io.write.upload_file import Params as UploadFileParams
from aries_se_core_tasks.io.write.upload_file import run_upload_file
from aries_se_core_tasks.transform.splitter_path.cloud_file_list_from_frame_splitter_result_list import (
    Params as ParamsCloudFileListFileSplitterResultList,
)
from aries_se_core_tasks.transform.splitter_path.cloud_file_list_from_frame_splitter_result_list import (
    run_cloud_file_list_from_file_splitter_result_list,
)
from aries_se_core_tasks.utilities.frame_manipulation import add_missing_columns
from aries_se_trades_tasks.order.transformations.feed.order_eze_oms_soft.static import (
    UNIQUE_IDENTIFIER_REGEX,
    EzeSoftOmsAllocationsSourceColumns,
    EzeSoftOmsExecutionsSourceColumns,
    EzeSoftOmsOrdersSourceColumns,
    Prefix,
)
from aries_task_link.models import AriesTaskInput
from dataclasses import dataclass
from integration_audit.auditor import upsert_audit
from integration_generic_tasks.splitters.file_splitter_by_criteria.input_schema import (
    FileSplitterByCriteriaAriesTaskInput,
)
from pathlib import Path
from se_conductor_utils.task_output import DynamicTask, create_dynamic_tasks_list
from se_core_tasks.controllers.multiple_files_input_controller import (
    SkipIfMissingFiles,
    SkipIfSourceTimestampLessThanPair,
    SkipIfSourceTimestampSameAlphaLess,
)
from se_core_tasks.core.core_dataclasses import FileSplitterResult
from se_data_lake.cloud_utils import (
    get_bucket,
    get_cloud_provider_from_file_uri,
    get_cloud_provider_prefix,
)
from se_data_lake.lake_path import get_ingest_lake_dir_for_task
from se_enums.cloud import CloudProviderEnum
from se_io_utils.tempfile_utils import tmp_directory
from se_trades_tasks.order.static import add_prefix
from typing import Dict, List

logger = logging.getLogger(__name__)


class SkipInvalidFile(Exception):
    pass


@dataclass
class BatchedFiles:
    cloud_uri: str
    local_path: str
    split_csv_files: List[FileSplitterResult]


def order_eze_oms_soft_criteria(
    aries_task_input: AriesTaskInput,
    workflow_config: addict.Dict,
    file_splitter_by_criteria_aries_task_input: FileSplitterByCriteriaAriesTaskInput,
    app_metrics_path: str,
    audit_path: str,
    **kwargs,
):
    """
    Create 3 separate merged files and batch each independently
    """
    cloud_provider: CloudProviderEnum = get_cloud_provider_from_file_uri(
        file_uri=workflow_config.tenant.lake_prefix
    )
    cloud_provider_prefix = get_cloud_provider_prefix(value=cloud_provider)

    # Get realm from input file path
    realm: str = get_bucket(file_uri=file_splitter_by_criteria_aries_task_input.file_uri)

    # Determine the Cloud Bucket of the tenant
    tenant_bucket_with_cloud_prefix = get_tenant_bucket(
        task_input=file_splitter_by_criteria_aries_task_input,
        cloud_provider_prefix=cloud_provider_prefix,
    )

    # Streamed
    streamed = workflow_config.workflow.streamed

    # Dynamic Task Input
    dynamic_task_input: DynamicTask = file_splitter_by_criteria_aries_task_input.dynamic_tasks[
        "dynamic_task"
    ]
    file_url = file_splitter_by_criteria_aries_task_input.file_uri
    bucket = realm

    # Create temporary directory
    temp_dir: Path = tmp_directory()

    # Merge and preprocess multiple files
    try:
        split_csv_files = merge_and_preprocess_multiple_files(
            file_url=file_url,
            realm=realm,
            workflow_config=workflow_config,
            cloud_provider=cloud_provider,
            temp_dir=temp_dir,
            streamed=streamed,
        )
    except (
        SkipIfSourceTimestampLessThanPair,
        SkipIfSourceTimestampSameAlphaLess,
        SkipIfMissingFiles,
        SkipInvalidFile,
    ) as skip_exception:
        upsert_audit(
            audit_path=audit_path,
            streamed=streamed,
            workflow_status=[skip_exception.message],
        )

        dynamic_tasks_output = create_dynamic_tasks_list(
            lst=[],
            task=dynamic_task_input,
            workflow=aries_task_input.workflow,
            common_input_parameters={},
        )
        return dynamic_tasks_output

    # Upload the file batches to cloud
    source_file_uris: List[str] = []
    all_splitter_results: List[FileSplitterResult] = []
    for file_uri, splitter_results in split_csv_files.items():
        all_splitter_results.extend(splitter_results)
        source_file_uris.extend([file_uri] * len(splitter_results))

    # Create the appropriate path where the files are to be uploaded
    split_file_upload_path_prefix = get_ingest_lake_dir_for_task(
        workflow_name=aries_task_input.workflow.name,
        task_name="file_splitter_by_criteria",
        workflow_start_timestamp=aries_task_input.workflow.start_timestamp,
        workflow_trace_id=aries_task_input.workflow.trace_id,
    )

    # Create cloud upload config for the file batches
    cloud_upload_config = run_cloud_file_list_from_file_splitter_result_list(
        params=ParamsCloudFileListFileSplitterResultList(
            cloud_key_prefix=split_file_upload_path_prefix,
            bucket_name=realm,
            datetime_field_in_file_path=0,
        ),
        file_splitter_result_list=all_splitter_results,
    )

    # Upload the file batches to cloud
    cloud_file_targets = run_upload_file(
        upload_target=cloud_upload_config, cloud_provider=cloud_provider, params=UploadFileParams()
    )

    # Create the file URIs
    file_uri_list = [
        f"{tenant_bucket_with_cloud_prefix}/{data.key_name}" for data in cloud_file_targets.targets
    ]

    dynamic_tasks_output = create_dynamic_tasks_list(
        lst=[
            {"file_uri": ndjson_path, "source_file_uri": source_file_uri}
            for ndjson_path, source_file_uri in zip(file_uri_list, source_file_uris)
        ],
        task=dynamic_task_input,
        workflow=aries_task_input.workflow,
        common_input_parameters={},
    )

    return dynamic_tasks_output


def merge_and_preprocess_multiple_files(
    file_url: str,
    realm: str,
    workflow_config: addict.Dict,
    cloud_provider: CloudProviderEnum,
    temp_dir: Path,
    streamed: bool,
):
    multiple_files_input_controller_result = run_multiple_files_input_controller(
        file_url=file_url,
        realm=realm,
        params=MultipleFilesInputControllerParams(
            list_of_files_regex=[
                ".*(Executions).*",
                ".*(Allocations).*",
                ".*(Orders).*",
            ],
            unique_identifier_regex=[
                UNIQUE_IDENTIFIER_REGEX,
            ],
            file_links_in_output=True,
            prefix_for_file_links_in_output="cloud_",
        ),
        cloud_provider=cloud_provider,
        skip_serializer=True,
    )
    if multiple_files_input_controller_result.empty:
        raise SkipInvalidFile(
            f"The input file: {Path(file_url).name} does not have to be processed."
        )

    executions_file_url = str(
        multiple_files_input_controller_result.loc[0, "cloud_executions_file_url"]
    )
    allocations_file_url = str(
        multiple_files_input_controller_result.loc[0, "cloud_allocations_file_url"]
    )
    orders_file_url = str(multiple_files_input_controller_result.loc[0, "cloud_orders_file_url"])

    executions_local_file_path = run_download_file(
        file_url=executions_file_url,
    )
    allocations_local_file_path = run_download_file(
        file_url=allocations_file_url,
    )
    orders_local_file_path = run_download_file(
        file_url=orders_file_url,
    )

    executions_df = pd.read_csv(executions_local_file_path, skiprows=1, delimiter="|")

    allocations_df = pd.read_csv(allocations_local_file_path, skiprows=1, delimiter="|")

    orders_df = pd.read_csv(orders_local_file_path, skiprows=1, delimiter="|")

    # Add prefixes to column names for identification
    executions_df = executions_df.rename(
        columns=lambda x: add_prefix(prefix=Prefix.EXECUTIONS, delimiter="_", attribute=x)
    )
    allocations_df = allocations_df.rename(
        columns=lambda x: add_prefix(prefix=Prefix.ALLOCATIONS, delimiter="_", attribute=x)
    )
    orders_df = orders_df.rename(
        columns=lambda x: add_prefix(prefix=Prefix.ORDERS, delimiter="_", attribute=x)
    )

    orders_df = add_missing_columns(
        dataframe=orders_df,
        columns=EzeSoftOmsOrdersSourceColumns.all(),
    )
    executions_df = add_missing_columns(
        dataframe=executions_df,
        columns=EzeSoftOmsExecutionsSourceColumns.all(),
    )
    allocations_df = add_missing_columns(
        dataframe=allocations_df,
        columns=EzeSoftOmsAllocationsSourceColumns.all(),
    )

    merge_df = orders_df.merge(
        executions_df,
        left_on=EzeSoftOmsOrdersSourceColumns.ORDER_ID,
        right_on=EzeSoftOmsExecutionsSourceColumns.ORDER_ID,
        how="outer",
    ).merge(
        allocations_df,
        left_on=EzeSoftOmsOrdersSourceColumns.ORDER_ID,
        right_on=EzeSoftOmsAllocationsSourceColumns.ORDER_ID,
        how="outer",
    )
    merged_file_path = temp_dir.joinpath("merged_result.csv")
    merge_df.to_csv(merged_file_path, index=False)

    # Batch the merge_df
    split_csv_files = run_csv_file_splitter(
        params=ParamsCsvFileSplitter(
            chunksize=workflow_config.max_batch_size,
            detect_encoding=True,
            normalise_columns=True,
        ),
        csv_path=str(merged_file_path),
        realm=realm,
        sources_dir=temp_dir.as_posix(),
        streamed=streamed,
    )

    # define output variable
    output_batches: Dict[str, List[FileSplitterResult]] = {
        file_url: split_csv_files,
    }

    return output_batches
