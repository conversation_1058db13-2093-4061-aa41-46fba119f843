import logging
from aries_task_link.models import AriesTaskInput, AriesTaskResult
from integration_generic_tasks.splitters.file_slice_generator.app_metrics_template import (
    APP_METRICS,
)
from integration_generic_tasks.splitters.file_slice_generator.file_slice_generator_flow import (
    file_slice_generator_flow,
)
from integration_wrapper.integration_aries_task import IntegrationAriesTask

logger = logging.getLogger("file_slice_generator")


def file_slice_generator_run(aries_task_input: AriesTaskInput) -> AriesTaskResult:
    integration = IntegrationAriesTask(
        aries_task_input=aries_task_input,
        app_metrics_template=APP_METRICS,
    )

    return integration.execute(flow=file_slice_generator_flow)
