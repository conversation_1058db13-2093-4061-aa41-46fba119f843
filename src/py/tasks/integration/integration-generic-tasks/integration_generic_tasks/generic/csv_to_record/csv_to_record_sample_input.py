import datetime
from aries_io_event.io_param import IOParamFieldSet
from aries_io_event.task import TaskFieldSet
from aries_io_event.workflow import WorkflowFieldSet
from aries_task_link.models import AriesTaskInput


def sample_input() -> AriesTaskInput:
    workflow = WorkflowFieldSet(
        trace_id="shrenik_cas_csv_to_record_11_1",
        name="order_blotter",
        stack="dev-shared-2",
        tenant="mares8",
        start_timestamp=datetime.datetime.utcnow(),
    )
    input_param = IOParamFieldSet(
        params=dict(
            file_uri="s3://mares8.dev.steeleye.co/aries/ingress/nonstreamed/evented/csv2instrument/2024/04/17/FinancialInstrument/cmeInstruments.XKLS.FMDXSX.multileg.20220324.txt",
            skiprows=1,
            nrows=100,
        )
    )
    task = TaskFieldSet(name="csv_to_record", version="latest", success=False)
    return AriesTaskInput(workflow=workflow, input_param=input_param, task=task)
