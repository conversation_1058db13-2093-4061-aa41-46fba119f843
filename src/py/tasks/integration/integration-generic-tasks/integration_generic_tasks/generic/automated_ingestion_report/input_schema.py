# ruff: noqa: E501
from pydantic import BaseModel, EmailStr, Field, root_validator
from typing import Any, Dict, List, Optional


class AutomatedIngestionReportInput(BaseModel):
    email_recipients: List[EmailStr] = Field(
        ...,
        description="List of email addresses that will receive the automated ingestion report",
        examples=["peter.richards@it_company.com", "john.doe@it_company.com"],
    )

    workflow_name: str = Field(
        ...,
        description="Name of the workflow that will be executed. Must match exactly "
        "the name of the workflow definition in Conductor and the workflow name"
        "in the Config DB.",
        examples=["order_blotter", "email", "commpeak_voice"],
    )

    initial_delay_in_minutes: int = Field(
        5,
        description="Once the workflow is triggered, this is the delay in minutes before the first check is made "
        "against the Conductor API. If we anticipate that a given workflow will take "
        "at least X minutes to complete, there is no "
        "need to pressure the Conductor API with frequent unnecessary requests.",
        examples=[0, 5, 10],
    )

    check_interval_in_minutes: int = Field(
        1,
        description="The interval in minutes between each check against the Conductor API. "
        "If the value is `1` it means that after `initial_delay_in_minutes` minutes, "
        "the Conductor API will be checked every minute for the status of the workflow. "
        "Once the workflow is completed or failed, or the `max_workflow_duration_in_minutes` "
        "is reached, the polling will stop.",
        examples=[1, 2, 3],
    )

    max_workflow_duration_in_minutes: int = Field(
        60,
        description="The maximum duration in minutes that the workflow is expected to take. "
        "If the workflow is not completed within this time frame, the polling will stop."
        "This is a safety measure to prevent the automated_ingestion_report task from "
        "running indefinitely.",
        examples=[30, 60, 90],
    )

    non_streamed_input_file: Optional[str] = Field(
        None,
        description="The absolute path of the input file that will be processed by the workflow"
        "`workflow_name`. Regardless of the location of this file, "
        "it will be copied into the right path in the target tenant's lake storage, to trigger the workflow."
        "This param is only valid for Evented Non-streamed workflows that "
        "expect a single file as an input. "
        "This is not a mandatory field as one can run the tool for other types of input files.",
        examples=[
            "s3://mares8.dev.steeleye.co/flows/order-universal-steeleye-trade-blotter/seBlotter.freetrade.eu_orders.20230619.220325.csv"
        ],
    )

    streamed_input_files: Optional[List[str]] = Field(
        None,
        description="The absolute paths of a list of files that will be processed by the workflow"
        "`workflow_name`. Regardless of the location of these files, they will all be copied"
        "into the right path in the target tenant's lake storage to trigger the workflow."
        "All of these files must be stored in the same directory. "
        "This parameter is only valid for Evented Streamed workflows that "
        "support multiple files as an input. This is not a mandatory field as"
        "one can run the tool for other types of input files.",
        examples=[
            [
                "s3://irises8.dev.steeleye.co/feeds/eml/steel-eye.com/013r211oohk6fdqt22rjeo5a1blli6l42kc8hdg1",
                "s3://irises8.dev.steeleye.co/feeds/eml/steel-eye.com/02gtlkeoql9sr8iqmgjfg763mhauf8s94spq7781",
                "s3://irises8.dev.steeleye.co/feeds/eml/steel-eye.com/041j4laltt65rtmvoqp5i6lpce130sudf7r6cm01",
            ]
        ],
    )

    streamed_input_files_by_path: Optional[Dict[str, List[str]]] = Field(
        None,
        description="The absolute paths of a list of files that will be processed by the workflow"
        "`workflow_name`. Regardless of the location of these files, they will al lbe copied"
        "into the right path in the target tenant's lake storage to trigger the workflow."
        "These files are NOT stored in the same directory, instead, "
        "the parameter contains a dictionary where the key is the directory relative path"
        " and the value is the list of files to be stored in that path. "
        "In the example below, specific files will be stored under the `cdr` path, "
        "and other files will be stored under the `recordings` path. Both the `cdr` and `recordings` dirs"
        "are inside the lake storage directory registered in the config db for this particular workflow."
        "The most common use case for this parameter is Voice workflows that need to segregate metadata and recording files,"
        "This parameter is only valid for Evented Streamed workflows that support multiple files as an input. "
        "This is not a mandatory field as one can run the tool for other types of input files. ",
        examples=[
            {
                "cdr": [
                    "s3://irises8.dev.steeleye.co/aries/ingress/streamed/evented/commpeak_voice/cdr/86ff46b0-89f2-4127-aa65-0a84837c20ed.json"
                ],
                "recordings": [
                    "s3://irises8.dev.steeleye.co/aries/ingress/streamed/evented/commpeak_voice/recordings/86ff46b0-89f2-4127-aa65-0a84837c20ed.flac"
                ],
            }
        ],
    )

    polled_input_files: Optional[List[str]] = Field(
        None,
        description="The absolute paths of a list of files that will be processed by the workflow"
        "`workflow_name`. All of these files will be stored in the exact same directories "
        "in the target tenant's lake storage. "
        "This parameter is only valid for Polled Non-streamed workflows that are not triggered"
        "by File drop events. Thus this tool will manually trigger the workflow, as soon as these files"
        "are copied into the target tenant's lake storage. "
        "This is not a mandatory field as one can run the tool for other types of input files.",
        examples=[
            [
                "s3://shrenik.uat.steeleye.co/aries/ingress/nonstreamed/polled/ringcentral_voice/2022/09/07/iuyt3k/file.ndjson",
                "s3://shrenik.uat.steeleye.co/aries/ingress/nonstreamed/polled/ringcentral_voice_poll/2022/09/07/iuyt3k/attachments/dedas6b7Bsm9zUA.mpeg",
                "s3://shrenik.uat.steeleye.co/aries/ingress/nonstreamed/polled/ringcentral_voice_poll/2022/09/07/iuyt3k/attachments/dellFjPNaJJrzUA.mpeg",
            ]
        ],
    )

    polled_workflow_params: Optional[Dict[str, Any]] = Field(
        None,
        description="Additional parameters that will be passed to the workflow `workflow_name` when it is triggered. "
        "This field is mandatory when `polled_input_files` is populated.",
        examples=[
            {
                "file_uri": "s3://irises8.dev.steeleye.co/aries/ingress/nonstreamed/polled/ringcentral_voice_poll/2022/09/07/iuyt3k/file.ndjson"
            }
        ],
    )

    @root_validator()
    def validate_mandatory_input_params(cls, values):
        if not (
            values.get("non_streamed_input_file")
            or values.get("streamed_input_files")
            or values.get("streamed_input_files_by_path")
            or values.get("polled_input_files")
            or values.get("polled_workflow_params")
        ):
            raise ValueError(
                "At least one of the input parameters from the following list must be provided:"
                " [non_streamed_input_file, streamed_input_files, streamed_input_files_by_path,"
                " polled_input_files, polled_workflow_params]"
            )

        return values

    @root_validator()
    def validate_params_are_passed_correctly(cls, values):
        input_params = [
            values.get("non_streamed_input_file"),
            values.get("streamed_input_files"),
            values.get("streamed_input_files_by_path"),
            values.get("polled_input_files"),
            values.get("polled_workflow_params"),
        ]
        input_params = [x for x in input_params if x]

        if (
            len(input_params) > 2
            or (
                len(input_params) == 2
                and (
                    not values.get("polled_workflow_params") or not values.get("polled_input_files")
                )
            )
        ) or (values.get("polled_input_files") and not values.get("polled_workflow_params")):
            raise ValueError(
                "1) You cannot specify more than 2 input parameters from the following list: "
                "[non_streamed_input_file, streamed_input_files, streamed_input_files_by_path, polled_input_files, polled_workflow_params]."
                "2) If you specify exactly 2 input parameters, one of them must be 'polled_input_files' "
                "and the other must be 'polled_workflow_params'."
                "3) If you specify 'polled_input_files', you must also specify 'polled_workflow_params'."
            )

        return values

    class Config:
        extra = "ignore"
