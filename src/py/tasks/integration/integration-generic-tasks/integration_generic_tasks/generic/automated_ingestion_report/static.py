from dataclasses import dataclass
from typing import Dict, List, Optional, Union


@dataclass
class AutomatedQaReport:
    workflow_link: str
    workflow_status: str
    workflow_start_timestamp: Optional[str]
    workflow_end_timestamp: Optional[str]
    workflow_duration_in_minutes: Optional[int]
    number_of_errored_records: Optional[int]
    audit_messages: Optional[List[Dict[str, str]]]
    ingestion_summary: Optional[List[Dict[str, Union[str, int]]]]
    validation_errors_by_id: Optional[List[Dict[str, List[Dict[str, str]]]]]
