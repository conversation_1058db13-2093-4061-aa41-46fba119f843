import datetime
from aries_io_event.io_param import IOParamFieldSet
from aries_io_event.task import TaskFieldSet
from aries_io_event.workflow import WorkflowFieldSet
from aries_task_link.models import AriesTaskInput


def sample_input() -> AriesTaskInput:
    workflow = WorkflowFieldSet(
        trace_id="test_email_transform1",
        name="email",
        stack="benchmark-blue",
        tenant="pinafore-benchmark",
        start_timestamp=datetime.datetime.utcnow(),
    )
    input_param = IOParamFieldSet(
        params=dict(
            file_uri="s3://pinafore.dev.steeleye.co/lake/ingress/090033___"
            "rfCzWyUS4gh08Ikt2HZ0M___batch.ndjson",
        )
    )
    task = TaskFieldSet(name="sink_file_audit_init", version="latest", success=False)
    return AriesTaskInput(workflow=workflow, input_param=input_param, task=task)
