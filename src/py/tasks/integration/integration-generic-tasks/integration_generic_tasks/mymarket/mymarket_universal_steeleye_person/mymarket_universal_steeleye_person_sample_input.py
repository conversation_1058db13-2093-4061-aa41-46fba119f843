from aries_io_event.io_param import IOParamFieldSet
from aries_io_event.task import TaskFieldSet
from aries_io_event.workflow import WorkflowFieldSet
from aries_task_link.models import AriesTaskInput
from datetime import datetime


def sample_input() -> AriesTaskInput:
    workflow = WorkflowFieldSet(
        trace_id="mymarket_universal_steeleye_person_trace_id_1_foo_bar",
        name="mymarket_person",
        stack="dev-shared-2",
        tenant="irises8",
        start_timestamp=datetime.now(),
    )

    input_param = IOParamFieldSet(
        params=dict(
            file_uri="s3://irises8.dev.steeleye.co/ON_TICKETS/ON-4198/mymarket_person_test_null_is_monitored_add_names.csv",  # noqa E501
        )
    )

    task = TaskFieldSet(name="mymarket_person_transform", version="latest", success=False)
    return AriesTaskInput(workflow=workflow, input_param=input_param, task=task)
