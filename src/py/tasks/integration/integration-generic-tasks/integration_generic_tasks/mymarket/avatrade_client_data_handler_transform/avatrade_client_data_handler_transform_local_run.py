# type: ignore
import logging
from benchmark_mode import benchmark
from integration_generic_tasks.integration_generic_tasks_task import integration_generic_tasks_run
from integration_generic_tasks.mymarket.avatrade_client_data_handler_transform.avatrade_client_data_handler_transform_sample_data_input import (  # noqa E501
    sample_input,
)
from integration_generic_tasks.mymarket.avatrade_client_data_handler_transform.avatrade_client_data_handler_transform_task import (  # noqa E501
    avatrade_client_data_handler_transform_run,
)

logger = logging.getLogger("avatrade_client_data_handler_transform")


@benchmark
def main():
    logger.info("Starting execution...")
    output = integration_generic_tasks_run(aries_task_input=sample_input())
    logger.info(f"Finished executing with output {output}")


if __name__ == "__main__":
    main()
