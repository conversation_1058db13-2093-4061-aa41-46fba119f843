# type: ignore
import pandas as pd
import pytest
from addict import addict
from aries_config_cached_client.tenant_workflow import CachedTenantWorkflowAPIClient
from aries_se_core_tasks.core.generic_app_metrics_enum import GenericAppMetricsEnum
from aries_se_core_tasks.generic.deduplicate_by_meta_id import DeduplicateByMetaId
from aries_task_link.models import AriesTaskInput, AriesTaskResult
from freezegun import freeze_time
from integration_generic_tasks.generic.deduplicate_by_meta_id.deduplicate_by_meta_id_task import (
    deduplicate_by_meta_id_run,
)
from integration_wrapper.static import StaticFields
from pathlib import Path
from se_data_lake import lake_path
from se_enums.elastic_search import EsActionEnum
from typing import Any, Dict, List
from unittest.mock import MagicMock, patch

FILENAME_SEPARATOR_PATTERN = "___"
FILENAME_TIMESTAMP_PATTERN = "%Y%m%d_%H%M%S"
CURRENT_PATH = Path(__file__).parent


def mock_all_participants() -> List[Dict[str, Any]]:
    return [
        {
            "&id": "3bc84dc7-5b9c-408d-96e6-5c6749909b1d",
            "&timestamp": 1644396617000,
            "uniqueIds": ["<EMAIL>"],
            "&uniqueProps": ["<EMAIL>"],
            "name": "Puneeth R",
            "communications": {"emails": ["<EMAIL>"]},
            "personalDetails": {"firstName": "Puneeth", "lastName": "R"},
        }
    ]


def get_scenario_path(scenario_index: int):
    scenario_path = f"{CURRENT_PATH}/data/valid_executions/scenario_{scenario_index}/"

    return f"{scenario_path}sample_eml"


def get_expected_output_path(scenario_index: int):
    scenario_path = f"{CURRENT_PATH}/data/valid_executions/scenario_{scenario_index}/"

    return f"{scenario_path}expected_output.ndjson"


def get_result_path(scenario_index: int):
    scenario_path = f"{CURRENT_PATH}/data/valid_executions/scenario_{scenario_index}/"

    return f"{scenario_path}result_transformed.ndjson"


def get_input_ndjson_path(scenario_index: int):
    scenario_path = f"{CURRENT_PATH}/data/valid_executions/scenario_{scenario_index}/"

    return f"{scenario_path}test_input_batch.ndjson"


class TestDeduplicateByMetaIdFlow:
    @staticmethod
    def teardown_method():
        # Delete result paths
        for result_path in [get_result_path(1)]:
            path = Path(result_path)
            if path.exists():
                path.unlink()

    @pytest.mark.parametrize(
        "input_ndjson, existing_meta_ids, result_path, expected_output_path",
        [
            (
                get_input_ndjson_path(1),
                pd.DataFrame(
                    columns=["&id"],
                    data=[
                        "f7a5e1007f181d95958c4d7ffc0f2ed753716ea977fee585a029c34555d0a60750ee1a4f9"
                        "a5babf6d4af0de382f1050389b2cd0ab945d1edb5e1232d11f69a46",
                        "fa659e6f2dc2bd21babbe9fd969e06230d843f1dc29097f0d563fe85b9af90039d208ca346"
                        "055bad1347ba869ae06cc575daf7c3a635a8d3582c4f2858e22901",
                    ],
                ),
                get_result_path(1),
                get_expected_output_path(1),
            )
        ],
    )
    def test_valid_executions(
        self,
        mocker,
        input_ndjson: str,
        existing_meta_ids,
        result_path: str,
        expected_output_path: str,
        sample_aries_task_input: AriesTaskInput,
    ):
        aries_task_result = self._validate_email_transform(
            input_ndjson=input_ndjson,
            mocker=mocker,
            existing_meta_ids_df=existing_meta_ids,
            result_path=result_path,
            sample_aries_task_input=sample_aries_task_input,
        )
        metrics = (
            aries_task_result.app_metric.metrics[StaticFields.DATA_INTEGRATION_METRICS_PREFIX][
                "deduplicate_by_meta_id"
            ]["deduplicate_by_meta_id_flow"]
            if aries_task_result.app_metric
            else {}
        )
        assert metrics[GenericAppMetricsEnum.INPUT_COUNT] == 7
        assert metrics[GenericAppMetricsEnum.ERRORED_COUNT] == 0
        assert metrics[GenericAppMetricsEnum.DUPLICATE_COUNT] == 4
        assert metrics[GenericAppMetricsEnum.OUTPUT_COUNT] == 3
        # Assert AriesTaskResult contains the expected data
        assert (
            aries_task_result.output_param.params
            == {
                "file_uri": result_path,
                "es_action": EsActionEnum.CREATE.value,
                "data_model": "se_elastic_schema.models.tenant.communication.email:Email",
            }
            if aries_task_result.output_param
            else {}
        )

        # Assert the NDJSON produced by the Flow contains the expected data
        with open(expected_output_path, "r") as expected_result:
            expected_result = pd.read_json(expected_result, lines=True)

        with open(result_path, "r") as flow_result:
            result = pd.read_json(flow_result, lines=True)

        assert result.equals(expected_result)

    def _validate_email_transform(
        self,
        mocker,
        input_ndjson: str,
        existing_meta_ids_df: pd.DataFrame,
        result_path: str,
        sample_aries_task_input: AriesTaskInput,
    ):
        aries_task_result = self._run_aries_task(
            mocker=mocker,
            input_ndjson=input_ndjson,
            existing_meta_ids_df=existing_meta_ids_df,
            result_path=result_path,
            sample_aries_task_input=sample_aries_task_input,
        )
        return aries_task_result

    @staticmethod
    @freeze_time(time_to_freeze="2023-07-21 06:59:38.911459+00:00")
    @patch.object(
        target=CachedTenantWorkflowAPIClient,
        attribute="get",
        return_value=addict.Dict(
            {
                "tenant": {
                    "lake_prefix": "s3://test.dev.steeleye.co",
                },
                "workflow": {"streamed": True},
            },
        ),
    )
    def _run_aries_task(
        _mock_cached_tenant_workflow_api_client,
        mocker,
        input_ndjson: str,
        existing_meta_ids_df: pd.DataFrame,
        result_path: str,
        sample_aries_task_input: AriesTaskInput,
    ) -> AriesTaskResult:
        aries_task_input = sample_aries_task_input
        aries_task_input.input_param.params["file_uri"] = input_ndjson

        # Mocks for create_ndjson_path
        mock_create_ndjson_process = mocker.patch(
            "integration_generic_tasks.generic.deduplicate_by_meta_id.deduplicate_by_meta_id_flow.create_ndjson_path"
        )
        mock_create_ndjson_process.return_value = result_path

        mocker.patch("integration_audit.auditor.write_json")

        # Mock check_existing_meta_ids
        mock_check_existing_meta_ids = mocker.patch.object(
            DeduplicateByMetaId, "check_existing_meta_ids"
        )
        mock_check_existing_meta_ids.return_value = existing_meta_ids_df

        mock_s3_put = MagicMock()
        mock_s3_put.put_object.return_value = {"VersionId": "9QfrKO0ou_1TjP6ACrf_h2GWiijQOu2W1"}

        mock_hash_input_param = mocker.patch.object(lake_path, "get_sha256_hash_of_dict")
        mock_hash_input_param.return_value = "i_am_a_fake_hash"

        # Run flow
        return deduplicate_by_meta_id_run(aries_task_input=aries_task_input)
