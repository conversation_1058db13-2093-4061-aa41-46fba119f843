{"body":{"displayText":"<PERSON> <PERSON>\n\nPlease find your daily CME Settlement Recap below;\n\n - Order ID, Ticker, Security, Time, Quantity, Price\n - SE|********|334|1, TYU3, 10YR TNotes SEP3, Buy, 10:21:00, 14848, 110.58\n - SE|********|337|1, TNU3, 10 YR UL TN SEP3, Buy, 10:11:00, 3624, 115.37\n\nFor any queries please reach out to the Settlement Team\n","text":"<PERSON> <PERSON>\n\nPlease find your daily CME Settlement Recap below;\n\n - Order ID, Ticker, Security, Time, Quantity, Price\n - SE|********|334|1, TYU3, 10YR TNotes SEP3, Buy, 10:21:00, 14848, 110.58\n - SE|********|337|1, TNU3, 10 YR UL TN SEP3, Buy, 10:11:00, 3624, 115.37\n\nFor any queries please reach out to the Settlement Team\n","type":"PLAIN"},"hasAttachment":false,"identifiers":{"allDomains":["steel-eye.com"],"allIds":["<EMAIL>","<EMAIL>"],"domains":[{"types":["TO","FROM"],"value":"steel-eye.com"}],"fromId":"<EMAIL>","localParts":[{"types":["TO"],"value":"benjamin.nahum"},{"types":["FROM"],"value":"settlements"}],"toIds":["<EMAIL>"]},"metadata":{"encodingType":"utf-8","header":{"\"Message-ID":"benjamin.nahum.trade.confirm.summary.********","Content-Transfer-Encoding":"quoted-printable","Content-Type":"text/plain;charset=utf-8","Date":"Wed, 14 Aug 2023 17:00:00","From":"<EMAIL>","Mime-Version":"1.0","Subject":"Daily CME Trade Confirmation Summary - ********","To":"<EMAIL>"},"source":{"client":"Microsoft Exchange","fileInfo":{"contentLength":657,"lastModified":"2023-08-16T10:29:39.000000Z","location":{"bucket":"pinafore.dev.steeleye.co","key":"feeds/eml/steel-eye.com/tradeConfirmSummary/********/tradeConfirmSummary.********.0.benjamin.nahum.txt"},"versionId":"xtPK.5i4jiLg5hopwzozS2YJH6Km6waH"}},"threadId":"Daily CME Trade Confirmation Summary - ********"},"participants":[{"types":["TO"],"value":{"&id":"cb76dcfd-d5fa-43cb-8e68-b2837b352577","&key":"AccountPerson:cb76dcfd-d5fa-43cb-8e68-b2837b352577:*************","communications":{"emails":["<EMAIL>","<EMAIL>"]},"name":"Benjamin Nahum","personalDetails":{"firstName":"Benjamin","lastName":"Nahum"},"retailOrProfessional":"N/A","sourceIndex":"74","sourceKey":"s3://pinafore.dev.steeleye.co/flows/mymarket-universal-steeleye-person/se.employees.csv","structure":{"role":"Head of Trade Surveillance"},"uniqueIds":["<EMAIL>","<EMAIL>"]}}],"subject":"Daily CME Trade Confirmation Summary - ********","timestamps":{"timestampStart":"2023-08-14T17:00:00.000000Z"}}
{"body":{"displayText":"Hi Mr X\n\nPlease find your daily X Settlement Recap below;\n\n - Order ID, Ticker, Security, Time, Quantity, Price\n - SE|********|334|1, TYU3, 10YR TNotes SEP3, Buy, 10:21:00, 14848, 110.58\n - SE|********|337|1, TNU3, 10 YR UL TN SEP3, Buy, 10:11:00, 3624, 115.37\n\nFor any queries please reach out to the Settlement Team\n","text":"Hi Mr X\n\nPlease find your daily X Settlement Recap below;\n\n - Order ID, Ticker, Security, Time, Quantity, Price\n - SE|********|334|1, TYU3, 10YR TNotes SEP3, Buy, 10:21:00, 14848, 110.58\n - SE|********|337|1, TNU3, 10 YR UL TN SEP3, Buy, 10:11:00, 3624, 115.37\n\nFor any queries please reach out to the Settlement Team\n","type":"PLAIN"},"hasAttachment":false,"identifiers":{"allDomains":["steel-eye.com"],"allIds":["<EMAIL>","<EMAIL>"],"domains":[{"types":["TO","FROM"],"value":"steel-eye.com"}],"fromId":"<EMAIL>","localParts":[{"types":["TO"],"value":"x.y"},{"types":["FROM"],"value":"settlements"}],"toIds":["<EMAIL>"]},"metadata":{"encodingType":"utf-8","header":{"\"Message-ID":"x.y.trade.confirm.summary.********","Content-Transfer-Encoding":"quoted-printable","Content-Type":"text/plain;charset=utf-8","Date":"Wed, 14 Aug 2023 17:00:00","From":"<EMAIL>","Mime-Version":"1.0","Subject":"Daily CME Trade Confirmation Summary - ********","To":"<EMAIL>"},"source":{"client":"Microsoft Exchange","fileInfo":{"contentLength":657,"lastModified":"2023-08-16T10:29:39.000000Z","location":{"bucket":"pinafore.dev.steeleye.co","key":"feeds/eml/steel-eye.com/tradeConfirmSummary/********/tradeConfirmSummary.********.0.x.y.txt"},"versionId":"xtPK.5i4jiLg5hopwzozS2YJH6Km6waH"}},"threadId":"Daily CME Trade Confirmation Summary - ********"},"participants":[{"types":["TO"],"value":{"&id":"cb76dcfd-d5fa-43cb-8e68-b2837b352577","&key":"AccountPerson:cb76dcfd-d5fa-43cb-8e68-b2837b352577:*************","communications":{"emails":["<EMAIL>","<EMAIL>"]},"name":"X Y","personalDetails":{"firstName":"X","lastName":"Y"},"retailOrProfessional":"N/A","sourceIndex":"74","sourceKey":"s3://pinafore.dev.steeleye.co/flows/mymarket-universal-steeleye-person/se.employees.csv","structure":{"role":"Head of Trade Surveillance"},"uniqueIds":["<EMAIL>","<EMAIL>"]}}],"subject":"Daily CME Trade Confirmation Summary - ********","timestamps":{"timestampStart":"2023-08-14T17:00:00.000000Z"}}
{"body":{"displayText":"Pls find below the lists of buys and sells for today.\n\n - Ticker, Security, Side, Quantity\n - FVU3, 5YR T NOTES SEP3, Buy, 5333\n\nSend recap at end of day\nDavid\n","text":"Pls find below the lists of buys and sells for today.\n\n - Ticker, Security, Side, Quantity\n - FVU3, 5YR T NOTES SEP3, Buy, 5333\n\nSend recap at end of day\nDavid\n","type":"PLAIN"},"hasAttachment":false,"identifiers":{"allDomains":["steel-eye.com"],"allIds":["<EMAIL>","<EMAIL>"],"domains":[{"types":["TO","FROM"],"value":"steel-eye.com"}],"fromId":"<EMAIL>","localParts":[{"types":["TO"],"value":"execution.desk"},{"types":["FROM"],"value":"david.griffiths"}],"toIds":["<EMAIL>"]},"metadata":{"encodingType":"utf-8","header":{"\"Message-ID":"david.griffiths.trade.request.summary.********","Content-Transfer-Encoding":"quoted-printable","Content-Type":"text/plain;charset=utf-8","Date":"Wed, 14 Aug 2023 09:00:00","From":"<EMAIL>","Mime-Version":"1.0","Subject":"CME Execution Request - ********","To":"<EMAIL>"},"source":{"client":"Microsoft Exchange","fileInfo":{"contentLength":481,"lastModified":"2023-08-16T10:29:51.000000Z","location":{"bucket":"pinafore.dev.steeleye.co","key":"feeds/eml/steel-eye.com/tradeConfirmSummary/********/tradeRequest.********.2.david.griffiths.txt"},"versionId":"5d0MnmeNbGSdbG4WK4WiTbdbbqu.VOVb"}},"threadId":"CME Execution Request - ********"},"participants":[{"types":["FROM"],"value":{"&id":"f1e97bb1-973b-42e6-91e8-cd3baffe62d2","&key":"MarketPerson:f1e97bb1-973b-42e6-91e8-cd3baffe62d2:*************","communications":{"emails":["<EMAIL>"],"imAccounts":[{"id":"david.griffiths","label":"BBG"},{"id":"david.griffiths","label":"Live Chat"}]},"name":"David Griffiths","officialIdentifiers":{"clientMandate":"NON-DISCRETIONARY","concatId":"********************","mifirId":"********************","mifirIdSubType":"CONCAT","mifirIdType":"N"},"personalDetails":{"dob":"1969-06-09","firstName":"David","lastName":"Griffiths","nationality":["GB"]},"retailOrProfessional":"RETAIL","sinkIdentifiers":{"tradeFileIdentifiers":[{"id":"david.griffiths","label":"id"},{"id":"david.griffiths","label":"account"}]},"sourceIndex":"2","sourceKey":"s3://pinafore.dev.steeleye.co/flows/mymarket-universal-steeleye-person/steeleyeBlotter.mymarket.********.csv","structure":{"role":"Retail Client","type":"Client"},"uniqueIds":["id:david.griffiths","account:david.griffiths","<EMAIL>","david.griffiths"]}}],"subject":"CME Execution Request - ********","timestamps":{"timestampStart":"2023-08-14T09:00:00.000000Z"}}
{"body":{"displayText":"Pls find below the lists of buys and sells for today.\n\n - Ticker, Security, Side, Quantity\n - FVU3, 5YR T NOTES SEP3, Buy, 5333\n\nSend recap at end of day\nDavid\n","text":"Pls find below the lists of buys and sells for today.\n\n - Ticker, Security, Side, Quantity\n - FVU3, 5YR T NOTES SEP3, Buy, 5333\n\nSend recap at end of day\nDavid\n","type":"PLAIN"},"hasAttachment":false,"identifiers":{"allDomains":["steel-eye.com"],"allIds":["<EMAIL>","<EMAIL>"],"domains":[{"types":["TO","FROM"],"value":"steel-eye.com"}],"fromId":"<EMAIL>","localParts":[{"types":["TO"],"value":"execution.desk"},{"types":["FROM"],"value":"p.r"}],"toIds":["<EMAIL>"]},"metadata":{"encodingType":"utf-8","header":{"\"Message-ID":"p.r.trade.request.summary.********","Content-Transfer-Encoding":"quoted-printable","Content-Type":"text/plain;charset=utf-8","Date":"Wed, 14 Aug 2023 09:00:00","From":"<EMAIL>","Mime-Version":"1.0","Subject":"CME Execution Request - ********","To":"<EMAIL>"},"source":{"client":"Microsoft Exchange","fileInfo":{"contentLength":481,"lastModified":"2023-08-16T10:29:51.000000Z","location":{"bucket":"pinafore.dev.steeleye.co","key":"feeds/eml/steel-eye.com/tradeConfirmSummary/********/tradeRequest.********.2.david.griffiths.txt"},"versionId":"5d0MnmeNbGSdbG4WK4WiTbdbbqu.VOVb"}},"threadId":"CME Execution Request - ********"},"participants":[{"types":["FROM"],"value":{"&id":"f1e97bb1-973b-42e6-91e8-cd3baffe62d2","&key":"MarketPerson:f1e97bb1-973b-42e6-91e8-cd3baffe62d2:*************","communications":{"emails":["<EMAIL>"],"imAccounts":[{"id":"p.r","label":"BBG"},{"id":"p.r","label":"Live Chat"}]},"name":"P R","officialIdentifiers":{"clientMandate":"NON-DISCRETIONARY","concatId":"********************","mifirId":"********************","mifirIdSubType":"CONCAT","mifirIdType":"N"},"personalDetails":{"dob":"1969-06-09","firstName":"P","lastName":"R","nationality":["GB"]},"retailOrProfessional":"RETAIL","sinkIdentifiers":{"tradeFileIdentifiers":[{"id":"p.r","label":"id"},{"id":"p.r","label":"account"}]},"sourceIndex":"2","sourceKey":"s3://pinafore.dev.steeleye.co/flows/mymarket-universal-steeleye-person/steeleyeBlotter.mymarket.********.csv","structure":{"role":"Retail Client","type":"Client"},"uniqueIds":["id:p.r","account:david.griffiths","<EMAIL>","p.r"]}}],"subject":"CME Execution Request - ********","timestamps":{"timestampStart":"2023-08-14T09:00:00.000000Z"}}
{"body":{"displayText":"Pls find below the lists of buys and sells for today.\n\n - Ticker, Security, Side, Quantity\n - FVU3, 5YR T NOTES SEP3, Buy, 5333\n\nSend recap at end of day\nDavid\n","text":"Pls find below the lists of buys and sells for today.\n\n - Ticker, Security, Side, Quantity\n - FVU3, 5YR T NOTES SEP3, Buy, 5333\n\nSend recap at end of day\nDavid\n","type":"PLAIN"},"hasAttachment":false,"identifiers":{"allDomains":["steel-eye.com"],"allIds":["<EMAIL>","<EMAIL>"],"domains":[{"types":["TO","FROM"],"value":"steel-eye.com"}],"fromId":"<EMAIL>","localParts":[{"types":["TO"],"value":"execution.desk"},{"types":["FROM"],"value":"david.griffiths"}],"toIds":["<EMAIL>"]},"metadata":{"encodingType":"utf-8","header":{"\"Message-ID":"david.griffiths.trade.request.summary.********","Content-Transfer-Encoding":"quoted-printable","Content-Type":"text/plain;charset=utf-8","Date":"Wed, 14 Aug 2023 09:00:00","From":"<EMAIL>","Mime-Version":"1.0","Subject":"CME Execution Request - ********","To":"<EMAIL>"},"source":{"client":"Microsoft Exchange","fileInfo":{"contentLength":481,"lastModified":"2023-08-16T10:29:51.000000Z","location":{"bucket":"pinafore.dev.steeleye.co","key":"feeds/eml/steel-eye.com/tradeConfirmSummary/********/tradeRequest.********.2.david.griffiths.txt"},"versionId":"5d0MnmeNbGSdbG4WK4WiTbdbbqu.VOVb"}},"threadId":"CME Execution Request - ********"},"participants":[{"types":["FROM"],"value":{"&id":"f1e97bb1-973b-42e6-91e8-cd3baffe62d2","&key":"MarketPerson:f1e97bb1-973b-42e6-91e8-cd3baffe62d2:*************","communications":{"emails":["<EMAIL>"],"imAccounts":[{"id":"david.griffiths","label":"BBG"},{"id":"david.griffiths","label":"Live Chat"}]},"name":"David Griffiths","officialIdentifiers":{"clientMandate":"NON-DISCRETIONARY","concatId":"********************","mifirId":"********************","mifirIdSubType":"CONCAT","mifirIdType":"N"},"personalDetails":{"dob":"1969-06-09","firstName":"David","lastName":"Griffiths","nationality":["GB"]},"retailOrProfessional":"RETAIL","sinkIdentifiers":{"tradeFileIdentifiers":[{"id":"david.griffiths","label":"id"},{"id":"david.griffiths","label":"account"}]},"sourceIndex":"2","sourceKey":"s3://pinafore.dev.steeleye.co/flows/mymarket-universal-steeleye-person/steeleyeBlotter.mymarket.********.csv","structure":{"role":"Retail Client","type":"Client"},"uniqueIds":["id:david.griffiths","account:david.griffiths","<EMAIL>","david.griffiths"]}}],"subject":"CME Execution Request - ********","timestamps":{"timestampStart":"2023-08-14T09:00:00.000000Z"}}
{"body":{"displayText":"Pls find below the lists of buys and sells for today.\n\n - Ticker, Security, Side, Quantity\n - FFX3, 30 DAY FED NOV3, Buy, 453\n - FVU3, 5YR T NOTES SEP3, Buy, 6573\n\nSend recap at end of day\nNeil\n","text":"Pls find below the lists of buys and sells for today.\n\n - Ticker, Security, Side, Quantity\n - FFX3, 30 DAY FED NOV3, Buy, 453\n - FVU3, 5YR T NOTES SEP3, Buy, 6573\n\nSend recap at end of day\nNeil\n","type":"PLAIN"},"hasAttachment":false,"identifiers":{"allDomains":["steel-eye.com"],"allIds":["<EMAIL>","<EMAIL>"],"domains":[{"types":["TO","FROM"],"value":"steel-eye.com"}],"fromId":"<EMAIL>","localParts":[{"types":["TO"],"value":"execution.desk"},{"types":["FROM"],"value":"neil.gooderham"}],"toIds":["<EMAIL>"]},"metadata":{"encodingType":"utf-8","header":{"\"Message-ID":"neil.gooderham.trade.request.summary.********","Content-Transfer-Encoding":"quoted-printable","Content-Type":"text/plain;charset=utf-8","Date":"Wed, 14 Aug 2023 09:00:00","From":"<EMAIL>","Mime-Version":"1.0","Subject":"CME Execution Request - ********","To":"<EMAIL>"},"source":{"client":"Microsoft Exchange","fileInfo":{"contentLength":514,"lastModified":"2023-08-16T10:29:58.000000Z","location":{"bucket":"pinafore.dev.steeleye.co","key":"feeds/eml/steel-eye.com/tradeConfirmSummary/********/tradeRequest.********.3.neil.gooderham.txt"},"versionId":"_IC7rHfBKIhfNgsgpu7mwDImiR3BWW2z"}},"threadId":"CME Execution Request - ********"},"subject":"CME Execution Request - ********","timestamps":{"timestampStart":"2023-08-14T09:00:00.000000Z"}}
{"body":{"displayText":"Pls find below the lists of buys and sells for today.\n\n - Ticker, Security, Side, Quantity\n - FFX3, 30 DAY FED NOV3, Buy, 453\n - FVU3, 5YR T NOTES SEP3, Buy, 6573\n\nSend recap at end of day\nNeil\n","text":"Pls find below the lists of buys and sells for today.\n\n - Ticker, Security, Side, Quantity\n - FFX3, 30 DAY FED NOV3, Buy, 453\n - FVU3, 5YR T NOTES SEP3, Buy, 6573\n\nSend recap at end of day\nNeil\n","type":"PLAIN"},"hasAttachment":false,"identifiers":{"allDomains":["steel-eye.com"],"allIds":["<EMAIL>","<EMAIL>"],"domains":[{"types":["TO","FROM"],"value":"steel-eye.com"}],"fromId":"<EMAIL>","localParts":[{"types":["TO"],"value":"execution.desk"},{"types":["FROM"],"value":"neil.gooderham"}],"toIds":["<EMAIL>"]},"metadata":{"encodingType":"utf-8","header":{"\"Message-ID":"neil.gooderham.trade.request.summary.********","Content-Transfer-Encoding":"quoted-printable","Content-Type":"text/plain;charset=utf-8","Date":"Wed, 14 Aug 2023 09:00:00","From":"<EMAIL>","Mime-Version":"1.0","Subject":"CME Execution Request - ********","To":"<EMAIL>"},"source":{"client":"Microsoft Exchange","fileInfo":{"contentLength":514,"lastModified":"2023-08-16T10:29:58.000000Z","location":{"bucket":"pinafore.dev.steeleye.co","key":"feeds/eml/steel-eye.com/tradeConfirmSummary/********/tradeRequest.********.3.neil.gooderham.txt"},"versionId":"_IC7rHfBKIhfNgsgpu7mwDImiR3BWW2z"}},"threadId":"CME Execution Request - ********"},"subject":"CME Execution Request - ********","timestamps":{"timestampStart":"2023-08-14T09:00:00.000000Z"}}