import datetime
import logging
import pandas as pd
from addict import addict
from aries_config_cached_client.tenant_workflow import CachedTenantWorkflowAPIClient
from aries_se_core_tasks.io.read.cloud.download_file import run_download_file
from aries_se_trades_tasks.order.static import OrderWorkflowNames
from aries_task_link.models import AriesTaskInput, AriesTaskResult
from freezegun import freeze_time
from integration_generic_tasks.splitters.file_splitter_by_criteria.file_splitter_by_criteria_task import (  # noqa E501
    file_splitter_by_criteria_run,
)
from integration_test_utils.aws_helpers.patch_response import (
    mock_aiobotocore_convert_to_response_dict,
)
from integration_test_utils.aws_helpers.s3_test_helpers import create_and_add_objects_to_s3_bucket
from mock.mock import patch
from moto import mock_aws
from pathlib import Path

BUCKET_NAME: str = "test.dev.steeleye.co"

CURRENT_PATH = Path(__file__).parent
LOCAL_BUCKET_PATH = CURRENT_PATH.joinpath(
    "data/order_flextrade_bell_potter_fix/buckets", BUCKET_NAME
)
EXPECTED_RESULTS_PATH = CURRENT_PATH.joinpath(
    "data/order_flextrade_bell_potter_fix/expected_results"
)
END_TO_END_RESULT_PICKLE_0 = EXPECTED_RESULTS_PATH.joinpath("end_to_end_expected_0.pkl")
END_TO_END_RESULT_PICKLE_1 = EXPECTED_RESULTS_PATH.joinpath("end_to_end_expected_1.pkl")
REPLACE_LOGIC_RESULT_PICKLE = EXPECTED_RESULTS_PATH.joinpath("replace_logic_expected.pkl")

RESULT_JSON_PATH = CURRENT_PATH.joinpath("result.json")

mock_aiobotocore_convert_to_response_dict()


class TestOrderFlextradeBellPotterFixCriteria:
    @staticmethod
    def teardown_method():
        """Deletes temporary files created during the test."""
        if RESULT_JSON_PATH.exists():
            RESULT_JSON_PATH.unlink()

    @patch.object(
        target=CachedTenantWorkflowAPIClient,
        attribute="get",
        return_value=addict.Dict(
            {
                "tenant": {
                    "cloud": "aws",
                    "lake_prefix": f"s3://{BUCKET_NAME}/",
                    "realm": "test.dev.steeleye.co",
                },
                "workflow": {
                    "streamed": False,
                    "name": OrderWorkflowNames.ORDER_FLEXTRADE_BELL_POTTER_FIX,
                    "tenant": "test",
                },
            },
        ),
    )
    @mock_aws
    @freeze_time(time_to_freeze="2024-05-22 06:59:38.911459+00:00")
    def test_normal_execution(
        self,
        mocker,
        sample_aries_task_input_order_flextrade_bell_potter_fix: AriesTaskInput,
    ):
        create_and_add_objects_to_s3_bucket(bucket_name=BUCKET_NAME, source_path=LOCAL_BUCKET_PATH)

        aries_task_result = file_splitter_by_criteria_run(
            aries_task_input=sample_aries_task_input_order_flextrade_bell_potter_fix
        )

        ndjson_s3_uri_0: str = aries_task_result.output_param.params["dynamicTaskInputs"][
            "order_flextrade_bell_potter_fix_subworkflow_0"
        ]["io_param"]["params"]["file_uri"]

        ndjson_s3_uri_1: str = aries_task_result.output_param.params["dynamicTaskInputs"][
            "order_flextrade_bell_potter_fix_subworkflow_1"
        ]["io_param"]["params"]["file_uri"]

        assert aries_task_result.output_param.params == {
            "dynamicTasks": [
                {
                    "taskReferenceName": "order_flextrade_bell_potter_fix_subworkflow_0",
                    "type": "SUB_WORKFLOW",
                    "subWorkflowParam": {"name": "order_flextrade_bell_potter_fix_subworkflow"},
                },
                {
                    "taskReferenceName": "order_flextrade_bell_potter_fix_subworkflow_1",
                    "type": "SUB_WORKFLOW",
                    "subWorkflowParam": {"name": "order_flextrade_bell_potter_fix_subworkflow"},
                },
            ],
            "dynamicTaskInputs": {
                "order_flextrade_bell_potter_fix_subworkflow_0": {
                    "io_param": {
                        "params": {
                            "cache_parquet_uri": "s3://test.dev.steeleye.co/aries/ingest/file-splitter-by-criteria/2024/11/25/trace/file_splitter_by_criteria/fix_cache.parquet",
                            "batch_total": 2,
                            "file_uri": ndjson_s3_uri_0,
                            "batch": 0,
                        }
                    },
                    "workflow": {
                        "trace_id": "trace",
                        "start_timestamp": "2024-11-25T10:00:00.041934",
                        "name": "file-splitter-by-criteria",
                        "stack": "dev-shared-2",
                        "tenant": "test",
                    },
                },
                "order_flextrade_bell_potter_fix_subworkflow_1": {
                    "io_param": {
                        "params": {
                            "cache_parquet_uri": "s3://test.dev.steeleye.co/aries/ingest/file-splitter-by-criteria/2024/11/25/trace/file_splitter_by_criteria/fix_cache.parquet",
                            "batch_total": 2,
                            "file_uri": ndjson_s3_uri_1,
                            "batch": 1,
                        }
                    },
                    "workflow": {
                        "trace_id": "trace",
                        "start_timestamp": "2024-11-25T10:00:00.041934",
                        "name": "file-splitter-by-criteria",
                        "stack": "dev-shared-2",
                        "tenant": "test",
                    },
                },
            },
        }

        self.assert_frame(
            task_result=aries_task_result,
            dynamic_task_input="order_flextrade_bell_potter_fix_subworkflow_0",
            expected_path=END_TO_END_RESULT_PICKLE_0,
        )

        self.assert_frame(
            task_result=aries_task_result,
            dynamic_task_input="order_flextrade_bell_potter_fix_subworkflow_1",
            expected_path=END_TO_END_RESULT_PICKLE_1,
        )

    @patch.object(
        target=CachedTenantWorkflowAPIClient,
        attribute="get",
        return_value=addict.Dict(
            {
                "tenant": {
                    "cloud": "aws",
                    "lake_prefix": f"s3://{BUCKET_NAME}/",
                    "realm": "test.dev.steeleye.co",
                },
                "workflow": {
                    "streamed": False,
                    "name": OrderWorkflowNames.ORDER_FLEXTRADE_BELL_POTTER_FIX,
                    "tenant": "test",
                },
            },
        ),
    )
    @mock_aws
    @freeze_time(time_to_freeze="2024-05-22 06:59:38.911459+00:00")
    def test_replace_logic(
        self,
        mocker,
        sample_aries_task_input_order_flextrade_bell_potter_fix_replace_logic: AriesTaskInput,
    ):
        create_and_add_objects_to_s3_bucket(bucket_name=BUCKET_NAME, source_path=LOCAL_BUCKET_PATH)

        aries_task_result = file_splitter_by_criteria_run(
            aries_task_input=sample_aries_task_input_order_flextrade_bell_potter_fix_replace_logic
        )

        ndjson_s3_uri: str = aries_task_result.output_param.params["dynamicTaskInputs"][
            "order_flextrade_bell_potter_fix_subworkflow_0"
        ]["io_param"]["params"]["file_uri"]

        batch: str = aries_task_result.output_param.params["dynamicTaskInputs"][
            "order_flextrade_bell_potter_fix_subworkflow_0"
        ]["io_param"]["params"]["batch"]

        assert aries_task_result.output_param.params == {
            "dynamicTasks": [
                {
                    "taskReferenceName": "order_flextrade_bell_potter_fix_subworkflow_0",
                    "type": "SUB_WORKFLOW",
                    "subWorkflowParam": {"name": "order_flextrade_bell_potter_fix_subworkflow"},
                }
            ],
            "dynamicTaskInputs": {
                "order_flextrade_bell_potter_fix_subworkflow_0": {
                    "io_param": {
                        "params": {
                            "cache_parquet_uri": "s3://test.dev.steeleye.co/aries/ingest/file-splitter-by-criteria/2024/11/25/trace2/file_splitter_by_criteria/fix_cache.parquet",
                            "batch_total": 1,
                            "file_uri": ndjson_s3_uri,
                            "batch": batch,
                        }
                    },
                    "workflow": {
                        "trace_id": "trace2",
                        "start_timestamp": "2024-11-25T10:00:00.041934",
                        "name": "file-splitter-by-criteria",
                        "stack": "dev-shared-2",
                        "tenant": "test",
                    },
                }
            },
        }

        self.assert_frame(
            task_result=aries_task_result,
            dynamic_task_input="order_flextrade_bell_potter_fix_subworkflow_0",
            expected_path=REPLACE_LOGIC_RESULT_PICKLE,
        )

    @patch.object(
        target=CachedTenantWorkflowAPIClient,
        attribute="get",
        return_value=addict.Dict(
            {
                "tenant": {
                    "cloud": "aws",
                    "lake_prefix": f"s3://{BUCKET_NAME}/",
                    "realm": "test.dev.steeleye.co",
                },
                "workflow": {
                    "streamed": False,
                    "name": OrderWorkflowNames.ORDER_FLEXTRADE_BELL_POTTER_FIX,
                    "tenant": "test",
                },
            },
        ),
    )
    @mock_aws
    @freeze_time(time_to_freeze="2024-05-22 06:59:38.911459+00:00")
    def test_no_orders(
        self,
        mocker,
        sample_aries_task_input_order_flextrade_bell_potter_fix: AriesTaskInput,
        caplog,
    ):
        caplog.set_level(logging.INFO)

        create_and_add_objects_to_s3_bucket(bucket_name=BUCKET_NAME, source_path=LOCAL_BUCKET_PATH)

        sample_aries_task_input_order_flextrade_bell_potter_fix.workflow.start_timestamp = (
            datetime.datetime.strptime("2024-10-07T17:00:00.041934", "%Y-%m-%dT%H:%M:%S.%f")
        )

        aries_task_result = file_splitter_by_criteria_run(
            aries_task_input=sample_aries_task_input_order_flextrade_bell_potter_fix
        )

        assert (
            "No FIX files found to process. Searched on paths: "
            "['ingress/raw/order_flextrade_bell_potter_fix/20241007', 'ingress/raw/order_flextrade_bell_potter_fix/20241006']"  # noqa: E501
        ) in caplog.text

        assert aries_task_result.output_param.params == {
            "dynamicTasks": [],
            "dynamicTaskInputs": {},
        }

    @patch.object(
        target=CachedTenantWorkflowAPIClient,
        attribute="get",
        return_value=addict.Dict(
            {
                "tenant": {
                    "cloud": "aws",
                    "lake_prefix": f"s3://{BUCKET_NAME}/",
                    "realm": "test.dev.steeleye.co",
                },
                "workflow": {
                    "streamed": False,
                    "name": OrderWorkflowNames.ORDER_FLEXTRADE_BELL_POTTER_FIX,
                    "tenant": "test",
                },
            },
        ),
    )
    @mock_aws
    @freeze_time(time_to_freeze="2024-05-22 06:59:38.911459+00:00")
    def test_no_fix_file_chunks(
        self,
        mocker,
        sample_aries_task_input_order_flextrade_bell_potter_fix: AriesTaskInput,
        caplog,
    ):
        caplog.set_level(logging.INFO)

        create_and_add_objects_to_s3_bucket(bucket_name=BUCKET_NAME, source_path=LOCAL_BUCKET_PATH)

        sample_aries_task_input_order_flextrade_bell_potter_fix.workflow.start_timestamp = (
            datetime.datetime.strptime("2024-11-26T17:00:00.041934", "%Y-%m-%dT%H:%M:%S.%f")
        )

        aries_task_result = file_splitter_by_criteria_run(
            aries_task_input=sample_aries_task_input_order_flextrade_bell_potter_fix
        )

        assert "No Fix chunks Found after Pre-Processing" in caplog.text

        assert aries_task_result.output_param.params == {
            "dynamicTasks": [],
            "dynamicTaskInputs": {},
        }

    @staticmethod
    def assert_frame(task_result: AriesTaskResult, dynamic_task_input: str, expected_path: Path):
        split_path = task_result.output_param.params["dynamicTaskInputs"][dynamic_task_input][  # type: ignore[union-attr]
            "io_param"
        ]["params"]["file_uri"]

        split_local = run_download_file(file_url=split_path)
        split_df = pd.read_pickle(split_local)
        expected_split = pd.read_pickle(expected_path)

        pd.testing.assert_frame_equal(
            split_df.loc[:, sorted(split_df.columns)],
            expected_split.loc[:, sorted(expected_split.columns)],
        )
