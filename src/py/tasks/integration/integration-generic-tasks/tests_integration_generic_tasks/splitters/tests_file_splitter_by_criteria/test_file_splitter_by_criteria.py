import pytest
from addict import addict
from aries_config_cached_client.tenant_workflow import CachedTenantWorkflowAPIClient
from aries_task_link.models import AriesTaskInput
from integration_generic_tasks.splitters.file_splitter_by_criteria.file_splitter_by_criteria_flow import (  # noqa E501
    FailIfNoCriteria,
    file_splitter_by_criteria_flow,
)
from integration_test_utils.aws_helpers.patch_response import (
    mock_aiobotocore_convert_to_response_dict,
)
from mock.mock import patch
from moto import mock_aws
from pathlib import Path

CURRENT_PATH = Path(__file__).parent
RESULT_JSON_PATH = CURRENT_PATH.joinpath("result.json")

mock_aiobotocore_convert_to_response_dict()


class TestFileSplitterByCriteria:
    @patch.object(
        target=CachedTenantWorkflowAPIClient,
        attribute="get",
        return_value=addict.Dict(
            {
                "tenant": {
                    "lake_prefix": "s3://test.dev.steeleye.co",
                },
                "max_batch_size": 5,
                "workflow": {"streamed": False, "name": "not_found"},
            },
        ),
    )
    @mock_aws
    def test_non_existing_workflow(
        self,
        mocker,
        sample_aries_task_input_tr_bbg_emsi_orders: AriesTaskInput,
    ):
        with pytest.raises(FailIfNoCriteria) as e:
            file_splitter_by_criteria_flow(
                aries_task_input=sample_aries_task_input_tr_bbg_emsi_orders
            )

        assert e.match("No criteria found for: not_found")
