import addict
import filecmp
import fsspec
import json
import os
import pandas as pd
import pytest
from aries_se_core_tasks.core.generic_app_metrics_enum import GenericAppMetricsEnum
from aries_se_core_tasks.io.read.utils import split_list_of_quoted_strings_by_delimiter
from integration_generic_tasks.splitters.file_splitter_by_criteria.criteria.mymarket_universal_steeleye_person.mymarket_universal_steeleye_person_criteria import (  # noqa: E501
    _de_dupe_header,
    get_all_emails_from_input_line_with_header,
)
from integration_wrapper.static import StaticFields
from mymarket_tasks.feeds.person.universal_steeleye_person.static import (
    MYMARKET_PERSON_FLOW_NAME,
)

os.environ["DATA_PLATFORM_CONFIG_API_URL"] = "https://test-enterprise.steeleye.co"

import shutil
from aries_config_cached_client.tenant_workflow import CachedTenantWorkflowAPIClient
from freezegun import freeze_time
from integration_generic_tasks.splitters.file_splitter_by_criteria.file_splitter_by_criteria_task import (  # noqa E501
    file_splitter_by_criteria_run,
)
from integration_test_utils.aws_helpers.patch_response import (
    mock_aiobotocore_convert_to_response_dict,
)
from integration_test_utils.aws_helpers.s3_test_helpers import create_and_add_objects_to_s3_bucket
from moto import mock_aws
from pathlib import Path
from se_io_utils.json_utils import read_json, write_named_temporary_json
from se_io_utils.tempfile_utils import tmp_directory

BUCKET_NAME: str = "test.dev.steeleye.co"

CURRENT_PATH = Path(__file__).parent
LOCAL_BUCKET_PATH = CURRENT_PATH.joinpath("data/buckets", BUCKET_NAME)


EXPECTED_RESULTS_PATH = CURRENT_PATH.joinpath("data/expected_results")
OUTPUT_BATCH_1 = EXPECTED_RESULTS_PATH.joinpath("output_batch_1.csv")
QUOTED_OUTPUT_BATCH_1 = EXPECTED_RESULTS_PATH.joinpath("quoted_output_batch_1.csv")
OUTPUT_BATCH_2 = EXPECTED_RESULTS_PATH.joinpath("output_batch_2.csv")
QUOTED_OUTPUT_BATCH_2 = EXPECTED_RESULTS_PATH.joinpath("quoted_output_batch_2.csv")
OUTPUT_BATCH_ENCODING_1 = EXPECTED_RESULTS_PATH.joinpath("output_batch_encoding_1.csv")
OUTPUT_BATCH_ENCODING_2 = EXPECTED_RESULTS_PATH.joinpath("output_batch_encoding_2.csv")

TEMP_DIR: Path = tmp_directory()
AUDIT_PATH: Path = TEMP_DIR.joinpath("audit.json")

mock_aiobotocore_convert_to_response_dict()


@mock_aws
@freeze_time(time_to_freeze="2024-01-22 07:00:00.000000+00:00")
class TestMyMarketUniversalSteelEyePersonCriteria:
    @pytest.mark.parametrize("is_quoted", [False, True])
    def test_normal_execution(
        self,
        mocker,
        sample_input_market_person_criteria,
        sample_quoted_input_market_person_criteria,
        is_quoted: bool,
    ):
        parametrised_sample_input = (
            sample_quoted_input_market_person_criteria
            if is_quoted
            else sample_input_market_person_criteria
        )
        mock_config = mocker.patch.object(CachedTenantWorkflowAPIClient, "get")
        mock_config.return_value = addict.Dict(
            {
                "tenant": {
                    "cloud": "aws",
                    "lake_prefix": f"s3://{BUCKET_NAME}/",
                    "realm": "test.dev.steeleye.co",
                },
                "workflow": {
                    "streamed": False,
                    "name": MYMARKET_PERSON_FLOW_NAME,
                    "tenant": "test",
                },
                "max_batch_size": 5,
            },
        )

        create_and_add_objects_to_s3_bucket(bucket_name=BUCKET_NAME, source_path=LOCAL_BUCKET_PATH)

        # Force writing to a specific audit .json file, so that we can easily retrieve its contents.
        write_named_temporary_json_mock = mocker.patch(
            "integration_wrapper.integration_aries_task.write_named_temporary_json"
        )
        write_named_temporary_json_mock.side_effect = write_named_temporary_json_side_effect

        aries_task_result = file_splitter_by_criteria_run(
            aries_task_input=parametrised_sample_input
        )

        fs, _, (_,) = fsspec.get_fs_token_paths(f"s3://{BUCKET_NAME}")
        results_path = f"s3://{BUCKET_NAME}/aries/ingest/mymarket_universal_steeleye_person/2024/01/22/{parametrised_sample_input.workflow.trace_id}/file_splitter_by_criteria"
        result_files = fs.ls(results_path)
        final_result_files = []

        dir = f"{LOCAL_BUCKET_PATH}/aries/ingest/mymarket_universal_steeleye_person/2024/01/22/{parametrised_sample_input.workflow.trace_id}/file_splitter_by_criteria"  # noqa: E501
        os.makedirs(dir, exist_ok=True)

        for file in result_files:
            with fs.open(f"s3://{file}", "rb") as mock_f:
                content = mock_f.read()

            local_path = f"{LOCAL_BUCKET_PATH.as_posix().replace(BUCKET_NAME, '')}{file}"
            with open(local_path, "w") as f:
                f.write(content.decode())

            final_result_files.append(local_path)

        # From a total input of 10 lines, we expect 2 output files:
        # - The first batch has reached the max batch size of 5
        # - The second batch has the remaining valid lines: 1

        # 4 input records must be dropped throughout the Task.
        # 3 must be dropped because they either miss the FirstName,
        # LastName or both. Another record must be dropped because
        # it is a true duplicate, including FirstName, LastName and Emails.

        # NOTE: QUOTED_OUTPUT_BATCH_1 is almost the same as OUTPUT_BATCH_1,
        # however, the DEPARTMENT column is "Investment Bank (DIV-IB001),
        # with a comma" for the first row.
        # This is on purpose to test unquoting tabular files that use the same delimiter
        # within a column value, in this case a comma.
        output_1 = QUOTED_OUTPUT_BATCH_1 if is_quoted else OUTPUT_BATCH_1
        output_2 = QUOTED_OUTPUT_BATCH_2 if is_quoted else OUTPUT_BATCH_2

        assert filecmp.cmp(output_1, final_result_files[0], shallow=False)
        assert pd.read_csv(final_result_files[0]).shape[0] == 5

        assert filecmp.cmp(output_2, final_result_files[1], shallow=False)
        assert pd.read_csv(final_result_files[1]).shape[0] == 1

        shutil.rmtree(dir)

        metrics = (
            aries_task_result.app_metric.metrics[StaticFields.DATA_INTEGRATION_METRICS_PREFIX][
                "mymarket_universal_steeleye_person"
            ]["file_splitter_by_criteria"]
            if aries_task_result.app_metric
            else {}
        )

        assert metrics[GenericAppMetricsEnum.SKIPPED_COUNT] == 3
        assert metrics[GenericAppMetricsEnum.DUPLICATE_COUNT] == 2

        audit_result = read_json(AUDIT_PATH.as_posix())

        assert audit_result == {
            "input_records": {
                "<EMAIL>": {
                    "Person": {
                        "created": 0,
                        "duplicate": 2,
                        "errored": 0,
                        "skipped": 0,
                        "status": [
                            "Duplicate email or missing email with duplicate full name in file"
                        ],
                        "updated": 0,
                    }
                },
                "ROW: 7": {
                    "Person": {
                        "created": 0,
                        "duplicate": 0,
                        "errored": 0,
                        "skipped": 1,
                        "status": ["First name or last name is missed for this record"],
                        "updated": 0,
                    }
                },
                "ROW: 8": {
                    "Person": {
                        "created": 0,
                        "duplicate": 0,
                        "errored": 0,
                        "skipped": 1,
                        "status": ["First name or last name is missed for this record"],
                        "updated": 0,
                    }
                },
                "ROW: 9": {
                    "Person": {
                        "created": 0,
                        "duplicate": 0,
                        "errored": 0,
                        "skipped": 1,
                        "status": ["First name or last name is missed for this record"],
                        "updated": 0,
                    }
                },
            },
            "workflow_status": [],
        }
        assert aries_task_result.output_param.params == {  # type: ignore
            "dynamicTaskInputs": {
                "mymarket_person_subworkflow_0": {
                    "io_param": {
                        "params": {
                            "file_uri": f"s3://test.dev.steeleye.co/aries/ingest/mymarket_universal_steeleye_person/2024/01/22/{parametrised_sample_input.workflow.trace_id}/file_splitter_by_criteria/output_batch_1.csv"
                        }
                    },
                    "workflow": {
                        "name": "mymarket_universal_steeleye_person",
                        "stack": "dev-shared-2",
                        "start_timestamp": "2024-01-22T00:00:00",
                        "tenant": "test",
                        "trace_id": f"{parametrised_sample_input.workflow.trace_id}",
                    },
                },
                "mymarket_person_subworkflow_1": {
                    "io_param": {
                        "params": {
                            "file_uri": f"s3://test.dev.steeleye.co/aries/ingest/mymarket_universal_steeleye_person/2024/01/22/{parametrised_sample_input.workflow.trace_id}/file_splitter_by_criteria/output_batch_2.csv"
                        }
                    },
                    "workflow": {
                        "name": "mymarket_universal_steeleye_person",
                        "stack": "dev-shared-2",
                        "start_timestamp": "2024-01-22T00:00:00",
                        "tenant": "test",
                        "trace_id": f"{parametrised_sample_input.workflow.trace_id}",
                    },
                },
            },
            "dynamicTasks": [
                {
                    "subWorkflowParam": {"name": "mymarket_person_subworkflow"},
                    "taskReferenceName": "mymarket_person_subworkflow_0",
                    "type": "SUB_WORKFLOW",
                },
                {
                    "subWorkflowParam": {"name": "mymarket_person_subworkflow"},
                    "taskReferenceName": "mymarket_person_subworkflow_1",
                    "type": "SUB_WORKFLOW",
                },
            ],
        }

    def test_normal_execution_with_encoding(
        self,
        mocker,
        sample_input_market_with_encoding_person_criteria,
    ):
        mock_config = mocker.patch.object(CachedTenantWorkflowAPIClient, "get")
        mock_config.return_value = addict.Dict(
            {
                "tenant": {
                    "cloud": "aws",
                    "lake_prefix": f"s3://{BUCKET_NAME}/",
                    "realm": "test.dev.steeleye.co",
                },
                "workflow": {
                    "streamed": False,
                    "name": MYMARKET_PERSON_FLOW_NAME,
                    "tenant": "test",
                },
                "max_batch_size": 5,
            },
        )
        trace_id = sample_input_market_with_encoding_person_criteria.workflow.trace_id

        create_and_add_objects_to_s3_bucket(bucket_name=BUCKET_NAME, source_path=LOCAL_BUCKET_PATH)

        # Force writing to a specific audit .json file, so that we can easily retrieve its contents.
        write_named_temporary_json_mock = mocker.patch(
            "integration_wrapper.integration_aries_task.write_named_temporary_json"
        )
        write_named_temporary_json_mock.side_effect = write_named_temporary_json_side_effect
        # Load the original test input from a .pkl file
        # (to avoid encoding issues with Pants and UTF-8 validation)
        df = pd.read_pickle(
            sample_input_market_with_encoding_person_criteria.input_param.params["file_uri"]
        )
        # Convert the DataFrame to a CSV file using ISO-8859-1 encoding
        # This simulates a real-world scenario where uploaded files may not use UTF-8
        csv_path = sample_input_market_with_encoding_person_criteria.input_param.params[
            "file_uri"
        ].replace("pkl", "csv")
        df.to_csv(csv_path, encoding="iso-8859-1")
        # Update the input parameter to point to the newly created CSV file

        sample_input_market_with_encoding_person_criteria.input_param.params["file_uri"] = csv_path

        aries_task_result = file_splitter_by_criteria_run(
            aries_task_input=sample_input_market_with_encoding_person_criteria
        )
        if os.path.exists(csv_path):
            os.remove(csv_path)

        fs, _, (_,) = fsspec.get_fs_token_paths(f"s3://{BUCKET_NAME}")
        results_path = f"s3://{BUCKET_NAME}/aries/ingest/mymarket_universal_steeleye_person/2024/01/22/{trace_id}/file_splitter_by_criteria"
        result_files = fs.ls(results_path)
        final_result_files = []

        dir = f"{LOCAL_BUCKET_PATH}/aries/ingest/mymarket_universal_steeleye_person/2024/01/22/{trace_id}/file_splitter_by_criteria"  # noqa: E501
        os.makedirs(dir, exist_ok=True)
        for file in result_files:
            with fs.open(f"s3://{file}", "rb") as mock_f:
                content = mock_f.read()

            local_path = f"{LOCAL_BUCKET_PATH.as_posix().replace(BUCKET_NAME, '')}{file}"
            # encoding_new  = get_encoding_from_file(Path(local_path))
            with open(local_path, "w") as f:
                f.write(content.decode())

            final_result_files.append(local_path)

        # From a total input of 10 lines, we expect 2 output files:
        # - The first batch has reached the max batch size of 5
        # - The second batch has the remaining valid lines: 1

        # 4 input records must be dropped throughout the Task.
        # 3 must be dropped because they either miss the FirstName,
        # LastName or both. Another record must be dropped because
        # it is a true duplicate, including FirstName, LastName and Emails.

        # NOTE: QUOTED_OUTPUT_BATCH_1 is almost the same as OUTPUT_BATCH_1,
        # however, the DEPARTMENT column is "Investment Bank (DIV-IB001),
        # with a comma" for the first row.
        # This is on purpose to test unquoting tabular files that use the same delimiter
        # within a column value, in this case a comma.
        #

        assert filecmp.cmp(OUTPUT_BATCH_ENCODING_1, final_result_files[0], shallow=False)
        assert pd.read_csv(final_result_files[0]).shape[0] == 5

        assert filecmp.cmp(OUTPUT_BATCH_ENCODING_2, final_result_files[1], shallow=False)
        assert pd.read_csv(final_result_files[1]).shape[0] == 1

        shutil.rmtree(dir)
        #
        metrics = (
            aries_task_result.app_metric.metrics[StaticFields.DATA_INTEGRATION_METRICS_PREFIX][
                "mymarket_universal_steeleye_person"
            ]["file_splitter_by_criteria"]
            if aries_task_result.app_metric
            else {}
        )
        #
        assert metrics[GenericAppMetricsEnum.SKIPPED_COUNT] == 3
        assert metrics[GenericAppMetricsEnum.DUPLICATE_COUNT] == 2
        #
        audit_result = read_json(AUDIT_PATH.as_posix())
        #
        assert audit_result == {
            "input_records": {
                "<EMAIL>": {
                    "Person": {
                        "created": 0,
                        "duplicate": 2,
                        "errored": 0,
                        "skipped": 0,
                        "status": [
                            "Duplicate email or missing email with duplicate full name in file"
                        ],
                        "updated": 0,
                    }
                },
                "ROW: 10": {
                    "Person": {
                        "created": 0,
                        "duplicate": 0,
                        "errored": 0,
                        "skipped": 1,
                        "status": ["First name or last name is missed for this record"],
                        "updated": 0,
                    }
                },
                "ROW: 8": {
                    "Person": {
                        "created": 0,
                        "duplicate": 0,
                        "errored": 0,
                        "skipped": 1,
                        "status": ["First name or last name is missed for this record"],
                        "updated": 0,
                    }
                },
                "ROW: 9": {
                    "Person": {
                        "created": 0,
                        "duplicate": 0,
                        "errored": 0,
                        "skipped": 1,
                        "status": ["First name or last name is missed for this record"],
                        "updated": 0,
                    }
                },
            },
            "workflow_status": [],
        }
        assert aries_task_result.output_param.params == {  # type: ignore
            "dynamicTaskInputs": {
                "mymarket_person_subworkflow_0": {
                    "io_param": {
                        "params": {
                            "file_uri": f"s3://test.dev.steeleye.co/aries/ingest/mymarket_universal_steeleye_person/2024/01/22/{trace_id}/file_splitter_by_criteria/output_batch_1.csv"
                        }
                    },
                    "workflow": {
                        "name": "mymarket_universal_steeleye_person",
                        "stack": "dev-shared-2",
                        "start_timestamp": "2024-01-22T00:00:00",
                        "tenant": "test",
                        "trace_id": f"{trace_id}",
                    },
                },
                "mymarket_person_subworkflow_1": {
                    "io_param": {
                        "params": {
                            "file_uri": f"s3://test.dev.steeleye.co/aries/ingest/mymarket_universal_steeleye_person/2024/01/22/{trace_id}/file_splitter_by_criteria/output_batch_2.csv"
                        }
                    },
                    "workflow": {
                        "name": "mymarket_universal_steeleye_person",
                        "stack": "dev-shared-2",
                        "start_timestamp": "2024-01-22T00:00:00",
                        "tenant": "test",
                        "trace_id": f"{trace_id}",
                    },
                },
            },
            "dynamicTasks": [
                {
                    "subWorkflowParam": {"name": "mymarket_person_subworkflow"},
                    "taskReferenceName": "mymarket_person_subworkflow_0",
                    "type": "SUB_WORKFLOW",
                },
                {
                    "subWorkflowParam": {"name": "mymarket_person_subworkflow"},
                    "taskReferenceName": "mymarket_person_subworkflow_1",
                    "type": "SUB_WORKFLOW",
                },
            ],
        }


def test_de_dupe_header():
    header = ["foo", "bar", "bar", "abc", "foo", "bar"]
    expected_header = ["FOO_0", "BAR_1", "BAR_2", "ABC", "FOO_4", "BAR_5"]

    result = _de_dupe_header(header=header)
    assert result == expected_header


def test_get_all_emails_from_input_line_with_header():
    input = {
        "foo": 3,
        "bar": 4,
        "EMAIL_2": "email1",
        "abc": 5,
        "EMAIL_4": "email3",
        "EMAIL_5": "email2",
    }
    expected_result = "email1,email2,email3"

    result = get_all_emails_from_input_line_with_header(input_line_with_header=input)
    assert result == expected_result


def write_named_temporary_json_side_effect(output_filename: str, **kwargs) -> str:
    if output_filename == "audit.json":
        with fsspec.open(AUDIT_PATH.as_posix(), "w") as file:
            json.dump({}, file)

        return AUDIT_PATH.as_posix()

    return write_named_temporary_json(output_filename=output_filename, **kwargs)


def test_split_list_of_quoted_strings_by_delimiter():
    delimiter = ";"

    raw_line = "'foo';'bar';'';'baz;qux'"
    use_single_quotes = True

    result = split_list_of_quoted_strings_by_delimiter(
        delimiter=delimiter, raw_line=raw_line, use_single_quotes=use_single_quotes
    )
    assert result == ["'foo'", "'bar'", "''", "'baz;qux'"]

    raw_line = '"foo";"bar";"";"baz;qux"'
    use_single_quotes = False

    result = split_list_of_quoted_strings_by_delimiter(
        delimiter=delimiter, raw_line=raw_line, use_single_quotes=use_single_quotes
    )
    assert result == ['"foo"', '"bar"', '""', '"baz;qux"']
