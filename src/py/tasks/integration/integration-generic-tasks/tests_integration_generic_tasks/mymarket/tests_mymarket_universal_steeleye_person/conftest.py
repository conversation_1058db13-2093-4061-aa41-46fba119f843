import datetime as dt
import os
import pytest
from aries_io_event.io_param import IOParamFieldSet
from aries_io_event.task import TaskFieldSet
from aries_io_event.workflow import WorkflowFieldSet
from aries_task_link.models import AriesTaskInput
from integration_wrapper.static import IntegrationAriesTaskVariables
from pathlib import Path

# This needs to be here since the conftest is loaded first
# than any import on the test files
os.environ[IntegrationAriesTaskVariables.DATA_PLATFORM_CONFIG_API_URL] = (
    "https://test-enterprise.steeleye.co"
)
os.environ["COGNITO_CLIENT_ID"] = "some_id"
os.environ["COGNITO_CLIENT_SECRET"] = "some_secret"
os.environ["COGNITO_AUTH_URL"] = "some_auth_url"
os.environ["MASTER_DATA_HOST"] = "some_host"
os.environ["MASTER_API_DATA_HOST"] = "some_host"

SCENARIO_PATH = f"{Path(__file__).parent}/data/valid_executions/scenario_aws"


@pytest.fixture()
def sample_aries_task_input_aws() -> AriesTaskInput:
    workflow = WorkflowFieldSet(
        trace_id="test_mymarket_universal_steeleye_person",
        name="mymarket_universal_steeleye_person",
        stack="dev-blue",
        tenant="test",
        start_timestamp=dt.datetime.utcnow(),
    )
    input_param = IOParamFieldSet(
        params=dict(
            file_uri="s3://test.dev.steeleye.co/aws_mymarket_universal_steeleye_person.csv",
        )
    )
    task = TaskFieldSet(name="mymarket_universal_steeleye_person", version="latest", success=False)
    return AriesTaskInput(workflow=workflow, input_param=input_param, task=task)


@pytest.fixture()
def expected_audit_result():
    return {
        "input_records": {
            "Brock Lee|<EMAIL>|<EMAIL>": {
                "MarketPerson": {
                    "created": 0,
                    "duplicate": 0,
                    "errored": 0,
                    "skipped": 1,
                    "status": ["Keep original record rule"],
                    "updated": 0,
                }
            },
            "Chuck Wagon|<EMAIL>": {
                "AccountPerson": {
                    "created": 0,
                    "duplicate": 0,
                    "errored": 0,
                    "skipped": 1,
                    "status": ["Keep original record rule"],
                    "updated": 0,
                }
            },
        },
        "workflow_status": [],
    }
