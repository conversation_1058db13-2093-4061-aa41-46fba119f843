import boto3
import fsspec
import json
import pandas as pd
import pytest
from addict import addict
from aries_config_cached_client.tenant_workflow import CachedTenantWorkflowAPIClient
from aries_se_core_tasks.io.read.cloud.download_file import run_download_file
from aries_task_link.models import AriesTaskInput, AriesTaskResult
from freezegun import freeze_time
from integration_test_utils.aws_helpers.patch_response import (
    mock_aiobotocore_convert_to_response_dict,
)
from integration_trades_tasks.order.feeds.order_blotter_converter.order_blotter_converter_task import (  # noqa: E501
    order_blotter_converter_run,
)
from moto import mock_aws
from pathlib import Path
from se_io_utils.json_utils import read_json, write_named_temporary_json
from se_io_utils.tempfile_utils import tmp_directory
from typing import Any, Tuple

CURRENT_PATH = Path(__file__).parent
DATA_PATH = CURRENT_PATH.joinpath("data")
BUCKET_NAME = "test.dev.steeleye.co"
LOCAL_BUCKET_PATH = DATA_PATH.joinpath("buckets", BUCKET_NAME)
EXPECTED_OUTPUT_FILE = DATA_PATH.joinpath("expected", "expected_output_file.csv")
TEMP_DIR: Path = tmp_directory()
AUDIT_PATH: Path = TEMP_DIR.joinpath("audit.json")

mock_aiobotocore_convert_to_response_dict()


class TestVirtuTritonOrderBlotterConverter:
    @pytest.mark.parametrize(
        "batch_size",
        [
            500,
            30,
        ],
    )
    @mock_aws
    def test_end_to_end(self, mocker, aries_task_input_virtu_triton: AriesTaskInput, batch_size):
        aries_task_result, s3 = self._run_aries_task(
            mocker=mocker,
            aries_task_input=aries_task_input_virtu_triton,
            batch_size=batch_size,
        )

        objects = s3.list_objects_v2(
            Bucket=BUCKET_NAME, Prefix="aries/ingress/nonstreamed/evented/order_blotter/"
        )

        assert len(objects["Contents"]) == 1

        local_result_file = run_download_file(
            file_url=f"s3://{BUCKET_NAME}/aries/ingress/nonstreamed/evented/"
            f"order_blotter/flow_trigger/2025/09/10/trace_id/sample_virtu_triton.csv"
        )

        result_frame = pd.read_csv(local_result_file)

        expected_frame = pd.read_csv(EXPECTED_OUTPUT_FILE)
        pd.testing.assert_frame_equal(result_frame, expected_frame)

        assert (
            aries_task_result.app_metric.metrics.get("dit")
            .get("order_blotter_converter")
            .get("order_blotter_converter")
            .get("skipped_count")
        ) == 6

        audit_result = read_json(AUDIT_PATH.as_posix())

        assert audit_result == {
            "input_records": {
                "4|382271509|Filled|S": {
                    "Order": {
                        "created": 0,
                        "errored": 0,
                        "skipped": 1,
                        "duplicate": 0,
                        "updated": 0,
                        "status": [
                            "Skipped due to Order Status == 'Filled' and Price and Traded Quantity == 0"  # noqa: E501
                        ],
                    }
                },
                "5|382271509|Filled|S": {
                    "Order": {
                        "created": 0,
                        "errored": 0,
                        "skipped": 1,
                        "duplicate": 0,
                        "updated": 0,
                        "status": [
                            "Skipped due to Order Status == 'Filled' and Price and Traded Quantity == 0"  # noqa: E501
                        ],
                    }
                },
                "10|382271501|Filled|S": {
                    "Order": {
                        "created": 0,
                        "errored": 0,
                        "skipped": 1,
                        "duplicate": 0,
                        "updated": 0,
                        "status": [
                            "Skipped due to Order Status == 'Filled' and Price and Traded Quantity == 0"  # noqa: E501
                        ],
                    }
                },
                "13|382271501|Filled|S": {
                    "Order": {
                        "created": 0,
                        "errored": 0,
                        "skipped": 1,
                        "duplicate": 0,
                        "updated": 0,
                        "status": [
                            "Skipped due to Order Status == 'Filled' and Price and Traded Quantity == 0"  # noqa: E501
                        ],
                    }
                },
                "29|382271511|Partial|B": {
                    "Order": {
                        "created": 0,
                        "errored": 0,
                        "skipped": 1,
                        "duplicate": 0,
                        "updated": 0,
                        "status": [
                            "Skipped due to Order Status in [Overfilled, Partial, PartialDone, Summary] and Price and Traded Quantity == 0"  # noqa: E501
                        ],
                    }
                },
                "30|382271511|Partial|B": {
                    "Order": {
                        "created": 0,
                        "errored": 0,
                        "skipped": 1,
                        "duplicate": 0,
                        "updated": 0,
                        "status": [
                            "Skipped due to Order Status in [Overfilled, Partial, PartialDone, Summary] and Price and Traded Quantity == 0"  # noqa: E501
                        ],
                    }
                },
            },
            "workflow_status": [],
        }

    @staticmethod
    @mock_aws
    @freeze_time(time_to_freeze="2025-09-10 08:00:00.000000+00:00")
    def _run_aries_task(
        mocker, aries_task_input: AriesTaskInput, batch_size: int
    ) -> Tuple[AriesTaskResult, Any]:
        # Create mock S3 bucket and add objects to it
        s3 = create_and_add_objects_to_s3_bucket(bucket_name=BUCKET_NAME)

        mock_task = mocker.patch(
            "integration_wrapper.integration_aries_task.write_named_temporary_json"
        )
        mock_task.side_effect = write_named_temporary_json_side_effect
        mocker.patch("integration_audit.auditor.write_json")

        mocker.patch.object(
            target=CachedTenantWorkflowAPIClient,
            attribute="get",
            return_value=addict.Dict(
                {
                    "tenant": {
                        "lake_prefix": "s3://test.dev.steeleye.co",
                    },
                    "workflow": {"streamed": False},
                    "max_batch_size": batch_size,
                },
            ),
        )

        # Run flow
        return order_blotter_converter_run(aries_task_input=aries_task_input), s3


def create_and_add_objects_to_s3_bucket(bucket_name: str):
    """Creates a mock s3 bucket and copies the input file(s) to it.

    :param bucket_name: Bucket name of Mock S3 bucket
    :return: None
    """

    # Create bucket
    s3 = boto3.client("s3", region_name="us-east-1")
    s3.create_bucket(Bucket=bucket_name)

    for file_ in LOCAL_BUCKET_PATH.rglob("*"):
        if file_.is_file():
            _path = file_.as_posix().replace(f"{LOCAL_BUCKET_PATH}/", "")

            with open(file_, "rb") as f:
                s3.put_object(Bucket=bucket_name, Key=_path, Body=f.read())

    return s3


def write_named_temporary_json_side_effect(output_filename: str, **kwargs) -> str:
    if output_filename == "audit.json":
        with fsspec.open(AUDIT_PATH.as_posix(), "w") as file:
            json.dump({}, file)

        return AUDIT_PATH.as_posix()

    return write_named_temporary_json(output_filename=output_filename, **kwargs)
