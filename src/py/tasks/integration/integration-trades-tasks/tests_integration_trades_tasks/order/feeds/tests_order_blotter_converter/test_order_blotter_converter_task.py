import pytest
from aries_task_link.models import AriesTaskInput
from integration_trades_tasks.order.feeds.order_blotter_converter.abstract_order_blotter_converter_flow import (  # noqa:E501
    order_blotter_converter_flow,
)
from integration_trades_tasks.order.feeds.order_blotter_converter.handlers.pad.pad_order_blotter_converter_flow import (  # noqa:E501
    PadOrderBlotterConverter,
)
from integration_trades_tasks.order.feeds.order_blotter_converter.order_blotter_converter_task import (  # noqa:E501
    order_blotter_converter_run,
)
from integration_wrapper.integration_aries_task import IntegrationAriesTask
from mock import patch


class TestOrderBlotterConverterTask:
    def test_no_handler_detected(self, aries_task_input: AriesTaskInput):
        aries_task_input.input_param.params["file_uri"] = (
            "s3://test.dev.steeleye.co/aries/ingress/"
            "nonstreamed/evented/order_blotter_converter/fake_handler/2025/09/10/sample.csv"
        )

        with pytest.raises(Exception) as e:
            order_blotter_converter_run(aries_task_input=aries_task_input)

        assert e.match(
            "Unexpected file uri: s3:/test.dev.steeleye.co/aries/ingress/nonstreamed/"
            "evented/order_blotter_converter/fake_handler/2025/09/10/"
            "sample.csv.\nDetected handlers: \\[\\]"
        )

    @patch(
        target="integration_trades_tasks.order.feeds.order_blotter_converter.order_blotter_converter_task.get_handlers",
        return_value={
            "fake_handler_1": PadOrderBlotterConverter,
            "fake_handler_2": PadOrderBlotterConverter,
        },
    )
    def test_multiple_handler_detected(self, mock, aries_task_input: AriesTaskInput):
        aries_task_input.input_param.params["file_uri"] = (
            "s3://test.dev.steeleye.co/aries/ingress/"
            "nonstreamed/evented/order_blotter_converter/fake_handler_1/fake_handler_2/2025/09/10/sample.csv"
        )

        with pytest.raises(Exception) as e:
            order_blotter_converter_run(aries_task_input=aries_task_input)

        assert e.match(
            "Unexpected file uri: s3:/test.dev.steeleye.co/aries/ingress/nonstreamed/evented/"
            "order_blotter_converter/fake_handler_1/fake_handler_2/2025/09/10/"
            "sample.csv.\nDetected handlers: \\['fake_handler_1', 'fake_handler_2'\\]"
        )

    def test_choosing_handler(self, aries_task_input: AriesTaskInput):
        with patch.object(IntegrationAriesTask, "execute") as mock_execute:
            order_blotter_converter_run(aries_task_input=aries_task_input)

            mock_execute.assert_called_with(
                flow=order_blotter_converter_flow,
                flow_override_class=PadOrderBlotterConverter,
            )

    def test_choosing_handler_casing(self, aries_task_input: AriesTaskInput):
        aries_task_input.input_param.params["file_uri"] = (
            "s3://test.dev.steeleye.co/aries/ingress/"
            "nonstreamed/evented/order_blotter_converter/pad/2025/09/10/sample.csv"
        )

        with patch.object(IntegrationAriesTask, "execute") as mock_execute:
            order_blotter_converter_run(aries_task_input=aries_task_input)

            mock_execute.assert_called_with(
                flow=order_blotter_converter_flow,
                flow_override_class=PadOrderBlotterConverter,
            )
