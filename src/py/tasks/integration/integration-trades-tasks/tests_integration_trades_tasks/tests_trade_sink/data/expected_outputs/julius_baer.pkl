��Y3      �pandas.core.frame��	DataFrame���)��}�(�_mgr��pandas.core.internals.managers��BlockManager���(�pandas._libs.internals��_unpickle_block����numpy.core.numeric��_frombuffer���(�                                ��numpy��dtype����b1�����R�(K�|�NNNJ����J����K t�bKK���C�t�R�h(�               )       +       8       �h�i8�����R�(K�<�NNNJ����J����K t�bK��ht�R�K��R�hh(�               33333sN@        ��(\�2D@        ��(\�2D@        ��(\�2D@        33333sN@        ��(\�2D@        ��(\�2D@        ��(\�2D@             �q@              Y@              Y@             �R@              �              �              �              �     �q@     �q@      Y@      Y@      Y@      Y@     �R@     �R@             �q@              Y@              Y@             �R@        33333sN@        ��(\�2D@        ��(\�2D@        ��(\�2D@      �              �              �              �        �h�f8�����R�(Kh NNNJ����J����K t�bKK��ht�R�h(�@                                   "       #       $       ;       �hK��ht�R�K��R�h�numpy.core.multiarray��_reconstruct���h�ndarray���K ��Cb���R�(KK0K��h�O8�����R�(KhNNNJ����J����K?t�b�]�(]�}�(�labelId��AU0000297962��path��instrumentDetails.instrument��type��se_elastic_schema.static.market��IdentifierType����OBJECT���R�ua]�}�(hFhGhHhIhJhPua]�}�(hF�US6516391066�hHhIhJhPua]�}�(hFhUhHhIhJhPua]�}�(hF�US6516391066�hHhIhJhPua]�}�(hFhZhHhIhJhPua]�}�(hF�US6516391066�hHhIhJhPua]�}�(hFh_hHhIhJhPua�BJCHBAU23389000100000001��BJCHBAU23389000100000001��BJCHBAU23389054790000001��BJCHBAU23389054790000001��BJCHBAU23389054790000002��BJCHBAU23389054790000002��BJCHBAU23389054790000003��BJCHBAU23389054790000003��2�hj�1�hkhkhkhkhk�
2023-12-04��
2023-12-04��
2023-12-05��
2023-12-05��
2023-12-05��
2023-12-05��
2023-12-05��
2023-12-05�]�(}�(hF�lei:2138008neory2dqv4r24�hH�reportDetails.executingEntity�hJhPu}�(hF�
id:inv.dec.id�hH�trader�hJhM�ARRAY���R�u}�(hF�lei:pnwu8o0blt17bbv61y18�hH�buyer�hJh}u}�(hF�id:03189544�hH�seller�hJh}u}�(hF�lei:2138008neory2dqv4r24�hH�sellerDecisionMaker�hJh}u}�(hF�lei:pnwu8o0blt17bbv61y18�hH�counterparty�hJhPu}�(hF�	id:u00901�hH�1tradersAlgosWaiversIndicators.executionWithinFirm�hJhPu}�(hF�
id:inv.dec.id�hH�:tradersAlgosWaiversIndicators.investmentDecisionWithinFirm�hJhPue]�(}�(hF�lei:2138008neory2dqv4r24�hHhwhJhPu}�(hF�
id:inv.dec.id�hHhzhJh}u}�(hF�lei:pnwu8o0blt17bbv61y18�hHh�hJh}u}�(hF�id:03189544�hHh�hJh}u}�(hF�lei:2138008neory2dqv4r24�hHh�hJh}u}�(hF�lei:pnwu8o0blt17bbv61y18�hHh�hJhPu}�(hF�	id:u00901�hHh�hJhPu}�(hF�
id:inv.dec.id�hHh�hJhPue]�(}�(hF�lei:2138008neory2dqv4r24�hHhwhJhPu}�(hF�
id:inv.dec.id�hHhzhJh}u}�(hF�id:03189544�hHh�hJh}u}�(hF�lei:2138008neory2dqv4r24�hH�buyerDecisionMaker�hJh}u}�(hF�lei:pnwu8o0blt17bbv61y18�hHh�hJh}u}�(hF�lei:pnwu8o0blt17bbv61y18�hHh�hJhPu}�(hF�	id:u00901�hHh�hJhPu}�(hF�
id:inv.dec.id�hHh�hJhPue]�(}�(hF�lei:2138008neory2dqv4r24�hHhwhJhPu}�(hF�
id:inv.dec.id�hHhzhJh}u}�(hF�id:03189544�hHh�hJh}u}�(hF�lei:2138008neory2dqv4r24�hHh�hJh}u}�(hF�lei:pnwu8o0blt17bbv61y18�hHh�hJh}u}�(hF�lei:pnwu8o0blt17bbv61y18�hHh�hJhPu}�(hF�	id:u00901�hHh�hJhPu}�(hF�
id:inv.dec.id�hHh�hJhPue]�(}�(hF�lei:2138008neory2dqv4r24�hHhwhJhPu}�(hF�
id:inv.dec.id�hHhzhJh}u}�(hF�id:03189544�hHh�hJh}u}�(hF�lei:2138008neory2dqv4r24�hHh�hJh}u}�(hF�lei:pnwu8o0blt17bbv61y18�hHh�hJh}u}�(hF�lei:pnwu8o0blt17bbv61y18�hHh�hJhPu}�(hF�	id:u00901�hHh�hJhPu}�(hF�
id:inv.dec.id�hHh�hJhPue]�(}�(hF�lei:2138008neory2dqv4r24�hHhwhJhPu}�(hF�
id:inv.dec.id�hHhzhJh}u}�(hF�id:03189544�hHh�hJh}u}�(hF�lei:2138008neory2dqv4r24�hHh�hJh}u}�(hF�lei:pnwu8o0blt17bbv61y18�hHh�hJh}u}�(hF�lei:pnwu8o0blt17bbv61y18�hHh�hJhPu}�(hF�	id:u00901�hHh�hJhPu}�(hF�
id:inv.dec.id�hHh�hJhPue]�(}�(hF�lei:2138008neory2dqv4r24�hHhwhJhPu}�(hF�
id:inv.dec.id�hHhzhJh}u}�(hF�id:03189544�hHh�hJh}u}�(hF�lei:2138008neory2dqv4r24�hHh�hJh}u}�(hF�lei:pnwu8o0blt17bbv61y18�hHh�hJh}u}�(hF�lei:pnwu8o0blt17bbv61y18�hHh�hJhPu}�(hF�	id:u00901�hHh�hJhPu}�(hF�
id:inv.dec.id�hHh�hJhPue]�(}�(hF�lei:2138008neory2dqv4r24�hHhwhJhPu}�(hF�
id:inv.dec.id�hHhzhJh}u}�(hF�id:03189544�hHh�hJh}u}�(hF�lei:2138008neory2dqv4r24�hHh�hJh}u}�(hF�lei:pnwu8o0blt17bbv61y18�hHh�hJh}u}�(hF�lei:pnwu8o0blt17bbv61y18�hHh�hJhPu}�(hF�	id:u00901�hHh�hJhPu}�(hF�
id:inv.dec.id�hHh�hJhPue�
id:inv.dec.id��
id:inv.dec.id��
id:inv.dec.id��
id:inv.dec.id��
id:inv.dec.id��
id:inv.dec.id��
id:inv.dec.id��
id:inv.dec.id��lei:pnwu8o0blt17bbv61y18��lei:pnwu8o0blt17bbv61y18��lei:pnwu8o0blt17bbv61y18��lei:pnwu8o0blt17bbv61y18��lei:pnwu8o0blt17bbv61y18��lei:pnwu8o0blt17bbv61y18��lei:pnwu8o0blt17bbv61y18��lei:pnwu8o0blt17bbv61y18��lei:pnwu8o0blt17bbv61y18��lei:pnwu8o0blt17bbv61y18��id:03189544��id:03189544��id:03189544��id:03189544��id:03189544��id:03189544��id:03189544��id:03189544��lei:pnwu8o0blt17bbv61y18��lei:pnwu8o0blt17bbv61y18��lei:pnwu8o0blt17bbv61y18��lei:pnwu8o0blt17bbv61y18��lei:pnwu8o0blt17bbv61y18��lei:pnwu8o0blt17bbv61y18��lei:2138008neory2dqv4r24��lei:2138008neory2dqv4r24�G�      G�      G�      G�      G�      G�      �lei:2138008neory2dqv4r24��lei:2138008neory2dqv4r24��lei:2138008neory2dqv4r24��lei:2138008neory2dqv4r24��lei:2138008neory2dqv4r24��lei:2138008neory2dqv4r24��lei:2138008neory2dqv4r24��lei:2138008neory2dqv4r24��pnwu8o0blt17bbv61y18��pnwu8o0blt17bbv61y18��03189544��03189544��03189544��03189544��03189544��03189544��03189544��03189544��pnwu8o0blt17bbv61y18��pnwu8o0blt17bbv61y18��pnwu8o0blt17bbv61y18��pnwu8o0blt17bbv61y18��pnwu8o0blt17bbv61y18��pnwu8o0blt17bbv61y18��pnwu8o0blt17bbv61y18��pnwu8o0blt17bbv61y18��pnwu8o0blt17bbv61y18��pnwu8o0blt17bbv61y18��pnwu8o0blt17bbv61y18��pnwu8o0blt17bbv61y18��pnwu8o0blt17bbv61y18��pnwu8o0blt17bbv61y18��2138008neory2dqv4r24��2138008neory2dqv4r24�G�      G�      G�      G�      G�      G�      �
inv.dec.id��
inv.dec.id��
inv.dec.id��
inv.dec.id��
inv.dec.id��
inv.dec.id��
inv.dec.id��
inv.dec.id��Order�jT  jT  jT  jT  jT  jT  jT  �is3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/julius_baer/mifir_03189576_20231206.csv��is3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/julius_baer/mifir_03189576_20231206.csv��is3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/julius_baer/mifir_03189576_20231206.csv��is3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/julius_baer/mifir_03189576_20231206.csv��is3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/julius_baer/mifir_03189576_20231206.csv��is3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/julius_baer/mifir_03189576_20231206.csv��is3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/julius_baer/mifir_03189576_20231206.csv��is3://temp.dev.steeleye.co/aries/ingress/nonstreamed/trade_sink_ns/julius_baer/mifir_03189576_20231206.csv��0��0��1��1��2��2��3��3��Thornbridge Julius Baer�je  je  je  je  je  je  je  G�      G�      �lei:2138008neory2dqv4r24��lei:2138008neory2dqv4r24��lei:2138008neory2dqv4r24��lei:2138008neory2dqv4r24��lei:2138008neory2dqv4r24��lei:2138008neory2dqv4r24�G�      G�      �2138008neory2dqv4r24��2138008neory2dqv4r24��2138008neory2dqv4r24��2138008neory2dqv4r24��2138008neory2dqv4r24��2138008neory2dqv4r24��AUD��AUD��USD��USD��USD��USD��USD��USD��SELL�jz  �BUYI�j{  j{  j{  j{  j{  �2023-12-04T23:10:00.000000Z��2023-12-04T23:10:00.000000Z��2023-12-05T14:37:05.720000Z��2023-12-05T14:37:05.720000Z��2023-12-05T14:37:06.760000Z��2023-12-05T14:37:06.760000Z��2023-12-05T14:37:06.800000Z��2023-12-05T14:37:06.800000Z��AOTC�j�  �AOTC�j�  �AOTC�j�  �AOTC�j�  �MONE�j�  j�  j�  j�  j�  j�  j�  �UNIT�j�  j�  j�  j�  j�  j�  j�  hbhchdhehfhghhhi�Market�j�  j�  j�  j�  j�  j�  j�  jz  jz  j{  j{  j{  j{  j{  j{  j�  j�  j�  j�  j�  j�  j�  j�  jz  jz  G�      G�      G�      G�      G�      G�      �PASV�j�  j�  j�  j�  j�  j�  j�  �NEWO��FILL�j�  j�  j�  j�  j�  j�  hbhchdhehfhghhhihjhjhkhkhkhkhkhkj�  j�  j�  j�  j�  j�  j�  j�  j|  j}  j~  j  j�  j�  j�  j�  j|  j}  j~  j  j�  j�  j�  j�  j|  j}  j~  j  j�  j�  j�  j�  j|  j}  j~  j  j�  j�  j�  j�  j*  j+  j,  j-  j.  j/  j0  j1  �	id:u00901��	id:u00901��	id:u00901��	id:u00901��	id:u00901��	id:u00901��	id:u00901��	id:u00901��
id:inv.dec.id��
id:inv.dec.id��
id:inv.dec.id��
id:inv.dec.id��
id:inv.dec.id��
id:inv.dec.id��
id:inv.dec.id��
id:inv.dec.id�jz  jz  G�      G�      G�      G�      G�      G�      G�      �XOFF�G�      �XOFF�G�      �XOFF�G�      �XOFF�G�      �BJCHBAU23389000100000001�G�      �BJCHBAU23389054790000001�G�      �BJCHBAU23389054790000002�G�      �BJCHBAU23389054790000003�et�bh(��                                                               	       
                     
                                                                                                                 !       %       &       '       (       *       ,       -       .       /       0       1       2       3       4       5       6       7       9       :       �hK0��ht�R�K��R�hh7h9K ��h;��R�(KKK��hA�]�(�pandas._libs.missing��NA���j�  j�  j�  j�  j�  j�  j�  et�b�builtins��slice���K<K=K��R�K��R�hh7h9K ��h;��R�(KKK��hA�]�(j�  j�  j�  j�  j�  j�  j�  j�  et�bj�  K=K>K��R�K��R�hh7h9K ��h;��R�(KKK��hA�]�(j�  j�  j�  j�  j�  j�  j�  j�  et�bj�  K>K?K��R�K��R�hh7h9K ��h;��R�(KKK��hA�]�(j�  j�  j�  j�  j�  j�  j�  j�  et�bj�  K?K@K��R�K��R�hh7h9K ��h;��R�(KKK��hA�]�(j�  j�  j�  j�  j�  j�  j�  j�  et�bj�  K@KAK��R�K��R�hh7h9K ��h;��R�(KKK��hA�]�(j�  j�  j�  j�  j�  j�  j�  j�  et�bj�  KAKBK��R�K��R�hh7h9K ��h;��R�(KKK��hA�]�(j�  j�  j�  j�  j�  j�  j�  j�  et�bj�  KBKCK��R�K��R�hh7h9K ��h;��R�(KKK��hA�]�(j�  j�  j�  j�  j�  j�  j�  j�  et�bj�  KCKDK��R�K��R�hh7h9K ��h;��R�(KKK��hA�]�(j�  j�  j�  j�  j�  j�  j�  j�  et�bj�  KDKEK��R�K��R�hh7h9K ��h;��R�(KKK��hA�]�(j�  j�  j�  j�  j�  j�  j�  j�  et�bj�  KEKFK��R�K��R�hh7h9K ��h;��R�(KKK��hA�]�(j�  j�  j�  j�  j�  j�  j�  j�  et�bj�  KFKGK��R�K��R�hh7h9K ��h;��R�(KKK��hA�]�(]�(hEhuhxh~h�h�h�h�h�e]�(hRh�h�h�h�h�h�h�h�e]�(hTh�h�h�h�h�h�h�h�e]�(hWh�h�h�h�h�h�h�h�e]�(hYh�h�h�h�h�h�h�h�e]�(h\h�h�h�h�h�h�h�h�e]�(h^h�h�h�h�h�h�h�h�e]�(hah�h�h�h�j   j  j  j  eet�bj�  KGKHK��R�K��R�t�]�(�pandas.core.indexes.base��
_new_Index���j5  �Index���}�(�data�h7h9K ��h;��R�(KKH��hA�]�(�marketIdentifiers.instrument��0integration_trades_tasks.trade_sink.utils.static��TempColumns����"__fb_is_created_through_fallback__���R��id��buySell��date��marketIdentifiers.parties��traderFileIdentifier��counterpartyFileIdentifier��buyerFileIdentifier��sellerFileIdentifier��!sellerDecisionMakerFileIdentifier��executingEntityFileIdentifier��__fallback_buyer__��__fallback_seller__��__fallback_counterparty__��__fallback_seller_dec_maker__��__fallback_trader__��__meta_model__��	sourceKey��sourceIndex��dataSourceName�� buyerDecisionMakerFileIdentifier��__fallback_buyer_dec_maker__��transactionDetails.price��transactionDetails.priceAverage��transactionDetails.quantity��%transactionDetails.cumulativeQuantity�� transactionDetails.priceCurrency��#transactionDetails.buySellIndicator��"transactionDetails.tradingDateTime��"transactionDetails.tradingCapacity�� transactionDetails.priceNotation��#transactionDetails.quantityNotation��orderIdentifiers.orderIdCode�� priceFormingData.initialQuantity��priceFormingData.tradedQuantity��priceFormingData.price��executionDetails.orderType��!executionDetails.buySellIndicator�� executionDetails.tradingCapacity��&executionDetails.shortSellingIndicator��+executionDetails.liquidityProvisionActivity��+executionDetails.passiveAggressiveIndicator��%executionDetails.passiveOnlyIndicator��executionDetails.orderStatus��	_order.id��_order.buySell��#_order.executionDetails.orderStatus��timestamps.orderSubmitted��timestamps.orderReceived��timestamps.tradingDateTime��timestamps.orderStatusUpdated��,reportDetails.executingEntity.fileIdentifier��?tradersAlgosWaiversIndicators.executionWithinFirmFileIdentifier��HtradersAlgosWaiversIndicators.investmentDecisionWithinFirmFileIdentifier��3tradersAlgosWaiversIndicators.shortSellingIndicator��=tradersAlgosWaiversIndicators.securitiesFinancingTxnIndicator��transactionDetails.venue��!orderIdentifiers.transactionRefNo��"priceFormingData.remainingQuantity��__asset_class__��__contract_multiplier__��__delivery_type__��__expiry_date__��__instrument_classification__��__instrument_full_name__��__instrument_venue__��__option_strike_price__��__fb_ext_strikePriceCurrency__��__temp_col_1__��__underlying_instruments__��marketIdentifiers�et�b�name�Nu��R�j7  �pandas.core.indexes.range��
RangeIndex���}�(j�  N�start�K �stop�K�step�Ku��R�e��R��_typ��	dataframe��	_metadata�]��attrs�}��_flags�}��allows_duplicate_labels��sub.