���      �pandas.core.frame��	DataFrame���)��}�(�_mgr��pandas.core.internals.managers��BlockManager���(�pandas._libs.internals��_unpickle_block����numpy.core.numeric��_frombuffer���(�
             ��numpy��dtype����b1�����R�(K�|�NNNJ����J����K t�bKK���C�t�R�h(�(              1       2       4       @       �h�i8�����R�(K�<�NNNJ����J����K t�bK��ht�R�K��R�hh(��                       \���(<d@,���P@|�j�1d@��k;^�P@     �T@     �X@      �@     (�@     �@    ���@     Q�@    ���@     �T@     �X@\���(<d@,���P@                                �h�f8�����R�(Kh NNNJ����J����K t�bKK��ht�R�h(�X                                          &       '       (       )       *       +       �hK��ht�R�K��R�h�numpy.core.multiarray��_reconstruct���h�ndarray���K ��Cb���R�(KK1K��h�O8�����R�(KhNNNJ����J����K?t�b�]�(]�(}�(�labelId��US9311421039USD1��path��instrumentDetails.instrument��type��se_elastic_schema.static.market��IdentifierType����OBJECT���R�u}�(hF�US9311421039�hHhIhJhPue]�(}�(hF�US36266G1076USD1�hHhIhJhPu}�(hF�US36266G1076�hHhIhJhPue�EDGX��XNGS��4Y6N00000R0001��230927-155054-ZC834-0��2��1��
2023-09-27��
2023-09-27�]�(}�(hF�lei:213800ch6iz9j98pis59�hH�reportDetails.executingEntity�hJhPu}�(hF�
id:express�hH�trader�hJhM�ARRAY���R�u}�(hF�id:intesalu�hH�clientIdentifiers.client�hJhiu}�(hF�id:opco�hH�counterparty�hJhPu}�(hF�id:opco�hH�buyer�hJhiu}�(hF�id:intesalu�hH�seller�hJhiu}�(hF�
id:express�hH�1tradersAlgosWaiversIndicators.executionWithinFirm�hJhPue]�(}�(hF�lei:213800ch6iz9j98pis59�hHhchJhPu}�(hF�
id:express�hHhfhJhiu}�(hF�id:anima�hHhlhJhiu}�(hF�id:opco�hHhohJhPu}�(hF�id:anima�hHhrhJhiu}�(hF�id:opco�hHhuhJhiu}�(hF�
id:express�hHhxhJhPue�id:intesalu��id:anima��
id:express��
id:express��id:opco��id:opco��id:opco��id:anima��id:intesalu��id:opco��lei:213800ch6iz9j98pis59��lei:213800ch6iz9j98pis59��opco��anima��intesalu��opco��opco��opco��intesalu��anima��express��express��Order�h���s3://temp.dev.steeleye.co/aries/ingress/streamed/trade_sink_s/oppenheimer_fidessa/cfcd1c10d2c8dd83e74417c8fa0fe6af2428d94367213a5c229a512b77147bb1_757.fix���s3://temp.dev.steeleye.co/aries/ingress/streamed/trade_sink_s/oppenheimer_fidessa/cfd6bf5c155f7cafe963640bb10da027cc44224548b87fce21126e24a7fe5f33_6137.fix��0��1��Oppenheimer Fidessa�h�hXhY�XOFF�h��USD��USD��SELL��BUYI��2023-09-27T14:33:25.000000Z��2023-09-27T17:09:20.000000Z��AOTC�h��MONE�h��UNIT�h��00005693548TRLO1.1.1��00005698740TRLO1.1.1��00000079973ORLO1��00000079990ORLO1�hZh[�&3 names @ MOC vwap max 1/3 vol otd pls��VWAP otd w auction SPOMA��Market�h�h�h�h�h�h�G�      �AGRE�h��PARF�h�hZh[h\h]�NEWO�h��2023-09-27T14:33:25.000000Z��2023-09-27T17:09:20.000000Z��2023-09-27T14:33:25.000000Z��2023-09-27T17:09:20.000000Z�h�h�h�h�h�h��
id:express��
id:express�h�G�      et�bh(��                                                        	       
                     
                                                                                                                 !       "       #       $       %       ,       -       .       /       0       3       5       6       7       8       9       :       ;       <       =       >       ?       �hK1��ht�R�K��R�hh7h9K ��h;��R�(KKK��hA�]�(�pandas._libs.missing��NA���h�et�b�builtins��slice���KAKBK��R�K��R�hh7h9K ��h;��R�(KKK��hA�]�(h�h�et�bh�KBKCK��R�K��R�hh7h9K ��h;��R�(KKK��hA�]�(h�h�et�bh�KCKDK��R�K��R�hh7h9K ��h;��R�(KKK��hA�]�(h�h�et�bh�KDKEK��R�K��R�hh7h9K ��h;��R�(KKK��hA�]�(h�h�et�bh�KEKFK��R�K��R�hh7h9K ��h;��R�(KKK��hA�]�(h�h�et�bh�KFKGK��R�K��R�hh7h9K ��h;��R�(KKK��hA�]�(h�h�et�bh�KGKHK��R�K��R�hh7h9K ��h;��R�(KKK��hA�]�(h�h�et�bh�KHKIK��R�K��R�hh7h9K ��h;��R�(KKK��hA�]�(h�h�et�bh�KIKJK��R�K��R�hh7h9K ��h;��R�(KKK��hA�]�(]�(hEhQhahdhjhmhphshve]�(hThVhzh|h~h�h�h�h�eet�bh�KJKKK��R�K��R�t�]�(�pandas.core.indexes.base��
_new_Index���j3  �Index���}�(�data�h7h9K ��h;��R�(KKK��hA�]�(�marketIdentifiers.instrument��0integration_trades_tasks.trade_sink.utils.static��TempColumns����__instrument_venue__���R�jB  �"__fb_is_created_through_fallback__���R�jB  �__option_strike_price__���R��id��buySell��date��marketIdentifiers.parties��clientFileIdentifier��traderFileIdentifier��counterpartyFileIdentifier��buyerFileIdentifier��sellerFileIdentifier��executingEntityFileIdentifier��__fallback_buyer__��__fallback_seller__��__fallback_counterparty__��__fallback_client__��__fallback_trader__��__meta_model__��	sourceKey��sourceIndex��dataSourceName�� transactionDetails.ultimateVenue��transactionDetails.venue��transactionDetails.price��transactionDetails.priceAverage��transactionDetails.quantity��%transactionDetails.cumulativeQuantity�� transactionDetails.priceCurrency��#transactionDetails.buySellIndicator��"transactionDetails.tradingDateTime��"transactionDetails.tradingCapacity�� transactionDetails.priceNotation��#transactionDetails.quantityNotation��!orderIdentifiers.transactionRefNo��$orderIdentifiers.internalOrderIdCode��orderIdentifiers.orderIdCode�� priceFormingData.initialQuantity��"priceFormingData.remainingQuantity��priceFormingData.tradedQuantity��priceFormingData.price��executionDetails.limitPrice��executionDetails.stopPrice��&executionDetails.outgoingOrderAddlInfo��executionDetails.orderType��!executionDetails.buySellIndicator�� executionDetails.tradingCapacity��&executionDetails.shortSellingIndicator��+executionDetails.liquidityProvisionActivity��&executionDetails.mesFirstExecutionOnly��+executionDetails.passiveAggressiveIndicator��%executionDetails.passiveOnlyIndicator��executionDetails.orderStatus��	_order.id��_order.buySell��#_order.executionDetails.orderStatus��timestamps.orderReceived��timestamps.orderSubmitted��timestamps.tradingDateTime��timestamps.orderStatusUpdated��,reportDetails.executingEntity.fileIdentifier��?tradersAlgosWaiversIndicators.executionWithinFirmFileIdentifier��3tradersAlgosWaiversIndicators.shortSellingIndicator��=tradersAlgosWaiversIndicators.securitiesFinancingTxnIndicator��__asset_class__��__contract_multiplier__��__delivery_type__��__expiry_date__��__instrument_classification__��__instrument_full_name__��__fb_ext_strikePriceCurrency__��__temp_col_1__��__underlying_instruments__��marketIdentifiers�et�b�name�Nu��R�j5  �pandas.core.indexes.range��
RangeIndex���}�(j�  N�start�K �stop�K�step�Ku��R�e��R��_typ��	dataframe��	_metadata�]��attrs�}��_flags�}��allows_duplicate_labels��sub.