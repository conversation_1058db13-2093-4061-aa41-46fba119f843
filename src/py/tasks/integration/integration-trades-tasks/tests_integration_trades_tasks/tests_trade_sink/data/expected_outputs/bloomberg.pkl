���'      �pandas.core.frame��	DataFrame���)��}�(�_mgr��pandas.core.internals.managers��BlockManager���(�pandas._libs.internals��_unpickle_block����numpy.core.numeric��_frombuffer���(�                       ��numpy��dtype����b1�����R�(K�|�NNNJ����J����K t�bKK���C�t�R�h(�(              5       7       C       D       �h�i8�����R�(K�<�NNNJ����J����K t�bK��ht�R�K��R�hh(�                                       �MbXy[@        J+��?            �OA            ��A            �OA            ��A                                        �G�A�G�A������A������A                                    �OA    �OA    ��A    ��A                                                                                                    �OA            ��A        �MbXy[@        J+��?                                                                                                              �              �        �h�f8�����R�(Kh NNNJ����J����K t�bKK��ht�R�h(��                                                 #       )       *       +       ,       -       .       /       0       1       E       �hK��ht�R�K��R�h�numpy.core.multiarray��_reconstruct���h�ndarray���K ��Cb���R�(KK0K��h�O8�����R�(KhNNNJ����J����K?t�b�]�(]�(}�(�labelId��FR0012558310EURBTFE��path��instrumentDetails.instrument��type��se_elastic_schema.static.market��IdentifierType����OBJECT���R�u}�(hF�FR0012558310�hHhIhJhPu}�(hFhRhHhIhJhPue]�(}�(hF�FR0012558310EURBTFE�hHhIhJhPu}�(hFhRhHhIhJhPu}�(hFhRhHhIhJhPue]�(}�(hF�FR0127034678EURBTFE�hHhIhJhPu}�(hF�FR0127034678�hHhIhJhPu}�(hFh]hHhIhJhPue]�(}�(hF�FR0127034678EURBTFE�hHhIhJhPu}�(hFh]hHhIhJhPu}�(hFh]hHhIhJhPue�BTFE�hd�BTFE�he�BAME:20220304:197:5��BAME:20220304:197:5��RBSM:20220304:159:5��RBSM:20220304:159:5��1�hjhjhj�
2022-03-04��
2022-03-04��
2022-03-04��
2022-03-04�]�(}�(hF�&id:goodbody stockbrokers unlimited co.�hH�buyer�hJhM�ARRAY���R�u}�(hF�id:bame�hH�seller�hJhuu}�(hF�&id:goodbody stockbrokers unlimited co.�hH�reportDetails.executingEntity�hJhPu}�(hF�id:ebitar5:27677130�hH�1tradersAlgosWaiversIndicators.executionWithinFirm�hJhPu}�(hF�id:ncorrigan1:5777408�hH�trader�hJhuu}�(hF�
id:8101147�hH�:tradersAlgosWaiversIndicators.investmentDecisionWithinFirm�hJhPu}�(hF�id:bame�hH�counterparty�hJhPue]�(}�(hF�&id:goodbody stockbrokers unlimited co.�hHhrhJhuu}�(hF�id:bame�hHhxhJhuu}�(hF�&id:goodbody stockbrokers unlimited co.�hHh{hJhPu}�(hF�id:ebitar5:27677130�hHh~hJhPu}�(hF�id:ncorrigan1:5777408�hHh�hJhuu}�(hF�
id:8101147�hHh�hJhPu}�(hF�id:bame�hHh�hJhPue]�(}�(hF�&id:goodbody stockbrokers unlimited co.�hHhrhJhuu}�(hF�id:rbsm�hHhxhJhuu}�(hF�&id:goodbody stockbrokers unlimited co.�hHh{hJhPu}�(hF�id:mtaaffe1:8101147�hHh~hJhPu}�(hF�id:mtaaffe1:8101147�hHh�hJhuu}�(hF�
id:8101147�hHh�hJhPu}�(hF�id:rbsm�hHh�hJhPue]�(}�(hF�&id:goodbody stockbrokers unlimited co.�hHhrhJhuu}�(hF�id:rbsm�hHhxhJhuu}�(hF�&id:goodbody stockbrokers unlimited co.�hHh{hJhPu}�(hF�id:mtaaffe1:8101147�hHh~hJhPu}�(hF�id:mtaaffe1:8101147�hHh�hJhuu}�(hF�
id:8101147�hHh�hJhPu}�(hF�id:rbsm�hHh�hJhPue�id:ncorrigan1:5777408��id:ncorrigan1:5777408��id:mtaaffe1:8101147��id:mtaaffe1:8101147��id:bame��id:bame��id:rbsm��id:rbsm��&id:goodbody stockbrokers unlimited co.��&id:goodbody stockbrokers unlimited co.��&id:goodbody stockbrokers unlimited co.��&id:goodbody stockbrokers unlimited co.��id:bame��id:bame��id:rbsm��id:rbsm��&id:goodbody stockbrokers unlimited co.��&id:goodbody stockbrokers unlimited co.��&id:goodbody stockbrokers unlimited co.��&id:goodbody stockbrokers unlimited co.��#goodbody stockbrokers unlimited co.��#goodbody stockbrokers unlimited co.��#goodbody stockbrokers unlimited co.��#goodbody stockbrokers unlimited co.��bame��bame��rbsm��rbsm��bame��bame��rbsm��rbsm��ncorrigan1:5777408��ncorrigan1:5777408��mtaaffe1:8101147��mtaaffe1:8101147��Order�h�h�hٌ�s3://temp.dev.steeleye.co/aries/ingress/streamed/trade_sink_s/bloomberg/4cef8a9efc3fb33b6829c128cee0cb4fbe782463c3df6db0eb0978b8a61b537b_859.fix���s3://temp.dev.steeleye.co/aries/ingress/streamed/trade_sink_s/bloomberg/4cef8a9efc3fb33b6829c128cee0cb4fbe782463c3df6db0eb0978b8a61b537b_859.fix���s3://temp.dev.steeleye.co/aries/ingress/streamed/trade_sink_s/bloomberg/69863718434836b4675e4a4040d66566cf68f86029f328e84dc4dec6bbb1b50d_1174.fix���s3://temp.dev.steeleye.co/aries/ingress/streamed/trade_sink_s/bloomberg/69863718434836b4675e4a4040d66566cf68f86029f328e84dc4dec6bbb1b50d_1174.fix��0��0��1��1��Bloomberg FXGO / TSOX / EMSX�h�h�h�hdhdhehe�BTFE�G�      �BTFE�G�      �EUR�h�EUR�h�EUR��EUR��EUR��EUR��BUYI�h�h�h�2022-03-04T12:07:42.640000Z��2022-03-04T12:07:42.640000Z��2022-03-04T14:44:50.372000Z��2022-03-04T14:44:50.372000Z��DEAL�h�h�h��PERC�h�YIEL�h�MONE�h�h�h�h�h�h�h�*BAME:20220304:197:5|BAME:20220304:197:5:12�G�      �*RBSM:20220304:159:5|RBSM:20220304:159:5:12�G�      �5947:20220304:197:5�h��3960:20220304:159:5�h�hfhghhhi�BBG0000000000B622200D3E0300005�h��BBG0000000000B622225D8DE040008�h��hCoupon Rate - 0.001, Yield - -0.0302219831, Sender Comp ID - MAP_BLP_PROD, Target Comp ID - MAP_GBS_PROD��hCoupon Rate - 0.001, Yield - -0.0302219831, Sender Comp ID - MAP_BLP_PROD, Target Comp ID - MAP_GBS_PROD��aCoupon Rate - 0.0, Yield - -0.00784, Sender Comp ID - MAP_BLP_PROD, Target Comp ID - MAP_GBS_PROD��aCoupon Rate - 0.0, Yield - -0.00784, Sender Comp ID - MAP_BLP_PROD, Target Comp ID - MAP_GBS_PROD�h�h�h�h�h�h�h�h��PASV�h�h�h��FILL��NEWO�h�j   hfhghhhihjhjhjhjj   j   j   j   �2022-03-04T12:07:42.640000Z��2022-03-04T12:07:42.640000Z��2022-03-04T14:44:50.372000Z��2022-03-04T14:44:50.372000Z��2022-03-04T12:07:42.640000Z��2022-03-04T12:07:42.640000Z��2022-03-04T14:44:50.372000Z��2022-03-04T14:44:50.372000Z�h�h�h�h�h�j  h�j  h�h�h�hȌid:ebitar5:27677130��id:ebitar5:27677130��id:mtaaffe1:8101147��id:mtaaffe1:8101147��
id:8101147��
id:8101147��
id:8101147��
id:8101147�et�bh(��                                                        	       
                     
                                                                                                          !       "       $       %       &       '       (       2       3       4       6       8       9       :       ;       <       =       >       ?       @       A       B       �hK0��ht�R�K��R�hh7h9K ��h;��R�(KKK��hA�]�(�pandas._libs.missing��NA���j  j  j  et�b�builtins��slice���KFKGK��R�K��R�hh7h9K ��h;��R�(KKK��hA�]�(j  j  j  j  et�bj#  KGKHK��R�K��R�hh7h9K ��h;��R�(KKK��hA�]�(j  j  j  j  et�bj#  KHKIK��R�K��R�hh7h9K ��h;��R�(KKK��hA�]�(j  j  j  j  et�bj#  KIKJK��R�K��R�hh7h9K ��h;��R�(KKK��hA�]�(j  j  j  j  et�bj#  KJKKK��R�K��R�hh7h9K ��h;��R�(KKK��hA�]�(j  j  j  j  et�bj#  KKKLK��R�K��R�hh7h9K ��h;��R�(KKK��hA�]�(j  j  j  j  et�bj#  KLKMK��R�K��R�hh7h9K ��h;��R�(KKK��hA�]�(j  j  j  j  et�bj#  KMKNK��R�K��R�hh7h9K ��h;��R�(KKK��hA�]�(j  j  j  j  et�bj#  KNKOK��R�K��R�hh7h9K ��h;��R�(KKK��hA�]�(]�(hEhQhShphvhyh|hh�h�e]�(hUhWhXh�h�h�h�h�h�h�e]�(hZh\h^h�h�h�h�h�h�h�e]�(h`hbhch�h�h�h�h�h�h�eet�bj#  KOKPK��R�K��R�t�]�(�pandas.core.indexes.base��
_new_Index���j�  �Index���}�(�data�h7h9K ��h;��R�(KKP��hA�]�(�marketIdentifiers.instrument��0integration_trades_tasks.trade_sink.utils.static��TempColumns����__instrument_venue__���R�j�  �"__fb_is_created_through_fallback__���R�j�  �__option_strike_price__���R��id��buySell��date��marketIdentifiers.parties��traderFileIdentifier��counterpartyFileIdentifier��buyerFileIdentifier��sellerFileIdentifier��executingEntityFileIdentifier��__fallback_buyer__��__fallback_seller__��__fallback_counterparty__��__fallback_trader__��__meta_model__��	sourceKey��sourceIndex��dataSourceName�� transactionDetails.ultimateVenue��transactionDetails.venue��transactionDetails.price��transactionDetails.quantity��%transactionDetails.cumulativeQuantity��#transactionDetails.quantityCurrency�� transactionDetails.priceCurrency��#transactionDetails.settlementAmount��transactionDetails.netAmount��#transactionDetails.buySellIndicator��"transactionDetails.tradingDateTime��"transactionDetails.tradingCapacity�� transactionDetails.priceNotation��#transactionDetails.quantityNotation��#transactionDetails.commissionAmount��+transactionDetails.commissionAmountCurrency��!orderIdentifiers.transactionRefNo��$orderIdentifiers.internalOrderIdCode��orderIdentifiers.orderIdCode��.orderIdentifiers.tradingVenueTransactionIdCode�� priceFormingData.initialQuantity��"priceFormingData.remainingQuantity��!priceFormingData.modifiedQuantity��"priceFormingData.displayedQuantity��priceFormingData.tradedQuantity��priceFormingData.price��&executionDetails.minAcceptableQuantity��executionDetails.limitPrice��executionDetails.stopPrice��&executionDetails.outgoingOrderAddlInfo��!executionDetails.buySellIndicator�� executionDetails.tradingCapacity��+executionDetails.liquidityProvisionActivity��+executionDetails.passiveAggressiveIndicator��%executionDetails.passiveOnlyIndicator��executionDetails.orderStatus��	_order.id��_order.buySell��#_order.executionDetails.orderStatus��timestamps.orderSubmitted��timestamps.orderReceived��timestamps.tradingDateTime��timestamps.orderStatusUpdated��,reportDetails.executingEntity.fileIdentifier��?tradersAlgosWaiversIndicators.executionWithinFirmFileIdentifier��HtradersAlgosWaiversIndicators.investmentDecisionWithinFirmFileIdentifier��=tradersAlgosWaiversIndicators.securitiesFinancingTxnIndicator��flags.overwrite��transactionDetails.priceAverage��__asset_class__��__contract_multiplier__��__delivery_type__��__expiry_date__��__instrument_classification__��__instrument_full_name__��__fb_ext_strikePriceCurrency__��__temp_col_1__��__underlying_instruments__��marketIdentifiers�et�b�name�Nu��R�j�  �pandas.core.indexes.range��
RangeIndex���}�(j�  N�start�K �stop�K�step�Ku��R�e��R��_typ��	dataframe��	_metadata�]��attrs�}��_flags�}��allows_duplicate_labels��sub.