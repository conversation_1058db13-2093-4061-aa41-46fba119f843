# ruff: noqa: E501
import os
import pytest
from aries_config_api_httpschema.mifir_scheduler_config import ScheduleTypeEnum
from aries_io_event.io_param import IOParamFieldSet
from aries_io_event.task import TaskFieldSet
from aries_io_event.workflow import WorkflowFieldSet
from aries_task_link.models import AriesTaskInput
from datetime import datetime
from integration_wrapper.static import IntegrationAriesTaskVariables

# This needs to be here since the conftest is loaded first
# than any import on the test files
os.environ[IntegrationAriesTaskVariables.DATA_PLATFORM_CONFIG_API_URL] = (
    "https://test-enterprise.steeleye.co"
)


@pytest.fixture()
def sample_aries_task_input() -> AriesTaskInput:
    workflow = WorkflowFieldSet(
        name="tr_arm_no_response_email",
        stack="dev-blue",
        tenant="test",
        start_timestamp=datetime(2022, 7, 6),
        trace_id="trace",
    )

    input_param = IOParamFieldSet(params=dict())

    task = TaskFieldSet(name="tr_arm_no_response_email", version="latest", success=False)

    return AriesTaskInput(workflow=workflow, input_param=input_param, task=task)


@pytest.fixture()
def tr_arm_no_response_config_data():
    tr_config = {
        "scheduleType": ScheduleTypeEnum.notify_on_no_arm_response.value,
        "workflowName": "tr_arm_no_response_email",
        "enabled": True,
        "cron": "30 9 * * MON-FRI",
        "recipients": ["<EMAIL>"],
        "scheduleISOTimeZone": "Asia/Kolkata",
        "createdDateTime": "2024-01-22T06:52:24.360173",
    }

    return tr_config


@pytest.fixture()
def transaction_report_data():
    return [
        {
            "submittedBy": "system",
            "&id": "steeleye_trmifir_894500wota5040khgx73.20221210121111",
            "reportId": "SteelEye_TRMiFIR_894500WOTA5040KHGX73.20221210121111",
            "&key": "RTS22TransactionReport:steeleye_trmifir_894500wota5040khgx73.20221210121111:1670674281140",
            "reportUrl": "s3://puneeth.dev.steeleye.co/flows/tr-workflow-unavista-submit-report/SteelEye_TRMiFIR_894500WOTA5040KHGX73.20221210121111.xml",
            "&model": "RTS22TransactionReport",
            "&version": 1,
            "submitted": "2023-12-25T19:30:00.803351Z",
            "generated": "2022-12-10T12:11:11.803351Z",
            "reported": 536,
            "&hash": "6ab801b7ea973fd80fad6935cff2816cdd65a76ce1cdda2b78d2ddf273f58905",
            "&timestamp": 1703497171140,
            "&user": "system",
        }
    ]
