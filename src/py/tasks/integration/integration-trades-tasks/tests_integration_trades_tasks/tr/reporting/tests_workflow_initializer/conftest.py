import os
import pytest
from aries_io_event.io_param import IOParamFieldSet
from aries_io_event.task import TaskFieldSet
from aries_io_event.workflow import WorkflowFieldSet
from aries_task_link.models import AriesTaskInput
from datetime import datetime
from integration_wrapper.static import IntegrationAriesTaskVariables

# This needs to be here since the conftest is loaded first
# than any import on the test files
os.environ[IntegrationAriesTaskVariables.DATA_PLATFORM_CONFIG_API_URL] = (
    "https://test-enterprise.steeleye.co"
)


@pytest.fixture()
def sample_aries_task_input() -> AriesTaskInput:
    workflow = WorkflowFieldSet(
        name="tr_workflow_initializer",
        stack="dev-blue",
        tenant="test",
        start_timestamp=datetime(2022, 7, 6),
        trace_id="trace",
    )

    input_param = IOParamFieldSet(params=dict())

    task = TaskFieldSet(name="tr_workflow_initializer", version="latest", success=False)

    return AriesTaskInput(workflow=workflow, input_param=input_param, task=task)
