DATA_PLATFORM_CONFIG_API_URL=https://config-api.nonprod-eu-ie-1.steeleye.co
ELASTIC_HOST=elasticsearch.uat-shared-steeleye.steeleye.co
ELASTIC_PORT=443
ELASTIC_SCHEME=https
ELASTIC_API_KEY=''

SRP_ELASTIC_HOST=localhost
SRP_ELASTIC_PORT=9801
SRP_ELASTIC_SCHEME=http

TASK_NAME=trade_sink
TASK_WORKER_DOMAIN=benchmark-blue
DEBUG=1
OMA_REST_PROXY_URL=https://kafka-rest.uat-oma.steeleye.co
OMA_ARIES_TOPIC=aries.oma.events
CONDUCTOR_API_URL=https://conductor.uat-enterprise.steeleye.co/api
STACK=uat-shared-steeleye
AWS_PROFILE=nonprod_infra

OPENFIGI_APIKEY=""

COGNITO_AUTH_URL=""
COGNITO_CLIENT_ID=""
COGNITO_CLIENT_SECRET=""

MARKET_DATA_API_URL=https://api.market-data.steeleye.co
MASTER_DATA_API_HOST=https://api.master-data.steeleye.co
MASTER_DATA_HOST=https://api.master-data.steeleye.co

BENCHMARK=false
#BENCHMARK=true

# This env var is needed to fetch EOD Market Data for BestEx
# You can disable it if you're using the AWS_PROFILE that has access to the master data bucket
ON_PREMISES=true