# ruff: noqa: E501
from aries_se_trades_tasks.order.static import OrderWorkflowNames
from typing import Callable


def resolve_task_func(task_name: str) -> Callable:
    match task_name:
        case "tr_arm_no_response_email":
            from integration_trades_tasks.tr.reporting.arm_no_response_email.arm_no_response_email_task import (
                tr_arm_no_response_email_run,
            )

            return tr_arm_no_response_email_run

        case "instrument_mapper":
            from integration_trades_tasks.order.generic.instrument_mapper.instrument_mapper_task import (
                instrument_mapper_run,
            )

            return instrument_mapper_run

        case "order_aladdin_v2":
            from integration_trades_tasks.order.feeds.order_aladdin_v2.order_aladdin_v2_task import (
                order_aladdin_v2_run,
            )

            return order_aladdin_v2_run

        case "order_blotter":
            from integration_trades_tasks.order.feeds.order_blotter.order_blotter_task import (
                order_blotter_run,
            )

            return order_blotter_run

        case "order_blotter_converter":
            from integration_trades_tasks.order.feeds.order_blotter_converter.order_blotter_converter_task import (
                order_blotter_converter_run,
            )

            return order_blotter_converter_run

        case "order_blotter_to_restricted_list":
            from integration_trades_tasks.order.feeds.order_blotter_to_restricted_list.order_blotter_to_restricted_list_task import (
                order_blotter_to_restricted_list_run,
            )

            return order_blotter_to_restricted_list_run

        case "order_crd":
            from integration_trades_tasks.order.feeds.order_crd.order_crd_task import order_crd_run

            return order_crd_run
        case "order_eze_oms_soft":
            from integration_trades_tasks.order.feeds.order_eze_oms_soft.order_eze_oms_soft_task import (
                order_eze_oms_soft_run,
            )

            return order_eze_oms_soft_run

        case "order_feed_cfox":
            from integration_trades_tasks.order.feeds.order_feed_cfox.order_feed_cfox_task import (
                order_feed_cfox_run,
            )

            return order_feed_cfox_run

        case "order_flextr_bp_fix_transform":
            from integration_trades_tasks.order.feeds.order_flextrade_bell_potter_fix.order_flextrade_bell_potter_fix_task import (
                order_flextrade_bell_potter_fix_run,
            )

            return order_flextrade_bell_potter_fix_run

        case "order_iress_bellp_fix_transform":
            from integration_trades_tasks.order.feeds.order_iress_bell_potter_fix.order_iress_bell_potter_fix_task import (
                order_iress_bell_potter_fix_run,
            )

            return order_iress_bell_potter_fix_run

        case OrderWorkflowNames.ORDER_TRADEWEB:
            from integration_trades_tasks.order.feeds.order_tradeweb.order_tradeweb_task import (
                order_tradeweb_run,
            )

            return order_tradeweb_run

        case "order_thornbridge_crims":
            from integration_trades_tasks.order.feeds.order_thornbridge_crims.order_thornbridge_crims_task import (
                order_thornbridge_crims_run,
            )

            return order_thornbridge_crims_run

        case "order_tr_fidessa_eod":
            from integration_trades_tasks.order.feeds.order_tr_fidessa_eod.order_tr_fidessa_eod_task import (
                order_tr_fidessa_eod_run,
            )

            return order_tr_fidessa_eod_run

        case "tr_pre_submission_email":
            from integration_trades_tasks.tr.reporting.pre_submission_email.pre_submission_email_task import (
                tr_pre_submission_email_run,
            )

            return tr_pre_submission_email_run

        case "tr_bbg_emsi_orders":
            from integration_trades_tasks.tr.feeds.tr_bbg_emsi_orders.tr_bbg_emsi_orders_flow_task import (
                tr_bbg_emsi_orders_run,
            )

            return tr_bbg_emsi_orders_run

        case "tr_jupiter_uv_transform":  #
            from integration_trades_tasks.tr.feeds.tr_jupiter_unavista.tr_jupiter_unavista_task import (
                tr_jupiter_unavista_run,
            )

            return tr_jupiter_unavista_run

        case "trade_sink_transform":
            from integration_trades_tasks.trade_sink.trade_sink_task import trade_sink_run

            return trade_sink_run

        case "tr_workflow_initializer":
            from integration_trades_tasks.tr.reporting.workflow_initializer.workflow_initializer_task import (
                tr_workflow_initializer_run,
            )

            return tr_workflow_initializer_run
        case _:
            raise ValueError(f"Unrecognized task name: '{task_name}'")
