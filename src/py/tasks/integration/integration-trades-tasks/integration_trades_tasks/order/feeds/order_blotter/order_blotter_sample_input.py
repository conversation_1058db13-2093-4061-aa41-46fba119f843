import datetime
import uuid
from aries_io_event.io_param import IOParamFieldSet
from aries_io_event.task import TaskFieldSet
from aries_io_event.workflow import WorkflowFieldSet
from aries_task_link.models import AriesTaskInput

# AWS provider with regular AWS client
# def sample_input() -> AriesTaskInput:
#     workflow = WorkflowFieldSet(
#         trace_id="test_order_blotter_gadohdrherhy5y43",
#         name="order_blotter",
#         stack="dev-blue",
#         tenant="pinafore",
#         start_timestamp=datetime.datetime.utcnow(),
#     )
#     input_param = IOParamFieldSet(
#         params=dict(
#             file_uri="s3://pinafore.dev.steeleye.co/aries/ingest/order_blotter/2023/10/02"
#             "/0gG6m4QjcI6f44yhCsU4Gfoobar23111111111foo/gpg_decryptor/"
#             "59bc7e4d6d2dc1d02befee98cbd2f801b84293497d2cc63c9fc69630fccb5856___/MASTER_SEBLOTTER_QA.csv",  # noqa: E501
#             skiprows=1,
#             nrows=100,
#         )
#     )
#     task = TaskFieldSet(name="order_blotter", version="latest", success=False)
#     return AriesTaskInput(workflow=workflow, input_param=input_param, task=task)


# Azure cloud provider with regular AWS client
# def sample_input() -> AriesTaskInput:
#     workflow = WorkflowFieldSet(
#         trace_id="test_order_blotter",
#         name="order_blotter",
#         stack="dev-blue",
#         tenant="pinafore",
#         start_timestamp=datetime.datetime.utcnow(),
#     )
#     input_param = IOParamFieldSet(
#         params=dict(
#             file_uri="az://golf/data-lake/ingress/raw/orders/csv/20220609_072932___001-steeleyeBlotter.base.20220608.bonds.corporate.csv",
#             skiprows=1,
#             nrows=1000,
#         )
#     )
#     task = TaskFieldSet(name="order_blotter", version="latest", success=False)
#     return AriesTaskInput(workflow=workflow, input_param=input_param, task=task)


# Azure cloud provider with UBS client
def sample_input() -> AriesTaskInput:
    workflow = WorkflowFieldSet(
        trace_id=uuid.uuid4().hex,
        name="order_blotter",
        stack="dev-shared-2",
        tenant="mares8",
        start_timestamp=datetime.datetime.utcnow(),
    )
    input_param = IOParamFieldSet(
        params=dict(
            file_uri="s3://mares8.dev.steeleye.co/aries/ingress/nonstreamed/evented/order_blotter/seBlotter.ubs.chx.20251002.132227_csv_2510010000337596_137rows.csv.csv",  # noqa: E501
            skiprows=1,
            nrows=1000,
        )
    )
    task = TaskFieldSet(name="order_blotter", version="latest", success=False)
    return AriesTaskInput(workflow=workflow, input_param=input_param, task=task)


# AWS ES8 input
# def sample_input() -> AriesTaskInput:
#     workflow = WorkflowFieldSet(
#         trace_id="test_order_blotter_aws_es8_1",
#         name="order_blotter",
#         stack="uat-shared-steeleye",
#         tenant="tom",
#         start_timestamp=datetime.datetime.utcnow(),
#     )
#     input_param = IOParamFieldSet(
#         params=dict(
#             file_uri="s3://tom.uat.steeleye.co/aries/ingress/nonstreamed/evented/order_blotter/Cheyne-SteelEye-Blotter-CDSsample2.csv",
#             skiprows=1,
#             nrows=3,
#         )
#     )
#     task = TaskFieldSet(name="order_blotter", version="latest", success=False)
#     return AriesTaskInput(workflow=workflow, input_param=input_param, task=task)
