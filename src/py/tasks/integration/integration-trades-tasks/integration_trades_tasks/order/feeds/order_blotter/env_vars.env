BENCHMARK=false
#BENCHMARK=true
# AWS
DATA_PLATFORM_CONFIG_API_URL=https://config-api.nonprod-eu-ie-1.steeleye.co
ELASTIC_HOST=elasticsearch.dev-shared-2.steeleye.co
ELASTIC_URL=https://elasticsearch.dev-shared-2.steeleye.co:443
ELASTIC_PORT=443
ELASTIC_SCHEME=https
ELASTIC_API_KEY=ZTU5OERvNEJRM0lZem1Yd3VnX2w6QWRuT3FLNllSUk9MQjB0c2k5a0IwQQ==
STACK=dev-shared-2
AWS_PROFILE=nonprod_infra
TASK_NAME=order_blotter
TASK_WORKER_DOMAIN=dev-shared-2
DEBUG=1
SRP_THROUGH_MASTER_DATA=True
FETCH_MARKET_EOD_DATA=false
CONDUCTOR_API_URL=https://conductor.sit-enterprise.steeleye.io/api
COGNITO_AUTH_URL=https://dev-master-data.auth.eu-west-1.amazoncognito.com/oauth2/token
COGNITO_CLIENT_SECRET=
COGNITO_CLIENT_ID=
MARKET_DATA_API_URL=https://api.dev-market-data.steeleye.co
MASTER_DATA_API_HOST=https://api.dev-master-data.steeleye.co
MASTER_DATA_HOST=https://api.dev-master-data.steeleye.co
AZURE_STORAGE_CONNECTION_STRING=
# TENANT_DB_PG_URL=postgresql://tenant:<EMAIL>:5432/tenant_db
## This env var is needed to fetch EOD Market Data for BestEx
## You can disable it if you're using the AWS_PROFILE that has access to the master data bucket
#ON_PREMISES=true

# AZURE

#DATA_PLATFORM_CONFIG_API_URL=https://config-api.nonprod-uk-so-1.steeleye.io
#ELASTIC_HOST=elasticsearch.dev-shared-2.steeleye.co
#ELASTIC_PORT=443
#ELASTIC_SCHEME=https
#ELASTIC_API_KEY=ZTU5OERvNEJRM0lZem1Yd3VnX2w6QWRuT3FLNllSUk9MQjB0c2k5a0IwQQ==
#STACK=dev-shared-2
#TASK_NAME=order_blotter
#TASK_WORKER_DOMAIN=dev-shared-2
#DEBUG=1
#SRP_THROUGH_MASTER_DATA=True
#FETCH_MARKET_EOD_DATA=false
#CONDUCTOR_API_URL=https://conductor.sit-enterprise.steeleye.io/api
#COGNITO_AUTH_URL=https://dev-master-data.auth.eu-west-1.amazoncognito.com/oauth2/token
#COGNITO_CLIENT_SECRET=
#COGNITO_CLIENT_ID=
#MARKET_DATA_API_URL=https://api.dev-market-data.steeleye.co
#MASTER_DATA_API_HOST=https://api.dev-master-data.steeleye.co
#MASTER_DATA_HOST=https://api.dev-master-data.steeleye.co
#AZURE_STORAGE_CONNECTION_STRING=

## This env var is needed to fetch EOD Market Data for BestEx
## You can disable it if you're using the AWS_PROFILE that has access to the master data bucket
#ON_PREMISES=true


