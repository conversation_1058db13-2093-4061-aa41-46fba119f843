from se_trades_tasks.abstractions.feed_schema import FeedSchema


class OrderCrdInputSchema:
    ACCT_NAME = FeedSchema(normalized_column_name="ACCT_NAME", column_data_type="string")
    BKR_NAME = FeedSchema(normalized_column_name="BKR_NAME", column_data_type="string")
    BKR_REASON = FeedSchema(normalized_column_name="BKR_REASON", column_data_type="string")
    COMMENTS = FeedSchema(normalized_column_name="COMMENTS", column_data_type="string")
    CREATE_DATE = FeedSchema(normalized_column_name="CREATE_DATE", column_data_type="string")
    CURRENCY = FeedSchema(normalized_column_name="CURRENCY", column_data_type="string")
    EXEC_PRICE = FeedSchema(normalized_column_name="EXEC_PRICE", column_data_type="float")
    EXEC_QTY = FeedSchema(normalized_column_name="EXEC_QTY", column_data_type="float")
    EXPIRE_DATE = FeedSchema(normalized_column_name="EXPIRE_DATE", column_data_type="string")
    EXT_SEC_ID = FeedSchema(normalized_column_name="EXT_SEC_ID", column_data_type="string")
    FRM_CURRENCY = FeedSchema(normalized_column_name="FRM_CURRENCY", column_data_type="string")
    FUNDID = FeedSchema(normalized_column_name="FUNDID", column_data_type="string")
    FUND_MANAGER_NAME = FeedSchema(
        normalized_column_name="FUND_MANAGER_NAME", column_data_type="string"
    )
    INITIAL_QTY = FeedSchema(normalized_column_name="INITIAL_QTY", column_data_type="float")
    INSTRUCTION = FeedSchema(normalized_column_name="INSTRUCTION", column_data_type="string")
    ISIN_NO = FeedSchema(normalized_column_name="ISIN_NO", column_data_type="string")
    ISO_EXCH_CD = FeedSchema(normalized_column_name="ISO_EXCH_CD", column_data_type="string")
    LAST_FILL = FeedSchema(normalized_column_name="LAST_FILL", column_data_type="string")
    MATURE_DATE = FeedSchema(normalized_column_name="MATURE_DATE", column_data_type="string")
    OPTION_TYPE = FeedSchema(normalized_column_name="OPTION_TYPE", column_data_type="string")
    ORDTR = FeedSchema(normalized_column_name="ORDTR", column_data_type="string")
    ORDER_DURATION = FeedSchema(normalized_column_name="ORDER_DURATION", column_data_type="string")
    ORDER_ID = FeedSchema(normalized_column_name="ORDER_ID", column_data_type="string")
    PLACE_DATE = FeedSchema(normalized_column_name="PLACE_DATE", column_data_type="string")
    RIC = FeedSchema(normalized_column_name="RIC", column_data_type="string")
    SALES_REP = FeedSchema(normalized_column_name="SALES_REP", column_data_type="string")
    SECTYPE = FeedSchema(normalized_column_name="SECTYPE", column_data_type="string")
    SEC_NAME = FeedSchema(normalized_column_name="SEC_NAME", column_data_type="string")
    SETTLE_DATE = FeedSchema(normalized_column_name="SETTLE_DATE", column_data_type="string")
    STATUS = FeedSchema(normalized_column_name="STATUS", column_data_type="string")
    STRIKE_PRICE = FeedSchema(normalized_column_name="STRIKE_PRICE", column_data_type="float")
    TICKER = FeedSchema(normalized_column_name="TICKER", column_data_type="string")
    TO_CURRENCY = FeedSchema(normalized_column_name="TO_CURRENCY", column_data_type="string")
    TRADE_DATE = FeedSchema(normalized_column_name="TRADE_DATE", column_data_type="string")
    TRADER = FeedSchema(normalized_column_name="TRADER", column_data_type="string")
    TRADE_ID = FeedSchema(normalized_column_name="TRADE_ID", column_data_type="string")
    TO_TRADER_DATE = FeedSchema(normalized_column_name="TO_TRADER_DATE", column_data_type="string")
