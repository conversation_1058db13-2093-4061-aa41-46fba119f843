import logging
from aries_task_link.models import AriesTaskInput, Aries<PERSON>askResult
from integration_trades_tasks.order.feeds.order_flextrade_bell_potter_fix.app_metrics_template import (  # noqa E501
    APP_METRICS,
)
from integration_trades_tasks.order.feeds.order_flextrade_bell_potter_fix.order_flextrade_bell_potter_fix_flow import (  # noqa E501
    order_flextrade_bell_potter_fix_flow,
)
from integration_wrapper.integration_aries_task import IntegrationAriesTask

logger = logging.getLogger("order_flextrade_bell_potter_fix_flow")


def order_flextrade_bell_potter_fix_run(aries_task_input: AriesTaskInput) -> AriesTaskResult:
    integration = IntegrationAriesTask(
        aries_task_input=aries_task_input,
        app_metrics_template=APP_METRICS,
    )

    return integration.execute(flow=order_flextrade_bell_potter_fix_flow)
