import os
import pandas as pd
import shutil
from addict import addict
from aries_config_cached_client.tenant_workflow import CachedTenantWorkflowAPIClient
from aries_se_core_tasks.aries.sink_file_audit.auditor_and_metrics_producer import (
    Metrics,
    RecordLevelAudit,
    run_auditor_and_metrics_producer,
)
from aries_se_core_tasks.aries.sink_file_audit.auditor_and_metrics_producer import (
    Params as AuditorAndMetricsProducerParams,
)
from aries_se_core_tasks.aries.sink_file_audit.static import (
    StandardAuditMessages,
    StatusDescriptionsOfSyntheticRecords,
)
from aries_se_core_tasks.aries.utility_tasks.finish_flow import add_nested_params, finish_flow
from aries_se_core_tasks.aries.utility_tasks.get_tenant_bucket import get_tenant_bucket
from aries_se_core_tasks.aries.utility_tasks.unpack_aries_task_input import unpack_aries_task_input
from aries_se_core_tasks.frame.filter_columns import Params as FilterColumnsParams
from aries_se_core_tasks.frame.filter_columns import run_filter_columns
from aries_se_core_tasks.frame.frame_column_manipulator import (
    Params as FrameColumnManipulatorParams,
)
from aries_se_core_tasks.frame.frame_column_manipulator import run_frame_column_manipulator
from aries_se_core_tasks.frame.frame_concatenator import Params as FrameConcatenatorParams
from aries_se_core_tasks.frame.frame_concatenator import run_frame_concatenator
from aries_se_core_tasks.frame.frame_splitter import Params as FrameSplitterParams
from aries_se_core_tasks.frame.frame_splitter import run_frame_splitter
from aries_se_core_tasks.frame.get_rows_by_condition import Params as GetRowsByConditionParams
from aries_se_core_tasks.frame.get_rows_by_condition import run_get_rows_by_condition
from aries_se_core_tasks.get_primary_transformations import run_get_primary_transformations
from aries_se_core_tasks.io.create_ndjson_path import create_ndjson_path
from aries_se_core_tasks.io.read.batch_producer import Params as BatchProducerParams
from aries_se_core_tasks.io.read.batch_producer import run_batch_producer
from aries_se_core_tasks.io.read.csv_file_extractor import (
    CsvFileExtractorResult,
    run_csv_file_extractor,
)
from aries_se_core_tasks.io.read.csv_file_extractor import Params as ExtractorParams
from aries_se_core_tasks.io.read.csv_file_splitter import Params as CsvSplitterParams
from aries_se_core_tasks.io.read.csv_file_splitter import run_csv_file_splitter
from aries_se_core_tasks.io.write.write_ndjson import run_write_ndjson
from aries_se_core_tasks.static import MetaModel
from aries_se_core_tasks.utilities.elasticsearch_utils import get_es_config
from aries_se_trades_tasks.instrument.instrument_fallback import Params as InstrumentFallbackParams
from aries_se_trades_tasks.instrument.instrument_fallback import run_instrument_fallback
from aries_se_trades_tasks.instrument.link_instrument import Params as LinkInstrumentParams
from aries_se_trades_tasks.instrument.link_instrument import run_link_instrument
from aries_se_trades_tasks.meta.assign_meta_parent import Params as MetaParentParams
from aries_se_trades_tasks.meta.assign_meta_parent import run_assign_meta_parent
from aries_se_trades_tasks.order.best_execution import run_best_execution
from aries_se_trades_tasks.order.static import OrderWorkflowNames
from aries_se_trades_tasks.order.transformations.feed.order_tradeweb.static import (
    TempColumns,
    TradeWebSourceColumns,
)
from aries_se_trades_tasks.order_app_metrics_enum import OrderAppMetricsEnum
from aries_se_trades_tasks.party.link_parties import Params as LinkPartiesParams
from aries_se_trades_tasks.party.link_parties import run_link_parties
from aries_se_trades_tasks.party.party_fallback_with_lei_lookup import Params as PartyFallbackParams
from aries_se_trades_tasks.party.party_fallback_with_lei_lookup import (
    run_party_fallback_with_lei_lookup,
)
from aries_se_trades_tasks.remove_duplicates.remove_duplicate_newo import (
    Params as RemoveDuplicateNewoParams,
)
from aries_se_trades_tasks.remove_duplicates.remove_duplicate_newo import run_remove_duplicate_newo
from aries_task_link.models import AriesTaskInput
from integration_trades_tasks.order.feeds.order_tradeweb.abstract_order_tradeweb_flow import (
    AbstractOrderTradeweb,
)
from integration_trades_tasks.order.feeds.order_tradeweb.input_schema import (
    OrderTradewebAriesTaskInput,
)
from integration_trades_tasks.order.feeds.order_tradeweb.order_tradeweb_schema import (
    OrderTradeWebInputSchema,
)
from se_core_tasks.core.core_dataclasses import CloudProviderEnum, FileSplitterResult
from se_core_tasks.frame.filter_columns import ActionEnum
from se_core_tasks.frame.frame_column_manipulator import Action
from se_core_tasks.frame.frame_concatenator import OrientEnum
from se_data_lake.cloud_utils import get_cloud_provider_from_file_uri, get_cloud_provider_prefix
from se_elastic_schema.elastic_schema.core.steeleye_schema_model import SteelEyeSchemaBaseModelES8
from se_elastic_schema.models import Order
from se_elasticsearch.repository import get_repository_by_cluster_version
from se_elasticsearch.repository.elasticsearch6 import ElasticsearchRepository
from se_enums.elastic_search import EsActionEnum
from se_io_utils.tempfile_utils import tmp_directory
from se_schema_meta import PARENT
from se_trades_tasks.abstractions.feed_schema import FeedSchema
from se_trades_tasks.order.static import ModelPrefix, OrderColumns, add_prefix
from se_trades_tasks.order_and_tr.instrument.fallback.instrument_fallback import (
    INSTRUMENT_PATH,
    FallbackInstrumentAttributes,
)
from se_trades_tasks.order_and_tr.static import InstrumentFields
from typing import List, Type

# supress chained_assignment warning to avoid polluting the logs
pd.options.mode.chained_assignment = None

FETCH_MARKET_EOD_DATA = os.getenv("FETCH_MARKET_EOD_DATA", "true").lower() == "true"


class OrderTradeWeb(AbstractOrderTradeweb):
    def run_flow(
        self,
        aries_task_input: AriesTaskInput,
        app_metrics_path: str,
        audit_path: str,
        result_path: str,
    ):
        # SETUP #

        # Parse and validate AriesTaskInput parameters
        order_tradeweb_input: OrderTradewebAriesTaskInput = unpack_aries_task_input(
            aries_task_input=aries_task_input, model=OrderTradewebAriesTaskInput
        )

        # Get tenant workflow tenant config from postgres
        cached_tenant_workflow_config: addict.Dict = CachedTenantWorkflowAPIClient.get(
            stack_name=aries_task_input.workflow.stack,
            tenant_name=aries_task_input.workflow.tenant,
            workflow_name=aries_task_input.workflow.name,
        )

        streamed: bool = cached_tenant_workflow_config.workflow.streamed

        # Create local temporary directory to store intermediate files
        tmp_storage: str = tmp_directory().as_posix()

        # Determine Cloud Provider
        cloud_provider = get_cloud_provider_from_file_uri(file_uri=order_tradeweb_input.file_uri)
        cloud_provider_prefix = get_cloud_provider_prefix(value=cloud_provider)

        # Determine the Cloud Bucket of the tenant
        tenant_bucket_with_cloud_prefix: str = get_tenant_bucket(
            task_input=order_tradeweb_input, cloud_provider_prefix=cloud_provider_prefix
        )

        es_client_tenant: ElasticsearchRepository = get_repository_by_cluster_version(
            resource_config=get_es_config()
        )

        used_fields = [
            value
            for name, value in OrderTradeWebInputSchema.__dict__.items()
            if isinstance(value, FeedSchema)
        ]

        # END SETUP #

        # BEGIN PRE-PROCESSING #

        # Extract the relevant subset of rows from the input CSV file and
        # produce a new smaller CSV file from it with the same header and encoding
        csv_extractor_result: CsvFileExtractorResult = run_csv_file_extractor(
            csv_file_uri=order_tradeweb_input.file_uri,
            target_dir=tmp_storage,
            params=ExtractorParams(
                skiprows=order_tradeweb_input.skiprows,
                nrows=order_tradeweb_input.nrows,
                populate_input_count_metrics=True,
            ),
            app_metrics_path=app_metrics_path,
            audit_path=audit_path,
        )

        # Read the input CSV file, normalise its columns,
        # and convert null-like strings to real null values.
        # This Task was used in Swarm-Flows to produce multiple CSV files
        # but that behavior is not needed here
        # as we are already working with a chunk of the original input file,
        # thus we are always
        # getting the first and only element of the resulting list of FileSplitterResults.
        csv_splitter_result: FileSplitterResult = run_csv_file_splitter(
            streamed=streamed,
            params=CsvSplitterParams(
                chunksize=order_tradeweb_input.nrows,
                encoding=csv_extractor_result.encoding,
                normalise_columns=True,
                audit_input_rows=False,
                drop_empty_rows=False,
            ),
            csv_path=str(csv_extractor_result.csv_file_path),
            realm=tenant_bucket_with_cloud_prefix,
            sources_dir=tmp_storage,
        )[0]

        # Run the BatchProducer Task to produce a Pandas DataFrame from the extracted CSV chunk
        # The Task also enforces datatypes, creates missing columns and
        # audits missing/unnecessary/empty columns
        # Multiple columns from the input csv are not used and therefore not included in used_fields
        # which causes the "Missing columns in data source" warning in audit that shows in Data Prov
        input_df: pd.DataFrame = run_batch_producer(
            streamed=streamed,
            params=BatchProducerParams(
                source_schema={
                    field.normalized_column_name: field.column_data_type for field in used_fields
                },
                audit_null_columns=False,
                remove_unknown_columns=True,
            ),
            file_splitter_result=csv_splitter_result,
            return_dataframe=True,
            skip_rows_count=order_tradeweb_input.skiprows,
            app_metrics_path=app_metrics_path,
            audit_path=audit_path,
        )

        # END PRE-PROCESSING #

        # BEGIN BUSINESS LOGIC #

        # Populate the majority of the target fields in a centralized transformations class
        transformed_df = run_get_primary_transformations(
            source_frame=input_df,
            flow=OrderWorkflowNames.ORDER_TRADEWEB,
            tenant=aries_task_input.workflow.tenant,
            realm=tenant_bucket_with_cloud_prefix,
            input_file_path=order_tradeweb_input.file_uri,
            app_metrics_path=app_metrics_path,
            audit_path=audit_path,
            es_client=es_client_tenant,
        )

        # Populate the `&parent` field for OrderStates
        meta_parent_df = run_assign_meta_parent(
            source_frame=transformed_df,
            params=MetaParentParams(
                parent_model_attribute=add_prefix(
                    prefix=ModelPrefix.ORDER, attribute=OrderColumns.META_MODEL
                ),
                parent_attributes_prefix=ModelPrefix.ORDER_DOT,
                target_attribute=add_prefix(prefix=ModelPrefix.ORDER_STATE, attribute=PARENT),
            ),
            app_metrics_path=app_metrics_path,
            audit_path=audit_path,
        )

        # Link identifiers built in Orders from
        # input party data with tenant MyMarket data
        link_parties_df = run_link_parties(
            tenant=aries_task_input.workflow.tenant,
            source_frame=transformed_df,
            params=LinkPartiesParams(identifiers_path=OrderColumns.MARKET_IDENTIFIERS_PARTIES),
            es_client=es_client_tenant,
            app_metrics_path=app_metrics_path,
            audit_path=audit_path,
        )

        party_fallback_input = run_frame_concatenator(
            transformed_df=transformed_df,
            link_parties_df=link_parties_df,
            params=FrameConcatenatorParams(orient=OrientEnum.horizontal),
            app_metrics_path=app_metrics_path,
            audit_path=audit_path,
        )

        # Create Party records embedded in the Orders for records
        # where LinkParties did not produce any hits
        party_fallback_df = run_party_fallback_with_lei_lookup(
            source_frame=party_fallback_input,
            params=PartyFallbackParams(),
        )

        # Link identifiers built in InstrumentIdentifier from input instrument
        # data with SRP and tenant instrument data
        link_instrument_df = run_link_instrument(
            source_frame=transformed_df,  # type: ignore
            params=LinkInstrumentParams(
                identifiers_path=OrderColumns.MARKET_IDENTIFIERS_INSTRUMENT,
                currency_attribute=OrderColumns.TRANSACTION_DETAILS_PRICE_CURRENCY,
                venue_attribute=OrderColumns.TRANSACTION_DETAILS_ULTIMATE_VENUE,
                master_data_api_enabled=True,
            ),
            tenant=aries_task_input.workflow.tenant,
            es_client_tenant=es_client_tenant,
        )

        instrument_fallback_input = run_frame_concatenator(
            transformed_df=transformed_df,
            link_instrument_df=link_instrument_df,
            params=FrameConcatenatorParams(orient=OrientEnum.horizontal),
            app_metrics_path=app_metrics_path,
            audit_path=audit_path,
        )

        # Create Instrument records embedded in the Orders for records
        # where LinkInstrument did not produce any hits
        instrument_fallback_df = run_instrument_fallback(
            source_frame=instrument_fallback_input,
            params=InstrumentFallbackParams(
                market_instrument_identifiers_attribute=OrderColumns.MARKET_IDENTIFIERS_INSTRUMENT,
                instrument_fields_map=[
                    FallbackInstrumentAttributes(
                        source_field=OrderColumns.TRANSACTION_DETAILS_PRICE_CURRENCY,
                        target_field=InstrumentFields.NOTIONAL_CURRENCY_1,
                    ),
                    FallbackInstrumentAttributes(
                        source_field=OrderColumns.TRANSACTION_DETAILS_ULTIMATE_VENUE,
                        target_field=InstrumentFields.EXT_VENUE_NAME,
                    ),
                    FallbackInstrumentAttributes(
                        source_field=OrderColumns.TRANSACTION_DETAILS_ULTIMATE_VENUE,
                        target_field=InstrumentFields.VENUE_TRADING_VENUE,
                    ),
                    FallbackInstrumentAttributes(
                        source_field=TempColumns.INSTRUMENT_CLASSIFICATION,
                        target_field=InstrumentFields.INSTRUMENT_CLASSIFICATION,
                    ),
                    FallbackInstrumentAttributes(
                        source_field="isin_attribute",
                        target_field=InstrumentFields.INSTRUMENT_ID_CODE,
                    ),
                    FallbackInstrumentAttributes(
                        source_field=TempColumns.MATURITY_DATE,
                        target_field=InstrumentFields.DERIVATIVE_EXPIRY_DATE,
                    ),
                    FallbackInstrumentAttributes(
                        source_field=TempColumns.INSTRUMENT_UNIQUE_IDENTIFIER,
                        target_field=InstrumentFields.EXT_INSTRUMENT_UNIQUE_ID,
                    ),
                    FallbackInstrumentAttributes(
                        source_field=TradeWebSourceColumns.SEC_DESCRIPTION,
                        target_field=InstrumentFields.INSTRUMENT_FULL_NAME,
                    ),
                    FallbackInstrumentAttributes(
                        source_field=OrderColumns.TRANSACTION_DETAILS_PRICE_NOTATION,
                        target_field=InstrumentFields.EXT_PRICE_NOTATION,
                    ),
                    FallbackInstrumentAttributes(
                        source_field=OrderColumns.TRANSACTION_DETAILS_QUANTITY_NOTATION,
                        target_field=InstrumentFields.EXT_QUANTITY_NOTATION,
                    ),
                    FallbackInstrumentAttributes(
                        source_field=TempColumns.CREATED_THROUGH_FALLBACK,
                        target_field=InstrumentFields.IS_CREATED_THROUGH_FALLBACK,
                    ),
                ],
                str_to_bool_dict={
                    "true": True,
                    "y": True,
                    "yes": True,
                    "t": True,
                    "on": True,
                    "false": False,
                    "n": False,
                    "no": False,
                    "f": False,
                    "off": False,
                },
                cfi_and_bestex_from_instrument_classification=True,
            ),
            app_metrics_path=app_metrics_path,
            audit_path=audit_path,
        )

        # Concat all relevant DataFrames and discard temporary
        # columns that must not be part of the final result
        aux_df = run_frame_concatenator(
            transformed_df=transformed_df,
            meta_parent_df=meta_parent_df,
            party_fallback_df=party_fallback_df,
            instrument_fallback_df=instrument_fallback_df,
            params=FrameConcatenatorParams(
                orient=OrientEnum.horizontal,
                drop_columns=[
                    TempColumns.CREATED_THROUGH_FALLBACK,
                    "_order.__meta_model__",
                    "_orderState.__meta_model__",
                    OrderColumns.MARKET_IDENTIFIERS_INSTRUMENT,
                    OrderColumns.MARKET_IDENTIFIERS_PARTIES,
                    TempColumns.MATURITY_DATE,
                    TempColumns.INSTRUMENT_UNIQUE_IDENTIFIER,
                    TradeWebSourceColumns.SEC_DESCRIPTION,
                    TempColumns.INSTRUMENT_CLASSIFICATION,
                    "__fallback_buyer__",
                    "__fallback_seller__",
                    "__fallback_counterparty__",
                    "__fallback_client__",
                    "__fallback_buyer_dec_maker__",
                    "__fallback_seller_dec_maker__",
                    "__fallback_inv_dec_in_firm__",
                    "__fallback_trader__",
                    "__fallback_exec_within_firm__",
                    "__fallback_executing_entity__",
                    "asset_class_attribute",
                    "bbg_figi_id_attribute",
                    "eurex_id_attribute",
                    "currency_attribute",
                    "expiry_date_attribute",
                    "interest_rate_start_date_attribute",
                    "isin_attribute",
                    "notional_currency_1_attribute",
                    "notional_currency_2_attribute",
                    "option_strike_price_attribute",
                    "option_type_attribute",
                    "swap_near_leg_date_attribute",
                    "underlying_index_name_attribute",
                    "underlying_index_name_leg_2_attribute",
                    "underlying_index_series_attribute",
                    "underlying_index_term_attribute",
                    "underlying_index_term_value_attribute",
                    "underlying_index_version_attribute",
                    "underlying_isin_attribute",
                    "underlying_symbol_attribute",
                    "underlying_symbol_expiry_code_attribute",
                    "underlying_index_term_leg_2_attribute",
                    "underlying_index_term_value_leg_2_attribute",
                    "venue_attribute",
                    "venue_financial_instrument_short_name_attribute",
                    "instrument_classification_attribute",
                ],
            ),
            app_metrics_path=app_metrics_path,
            audit_path=audit_path,
        )

        # Split the columns pertaining to Orders to a separate DataFrame
        order_records_df = run_frame_splitter(
            source_frame=aux_df,
            params=FrameSplitterParams(
                except_prefix=ModelPrefix.ORDER_STATE_DOT, strip_prefix=True
            ),
            app_metrics_path=app_metrics_path,
            audit_path=audit_path,
        )

        # Strip the Order prefix
        parsed_order_records_df = run_frame_column_manipulator(
            source_frame=order_records_df,
            params=FrameColumnManipulatorParams(action=Action.strip, prefix=ModelPrefix.ORDER_DOT),
            app_metrics_path=app_metrics_path,
            audit_path=audit_path,
        )

        # Split the columns pertaining to OrderStates to a separate DataFrame
        order_state_records_df = run_frame_splitter(
            source_frame=aux_df,
            params=FrameSplitterParams(except_prefix=ModelPrefix.ORDER_DOT, strip_prefix=True),
            app_metrics_path=app_metrics_path,
            audit_path=audit_path,
        )

        # Strip the OrderState prefix
        parsed_order_state_records_df = run_frame_column_manipulator(
            source_frame=order_state_records_df,
            params=FrameColumnManipulatorParams(
                action=Action.strip, prefix=ModelPrefix.ORDER_STATE_DOT
            ),
            app_metrics_path=app_metrics_path,
            audit_path=audit_path,
        )

        # Concat a final DataFrame that contains all Order +
        # OrderState records without any temporary columns/prefixes
        orders_and_order_states_df = run_frame_concatenator(
            parsed_order_records_df=parsed_order_records_df,
            parsed_order_state_records_df=parsed_order_state_records_df,
            params=FrameConcatenatorParams(
                orient=OrientEnum.vertical, reset_index=True, drop_index=True
            ),
            app_metrics_path=app_metrics_path,
            audit_path=audit_path,
        )

        # Exclude all records that do not have a value in the "executionDetails.orderStatus" field
        # Such records are either missing from the input data column `ORDERSTATUS` or they have
        # an invalid Order Status which is not part of the map defined in the PrimaryTransformations
        filtered_orders_and_order_states_df = run_get_rows_by_condition(
            source_frame=orders_and_order_states_df,
            params=GetRowsByConditionParams(
                query=f"`{OrderColumns.EXECUTION_DETAILS_ORDER_STATUS}`.notnull()"
            ),
            app_metrics_path=app_metrics_path,
            audit_path=audit_path,
        )

        # Remove duplicate NEWOs from dataset (i.e. synthetic NEWOs that were created unnecessarily)
        deduplicated_data_df = run_remove_duplicate_newo(
            source_frame=filtered_orders_and_order_states_df,
            params=RemoveDuplicateNewoParams(
                newo_in_file_col=TempColumns.NEWO_IN_FILE,
                drop_newo_in_file_col=False,
            ),
            tenant=aries_task_input.workflow.tenant,
            es_client=es_client_tenant,
            streamed=streamed,
            cloud_provider=cloud_provider,
            audit_path=audit_path,
            app_metrics_path=app_metrics_path,
        )

        best_execution_df = run_best_execution(
            source_frame=deduplicated_data_df,
            es_client=es_client_tenant,
            fetch_market_eod_data=FETCH_MARKET_EOD_DATA,
            streamed=streamed,
            app_metrics_path=app_metrics_path,
            audit_path=audit_path,
        )

        # Note that we are not discarding the `&parent` column here
        # this can be propagated to the ApplyMeta Conductor Task, which will reuse it
        final_result_df = run_frame_concatenator(
            best_execution_df=best_execution_df,
            deduplicated_data_df=deduplicated_data_df,
            params=FrameConcatenatorParams(
                orient=OrientEnum.horizontal, drop_columns=[OrderColumns.META_MODEL]
            ),
            app_metrics_path=app_metrics_path,
            audit_path=audit_path,
        )

        # CREATE synthetic NEWOs

        synthetic_newo_df = run_get_rows_by_condition(
            source_frame=final_result_df,
            params=GetRowsByConditionParams(
                query=f"`{OrderColumns.EXECUTION_DETAILS_ORDER_STATUS}`"
                f".astype('str').str.upper() == 'NEWO' & "
                f"`{TempColumns.NEWO_IN_FILE}`.astype('str').str.lower() == 'false'"  # noqa: E501
            ),
        )
        if synthetic_newo_df.empty:
            synthetic_newo_ndjson_path = None

        else:
            synthetic_newo_df = run_filter_columns(
                source_frame=synthetic_newo_df,
                params=FilterColumnsParams(
                    columns=[TempColumns.NEWO_IN_FILE], action=ActionEnum.drop
                ),
                app_metrics_path=app_metrics_path,
                audit_path=audit_path,
                skip_serializer=True,
            )

            # Audit each synthetic NEWO and populate the app metrics synthetic newo count
            run_auditor_and_metrics_producer(
                source_frame=synthetic_newo_df,
                params=AuditorAndMetricsProducerParams(
                    record_level_audits=[
                        RecordLevelAudit(
                            query="index==index",
                            status_message=StatusDescriptionsOfSyntheticRecords.IS_SYNTHETIC_NEWO,
                            meta_model=Order,
                        )
                    ],
                    metrics=[
                        Metrics(
                            query="index==index",
                            field=OrderAppMetricsEnum.OUTPUT_SYNTHETIC_NEWOS_COUNT,
                        )
                    ],
                    models=[Order],
                ),
                streamed=streamed,
                cloud_provider=cloud_provider,
                app_metrics_path=app_metrics_path,
                audit_path=audit_path,
            )

            self._audit_fallbacks(
                df=synthetic_newo_df,
                col_name=INSTRUMENT_PATH,
                fallback_key="isCreatedThroughFallback",
                status_message=StandardAuditMessages.INSTRUMENT_THROUGH_FALLBACK,
                meta_model=Order,
                models=[Order],
                streamed=streamed,
                app_metrics_path=app_metrics_path,
                audit_path=audit_path,
                cloud_provider=cloud_provider,
            )
            synthetic_newo_ndjson_path = create_ndjson_path(
                tenant_bucket=tenant_bucket_with_cloud_prefix,
                aries_task_input=aries_task_input,
                model=MetaModel.ORDER,
                suffix="synthetic",
            )

            # Write the transformed_df data frame into a ndjson file to the generated ndjson path
            run_write_ndjson(
                source_serializer_result=synthetic_newo_df,
                output_filepath=synthetic_newo_ndjson_path,
                audit_output=True,
                app_metrics_path=app_metrics_path,
                audit_path=audit_path,
            )

        synthetic_newo_output = add_nested_params(
            file_uri=synthetic_newo_ndjson_path,
            es_action=EsActionEnum.CREATE,
            data_model=Order.get_reference().get_qualified_reference(),
        )

        # INDEX Orders

        orders_df = run_get_rows_by_condition(
            source_frame=final_result_df,
            params=GetRowsByConditionParams(
                query=f"(`{OrderColumns.EXECUTION_DETAILS_ORDER_STATUS}`.astype('str').str.upper() == 'NEWO' & `{TempColumns.NEWO_IN_FILE}`.astype('str').str.lower() == 'true') | (`{OrderColumns.EXECUTION_DETAILS_ORDER_STATUS}`.str.upper() != 'NEWO')"  # noqa: E501
            ),
        )

        orders_df = run_filter_columns(
            source_frame=orders_df,
            params=FilterColumnsParams(columns=[TempColumns.NEWO_IN_FILE], action=ActionEnum.drop),
            app_metrics_path=app_metrics_path,
            audit_path=audit_path,
        )

        if orders_df.empty:
            orders_ndjson_path = None
        else:
            self._audit_fallbacks(
                df=orders_df.frame(),
                col_name=INSTRUMENT_PATH,
                fallback_key="isCreatedThroughFallback",
                status_message=StandardAuditMessages.INSTRUMENT_THROUGH_FALLBACK,
                meta_model=Order,
                models=[Order],
                streamed=streamed,
                cloud_provider=cloud_provider,
                app_metrics_path=app_metrics_path,
                audit_path=audit_path,
            )

            orders_ndjson_path = create_ndjson_path(
                tenant_bucket=tenant_bucket_with_cloud_prefix,
                aries_task_input=aries_task_input,
                model=MetaModel.ORDER,
            )

            run_write_ndjson(
                source_serializer_result=orders_df,
                output_filepath=orders_ndjson_path,
                audit_output=True,
                app_metrics_path=app_metrics_path,
                audit_path=audit_path,
            )

        orders_output = add_nested_params(
            file_uri=orders_ndjson_path,
            es_action=EsActionEnum.INDEX,
            data_model=Order.get_reference().get_qualified_reference(),
        )

        shutil.rmtree(tmp_storage)

        finish_flow(
            result_path=result_path,
            result_data={
                "synthetic_newo": synthetic_newo_output,
                "orders": orders_output,
            },
        )

    @staticmethod
    def _audit_fallbacks(
        df: pd.DataFrame,
        col_name: str,
        fallback_key: str,
        status_message: str,
        meta_model: Type[SteelEyeSchemaBaseModelES8],
        models: List[Type[SteelEyeSchemaBaseModelES8]],
        streamed: bool,
        cloud_provider: CloudProviderEnum,
        app_metrics_path: str,
        audit_path: str,
    ):
        instruments_created_through_fallback = (
            df.loc[:, col_name].str.get(fallback_key).fillna(False)  # type: ignore
        )
        if any(instruments_created_through_fallback):
            run_auditor_and_metrics_producer(
                source_frame=df.loc[instruments_created_through_fallback, :],
                params=AuditorAndMetricsProducerParams(
                    record_level_audits=[
                        RecordLevelAudit(
                            query="index==index",
                            status_message=status_message,
                            meta_model=meta_model,
                        )
                    ],
                    metrics=[
                        Metrics(
                            query="index==index",
                            field=OrderAppMetricsEnum.SYNTHETIC_INSTRUMENTS_COUNT,
                        )
                    ],
                    models=models,
                ),
                streamed=streamed,
                cloud_provider=cloud_provider,
                app_metrics_path=app_metrics_path,
                audit_path=audit_path,
            )


def order_tradeweb_flow(
    flow_override_class,
    aries_task_input: AriesTaskInput,
    app_metrics_path: str,
    audit_path: str,
    result_path: str,
):
    flow_override_class().run_flow(
        aries_task_input=aries_task_input,
        app_metrics_path=app_metrics_path,
        audit_path=audit_path,
        result_path=result_path,
    )
