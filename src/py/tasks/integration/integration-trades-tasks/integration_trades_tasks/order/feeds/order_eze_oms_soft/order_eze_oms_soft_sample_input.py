import datetime
from aries_io_event.io_param import IOParamFieldSet
from aries_io_event.task import TaskFieldSet
from aries_io_event.workflow import WorkflowFieldSet
from aries_task_link.models import AriesTaskInput


# AWS ES8 input
def sample_input() -> AriesTaskInput:
    workflow = WorkflowFieldSet(
        trace_id="test_eze_oms_soft",
        name="order_eze_oms_soft",
        stack="dev-shared-2",
        tenant="mares8",
        start_timestamp=datetime.datetime.utcnow(),
    )
    input_param = IOParamFieldSet(
        params=dict(
            file_uri="s3://mares8.dev.steeleye.co/aries/ingest/order_eze_oms_soft/2024/10/29/fe98a870654247b99c117e4ecfd5ff9d/file_splitter_by_criteria/batch_0.csv",
            skiprows=1,
            nrows=6,
        )
    )
    task = TaskFieldSet(name="order_eze_oms_soft", version="latest", success=False)
    return AriesTaskInput(workflow=workflow, input_param=input_param, task=task)
