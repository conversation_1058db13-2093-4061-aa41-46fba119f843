from aries_se_core_tasks.core.generic_app_metrics_enum import GenericAppMetricsEnum
from aries_se_trades_tasks.order_app_metrics_enum import OrderAppMetricsEnum
from integration_wrapper.app_metrics_enum import IntegrationAriesTaskAppMetricsEnum

METRICS_ENUMS = [GenericAppMetricsEnum, IntegrationAriesTaskAppMetricsEnum, OrderAppMetricsEnum]
METRICS_LIST = [val for sub_list in [_enum.list() for _enum in METRICS_ENUMS] for val in sub_list]
APP_METRICS = {v: 0 for v in METRICS_LIST}
