import logging
from aries_task_link.models import AriesTaskInput, AriesTaskResult
from integration_trades_tasks.order.feeds.order_crd.app_metrics_template import APP_METRICS
from integration_trades_tasks.order.feeds.order_crd.order_crd_flow import (
    DefaultOrderCrd,
    order_crd_flow,
)
from integration_wrapper.integration_aries_task import IntegrationAriesTask

logger = logging.getLogger("order_crd_flow")


def order_crd_run(aries_task_input: AriesTaskInput) -> AriesTaskResult:
    integration = IntegrationAriesTask(
        aries_task_input=aries_task_input,
        app_metrics_template=APP_METRICS,
    )

    tenant: str = aries_task_input.workflow.tenant

    flow_overrides_map = {"default": DefaultOrderCrd, "allspring": DefaultOrderCrd}

    flow_override_class = flow_overrides_map.get(tenant, flow_overrides_map["default"])

    return integration.execute(flow=order_crd_flow, flow_override_class=flow_override_class)
