import logging
from aries_task_link.models import AriesTaskInput, AriesTaskResult
from integration_trades_tasks.order.feeds.order_eze_oms_soft.app_metrics_template import (
    APP_METRICS,
)
from integration_trades_tasks.order.feeds.order_eze_oms_soft.order_eze_oms_soft_flow import (
    order_eze_oms_soft_flow,
)
from integration_wrapper.integration_aries_task import IntegrationAriesTask

logger = logging.getLogger("order_thornbridge_crims_flow")


def order_eze_oms_soft_run(aries_task_input: AriesTaskInput) -> AriesTaskResult:
    integration = IntegrationAriesTask(
        aries_task_input=aries_task_input,
        app_metrics_template=APP_METRICS,
    )

    return integration.execute(flow=order_eze_oms_soft_flow)
