from integration_wrapper.integration_aries_task_input import IntegrationAriesTaskInput


class OrderEzeSoftOmsAriesTaskInput(IntegrationAriesTaskInput):
    """The combination of `skiprows` and `nrows` is what allows the
    OrderThornbridgeCrims task to read only a sub-set of the input CSV file's
    rows.

    The workflow contains a FileSpliceGenerator Task that will
    essentially "splice" the input CSV file in multiple events, each
    with their own `skiprows` value + the `nrows` which is the
    equivalent to the chunk size. All events will be fed into a Dynamic
    Fork Join in Conductor, and each OrderThornbridgeCrims worker will
    process a subset of the original CSV file.
    """
