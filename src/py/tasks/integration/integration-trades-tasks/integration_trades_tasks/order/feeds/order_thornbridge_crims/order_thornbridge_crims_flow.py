# mypy: disable-error-code="attr-defined"
import pandas as pd
import shutil
from addict import addict
from aries_config_cached_client.tenant_workflow import CachedTenantWorkflowAPIClient
from aries_se_core_tasks.aries.sink_file_audit.auditor_and_metrics_producer import (
    Metrics,
    RecordLevelAudit,
    run_auditor_and_metrics_producer,
)
from aries_se_core_tasks.aries.sink_file_audit.auditor_and_metrics_producer import (
    Params as AuditorAndMetricsProducerParams,
)
from aries_se_core_tasks.aries.sink_file_audit.static import (
    StandardAuditMessages,
    StatusDescriptionsOfSyntheticRecords,
)
from aries_se_core_tasks.aries.utility_tasks.finalize_task import (
    create_path_and_upload_model_results,
)
from aries_se_core_tasks.aries.utility_tasks.finish_flow import add_nested_params, finish_flow
from aries_se_core_tasks.aries.utility_tasks.get_tenant_bucket import get_tenant_bucket
from aries_se_core_tasks.aries.utility_tasks.unpack_aries_task_input import unpack_aries_task_input
from aries_se_core_tasks.frame.frame_column_manipulator import (
    Params as FrameColumnManipulatorParams,
)
from aries_se_core_tasks.frame.frame_column_manipulator import (
    run_frame_column_manipulator,
)
from aries_se_core_tasks.frame.frame_concatenator import run_frame_concatenator
from aries_se_core_tasks.frame.frame_splitter import (
    Params as FrameSplitterParams,
)
from aries_se_core_tasks.frame.frame_splitter import (
    run_frame_splitter,
)
from aries_se_core_tasks.frame.get_rows_by_condition import (
    Params as GetRowsByConditionParams,
)
from aries_se_core_tasks.frame.get_rows_by_condition import (
    run_get_rows_by_condition,
)
from aries_se_core_tasks.get_primary_transformations import run_get_primary_transformations
from aries_se_core_tasks.io.create_ndjson_path import create_ndjson_path
from aries_se_core_tasks.io.read.batch_producer import run_batch_producer
from aries_se_core_tasks.io.read.csv_file_extractor import (
    CsvFileExtractorResult,
    run_csv_file_extractor,
)
from aries_se_core_tasks.io.read.csv_file_extractor import Params as ExtractorParams
from aries_se_core_tasks.io.read.csv_file_splitter import run_csv_file_splitter
from aries_se_core_tasks.io.write.write_ndjson import run_write_ndjson
from aries_se_core_tasks.static import MetaModel
from aries_se_core_tasks.utilities.elasticsearch_utils import get_es_config, get_srp_es_config
from aries_se_trades_tasks.instrument.instrument_fallback import run_instrument_fallback
from aries_se_trades_tasks.instrument.link_instrument import run_link_instrument
from aries_se_trades_tasks.meta.assign_meta_parent import run_assign_meta_parent
from aries_se_trades_tasks.order.best_execution import run_best_execution
from aries_se_trades_tasks.order.static import OrderWorkflowNames
from aries_se_trades_tasks.order_app_metrics_enum import OrderAppMetricsEnum
from aries_se_trades_tasks.orders_and_tr.quarantine import (
    check_if_tenant_in_quarantine_disabled_list,
    run_quarantined_records,
)
from aries_se_trades_tasks.party.link_parties import run_link_parties
from aries_se_trades_tasks.party.party_fallback_with_lei_lookup import (
    run_party_fallback_with_lei_lookup,
)
from aries_se_trades_tasks.remove_duplicates.remove_duplicate_newo import (
    Params as RemoveDuplicateNewoParams,
)
from aries_se_trades_tasks.remove_duplicates.remove_duplicate_newo import (
    run_remove_duplicate_newo,
)
from aries_task_link.models import AriesTaskInput
from integration_trades_tasks.order.feeds.order_thornbridge_crims.input_schema import (
    OrderThornbridgeCrimsAriesTaskInput,
)
from pathlib import Path
from pydantic import BaseSettings, Field
from se_core_tasks.core.core_dataclasses import FileSplitterResult
from se_core_tasks.frame.frame_column_manipulator import Action
from se_core_tasks.frame.frame_concatenator import OrientEnum
from se_core_tasks.frame.frame_concatenator import Params as FrameConcatenatorParams
from se_core_tasks.io.read.batch_producer import Params as BatchProducerParams
from se_core_tasks.io.read.csv_file_splitter import Params as CsvSplitterParams
from se_core_tasks.utils.cloud import copy_file
from se_data_lake.cloud_utils import get_cloud_provider_from_file_uri, get_cloud_provider_prefix
from se_elastic_schema.elastic_schema.core.steeleye_schema_model import SteelEyeSchemaBaseModelES8
from se_elastic_schema.models import Order, QuarantinedOrder, SinkRecordAudit
from se_elastic_schema.static.mifid2 import OrderStatus
from se_elasticsearch.repository import get_repository_by_cluster_version
from se_elasticsearch.repository.elasticsearch6 import ElasticsearchRepository
from se_enums.cloud import CloudProviderEnum
from se_enums.elastic_search import EsActionEnum
from se_io_utils.tempfile_utils import tmp_directory
from se_schema_meta import PARENT
from se_trades_tasks.meta.assign_meta_parent import Params as MetaParentParams
from se_trades_tasks.order.party.fallback.party_fallback_with_lei_lookup import (
    Params as PartyFallbackParams,
)
from se_trades_tasks.order.party.static import (
    PARTY_FILE_ID_FALLBACK_COL_MAPPING,
)
from se_trades_tasks.order.static import ModelPrefix, OrderColumns, OrderTempColumns, add_prefix
from se_trades_tasks.order.transformations.thornbridge.static import (
    ThornbridgeCrimsOrderTempColumns,
)
from se_trades_tasks.order_and_tr.instrument.fallback.instrument_fallback import (
    FallbackInstrumentAttributes,
)
from se_trades_tasks.order_and_tr.instrument.fallback.instrument_fallback import (
    Params as InstrumentFallbackParams,
)
from se_trades_tasks.order_and_tr.instrument.link.link_instrument import (
    Params as LinkInstrumentParams,
)
from se_trades_tasks.order_and_tr.party.link_parties import Params as LinkPartiesParams
from se_trades_tasks.order_and_tr.static import INSTRUMENT_PATH, InstrumentFields
from se_trades_tasks.tr.transformations.thornbridge.crims.static import (
    ThornbridgeCrimsSourceColumns,
)
from typing import List, Type

# supress chained_assignment warning to avoid polluting the logs
pd.options.mode.chained_assignment = None

TR_SWARM_FLOWS_PATH = "flows/tr-feed-thornbridge-crims"


class OrderThornbridgeCrimsSettings(BaseSettings):
    srp_through_master_data: bool = Field(default=False, env="SRP_THROUGH_MASTER_DATA")


def order_thornbridge_crims_flow(
    aries_task_input: AriesTaskInput,
    app_metrics_path: str,
    audit_path: str,
    result_path: str,
):
    # SETUP #
    env_config = OrderThornbridgeCrimsSettings()
    # Parse and validate AriesTaskInput parameters
    order_thornbridge_crims_input: OrderThornbridgeCrimsAriesTaskInput = unpack_aries_task_input(
        aries_task_input=aries_task_input, model=OrderThornbridgeCrimsAriesTaskInput
    )

    # Get tenant workflow tenant config from postgres
    cached_tenant_workflow_config: addict.Dict = CachedTenantWorkflowAPIClient.get(
        stack_name=aries_task_input.workflow.stack,
        tenant_name=aries_task_input.workflow.tenant,
        workflow_name=aries_task_input.workflow.name,
    )

    streamed: bool = cached_tenant_workflow_config.workflow.streamed
    tenant: str = aries_task_input.workflow.tenant

    # Create local temporary directory to store intermediate files
    tmp_storage: str = tmp_directory().as_posix()

    # Determine Cloud Provider
    cloud_provider = get_cloud_provider_from_file_uri(
        file_uri=order_thornbridge_crims_input.file_uri
    )
    cloud_provider_prefix = get_cloud_provider_prefix(value=cloud_provider)

    # Determine the Cloud Bucket of the tenant
    tenant_bucket_with_cloud_prefix: str = get_tenant_bucket(
        task_input=order_thornbridge_crims_input, cloud_provider_prefix=cloud_provider_prefix
    )

    es_client_tenant: ElasticsearchRepository = get_repository_by_cluster_version(
        resource_config=get_es_config()
    )
    es_client_srp = instantiate_srp_es_client(
        srp_through_master_data=env_config.srp_through_master_data
    )

    target_file_name = Path(order_thornbridge_crims_input.file_uri).name
    target_file_path = (
        f"{tenant_bucket_with_cloud_prefix.rstrip('/')}"
        f"/{TR_SWARM_FLOWS_PATH.rstrip('/')}/{target_file_name}"
    )
    # COPY FILE to trigger Swarm TR Flow
    copy_file(source=order_thornbridge_crims_input.file_uri, target=target_file_path)

    # END SETUP #

    # BEGIN PRE-PROCESSING #

    # Extract the relevant subset of rows from the input CSV file and
    # produce a new smaller CSV file from it with the same header and encoding
    csv_extractor_result: CsvFileExtractorResult = run_csv_file_extractor(
        csv_file_uri=order_thornbridge_crims_input.file_uri,
        target_dir=tmp_storage,
        params=ExtractorParams(
            skiprows=order_thornbridge_crims_input.skiprows,
            nrows=order_thornbridge_crims_input.nrows,
            populate_input_count_metrics=True,
        ),
        app_metrics_path=app_metrics_path,
        audit_path=audit_path,
    )

    # Read the input CSV file, normalise its columns,
    # and convert null-like "string",s to real null values.
    # This Task was used in Swarm-Flows to produce multiple CSV files
    # but that behavior is not needed here
    # as we are already working with a chunk of the original input file,
    # thus we are always
    # getting the first and only element of the resulting list of FileSplitterResults.
    csv_splitter_result: FileSplitterResult = run_csv_file_splitter(
        streamed=streamed,
        params=CsvSplitterParams(
            chunksize=order_thornbridge_crims_input.nrows,
            encoding=csv_extractor_result.encoding,
            normalise_columns=True,
            audit_input_rows=False,
            drop_empty_rows=False,
            delimiter="\t",
        ),
        csv_path=str(csv_extractor_result.csv_file_path),
        realm=tenant_bucket_with_cloud_prefix,
        sources_dir=tmp_storage,
    )[0]

    # Run the BatchProducer Task to produce a Pandas DataFrame from the extracted CSV chunk
    # The Task also enforces datatypes, creates missing columns and
    # audits missing/unnecessary/empty columns
    input_df: pd.DataFrame = run_batch_producer(
        streamed=streamed,
        params=BatchProducerParams(
            source_schema={
                ThornbridgeCrimsSourceColumns.BUYER_DM_ID: "string",
                ThornbridgeCrimsSourceColumns.BUYER_ID: "string",
                ThornbridgeCrimsSourceColumns.BUYER_PERS_NO: "string",
                ThornbridgeCrimsSourceColumns.EXE_DEC_WITHIN_FIRM: "string",
                ThornbridgeCrimsSourceColumns.EXECUTING_ENTITY_ID_CODE: "string",
                ThornbridgeCrimsSourceColumns.INSTRUMENT_ID: "string",
                ThornbridgeCrimsSourceColumns.INV_DEC_WITHIN_FIRM: "string",
                ThornbridgeCrimsSourceColumns.NET_AMOUNT: "float",
                ThornbridgeCrimsSourceColumns.NOMINAL_TYPE: "string",
                ThornbridgeCrimsSourceColumns.NO_NOMINAL: "string",
                ThornbridgeCrimsSourceColumns.PRICE: "float",
                ThornbridgeCrimsSourceColumns.PRICE_CCY: "string",
                ThornbridgeCrimsSourceColumns.PRICE_TYPE: "string",
                ThornbridgeCrimsSourceColumns.SEC_FIN_TR_INDIC: "string",
                ThornbridgeCrimsSourceColumns.SELLER_ID: "string",
                ThornbridgeCrimsSourceColumns.SELLER_DM_ID: "string",
                ThornbridgeCrimsSourceColumns.SELLER_PERS_NO: "string",
                ThornbridgeCrimsSourceColumns.SUBMITTING_ENTITY_ID_CODE: "string",
                ThornbridgeCrimsSourceColumns.TRADE_DATE: "string",
                ThornbridgeCrimsSourceColumns.TRADE_TIME: "string",
                ThornbridgeCrimsSourceColumns.TRADING_CAPACITY: "string",
                ThornbridgeCrimsSourceColumns.TRANSACTION_REFERENCE_NUMBER: "string",
                ThornbridgeCrimsSourceColumns.VENUE: "string",
            },
            audit_null_columns=False,
        ),
        file_splitter_result=csv_splitter_result,
        return_dataframe=True,
        skip_rows_count=order_thornbridge_crims_input.skiprows,
        app_metrics_path=app_metrics_path,
        audit_path=audit_path,
    )

    # END PRE-PROCESSING #

    # BEGIN BUSINESS LOGIC #

    # Populate the majority of the target fields in a centralized transformations class
    order_mappings_df = run_get_primary_transformations(
        source_frame=input_df,
        flow=OrderWorkflowNames.ORDER_THORNBRIDGE_CRIMS,
        tenant=aries_task_input.workflow.tenant,
        realm=tenant_bucket_with_cloud_prefix,
        source_file_uri=order_thornbridge_crims_input.file_uri,
        app_metrics_path=app_metrics_path,
        audit_path=audit_path,
        es_client=es_client_tenant,
    )

    # Populate the `&parent` field for OrderStates
    meta_parent_df = run_assign_meta_parent(
        source_frame=order_mappings_df,
        params=MetaParentParams(
            parent_model_attribute=add_prefix(
                prefix=ModelPrefix.ORDER, attribute=OrderColumns.META_MODEL
            ),
            parent_attributes_prefix=ModelPrefix.ORDER_DOT,
            target_attribute=add_prefix(prefix=ModelPrefix.ORDER_STATE, attribute=PARENT),
        ),
        app_metrics_path=app_metrics_path,
        audit_path=audit_path,
    )

    # Link identifiers built in InstrumentIdentifier from input instrument
    # data with SRP and tenant instrument data
    link_instrument_df = run_link_instrument(
        source_frame=order_mappings_df,
        params=LinkInstrumentParams(
            identifiers_path=OrderColumns.MARKET_IDENTIFIERS_INSTRUMENT,
            currency_attribute=OrderColumns.TRANSACTION_DETAILS_PRICE_CURRENCY,
            venue_attribute=OrderColumns.TRANSACTION_DETAILS_ULTIMATE_VENUE,
        ),
        tenant=aries_task_input.workflow.tenant,
        es_client_srp=es_client_srp,
        es_client_tenant=es_client_tenant,
    )

    # Link identifiers built in BlotterPartyIdentifiers from
    # input party data with tenant MyMarket data
    link_parties_df = run_link_parties(
        tenant=aries_task_input.workflow.tenant,
        source_frame=order_mappings_df,
        params=LinkPartiesParams(identifiers_path=OrderColumns.MARKET_IDENTIFIERS_PARTIES),
        es_client=es_client_tenant,
        app_metrics_path=app_metrics_path,
        audit_path=audit_path,
    )

    party_fallback_input = run_frame_concatenator(
        transformed_df=order_mappings_df,
        link_parties_df=link_parties_df,
        params=FrameConcatenatorParams(orient=OrientEnum.horizontal),
        app_metrics_path=app_metrics_path,
        audit_path=audit_path,
    )

    # Create Party records embedded in the Orders for records
    # where LinkParties did not produce any hits
    party_fallback_df = run_party_fallback_with_lei_lookup(
        source_frame=party_fallback_input,
        params=PartyFallbackParams(),
    )

    instrument_fallback_input = run_frame_concatenator(
        transformed_df=order_mappings_df,
        link_instrument_df=link_instrument_df,
        params=FrameConcatenatorParams(orient=OrientEnum.horizontal),
        app_metrics_path=app_metrics_path,
        audit_path=audit_path,
    )

    # Create Instrument records embedded in the Orders for records
    # where LinkInstrument did not produce any hits
    instrument_fallback_df = run_instrument_fallback(
        source_frame=instrument_fallback_input,
        params=InstrumentFallbackParams(
            market_instrument_identifiers_attribute=OrderColumns.MARKET_IDENTIFIERS_INSTRUMENT,
            instrument_fields_map=[
                # InstrumentFields.EXT_VENUE_NAME, InstrumentFields.VENUE_TRADING_VENUE
                # and InstrumentFields.INSTRUMENT_ID_CODE are populated directly from
                # the respective instrument identifier columns in
                # INSTRUMENT_IDENTIFIER_TO_INSTRUMENT_FIELD_MAP
                FallbackInstrumentAttributes(
                    source_field=OrderColumns.TRANSACTION_DETAILS_PRICE_CURRENCY,
                    target_field=InstrumentFields.NOTIONAL_CURRENCY_1,
                ),
                FallbackInstrumentAttributes(
                    source_field=ThornbridgeCrimsOrderTempColumns.INSTRUMENT_FULL_NAME,
                    target_field=InstrumentFields.INSTRUMENT_FULL_NAME,
                ),
                FallbackInstrumentAttributes(
                    source_field=ThornbridgeCrimsOrderTempColumns.INSTRUMENT_UNIQUE_IDENTIFIER,
                    target_field=InstrumentFields.EXT_INSTRUMENT_UNIQUE_ID,
                ),
                FallbackInstrumentAttributes(
                    source_field=ThornbridgeCrimsOrderTempColumns.INSTRUMENT_IS_CREATED_THROUGH_FALLBACK,
                    target_field=InstrumentFields.IS_CREATED_THROUGH_FALLBACK,
                ),
            ],
            str_to_bool_dict={
                "true": True,
                "false": False,
            },
        ),
        app_metrics_path=app_metrics_path,
        audit_path=audit_path,
    )

    # Concat all relevant DataFrames and discard temporary
    # columns that must not be part of the final result
    aux_df = run_frame_concatenator(
        order_mappings_df=order_mappings_df,
        meta_parent_df=meta_parent_df,
        party_fallback_df=party_fallback_df,
        instrument_fallback_df=instrument_fallback_df,
        params=FrameConcatenatorParams(
            orient=OrientEnum.horizontal,
            drop_columns=[
                OrderColumns.MARKET_IDENTIFIERS_INSTRUMENT,
                OrderColumns.MARKET_IDENTIFIERS_PARTIES,
                OrderColumns.META_MODEL,
                ThornbridgeCrimsOrderTempColumns.INSTRUMENT_FULL_NAME,
                ThornbridgeCrimsOrderTempColumns.INSTRUMENT_UNIQUE_IDENTIFIER,
                ThornbridgeCrimsOrderTempColumns.INSTRUMENT_IS_CREATED_THROUGH_FALLBACK,
                "asset_class_attribute",
                "bbg_figi_id_attribute",
                "eurex_id_attribute",
                "currency_attribute",
                "expiry_date_attribute",
                "interest_rate_start_date_attribute",
                "isin_attribute",
                "notional_currency_1_attribute",
                "notional_currency_2_attribute",
                "option_strike_price_attribute",
                "option_type_attribute",
                "swap_near_leg_date_attribute",
                "underlying_index_name_attribute",
                "underlying_index_name_leg_2_attribute",
                "underlying_index_series_attribute",
                "underlying_index_term_attribute",
                "underlying_index_term_value_attribute",
                "underlying_index_version_attribute",
                "underlying_isin_attribute",
                "underlying_symbol_attribute",
                "underlying_symbol_expiry_code_attribute",
                "underlying_index_term_leg_2_attribute",
                "underlying_index_term_value_leg_2_attribute",
                "venue_attribute",
                "venue_financial_instrument_short_name_attribute",
                "instrument_classification_attribute",
            ]
            + list(PARTY_FILE_ID_FALLBACK_COL_MAPPING.values()),
        ),
        app_metrics_path=app_metrics_path,
        audit_path=audit_path,
    )

    # Split the columns pertaining to Orders to a separate DataFrame
    order_records_df = run_frame_splitter(
        source_frame=aux_df,
        params=FrameSplitterParams(except_prefix=ModelPrefix.ORDER_STATE_DOT, strip_prefix=True),
        app_metrics_path=app_metrics_path,
        audit_path=audit_path,
    )

    # Strip the Order prefix
    parsed_order_records_df = run_frame_column_manipulator(
        source_frame=order_records_df,
        params=FrameColumnManipulatorParams(action=Action.strip, prefix=ModelPrefix.ORDER_DOT),
        app_metrics_path=app_metrics_path,
        audit_path=audit_path,
    )

    # Split the columns pertaining to OrderStates to a separate DataFrame
    order_state_records_df = run_frame_splitter(
        source_frame=aux_df,
        params=FrameSplitterParams(except_prefix=ModelPrefix.ORDER_DOT, strip_prefix=True),
        app_metrics_path=app_metrics_path,
        audit_path=audit_path,
    )

    # Strip the OrderState prefix
    parsed_order_state_records_df = run_frame_column_manipulator(
        source_frame=order_state_records_df,
        params=FrameColumnManipulatorParams(
            action=Action.strip, prefix=ModelPrefix.ORDER_STATE_DOT
        ),
        app_metrics_path=app_metrics_path,
        audit_path=audit_path,
    )

    # Concat a final DataFrame that contains all Order +
    # OrderState records without any temporary columns/prefixes
    orders_and_order_states_df = run_frame_concatenator(
        parsed_order_records_df=parsed_order_records_df,
        parsed_order_state_records_df=parsed_order_state_records_df,
        params=FrameConcatenatorParams(
            orient=OrientEnum.vertical, reset_index=True, drop_index=True
        ),
        app_metrics_path=app_metrics_path,
        audit_path=audit_path,
    )

    # Remove duplicate NEWOs from dataset (i.e. synthetic NEWOs that were created unnecessarily)
    deduplicated_data_df = run_remove_duplicate_newo(
        source_frame=orders_and_order_states_df,
        params=RemoveDuplicateNewoParams(
            newo_in_file_col=OrderTempColumns.NEWO_IN_FILE,
            drop_newo_in_file_col=True,
            query_elastic_for_existing_newo=True,
        ),
        tenant=aries_task_input.workflow.tenant,
        es_client=es_client_tenant,
        streamed=streamed,
        cloud_provider=cloud_provider,
        audit_path=audit_path,
        app_metrics_path=app_metrics_path,
    )

    best_execution_df = run_best_execution(
        source_frame=deduplicated_data_df,
        es_client=es_client_tenant,
        streamed=streamed,
        app_metrics_path=app_metrics_path,
        audit_path=audit_path,
    )

    # Note that we are not discarding the `&parent` column here
    # this can be propagated to the ApplyMeta Conductor Task, which will reuse it
    final_result_df = run_frame_concatenator(
        best_execution_df=best_execution_df,
        deduplicated_data_df=deduplicated_data_df,
        params=FrameConcatenatorParams(
            orient=OrientEnum.horizontal, drop_columns=[OrderColumns.META_MODEL]
        ),
        app_metrics_path=app_metrics_path,
        audit_path=audit_path,
    )

    # CREATE SYNTHETIC NEWOs
    # As we create a NEWO and a FILL for every row, every NEWO record is essentially
    # a synthetic NEWO.

    synthetic_newo_df = run_get_rows_by_condition(
        source_frame=final_result_df,
        params=GetRowsByConditionParams(
            query=f"`{OrderColumns.EXECUTION_DETAILS_ORDER_STATUS}`"
            f".astype('str').str.upper() == '{OrderStatus.NEWO.value}'"  # noqa: E501
        ),
    )
    if synthetic_newo_df.empty:
        synthetic_newo_ndjson_path = None

    else:
        # Audit each synthetic NEWO and populate the app metrics synthetic newo count
        run_auditor_and_metrics_producer(
            source_frame=synthetic_newo_df,
            params=AuditorAndMetricsProducerParams(
                record_level_audits=[
                    RecordLevelAudit(
                        query="index==index",
                        status_message=StatusDescriptionsOfSyntheticRecords.IS_SYNTHETIC_NEWO,
                        meta_model=Order,
                    )
                ],
                metrics=[
                    Metrics(
                        query="index==index",
                        field=OrderAppMetricsEnum.OUTPUT_SYNTHETIC_NEWOS_COUNT,
                    )
                ],
                models=[Order],
            ),
            streamed=streamed,
            cloud_provider=cloud_provider,
            app_metrics_path=app_metrics_path,
            audit_path=audit_path,
        )

        # Create the appropriate path where the ndjson result is to be uploaded

        _audit_fallbacks(
            df=synthetic_newo_df.frame(),
            col_name=INSTRUMENT_PATH,
            fallback_key="isCreatedThroughFallback",
            status_message=StandardAuditMessages.INSTRUMENT_THROUGH_FALLBACK,
            meta_model=Order,
            models=[Order],
            streamed=streamed,
            app_metrics_path=app_metrics_path,
            audit_path=audit_path,
            cloud_provider=cloud_provider,
        )
        synthetic_newo_ndjson_path = create_ndjson_path(
            tenant_bucket=tenant_bucket_with_cloud_prefix,
            aries_task_input=aries_task_input,
            model=MetaModel.ORDER,
            suffix="synthetic",
        )

        # Write the transformed_df data frame into a ndjson file to the generated ndjson path
        run_write_ndjson(
            source_serializer_result=synthetic_newo_df,
            output_filepath=synthetic_newo_ndjson_path,
            audit_output=True,
            app_metrics_path=app_metrics_path,
            audit_path=audit_path,
        )

    synthetic_newo_output = add_nested_params(
        file_uri=synthetic_newo_ndjson_path,
        es_action=EsActionEnum.CREATE,
        data_model=Order.get_reference().get_qualified_reference(),
    )

    # INDEX Orders

    orders_df = run_get_rows_by_condition(
        source_frame=final_result_df,
        params=GetRowsByConditionParams(
            query=f"`{OrderColumns.EXECUTION_DETAILS_ORDER_STATUS}`"
            f".astype('str').str.upper() == '{OrderStatus.FILL.value}'"
        ),
    )

    orders_df, quarantined_order_df, sink_record_audit_df = run_quarantined_records(
        source_frame=orders_df,
        tenant=tenant,
        model=Order,
        cloud_provider=cloud_provider,
        aries_task_input=aries_task_input,
        es_client=es_client_tenant,
        audit_path=audit_path,
        app_metrics_path=app_metrics_path,
    )

    if orders_df.empty:
        orders_ndjson_path = None
    else:
        _audit_fallbacks(
            df=orders_df.frame(),
            col_name=INSTRUMENT_PATH,
            fallback_key="isCreatedThroughFallback",
            status_message=StandardAuditMessages.INSTRUMENT_THROUGH_FALLBACK,
            meta_model=Order,
            models=[Order],
            streamed=streamed,
            cloud_provider=cloud_provider,
            app_metrics_path=app_metrics_path,
            audit_path=audit_path,
        )

        orders_ndjson_path = create_ndjson_path(
            tenant_bucket=tenant_bucket_with_cloud_prefix,
            aries_task_input=aries_task_input,
            model=MetaModel.ORDER,
        )

        run_write_ndjson(
            source_serializer_result=orders_df,
            output_filepath=orders_ndjson_path,
            audit_output=True,
            app_metrics_path=app_metrics_path,
            audit_path=audit_path,
        )

    # If tenant in disabled quarantined logic list then we
    # overwrite the order record to the latest value
    if check_if_tenant_in_quarantine_disabled_list(tenant=tenant):
        order_es_action = EsActionEnum.INDEX
    else:
        order_es_action = EsActionEnum.CREATE

    orders_output = add_nested_params(
        file_uri=orders_ndjson_path,
        es_action=order_es_action,
        data_model=Order.get_reference().get_qualified_reference(),
    )

    shutil.rmtree(tmp_storage)

    quarantined_output = create_path_and_upload_model_results(
        final_transformed_df=quarantined_order_df,
        aries_task_input=aries_task_input,
        tenant_bucket=tenant_bucket_with_cloud_prefix,
        model=QuarantinedOrder,
        es_action=EsActionEnum.INDEX,
        app_metrics_path=app_metrics_path,
        audit_path=audit_path,
    )

    sink_record_audit_output = create_path_and_upload_model_results(
        final_transformed_df=sink_record_audit_df,
        aries_task_input=aries_task_input,
        tenant_bucket=tenant_bucket_with_cloud_prefix,
        model=SinkRecordAudit,
        es_action=EsActionEnum.INDEX,
        app_metrics_path=app_metrics_path,
        audit_path=audit_path,
    )

    finish_flow(
        result_path=result_path,
        result_data={
            "synthetic_newo": synthetic_newo_output,
            "orders": orders_output,
            MetaModel.QUARANTINED_ORDER: quarantined_output,
            MetaModel.SINK_RECORD_AUDIT: sink_record_audit_output,
        },
    )


def instantiate_srp_es_client(srp_through_master_data: bool):
    if srp_through_master_data:
        return None
    es_client_srp: ElasticsearchRepository = get_repository_by_cluster_version(
        resource_config=get_srp_es_config()
    )
    return es_client_srp


#


def _audit_fallbacks(
    df: pd.DataFrame,
    col_name: str,
    fallback_key: str,
    status_message: str,
    meta_model: Type[SteelEyeSchemaBaseModelES8],
    models: List[Type[SteelEyeSchemaBaseModelES8]],
    streamed: bool,
    cloud_provider: CloudProviderEnum,
    app_metrics_path: str,
    audit_path: str,
):
    instruments_created_through_fallback = (
        df.loc[:, col_name].str.get(fallback_key).fillna(False)  # type: ignore
    )
    if any(instruments_created_through_fallback):
        run_auditor_and_metrics_producer(
            source_frame=df.loc[instruments_created_through_fallback, :],
            params=AuditorAndMetricsProducerParams(
                record_level_audits=[
                    RecordLevelAudit(
                        query="index==index",
                        status_message=status_message,
                        meta_model=meta_model,
                    )
                ],
                metrics=[
                    Metrics(
                        query="index==index",
                        field=OrderAppMetricsEnum.SYNTHETIC_INSTRUMENTS_COUNT,
                    )
                ],
                models=models,
            ),
            streamed=streamed,
            cloud_provider=cloud_provider,
            app_metrics_path=app_metrics_path,
            audit_path=audit_path,
        )
