from aries_se_core_tasks.io.write.upload_file import (  # type: ignore[attr-defined]
    Params as UploadFileParams,
)
from aries_se_core_tasks.io.write.upload_file import run_upload_file
from aries_task_link.models import AriesTaskInput
from integration_trades_tasks.order.feeds.order_blotter.order_blotter_flow import (
    DefaultOrderBlotter,
)
from integration_trades_tasks.order.feeds.order_blotter_to_restricted_list.order_blotter_to_restricted_list_flow import (  # noqa E501
    FLOW_NAME,
)
from pathlib import Path
from se_core_tasks.core.core_dataclasses import CloudAction, CloudFile
from se_data_lake.lake_path import get_non_streamed_poller_file_path


class CornerBankOrderBlotter(DefaultOrderBlotter):
    def trigger_restricted_list_converter(
        self,
        aries_task_input: AriesTaskInput,
        realm: str,
        file_path: str,
    ):
        # Create the appropriate path to upload the files to
        upload_path_prefix = get_non_streamed_poller_file_path(
            workflow_name=FLOW_NAME,
            workflow_trace_id=aries_task_input.workflow.trace_id,
            workflow_start_timestamp=aries_task_input.workflow.start_timestamp,
            is_evented=True,
        )

        # Create CloudFile instance to be uploaded
        file_to_upload = CloudFile(
            file_path=Path(file_path),
            bucket_name=realm,
            key_name=f"{upload_path_prefix}/{Path(file_path).name}",
            action=CloudAction.UPLOAD,
        )

        # Upload the file batches to cloud
        run_upload_file(
            upload_target=file_to_upload,
            cloud_provider=self.cloud_provider,
            params=UploadFileParams(),
        )
