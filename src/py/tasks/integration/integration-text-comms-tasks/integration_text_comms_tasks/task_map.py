# ruff: noqa: E501
from typing import Callable


def resolve_task_func(task_name: str) -> Callable:
    match task_name:
        case "asc_chat_transform":
            from integration_text_comms_tasks.message.asc_chat_transform.asc_chat_transform_task import (
                asc_chat_transform_run,
            )

            return asc_chat_transform_run

        case "bloomberg_transform":
            from integration_text_comms_tasks.message.bloomberg_transform.bloomberg_transform_task import (
                bloomberg_transform_run,
            )

            return bloomberg_transform_run

        case "deepview_chat_transform":
            from integration_text_comms_tasks.message.deepview_chat_transform.deepview_chat_transform_task import (
                deepview_chat_transform_run,
            )

            return deepview_chat_transform_run

        case "email_transform":
            from integration_text_comms_tasks.email.email_transform.email_transform_task import (
                email_transform_run,
            )

            return email_transform_run

        case "enmacc_chat_transform":
            from integration_text_comms_tasks.message.enmacc_chat_transform.enmacc_chat_transform_task import (
                enmacc_chat_transform_run,
            )

            return enmacc_chat_transform_run

        case "ice_chat_transform":
            from integration_text_comms_tasks.message.ice_chat_transform.ice_chat_transform_task import (
                ice_chat_transform_run,
            )

            return ice_chat_transform_run

        case "ice_vantage_chat_transform":
            from integration_text_comms_tasks.message.ice_vantage_chat_transform.ice_vantage_chat_transform_task import (
                ice_vantage_chat_transform_run,
            )

            return ice_vantage_chat_transform_run

        case "ing_ms_teams_chat_transform":
            from integration_text_comms_tasks.message.ing_ms_teams_chat_transform.ing_ms_teams_chat_transform_task import (
                ing_ms_teams_chat_transform_run,
            )

            return ing_ms_teams_chat_transform_run

        case "kerv_text_transform":
            from integration_text_comms_tasks.message.kerv_text_transform.kerv_text_transform_task import (
                kerv_text_transform_run,
            )

            return kerv_text_transform_run

        case "leapxpert_chat_transform":
            from integration_text_comms_tasks.message.leapxpert_chat_transform.leapxpert_chat_transform_task import (
                leapxpert_chat_transform_run,
            )

            return leapxpert_chat_transform_run

        case "ms_graph_email_transform":
            from integration_text_comms_tasks.email.ms_graph_email_transform.ms_graph_email_transform_task import (
                ms_graph_email_transform_run,
            )

            return ms_graph_email_transform_run

        case "ms_teams_chat_transform":
            from integration_text_comms_tasks.message.ms_teams_chat_transform.ms_teams_chat_transform_task import (
                ms_teams_chat_transform_run,
            )

            return ms_teams_chat_transform_run

        case "o2_sms_message_transform":
            from integration_text_comms_tasks.message.o2_sms_message_transform.o2_sms_message_transform_task import (
                o2_sms_message_transform_run,
            )

            return o2_sms_message_transform_run

        case "o2_sms_preprocess":
            from integration_text_comms_tasks.message.o2_sms_preprocess.o2_sms_preprocess_task import (
                o2_sms_preprocess_run,
            )

            return o2_sms_preprocess_run

        case "onsim_sms_transform":
            from integration_text_comms_tasks.message.onsim_sms_transform.onsim_sms_transform_task import (
                onsim_sms_transform_run,
            )

            return onsim_sms_transform_run

        case "refinitiv_fxt_chat_transform":
            from integration_text_comms_tasks.message.refinitiv_fxt_chat_transform.refinitiv_fxt_chat_transform_task import (
                refinitiv_fxt_chat_transform_run,
            )

            return refinitiv_fxt_chat_transform_run

        case "refinitiv_tr_eikon_chat":
            from integration_text_comms_tasks.message.refinitiv_tr_eikon_chat_transform.refinitiv_tr_eikon_chat_transform_task import (
                refinitiv_tr_eikon_chat_transform_run,
            )

            return refinitiv_tr_eikon_chat_transform_run

        case "slack_chat_transform":
            from integration_text_comms_tasks.message.slack_chat_transform.slack_chat_transform_task import (
                slack_chat_transform_run,
            )

            return slack_chat_transform_run

        case "snippet_sentry_chat_transform":
            from integration_text_comms_tasks.message.snippet_sentry_chat_transform.snippet_sentry_chat_transform_task import (
                snippet_sentry_chat_transform_run,
            )

            return snippet_sentry_chat_transform_run

        case "steeleye_universal_chat":
            from integration_text_comms_tasks.message.steeleye_universal_chat_transform.steeleye_universal_chat_transform_task import (
                steeleye_universal_chat_transform_run,
            )

            return steeleye_universal_chat_transform_run

        case "symphony_chat_extractor":
            from integration_text_comms_tasks.hybrid.symphony_chat_extractor.symphony_chat_extractor_task import (
                symphony_chat_extractor_run,
            )

            return symphony_chat_extractor_run

        case "symphony_xml_chat":
            from integration_text_comms_tasks.message.symphony_xml_chat_transform.symphony_xml_chat_transform_task import (
                symphony_xml_chat_transform_run,
            )

            return symphony_xml_chat_transform_run

        case _:
            raise ValueError(f"Unrecognized task name: '{task_name}'")
