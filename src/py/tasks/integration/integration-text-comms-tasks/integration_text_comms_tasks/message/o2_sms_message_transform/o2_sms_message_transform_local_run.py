# mypy: disable-error-code="attr-defined"
import logging
from benchmark_mode import benchmark
from integration_text_comms_tasks.integration_text_comms_tasks_task import (
    integration_text_comms_tasks_run,
)
from integration_text_comms_tasks.message.o2_sms_message_transform.o2_sms_message_transform_sample_input import (  # noqa E501
    sample_input,
)

logger = logging.getLogger("o2_sms_message_transform")


@benchmark
def main():
    logger.info("Starting execution...")
    output = integration_text_comms_tasks_run(aries_task_input=sample_input())
    logger.info(f"Finished executing with output {output}")


if __name__ == "__main__":
    main()
