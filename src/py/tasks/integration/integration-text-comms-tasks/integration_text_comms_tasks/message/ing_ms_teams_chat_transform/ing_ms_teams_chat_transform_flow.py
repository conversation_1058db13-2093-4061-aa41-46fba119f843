# mypy: disable-error-code="attr-defined, assignment"
import logging
import os
import pandas as pd
from aries_config_cached_client.tenant_workflow import CachedTenantWorkflowAPIClient
from aries_se_comms_tasks.chat_event.static import ChatEventColumns
from aries_se_comms_tasks.feeds.message.app_metrics_enum import MessageTransformAppMetricsEnum
from aries_se_comms_tasks.feeds.message.ms_teams_chat.ing.extract_ms_teams_messages_from_eml import (  # noqa: E501
    run_extract_ms_teams_messages_from_eml,
)
from aries_se_comms_tasks.feeds.message.ms_teams_chat.ing.static import (
    ING_MS_TEAMS_CHAT_EVENT_MAPPINGS_NAME,
    ING_MS_TEAMS_MESSAGE_MAPPINGS_NAME,
)
from aries_se_comms_tasks.generic.participants.link_participants import (
    Params as LinkParticipantsParams,
)
from aries_se_comms_tasks.generic.participants.link_participants import run_link_participants
from aries_se_comms_tasks.message.static import MessageColumns
from aries_se_core_tasks.aries.parse_batch_ndjson import run_parse_batch_ndjson
from aries_se_core_tasks.aries.sink_file_audit.auditor_and_metrics_producer import (
    Metrics,
    run_auditor_and_metrics_producer,
)
from aries_se_core_tasks.aries.sink_file_audit.auditor_and_metrics_producer import (
    Params as AuditorAndMetricsProducerParams,
)
from aries_se_core_tasks.aries.utility_tasks.finish_flow import finish_flow
from aries_se_core_tasks.aries.utility_tasks.get_tenant_bucket import get_tenant_bucket
from aries_se_core_tasks.aries.utility_tasks.unpack_aries_task_input import unpack_aries_task_input
from aries_se_core_tasks.frame.frame_concatenator import Params as FrameConcatenatorParams
from aries_se_core_tasks.frame.frame_concatenator import run_frame_concatenator
from aries_se_core_tasks.generic.generate_record_identifiers_for_df import (
    Params as GenerateRecordFileIdentifiersForDfParams,
)
from aries_se_core_tasks.generic.generate_record_identifiers_for_df import (
    run_generate_record_identifiers_for_df,
)
from aries_se_core_tasks.get_primary_transformations import run_get_primary_transformations
from aries_se_core_tasks.io.create_ndjson_path import create_ndjson_path
from aries_se_core_tasks.io.write.write_ndjson import run_write_ndjson
from aries_se_core_tasks.static import MetaModel
from aries_se_core_tasks.utilities.unload_frame import unload_frame_to_list
from aries_task_link.models import AriesTaskInput
from integration_text_comms_tasks.message.ing_ms_teams_chat_transform.input_schema import (
    IngMsTeamsChatAriesTaskInput,
)
from se_conductor_utils.task_output import DynamicTask, create_dynamic_tasks_list
from se_core_tasks.frame.frame_concatenator import OrientEnum
from se_data_lake.cloud_utils import (
    get_bucket,
    get_cloud_provider_from_file_uri,
    get_cloud_provider_prefix,
)
from se_data_lake.lake_path import get_ingress_depository_lake_path_for_attachments
from se_elastic_schema.elastic_schema.core.steeleye_schema_model import SteelEyeSchemaBaseModelES8
from se_elastic_schema.models import ChatEvent, Message
from se_enums.cloud import CloudProviderEnum
from se_enums.elastic_search import EsActionEnum
from typing import List, Optional, Type

logger = logging.getLogger(__name__)

# Define batch sizes for each type of output
BATCH_SIZE: int = int(os.environ.get("BATCH_SIZE", 100))
CREATE_MESSAGE_BATCH_SIZE: int = int(os.environ.get("CREATE_MESSAGE_BATCH_SIZE", BATCH_SIZE))
UPDATE_MESSAGE_BATCH_SIZE: int = int(os.environ.get("UPDATE_MESSAGE_BATCH_SIZE", BATCH_SIZE))
CHAT_EVENT_BATCH_SIZE: int = int(os.environ.get("CHAT_EVENT_BATCH_SIZE", BATCH_SIZE))
# Batch size for WriteNdjson
WRITE_NDJSON_BATCH_SIZE: int = int(os.environ.get("WRITE_NDJSON_BATCH_SIZE", 50))

# Get Azure SAS token and Storage account to get attachments from env vars
AZURE_ATTACHMENT_STORAGE_ACCOUNT = os.getenv("AZURE_ATTACHMENT_STORAGE_ACCOUNT")
AZURE_ATTACHMENT_SAS_TOKEN = os.getenv("AZURE_ATTACHMENT_SAS_TOKEN")
AZURE_ATTACHMENT_BLOB_CONTAINER_NAME = os.getenv("AZURE_ATTACHMENT_BLOB_CONTAINER_NAME")


def ing_ms_teams_chat_transform_flow(
    aries_task_input: AriesTaskInput,
    app_metrics_path: Optional[str] = None,
    audit_path: Optional[str] = None,
    result_path: str = "result.json",
):
    # Init output paths with empty lists
    messages_to_create_ndjson_paths_list: List[str] = []
    messages_to_update_ndjson_paths_list: List[str] = []
    chat_events_ndjson_paths_list: List[str] = []

    # These lists hold the dataframes with the correct size, after
    # being batched by the `BATCH_SIZE` variable.
    # The key thing to understand is they are not stored in memory,
    # but in the disk, and they are unloaded from memory through the
    # `serializer` functionality.
    messages_to_create_frames_lst: List[pd.DataFrame] = []
    messages_to_update_frames_lst: List[pd.DataFrame] = []
    chat_events_frames_lst: List[pd.DataFrame] = []

    # Get tenant workflow tenant config from postgres
    cached_tenant_workflow_config = CachedTenantWorkflowAPIClient.get(
        tenant_name=aries_task_input.workflow.tenant,
        workflow_name=aries_task_input.workflow.name,
        stack_name=aries_task_input.workflow.stack,
    )
    cloud_provider: CloudProviderEnum = get_cloud_provider_from_file_uri(
        file_uri=cached_tenant_workflow_config.tenant.lake_prefix
    )
    cloud_provider_prefix = get_cloud_provider_prefix(value=cloud_provider)
    streamed: bool = cached_tenant_workflow_config.workflow.streamed

    # Parse and validate AriesTaskInput parameters
    task_transform_input = unpack_aries_task_input(
        aries_task_input=aries_task_input, model=IngMsTeamsChatAriesTaskInput
    )
    # get input file
    file_uri = task_transform_input.file_uri

    # Get realm from input file path
    realm: str = get_bucket(file_uri=file_uri)
    tenant: str = aries_task_input.workflow.tenant
    # Determine the Cloud Bucket of the tenant
    tenant_bucket_with_cloud_prefix: str = get_tenant_bucket(
        task_input=task_transform_input, cloud_provider_prefix=cloud_provider_prefix
    )

    # Get all the dynamic task definitions from workflow input
    message_create_output_dynamic_task_input: DynamicTask = task_transform_input.dynamic_tasks.get(
        "message_create"
    )

    message_update_output_dynamic_task_input: DynamicTask = task_transform_input.dynamic_tasks.get(
        "message_update"
    )

    chat_event_output_dynamic_task_input: DynamicTask = task_transform_input.dynamic_tasks.get(
        "chat_event"
    )

    # Extract list of emails from input NDJSON
    eml_list = run_parse_batch_ndjson(
        transform_input=task_transform_input,
        app_metrics_path=app_metrics_path,
        audit_path=audit_path,
    )

    for eml_file_url in eml_list:
        messages_to_create_df, messages_to_update_df, chat_events_df = (
            run_extract_ms_teams_messages_from_eml(
                eml_file_url=eml_file_url,
                cloud_provider=cloud_provider,
                streamed=streamed,
                tenant=tenant,
                audit_path=audit_path,
            )
        )
        if not messages_to_create_df.empty:
            attachments_upload_path = get_ingress_depository_lake_path_for_attachments(
                workflow_name=aries_task_input.workflow.name,
                workflow_start_timestamp=aries_task_input.workflow.start_timestamp,
                workflow_trace_id=aries_task_input.workflow.trace_id,
                task_io_params=aries_task_input.input_param.params,
            )
            message_mappings_result = run_get_primary_transformations(
                source_frame=messages_to_create_df,
                flow=ING_MS_TEAMS_MESSAGE_MAPPINGS_NAME,
                realm=realm,
                tenant=tenant,
                source_file_uri=file_uri,
                eml_file_url=eml_file_url,
                cloud_provider=cloud_provider,
                azure_attachment_storage_account_name=AZURE_ATTACHMENT_STORAGE_ACCOUNT,
                azure_attachment_sas_token=AZURE_ATTACHMENT_SAS_TOKEN,
                azure_attachment_blob_container_name=AZURE_ATTACHMENT_BLOB_CONTAINER_NAME,
                attachment_upload_prefix=attachments_upload_path,
            )

            run_auditor_and_metrics_producer(
                source_frame=message_mappings_result,
                params=AuditorAndMetricsProducerParams(
                    metrics=[
                        Metrics(
                            query=f"`{MessageColumns.ATTACHMENTS}`.notnull()",
                            field=MessageTransformAppMetricsEnum.ATTACHMENTS_COUNT,
                        )
                    ],
                    models=[Message],
                ),
                streamed=streamed,
                cloud_provider=cloud_provider,
                app_metrics_path=app_metrics_path,
                audit_path=audit_path,
            )
            messages_to_create_frames_lst = unload_frame_to_list(
                df=message_mappings_result,
                lst=messages_to_create_frames_lst,
                size=CREATE_MESSAGE_BATCH_SIZE,
            )

        # Handle Messages to Update
        if not messages_to_update_df.empty:
            messages_to_update_frames_lst = unload_frame_to_list(
                df=messages_to_update_df,  # type: ignore[arg-type]
                lst=messages_to_update_frames_lst,
                size=UPDATE_MESSAGE_BATCH_SIZE,
            )

        # Handle Chat Events
        if not chat_events_df.empty:
            chat_event_mappings_result = run_get_primary_transformations(
                source_frame=chat_events_df,
                flow=ING_MS_TEAMS_CHAT_EVENT_MAPPINGS_NAME,
                realm=realm,
                tenant=tenant,
                source_file_uri=file_uri,
                eml_file_url=eml_file_url,
            )

            chat_events_frames_lst = unload_frame_to_list(
                df=chat_event_mappings_result,
                lst=chat_events_frames_lst,
                size=CHAT_EVENT_BATCH_SIZE,
            )

    messages_create_batch_num: int = 0
    messages_update_batch_num: int = 0
    chat_events_batch_num: int = 0
    for frame in messages_to_create_frames_lst:
        ndjson_path = _process(
            data_models=[Message],
            meta_model=MetaModel.MESSAGE,
            streamed=streamed,
            cloud_provider=cloud_provider,
            tenant=tenant,
            bucket=tenant_bucket_with_cloud_prefix,
            batch_num=messages_create_batch_num,
            source_frame=frame,
            target_participants_column=MessageColumns.PARTICIPANTS,
            aries_task_input=aries_task_input,
            app_metrics_path=app_metrics_path,
            audit_path=audit_path,
        )

        if ndjson_path:
            messages_to_create_ndjson_paths_list.append(ndjson_path)
            messages_create_batch_num += 1

    del messages_to_create_frames_lst

    for frame in messages_to_update_frames_lst:
        # Create the appropriate path where the ndjson result is to be uploaded
        update_messages_path = create_ndjson_path(
            tenant_bucket=tenant_bucket_with_cloud_prefix,
            aries_task_input=aries_task_input,
            model=MetaModel.MESSAGE,
            suffix=f"update_{messages_update_batch_num}",
        )

        # Write the transformed_df data frame into a ndjson file to the generated ndjson path
        run_write_ndjson(
            source_serializer_result=frame,  # type: ignore[arg-type]
            output_filepath=update_messages_path,
            audit_output=True,
            app_metrics_path=app_metrics_path,
            audit_path=audit_path,
            write_ndjson_batch_size=WRITE_NDJSON_BATCH_SIZE,
        )

        if update_messages_path:
            messages_update_batch_num += 1
            messages_to_update_ndjson_paths_list.append(update_messages_path)

    del messages_to_update_frames_lst

    for frame in chat_events_frames_lst:
        ndjson_path = _process(
            data_models=[ChatEvent],
            meta_model=MetaModel.CHAT_EVENT,
            streamed=streamed,
            cloud_provider=cloud_provider,
            tenant=tenant,
            bucket=tenant_bucket_with_cloud_prefix,
            batch_num=chat_events_batch_num,
            source_frame=frame,
            target_participants_column=ChatEventColumns.PARTICIPANTS,
            aries_task_input=aries_task_input,
            app_metrics_path=app_metrics_path,
            audit_path=audit_path,
        )

        if ndjson_path:
            chat_events_ndjson_paths_list.append(ndjson_path)
            chat_events_batch_num += 1

    del chat_events_frames_lst

    messages_to_create_output = create_dynamic_tasks_list(
        lst=[{"file_uri": ndjson_path} for ndjson_path in messages_to_create_ndjson_paths_list],
        task=message_create_output_dynamic_task_input,
        common_input_parameters={
            "es_action": EsActionEnum.INDEX.value,
            "data_model": "se_elastic_schema.models.tenant.communication.message:Message",
        },
        workflow=aries_task_input.workflow,
    )

    messages_to_update_output = create_dynamic_tasks_list(
        lst=[{"file_uri": ndjson_path} for ndjson_path in messages_to_update_ndjson_paths_list],
        task=message_update_output_dynamic_task_input,
        common_input_parameters={
            "es_action": EsActionEnum.INDEX.value,
            "data_model": "se_elastic_schema.models.tenant.communication.message:Message",
        },
        workflow=aries_task_input.workflow,
    )

    chat_events_output = create_dynamic_tasks_list(
        lst=[{"file_uri": ndjson_path} for ndjson_path in chat_events_ndjson_paths_list],
        task=chat_event_output_dynamic_task_input,
        common_input_parameters={
            "es_action": EsActionEnum.INDEX.value,
            "data_model": "se_elastic_schema.models.tenant.communication.chat_event:ChatEvent",
        },
        workflow=aries_task_input.workflow,
    )

    finish_flow(
        result_path=result_path,
        result_data={
            f"{MetaModel.MESSAGE}_TO_CREATE": messages_to_create_output,
            f"{MetaModel.MESSAGE}_TO_UPDATE": messages_to_update_output,
            MetaModel.CHAT_EVENT: chat_events_output,
        },
    )


def _process(
    data_models: List[Type[SteelEyeSchemaBaseModelES8]],
    meta_model: MetaModel,
    streamed: bool,
    cloud_provider: CloudProviderEnum,
    tenant: str,
    bucket: str,
    batch_num: int,
    source_frame: pd.DataFrame,
    target_participants_column: str,
    aries_task_input: AriesTaskInput,
    app_metrics_path: Optional[str],
    audit_path: Optional[str],
) -> str | None:
    """This function has common logic for processing messages and chat events.

    When using this function, make sure it the source frame has the
    desired output size, as it will be saved and uploaded.
    """

    # Get the sourceKey, this is used for the audit keys
    frame_with_amp_id = run_generate_record_identifiers_for_df(
        source_frame=source_frame,
        params=GenerateRecordFileIdentifiersForDfParams(
            data_model=meta_model, target_record_identifier_col="record_identifier"
        ),
        streamed=streamed,
        cloud_provider=cloud_provider,
    )

    # Enrichment: Link participants based on the create participant identifiers
    participants_result = run_link_participants(
        tenant=tenant,
        source_frame=frame_with_amp_id,
        params=LinkParticipantsParams(target_participants_column=target_participants_column),
        streamed=streamed,
        app_metrics_path=app_metrics_path,
        audit_path=audit_path,
        data_models=data_models,
        record_identifier_column="record_identifier",
    )

    final_result = run_frame_concatenator(
        participants_result=participants_result,
        primary_mappings_result=source_frame,
        params=FrameConcatenatorParams(orient=OrientEnum.horizontal),
    )

    final_records_length: int = final_result.shape[0]

    if final_records_length > 0:
        # Create the appropriate path where the ndjson result is to be uploaded
        ndjson_path = create_ndjson_path(
            tenant_bucket=bucket,
            aries_task_input=aries_task_input,
            model=meta_model,
            suffix=f"{batch_num}",
        )

        # Write the transformed_df data frame into a ndjson file to the generated ndjson path
        run_write_ndjson(
            source_serializer_result=final_result,
            output_filepath=ndjson_path,
            audit_output=True,
            app_metrics_path=app_metrics_path,
            audit_path=audit_path,
            write_ndjson_batch_size=WRITE_NDJSON_BATCH_SIZE,
        )

        return ndjson_path

    return None
