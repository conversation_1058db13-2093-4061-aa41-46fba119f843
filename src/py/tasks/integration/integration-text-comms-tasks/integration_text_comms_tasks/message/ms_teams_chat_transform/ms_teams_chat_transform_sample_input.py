from aries_io_event.io_param import IOParamFieldSet
from aries_io_event.task import TaskFieldSet
from aries_io_event.workflow import WorkflowFieldSet
from aries_task_link.models import AriesTaskInput
from datetime import datetime


def sample_input() -> AriesTaskInput:
    workflow = WorkflowFieldSet(
        trace_id="test_ms_teams_chat1",
        name="ms_teams_chat",
        stack="uat-shared-1",
        tenant="puneeth",
        start_timestamp=datetime.now(),
    )
    input_param = IOParamFieldSet(
        params=dict(
            file_uri="s3://puneeth.uat.steeleye.co/aries/ingress/nonstreamed/polled/ms_teams_poll/2025/03/05/lZ1HyL7PCeZzkt43QLIwT/20250305_Ft6W_Ml9i2.json",  # noqa E501
        )
    )
    task = TaskFieldSet(name="ms_teams_chat_transform", version="latest", success=False)
    return AriesTaskInput(workflow=workflow, input_param=input_param, task=task)
