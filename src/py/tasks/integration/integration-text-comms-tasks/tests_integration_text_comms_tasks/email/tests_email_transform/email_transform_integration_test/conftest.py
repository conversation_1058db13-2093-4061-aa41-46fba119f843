import datetime
import os
import pandas as pd
import pytest
from aries_io_event.io_param import IOParamFieldSet
from aries_io_event.task import TaskFieldSet
from aries_io_event.workflow import WorkflowFieldSet
from aries_task_link.models import AriesTaskInput
from integration_wrapper.static import IntegrationAriesTaskVariables

# This needs to be here since the conftest is loaded first
# than any import on the test files
os.environ[IntegrationAriesTaskVariables.DATA_PLATFORM_CONFIG_API_URL] = (
    "https://test-enterprise.steeleye.co"
)


@pytest.fixture
def tenant_config_sample():
    tenant_config_sample = {
        "test.dev.steeleye.co": {
            "&id": "test.dev.steeleye.co",
            "&traitFqn": "tenant/configuration",
            "&ancestor": "TenantConfiguration:test.dev.steeleye.co:1610438215209",
            "emailDomains": ["steel-eye.com"],
            "&key": "TenantConfiguration:test.dev.steeleye.co:1610438992064",
            "subscribedMarketAbuseReports": ["ABUSIVE_SQUEEZE"],
            "subscribedModules": ["Market", "Communications"],
            "usageTrackingEnabled": True,
            "&model": "TenantConfiguration",
            "tenantId": "test.dev.steeleye.co",
            "featureFlags": ["classifier", "threadEmails", "zoning", "apply_validations"],
            "userIdleTimeoutMs": 86400000,
            "&timestamp": "1610438992064",
            "&user": "AKIAIG2C2VNWA5ZTLKEA",
        }
    }
    return tenant_config_sample


@pytest.fixture
def process_attachments_side_effect():
    process_attachments_side_effect = [
        pd.Series(
            {
                "file_url": "s3://test.dev.steeleye.co/sample/sample",
                "realm": "test.dev.steeleye.co",
                "tenant": "test",
                "fileName": "black-logo-steeleye_85aef33d-e9cb-4294-a5c4-b1db2e7276e7.png",
                "fileType": "png",
                "mimeTag": "image/png",
                "sizeInBytes": 83892,
                "content": "",
                "tenant_configuration": tenant_config_sample,
                "fileInfo.location.bucket": "test.dev.steeleye.co",
                "fileInfo.location.key": "attachments/eml/055638l3ifg02dsrq2ckvg390vja8ufjpguap4g1/black-logo-steeleye_85aef33d-e9cb-4294-a5c4-b1db2e7276e7.png",  # noqa: E501
                "fileInfo.processed": "2022-01-18T04:57:22.572647",
                "fileInfo.contentLength": 83892,
                "fileInfo.versionId": "9QfrKO0ou_1TjP6ACrf_h2GWiijQOu2W",
            }
        ),
        pd.Series(
            {
                "file_url": "s3://test.dev.steeleye.co/sample/sample",
                "realm": "test.dev.steeleye.co",
                "tenant": "test",
                "fileName": "SocialLink_Linkedin_32x32_79fca46c-1231-4405-bcf7-36c514245755.png",
                "fileType": "png",
                "mimeTag": "image/png",
                "sizeInBytes": 642,
                "content": "",
                "tenant_configuration": tenant_config_sample,
                "fileInfo.location.bucket": "test.dev.steeleye.co",
                "fileInfo.location.key": "attachments/eml/055638l3ifg02dsrq2ckvg390vja8ufjpguap4g1/SocialLink_Linkedin_32x32_79fca46c-1231-4405-bcf7-36c514245755.png",  # noqa: E501
                "fileInfo.processed": "2022-01-18T04:57:22.572647",
                "fileInfo.contentLength": 642,
                "fileInfo.versionId": "gxfmZUe2Vudml4S4sk06JRO1AFE.PoeP",
            }
        ),
        pd.Series(
            {
                "file_url": "s3://test.dev.steeleye.co/sample/sample",
                "realm": "test.dev.steeleye.co",
                "tenant": "test",
                "fileName": "SocialLink_Twitter_32x32_ba8e2566-64ff-4f29-835b-80ed4e3090e7.png",
                "fileType": "png",
                "mimeTag": "image/png",
                "sizeInBytes": 850,
                "content": "",
                "tenant_configuration": tenant_config_sample,
                "fileInfo.location.bucket": "test.dev.steeleye.co",
                "fileInfo.location.key": "attachments/eml/055638l3ifg02dsrq2ckvg390vja8ufjpguap4g1/SocialLink_Twitter_32x32_ba8e2566-64ff-4f29-835b-80ed4e3090e7.png",  # noqa: E501
                "fileInfo.processed": "2022-01-18T04:57:22.572647",
                "fileInfo.contentLength": 850,
                "fileInfo.versionId": "NvnXVavg0wLfnp1pKFT3dlIqPGIBj93.",
            }
        ),
    ]
    return process_attachments_side_effect


@pytest.fixture
def sample_s3_response():
    sample_s3_response = {
        "ResponseMetadata": {
            "RequestId": "83PSMRBXB2J36TMG",
            "HostId": "weLeuW1NfszIXo2TenoVJAu8kB5MQeRQ7y7ZKGteJdpSQfVdnS3dJSxo0HhjzbD32A+i1rRVEms=",  # noqa: E501
            "HTTPStatusCode": 200,
            "HTTPHeaders": {
                "x-amz-id-2": "weLeuW1NfszIXo2TenoVJAu8kB5MQeRQ7y7ZKGteJdpSQfVdnS3dJSxo0HhjzbD32A+i1rRVEms=",  # noqa: E501
                "x-amz-request-id": "83PSMRBXB2J36TMG",
                "date": "Mon, 17 Jan 2022 17:56:47 GMT",
                "last-modified": "Thu, 13 Jan 2022 07:05:12 GMT",
                "etag": '"b2b871757f4400597ee4b3fe8810b57c"',
                "x-amz-server-side-encryption": "AES256",
                "x-amz-meta-x-amz-tag-len": "128",
                "x-amz-meta-x-amz-unencrypted-content-length": "112627",
                "x-amz-meta-x-amz-wrap-alg": "kms",
                "x-amz-meta-x-amz-matdesc": '{"aws:ses:message-id":"055638l3ifg02dsrq2ckvg390vja8ufjpguap4g1","aws:ses:rule-name":"test.dev.steeleye.co","aws:ses:source-account":"************","kms_cmk_id":"arn:aws:kms:eu-west-1:************:alias/test/dev/steeleye/co"}',  # noqa: E501
                "x-amz-meta-x-amz-key-v2": "AQIDAHiVv78QwZgt6iqvLxmCkjpMqf/C4kBaIiQTNgmokABVNQEzb8/ANhm9kS8JICMVf9tcAAAAfjB8BgkqhkiG9w0BBwagbzBtAgEAMGgGCSqGSIb3DQEHATAeBglghkgBZQMEAS4wEQQMzwjUzYy039bGFb2hAgEQgDtUpihV3irE+yLNVZNhSoo3IMKY/di+2D2e8YVshb5AkeEN+6J+yA5YF3PhoK6hzW3Uxy5JgcduMik0Fg==",  # noqa: E501
                "x-amz-meta-x-amz-cek-alg": "AES/GCM/NoPadding",
                "x-amz-meta-x-amz-iv": "cbKcY5flW69tbzz4",
                "x-amz-version-id": "kNH2a9EwTTeo5CeGR10rCj806izUggpH",
                "accept-ranges": "bytes",
                "content-type": "application/octet-stream",
                "server": "AmazonS3",
                "content-length": "112643",
            },
            "RetryAttempts": 0,
        },
        "AcceptRanges": "bytes",
        "LastModified": datetime.datetime(2022, 1, 13, 7, 5, 12),
        "ContentLength": 112643,
        "ETag": '"b2b871757f4400597ee4b3fe8810b57c"',
        "VersionId": "kNH2a9EwTTeo5CeGR10rCj806izUggpH",
        "ContentType": "application/octet-stream",
        "ServerSideEncryption": "AES256",
        "Metadata": {
            "x-amz-tag-len": "128",
            "x-amz-unencrypted-content-length": "112627",
            "x-amz-wrap-alg": "kms",
            "x-amz-matdesc": '{"aws:ses:message-id":"055638l3ifg02dsrq2ckvg390vja8ufjpguap4g1","aws:ses:rule-name":"test.dev.steeleye.co","aws:ses:source-account":"************","kms_cmk_id":"arn:aws:kms:eu-west-1:************:alias/test/dev/steeleye/co"}',  # noqa: E501
            "x-amz-key-v2": "AQIDAHiVv78QwZgt6iqvLxmCkjpMqf/C4kBaIiQTNgmokABVNQEzb8/ANhm9kS8JICMVf9tcAAAAfjB8BgkqhkiG9w0BBwagbzBtAgEAMGgGCSqGSIb3DQEHATAeBglghkgBZQMEAS4wEQQMzwjUzYy039bGFb2hAgEQgDtUpihV3irE+yLNVZNhSoo3IMKY/di+2D2e8YVshb5AkeEN+6J+yA5YF3PhoK6hzW3Uxy5JgcduMik0Fg==",  # noqa: E501
            "x-amz-cek-alg": "AES/GCM/NoPadding",
            "x-amz-iv": "cbKcY5flW69tbzz4",
        },
    }
    return sample_s3_response


@pytest.fixture()
def sample_aries_task_input() -> AriesTaskInput:
    workflow = WorkflowFieldSet(
        trace_id="test_email",
        name="email",
        stack="dev-blue",
        tenant="test",
        start_timestamp="2023-07-19T17:28:00.000000+00:00",
    )
    input_param = IOParamFieldSet(params=dict(file_uri=""))
    task = TaskFieldSet(name="email_transform", version="latest", success=False)
    return AriesTaskInput(workflow=workflow, input_param=input_param, task=task)
