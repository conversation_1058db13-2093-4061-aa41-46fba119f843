# type: ignore
import boto3
import glob
import json
import os
import pandas as pd
import pytest
import shutil
from addict import addict
from aries_config_cached_client.tenant_workflow import CachedTenantWorkflowAPIClient
from aries_se_comms_tasks.comms_app_metrics_enum import CommsAppMetricsEnum
from aries_se_comms_tasks.generic.link import link_participants_text_files
from aries_se_comms_tasks.monitored_users.abstractions import abstract_chat_monitored_users
from aries_se_core_tasks.core.generic_app_metrics_enum import GenericAppMetricsEnum
from aries_se_core_tasks.utilities.file_utils import get_batched_file_name
from aries_se_core_tasks.utilities.helpers_for_tests import sort_identifiers_columns
from aries_task_link.models import AriesTaskInput
from freezegun import freeze_time
from integration_test_utils.aws_helpers.patch_response import (
    mock_aiobotocore_convert_to_response_dict,
)
from integration_text_comms_tasks.message.bloomberg_transform import bloomberg_transform_flow
from integration_text_comms_tasks.message.bloomberg_transform.bloomberg_transform_task import (
    bloomberg_transform_run,
)
from integration_wrapper.static import St<PERSON><PERSON>ields
from math import ceil
from mock.mock import DEFAULT, MagicMock, patch
from moto import mock_aws
from mymarket_tasks.static import PersonColumns
from pathlib import Path
from se_elasticsearch.repository import ResourceConfig
from se_elasticsearch.repository.static import MetaPrefix
from se_io_utils.tempfile_utils import tmp_directory
from shutil import rmtree
from typing import List, Optional

DATA_PATH = Path(__file__).parent.joinpath("data")
DATA_INPUT_PATH = DATA_PATH.joinpath("input")

TEMP_DIR: Path = tmp_directory()
AUDIT_PATH: Path = TEMP_DIR.joinpath("audit.json")

# Input paths
EMAIL_INPUT_FILES_PATH = DATA_PATH.joinpath("input", "email")
CHAT_INPUT_FILES_PATH = DATA_PATH.joinpath("input", "chat")

# Ndjson directories
EXPECTED_EMAIL_RESULTS_PATH = DATA_PATH.joinpath("expected_results", "email")
EXPECTED_CHAT_RESULTS_PATH = DATA_PATH.joinpath("expected_results", "chat")

# Output ndjson paths
OUTPUT_EMAIL_NDJSON_PATH = f"{EXPECTED_EMAIL_RESULTS_PATH}/result_email_transformed.ndjson"

OUTPUT_CHAT_EVENT_NDJSON_PATH = (
    f"{EXPECTED_CHAT_RESULTS_PATH}/result_chat_events_transformed.ndjson"
)
OUTPUT_MESSAGE_NDJSON_PATH = f"{EXPECTED_CHAT_RESULTS_PATH}/result_message_transformed.ndjson"

# Expected ndjson paths

EXPECTED_EMAIL_NDJSON_PATH = f"{EXPECTED_EMAIL_RESULTS_PATH}/expected_email_transformed.ndjson"
EXPECTED_EMAIL_MONITORED_USERS_NDJSON_PATH = (
    f"{EXPECTED_EMAIL_RESULTS_PATH}/expected_email_with_monitored_users.ndjson"
)
EXPECTED_KYTE_EMAIL_NDJSON_PATH = (
    f"{EXPECTED_EMAIL_RESULTS_PATH}/expected_kyte_email_transformed.ndjson"
)

EXPECTED_CHAT_EVENT_NDJSON_PATH = (
    f"{EXPECTED_CHAT_RESULTS_PATH}/expected_chat_events_transformed.ndjson"
)
EXPECTED_KYTE_CHAT_EVENT_NDJSON_PATH = (
    f"{EXPECTED_CHAT_RESULTS_PATH}/expected_kyte_chat_events_transformed.ndjson"
)
EXPECTED_MESSAGE_NDJSON_PATH = f"{EXPECTED_CHAT_RESULTS_PATH}/expected_message_transformed.ndjson"
EXPECTED_MESSAGE_MONITORED_USERS_NDJSON_PATH = (
    f"{EXPECTED_CHAT_RESULTS_PATH}/expected_message_with_monitored_users.ndjson"
)
EXPECTED_KYTE_MESSAGE_NDJSON_PATH = (
    f"{EXPECTED_CHAT_RESULTS_PATH}/expected_kyte_message_transformed.ndjson"
)
EXPECTED_CHAT_EVENT_MONITORED_USERS_NDJSON_PATH = (
    f"{EXPECTED_CHAT_RESULTS_PATH}/expected_chat_events_with_monitored_users.ndjson"
)


mock_aiobotocore_convert_to_response_dict()


@pytest.fixture(scope="session", autouse=True)
def cleanup(request):
    def _end():
        rmtree(TEMP_DIR)

    request.addfinalizer(_end)


class TestBloombergTransform:
    """Tests Bloomberg transform end to end."""

    @freeze_time(time_to_freeze="2023-06-06 06:59:38.911459+00:00")
    def test_valid_email_executions(
        self,
        sample_aries_task_input_email: AriesTaskInput,
        link_participants_scroll_result,
        tenant_config_without_monitored_users_flag: dict,
        mocker,
    ):
        batch_size = 100
        aries_task_output = self._run_email_aries_task(
            sample_aries_task_input=sample_aries_task_input_email,
            bucket_name="test.dev.steeleye.co",
            link_participants_scroll_result=link_participants_scroll_result,
            batch_size=batch_size,
            tenant_config=tenant_config_without_monitored_users_flag,
            mocker=mocker,
        )
        expected_email_df = pd.read_json(EXPECTED_EMAIL_NDJSON_PATH, lines=True)
        no_of_batches = ceil(expected_email_df.shape[0] / batch_size)
        output_ndjsons = []
        for i in range(no_of_batches):
            ndjson_path = get_batched_file_name(file_path=OUTPUT_EMAIL_NDJSON_PATH, batch_num=i)
            output_ndjsons.append(pd.read_json(ndjson_path, lines=True))
        output_email_df = pd.concat(output_ndjsons).reset_index(drop=True)

        sort_identifiers_columns(result_df=output_email_df, expected_result_df=expected_email_df)

        # Test that the chat event, message dfs are as expected
        pd.testing.assert_frame_equal(left=output_email_df, right=expected_email_df)

        # Assert metrics
        metrics = (
            aries_task_output.app_metric.metrics[StaticFields.DATA_INTEGRATION_METRICS_PREFIX][
                "bloomberg"
            ]["bloomberg_transform"]
            if aries_task_output.app_metric
            else {}
        )
        assert metrics[GenericAppMetricsEnum.INPUT_COUNT] == 9
        assert metrics[GenericAppMetricsEnum.OUTPUT_COUNT] == 9
        assert metrics[CommsAppMetricsEnum.RECORDS_WITHOUT_LINKED_PARTICIPANTS_COUNT] == 0

    @freeze_time(time_to_freeze="2023-06-06 06:59:38.911459+00:00")
    def test_email_execution_with_monitored_users(
        self,
        sample_aries_task_input_email: AriesTaskInput,
        link_participants_scroll_result,
        tenant_config_with_monitored_users_flag: dict,
        monitored_users_from_elastic_mock: pd.DataFrame,
        mocker,
    ):
        batch_size = 100
        mock_monitored_elastic_df = mocker.patch.object(
            abstract_chat_monitored_users.AbstractChatMonitoredUsers,
            "fetch_monitored_user_df_from_elastic",
        )
        mock_monitored_elastic_df.return_value = monitored_users_from_elastic_mock
        aries_task_output = self._run_email_aries_task(
            sample_aries_task_input=sample_aries_task_input_email,
            bucket_name="test.dev.steeleye.co",
            link_participants_scroll_result=link_participants_scroll_result,
            batch_size=batch_size,
            tenant_config=tenant_config_with_monitored_users_flag,
            mocker=mocker,
        )
        expected_email_df = pd.read_json(EXPECTED_EMAIL_MONITORED_USERS_NDJSON_PATH, lines=True)
        no_of_batches = ceil(expected_email_df.shape[0] / batch_size)
        output_ndjsons = []
        for i in range(no_of_batches):
            ndjson_path = get_batched_file_name(file_path=OUTPUT_EMAIL_NDJSON_PATH, batch_num=i)
            output_ndjsons.append(pd.read_json(ndjson_path, lines=True))
        output_email_df = pd.concat(output_ndjsons).reset_index(drop=True)

        sort_identifiers_columns(result_df=output_email_df, expected_result_df=expected_email_df)

        # Test that the chat event, message dfs are as expected
        pd.testing.assert_frame_equal(left=output_email_df, right=expected_email_df)

        # Assert metrics
        metrics = (
            aries_task_output.app_metric.metrics[StaticFields.DATA_INTEGRATION_METRICS_PREFIX][
                "bloomberg"
            ]["bloomberg_transform"]
            if aries_task_output.app_metric
            else {}
        )
        assert metrics[GenericAppMetricsEnum.INPUT_COUNT] == 9
        assert metrics[GenericAppMetricsEnum.OUTPUT_COUNT] == 3
        assert metrics[CommsAppMetricsEnum.RECORDS_WITHOUT_LINKED_PARTICIPANTS_COUNT] == 0

    @freeze_time(time_to_freeze="2023-06-06 06:59:38.911459+00:00")
    def test_monitored_users_audits_is_batched(
        self,
        sample_aries_task_input_email: AriesTaskInput,
        link_participants_scroll_result,
        tenant_config_with_monitored_users_flag: dict,
        monitored_users_from_elastic_mock: pd.DataFrame,
        mocker,
    ):
        batch_size = 100
        mock_monitored_elastic_df = mocker.patch.object(
            abstract_chat_monitored_users.AbstractChatMonitoredUsers,
            "fetch_monitored_user_df_from_elastic",
        )
        mock_monitored_elastic_df.return_value = monitored_users_from_elastic_mock
        aries_task_output = self._run_email_aries_task(
            sample_aries_task_input=sample_aries_task_input_email,
            bucket_name="test.dev.steeleye.co",
            link_participants_scroll_result=link_participants_scroll_result,
            batch_size=batch_size,
            tenant_config=tenant_config_with_monitored_users_flag,
            mocker=mocker,
        )
        expected_email_df = pd.read_json(EXPECTED_EMAIL_MONITORED_USERS_NDJSON_PATH, lines=True)
        no_of_batches = ceil(expected_email_df.shape[0] / batch_size)
        output_ndjsons = []
        for i in range(no_of_batches):
            ndjson_path = get_batched_file_name(file_path=OUTPUT_EMAIL_NDJSON_PATH, batch_num=i)
            output_ndjsons.append(pd.read_json(ndjson_path, lines=True))
        output_email_df = pd.concat(output_ndjsons).reset_index(drop=True)

        sort_identifiers_columns(result_df=output_email_df, expected_result_df=expected_email_df)

        # Test that the chat event, message dfs are as expected
        pd.testing.assert_frame_equal(left=output_email_df, right=expected_email_df)

        # Assert metrics
        metrics = (
            aries_task_output.app_metric.metrics[StaticFields.DATA_INTEGRATION_METRICS_PREFIX][
                "bloomberg"
            ]["bloomberg_transform"]
            if aries_task_output.app_metric
            else {}
        )

        # 111 Monitored user emails so MUST be batches in two audit records
        audit = json.loads(AUDIT_PATH.read_text())
        assert len(eval(audit["workflow_status"][0].split("Monitored User IDs: ")[1])) == 100
        assert len(eval(audit["workflow_status"][1].split("Monitored User IDs: ")[1])) == 11

        assert metrics[GenericAppMetricsEnum.INPUT_COUNT] == 9
        assert metrics[GenericAppMetricsEnum.OUTPUT_COUNT] == 3
        assert metrics[CommsAppMetricsEnum.RECORDS_WITHOUT_LINKED_PARTICIPANTS_COUNT] == 0

    @freeze_time(time_to_freeze="2023-06-06 06:59:38.911459+00:00")
    def test_email_execution_with_monitoring_enabled_but_no_monitored_users(
        self,
        sample_aries_task_input_email: AriesTaskInput,
        link_participants_scroll_result,
        tenant_config_with_monitored_users_flag: dict,
        mocker,
    ):
        batch_size = 100
        mock_monitored_elastic_df = mocker.patch.object(
            abstract_chat_monitored_users.AbstractChatMonitoredUsers,
            "fetch_monitored_user_df_from_elastic",
        )
        mock_monitored_elastic_df.return_value = pd.DataFrame(
            columns=[PersonColumns.COMMUNICATIONS_EMAILS, PersonColumns.COMMUNICATIONS_IMACCOUNTS]
        )
        aries_task_output = self._run_email_aries_task(
            sample_aries_task_input=sample_aries_task_input_email,
            bucket_name="test.dev.steeleye.co",
            link_participants_scroll_result=link_participants_scroll_result,
            batch_size=batch_size,
            tenant_config=tenant_config_with_monitored_users_flag,
            mocker=mocker,
        )
        # Assert metrics
        metrics = (
            aries_task_output.app_metric.metrics[StaticFields.DATA_INTEGRATION_METRICS_PREFIX][
                "bloomberg"
            ]["bloomberg_transform"]
            if aries_task_output.app_metric
            else {}
        )
        assert metrics[GenericAppMetricsEnum.INPUT_COUNT] == 9
        assert metrics[GenericAppMetricsEnum.OUTPUT_COUNT] == 0
        assert metrics[CommsAppMetricsEnum.RECORDS_WITHOUT_LINKED_PARTICIPANTS_COUNT] == 0
        assert metrics[CommsAppMetricsEnum.NON_MONITORED_CONVERSATIONS_COUNT] == 9
        assert aries_task_output.output_param.params["Email"] is None
        assert aries_task_output.output_param.params["ChatEvent"] is None
        assert aries_task_output.output_param.params["Message"] is None

    @freeze_time(time_to_freeze="2023-06-06 06:59:38.911459+00:00")
    def test_valid_chat_executions(
        self,
        sample_aries_task_input_chat: AriesTaskInput,
        link_participants_scroll_result,
        tenant_config_without_monitored_users_flag,
        mocker,
    ):
        batch_size = 20
        aries_task_output = self._run_chat_aries_task(
            sample_aries_task_input=sample_aries_task_input_chat,
            bucket_name="test.dev.steeleye.co",
            link_participants_scroll_result=link_participants_scroll_result,
            batch_size=batch_size,
            tenant_config=tenant_config_without_monitored_users_flag,
            mocker=mocker,
        )

        expected_message_df = pd.read_json(EXPECTED_MESSAGE_NDJSON_PATH, lines=True)
        expected_chat_event_df = pd.read_json(EXPECTED_CHAT_EVENT_NDJSON_PATH, lines=True)

        no_of_msg_batches = ceil(expected_message_df.shape[0] / batch_size)
        output_msg_ndjsons = []
        for i in range(no_of_msg_batches):
            output_msg_ndjsons.append(
                pd.read_json(
                    get_batched_file_name(file_path=OUTPUT_MESSAGE_NDJSON_PATH, batch_num=i),
                    lines=True,
                )
            )
        output_message_df = pd.concat(output_msg_ndjsons).reset_index(drop=True)

        no_of_chat_batches = ceil(expected_chat_event_df.shape[0] / batch_size)
        output_chat_ndjsons = []
        for i in range(no_of_chat_batches):
            output_chat_ndjsons.append(
                pd.read_json(
                    get_batched_file_name(file_path=OUTPUT_CHAT_EVENT_NDJSON_PATH, batch_num=i),
                    lines=True,
                )
            )
        output_chat_event_df = pd.concat(output_chat_ndjsons).reset_index(drop=True)

        sort_identifiers_columns(
            result_df=output_message_df, expected_result_df=expected_message_df
        )
        sort_identifiers_columns(
            result_df=output_chat_event_df, expected_result_df=expected_chat_event_df
        )
        # Test that the chat event, message dfs are as expected
        pd.testing.assert_frame_equal(left=output_message_df, right=expected_message_df)
        pd.testing.assert_frame_equal(left=output_chat_event_df, right=expected_chat_event_df)

        # Assert metrics
        metrics = (
            aries_task_output.app_metric.metrics[StaticFields.DATA_INTEGRATION_METRICS_PREFIX][
                "bloomberg"
            ]["bloomberg_transform"]
            if aries_task_output.app_metric
            else {}
        )
        assert metrics[GenericAppMetricsEnum.INPUT_COUNT] == 330
        assert metrics[GenericAppMetricsEnum.OUTPUT_COUNT] == 330
        assert metrics[CommsAppMetricsEnum.RECORDS_WITHOUT_LINKED_PARTICIPANTS_COUNT] == 330

    @freeze_time(time_to_freeze="2023-06-06 06:59:38.911459+00:00")
    def test_it_can_handle_invalid_chat_files(
        self,
        sample_aries_task_input_chat: AriesTaskInput,
        link_participants_scroll_result,
        tenant_config_without_monitored_users_flag,
        mocker,
    ):
        with pytest.raises(Exception) as e:
            self._run_chat_aries_task(
                sample_aries_task_input=sample_aries_task_input_chat,
                bucket_name="test.dev.steeleye.co",
                link_participants_scroll_result=link_participants_scroll_result,
                batch_size=20,
                tenant_config=tenant_config_without_monitored_users_flag,
                input_local_file_path=DATA_INPUT_PATH.joinpath("invalid.xml"),
                mocker=mocker,
            )

        assert e.match(
            "The input file is not a valid XML file: 'no element found: line 13, column 11'"
        )  # noqa E501

    @freeze_time(time_to_freeze="2023-06-06 06:59:38.911459+00:00")
    def test_it_can_handle_invalid_email_files(
        self,
        sample_aries_task_input_email: AriesTaskInput,
        link_participants_scroll_result,
        tenant_config_without_monitored_users_flag: dict,
        mocker,
    ):
        with pytest.raises(Exception) as e:
            self._run_email_aries_task(
                sample_aries_task_input=sample_aries_task_input_email,
                bucket_name="test.dev.steeleye.co",
                link_participants_scroll_result=link_participants_scroll_result,
                batch_size=100,
                tenant_config=tenant_config_without_monitored_users_flag,
                input_local_file_path=DATA_INPUT_PATH.joinpath("invalid.xml"),
                mocker=mocker,
            )

        assert e.match(
            "The input file is not a valid XML file: 'no element found: line 13, column 11'"
        )  # noqa E501

    @freeze_time(time_to_freeze="2023-06-06 06:59:38.911459+00:00")
    def test_chat_executions_with_monitored_users(
        self,
        sample_aries_task_input_chat: AriesTaskInput,
        link_participants_scroll_result,
        tenant_config_with_monitored_users_flag,
        monitored_users_from_elastic_mock: pd.DataFrame,
        mocker,
    ):
        batch_size = 20
        mock_monitored_elastic_df = mocker.patch.object(
            abstract_chat_monitored_users.AbstractChatMonitoredUsers,
            "fetch_monitored_user_df_from_elastic",
        )
        mock_monitored_elastic_df.return_value = monitored_users_from_elastic_mock
        aries_task_output = self._run_chat_aries_task(
            sample_aries_task_input=sample_aries_task_input_chat,
            bucket_name="test.dev.steeleye.co",
            link_participants_scroll_result=link_participants_scroll_result,
            batch_size=batch_size,
            tenant_config=tenant_config_with_monitored_users_flag,
            mocker=mocker,
        )

        expected_message_df = pd.read_json(EXPECTED_MESSAGE_MONITORED_USERS_NDJSON_PATH, lines=True)
        expected_chat_event_df = pd.read_json(
            EXPECTED_CHAT_EVENT_MONITORED_USERS_NDJSON_PATH, lines=True
        )

        no_of_msg_batches = ceil(expected_message_df.shape[0] / batch_size)
        output_msg_ndjsons = []
        for i in range(no_of_msg_batches):
            output_msg_ndjsons.append(
                pd.read_json(
                    get_batched_file_name(file_path=OUTPUT_MESSAGE_NDJSON_PATH, batch_num=i),
                    lines=True,
                )
            )
        output_message_df = pd.concat(output_msg_ndjsons).reset_index(drop=True)

        no_of_chat_batches = ceil(expected_chat_event_df.shape[0] / batch_size)
        output_chat_ndjsons = []
        for i in range(no_of_chat_batches):
            output_chat_ndjsons.append(
                pd.read_json(
                    get_batched_file_name(file_path=OUTPUT_CHAT_EVENT_NDJSON_PATH, batch_num=i),
                    lines=True,
                )
            )
        output_chat_event_df = pd.concat(output_chat_ndjsons).reset_index(drop=True)

        sort_identifiers_columns(
            result_df=output_message_df, expected_result_df=expected_message_df
        )
        sort_identifiers_columns(
            result_df=output_chat_event_df, expected_result_df=expected_chat_event_df
        )
        # Test that the chat event, message dfs are as expected
        pd.testing.assert_frame_equal(left=output_message_df, right=expected_message_df)
        pd.testing.assert_frame_equal(left=output_chat_event_df, right=expected_chat_event_df)

        # Assert metrics
        metrics = (
            aries_task_output.app_metric.metrics[StaticFields.DATA_INTEGRATION_METRICS_PREFIX][
                "bloomberg"
            ]["bloomberg_transform"]
            if aries_task_output.app_metric
            else {}
        )
        assert metrics[GenericAppMetricsEnum.INPUT_COUNT] == 37
        assert metrics[GenericAppMetricsEnum.OUTPUT_COUNT] == 27
        assert metrics[CommsAppMetricsEnum.NON_MONITORED_CONVERSATIONS_COUNT] == 10
        assert metrics[CommsAppMetricsEnum.RECORDS_WITHOUT_LINKED_PARTICIPANTS_COUNT] == 27

    @freeze_time(time_to_freeze="2023-06-06 06:59:38.911459+00:00")
    def test_chat_executions_with_monitoring_enabled_but_no_monitored_users(
        self,
        sample_aries_task_input_chat: AriesTaskInput,
        link_participants_scroll_result,
        tenant_config_with_monitored_users_flag,
        mocker,
    ):
        batch_size = 20
        mock_monitored_elastic_df = mocker.patch.object(
            abstract_chat_monitored_users.AbstractChatMonitoredUsers,
            "fetch_monitored_user_df_from_elastic",
        )
        mock_monitored_elastic_df.return_value = pd.DataFrame(
            columns=[PersonColumns.COMMUNICATIONS_EMAILS, PersonColumns.COMMUNICATIONS_IMACCOUNTS]
        )
        aries_task_output = self._run_chat_aries_task(
            sample_aries_task_input=sample_aries_task_input_chat,
            bucket_name="test.dev.steeleye.co",
            link_participants_scroll_result=link_participants_scroll_result,
            batch_size=batch_size,
            tenant_config=tenant_config_with_monitored_users_flag,
            mocker=mocker,
        )

        # Assert metrics
        metrics = (
            aries_task_output.app_metric.metrics[StaticFields.DATA_INTEGRATION_METRICS_PREFIX][
                "bloomberg"
            ]["bloomberg_transform"]
            if aries_task_output.app_metric
            else {}
        )
        assert metrics[GenericAppMetricsEnum.INPUT_COUNT] == 11
        assert metrics[GenericAppMetricsEnum.OUTPUT_COUNT] == 0
        assert metrics[CommsAppMetricsEnum.NON_MONITORED_CONVERSATIONS_COUNT] == 11
        assert metrics[CommsAppMetricsEnum.RECORDS_WITHOUT_LINKED_PARTICIPANTS_COUNT] == 0

    @mock_aws
    @patch.object(abstract_chat_monitored_users, "run_fetch_tenant_configuration")
    @patch("integration_wrapper.integration_aries_task.upload_audit")
    @patch.object(
        target=CachedTenantWorkflowAPIClient,
        attribute="get",
        return_value=addict.Dict(
            {
                "tenant": {
                    "lake_prefix": "s3://test.dev.steeleye.co",
                },
                "workflow": {"streamed": False},
            },
        ),
    )
    def test_skip_if_empty_chat(
        self,
        _mock_cached_tenant_workflow_api_client,
        mock_auditor_upload,
        mock_fetch_tenant_config,
        aries_task_input_empty_chat_file: AriesTaskInput,
        tenant_config_without_monitored_users_flag,
    ):
        mock_fetch_tenant_config.return_value = tenant_config_without_monitored_users_flag

        # Mock upload audit
        mock_auditor_upload.side_effect = mock_auditor_upload_side_effect

        s3_add_objects_to_bucket(bucket_name="test.dev.steeleye.co", email_or_chat="chat")

        aries_task_output = bloomberg_transform_run(
            aries_task_input=aries_task_input_empty_chat_file
        )

        audit = json.loads(AUDIT_PATH.read_text())

        assert audit == {
            "input_records": {},
            "workflow_status": ["The input file does not contain any messages or chat events."],
        }

        # Assert metrics
        metrics = (
            aries_task_output.app_metric.metrics[StaticFields.DATA_INTEGRATION_METRICS_PREFIX][
                "bloomberg"
            ]["bloomberg_transform"]
            if aries_task_output.app_metric
            else {}
        )

        assert metrics[GenericAppMetricsEnum.INPUT_COUNT] == 0
        assert metrics[GenericAppMetricsEnum.OUTPUT_COUNT] == 0
        assert metrics[GenericAppMetricsEnum.SKIPPED_COUNT] == 0
        assert metrics[GenericAppMetricsEnum.ERRORED_COUNT] == 0
        assert metrics[GenericAppMetricsEnum.DUPLICATE_COUNT] == 0
        assert metrics[GenericAppMetricsEnum.SKIPPED_EMPTY_COUNT] == 0
        assert metrics[CommsAppMetricsEnum.NON_MONITORED_CONVERSATIONS_COUNT] == 0
        assert metrics[CommsAppMetricsEnum.RECORDS_WITHOUT_LINKED_PARTICIPANTS_COUNT] == 0

        assert aries_task_output.output_param.params == {
            "ChatEvent": None,
            "Email": None,
            "Message": None,
        }

    @mock_aws
    @patch.object(abstract_chat_monitored_users, "run_fetch_tenant_configuration")
    @patch("integration_wrapper.integration_aries_task.upload_audit")
    @patch.object(
        target=CachedTenantWorkflowAPIClient,
        attribute="get",
        return_value=addict.Dict(
            {
                "tenant": {
                    "lake_prefix": "s3://test.dev.steeleye.co",
                },
                "workflow": {"streamed": False},
            },
        ),
    )
    def test_skip_if_empty_email(
        self,
        _mock_cached_tenant_workflow_api_client,
        mock_auditor_upload,
        mock_fetch_tenant_config,
        aries_task_input_empty_email_file: AriesTaskInput,
        tenant_config_without_monitored_users_flag: dict,
    ):
        mock_fetch_tenant_config.return_value = tenant_config_without_monitored_users_flag

        # Mock upload audit
        mock_auditor_upload.side_effect = mock_auditor_upload_side_effect

        s3_add_objects_to_bucket(bucket_name="test.dev.steeleye.co", email_or_chat="email")

        aries_task_output = bloomberg_transform_run(
            aries_task_input=aries_task_input_empty_email_file
        )

        audit = json.loads(AUDIT_PATH.read_text())

        assert audit == {
            "input_records": {},
            "workflow_status": ["The input file does not contain any emails."],
        }

        # Assert metrics
        metrics = (
            aries_task_output.app_metric.metrics[StaticFields.DATA_INTEGRATION_METRICS_PREFIX][
                "bloomberg"
            ]["bloomberg_transform"]
            if aries_task_output.app_metric
            else {}
        )

        assert metrics[GenericAppMetricsEnum.INPUT_COUNT] == 0
        assert metrics[GenericAppMetricsEnum.OUTPUT_COUNT] == 0
        assert metrics[GenericAppMetricsEnum.SKIPPED_COUNT] == 0
        assert metrics[GenericAppMetricsEnum.ERRORED_COUNT] == 0
        assert metrics[GenericAppMetricsEnum.DUPLICATE_COUNT] == 0
        assert metrics[GenericAppMetricsEnum.SKIPPED_EMPTY_COUNT] == 0
        assert metrics[CommsAppMetricsEnum.NON_MONITORED_CONVERSATIONS_COUNT] == 0
        assert metrics[CommsAppMetricsEnum.RECORDS_WITHOUT_LINKED_PARTICIPANTS_COUNT] == 0

        assert aries_task_output.output_param.params == {
            "ChatEvent": None,
            "Email": None,
            "Message": None,
        }

    @patch("integration_wrapper.integration_aries_task.upload_audit")
    @patch.object(
        target=CachedTenantWorkflowAPIClient,
        attribute="get",
        return_value=addict.Dict(
            {
                "tenant": {
                    "lake_prefix": "s3://test.dev.steeleye.co",
                },
                "workflow": {"streamed": False},
            },
        ),
    )
    def test_skip_if_filepart_file(
        self,
        _mock_cached_tenant_workflow_api_client,
        mock_auditor_upload,
        aries_task_input_filepart_file: AriesTaskInput,
    ):
        # Mock upload audit
        mock_auditor_upload.side_effect = mock_auditor_upload_side_effect

        aries_task_output = bloomberg_transform_run(aries_task_input=aries_task_input_filepart_file)

        audit = json.loads(AUDIT_PATH.read_text())

        assert audit == {
            "input_records": {},
            "workflow_status": [
                "Workflow has been skipped as input file is a .filepart file (incomplete upload)."
            ],
        }

        # Assert metrics
        metrics = (
            aries_task_output.app_metric.metrics[StaticFields.DATA_INTEGRATION_METRICS_PREFIX][
                "bloomberg"
            ]["bloomberg_transform"]
            if aries_task_output.app_metric
            else {}
        )

        assert metrics[GenericAppMetricsEnum.INPUT_COUNT] == 0
        assert metrics[GenericAppMetricsEnum.OUTPUT_COUNT] == 0
        assert metrics[GenericAppMetricsEnum.SKIPPED_COUNT] == 0
        assert metrics[GenericAppMetricsEnum.ERRORED_COUNT] == 0
        assert metrics[GenericAppMetricsEnum.DUPLICATE_COUNT] == 0
        assert metrics[GenericAppMetricsEnum.SKIPPED_EMPTY_COUNT] == 0
        assert metrics[CommsAppMetricsEnum.NON_MONITORED_CONVERSATIONS_COUNT] == 0
        assert metrics[CommsAppMetricsEnum.RECORDS_WITHOUT_LINKED_PARTICIPANTS_COUNT] == 0

        assert aries_task_output.output_param.params == {
            "ChatEvent": None,
            "Email": None,
            "Message": None,
        }

    @freeze_time(time_to_freeze="2023-06-06 06:59:38.911459+00:00")
    def test_kyte_email_executions(
        self,
        aries_task_input_email_kyte: AriesTaskInput,
        link_participants_scroll_result,
        tenant_config_without_monitored_users_flag: dict,
        mocker,
    ):
        batch_size = 100
        # Enable the kyte override by changing the tenant
        aries_task_output = self._run_email_aries_task(
            sample_aries_task_input=aries_task_input_email_kyte,
            bucket_name="kytebroking.dev.steeleye.co",
            link_participants_scroll_result=link_participants_scroll_result,
            batch_size=batch_size,
            tenant_config=tenant_config_without_monitored_users_flag,
            mocker=mocker,
        )
        expected_email_df = pd.read_json(EXPECTED_KYTE_EMAIL_NDJSON_PATH, lines=True)
        no_of_batches = ceil(expected_email_df.shape[0] / batch_size)
        output_ndjsons = []
        for i in range(no_of_batches):
            ndjson_path = get_batched_file_name(file_path=OUTPUT_EMAIL_NDJSON_PATH, batch_num=i)
            output_ndjsons.append(pd.read_json(ndjson_path, lines=True))
        output_email_df = pd.concat(output_ndjsons).reset_index(drop=True)

        sort_identifiers_columns(result_df=output_email_df, expected_result_df=expected_email_df)

        # Test that the chat event, message dfs are as expected
        pd.testing.assert_frame_equal(left=output_email_df, right=expected_email_df)

        # Assert metrics
        metrics = (
            aries_task_output.app_metric.metrics[StaticFields.DATA_INTEGRATION_METRICS_PREFIX][
                "bloomberg"
            ]["bloomberg_transform"]
            if aries_task_output.app_metric
            else {}
        )
        assert metrics[GenericAppMetricsEnum.INPUT_COUNT] == 9
        assert metrics[GenericAppMetricsEnum.OUTPUT_COUNT] == 9
        assert metrics[CommsAppMetricsEnum.RECORDS_WITHOUT_LINKED_PARTICIPANTS_COUNT] == 0

    @freeze_time(time_to_freeze="2023-06-06 06:59:38.911459+00:00")
    def test_kyte_chat_executions(
        self,
        aries_task_input_chat_kyte: AriesTaskInput,
        link_participants_scroll_result,
        tenant_config_without_monitored_users_flag,
        mocker,
    ):
        batch_size = 500
        aries_task_output = self._run_chat_aries_task(
            sample_aries_task_input=aries_task_input_chat_kyte,
            bucket_name="kytebroking.dev.steeleye.co",
            link_participants_scroll_result=link_participants_scroll_result,
            batch_size=batch_size,
            tenant_config=tenant_config_without_monitored_users_flag,
            mocker=mocker,
        )

        expected_message_df = pd.read_json(EXPECTED_KYTE_MESSAGE_NDJSON_PATH, lines=True)
        expected_chat_event_df = pd.read_json(EXPECTED_KYTE_CHAT_EVENT_NDJSON_PATH, lines=True)

        no_of_msg_batches = ceil(expected_message_df.shape[0] / batch_size)
        output_msg_ndjsons = []
        for i in range(no_of_msg_batches):
            output_msg_ndjsons.append(
                pd.read_json(
                    get_batched_file_name(file_path=OUTPUT_MESSAGE_NDJSON_PATH, batch_num=i),
                    lines=True,
                )
            )
        output_message_df = pd.concat(output_msg_ndjsons).reset_index(drop=True)

        no_of_chat_batches = ceil(expected_chat_event_df.shape[0] / batch_size)
        output_chat_ndjsons = []
        for i in range(no_of_chat_batches):
            output_chat_ndjsons.append(
                pd.read_json(
                    get_batched_file_name(file_path=OUTPUT_CHAT_EVENT_NDJSON_PATH, batch_num=i),
                    lines=True,
                )
            )
        output_chat_event_df = pd.concat(output_chat_ndjsons).reset_index(drop=True)

        sort_identifiers_columns(
            result_df=output_message_df, expected_result_df=expected_message_df
        )
        sort_identifiers_columns(
            result_df=output_chat_event_df, expected_result_df=expected_chat_event_df
        )
        # Test that the chat event, message dfs are as expected
        pd.testing.assert_frame_equal(left=output_message_df, right=expected_message_df)
        pd.testing.assert_frame_equal(left=output_chat_event_df, right=expected_chat_event_df)

        # Assert metrics
        metrics = (
            aries_task_output.app_metric.metrics[StaticFields.DATA_INTEGRATION_METRICS_PREFIX][
                "bloomberg"
            ]["bloomberg_transform"]
            if aries_task_output.app_metric
            else {}
        )
        assert metrics[GenericAppMetricsEnum.INPUT_COUNT] == 330
        assert metrics[GenericAppMetricsEnum.OUTPUT_COUNT] == 330
        assert metrics[CommsAppMetricsEnum.RECORDS_WITHOUT_LINKED_PARTICIPANTS_COUNT] == 140

    @mock_aws
    @patch.multiple(
        "aries_se_comms_tasks.feeds.bloomberg.download_and_transform_bbg_mail", fsspec=DEFAULT
    )
    @patch("integration_wrapper.integration_aries_task.upload_audit")
    @patch.object(link_participants_text_files, "get_repository_by_cluster_version")
    @patch.object(link_participants_text_files, "get_es_config")
    @patch.object(bloomberg_transform_flow, "create_ndjson_path")
    @patch.object(abstract_chat_monitored_users, "run_fetch_tenant_configuration")
    @patch.object(
        target=CachedTenantWorkflowAPIClient,
        attribute="get",
        return_value=addict.Dict(
            {
                "tenant": {
                    "lake_prefix": "s3://test.dev.steeleye.co",
                },
                "workflow": {"streamed": False},
            },
        ),
    )
    def _run_email_aries_task(
        self,
        _mock_cached_tenant_workflow_api_client,
        mock_fetch_tenant_config,
        mock_create_ndjson_path,
        mock_link_participants_get_es_config,
        mock_link_participants_elasticsearch_repository,
        mock_auditor_upload,
        sample_aries_task_input,
        bucket_name: str,
        link_participants_scroll_result: List,
        batch_size: int,
        tenant_config: dict,
        mocker,
        input_local_file_path: Optional[Path] = None,
        **kwargs,
    ):
        s3_add_objects_to_bucket(bucket_name=bucket_name, email_or_chat="email")
        aries_task_input = sample_aries_task_input

        # Mocks for link_participants elastic config
        mock_link_participants_get_es_config.return_value = ResourceConfig(
            host="localhost",
            port=9200,
            scheme="http",
            meta_prefix=MetaPrefix.AMPERSAND,
        )
        mock_fetch_tenant_config.return_value = tenant_config
        # Mocks link participants elastic repository
        es_obj = mock_link_participants_elasticsearch_repository.return_value
        es_obj.scroll.return_value = link_participants_scroll_result
        es_obj.MAX_TERMS_SIZE = 1024
        es_obj.meta.prefix = MetaPrefix.AMPERSAND
        es_obj.meta.key = "&key"
        es_obj.meta.id = "&id"

        # Mock create ndjson path
        mock_create_ndjson_path.return_value = OUTPUT_EMAIL_NDJSON_PATH

        # Mock upload audit
        mock_auditor_upload.side_effect = mock_auditor_upload_side_effect

        # Mock Download bbg file
        mock_fs = MagicMock()

        kwargs["fsspec"].get_fs_token_paths.return_value = (mock_fs, None, (None,))

        input_local_file_path = input_local_file_path or EMAIL_INPUT_FILES_PATH.joinpath(
            "1671010290.msg.220509.xml"
        )

        mock_fs.open.return_value = open(input_local_file_path, "rb")

        # Mock batch size
        mocker.patch(
            "integration_text_comms_tasks.message.bloomberg_transform.bloomberg_transform_flow.EMAIL_BATCH_SIZE",
            batch_size,
        )

        # Run flow
        return bloomberg_transform_run(aries_task_input=aries_task_input)

    @mock_aws
    @patch.multiple(
        "aries_se_comms_tasks.feeds.bloomberg.download_and_transform_ib_chat", fsspec=DEFAULT
    )
    @patch("integration_wrapper.integration_aries_task.upload_audit")
    @patch.object(link_participants_text_files, "get_repository_by_cluster_version")
    @patch.object(link_participants_text_files, "get_es_config")
    @patch.object(bloomberg_transform_flow, "create_ndjson_path")
    @patch.object(abstract_chat_monitored_users, "run_fetch_tenant_configuration")
    @patch.object(
        target=CachedTenantWorkflowAPIClient,
        attribute="get",
        return_value=addict.Dict(
            {
                "tenant": {
                    "lake_prefix": "s3://test.dev.steeleye.co",
                },
                "workflow": {"streamed": False},
            },
        ),
    )
    def _run_chat_aries_task(
        self,
        _mock_cached_tenant_workflow_api_client,
        mock_fetch_tenant_config,
        mock_create_ndjson_path,
        mock_link_participants_get_es_config,
        mock_link_participants_elasticsearch_repository,
        mock_auditor_upload,
        sample_aries_task_input,
        bucket_name: str,
        link_participants_scroll_result: List,
        tenant_config: dict,
        batch_size: int,
        mocker,
        input_local_file_path: Optional[Path] = None,
        **kwargs,
    ):
        s3_add_objects_to_bucket(bucket_name=bucket_name, email_or_chat="chat")
        aries_task_input = sample_aries_task_input

        # Mocks for link_participants elastic config
        mock_link_participants_get_es_config.return_value = ResourceConfig(
            host="localhost",
            port=9200,
            scheme="http",
            meta_prefix=MetaPrefix.AMPERSAND,
        )
        # Mocks link participants elastic repository
        es_obj = mock_link_participants_elasticsearch_repository.return_value
        es_obj.scroll.return_value = link_participants_scroll_result
        es_obj.MAX_TERMS_SIZE = 1024
        es_obj.meta.prefix = MetaPrefix.AMPERSAND
        es_obj.meta.key = "&key"
        es_obj.meta.id = "&id"

        # Mock create ndjson path
        mock_create_ndjson_path.side_effect = [
            OUTPUT_CHAT_EVENT_NDJSON_PATH,
            OUTPUT_MESSAGE_NDJSON_PATH,
        ]

        mock_fetch_tenant_config.return_value = tenant_config

        # Mock upload audit
        mock_auditor_upload.side_effect = mock_auditor_upload_side_effect

        # Mock Download bbg file
        mock_fs = MagicMock()

        kwargs["fsspec"].get_fs_token_paths.return_value = (mock_fs, None, (None,))

        input_local_file_path = input_local_file_path or CHAT_INPUT_FILES_PATH.joinpath(
            "1666011684.ib19.220509.xml"
        )

        mock_fs.open.return_value = open(input_local_file_path, "rb")

        # Mock batch size
        mocker.patch(
            "integration_text_comms_tasks.message.bloomberg_transform.bloomberg_transform_flow.MESSAGES_BATCH_SIZE",
            batch_size,
        )
        mocker.patch(
            "integration_text_comms_tasks.message.bloomberg_transform.bloomberg_transform_flow.CHAT_EVENTS_BATCH_SIZE",
            batch_size,
        )
        # Run flow
        return bloomberg_transform_run(aries_task_input=aries_task_input)

    @staticmethod
    def teardown_method():
        if AUDIT_PATH.exists():
            AUDIT_PATH.unlink()

        """Deletes temporary files created during the test."""
        chat_results_path = Path(OUTPUT_CHAT_EVENT_NDJSON_PATH).parent
        email_results_path = Path(OUTPUT_EMAIL_NDJSON_PATH).parent
        chat_results_prefix = Path(OUTPUT_CHAT_EVENT_NDJSON_PATH).stem
        message_results_prefix = Path(OUTPUT_MESSAGE_NDJSON_PATH).stem
        email_results_prefix = Path(OUTPUT_EMAIL_NDJSON_PATH).stem

        for file_ in os.listdir(chat_results_path):
            if file_.startswith(chat_results_prefix) or file_.startswith(message_results_prefix):
                os.remove(os.path.join(chat_results_path, file_))
        for file_ in os.listdir(email_results_path):
            if file_.startswith(email_results_prefix):
                os.remove(os.path.join(email_results_path, file_))


def s3_add_objects_to_bucket(bucket_name: str, email_or_chat: str):
    """
    Puts the single ZIP file in the correct path in the mock S3
    bucket 'bucket_name'.
    NOTE: The recording files inside the ZIP file are dummy mp3 files with 1
    second duration. There are 3 such recording files.
    :param bucket_name: Bucket name of Mock S3 bucket
    :param email_or_chat: email or bbg message
    :return: None, uploads files to the mock S3 bucket
    """

    # Create bucket
    s3 = boto3.client("s3", region_name="us-east-1")
    s3.create_bucket(Bucket=bucket_name)
    if email_or_chat == "email":
        base_key = "aries/ingress/nonstreamed/polled/bloomberg_poll/landing/shrenik/2023/06/08"
        for file_ in glob.iglob(f"{EMAIL_INPUT_FILES_PATH.as_posix()}/*"):
            file_name = Path(file_).name
            if file_name.endswith(".xml"):
                with open(file_, "rb") as f:
                    s3.put_object(
                        Bucket=bucket_name,
                        Key=f"{base_key}/{file_name}",
                        Body=f.read(),
                    )
            else:
                with open(file_, "rb") as f:
                    s3.put_object(
                        Bucket=bucket_name,
                        Key=f"{base_key}/bloomberg_msg_attachments_220509/{file_name}",
                        Body=f.read(),
                    )
    if email_or_chat == "chat":
        base_key = "aries/ingress/nonstreamed/polled/bloomberg_poll/landing/shrenik/2023/06/08"
        for file_ in glob.iglob(f"{CHAT_INPUT_FILES_PATH.as_posix()}/*"):
            file_name = Path(file_).name
            if file_name.endswith(".xml"):
                with open(file_, "rb") as f:
                    s3.put_object(
                        Bucket=bucket_name,
                        Key=f"{base_key}/{file_name}",
                        Body=f.read(),
                    )
            else:
                with open(file_, "rb") as f:
                    s3.put_object(
                        Bucket=bucket_name,
                        Key=f"{base_key}/bloomberg_ib19_attachments_220509/{file_name}",
                        Body=f.read(),
                    )


def mock_auditor_upload_side_effect(audit_filepath: str, **kwargs) -> None:
    shutil.copy(Path(audit_filepath), AUDIT_PATH)

    return None
