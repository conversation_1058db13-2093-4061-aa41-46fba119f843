import os
import pandas as pd
import pytest
from aries_io_event.io_param import IOParamFieldSet
from aries_io_event.task import TaskFieldSet
from aries_io_event.workflow import WorkflowFieldSet
from aries_task_link.models import AriesTaskInput
from datetime import datetime

os.environ["DATA_PLATFORM_CONFIG_API_URL"] = "https://test-enterprise.steeleye.co"


@pytest.fixture()
def sample_aries_task_input() -> AriesTaskInput:
    workflow = WorkflowFieldSet(
        trace_id="test_o2_sms_message",
        name="o2_sms",
        stack="dev-blue",
        tenant="test",
        start_timestamp=datetime(2022, 7, 6),
    )

    input_param = IOParamFieldSet(
        params=dict(
            file_uri=(
                "s3://test.dev.steeleye.co/aries/ingest_batching/o2_sms_poll/2023/12/22/iuyt3k/20231222___2files.ndjson"
            ),
        )
    )

    task = TaskFieldSet(name="o2_sms_preprocess", version="latest", success=False)

    return AriesTaskInput(workflow=workflow, input_param=input_param, task=task)


@pytest.fixture()
def elastic_df_file_1_all_records_in_elastic() -> pd.DataFrame:
    return pd.DataFrame(
        {
            "metadata.messageId": [
                "3693",
                "3892",
                "3461",
                "4015",
                "3650",
                "4003",
                "3785",
                "3430",
                "3628",
                "4004",
                "3809",
                "3881",
                "3724",
                "3868",
                "3967",
                "3956",
                "3629",
                "3472",
                "3837",
                "4014",
                "4066",
                "4067",
                "3816",
                "3715",
                "3660",
                "3879",
                "3861",
                "3691",
                "3544",
                "3512",
                "3900",
                "3989",
                "3926",
                "3448",
                "4033",
                "3839",
                "3483",
                "3459",
                "3827",
                "3860",
                "3597",
                "3859",
                "3807",
                "4026",
                "3915",
                "3743",
                "4018",
                "3588",
                "4044",
                "3525",
                "3893",
                "3630",
                "3818",
                "4037",
                "3870",
                "3974",
                "4055",
                "3606",
                "3754",
                "3834",
                "3771",
                "3590",
                "3569",
                "3727",
                "4028",
                "3856",
                "3912",
                "3782",
                "3871",
                "3932",
                "3433",
                "4027",
                "3911",
                "3690",
                "3845",
                "3531",
                "3775",
                "3679",
                "4038",
                "3922",
                "3819",
                "3594",
                "3631",
                "3984",
                "3863",
                "3805",
                "3934",
                "3966",
                "3783",
                "3826",
                "3997",
                "3804",
                "3955",
                "3852",
                "4071",
                "3752",
                "3705",
                "3646",
                "3476",
                "3683",
                "3962",
                "3914",
                "3878",
                "3867",
                "3746",
                "4049",
                "4036",
                "3720",
                "3953",
                "3443",
                "3835",
                "3603",
                "3698",
                "3602",
                "3833",
                "3730",
                "3571",
                "3935",
                "3936",
                "4035",
                "3957",
                "3899",
                "3800",
                "3507",
                "3866",
                "3898",
                "3436",
                "3874",
                "3801",
                "3921",
                "3760",
                "3813",
                "3486",
                "3447",
                "3781",
                "3562",
                "3718",
                "3864",
                "3645",
                "3976",
                "3913",
                "3895",
                "3601",
                "3865",
                "4039",
                "3802",
                "3812",
                "3965",
                "4002",
                "3643",
                "4031",
                "4042",
                "3875",
                "3697",
                "4053",
                "3896",
                "3917",
                "3869",
                "3407",
                "3626",
                "3810",
                "3843",
                "3409",
                "3589",
                "3980",
                "3688",
                "3740",
                "4001",
                "3958",
                "3803",
                "3762",
                "3969",
                "3981",
                "4043",
                "3647",
                "3806",
                "3897",
                "3449",
                "3887",
                "4032",
                "3970",
                "3784",
                "3689",
                "3960",
                "3460",
                "3644",
                "3979",
                "3873",
                "4034",
                "4021",
                "4010",
                "3773",
                "3968",
                "3872",
                "3648",
                "3894",
                "3905",
                "3836",
                "3916",
                "3883",
            ],
            "&id": range(1, 201),
        }
    )


@pytest.fixture()
def elastic_df_file_1_some_records_in_elastic() -> pd.DataFrame:
    return pd.DataFrame(
        {
            "metadata.messageId": [
                "3693",
                "3892",
                "3461",
                "4015",
                "3650",
                "4003",
                "3785",
                "3430",
                "3628",
                "4004",
                "3809",
                "3881",
                "3724",
                "3868",
                "3967",
                "3956",
                "3629",
                "3472",
                "3837",
                "4014",
                "4066",
                "4067",
                "3816",
                "3715",
                "3660",
                "3879",
                "3861",
                "3691",
                "3544",
                "3512",
                "3900",
                "3989",
                "3926",
                "3448",
                "4033",
                "3839",
                "3483",
                "3459",
                "3827",
                "3860",
                "3597",
                "3859",
                "3807",
                "4026",
                "3915",
                "3743",
                "4018",
                "3588",
                "4044",
                "3525",
                "3893",
                "3630",
                "3818",
                "4037",
                "3870",
                "3974",
                "4055",
                "3606",
                "3754",
                "3834",
                "3771",
                "3590",
                "3569",
                "3727",
                "4028",
                "3856",
                "3912",
                "3782",
                "3871",
                "3932",
                "3433",
                "4027",
                "3911",
                "3690",
                "3845",
                "3531",
                "3775",
                "3679",
                "4038",
                "3922",
                "3819",
                "3594",
                "3631",
                "3984",
                "3863",
                "3805",
                "3934",
                "3966",
                "3783",
                "3826",
                "3997",
                "3804",
                "3955",
                "3852",
                "4071",
                "3752",
                "3705",
                "3646",
                "3476",
                "3683",
                "3962",
                "3914",
                "3878",
                "3867",
                "3746",
                "4049",
                "4036",
            ],
            "&id": range(1, 108),
        }
    )


@pytest.fixture()
def elastic_df_file_2_all_records_in_elastic() -> pd.DataFrame:
    return pd.DataFrame(
        {
            "metadata.messageId": [
                "98",
                "159",
                "160",
                "164",
                "286",
                "162",
                "329",
                "288",
                "211",
                "214",
                "209",
                "212",
                "213",
                "210",
                "92",
                "308",
                "46",
                "312",
                "311",
                "309",
                "295",
                "310",
                "296",
                "315",
                "314",
                "108",
            ],
            "&id": range(1, 27),
        }
    )
