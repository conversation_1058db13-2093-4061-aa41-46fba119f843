{"messages": [{"type": "message", "subtype": "channel_archive", "ts": "1676532249.621939", "user": "U024XCUUY8Y", "text": "archived the channel"}], "members": ["U3NA51EUR", "U4PA3EVJT", "U8X0K6K7E", "UMBBL055L", "UR71HD9UZ", "U012K27RE9E", "U013W0HT6KW", "U014MHCS8NR", "U017T4H8Z0E", "U01DFSQNYNB", "U01PGFLH47Q", "U01TDJ03WHW", "U020CT12DQF", "U023KEU40SZ", "U024XCUUY8Y", "U028BQ5TFDJ", "U02AM8YRR5Y", "U02AMERNUFM", "U02BKE4MRSN", "U02GC2BK6AJ", "U02TV907UEM", "U03A1PZEBDX", "U03DVFYQD43", "U03S4FD0KU1", "U046960HEJU"], "info": {"id": "C02MPNNTE05", "name": "system-design", "created": 1637213063, "member_count": 0, "is_general": false, "is_private": false, "is_im": false, "is_mpim": false, "is_file": false, "is_deleted": false, "is_archived": true, "creator": "U024XCUUY8Y", "is_moved": false, "is_shared": false, "name_normalized": "system-design", "is_global_shared": false, "is_org_shared": false, "is_org_mandatory": false, "is_org_default": false, "previous_names": ["csurv-email-feed"], "purpose": {"text": "", "set_by": "", "date_set": 0}, "topic": {"text": "<https://steeleye.atlassian.net/wiki/x/CgDhgg>", "set_by": "U3NA51EUR", "date_set": 1638346896}, "retention": {"type": "default", "duration": 0}, "shared": {"shared_team_ids": [], "connected_team_ids": [], "internal_team_ids": [], "connected_limited_team_ids": []}, "has_guests": false}, "edits": []}
{"messages": [{"type": "message", "subtype": "channel_leave", "ts": "1676541567.222919", "user": "U014MHCS8NR", "text": "<@U014MHCS8NR> has left the channel"}], "members": ["U01QHBXEMK4", "U020CF0A88P", "U025DCU40CA"], "info": {"id": "C04NUER9EKG", "name": "eu-5873", "created": 1676025833, "member_count": 3, "is_general": false, "is_private": false, "is_im": false, "is_mpim": false, "is_file": false, "is_deleted": false, "is_archived": false, "creator": "U014MHCS8NR", "is_moved": false, "is_shared": false, "name_normalized": "eu-5873", "is_global_shared": false, "is_org_shared": false, "is_org_mandatory": false, "is_org_default": false, "previous_names": [], "purpose": {"text": "<https://steeleye.atlassian.net/browse/EU-5873> (user not receiving activation email)", "set_by": "U014MHCS8NR", "date_set": 1676025834}, "topic": {"text": "", "set_by": "", "date_set": 0}, "retention": {"type": "default", "duration": 0}, "shared": {"shared_team_ids": [], "connected_team_ids": [], "internal_team_ids": [], "connected_limited_team_ids": []}, "has_guests": false}, "edits": []}
{"messages": [{"type": "message", "subtype": "channel_archive", "ts": "1676560045.212459", "user": "U4PA3EVJT", "text": "archived the channel"}], "members": ["U3NA51EUR", "U4PA3EVJT", "UGH78B1PG", "UMBBL055L", "UMRDJ231P", "UVDCV9LQ0", "U010M7KJX2S", "U011XA81ZSA", "U01T3T2LSTH", "U020CF0A88P", "U0282227N83", "U02JDB3USF4"], "info": {"id": "C03G4L0FU3G", "name": "cenkos-working-group", "created": 1653052502, "member_count": 0, "is_general": false, "is_private": false, "is_im": false, "is_mpim": false, "is_file": false, "is_deleted": false, "is_archived": true, "creator": "U0282227N83", "is_moved": false, "is_shared": false, "name_normalized": "cenkos-working-group", "is_global_shared": false, "is_org_shared": false, "is_org_mandatory": false, "is_org_default": false, "previous_names": [], "purpose": {"text": "", "set_by": "", "date_set": 0}, "topic": {"text": "", "set_by": "", "date_set": 0}, "retention": {"type": "default", "duration": 0}, "shared": {"shared_team_ids": [], "connected_team_ids": [], "internal_team_ids": [], "connected_limited_team_ids": []}, "has_guests": false}, "edits": []}
{"messages": [{"type": "message", "subtype": "channel_leave", "ts": "1676560265.650659", "user": "U01T3T2LSTH", "text": "<@U01T3T2LSTH> has left the channel"}], "members": ["UGH78B1PG", "U0148KU07UJ"], "info": {"id": "C03CKJC910F", "name": "rwc-poc", "created": 1650988385, "member_count": 2, "is_general": false, "is_private": false, "is_im": false, "is_mpim": false, "is_file": false, "is_deleted": false, "is_archived": false, "creator": "U01FWGE5Q2E", "is_moved": false, "is_shared": false, "name_normalized": "rwc-poc", "is_global_shared": false, "is_org_shared": false, "is_org_mandatory": false, "is_org_default": false, "previous_names": [], "purpose": {"text": "", "set_by": "", "date_set": 0}, "topic": {"text": "", "set_by": "", "date_set": 0}, "retention": {"type": "default", "duration": 0}, "shared": {"shared_team_ids": [], "connected_team_ids": [], "internal_team_ids": [], "connected_limited_team_ids": []}, "has_guests": false}, "edits": []}
{"messages": [{"type": "message", "subtype": "channel_leave", "ts": "1676561063.364899", "user": "U01AWB8GG1F", "text": "<@U01AWB8GG1F> has left the channel"}], "members": ["U3NA51EUR", "U013W0HT6KW", "U017T4H8Z0E", "U01DFSQNYNB", "U023KEU40SZ"], "info": {"id": "C042CU2LA3T", "name": "r-n-d-tax-credits", "created": 1662991663, "member_count": 5, "is_general": false, "is_private": false, "is_im": false, "is_mpim": false, "is_file": false, "is_deleted": false, "is_archived": false, "creator": "U4E48R19P", "is_moved": false, "is_shared": false, "name_normalized": "r-n-d-tax-credits", "is_global_shared": false, "is_org_shared": false, "is_org_mandatory": false, "is_org_default": false, "previous_names": [], "purpose": {"text": "", "set_by": "", "date_set": 0}, "topic": {"text": "", "set_by": "", "date_set": 0}, "retention": {"type": "default", "duration": 0}, "shared": {"shared_team_ids": [], "connected_team_ids": [], "internal_team_ids": [], "connected_limited_team_ids": []}, "has_guests": false}, "edits": []}
{"messages": [{"type": "message", "subtype": "channel_archive", "ts": "1676561117.821189", "user": "U4E48R19P", "text": "archived the channel"}, {"type": "message", "subtype": "channel_leave", "ts": "1676561112.652499", "user": "U01AWB8GG1F", "text": "<@U01AWB8GG1F> has left the channel"}, {"type": "message", "subtype": "channel_leave", "ts": "1676541544.671879", "user": "U014MHCS8NR", "text": "<@U014MHCS8NR> has left the channel"}], "members": ["U3NA51EUR", "U4E48R19P", "U4PA3EVJT", "U4XDQTVFA", "UGH78B1PG", "UMRDJ231P", "UV29FBAHJ", "U012K27RE9E", "U017T4H8Z0E", "U01ABDBUKPU", "U01DFSQNYNB", "U01PGFLH47Q", "U01QHBXEMK4", "U020656MUMU", "U020CF0A88P", "U023KEU40SZ", "U025DCU40CA", "U028BQ5TFDJ", "U02AM8YRR5Y", "U02GC2BK6AJ", "U02HQ2K2K1N", "U03A1PZEBDX", "U03EPS7UM45", "U03J6HQPWEA", "U03JCM0SG0G", "U03SLP52804", "U04BFE9NZ6H", "U04CDMJJUDB", "U04M61G19N2"], "info": {"id": "C04K3NYKHUL", "name": "sev1-prod-master-data-down", "created": 1673852935, "member_count": 0, "is_general": false, "is_private": false, "is_im": false, "is_mpim": false, "is_file": false, "is_deleted": false, "is_archived": true, "creator": "U4E48R19P", "is_moved": false, "is_shared": false, "name_normalized": "sev1-prod-master-data-down", "is_global_shared": false, "is_org_shared": false, "is_org_mandatory": false, "is_org_default": false, "previous_names": [], "purpose": {"text": "", "set_by": "", "date_set": 0}, "topic": {"text": "", "set_by": "", "date_set": 0}, "retention": {"type": "default", "duration": 0}, "shared": {"shared_team_ids": [], "connected_team_ids": [], "internal_team_ids": [], "connected_limited_team_ids": []}, "has_guests": false}, "edits": []}
{"messages": [{"client_msg_id": "eecddb24-118c-427e-b10d-802f60438b91", "type": "message", "text": "great to see this thread alive again", "user": "U4PA3EVJT", "ts": "1676625209.069039", "blocks": [{"type": "rich_text", "block_id": "wYhP", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "great to see this thread alive again"}]}]}], "team": "T3P0PGX18", "thread_ts": "1676282749.569729", "parent_user_id": "U01PGFLH47Q", "reactions": [{"name": "joy", "users": ["U035GAPLD0W", "U030PK30L9L"], "count": 2}]}, {"type": "message", "text": "", "files": [{"id": "F04Q37ECV0A", "created": 1676621675, "timestamp": 1676621675, "name": "3_emails.pst", "title": "3_emails.pst", "mimetype": "application/octet-stream", "filetype": "binary", "pretty_type": "Binary", "user": "U01PGFLH47Q", "user_team": "T3P0PGX18", "editable": false, "size": 271360, "mode": "hosted", "is_external": false, "external_type": "", "is_public": true, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T3P0PGX18-F04Q37ECV0A/3_emails.pst", "url_private_download": "https://files.slack.com/files-pri/T3P0PGX18-F04Q37ECV0A/download/3_emails.pst", "media_display_type": "unknown", "permalink": "https://steeleye.slack.com/files/U01PGFLH47Q/F04Q37ECV0A/3_emails.pst", "permalink_public": "https://slack-files.com/T3P0PGX18-F04Q37ECV0A-2bead29dbb", "has_rich_preview": false, "file_access": "visible"}, {"id": "F04Q37ESAV8", "created": 1676621681, "timestamp": 1676621681, "name": "[Confluence] Product _ Comms - Give me Context.eml", "title": "[Confluence] Product _ Comms - Give me Context.eml", "mimetype": "text/plain", "filetype": "text", "pretty_type": "Plain Text", "user": "U01PGFLH47Q", "user_team": "T3P0PGX18", "editable": true, "size": 114898, "mode": "snippet", "is_external": false, "external_type": "", "is_public": true, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T3P0PGX18-F04Q37ESAV8/_confluence__product___comms_-_give_me_context.eml", "url_private_download": "https://files.slack.com/files-pri/T3P0PGX18-F04Q37ESAV8/download/_confluence__product___comms_-_give_me_context.eml", "permalink": "https://steeleye.slack.com/files/U01PGFLH47Q/F04Q37ESAV8/_confluence__product___comms_-_give_me_context.eml", "permalink_public": "https://slack-files.com/T3P0PGX18-F04Q37ESAV8-8422a42e2e", "edit_link": "https://steeleye.slack.com/files/U01PGFLH47Q/F04Q37ESAV8/_confluence__product___comms_-_give_me_context.eml/edit", "has_rich_preview": false, "file_access": "visible"}], "upload": false, "user": "U01PGFLH47Q", "ts": "1676621684.274649", "client_msg_id": "0aaeb34c-2c75-4c36-9e5f-8e2c8aa94f2a", "file_uploads": [{"id": "F04Q37ECV0A", "name": "name", "title": "title", "mimetype": "mimetype", "user": "user", "s3_path": "s3://test.dev.steeleye.co/aries/ingress/depository/slack_chat_poll/random.txt", "filetype": "filetype", "pretty_type": "pretty_type"}, {"id": "F04Q37ESAV8", "name": "name", "title": "title", "mimetype": "mimetype", "user": "user", "s3_path": "s3://test.dev.steeleye.co/aries/ingress/depository/slack_chat_poll/random.txt", "filetype": "filetype", "pretty_type": "pretty_type"}]}, {"type": "message", "text": "Edit: one file to be del in next batch", "files": [{"id": "F04PWK0NFU6", "created": 1676621567, "timestamp": 1676621567, "name": "swarm_env.env", "title": "swarm_env.env", "mimetype": "text/plain", "filetype": "text", "pretty_type": "Plain Text", "user": "U01PGFLH47Q", "user_team": "T3P0PGX18", "editable": true, "size": 412, "mode": "snippet", "is_external": false, "external_type": "", "is_public": true, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T3P0PGX18-F04PWK0NFU6/swarm_env.env", "url_private_download": "https://files.slack.com/files-pri/T3P0PGX18-F04PWK0NFU6/download/swarm_env.env", "permalink": "https://steeleye.slack.com/files/U01PGFLH47Q/F04PWK0NFU6/swarm_env.env", "permalink_public": "https://slack-files.com/T3P0PGX18-F04PWK0NFU6-7ed2767efe", "edit_link": "https://steeleye.slack.com/files/U01PGFLH47Q/F04PWK0NFU6/swarm_env.env/edit", "has_rich_preview": false, "file_access": "visible"}, {"id": "F04QSTL2EU8", "created": 1676621577, "timestamp": 1676621577, "name": "es__mapping.json", "title": "es__mapping.json", "mimetype": "text/plain", "filetype": "json", "pretty_type": "JSON", "user": "U01PGFLH47Q", "user_team": "T3P0PGX18", "editable": false, "size": 49094297, "mode": "hosted", "is_external": false, "external_type": "", "is_public": true, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T3P0PGX18-F04QSTL2EU8/es__mapping.json", "url_private_download": "https://files.slack.com/files-pri/T3P0PGX18-F04QSTL2EU8/download/es__mapping.json", "media_display_type": "unknown", "permalink": "https://steeleye.slack.com/files/U01PGFLH47Q/F04QSTL2EU8/es__mapping.json", "permalink_public": "https://slack-files.com/T3P0PGX18-F04QSTL2EU8-1e1c9a0707", "has_rich_preview": false, "file_access": "visible"}], "upload": false, "user": "U01PGFLH47Q", "ts": "1676621652.949519", "blocks": [{"type": "rich_text", "block_id": "UUARE", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Edit: one file to be del in next batch"}]}]}], "edited": {"user": "U01PGFLH47Q", "ts": "1676621665.000000"}, "client_msg_id": "535404aa-378c-4820-9d97-1a86f993e1cc", "file_uploads": [{"id": "F04PWK0NFU6", "name": "name", "title": "title", "mimetype": "mimetype", "user": "user", "s3_path": "s3://test.dev.steeleye.co/aries/ingress/depository/slack_chat_poll/random.txt", "filetype": "filetype", "pretty_type": "pretty_type"}, {"id": "F04QSTL2EU8", "name": "name", "title": "title", "mimetype": "mimetype", "user": "user", "s3_path": "s3://test.dev.steeleye.co/aries/ingress/depository/slack_chat_poll/random.txt", "filetype": "filetype", "pretty_type": "pretty_type"}]}, {"type": "message", "text": "edit to be del in next batch", "files": [{"id": "F04Q5LUJWDA", "created": 1676621517, "timestamp": 1676621517, "name": "attachments_only.csv", "title": "attachments_only.csv", "mimetype": "text/csv", "filetype": "csv", "pretty_type": "CSV", "user": "U01PGFLH47Q", "user_team": "T3P0PGX18", "editable": true, "size": 306, "mode": "snippet", "is_external": false, "external_type": "", "is_public": true, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T3P0PGX18-F04Q5LUJWDA/attachments_only.csv", "url_private_download": "https://files.slack.com/files-pri/T3P0PGX18-F04Q5LUJWDA/download/attachments_only.csv", "permalink": "https://steeleye.slack.com/files/U01PGFLH47Q/F04Q5LUJWDA/attachments_only.csv", "permalink_public": "https://slack-files.com/T3P0PGX18-F04Q5LUJWDA-4ecf887a3f", "edit_link": "https://steeleye.slack.com/files/U01PGFLH47Q/F04Q5LUJWDA/attachments_only.csv/edit", "has_rich_preview": false, "file_access": "visible"}], "upload": false, "user": "U01PGFLH47Q", "display_as_bot": false, "ts": "1676621535.732619", "blocks": [{"type": "rich_text", "block_id": "GgG", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "edit to be del in next batch"}]}]}], "edited": {"user": "U01PGFLH47Q", "ts": "1676621548.000000"}, "client_msg_id": "9cdd92b9-0c85-45ce-a534-4a2982cc5dba", "file_uploads": [{"id": "F04Q5LUJWDA", "name": "name", "title": "title", "mimetype": "mimetype", "user": "user", "s3_path": "s3://test.dev.steeleye.co/aries/ingress/depository/slack_chat_poll/random.txt", "filetype": "filetype", "pretty_type": "pretty_type"}]}, {"type": "message", "text": "", "files": [{"id": "F04Q5LQ4N5A", "created": 1676621451, "timestamp": 1676621451, "name": "swarm_env.env", "title": "swarm_env.env", "mimetype": "text/plain", "filetype": "text", "pretty_type": "Plain Text", "user": "U01PGFLH47Q", "user_team": "T3P0PGX18", "editable": true, "size": 546, "mode": "snippet", "is_external": false, "external_type": "", "is_public": true, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T3P0PGX18-F04Q5LQ4N5A/swarm_env.env", "url_private_download": "https://files.slack.com/files-pri/T3P0PGX18-F04Q5LQ4N5A/download/swarm_env.env", "permalink": "https://steeleye.slack.com/files/U01PGFLH47Q/F04Q5LQ4N5A/swarm_env.env", "permalink_public": "https://slack-files.com/T3P0PGX18-F04Q5LQ4N5A-7eb5a35c91", "edit_link": "https://steeleye.slack.com/files/U01PGFLH47Q/F04Q5LQ4N5A/swarm_env.env/edit", "has_rich_preview": false, "file_access": "visible"}, {"id": "F04QFRRQ31P", "created": 1676621467, "timestamp": 1676621467, "name": "SinkFileAudit 2023-02-03T09_16_39.xlsx", "title": "SinkFileAudit 2023-02-03T09_16_39.xlsx", "mimetype": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", "filetype": "xlsx", "pretty_type": "Excel Spreadsheet", "user": "U01PGFLH47Q", "user_team": "T3P0PGX18", "editable": false, "size": 5881, "mode": "hosted", "is_external": false, "external_type": "", "is_public": true, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T3P0PGX18-F04QFRRQ31P/sinkfileaudit_2023-02-03t09_16_39.xlsx", "url_private_download": "https://files.slack.com/files-pri/T3P0PGX18-F04QFRRQ31P/download/sinkfileaudit_2023-02-03t09_16_39.xlsx", "media_display_type": "unknown", "converted_pdf": "https://files.slack.com/files-tmb/T3P0PGX18-F04QFRRQ31P-63b6d62fe5/sinkfileaudit_2023-02-03t09_16_39_converted.pdf", "thumb_pdf": "https://files.slack.com/files-tmb/T3P0PGX18-F04QFRRQ31P-63b6d62fe5/sinkfileaudit_2023-02-03t09_16_39_thumb_pdf.png", "thumb_pdf_w": 909, "thumb_pdf_h": 1286, "permalink": "https://steeleye.slack.com/files/U01PGFLH47Q/F04QFRRQ31P/sinkfileaudit_2023-02-03t09_16_39.xlsx", "permalink_public": "https://slack-files.com/T3P0PGX18-F04QFRRQ31P-3c4a050ecc", "has_rich_preview": false, "file_access": "visible"}], "upload": false, "user": "U01PGFLH47Q", "ts": "1676621488.732089", "client_msg_id": "2dad87a2-a664-4541-9451-3202d9fac5bd", "file_uploads": [{"id": "F04Q5LQ4N5A", "name": "name", "title": "title", "mimetype": "mimetype", "user": "user", "s3_path": "s3://test.dev.steeleye.co/aries/ingress/depository/slack_chat_poll/random.txt", "filetype": "filetype", "pretty_type": "pretty_type"}, {"id": "F04QFRRQ31P", "name": "name", "title": "title", "mimetype": "mimetype", "user": "user", "s3_path": "s3://test.dev.steeleye.co/aries/ingress/depository/slack_chat_poll/random.txt", "filetype": "filetype", "pretty_type": "pretty_type"}]}, {"type": "message", "text": "", "files": [{"id": "F04QFRKT2FK", "created": 1676621380, "timestamp": 1676621380, "name": "attachments_only.csv", "title": "attachments_only.csv", "mimetype": "text/csv", "filetype": "csv", "pretty_type": "CSV", "user": "U01PGFLH47Q", "user_team": "T3P0PGX18", "editable": true, "size": 306, "mode": "snippet", "is_external": false, "external_type": "", "is_public": true, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T3P0PGX18-F04QFRKT2FK/attachments_only.csv", "url_private_download": "https://files.slack.com/files-pri/T3P0PGX18-F04QFRKT2FK/download/attachments_only.csv", "permalink": "https://steeleye.slack.com/files/U01PGFLH47Q/F04QFRKT2FK/attachments_only.csv", "permalink_public": "https://slack-files.com/T3P0PGX18-F04QFRKT2FK-b70458a635", "edit_link": "https://steeleye.slack.com/files/U01PGFLH47Q/F04QFRKT2FK/attachments_only.csv/edit", "has_rich_preview": false, "file_access": "visible"}, {"id": "F04Q08QDJE9", "created": 1676621382, "timestamp": 1676621382, "name": "legacy.csv", "title": "legacy.csv", "mimetype": "text/csv", "filetype": "csv", "pretty_type": "CSV", "user": "U01PGFLH47Q", "user_team": "T3P0PGX18", "editable": true, "size": 306, "mode": "snippet", "is_external": false, "external_type": "", "is_public": true, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T3P0PGX18-F04Q08QDJE9/legacy.csv", "url_private_download": "https://files.slack.com/files-pri/T3P0PGX18-F04Q08QDJE9/download/legacy.csv", "permalink": "https://steeleye.slack.com/files/U01PGFLH47Q/F04Q08QDJE9/legacy.csv", "permalink_public": "https://slack-files.com/T3P0PGX18-F04Q08QDJE9-41ff031fba", "edit_link": "https://steeleye.slack.com/files/U01PGFLH47Q/F04Q08QDJE9/legacy.csv/edit", "has_rich_preview": false, "file_access": "visible"}], "upload": false, "user": "U01PGFLH47Q", "ts": "1676621386.841009", "client_msg_id": "e6d33272-2e35-422d-8e0e-25902d8b33cf", "file_uploads": [{"id": "F04QFRKT2FK", "name": "name", "title": "title", "mimetype": "mimetype", "user": "user", "s3_path": "s3://test.dev.steeleye.co/aries/ingress/depository/slack_chat_poll/random.txt", "filetype": "filetype", "pretty_type": "pretty_type"}, {"id": "F04Q08QDJE9", "name": "name", "title": "title", "mimetype": "mimetype", "user": "user", "s3_path": "s3://test.dev.steeleye.co/aries/ingress/depository/slack_chat_poll/random.txt", "filetype": "filetype", "pretty_type": "pretty_type"}]}, {"type": "message", "text": "Test 4: message with 2 files", "files": [{"id": "F04PNLHK3KR", "created": 1676621359, "timestamp": 1676621359, "name": "flows_result.json", "title": "flows_result.json", "mimetype": "text/plain", "filetype": "json", "pretty_type": "JSON", "user": "U01PGFLH47Q", "user_team": "T3P0PGX18", "editable": true, "size": 7404, "mode": "snippet", "is_external": false, "external_type": "", "is_public": true, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T3P0PGX18-F04PNLHK3KR/flows_result.json", "url_private_download": "https://files.slack.com/files-pri/T3P0PGX18-F04PNLHK3KR/download/flows_result.json", "permalink": "https://steeleye.slack.com/files/U01PGFLH47Q/F04PNLHK3KR/flows_result.json", "permalink_public": "https://slack-files.com/T3P0PGX18-F04PNLHK3KR-fde8a69219", "edit_link": "https://steeleye.slack.com/files/U01PGFLH47Q/F04PNLHK3KR/flows_result.json/edit", "has_rich_preview": false, "file_access": "visible"}, {"id": "F04Q5LJ09JQ", "created": 1676621362, "timestamp": 1676621362, "name": "legacy_result.json", "title": "legacy_result.json", "mimetype": "text/plain", "filetype": "json", "pretty_type": "JSON", "user": "U01PGFLH47Q", "user_team": "T3P0PGX18", "editable": true, "size": 19691, "mode": "snippet", "is_external": false, "external_type": "", "is_public": true, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T3P0PGX18-F04Q5LJ09JQ/legacy_result.json", "url_private_download": "https://files.slack.com/files-pri/T3P0PGX18-F04Q5LJ09JQ/download/legacy_result.json", "permalink": "https://steeleye.slack.com/files/U01PGFLH47Q/F04Q5LJ09JQ/legacy_result.json", "permalink_public": "https://slack-files.com/T3P0PGX18-F04Q5LJ09JQ-c8a242d890", "edit_link": "https://steeleye.slack.com/files/U01PGFLH47Q/F04Q5LJ09JQ/legacy_result.json/edit", "has_rich_preview": false, "file_access": "visible"}], "upload": false, "user": "U01PGFLH47Q", "ts": "1676621365.140039", "blocks": [{"type": "rich_text", "block_id": "cJ7qk", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Test 4: message with 2 files"}]}]}], "client_msg_id": "d7a6ce1f-8f57-4b8c-91e9-727857da1ec0", "file_uploads": [{"id": "F04PNLHK3KR", "name": "name", "title": "title", "mimetype": "mimetype", "user": "user", "s3_path": "s3://test.dev.steeleye.co/aries/ingress/depository/slack_chat_poll/random.txt", "filetype": "filetype", "pretty_type": "pretty_type"}, {"id": "F04Q5LJ09JQ", "name": "name", "title": "title", "mimetype": "mimetype", "user": "user", "s3_path": "s3://test.dev.steeleye.co/aries/ingress/depository/slack_chat_poll/random.txt", "filetype": "filetype", "pretty_type": "pretty_type"}]}, {"type": "message", "text": "Test 3: message with 1 file.", "files": [{"id": "F04QFRF8H5X", "created": 1676621311, "timestamp": 1676621311, "name": "legacy_result.json", "title": "legacy_result.json", "mimetype": "text/plain", "filetype": "json", "pretty_type": "JSON", "user": "U01PGFLH47Q", "user_team": "T3P0PGX18", "editable": true, "size": 19691, "mode": "snippet", "is_external": false, "external_type": "", "is_public": true, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T3P0PGX18-F04QFRF8H5X/legacy_result.json", "url_private_download": "https://files.slack.com/files-pri/T3P0PGX18-F04QFRF8H5X/download/legacy_result.json", "permalink": "https://steeleye.slack.com/files/U01PGFLH47Q/F04QFRF8H5X/legacy_result.json", "permalink_public": "https://slack-files.com/T3P0PGX18-F04QFRF8H5X-0f0fa077bf", "edit_link": "https://steeleye.slack.com/files/U01PGFLH47Q/F04QFRF8H5X/legacy_result.json/edit", "has_rich_preview": false, "file_access": "visible"}], "upload": false, "user": "U01PGFLH47Q", "display_as_bot": false, "ts": "1676621342.574179", "blocks": [{"type": "rich_text", "block_id": "8vTN", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Test 3: message with 1 file."}]}]}], "client_msg_id": "1bed2462-83ec-4a66-bb3a-ccb5625a4ba8", "file_uploads": [{"id": "F04QFRF8H5X", "name": "name", "title": "title", "mimetype": "mimetype", "user": "user", "s3_path": "s3://test.dev.steeleye.co/aries/ingress/depository/slack_chat_poll/random.txt", "filetype": "filetype", "pretty_type": "pretty_type"}]}, {"type": "message", "text": "", "files": [{"id": "F04QST0FWBS", "created": 1676621289, "timestamp": 1676621289, "name": "swarm_env.env", "title": "swarm_env.env", "mimetype": "text/plain", "filetype": "text", "pretty_type": "Plain Text", "user": "U01PGFLH47Q", "user_team": "T3P0PGX18", "editable": true, "size": 394, "mode": "snippet", "is_external": false, "external_type": "", "is_public": true, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T3P0PGX18-F04QST0FWBS/swarm_env.env", "url_private_download": "https://files.slack.com/files-pri/T3P0PGX18-F04QST0FWBS/download/swarm_env.env", "permalink": "https://steeleye.slack.com/files/U01PGFLH47Q/F04QST0FWBS/swarm_env.env", "permalink_public": "https://slack-files.com/T3P0PGX18-F04QST0FWBS-70fc002a51", "edit_link": "https://steeleye.slack.com/files/U01PGFLH47Q/F04QST0FWBS/swarm_env.env/edit", "has_rich_preview": false, "file_access": "visible"}], "upload": false, "user": "U01PGFLH47Q", "display_as_bot": false, "ts": "1676621292.922279", "client_msg_id": "15b5464f-0d4d-4983-b0b8-d397a2992701", "file_uploads": [{"id": "F04QST0FWBS", "name": "name", "title": "title", "mimetype": "mimetype", "user": "user", "s3_path": "s3://test.dev.steeleye.co/aries/ingress/depository/slack_chat_poll/random.txt", "filetype": "filetype", "pretty_type": "pretty_type"}]}, {"type": "message", "text": "", "files": [{"id": "F04PNLAPSH5", "created": 1676621258, "timestamp": 1676621258, "name": "countries.json", "title": "countries.json", "mimetype": "text/plain", "filetype": "json", "pretty_type": "JSON", "user": "U01PGFLH47Q", "user_team": "T3P0PGX18", "editable": true, "size": 4615, "mode": "snippet", "is_external": false, "external_type": "", "is_public": true, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T3P0PGX18-F04PNLAPSH5/countries.json", "url_private_download": "https://files.slack.com/files-pri/T3P0PGX18-F04PNLAPSH5/download/countries.json", "permalink": "https://steeleye.slack.com/files/U01PGFLH47Q/F04PNLAPSH5/countries.json", "permalink_public": "https://slack-files.com/T3P0PGX18-F04PNLAPSH5-e77a45311c", "edit_link": "https://steeleye.slack.com/files/U01PGFLH47Q/F04PNLAPSH5/countries.json/edit", "has_rich_preview": false, "file_access": "visible"}], "upload": false, "user": "U01PGFLH47Q", "display_as_bot": false, "ts": "1676621267.607259", "client_msg_id": "faf56e75-b585-4a38-9081-ef267691ee72", "file_uploads": [{"id": "F04PNLAPSH5", "name": "name", "title": "title", "mimetype": "mimetype", "user": "user", "s3_path": "s3://test.dev.steeleye.co/aries/ingress/depository/slack_chat_poll/random.txt", "filetype": "filetype", "pretty_type": "pretty_type"}]}, {"client_msg_id": "d96e69e1-52c1-4085-ac74-43d5545c633a", "type": "message", "text": "Following tests are for files deleted _later, in a new batch._", "user": "U01PGFLH47Q", "ts": "1676621231.971219", "blocks": [{"type": "rich_text", "block_id": "UEsm", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Following tests are for files deleted "}, {"type": "text", "text": "later, in a new batch.", "style": {"italic": true}}]}]}], "team": "T3P0PGX18"}, {"type": "message", "text": "Test with text: delete 1 of 2 files.", "files": [{"id": "F04Q35UKJSE", "created": 1676620903, "timestamp": 1676620903, "name": "1629308446_8751035.csv", "title": "1629308446_8751035.csv", "mimetype": "text/csv", "filetype": "csv", "pretty_type": "CSV", "user": "U01PGFLH47Q", "user_team": "T3P0PGX18", "editable": true, "size": 1276, "mode": "snippet", "is_external": false, "external_type": "", "is_public": true, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T3P0PGX18-F04Q35UKJSE/1629308446_8751035.csv", "url_private_download": "https://files.slack.com/files-pri/T3P0PGX18-F04Q35UKJSE/download/1629308446_8751035.csv", "permalink": "https://steeleye.slack.com/files/U01PGFLH47Q/F04Q35UKJSE/1629308446_8751035.csv", "permalink_public": "https://slack-files.com/T3P0PGX18-F04Q35UKJSE-a1e46995c7", "edit_link": "https://steeleye.slack.com/files/U01PGFLH47Q/F04Q35UKJSE/1629308446_8751035.csv/edit", "has_rich_preview": false, "file_access": "visible"}, {"id": "F04Q32HRK9R", "mode": "tombstone"}], "upload": false, "user": "U01PGFLH47Q", "ts": "1676620962.008659", "blocks": [{"type": "rich_text", "block_id": "Upvhd", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Test with text: delete 1 of 2 files."}]}]}], "client_msg_id": "6a79998c-d4a1-4f27-9882-00f9b2072fd3", "file_uploads": [{"id": "F04Q35UKJSE", "name": "name", "title": "title", "mimetype": "mimetype", "user": "user", "s3_path": "s3://test.dev.steeleye.co/aries/ingress/depository/slack_chat_poll/random.txt", "filetype": "filetype", "pretty_type": "pretty_type"}]}, {"type": "message", "text": "", "files": [{"id": "F04Q07J2ECD", "created": 1676620828, "timestamp": 1676620828, "name": "market_input_1.csv", "title": "market_input_1.csv", "mimetype": "text/csv", "filetype": "csv", "pretty_type": "CSV", "user": "U01PGFLH47Q", "user_team": "T3P0PGX18", "editable": true, "size": 220, "mode": "snippet", "is_external": false, "external_type": "", "is_public": true, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T3P0PGX18-F04Q07J2ECD/market_input_1.csv", "url_private_download": "https://files.slack.com/files-pri/T3P0PGX18-F04Q07J2ECD/download/market_input_1.csv", "permalink": "https://steeleye.slack.com/files/U01PGFLH47Q/F04Q07J2ECD/market_input_1.csv", "permalink_public": "https://slack-files.com/T3P0PGX18-F04Q07J2ECD-4b626aa7d3", "edit_link": "https://steeleye.slack.com/files/U01PGFLH47Q/F04Q07J2ECD/market_input_1.csv/edit", "has_rich_preview": false, "file_access": "visible"}, {"id": "F04Q07K2N93", "mode": "tombstone"}, {"id": "F04PWHJBTU6", "mode": "tombstone"}], "upload": false, "user": "U01PGFLH47Q", "ts": "1676620858.201069", "client_msg_id": "2da5363a-05ad-40a2-97d6-e979fb551262", "file_uploads": [{"id": "F04Q07J2ECD", "name": "name", "title": "title", "mimetype": "mimetype", "user": "user", "s3_path": "s3://test.dev.steeleye.co/aries/ingress/depository/slack_chat_poll/random.txt", "filetype": "filetype", "pretty_type": "pretty_type"}]}, {"type": "message", "text": "Test 10: edit and add message, delete 1 of 2 files", "files": [{"id": "F04QSRB8RPS", "created": 1676620533, "timestamp": 1676620533, "name": "test_filter_columns.py", "title": "test_filter_columns.py", "mimetype": "text/plain", "filetype": "python", "pretty_type": "Python", "user": "U01PGFLH47Q", "user_team": "T3P0PGX18", "editable": true, "size": 3055, "mode": "snippet", "is_external": false, "external_type": "", "is_public": true, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T3P0PGX18-F04QSRB8RPS/test_filter_columns.py", "url_private_download": "https://files.slack.com/files-pri/T3P0PGX18-F04QSRB8RPS/download/test_filter_columns.py", "permalink": "https://steeleye.slack.com/files/U01PGFLH47Q/F04QSRB8RPS/test_filter_columns.py", "permalink_public": "https://slack-files.com/T3P0PGX18-F04QSRB8RPS-4bd9544bb0", "edit_link": "https://steeleye.slack.com/files/U01PGFLH47Q/F04QSRB8RPS/test_filter_columns.py/edit", "has_rich_preview": false, "file_access": "visible"}, {"id": "F04PNJPU807", "mode": "tombstone"}], "upload": false, "user": "U01PGFLH47Q", "ts": "1676620559.152819", "blocks": [{"type": "rich_text", "block_id": "PU4Am", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Test 10: edit and add message, delete 1 of 2 files"}]}]}], "edited": {"user": "U01PGFLH47Q", "ts": "1676620573.000000"}, "client_msg_id": "4c2e2933-ec7e-489c-946b-2b5d4d70206e", "file_uploads": [{"id": "F04QSRB8RPS", "name": "name", "title": "title", "mimetype": "mimetype", "user": "user", "s3_path": "s3://test.dev.steeleye.co/aries/ingress/depository/slack_chat_poll/random.txt", "filetype": "filetype", "pretty_type": "pretty_type"}]}, {"client_msg_id": "480f0c94-45b2-4b14-9974-2cde2c8131db", "type": "message", "text": "Test `replying to old threads`.", "user": "U035GAPLD0W", "ts": "1676620529.664789", "blocks": [{"type": "rich_text", "block_id": "bTI", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Test "}, {"type": "text", "text": "replying to old threads", "style": {"code": true}}, {"type": "text", "text": "."}]}]}], "team": "T3P0PGX18", "thread_ts": "1670917144.097999", "parent_user_id": "U01PGFLH47Q"}, {"client_msg_id": "fc701674-cdf0-4582-b652-9de3f93ec51e", "type": "message", "text": "Test `replying to old threads`.", "user": "U035GAPLD0W", "ts": "1676620521.894609", "blocks": [{"type": "rich_text", "block_id": "z1soB", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Test "}, {"type": "text", "text": "replying to old threads", "style": {"code": true}}, {"type": "text", "text": "."}]}]}], "team": "T3P0PGX18", "thread_ts": "1671630411.416359", "parent_user_id": "U01PGFLH47Q"}, {"client_msg_id": "1b829041-1003-42ee-8d80-a05b88cdfe2f", "type": "message", "text": "Test `replying to old threads`.", "user": "U035GAPLD0W", "ts": "1676620513.287129", "blocks": [{"type": "rich_text", "block_id": "9bi", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Test "}, {"type": "text", "text": "replying to old threads", "style": {"code": true}}, {"type": "text", "text": "."}]}]}], "team": "T3P0PGX18", "thread_ts": "1674457393.454269", "parent_user_id": "U011XA81ZSA"}, {"client_msg_id": "1b4515ea-aa64-473d-b22a-492cb078b4e6", "type": "message", "text": "Test `replying to old threads`.", "user": "U035GAPLD0W", "ts": "1676620502.376899", "blocks": [{"type": "rich_text", "block_id": "JEUN", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Test "}, {"type": "text", "text": "replying to old threads", "style": {"code": true}}, {"type": "text", "text": "."}]}]}], "team": "T3P0PGX18", "thread_ts": "1676282749.569729", "parent_user_id": "U01PGFLH47Q"}, {"client_msg_id": "2e24bef0-3f8e-404f-887e-d728e92b46a3", "type": "message", "text": "Test `replying to old threads`.", "user": "U035GAPLD0W", "ts": "1676620483.554539", "blocks": [{"type": "rich_text", "block_id": "18c", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Test "}, {"type": "text", "text": "replying to old threads", "style": {"code": true}}, {"type": "text", "text": "."}]}]}], "team": "T3P0PGX18", "thread_ts": "1674468508.826429", "parent_user_id": "U4PA3EVJT"}, {"type": "message", "text": "Test 8: edit and add message, delete only file.", "files": [{"id": "F04Q5JDFUDS", "mode": "tombstone"}], "upload": false, "user": "U01PGFLH47Q", "display_as_bot": false, "ts": "1676620395.813469", "blocks": [{"type": "rich_text", "block_id": "XCIX/", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Test 8: edit and add message, delete only file."}]}]}], "edited": {"user": "U01PGFLH47Q", "ts": "1676620411.000000"}, "client_msg_id": "31cd4505-5397-4c89-ad50-d1385116da69"}, {"client_msg_id": "ccf54f43-f53e-4387-9e43-b5c123b5c768", "type": "message", "text": "Files in same batch tests", "user": "U01PGFLH47Q", "ts": "1676620243.933179", "blocks": [{"type": "rich_text", "block_id": "GMu", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Files in same batch tests"}]}]}], "team": "T3P0PGX18"}], "members": ["U4PA3EVJT", "U011XA81ZSA", "U01PGFLH47Q", "U027KV4NYP3", "U02AMERNUFM", "U030FLHR2K1", "U030PK30L9L", "U035GAPLD0W"], "info": {"id": "C04DX1D9JLX", "name": "slack-test", "created": 1670503770, "member_count": 8, "is_general": false, "is_private": false, "is_im": false, "is_mpim": false, "is_file": false, "is_deleted": false, "is_archived": false, "creator": "U01PGFLH47Q", "is_moved": false, "is_shared": false, "name_normalized": "slack-test", "is_global_shared": false, "is_org_shared": false, "is_org_mandatory": false, "is_org_default": false, "previous_names": [], "purpose": {"text": "", "set_by": "", "date_set": 0}, "topic": {"text": "", "set_by": "", "date_set": 0}, "retention": {"type": "default", "duration": 0}, "shared": {"shared_team_ids": [], "connected_team_ids": [], "internal_team_ids": [], "connected_limited_team_ids": []}, "has_guests": false}, "edits": [{"type": "message", "user": "U01PGFLH47Q", "upload": false, "ts": "1676622541.000000", "text": "", "previous": {"text": "This is another test for deleting the entire message when there are multiple files.", "files": [{"id": "F04Q3451CUB", "created": 1676621727, "timestamp": 1676621727, "name": "SteelEye_Test_1.pst", "title": "SteelEye_Test_1.pst", "mimetype": "application/octet-stream", "filetype": "binary", "pretty_type": "Binary", "user": "U01PGFLH47Q", "user_team": "T3P0PGX18", "editable": false, "size": 567092224, "mode": "hosted", "is_external": false, "external_type": "", "is_public": true, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T3P0PGX18-F04Q3451CUB/steeleye_test_1.pst", "url_private_download": "https://files.slack.com/files-pri/T3P0PGX18-F04Q3451CUB/download/steeleye_test_1.pst", "media_display_type": "unknown", "permalink": "https://steeleye.slack.com/files/U01PGFLH47Q/F04Q3451CUB/steeleye_test_1.pst", "permalink_public": "https://slack-files.com/T3P0PGX18-F04Q3451CUB-f8f77aaa89", "has_rich_preview": false, "file_access": "visible"}, {"id": "F04Q0B01E4D", "created": 1676622498, "timestamp": 1676622498, "name": "comms_env.yaml", "title": "comms_env.yaml", "mimetype": "text/plain", "filetype": "yaml", "pretty_type": "YAML", "user": "U01PGFLH47Q", "user_team": "T3P0PGX18", "editable": true, "size": 1242, "mode": "snippet", "is_external": false, "external_type": "", "is_public": true, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T3P0PGX18-F04Q0B01E4D/comms_env.yaml", "url_private_download": "https://files.slack.com/files-pri/T3P0PGX18-F04Q0B01E4D/download/comms_env.yaml", "permalink": "https://steeleye.slack.com/files/U01PGFLH47Q/F04Q0B01E4D/comms_env.yaml", "permalink_public": "https://slack-files.com/T3P0PGX18-F04Q0B01E4D-29efd7003c", "edit_link": "https://steeleye.slack.com/files/U01PGFLH47Q/F04Q0B01E4D/comms_env.yaml/edit", "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "B8vk2", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "This is another test for deleting the entire message when there are multiple files."}]}]}]}, "original_ts": "1676622500.021309", "subtype": "message_deleted", "editor_id": "U01PGFLH47Q", "file": {"id": "F04Q3451CUB"}}, {"type": "message", "user": "U01PGFLH47Q", "upload": false, "ts": "1676621665.000000", "text": "Edit: one file to be del in next batch", "previous": {"text": "", "files": [{"id": "F04PWK0NFU6", "created": 1676621567, "timestamp": 1676621567, "name": "swarm_env.env", "title": "swarm_env.env", "mimetype": "text/plain", "filetype": "text", "pretty_type": "Plain Text", "user": "U01PGFLH47Q", "user_team": "T3P0PGX18", "editable": true, "size": 412, "mode": "snippet", "is_external": false, "external_type": "", "is_public": true, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T3P0PGX18-F04PWK0NFU6/swarm_env.env", "url_private_download": "https://files.slack.com/files-pri/T3P0PGX18-F04PWK0NFU6/download/swarm_env.env", "permalink": "https://steeleye.slack.com/files/U01PGFLH47Q/F04PWK0NFU6/swarm_env.env", "permalink_public": "https://slack-files.com/T3P0PGX18-F04PWK0NFU6-7ed2767efe", "edit_link": "https://steeleye.slack.com/files/U01PGFLH47Q/F04PWK0NFU6/swarm_env.env/edit", "has_rich_preview": false, "file_access": "visible"}, {"id": "F04QSTL2EU8", "created": 1676621577, "timestamp": 1676621577, "name": "es__mapping.json", "title": "es__mapping.json", "mimetype": "text/plain", "filetype": "json", "pretty_type": "JSON", "user": "U01PGFLH47Q", "user_team": "T3P0PGX18", "editable": false, "size": 49094297, "mode": "hosted", "is_external": false, "external_type": "", "is_public": true, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T3P0PGX18-F04QSTL2EU8/es__mapping.json", "url_private_download": "https://files.slack.com/files-pri/T3P0PGX18-F04QSTL2EU8/download/es__mapping.json", "media_display_type": "unknown", "permalink": "https://steeleye.slack.com/files/U01PGFLH47Q/F04QSTL2EU8/es__mapping.json", "permalink_public": "https://slack-files.com/T3P0PGX18-F04QSTL2EU8-1e1c9a0707", "has_rich_preview": false, "file_access": "visible"}]}, "original_ts": "1676621652.949519", "subtype": "message_changed", "editor_id": "U01PGFLH47Q", "blocks": [{"type": "rich_text", "block_id": "UUARE", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Edit: one file to be del in next batch"}]}]}]}, {"type": "message", "user": "U01PGFLH47Q", "upload": false, "ts": "1676621548.000000", "text": "edit to be del in next batch", "previous": {"text": "", "files": [{"id": "F04Q5LUJWDA", "created": 1676621517, "timestamp": 1676621517, "name": "attachments_only.csv", "title": "attachments_only.csv", "mimetype": "text/csv", "filetype": "csv", "pretty_type": "CSV", "user": "U01PGFLH47Q", "user_team": "T3P0PGX18", "editable": true, "size": 306, "mode": "snippet", "is_external": false, "external_type": "", "is_public": true, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T3P0PGX18-F04Q5LUJWDA/attachments_only.csv", "url_private_download": "https://files.slack.com/files-pri/T3P0PGX18-F04Q5LUJWDA/download/attachments_only.csv", "permalink": "https://steeleye.slack.com/files/U01PGFLH47Q/F04Q5LUJWDA/attachments_only.csv", "permalink_public": "https://slack-files.com/T3P0PGX18-F04Q5LUJWDA-4ecf887a3f", "edit_link": "https://steeleye.slack.com/files/U01PGFLH47Q/F04Q5LUJWDA/attachments_only.csv/edit", "has_rich_preview": false, "file_access": "visible"}]}, "original_ts": "1676621535.732619", "subtype": "message_changed", "editor_id": "U01PGFLH47Q", "blocks": [{"type": "rich_text", "block_id": "GgG", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "edit to be del in next batch"}]}]}]}, {"type": "message", "user": "U01PGFLH47Q", "upload": false, "ts": "1676621110.000000", "text": "", "previous": {"text": "", "files": [{"id": "F04Q32TMSLB", "mode": "tombstone"}, {"id": "F04QSSMH91N", "mode": "tombstone"}]}, "original_ts": "1676621102.606069", "subtype": "message_deleted", "editor_id": "U00", "file": {"id": "F04Q32TMSLB"}}, {"type": "message", "user": "U01PGFLH47Q", "upload": false, "ts": "1676621105.000000", "text": "", "previous": {"text": "", "files": [{"id": "F04Q32TMSLB", "mode": "tombstone"}, {"id": "F04QSSMH91N", "mode": "tombstone"}]}, "original_ts": "1676621102.606069", "subtype": "message_changed", "editor_id": "U00"}, {"type": "message", "user": "U01PGFLH47Q", "upload": false, "ts": "1676621053.000000", "text": "", "previous": {"text": "", "files": [{"id": "F04Q5KT8NLU", "created": 1676621006, "timestamp": 1676621006, "name": "mkdocs.yml", "title": "mkdocs.yml", "mimetype": "text/plain", "filetype": "yaml", "pretty_type": "YAML", "user": "U01PGFLH47Q", "user_team": "T3P0PGX18", "editable": true, "size": 1268, "mode": "snippet", "is_external": false, "external_type": "", "is_public": true, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T3P0PGX18-F04Q5KT8NLU/mkdocs.yml", "url_private_download": "https://files.slack.com/files-pri/T3P0PGX18-F04Q5KT8NLU/download/mkdocs.yml", "permalink": "https://steeleye.slack.com/files/U01PGFLH47Q/F04Q5KT8NLU/mkdocs.yml", "permalink_public": "https://slack-files.com/T3P0PGX18-F04Q5KT8NLU-097f1959e8", "edit_link": "https://steeleye.slack.com/files/U01PGFLH47Q/F04Q5KT8NLU/mkdocs.yml/edit", "has_rich_preview": false, "file_access": "visible"}, {"id": "F04Q07ZHZCM", "created": 1676621023, "timestamp": 1676621023, "name": "README.md", "title": "README.md", "mimetype": "text/plain", "filetype": "markdown", "pretty_type": "Markdown (raw)", "user": "U01PGFLH47Q", "user_team": "T3P0PGX18", "editable": true, "size": 99, "mode": "snippet", "is_external": false, "external_type": "", "is_public": true, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T3P0PGX18-F04Q07ZHZCM/readme.md", "url_private_download": "https://files.slack.com/files-pri/T3P0PGX18-F04Q07ZHZCM/download/readme.md", "permalink": "https://steeleye.slack.com/files/U01PGFLH47Q/F04Q07ZHZCM/readme.md", "permalink_public": "https://slack-files.com/T3P0PGX18-F04Q07ZHZCM-c2e09a154f", "edit_link": "https://steeleye.slack.com/files/U01PGFLH47Q/F04Q07ZHZCM/readme.md/edit", "has_rich_preview": false, "file_access": "visible"}]}, "original_ts": "1676621050.133199", "subtype": "message_deleted", "editor_id": "U01PGFLH47Q", "file": {"id": "F04Q5KT8NLU"}}, {"type": "message", "user": "U01PGFLH47Q", "upload": false, "ts": "1676620968.000000", "text": "Test with text: delete 1 of 2 files.", "previous": {"text": "Test with text: delete 1 of 2 files.", "files": [{"id": "F04Q35UKJSE", "created": 1676620903, "timestamp": 1676620903, "name": "1629308446_8751035.csv", "title": "1629308446_8751035.csv", "mimetype": "text/csv", "filetype": "csv", "pretty_type": "CSV", "user": "U01PGFLH47Q", "user_team": "T3P0PGX18", "editable": true, "size": 1276, "mode": "snippet", "is_external": false, "external_type": "", "is_public": true, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T3P0PGX18-F04Q35UKJSE/1629308446_8751035.csv", "url_private_download": "https://files.slack.com/files-pri/T3P0PGX18-F04Q35UKJSE/download/1629308446_8751035.csv", "permalink": "https://steeleye.slack.com/files/U01PGFLH47Q/F04Q35UKJSE/1629308446_8751035.csv", "permalink_public": "https://slack-files.com/T3P0PGX18-F04Q35UKJSE-a1e46995c7", "edit_link": "https://steeleye.slack.com/files/U01PGFLH47Q/F04Q35UKJSE/1629308446_8751035.csv/edit", "has_rich_preview": false, "file_access": "visible"}, {"id": "F04Q32HRK9R", "mode": "tombstone"}], "blocks": [{"type": "rich_text", "block_id": "Upvhd", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Test with text: delete 1 of 2 files."}]}]}]}, "original_ts": "1676620962.008659", "subtype": "message_changed", "editor_id": "U00", "blocks": [{"type": "rich_text", "block_id": "Upvhd", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Test with text: delete 1 of 2 files."}]}]}]}, {"type": "message", "user": "U01PGFLH47Q", "upload": false, "ts": "1676620889.000000", "text": "", "previous": {"text": "", "files": [{"id": "F04Q07J2ECD", "created": 1676620828, "timestamp": 1676620828, "name": "market_input_1.csv", "title": "market_input_1.csv", "mimetype": "text/csv", "filetype": "csv", "pretty_type": "CSV", "user": "U01PGFLH47Q", "user_team": "T3P0PGX18", "editable": true, "size": 220, "mode": "snippet", "is_external": false, "external_type": "", "is_public": true, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T3P0PGX18-F04Q07J2ECD/market_input_1.csv", "url_private_download": "https://files.slack.com/files-pri/T3P0PGX18-F04Q07J2ECD/download/market_input_1.csv", "permalink": "https://steeleye.slack.com/files/U01PGFLH47Q/F04Q07J2ECD/market_input_1.csv", "permalink_public": "https://slack-files.com/T3P0PGX18-F04Q07J2ECD-4b626aa7d3", "edit_link": "https://steeleye.slack.com/files/U01PGFLH47Q/F04Q07J2ECD/market_input_1.csv/edit", "has_rich_preview": false, "file_access": "visible"}, {"id": "F04Q07K2N93", "mode": "tombstone"}, {"id": "F04PWHJBTU6", "mode": "tombstone"}]}, "original_ts": "1676620858.201069", "subtype": "message_changed", "editor_id": "U00"}, {"type": "message", "user": "U01PGFLH47Q", "upload": false, "ts": "1676620863.000000", "text": "", "previous": {"text": "", "files": [{"id": "F04Q07J2ECD", "created": 1676620828, "timestamp": 1676620828, "name": "market_input_1.csv", "title": "market_input_1.csv", "mimetype": "text/csv", "filetype": "csv", "pretty_type": "CSV", "user": "U01PGFLH47Q", "user_team": "T3P0PGX18", "editable": true, "size": 220, "mode": "snippet", "is_external": false, "external_type": "", "is_public": true, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T3P0PGX18-F04Q07J2ECD/market_input_1.csv", "url_private_download": "https://files.slack.com/files-pri/T3P0PGX18-F04Q07J2ECD/download/market_input_1.csv", "permalink": "https://steeleye.slack.com/files/U01PGFLH47Q/F04Q07J2ECD/market_input_1.csv", "permalink_public": "https://slack-files.com/T3P0PGX18-F04Q07J2ECD-4b626aa7d3", "edit_link": "https://steeleye.slack.com/files/U01PGFLH47Q/F04Q07J2ECD/market_input_1.csv/edit", "has_rich_preview": false, "file_access": "visible"}, {"id": "F04Q07K2N93", "mode": "tombstone"}, {"id": "F04PWHJBTU6", "mode": "tombstone"}]}, "original_ts": "1676620858.201069", "subtype": "message_changed", "editor_id": "U00"}, {"type": "message", "user": "U01PGFLH47Q", "upload": false, "ts": "1676620786.000000", "text": "", "previous": {"text": "", "files": [{"id": "F04Q5K3NP1S", "mode": "tombstone"}]}, "original_ts": "1676620781.883349", "subtype": "message_deleted", "editor_id": "U00", "file": {"id": "F04Q5K3NP1S"}}, {"type": "message", "user": "U01PGFLH47Q", "upload": false, "ts": "1676620714.000000", "text": "", "previous": {"text": "", "files": [{"id": "F04PNK0BC07", "created": 1676620704, "timestamp": 1676620704, "name": "swarm_env.env", "title": "swarm_env.env", "mimetype": "text/plain", "filetype": "text", "pretty_type": "Plain Text", "user": "U01PGFLH47Q", "user_team": "T3P0PGX18", "editable": true, "size": 407, "mode": "snippet", "is_external": false, "external_type": "", "is_public": true, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T3P0PGX18-F04PNK0BC07/swarm_env.env", "url_private_download": "https://files.slack.com/files-pri/T3P0PGX18-F04PNK0BC07/download/swarm_env.env", "permalink": "https://steeleye.slack.com/files/U01PGFLH47Q/F04PNK0BC07/swarm_env.env", "permalink_public": "https://slack-files.com/T3P0PGX18-F04PNK0BC07-a6c3e1708c", "edit_link": "https://steeleye.slack.com/files/U01PGFLH47Q/F04PNK0BC07/swarm_env.env/edit", "has_rich_preview": false, "file_access": "visible"}]}, "original_ts": "1676620710.787859", "subtype": "message_deleted", "editor_id": "U01PGFLH47Q", "file": {"id": "F04PNK0BC07"}}, {"type": "message", "user": "U01PGFLH47Q", "upload": false, "ts": "1676620682.000000", "text": "", "previous": {"text": "2 file test \u2014 delete entire message", "files": [{"id": "F04QSRJ2TJ4", "created": 1676620642, "timestamp": 1676620642, "name": "2filetest.csv", "title": "2filetest.csv", "mimetype": "text/csv", "filetype": "csv", "pretty_type": "CSV", "user": "U01PGFLH47Q", "user_team": "T3P0PGX18", "editable": true, "size": 594, "mode": "snippet", "is_external": false, "external_type": "", "is_public": true, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T3P0PGX18-F04QSRJ2TJ4/2filetest.csv", "url_private_download": "https://files.slack.com/files-pri/T3P0PGX18-F04QSRJ2TJ4/download/2filetest.csv", "permalink": "https://steeleye.slack.com/files/U01PGFLH47Q/F04QSRJ2TJ4/2filetest.csv", "permalink_public": "https://slack-files.com/T3P0PGX18-F04QSRJ2TJ4-b3aecb3d0b", "edit_link": "https://steeleye.slack.com/files/U01PGFLH47Q/F04QSRJ2TJ4/2filetest.csv/edit", "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "h6K", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "2 file test \u2014 delete entire message"}]}]}]}, "original_ts": "1676620658.397269", "subtype": "message_deleted", "editor_id": "U01PGFLH47Q", "file": {"id": "F04QSRJ2TJ4"}}, {"type": "message", "user": "U01PGFLH47Q", "upload": false, "ts": "1676620578.000000", "text": "Test 10: edit and add message, delete 1 of 2 files", "previous": {"text": "Test 10: edit and add message, delete 1 of 2 files", "files": [{"id": "F04QSRB8RPS", "created": 1676620533, "timestamp": 1676620533, "name": "test_filter_columns.py", "title": "test_filter_columns.py", "mimetype": "text/plain", "filetype": "python", "pretty_type": "Python", "user": "U01PGFLH47Q", "user_team": "T3P0PGX18", "editable": true, "size": 3055, "mode": "snippet", "is_external": false, "external_type": "", "is_public": true, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T3P0PGX18-F04QSRB8RPS/test_filter_columns.py", "url_private_download": "https://files.slack.com/files-pri/T3P0PGX18-F04QSRB8RPS/download/test_filter_columns.py", "permalink": "https://steeleye.slack.com/files/U01PGFLH47Q/F04QSRB8RPS/test_filter_columns.py", "permalink_public": "https://slack-files.com/T3P0PGX18-F04QSRB8RPS-4bd9544bb0", "edit_link": "https://steeleye.slack.com/files/U01PGFLH47Q/F04QSRB8RPS/test_filter_columns.py/edit", "has_rich_preview": false, "file_access": "visible"}, {"id": "F04PNJPU807", "mode": "tombstone"}], "blocks": [{"type": "rich_text", "block_id": "PU4Am", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Test 10: edit and add message, delete 1 of 2 files"}]}]}]}, "original_ts": "1676620559.152819", "subtype": "message_changed", "editor_id": "U00", "blocks": [{"type": "rich_text", "block_id": "PU4Am", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Test 10: edit and add message, delete 1 of 2 files"}]}]}]}, {"type": "message", "user": "U01PGFLH47Q", "upload": false, "ts": "1676620573.000000", "text": "Test 10: edit and add message, delete 1 of 2 files", "previous": {"text": "", "files": [{"id": "F04QSRB8RPS", "created": 1676620533, "timestamp": 1676620533, "name": "test_filter_columns.py", "title": "test_filter_columns.py", "mimetype": "text/plain", "filetype": "python", "pretty_type": "Python", "user": "U01PGFLH47Q", "user_team": "T3P0PGX18", "editable": true, "size": 3055, "mode": "snippet", "is_external": false, "external_type": "", "is_public": true, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T3P0PGX18-F04QSRB8RPS/test_filter_columns.py", "url_private_download": "https://files.slack.com/files-pri/T3P0PGX18-F04QSRB8RPS/download/test_filter_columns.py", "permalink": "https://steeleye.slack.com/files/U01PGFLH47Q/F04QSRB8RPS/test_filter_columns.py", "permalink_public": "https://slack-files.com/T3P0PGX18-F04QSRB8RPS-4bd9544bb0", "edit_link": "https://steeleye.slack.com/files/U01PGFLH47Q/F04QSRB8RPS/test_filter_columns.py/edit", "has_rich_preview": false, "file_access": "visible"}, {"id": "F04PNJPU807", "mode": "tombstone"}]}, "original_ts": "1676620559.152819", "subtype": "message_changed", "editor_id": "U01PGFLH47Q", "blocks": [{"type": "rich_text", "block_id": "PU4Am", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Test 10: edit and add message, delete 1 of 2 files"}]}]}]}, {"type": "message", "user": "U030PK30L9L", "upload": false, "ts": "1676620527.000000", "text": "", "previous": {"text": "Test `replying to old threads`  and deleted", "blocks": [{"type": "rich_text", "block_id": "h8s", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Test "}, {"type": "text", "text": "replying to old threads", "style": {"code": true}}, {"type": "text", "text": "  and deleted"}]}]}]}, "original_ts": "1676620521.912239", "subtype": "message_deleted", "editor_id": "U030PK30L9L"}, {"type": "message", "user": "U01PGFLH47Q", "upload": false, "ts": "1676620521.000000", "text": "", "previous": {"text": "Test 9: edit and add message, delete all 3 files.", "files": [{"id": "F04Q06PKDMK", "created": 1676620450, "timestamp": 1676620450, "name": "SteelEye_Conv-07022023.xlsx", "title": "SteelEye_Conv-07022023.xlsx", "mimetype": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", "filetype": "xlsx", "pretty_type": "Excel Spreadsheet", "user": "U01PGFLH47Q", "user_team": "T3P0PGX18", "editable": false, "size": 88034, "mode": "hosted", "is_external": false, "external_type": "", "is_public": true, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T3P0PGX18-F04Q06PKDMK/steeleye_conv-07022023.xlsx", "url_private_download": "https://files.slack.com/files-pri/T3P0PGX18-F04Q06PKDMK/download/steeleye_conv-07022023.xlsx", "media_display_type": "unknown", "converted_pdf": "https://files.slack.com/files-tmb/T3P0PGX18-F04Q06PKDMK-67dc7a1d36/steeleye_conv-07022023_converted.pdf", "thumb_pdf": "https://files.slack.com/files-tmb/T3P0PGX18-F04Q06PKDMK-67dc7a1d36/steeleye_conv-07022023_thumb_pdf.png", "thumb_pdf_w": 909, "thumb_pdf_h": 1286, "permalink": "https://steeleye.slack.com/files/U01PGFLH47Q/F04Q06PKDMK/steeleye_conv-07022023.xlsx", "permalink_public": "https://slack-files.com/T3P0PGX18-F04Q06PKDMK-5470072190", "has_rich_preview": false, "file_access": "visible"}, {"id": "F04Q31ENQ8K", "created": 1676620455, "timestamp": 1676620455, "name": "Conductor_orchestration.png", "title": "Conductor_orchestration.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U01PGFLH47Q", "user_team": "T3P0PGX18", "editable": false, "size": 193187, "mode": "hosted", "is_external": false, "external_type": "", "is_public": true, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T3P0PGX18-F04Q31ENQ8K/conductor_orchestration.png", "url_private_download": "https://files.slack.com/files-pri/T3P0PGX18-F04Q31ENQ8K/download/conductor_orchestration.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T3P0PGX18-F04Q31ENQ8K-497190ca9c/conductor_orchestration_64.png", "thumb_80": "https://files.slack.com/files-tmb/T3P0PGX18-F04Q31ENQ8K-497190ca9c/conductor_orchestration_80.png", "thumb_360": "https://files.slack.com/files-tmb/T3P0PGX18-F04Q31ENQ8K-497190ca9c/conductor_orchestration_360.png", "thumb_360_w": 360, "thumb_360_h": 186, "thumb_480": "https://files.slack.com/files-tmb/T3P0PGX18-F04Q31ENQ8K-497190ca9c/conductor_orchestration_480.png", "thumb_480_w": 480, "thumb_480_h": 248, "thumb_160": "https://files.slack.com/files-tmb/T3P0PGX18-F04Q31ENQ8K-497190ca9c/conductor_orchestration_160.png", "thumb_720": "https://files.slack.com/files-tmb/T3P0PGX18-F04Q31ENQ8K-497190ca9c/conductor_orchestration_720.png", "thumb_720_w": 720, "thumb_720_h": 371, "thumb_800": "https://files.slack.com/files-tmb/T3P0PGX18-F04Q31ENQ8K-497190ca9c/conductor_orchestration_800.png", "thumb_800_w": 800, "thumb_800_h": 413, "thumb_960": "https://files.slack.com/files-tmb/T3P0PGX18-F04Q31ENQ8K-497190ca9c/conductor_orchestration_960.png", "thumb_960_w": 960, "thumb_960_h": 495, "thumb_1024": "https://files.slack.com/files-tmb/T3P0PGX18-F04Q31ENQ8K-497190ca9c/conductor_orchestration_1024.png", "thumb_1024_w": 1024, "thumb_1024_h": 528, "original_w": 1942, "original_h": 1002, "thumb_tiny": "AwAYADBmmqqzSgMGAAwwFaOB6VgRyvESUYqT6VpRQXEsSv8AaiNwzjFIC4MHtTlA54FUpVntYGfzt/bBHSiLDRKzXEwJGTj/APVTAuuo8tsgdDWXZxwNATIoLbj1NS3TbIGKXEpPTBP/ANasygAqeK6mUBBKQoHHSoKKAJpbiWQFHkLLUiX8yIqALhRgZFVaKALE17LNGUYLg+gqvRRQB//Z", "permalink": "https://steeleye.slack.com/files/U01PGFLH47Q/F04Q31ENQ8K/conductor_orchestration.png", "permalink_public": "https://slack-files.com/T3P0PGX18-F04Q31ENQ8K-02256fa7b6", "has_rich_preview": false, "file_access": "visible"}, {"id": "F04QFPKUR3K", "created": 1676620461, "timestamp": 1676620461, "name": "Screenshot 2021-03-04 at 2.43.28 PM.png", "title": "Screenshot 2021-03-04 at 2.43.28 PM.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U01PGFLH47Q", "user_team": "T3P0PGX18", "editable": false, "size": 285674, "mode": "hosted", "is_external": false, "external_type": "", "is_public": true, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T3P0PGX18-F04QFPKUR3K/screenshot_2021-03-04_at_2.43.28_pm.png", "url_private_download": "https://files.slack.com/files-pri/T3P0PGX18-F04QFPKUR3K/download/screenshot_2021-03-04_at_2.43.28_pm.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T3P0PGX18-F04QFPKUR3K-1439e93c6a/screenshot_2021-03-04_at_2.43.28_pm_64.png", "thumb_80": "https://files.slack.com/files-tmb/T3P0PGX18-F04QFPKUR3K-1439e93c6a/screenshot_2021-03-04_at_2.43.28_pm_80.png", "thumb_360": "https://files.slack.com/files-tmb/T3P0PGX18-F04QFPKUR3K-1439e93c6a/screenshot_2021-03-04_at_2.43.28_pm_360.png", "thumb_360_w": 360, "thumb_360_h": 236, "thumb_480": "https://files.slack.com/files-tmb/T3P0PGX18-F04QFPKUR3K-1439e93c6a/screenshot_2021-03-04_at_2.43.28_pm_480.png", "thumb_480_w": 480, "thumb_480_h": 315, "thumb_160": "https://files.slack.com/files-tmb/T3P0PGX18-F04QFPKUR3K-1439e93c6a/screenshot_2021-03-04_at_2.43.28_pm_160.png", "thumb_720": "https://files.slack.com/files-tmb/T3P0PGX18-F04QFPKUR3K-1439e93c6a/screenshot_2021-03-04_at_2.43.28_pm_720.png", "thumb_720_w": 720, "thumb_720_h": 473, "thumb_800": "https://files.slack.com/files-tmb/T3P0PGX18-F04QFPKUR3K-1439e93c6a/screenshot_2021-03-04_at_2.43.28_pm_800.png", "thumb_800_w": 800, "thumb_800_h": 525, "thumb_960": "https://files.slack.com/files-tmb/T3P0PGX18-F04QFPKUR3K-1439e93c6a/screenshot_2021-03-04_at_2.43.28_pm_960.png", "thumb_960_w": 960, "thumb_960_h": 630, "thumb_1024": "https://files.slack.com/files-tmb/T3P0PGX18-F04QFPKUR3K-1439e93c6a/screenshot_2021-03-04_at_2.43.28_pm_1024.png", "thumb_1024_w": 1024, "thumb_1024_h": 672, "original_w": 2136, "original_h": 1402, "thumb_tiny": "AwAfADCvxjqc0n50lFMBenFHNHel/E0gEwaOaPxNFACUUrKVODSUAOpCTSZooAXJozSDmlxQB//Z", "permalink": "https://steeleye.slack.com/files/U01PGFLH47Q/F04QFPKUR3K/screenshot_2021-03-04_at_2.43.28_pm.png", "permalink_public": "https://slack-files.com/T3P0PGX18-F04QFPKUR3K-5211d8a147", "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "2Lcl", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Test 9: edit and add message, delete all 3 files."}]}]}]}, "original_ts": "1676620475.340619", "subtype": "message_deleted", "editor_id": "U01PGFLH47Q", "file": {"id": "F04Q06PKDMK"}}, {"type": "message", "user": "U01PGFLH47Q", "upload": false, "ts": "1676620493.000000", "text": "Test 9: edit and add message, delete all 3 files.", "previous": {"text": "", "files": [{"id": "F04Q06PKDMK", "created": 1676620450, "timestamp": 1676620450, "name": "SteelEye_Conv-07022023.xlsx", "title": "SteelEye_Conv-07022023.xlsx", "mimetype": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", "filetype": "xlsx", "pretty_type": "Excel Spreadsheet", "user": "U01PGFLH47Q", "user_team": "T3P0PGX18", "editable": false, "size": 88034, "mode": "hosted", "is_external": false, "external_type": "", "is_public": true, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T3P0PGX18-F04Q06PKDMK/steeleye_conv-07022023.xlsx", "url_private_download": "https://files.slack.com/files-pri/T3P0PGX18-F04Q06PKDMK/download/steeleye_conv-07022023.xlsx", "media_display_type": "unknown", "converted_pdf": "https://files.slack.com/files-tmb/T3P0PGX18-F04Q06PKDMK-67dc7a1d36/steeleye_conv-07022023_converted.pdf", "thumb_pdf": "https://files.slack.com/files-tmb/T3P0PGX18-F04Q06PKDMK-67dc7a1d36/steeleye_conv-07022023_thumb_pdf.png", "thumb_pdf_w": 909, "thumb_pdf_h": 1286, "permalink": "https://steeleye.slack.com/files/U01PGFLH47Q/F04Q06PKDMK/steeleye_conv-07022023.xlsx", "permalink_public": "https://slack-files.com/T3P0PGX18-F04Q06PKDMK-5470072190", "has_rich_preview": false, "file_access": "visible"}, {"id": "F04Q31ENQ8K", "created": 1676620455, "timestamp": 1676620455, "name": "Conductor_orchestration.png", "title": "Conductor_orchestration.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U01PGFLH47Q", "user_team": "T3P0PGX18", "editable": false, "size": 193187, "mode": "hosted", "is_external": false, "external_type": "", "is_public": true, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T3P0PGX18-F04Q31ENQ8K/conductor_orchestration.png", "url_private_download": "https://files.slack.com/files-pri/T3P0PGX18-F04Q31ENQ8K/download/conductor_orchestration.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T3P0PGX18-F04Q31ENQ8K-497190ca9c/conductor_orchestration_64.png", "thumb_80": "https://files.slack.com/files-tmb/T3P0PGX18-F04Q31ENQ8K-497190ca9c/conductor_orchestration_80.png", "thumb_360": "https://files.slack.com/files-tmb/T3P0PGX18-F04Q31ENQ8K-497190ca9c/conductor_orchestration_360.png", "thumb_360_w": 360, "thumb_360_h": 186, "thumb_480": "https://files.slack.com/files-tmb/T3P0PGX18-F04Q31ENQ8K-497190ca9c/conductor_orchestration_480.png", "thumb_480_w": 480, "thumb_480_h": 248, "thumb_160": "https://files.slack.com/files-tmb/T3P0PGX18-F04Q31ENQ8K-497190ca9c/conductor_orchestration_160.png", "thumb_720": "https://files.slack.com/files-tmb/T3P0PGX18-F04Q31ENQ8K-497190ca9c/conductor_orchestration_720.png", "thumb_720_w": 720, "thumb_720_h": 371, "thumb_800": "https://files.slack.com/files-tmb/T3P0PGX18-F04Q31ENQ8K-497190ca9c/conductor_orchestration_800.png", "thumb_800_w": 800, "thumb_800_h": 413, "thumb_960": "https://files.slack.com/files-tmb/T3P0PGX18-F04Q31ENQ8K-497190ca9c/conductor_orchestration_960.png", "thumb_960_w": 960, "thumb_960_h": 495, "thumb_1024": "https://files.slack.com/files-tmb/T3P0PGX18-F04Q31ENQ8K-497190ca9c/conductor_orchestration_1024.png", "thumb_1024_w": 1024, "thumb_1024_h": 528, "original_w": 1942, "original_h": 1002, "thumb_tiny": "AwAYADBmmqqzSgMGAAwwFaOB6VgRyvESUYqT6VpRQXEsSv8AaiNwzjFIC4MHtTlA54FUpVntYGfzt/bBHSiLDRKzXEwJGTj/APVTAuuo8tsgdDWXZxwNATIoLbj1NS3TbIGKXEpPTBP/ANasygAqeK6mUBBKQoHHSoKKAJpbiWQFHkLLUiX8yIqALhRgZFVaKALE17LNGUYLg+gqvRRQB//Z", "permalink": "https://steeleye.slack.com/files/U01PGFLH47Q/F04Q31ENQ8K/conductor_orchestration.png", "permalink_public": "https://slack-files.com/T3P0PGX18-F04Q31ENQ8K-02256fa7b6", "has_rich_preview": false, "file_access": "visible"}, {"id": "F04QFPKUR3K", "created": 1676620461, "timestamp": 1676620461, "name": "Screenshot 2021-03-04 at 2.43.28 PM.png", "title": "Screenshot 2021-03-04 at 2.43.28 PM.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U01PGFLH47Q", "user_team": "T3P0PGX18", "editable": false, "size": 285674, "mode": "hosted", "is_external": false, "external_type": "", "is_public": true, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T3P0PGX18-F04QFPKUR3K/screenshot_2021-03-04_at_2.43.28_pm.png", "url_private_download": "https://files.slack.com/files-pri/T3P0PGX18-F04QFPKUR3K/download/screenshot_2021-03-04_at_2.43.28_pm.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T3P0PGX18-F04QFPKUR3K-1439e93c6a/screenshot_2021-03-04_at_2.43.28_pm_64.png", "thumb_80": "https://files.slack.com/files-tmb/T3P0PGX18-F04QFPKUR3K-1439e93c6a/screenshot_2021-03-04_at_2.43.28_pm_80.png", "thumb_360": "https://files.slack.com/files-tmb/T3P0PGX18-F04QFPKUR3K-1439e93c6a/screenshot_2021-03-04_at_2.43.28_pm_360.png", "thumb_360_w": 360, "thumb_360_h": 236, "thumb_480": "https://files.slack.com/files-tmb/T3P0PGX18-F04QFPKUR3K-1439e93c6a/screenshot_2021-03-04_at_2.43.28_pm_480.png", "thumb_480_w": 480, "thumb_480_h": 315, "thumb_160": "https://files.slack.com/files-tmb/T3P0PGX18-F04QFPKUR3K-1439e93c6a/screenshot_2021-03-04_at_2.43.28_pm_160.png", "thumb_720": "https://files.slack.com/files-tmb/T3P0PGX18-F04QFPKUR3K-1439e93c6a/screenshot_2021-03-04_at_2.43.28_pm_720.png", "thumb_720_w": 720, "thumb_720_h": 473, "thumb_800": "https://files.slack.com/files-tmb/T3P0PGX18-F04QFPKUR3K-1439e93c6a/screenshot_2021-03-04_at_2.43.28_pm_800.png", "thumb_800_w": 800, "thumb_800_h": 525, "thumb_960": "https://files.slack.com/files-tmb/T3P0PGX18-F04QFPKUR3K-1439e93c6a/screenshot_2021-03-04_at_2.43.28_pm_960.png", "thumb_960_w": 960, "thumb_960_h": 630, "thumb_1024": "https://files.slack.com/files-tmb/T3P0PGX18-F04QFPKUR3K-1439e93c6a/screenshot_2021-03-04_at_2.43.28_pm_1024.png", "thumb_1024_w": 1024, "thumb_1024_h": 672, "original_w": 2136, "original_h": 1402, "thumb_tiny": "AwAfADCvxjqc0n50lFMBenFHNHel/E0gEwaOaPxNFACUUrKVODSUAOpCTSZooAXJozSDmlxQB//Z", "permalink": "https://steeleye.slack.com/files/U01PGFLH47Q/F04QFPKUR3K/screenshot_2021-03-04_at_2.43.28_pm.png", "permalink_public": "https://slack-files.com/T3P0PGX18-F04QFPKUR3K-5211d8a147", "has_rich_preview": false, "file_access": "visible"}]}, "original_ts": "1676620475.340619", "subtype": "message_changed", "editor_id": "U01PGFLH47Q", "blocks": [{"type": "rich_text", "block_id": "2Lcl", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Test 9: edit and add message, delete all 3 files."}]}]}]}, {"type": "message", "user": "U01PGFLH47Q", "upload": false, "ts": "1676620417.000000", "text": "Test 8: edit and add message, delete only file.", "previous": {"text": "Test 8: edit and add message, delete only file.", "files": [{"id": "F04Q5JDFUDS", "mode": "tombstone"}], "blocks": [{"type": "rich_text", "block_id": "XCIX/", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Test 8: edit and add message, delete only file."}]}]}]}, "original_ts": "1676620395.813469", "subtype": "message_changed", "editor_id": "U00", "blocks": [{"type": "rich_text", "block_id": "XCIX/", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Test 8: edit and add message, delete only file."}]}]}]}, {"type": "message", "user": "U01PGFLH47Q", "upload": false, "ts": "1676620411.000000", "text": "Test 8: edit and add message, delete only file.", "previous": {"text": "", "files": [{"id": "F04Q5JDFUDS", "mode": "tombstone"}]}, "original_ts": "1676620395.813469", "subtype": "message_changed", "editor_id": "U01PGFLH47Q", "blocks": [{"type": "rich_text", "block_id": "XCIX/", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Test 8: edit and add message, delete only file."}]}]}]}, {"type": "message", "user": "U01PGFLH47Q", "upload": false, "ts": "1676620331.000000", "text": "", "previous": {"text": "Test 7: edit and add, delete.", "files": [{"id": "F04QFP7FJ49", "created": 1676620256, "timestamp": 1676620256, "name": "Conductor_orchestration.png", "title": "Conductor_orchestration.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U01PGFLH47Q", "user_team": "T3P0PGX18", "editable": false, "size": 193187, "mode": "hosted", "is_external": false, "external_type": "", "is_public": true, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T3P0PGX18-F04QFP7FJ49/conductor_orchestration.png", "url_private_download": "https://files.slack.com/files-pri/T3P0PGX18-F04QFP7FJ49/download/conductor_orchestration.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T3P0PGX18-F04QFP7FJ49-ff0b224910/conductor_orchestration_64.png", "thumb_80": "https://files.slack.com/files-tmb/T3P0PGX18-F04QFP7FJ49-ff0b224910/conductor_orchestration_80.png", "thumb_360": "https://files.slack.com/files-tmb/T3P0PGX18-F04QFP7FJ49-ff0b224910/conductor_orchestration_360.png", "thumb_360_w": 360, "thumb_360_h": 186, "thumb_480": "https://files.slack.com/files-tmb/T3P0PGX18-F04QFP7FJ49-ff0b224910/conductor_orchestration_480.png", "thumb_480_w": 480, "thumb_480_h": 248, "thumb_160": "https://files.slack.com/files-tmb/T3P0PGX18-F04QFP7FJ49-ff0b224910/conductor_orchestration_160.png", "thumb_720": "https://files.slack.com/files-tmb/T3P0PGX18-F04QFP7FJ49-ff0b224910/conductor_orchestration_720.png", "thumb_720_w": 720, "thumb_720_h": 371, "thumb_800": "https://files.slack.com/files-tmb/T3P0PGX18-F04QFP7FJ49-ff0b224910/conductor_orchestration_800.png", "thumb_800_w": 800, "thumb_800_h": 413, "thumb_960": "https://files.slack.com/files-tmb/T3P0PGX18-F04QFP7FJ49-ff0b224910/conductor_orchestration_960.png", "thumb_960_w": 960, "thumb_960_h": 495, "thumb_1024": "https://files.slack.com/files-tmb/T3P0PGX18-F04QFP7FJ49-ff0b224910/conductor_orchestration_1024.png", "thumb_1024_w": 1024, "thumb_1024_h": 528, "original_w": 1942, "original_h": 1002, "thumb_tiny": "AwAYADBmmqqzSgMGAAwwFaOB6VgRyvESUYqT6VpRQXEsSv8AaiNwzjFIC4MHtTlA54FUpVntYGfzt/bBHSiLDRKzXEwJGTj/APVTAuuo8tsgdDWXZxwNATIoLbj1NS3TbIGKXEpPTBP/ANasygAqeK6mUBBKQoHHSoKKAJpbiWQFHkLLUiX8yIqALhRgZFVaKALE17LNGUYLg+gqvRRQB//Z", "permalink": "https://steeleye.slack.com/files/U01PGFLH47Q/F04QFP7FJ49/conductor_orchestration.png", "permalink_public": "https://slack-files.com/T3P0PGX18-F04QFP7FJ49-6919b84a5d", "has_rich_preview": false, "file_access": "visible"}], "blocks": [{"type": "rich_text", "block_id": "FWz=a", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Test 7: edit and add, delete."}]}]}]}, "original_ts": "1676620263.138829", "subtype": "message_deleted", "editor_id": "U01PGFLH47Q", "file": {"id": "F04QFP7FJ49"}}, {"type": "message", "user": "U01PGFLH47Q", "upload": false, "ts": "1676620323.000000", "text": "Test 7: edit and add, delete.", "previous": {"text": "", "files": [{"id": "F04QFP7FJ49", "created": 1676620256, "timestamp": 1676620256, "name": "Conductor_orchestration.png", "title": "Conductor_orchestration.png", "mimetype": "image/png", "filetype": "png", "pretty_type": "PNG", "user": "U01PGFLH47Q", "user_team": "T3P0PGX18", "editable": false, "size": 193187, "mode": "hosted", "is_external": false, "external_type": "", "is_public": true, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T3P0PGX18-F04QFP7FJ49/conductor_orchestration.png", "url_private_download": "https://files.slack.com/files-pri/T3P0PGX18-F04QFP7FJ49/download/conductor_orchestration.png", "media_display_type": "unknown", "thumb_64": "https://files.slack.com/files-tmb/T3P0PGX18-F04QFP7FJ49-ff0b224910/conductor_orchestration_64.png", "thumb_80": "https://files.slack.com/files-tmb/T3P0PGX18-F04QFP7FJ49-ff0b224910/conductor_orchestration_80.png", "thumb_360": "https://files.slack.com/files-tmb/T3P0PGX18-F04QFP7FJ49-ff0b224910/conductor_orchestration_360.png", "thumb_360_w": 360, "thumb_360_h": 186, "thumb_480": "https://files.slack.com/files-tmb/T3P0PGX18-F04QFP7FJ49-ff0b224910/conductor_orchestration_480.png", "thumb_480_w": 480, "thumb_480_h": 248, "thumb_160": "https://files.slack.com/files-tmb/T3P0PGX18-F04QFP7FJ49-ff0b224910/conductor_orchestration_160.png", "thumb_720": "https://files.slack.com/files-tmb/T3P0PGX18-F04QFP7FJ49-ff0b224910/conductor_orchestration_720.png", "thumb_720_w": 720, "thumb_720_h": 371, "thumb_800": "https://files.slack.com/files-tmb/T3P0PGX18-F04QFP7FJ49-ff0b224910/conductor_orchestration_800.png", "thumb_800_w": 800, "thumb_800_h": 413, "thumb_960": "https://files.slack.com/files-tmb/T3P0PGX18-F04QFP7FJ49-ff0b224910/conductor_orchestration_960.png", "thumb_960_w": 960, "thumb_960_h": 495, "thumb_1024": "https://files.slack.com/files-tmb/T3P0PGX18-F04QFP7FJ49-ff0b224910/conductor_orchestration_1024.png", "thumb_1024_w": 1024, "thumb_1024_h": 528, "original_w": 1942, "original_h": 1002, "thumb_tiny": "AwAYADBmmqqzSgMGAAwwFaOB6VgRyvESUYqT6VpRQXEsSv8AaiNwzjFIC4MHtTlA54FUpVntYGfzt/bBHSiLDRKzXEwJGTj/APVTAuuo8tsgdDWXZxwNATIoLbj1NS3TbIGKXEpPTBP/ANasygAqeK6mUBBKQoHHSoKKAJpbiWQFHkLLUiX8yIqALhRgZFVaKALE17LNGUYLg+gqvRRQB//Z", "permalink": "https://steeleye.slack.com/files/U01PGFLH47Q/F04QFP7FJ49/conductor_orchestration.png", "permalink_public": "https://slack-files.com/T3P0PGX18-F04QFP7FJ49-6919b84a5d", "has_rich_preview": false, "file_access": "visible"}]}, "original_ts": "1676620263.138829", "subtype": "message_changed", "editor_id": "U01PGFLH47Q", "blocks": [{"type": "rich_text", "block_id": "FWz=a", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Test 7: edit and add, delete."}]}]}]}]}
{"messages": [{"client_msg_id": "f35c7d94-e7e3-4e5e-9bbe-dbddeb9c029e", "type": "message", "text": "Thanks Alicia", "user": "U02TAHUHMR6", "ts": "1676629113.906069", "blocks": [{"type": "rich_text", "block_id": "8iHU", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Thanks Alicia"}]}]}], "team": "T3P0PGX18"}, {"client_msg_id": "60A579B3-59D8-4997-B8BF-07E0232B3EDE", "type": "message", "text": "Hi Emma - Aga is off today so I\u2019ll ask Sonia to send to you directly ", "user": "U9G6MC36V", "ts": "1676628470.636449", "blocks": [{"type": "rich_text", "block_id": "6Gd", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Hi Emma - Aga is off today so "}, {"type": "text", "text": "I\u2019ll"}, {"type": "text", "text": " ask Sonia to send to you directly "}]}]}], "team": "T3P0PGX18"}, {"client_msg_id": "7bd8211a-a81c-4320-a64d-b08575d897b4", "type": "message", "text": "Hello everyone, please let me know of any updates for today's newsletter :slightly_smiling_face: <@U03AQU936HG> if you have a bio for Sonia that I can include that would be great.", "user": "U02TAHUHMR6", "ts": "1676627675.494419", "blocks": [{"type": "rich_text", "block_id": "7i4", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Hello everyone, please let me know of any updates for today's newsletter "}, {"type": "emoji", "name": "slightly_smiling_face", "unicode": "1f642"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U03AQU936HG"}, {"type": "text", "text": " if you have a bio for Sonia that I can include that would be great."}]}]}], "team": "T3P0PGX18"}], "members": ["U3NA51EUR", "U4E48R19P", "U4FG72DQQ", "U4PA3EVJT", "U9G6MC36V", "UJ21VFGHG", "UTFLSMCGM", "UVDCV9LQ0", "U012K27RE9E", "U01QHBXEMK4", "U02FPCF9MSA", "U02S4EAL82Z", "U02TAHUHMR6", "U03AQU936HG", "U04DRKEF7CK"], "info": {"id": "C03CY3FGSCX", "name": "internal-weekly-update", "created": 1651160603, "member_count": 15, "is_general": false, "is_private": false, "is_im": false, "is_mpim": false, "is_file": false, "is_deleted": false, "is_archived": false, "creator": "U02TAHUHMR6", "is_moved": false, "is_shared": false, "name_normalized": "internal-weekly-update", "is_global_shared": false, "is_org_shared": false, "is_org_mandatory": false, "is_org_default": false, "previous_names": [], "purpose": {"text": "", "set_by": "", "date_set": 0}, "topic": {"text": "", "set_by": "", "date_set": 0}, "retention": {"type": "default", "duration": 0}, "shared": {"shared_team_ids": [], "connected_team_ids": [], "internal_team_ids": [], "connected_limited_team_ids": []}, "has_guests": false}, "edits": []}
{"messages": [{"client_msg_id": "01e42742-c5af-407b-b190-cd3f200583f0", "type": "message", "text": "we have again some tickets which were not deployed to uat as devs think that it is forbidden, let me create a new chat dedicated to Regression Testing where we can discuss it", "user": "U04G288E8BW", "ts": "1676636652.313149", "blocks": [{"type": "rich_text", "block_id": "E1hPc", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "we have again some tickets which were not deployed to uat as devs think that it is forbidden, let me create a new chat dedicated to Regression Testing where we can discuss it"}]}]}], "team": "T3P0PGX18"}, {"client_msg_id": "e5c26f9f-0e4c-465f-af63-47c4a49a8ea9", "type": "message", "text": "Yes, it would be fixes only. So the devs will need to make sure they're branching/cherry-picking appropriately", "user": "U4XDQTVFA", "ts": "1676632925.762359", "blocks": [{"type": "rich_text", "block_id": "ssT", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Yes, it would be fixes only. So the devs will need to make sure they're branching/cherry-picking appropriately"}]}]}], "team": "T3P0PGX18"}, {"client_msg_id": "ef2dba25-eed0-4208-b65f-05aaf85b3967", "type": "message", "text": "yep so at least it is my understanding as we discussed before right Stephen <@U4XDQTVFA>?", "user": "U04G288E8BW", "ts": "1676632885.998339", "blocks": [{"type": "rich_text", "block_id": "2Ekf6", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "yep so at least it is my understanding as we discussed before right Stephen "}, {"type": "user", "user_id": "U4XDQTVFA"}, {"type": "text", "text": "?"}]}]}], "team": "T3P0PGX18"}, {"client_msg_id": "55806447-799d-49d2-9ff9-236259d612fa", "type": "message", "text": "<@U04G288E8BW> but we're containing just the 'new stuff' to the critical items right?\nany new features are out of scope for updating uat _with_ ?", "user": "U4PA3EVJT", "ts": "1676632801.143769", "blocks": [{"type": "rich_text", "block_id": "Rf7HH", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04G288E8BW"}, {"type": "text", "text": " but we're containing just the 'new stuff' to the critical items right?\nany new features are out of scope for updating uat "}, {"type": "text", "text": "with", "style": {"italic": true}}, {"type": "text", "text": " ?"}]}]}], "team": "T3P0PGX18"}, {"client_msg_id": "9777fd16-7974-413d-8e36-efe8cb85cda4", "type": "message", "text": "ok then it should be fine thanks", "user": "U04G288E8BW", "ts": "1676631602.665129", "blocks": [{"type": "rich_text", "block_id": "V5=R", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "ok then it should be fine thanks"}]}]}], "team": "T3P0PGX18"}, {"client_msg_id": "9f491f83-7b3d-46ce-83fd-27bb34bdf831", "type": "message", "text": "There is no down-time during deployment", "user": "U4XDQTVFA", "ts": "1676631582.344489", "blocks": [{"type": "rich_text", "block_id": "t6YpT", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "There is no down-time during deployment"}]}]}], "team": "T3P0PGX18"}, {"client_msg_id": "05992ed4-105c-45f1-991b-d791f79ab974", "type": "message", "text": "<@U4XDQTVFA> good question... From one hand I would say it is better to deploy them as-and-when they're available so we can check them asap and continue the appropriate regression module. Does env go down for long during deployment? Regression is usually running early in the morning so this shouldn't impact so much. So can we try the second option for now? and I'll let you know if it is blocking any processes on our side", "user": "U04G288E8BW", "ts": "1676631527.373539", "blocks": [{"type": "rich_text", "block_id": "J/n", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U4XDQTVFA"}, {"type": "text", "text": " good question... From one hand I would say it is better to deploy them as-and-when they're available so we can check them asap and continue the appropriate regression module. Does env go down for long during deployment? Regression is usually running early in the morning so this shouldn't impact so much. So can we try the second option for now? and I'll let you know if it is blocking any processes on our side"}]}]}], "team": "T3P0PGX18"}, {"client_msg_id": "b205dad2-f750-4928-9333-acaab95c3e8f", "type": "message", "text": "<@U04G288E8BW> how would you like new versions handled? Do you want to wait until end of day for a list of issues and then we do a single deployment to UAT or do you want them deployed as-and-when they're available?", "user": "U4XDQTVFA", "ts": "1676630949.639329", "blocks": [{"type": "rich_text", "block_id": "06zm", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04G288E8BW"}, {"type": "text", "text": " how would you like new versions handled? Do you want to wait until end of day for a list of issues and then we do a single deployment to UAT or do you want them deployed as-and-when they're available?"}]}]}], "team": "T3P0PGX18"}, {"client_msg_id": "9c20b603-574f-4abf-a681-827664a594d2", "type": "message", "text": "yep ok that is my understanding too I'll ask Prashant to push it to uat, thanks", "user": "U04G288E8BW", "ts": "1676628114.861539", "blocks": [{"type": "rich_text", "block_id": "5yp", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "yep ok that is my understanding too I'll ask Prashant to push it to uat, thanks"}]}]}], "team": "T3P0PGX18"}, {"client_msg_id": "fdd493ce-bd37-4a4d-8ac9-1245cbb8eef1", "type": "message", "text": "If there are updated versions and/or regression issues identified which require new versions, those can be deployed.", "user": "U4XDQTVFA", "ts": "1676628063.204619", "blocks": [{"type": "rich_text", "block_id": "pdZL", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "If there are updated versions and/or regression issues identified which require new versions, those can be deployed."}]}]}], "team": "T3P0PGX18"}, {"client_msg_id": "be7c6a1e-28de-4a08-bbce-120c8dccbb7e", "type": "message", "text": "The lock down means we are no longer auto-deploying to UAT", "user": "U4XDQTVFA", "ts": "1676628043.863469", "blocks": [{"type": "rich_text", "block_id": "rULV", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "The lock down means we are no longer auto-deploying to UAT"}]}]}], "team": "T3P0PGX18"}, {"client_msg_id": "ab7a2d06-bf40-4c91-b0e0-215ff29b4532", "type": "message", "text": "No. Pushes for defect will be allowed.", "user": "U4XDQTVFA", "ts": "1676628021.360759", "blocks": [{"type": "rich_text", "block_id": "KYm", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "No. Pushes for defect will be allowed."}]}]}], "team": "T3P0PGX18"}, {"client_msg_id": "daa512ec-a381-4ddd-8058-9de25d6435db", "type": "message", "text": "<@U4XDQTVFA> Hey Stephen, as we are going to run regression in UAT as we agreed but UAT is locked down from the other hand... Does it mean that we are not able to have pushes for defects fixes we entered in UAT. Asking because for example there is important thing like EU-5929 (discussed in sre-group chat as well) the fix was pushed to dev only", "user": "U04G288E8BW", "ts": "1676627893.418249", "blocks": [{"type": "rich_text", "block_id": "/nk", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U4XDQTVFA"}, {"type": "text", "text": " Hey Stephen, as we are going to run regression in UAT as we agreed but UAT is locked down from the other hand... Does it mean that we are not able to have pushes for defects fixes we entered in UAT. Asking because for example there is important thing like EU-5929 (discussed in sre-group chat as well) the fix was pushed to dev only"}]}]}], "team": "T3P0PGX18", "edited": {"user": "U04G288E8BW", "ts": "1676627925.000000"}}, {"client_msg_id": "781d37bf-f7d1-4c7d-8d08-833f5f2a07b3", "type": "message", "text": "that works", "user": "U4PA3EVJT", "ts": "1676625194.621919", "blocks": [{"type": "rich_text", "block_id": "+jY", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "that works"}]}]}], "team": "T3P0PGX18", "thread_ts": "1676565783.378899", "parent_user_id": "U4E48R19P"}, {"client_msg_id": "0e29e36e-3033-4ae1-93c4-f6c32d8726d5", "type": "message", "text": "We can/should align SIT with prod for now", "user": "U4XDQTVFA", "ts": "1676622242.188179", "blocks": [{"type": "rich_text", "block_id": "lBYW", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "We can/should align SIT with prod for now"}]}]}], "team": "T3P0PGX18", "thread_ts": "1676565783.378899", "parent_user_id": "U4E48R19P"}, {"client_msg_id": "4ae77196-254c-47ec-8e9e-0883e9cdae48", "type": "message", "text": "So _if_ we need to do a hotfix, we can do dev-&gt;SIT-&gt;prod", "user": "U4XDQTVFA", "ts": "1676622228.774519", "blocks": [{"type": "rich_text", "block_id": "5vG", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "So "}, {"type": "text", "text": "if", "style": {"italic": true}}, {"type": "text", "text": " we need to do a hotfix, we can do dev->SIT->prod"}]}]}], "team": "T3P0PGX18", "thread_ts": "1676565783.378899", "parent_user_id": "U4E48R19P"}, {"client_msg_id": "a6a39526-0918-49e2-be18-57f0676cafd5", "type": "message", "text": "This is why I'm suggesting we use SIT", "user": "U4XDQTVFA", "ts": "1676622205.597579", "blocks": [{"type": "rich_text", "block_id": "7zufj", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "This is why I'm suggesting we use SIT"}]}]}], "team": "T3P0PGX18", "thread_ts": "1676565783.378899", "parent_user_id": "U4E48R19P"}, {"client_msg_id": "6578a183-274e-414a-8b10-30bdb6ff4fa9", "type": "message", "text": "so yes, the concern is that we identify a bug requiring a hot fix\nhopefully we don\u2019t get such a case during the regression and sit is fully formed next time around\u2026.", "user": "U4PA3EVJT", "ts": "1676615965.018929", "blocks": [{"type": "rich_text", "block_id": "LHc2", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "so yes, the concern is that we identify a bug requiring a hot fix\nhopefully we don\u2019t get such a case during the regression and sit is fully formed next time around\u2026."}]}]}], "team": "T3P0PGX18", "thread_ts": "1676565783.378899", "parent_user_id": "U4E48R19P"}, {"client_msg_id": "745476FF-E1D8-4937-BE38-36E6D2AEF8E5", "type": "message", "text": "<@U4PA3EVJT> would be risky to promote fixes from Dev to Production directly :cold_face: ", "user": "U4E48R19P", "ts": "1676599520.599199", "blocks": [{"type": "rich_text", "block_id": "ARzhe", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U4PA3EVJT"}, {"type": "text", "text": " would be risky to promote fixes from Dev to Production directly "}, {"type": "emoji", "name": "cold_face", "unicode": "1f976"}, {"type": "text", "text": " "}]}]}], "team": "T3P0PGX18", "thread_ts": "1676565783.378899", "parent_user_id": "U4E48R19P"}, {"client_msg_id": "9663b2ee-ab36-4ee8-b798-f1086c854adc", "type": "message", "text": "<@U4E48R19P> why can't bug qa-ing of new tickets occur in dev for now?", "user": "U4PA3EVJT", "ts": "1676569411.233069", "blocks": [{"type": "rich_text", "block_id": "kjN", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U4E48R19P"}, {"type": "text", "text": " why can't bug qa-ing of new tickets occur in dev for now?"}]}]}], "team": "T3P0PGX18", "thread_ts": "1676565783.378899", "parent_user_id": "U4E48R19P"}, {"client_msg_id": "041E28A3-AFAE-41F3-B9A7-1EBDE3A87980", "type": "message", "text": "Okay, thanks for the update Steve", "user": "U4E48R19P", "ts": "1676566811.667419", "blocks": [{"type": "rich_text", "block_id": "Yb1Bp", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Okay, thanks for the update Steve"}]}]}], "team": "T3P0PGX18", "thread_ts": "1676565783.378899", "parent_user_id": "U4E48R19P"}, {"client_msg_id": "e6a45d4d-a0ee-464c-85f5-8d9661ce5269", "type": "message", "text": "I thought it was ready. The most recent deployment proved otherwise. Don't know how many of the issues are just config-related or more involved. There wasn't SRE bandwidth today to investigate, hence the decision to use UAT for regression testing as they have already been delayed by 4 days", "user": "U4XDQTVFA", "ts": "1676566301.493009", "blocks": [{"type": "rich_text", "block_id": "+Kd", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I thought it was ready. The most recent deployment proved otherwise. Don't know how many of the issues are just config-related or more involved. There wasn't SRE bandwidth today to investigate, hence the decision to use UAT for regression testing as they have already been delayed by 4 days"}]}]}], "team": "T3P0PGX18", "thread_ts": "1676565783.378899", "parent_user_id": "U4E48R19P"}, {"client_msg_id": "6045A35C-2646-459D-9EB4-38FCDF941491", "type": "message", "text": "How far out is SIT?", "user": "U4E48R19P", "ts": "1676566225.542219", "blocks": [{"type": "rich_text", "block_id": "UDzc", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "How far out is SIT?"}]}]}], "team": "T3P0PGX18", "thread_ts": "1676565783.378899", "parent_user_id": "U4E48R19P"}, {"client_msg_id": "ee2075e9-098e-4056-9b93-bec0874f52e9", "type": "message", "text": "One possibility is to align SIT with prod", "user": "U4XDQTVFA", "ts": "1676566027.676189", "blocks": [{"type": "rich_text", "block_id": "GCLc", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "One possibility is to align SIT with prod"}]}]}], "team": "T3P0PGX18", "thread_ts": "1676565783.378899", "parent_user_id": "U4E48R19P"}, {"client_msg_id": "3db3094b-babe-4fd3-8dca-35d2d3b98cc5", "type": "message", "text": "Well, if we can get SIT to a usable state, that can be the place until we make it through this release", "user": "U4XDQTVFA", "ts": "1676566006.281089", "blocks": [{"type": "rich_text", "block_id": "2eMZ", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Well, if we can get SIT to a usable state, that can be the place until we make it through this release"}]}]}], "team": "T3P0PGX18", "thread_ts": "1676565783.378899", "parent_user_id": "U4E48R19P"}, {"client_msg_id": "27EA1EC6-139E-41E9-95EB-A344B555B890", "type": "message", "text": "Especially API and Frontend", "user": "U4E48R19P", "ts": "1676565853.337529", "blocks": [{"type": "rich_text", "block_id": "e5m", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Especially API and Frontend"}]}]}], "team": "T3P0PGX18", "thread_ts": "1676565783.378899", "parent_user_id": "U4E48R19P"}, {"client_msg_id": "FFD3EB61-392D-4E12-BCAF-42C62086E75C", "type": "message", "text": "If I have to test bug fixes arising out of Zendesk", "user": "U4E48R19P", "ts": "1676565844.045729", "blocks": [{"type": "rich_text", "block_id": "QfPob", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "If I have to test bug fixes arising out of Zendesk"}]}]}], "team": "T3P0PGX18", "thread_ts": "1676565783.378899", "parent_user_id": "U4E48R19P"}, {"client_msg_id": "ac4b7397-2446-4bb3-8ab6-4a0aab0ff6aa", "type": "message", "text": "UAT is _NOT_ locked down for integration...", "user": "U4XDQTVFA", "ts": "1676565816.623299", "blocks": [{"type": "rich_text", "block_id": "vEFB", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "UAT is "}, {"type": "text", "text": "NOT", "style": {"italic": true}}, {"type": "text", "text": " locked down for integration..."}]}]}], "team": "T3P0PGX18", "thread_ts": "1676565783.378899", "parent_user_id": "U4E48R19P"}, {"client_msg_id": "c37df486-5d94-422b-8733-a4c93da97980", "type": "message", "text": "UAT would have been locked down even if SIT was being used", "user": "U4XDQTVFA", "ts": "1676565809.923059", "blocks": [{"type": "rich_text", "block_id": "k/n0c", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "UAT would have been locked down even if SIT was being used"}]}]}], "team": "T3P0PGX18", "thread_ts": "1676565783.378899", "parent_user_id": "U4E48R19P"}, {"client_msg_id": "0215c3f9-fd08-46fb-bf8d-ea44d1bcbd4a", "type": "message", "text": "How so?", "user": "U4XDQTVFA", "ts": "1676565800.566239", "blocks": [{"type": "rich_text", "block_id": "E+yq", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "How so?"}]}]}], "team": "T3P0PGX18", "thread_ts": "1676565783.378899", "parent_user_id": "U4E48R19P"}, {"client_msg_id": "B613380A-B325-4CD0-9BBA-EDA64823AA44", "type": "message", "text": "Okay, this will be a major blocker for me if UAT is locked down ", "user": "U4E48R19P", "ts": "1676565783.378899", "blocks": [{"type": "rich_text", "block_id": "fvnb", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Okay, this will be a major blocker for me if UAT is locked down "}]}]}], "team": "T3P0PGX18", "thread_ts": "1676565783.378899", "reply_count": 17, "reply_users_count": 3, "latest_reply": "1676625194.621919", "reply_users": ["U4XDQTVFA", "U4E48R19P", "U4PA3EVJT"], "replies": [{"user": "U4XDQTVFA", "ts": "1676565800.566239"}, {"user": "U4XDQTVFA", "ts": "1676565809.923059"}, {"user": "U4XDQTVFA", "ts": "1676565816.623299"}, {"user": "U4E48R19P", "ts": "1676565844.045729"}, {"user": "U4E48R19P", "ts": "1676565853.337529"}, {"user": "U4XDQTVFA", "ts": "1676566006.281089"}, {"user": "U4XDQTVFA", "ts": "1676566027.676189"}, {"user": "U4E48R19P", "ts": "1676566225.542219"}, {"user": "U4XDQTVFA", "ts": "1676566301.493009"}, {"user": "U4E48R19P", "ts": "1676566811.667419"}, {"user": "U4PA3EVJT", "ts": "1676569411.233069"}, {"user": "U4E48R19P", "ts": "1676599520.599199"}, {"user": "U4PA3EVJT", "ts": "1676615965.018929"}, {"user": "U4XDQTVFA", "ts": "1676622205.597579"}, {"user": "U4XDQTVFA", "ts": "1676622228.774519"}, {"user": "U4XDQTVFA", "ts": "1676622242.188179"}, {"user": "U4PA3EVJT", "ts": "1676625194.621919"}], "is_locked": false}, {"client_msg_id": "2d3d1f8c-5c2f-487c-a82c-5f6053d73666", "type": "message", "text": "and then the SIT -&gt; UAT process will be used in the next release process as SIT isn't ready right now", "user": "U4XDQTVFA", "ts": "1676565779.264439", "blocks": [{"type": "rich_text", "block_id": "5Gbn", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "and then the SIT -> UAT process will be used in the next release process as SIT isn't ready right now"}]}]}], "team": "T3P0PGX18"}, {"client_msg_id": "ef5c4aa3-4566-44c2-be10-1724f061ee1f", "type": "message", "text": "No. We're using the existing UAT stack and deploying the versions that were going to be put into SIT", "user": "U4XDQTVFA", "ts": "1676565760.341879", "blocks": [{"type": "rich_text", "block_id": "ihEKW", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "No. We're using the existing UAT stack and deploying the versions that were going to be put into SIT"}]}]}], "team": "T3P0PGX18"}, {"client_msg_id": "79DC5908-AD4F-49D9-AE3B-9AD9C4FF9671", "type": "message", "text": "Is there a different UAT stack we can use for testing bug fixes?", "user": "U4E48R19P", "ts": "1676565744.862449", "blocks": [{"type": "rich_text", "block_id": "ZMPE", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Is there a different UAT stack we can use for testing bug fixes?"}]}]}], "team": "T3P0PGX18"}, {"client_msg_id": "845d2315-5b1a-466c-8b65-4ab149074838", "type": "message", "text": "It did earlier this week", "user": "U4XDQTVFA", "ts": "1676565702.630989", "blocks": [{"type": "rich_text", "block_id": "OxoP", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "It did earlier this week"}]}]}], "team": "T3P0PGX18", "reactions": [{"name": "+1::skin-tone-2", "users": ["U4E48R19P"], "count": 1}]}, {"client_msg_id": "D5EF20B4-38EB-4DC8-A121-90C3F7B88D0D", "type": "message", "text": "When does UAT enter lockdown phase?", "user": "U4E48R19P", "ts": "1676565687.834069", "blocks": [{"type": "rich_text", "block_id": "0MXR", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "When does UAT enter lockdown phase?"}]}]}], "team": "T3P0PGX18"}, {"client_msg_id": "fb389c0a-51cd-4166-b3d2-8fae12aa1750", "type": "message", "text": "but if sit requires more time and uat is stable and has the latest code version we need then we can start there as we have lost much time indeed...", "user": "U04G288E8BW", "ts": "1676565560.578779", "blocks": [{"type": "rich_text", "block_id": "Z6Tr9", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "but if sit requires more time and uat is stable and has the latest code version we need then we can start there as we have lost much time indeed..."}]}]}], "team": "T3P0PGX18"}, {"client_msg_id": "ab3e3311-7e41-495e-96c1-30af1bc8380b", "type": "message", "text": "we'll need to spend some time to make sure that uat and sit have the same conditions and everything is up and running properly in sit, it will mean we'll need to rerun some part of what we  have already done", "user": "U04G288E8BW", "ts": "1676565423.559619", "blocks": [{"type": "rich_text", "block_id": "isB", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "we'll need to spend some time to make sure that uat and sit have the same conditions and everything is up and running properly in sit, it will mean we'll need to rerun some part of what we  have already done"}]}]}], "team": "T3P0PGX18"}, {"client_msg_id": "80062fbe-320d-4d42-b35b-8aa6aed58bea", "type": "message", "text": "no I mean we'll be running Mar test cases in mar.uat and other regression in iris.uat but if for example sit will be finally complete in 2 weeks it will be quite hard to switch regression run there", "user": "U04G288E8BW", "ts": "1676565237.079599", "blocks": [{"type": "rich_text", "block_id": "Goa", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "no I mean we'll be running Mar test cases in mar.uat and other regression in iris.uat but if for example sit will be finally complete in 2 weeks it will be quite hard to switch regression run there"}]}]}], "team": "T3P0PGX18", "edited": {"user": "U04G288E8BW", "ts": "1676565268.000000"}}, {"client_msg_id": "62c4c9c1-15d2-4dc1-91d6-b4e4ae9165fb", "type": "message", "text": "I can ingest test cases into iris.uat if that's preferable, no problemo, the tenants are on the same stack so only difference is the data in the environments..", "user": "U4PA3EVJT", "ts": "1676565139.976959", "blocks": [{"type": "rich_text", "block_id": "cR3", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I can ingest test cases into iris.uat if that's preferable, no problemo, the tenants are on the same stack so only difference is the data in the environments.."}]}]}], "team": "T3P0PGX18"}, {"client_msg_id": "dd0f4679-b9b3-40f6-84ac-7046a2d386a0", "type": "message", "text": "I mean if we start in iris.uat then it will be reasonable to continue in this env as well", "user": "U04G288E8BW", "ts": "1676563916.289909", "blocks": [{"type": "rich_text", "block_id": "2X6", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I mean if we start in iris.uat then it will be reasonable to continue in this env as well"}]}]}], "team": "T3P0PGX18"}, {"client_msg_id": "95d98127-d8d8-4ea5-8833-82e58f6e6e15", "type": "message", "text": "ok so everything else in iris.uat right?", "user": "U04G288E8BW", "ts": "1676563878.383359", "blocks": [{"type": "rich_text", "block_id": "YxYwK", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "ok so everything else in iris.uat right?"}]}]}], "team": "T3P0PGX18"}, {"client_msg_id": "0d4f2ba5-d14a-4d49-b164-b1399b6b52ee", "type": "message", "text": "we can do MAR Unit tests in uat\neasier as all the tests are already in mar.uat", "user": "U4PA3EVJT", "ts": "1676563846.496209", "blocks": [{"type": "rich_text", "block_id": "8pQl", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "we can do MAR Unit tests in uat\neasier as all the tests are already in mar.uat"}]}]}], "team": "T3P0PGX18", "edited": {"user": "U4PA3EVJT", "ts": "1676563851.000000"}}, {"client_msg_id": "08cba388-7c5b-4c91-b963-a7579ce6a9e8", "type": "message", "text": "I think more time is needed to get SIT sorted.", "user": "U4XDQTVFA", "ts": "1676563783.907649", "blocks": [{"type": "rich_text", "block_id": "dgw", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I think more time is needed to get SIT sorted."}]}]}], "team": "T3P0PGX18"}, {"client_msg_id": "663066e8-a3be-44b1-9051-adb0f93d8f03", "type": "message", "text": "<@U4XDQTVFA> Yep we can but to be honest the idea was to check everything even MAR units in sit (to start with some smoke and to continue with general regression if it is stable). Or do you mean that you need more time to fix everything in sit?", "user": "U04G288E8BW", "ts": "1676563754.030269", "blocks": [{"type": "rich_text", "block_id": "Twmp9", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U4XDQTVFA"}, {"type": "text", "text": " Yep we can but to be honest the idea was to check everything even MAR units in sit (to start with some smoke and to continue with general regression if it is stable). Or do you mean that you need more time to fix everything in sit?"}]}]}], "team": "T3P0PGX18"}, {"client_msg_id": "db396eb4-02ee-413c-82cc-83d279c2b8b7", "type": "message", "text": "The release candidate has been deployed to the UAT stack", "user": "U4XDQTVFA", "ts": "1676563571.222299", "blocks": [{"type": "rich_text", "block_id": "oYnkq", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "The release candidate has been deployed to the UAT stack"}]}]}], "team": "T3P0PGX18", "edited": {"user": "U4XDQTVFA", "ts": "1676563572.000000"}}, {"client_msg_id": "3153721e-1a96-4068-9056-9cb714d99550", "type": "message", "text": "<@U04G288E8BW> Can you please proceed with using the UAT env (`iris.uat`) for regression testing?", "user": "U4XDQTVFA", "ts": "1676563560.030159", "blocks": [{"type": "rich_text", "block_id": "dJW", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04G288E8BW"}, {"type": "text", "text": " Can you please proceed with using the UAT env ("}, {"type": "text", "text": "iris.uat", "style": {"code": true}}, {"type": "text", "text": ") for regression testing?"}]}]}], "team": "T3P0PGX18"}, {"client_msg_id": "f015a1f0-9891-4d79-b99b-627408ea6b0a", "type": "message", "text": "ok thanks please let us know if and when they are resolved", "user": "U04G288E8BW", "ts": "1676546770.460389", "blocks": [{"type": "rich_text", "block_id": "g+SWh", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "ok thanks please let us know if and when they are resolved"}]}]}], "team": "T3P0PGX18"}, {"client_msg_id": "0ee49038-e0bf-40c2-9b05-7dc85ad6a548", "type": "message", "text": "<@U04G288E8BW> - bear with us - we are going to prepare the release in the UAT environment. I think the SIT stack still has some teething issues that need to be worked out before it can be used for QA regression", "user": "U4XDQTVFA", "ts": "1676546621.866479", "blocks": [{"type": "rich_text", "block_id": "Kpy", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04G288E8BW"}, {"type": "text", "text": " - bear with us - we are going to prepare the release in the UAT environment. I think the SIT stack still has some teething issues that need to be worked out before it can be used for QA regression"}]}]}], "team": "T3P0PGX18"}, {"client_msg_id": "50036d0c-2844-4969-b13d-00e2e4fdf3fb", "type": "message", "text": "Who can help us on this?", "user": "U04G288E8BW", "ts": "1676544344.556789", "blocks": [{"type": "rich_text", "block_id": "lQU4P", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Who can help us on this?"}]}]}], "team": "T3P0PGX18"}, {"client_msg_id": "ffd0799f-7397-49cc-8379-c92eb1e7cf04", "type": "message", "text": "<@U4XDQTVFA> it seems data is corrupted in sit env, I can't check on s3 as don't have access but what we see right now, we are not able to download anything, for example for Communications module try to download any email, call you'll get 500 error, for messages you'll get 422 error", "user": "U04G288E8BW", "ts": "1676544332.246509", "blocks": [{"type": "rich_text", "block_id": "/UaB3", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U4XDQTVFA"}, {"type": "text", "text": " it seems data is corrupted in sit env, I can't check on s3 as don't have access but what we see right now, we are not able to download anything, for example for Communications module try to download any email, call you'll get 500 error, for messages you'll get 422 error"}]}]}], "team": "T3P0PGX18"}, {"type": "message", "subtype": "channel_join", "ts": "1676543918.123249", "user": "U04FQMM6FLM", "text": "<@U04FQMM6FLM> has joined the channel", "inviter": "U04G288E8BW"}, {"type": "message", "subtype": "channel_join", "ts": "1676543918.035099", "user": "U049ZP030R2", "text": "<@U049ZP030R2> has joined the channel", "inviter": "U04G288E8BW"}, {"type": "message", "subtype": "channel_join", "ts": "1676543917.943769", "user": "U04GCDHMM5X", "text": "<@U04GCDHMM5X> has joined the channel", "inviter": "U04G288E8BW"}, {"client_msg_id": "14a8c30c-9fd7-4566-8258-27bb82984592", "type": "message", "text": "Great, thanks everyone!", "user": "U04G288E8BW", "ts": "1676542013.757979", "blocks": [{"type": "rich_text", "block_id": "mgD", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Great, thanks everyone!"}]}]}], "team": "T3P0PGX18"}, {"client_msg_id": "ad43f1a7-9512-48c2-b133-79eb8831951b", "type": "message", "text": "<@U04G288E8BW> that is correct. The scope of 1.27.0 has been deployed. Please let me know if you notice any instability", "user": "U4XDQTVFA", "ts": "1676541932.873739", "blocks": [{"type": "rich_text", "block_id": "bJb", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U04G288E8BW"}, {"type": "text", "text": " that is correct. The scope of 1.27.0 has been deployed. Please let me know if you notice any instability"}]}]}], "team": "T3P0PGX18"}, {"client_msg_id": "6186778f-e6df-4bf2-936c-471e7cbe53bd", "type": "message", "text": "<@U4XDQTVFA> , <@U020CT12DQF> do we need anything else still to start the regression testing in sit? Is the scope of 1.27 release pushed there?", "user": "U04G288E8BW", "ts": "1676541865.538579", "blocks": [{"type": "rich_text", "block_id": "M/KR", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U4XDQTVFA"}, {"type": "text", "text": " , "}, {"type": "user", "user_id": "U020CT12DQF"}, {"type": "text", "text": " do we need anything else still to start the regression testing in sit? Is the scope of 1.27 release pushed there?"}]}]}], "team": "T3P0PGX18"}, {"client_msg_id": "166c8d48-93f9-485f-b3dc-8b1bf3258cb7", "type": "message", "text": "<@U4PA3EVJT> this has been deployed", "user": "U4XDQTVFA", "ts": "1676541392.967399", "blocks": [{"type": "rich_text", "block_id": "q6L", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U4PA3EVJT"}, {"type": "text", "text": " this has been deployed"}]}]}], "team": "T3P0PGX18"}], "members": ["U3NA51EUR", "U4E48R19P", "U4PA3EVJT", "U4XDQTVFA", "U011XA81ZSA", "U012K27RE9E", "U013W0HT6KW", "U01AWB8GG1F", "U01DFSQNYNB", "U020CT12DQF", "U024XCUUY8Y", "U025DCU40CA", "U028BQ5TFDJ", "U02BKE4MRSN", "U03A1PZEBDX", "U049ZP030R2", "U04CDMJJUDB", "U04FQMM6FLM", "U04G288E8BW", "U04GCDHMM5X"], "info": {"id": "C04KF0WGFTM", "name": "sit-build-out", "created": 1674049247, "member_count": 20, "is_general": false, "is_private": false, "is_im": false, "is_mpim": false, "is_file": false, "is_deleted": false, "is_archived": false, "creator": "U3NA51EUR", "is_moved": false, "is_shared": false, "name_normalized": "sit-build-out", "is_global_shared": false, "is_org_shared": false, "is_org_mandatory": false, "is_org_default": false, "previous_names": [], "purpose": {"text": "temp channel to finalise sit env ahead of qa start on monday", "set_by": "U3NA51EUR", "date_set": 1674049247}, "topic": {"text": "", "set_by": "", "date_set": 0}, "retention": {"type": "default", "duration": 0}, "shared": {"shared_team_ids": [], "connected_team_ids": [], "internal_team_ids": [], "connected_limited_team_ids": []}, "has_guests": false}, "edits": [{"type": "message", "user": "U04G288E8BW", "upload": false, "ts": "1676627925.000000", "text": "<@U4XDQTVFA> Hey Stephen, as we are going to run regression in UAT as we agreed but UAT is locked down from the other hand... Does it mean that we are not able to have pushes for defects fixes we entered in UAT. Asking because for example there is important thing like EU-5929 (discussed in sre-group chat as well) the fix was pushed to dev only", "previous": {"text": "<@U4XDQTVFA> Hey Stephen, as we are going to run regression in UAT as we agreed but UAT is locked down from the other hand... Does it mean that we are not able to have pushes to defects we entered in UAT. Asking because for example there is important thing like EU-5929 (discussed in sre-group chat as well) the fix was pushed to dev only", "blocks": [{"type": "rich_text", "block_id": "KysN", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U4XDQTVFA"}, {"type": "text", "text": " Hey Stephen, as we are going to run regression in UAT as we agreed but UAT is locked down from the other hand... Does it mean that we are not able to have pushes to defects we entered in UAT. Asking because for example there is important thing like EU-5929 (discussed in sre-group chat as well) the fix was pushed to dev only"}]}]}]}, "original_ts": "1676627893.418249", "subtype": "message_changed", "editor_id": "U04G288E8BW", "blocks": [{"type": "rich_text", "block_id": "/nk", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U4XDQTVFA"}, {"type": "text", "text": " Hey Stephen, as we are going to run regression in UAT as we agreed but UAT is locked down from the other hand... Does it mean that we are not able to have pushes for defects fixes we entered in UAT. Asking because for example there is important thing like EU-5929 (discussed in sre-group chat as well) the fix was pushed to dev only"}]}]}]}, {"type": "message", "user": "U04G288E8BW", "upload": false, "ts": "1676565268.000000", "text": "no I mean we'll be running Mar test cases in mar.uat and other regression in iris.uat but if for example sit will be finally complete in 2 weeks it will be quite hard to switch regression run there", "previous": {"text": "no I mean we'll be running Mar test cases in mar.uat and other regression in iris.uat but if for example sit will be finally complete in 2 weeks it will be quite hard to switch regression there", "blocks": [{"type": "rich_text", "block_id": "nTl", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "no I mean we'll be running Mar test cases in mar.uat and other regression in iris.uat but if for example sit will be finally complete in 2 weeks it will be quite hard to switch regression there"}]}]}]}, "original_ts": "1676565237.079599", "subtype": "message_changed", "editor_id": "U04G288E8BW", "blocks": [{"type": "rich_text", "block_id": "Goa", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "no I mean we'll be running Mar test cases in mar.uat and other regression in iris.uat but if for example sit will be finally complete in 2 weeks it will be quite hard to switch regression run there"}]}]}]}, {"type": "message", "user": "U4PA3EVJT", "upload": false, "ts": "1676563851.000000", "text": "we can do MAR Unit tests in uat\neasier as all the tests are already in mar.uat", "previous": {"text": "we can do MAR Unit tests in uat\neasier as all the tests are already in <http://mar.ua|mar.ua> t", "blocks": [{"type": "rich_text", "block_id": "bhcO", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "we can do MAR Unit tests in uat\neasier as all the tests are already in "}, {"type": "link", "url": "http://mar.ua", "text": "mar.ua"}, {"type": "text", "text": " t"}]}]}]}, "original_ts": "1676563846.496209", "subtype": "message_changed", "editor_id": "U4PA3EVJT", "blocks": [{"type": "rich_text", "block_id": "8pQl", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "we can do MAR Unit tests in uat\neasier as all the tests are already in mar.uat"}]}]}]}, {"type": "message", "user": "U4XDQTVFA", "upload": false, "ts": "1676563572.000000", "text": "The release candidate has been deployed to the UAT stack", "previous": {"text": "The release candidate has been deployed to the UAT stacks", "blocks": [{"type": "rich_text", "block_id": "tD/f", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "The release candidate has been deployed to the UAT stacks"}]}]}]}, "original_ts": "1676563571.222299", "subtype": "message_changed", "editor_id": "U4XDQTVFA", "blocks": [{"type": "rich_text", "block_id": "oYnkq", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "The release candidate has been deployed to the UAT stack"}]}]}]}]}
{"messages": [{"client_msg_id": "48a767cb-e1e5-444d-9de4-623d60d65944", "type": "message", "text": "derived from test results available at <https://steeleye.atlassian.net/wiki/spaces/EN/pages/2700148852/BK-25+Tests>", "user": "U046960HEJU", "ts": "1676637139.232029", "blocks": [{"type": "rich_text", "block_id": "VrDo", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "derived from test results available at "}, {"type": "link", "url": "https://steeleye.atlassian.net/wiki/spaces/EN/pages/2700148852/BK-25+Tests"}]}]}], "team": "T3P0PGX18", "attachments": [{"id": 1, "blocks": [{"type": "section", "block_id": "page:{\"contentId\":\"2700148852\",\"instanceId\":\"32768f85-cbed-3e75-bab4-fa14fc6d2490\"}", "text": {"type": "mrkdwn", "text": "*<https://steeleye.atlassian.net/wiki/spaces/EN/pages/2700148852/BK-25+Tests?atlOrigin=eyJpIjoiYzA5YjVhMGUwZmEwNGZkZWJjNDMyNDNmNWUyYzdjOWQiLCJwIjoiY29uZmx1ZW5jZS1jaGF0cy1pbnQifQ|BK-25 Tests>* in *<https://steeleye.atlassian.net/wiki/spaces/EN?atlOrigin=eyJpIjoiNjY4NDRlZmQwZTI1NGQzOTgwM2RjMjA0MWI5Njg5OGIiLCJwIjoiY29uZmx1ZW5jZS1jaGF0cy1pbnQifQ|Engineering>*", "verbatim": false}}, {"type": "context", "block_id": "zbKEg", "elements": [{"type": "mrkdwn", "text": "Last updated 23 minutes ago by <https://steeleye.atlassian.net/people/************************|victor popescu> | 1 comment", "verbatim": false}]}, {"type": "actions", "block_id": "interactivity:{\"contentType\":\"page\",\"instanceId\":\"32768f85-cbed-3e75-bab4-fa14fc6d2490\",\"id\":\"2700148852\",\"source\":\"unfurl\"}", "elements": [{"type": "button", "action_id": "like", "text": {"type": "plain_text", "text": ":thumbsup: Like page", "emoji": true}, "style": "primary"}, {"type": "button", "action_id": "watchContent", "text": {"type": "plain_text", "text": "Watch", "emoji": true}}, {"type": "static_select", "action_id": "overflowActions", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "addComment"}, {"text": {"type": "plain_text", "text": "Star", "emoji": true}, "value": "saveContentForLater"}, {"text": {"type": "plain_text", "text": "Share page", "emoji": true}, "value": "shareContent"}]}]}], "color": "#2684ff", "fallback": "BK-25 Tests in Engineering", "bot_id": "BQ8HY15KL", "app_unfurl_url": "https://steeleye.atlassian.net/wiki/spaces/EN/pages/2700148852/BK-25+Tests", "is_app_unfurl": true}]}, {"client_msg_id": "bdf22a06-8891-4b8e-b39a-b7acb98f0e2a", "type": "message", "text": "conclusions from BK-25", "user": "U046960HEJU", "ts": "1676637127.978869", "blocks": [{"type": "rich_text", "block_id": "Pznsx", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "conclusions from BK-25"}]}]}], "team": "T3P0PGX18"}, {"client_msg_id": "5593640e-3038-4f87-b99d-3e3b66ef97e3", "type": "message", "text": "<https://steeleye.atlassian.net/wiki/spaces/EN/pages/2733408270/BK-25+Approach+Components+and+Conclusions>", "user": "U046960HEJU", "ts": "1676637118.918699", "blocks": [{"type": "rich_text", "block_id": "9cqDw", "elements": [{"type": "rich_text_section", "elements": [{"type": "link", "url": "https://steeleye.atlassian.net/wiki/spaces/EN/pages/2733408270/BK-25+Approach+Components+and+Conclusions"}]}]}], "team": "T3P0PGX18", "attachments": [{"id": 1, "blocks": [{"type": "section", "block_id": "page:{\"contentId\":\"2733408270\",\"instanceId\":\"32768f85-cbed-3e75-bab4-fa14fc6d2490\"}", "text": {"type": "mrkdwn", "text": "*<https://steeleye.atlassian.net/wiki/spaces/EN/pages/2733408270/BK-25+Approach%2C+Components+and+Conclusions?atlOrigin=eyJpIjoiMzkyYjVjZjgxYzA4NDkxZWJiNzc1ZTE5ZjU3Y2E3ZDQiLCJwIjoiY29uZmx1ZW5jZS1jaGF0cy1pbnQifQ|BK-25 Approach, Components and Conclusions>* in *<https://steeleye.atlassian.net/wiki/spaces/EN?atlOrigin=eyJpIjoiODQzM2M1YzVhOGQzNGJjYzg4ZGIyY2VkMGViOTQzNTgiLCJwIjoiY29uZmx1ZW5jZS1jaGF0cy1pbnQifQ|Engineering>*", "verbatim": false}}, {"type": "context", "block_id": "OdU", "elements": [{"type": "mrkdwn", "text": "Last updated 3 minutes ago by <https://steeleye.atlassian.net/people/************************|victor popescu>", "verbatim": false}]}, {"type": "actions", "block_id": "interactivity:{\"contentType\":\"page\",\"instanceId\":\"32768f85-cbed-3e75-bab4-fa14fc6d2490\",\"id\":\"2733408270\",\"source\":\"unfurl\"}", "elements": [{"type": "button", "action_id": "like", "text": {"type": "plain_text", "text": ":thumbsup: Like page", "emoji": true}, "style": "primary"}, {"type": "button", "action_id": "watchContent", "text": {"type": "plain_text", "text": "Watch", "emoji": true}}, {"type": "static_select", "action_id": "overflowActions", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "addComment"}, {"text": {"type": "plain_text", "text": "Star", "emoji": true}, "value": "saveContentForLater"}, {"text": {"type": "plain_text", "text": "Share page", "emoji": true}, "value": "shareContent"}]}]}], "color": "#2684ff", "fallback": "BK-25 Approach, Components and Conclusions in Engineering", "bot_id": "BQ8HY15KL", "app_unfurl_url": "https://steeleye.atlassian.net/wiki/spaces/EN/pages/2733408270/BK-25+Approach+Components+and+Conclusions", "is_app_unfurl": true}]}], "members": ["U4PA3EVJT", "U014MHCS8NR", "U01DFSQNYNB", "U02BKE4MRSN", "U03G5R5PJRM", "U046960HEJU", "U04CDMJJUDB"], "info": {"id": "C04HPCUNNLU", "name": "se-benchmark-bk-25", "created": 1672918318, "member_count": 7, "is_general": false, "is_private": false, "is_im": false, "is_mpim": false, "is_file": false, "is_deleted": false, "is_archived": false, "creator": "U046960HEJU", "is_moved": false, "is_shared": false, "name_normalized": "se-benchmark-bk-25", "is_global_shared": false, "is_org_shared": false, "is_org_mandatory": false, "is_org_default": false, "previous_names": [], "purpose": {"text": "<https://steeleye.atlassian.net/jira/software/c/projects/BK/boards/170/roadmap?selectedIssue=BK-25>", "set_by": "U046960HEJU", "date_set": 1672918318}, "topic": {"text": "", "set_by": "", "date_set": 0}, "retention": {"type": "default", "duration": 0}, "shared": {"shared_team_ids": [], "connected_team_ids": [], "internal_team_ids": [], "connected_limited_team_ids": []}, "has_guests": false}, "edits": [{"type": "message", "user": "U046960HEJU", "upload": false, "ts": "1676637140.000000", "text": "derived from test results available at <https://steeleye.atlassian.net/wiki/spaces/EN/pages/2700148852/BK-25+Tests>", "previous": {"text": "derived from test results available at <https://steeleye.atlassian.net/wiki/spaces/EN/pages/2700148852/BK-25+Tests>", "blocks": [{"type": "rich_text", "block_id": "VrDo", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "derived from test results available at "}, {"type": "link", "url": "https://steeleye.atlassian.net/wiki/spaces/EN/pages/2700148852/BK-25+Tests"}]}]}]}, "original_ts": "1676637139.232029", "subtype": "message_changed", "editor_id": "U00", "attachments": [{"id": 1, "blocks": [{"type": "section", "block_id": "page:{\"contentId\":\"2700148852\",\"instanceId\":\"32768f85-cbed-3e75-bab4-fa14fc6d2490\"}", "text": {"type": "mrkdwn", "text": "*<https://steeleye.atlassian.net/wiki/spaces/EN/pages/2700148852/BK-25+Tests?atlOrigin=eyJpIjoiYzA5YjVhMGUwZmEwNGZkZWJjNDMyNDNmNWUyYzdjOWQiLCJwIjoiY29uZmx1ZW5jZS1jaGF0cy1pbnQifQ|BK-25 Tests>* in *<https://steeleye.atlassian.net/wiki/spaces/EN?atlOrigin=eyJpIjoiNjY4NDRlZmQwZTI1NGQzOTgwM2RjMjA0MWI5Njg5OGIiLCJwIjoiY29uZmx1ZW5jZS1jaGF0cy1pbnQifQ|Engineering>*", "verbatim": false}}, {"type": "context", "block_id": "zbKEg", "elements": [{"type": "mrkdwn", "text": "Last updated 23 minutes ago by <https://steeleye.atlassian.net/people/************************|victor popescu> | 1 comment", "verbatim": false}]}, {"type": "actions", "block_id": "interactivity:{\"contentType\":\"page\",\"instanceId\":\"32768f85-cbed-3e75-bab4-fa14fc6d2490\",\"id\":\"2700148852\",\"source\":\"unfurl\"}", "elements": [{"type": "button", "action_id": "like", "text": {"type": "plain_text", "text": ":thumbsup: Like page", "emoji": true}, "style": "primary"}, {"type": "button", "action_id": "watchContent", "text": {"type": "plain_text", "text": "Watch", "emoji": true}}, {"type": "static_select", "action_id": "overflowActions", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "addComment"}, {"text": {"type": "plain_text", "text": "Star", "emoji": true}, "value": "saveContentForLater"}, {"text": {"type": "plain_text", "text": "Share page", "emoji": true}, "value": "shareContent"}]}]}], "color": "#2684ff", "fallback": "BK-25 Tests in Engineering", "bot_id": "BQ8HY15KL", "app_unfurl_url": "https://steeleye.atlassian.net/wiki/spaces/EN/pages/2700148852/BK-25+Tests", "is_app_unfurl": true}], "blocks": [{"type": "rich_text", "block_id": "VrDo", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "derived from test results available at "}, {"type": "link", "url": "https://steeleye.atlassian.net/wiki/spaces/EN/pages/2700148852/BK-25+Tests"}]}]}]}, {"type": "message", "user": "U046960HEJU", "upload": false, "ts": "1676637120.000000", "text": "<https://steeleye.atlassian.net/wiki/spaces/EN/pages/2733408270/BK-25+Approach+Components+and+Conclusions>", "previous": {"text": "<https://steeleye.atlassian.net/wiki/spaces/EN/pages/2733408270/BK-25+Approach+Components+and+Conclusions>", "blocks": [{"type": "rich_text", "block_id": "9cqDw", "elements": [{"type": "rich_text_section", "elements": [{"type": "link", "url": "https://steeleye.atlassian.net/wiki/spaces/EN/pages/2733408270/BK-25+Approach+Components+and+Conclusions"}]}]}]}, "original_ts": "1676637118.918699", "subtype": "message_changed", "editor_id": "U00", "attachments": [{"id": 1, "blocks": [{"type": "section", "block_id": "page:{\"contentId\":\"2733408270\",\"instanceId\":\"32768f85-cbed-3e75-bab4-fa14fc6d2490\"}", "text": {"type": "mrkdwn", "text": "*<https://steeleye.atlassian.net/wiki/spaces/EN/pages/2733408270/BK-25+Approach%2C+Components+and+Conclusions?atlOrigin=eyJpIjoiMzkyYjVjZjgxYzA4NDkxZWJiNzc1ZTE5ZjU3Y2E3ZDQiLCJwIjoiY29uZmx1ZW5jZS1jaGF0cy1pbnQifQ|BK-25 Approach, Components and Conclusions>* in *<https://steeleye.atlassian.net/wiki/spaces/EN?atlOrigin=eyJpIjoiODQzM2M1YzVhOGQzNGJjYzg4ZGIyY2VkMGViOTQzNTgiLCJwIjoiY29uZmx1ZW5jZS1jaGF0cy1pbnQifQ|Engineering>*", "verbatim": false}}, {"type": "context", "block_id": "OdU", "elements": [{"type": "mrkdwn", "text": "Last updated 3 minutes ago by <https://steeleye.atlassian.net/people/************************|victor popescu>", "verbatim": false}]}, {"type": "actions", "block_id": "interactivity:{\"contentType\":\"page\",\"instanceId\":\"32768f85-cbed-3e75-bab4-fa14fc6d2490\",\"id\":\"2733408270\",\"source\":\"unfurl\"}", "elements": [{"type": "button", "action_id": "like", "text": {"type": "plain_text", "text": ":thumbsup: Like page", "emoji": true}, "style": "primary"}, {"type": "button", "action_id": "watchContent", "text": {"type": "plain_text", "text": "Watch", "emoji": true}}, {"type": "static_select", "action_id": "overflowActions", "placeholder": {"type": "plain_text", "text": "More actions...", "emoji": true}, "options": [{"text": {"type": "plain_text", "text": "Comment", "emoji": true}, "value": "addComment"}, {"text": {"type": "plain_text", "text": "Star", "emoji": true}, "value": "saveContentForLater"}, {"text": {"type": "plain_text", "text": "Share page", "emoji": true}, "value": "shareContent"}]}]}], "color": "#2684ff", "fallback": "BK-25 Approach, Components and Conclusions in Engineering", "bot_id": "BQ8HY15KL", "app_unfurl_url": "https://steeleye.atlassian.net/wiki/spaces/EN/pages/2733408270/BK-25+Approach+Components+and+Conclusions", "is_app_unfurl": true}], "blocks": [{"type": "rich_text", "block_id": "9cqDw", "elements": [{"type": "rich_text_section", "elements": [{"type": "link", "url": "https://steeleye.atlassian.net/wiki/spaces/EN/pages/2733408270/BK-25+Approach+Components+and+Conclusions"}]}]}]}]}
{"messages": [{"type": "message", "subtype": "channel_join", "ts": "1676646081.655509", "user": "U03P9RN0878", "text": "<@U03P9RN0878> has joined the channel", "inviter": "U02TAHUHMR6"}], "members": ["U3NA51EUR", "U4E48R19P", "U4FG72DQQ", "U4PA3EVJT", "U4XDQTVFA", "U8X0K6K7E", "U9G6MC36V", "U9S42HJDR", "UGH78B1PG", "UJ21VFGHG", "UMBBL055L", "UMRDJ231P", "UNDB5EPK7", "UTFLSMCGM", "UUKKV7332", "UUQ1HG2SW", "UVDCV9LQ0", "U010M7KJX2S", "U012K27RE9E", "U0148KU07UJ", "U018725FCQ6", "U01AMPACW4Q", "U01AWB8GG1F", "U01DFSQNYNB", "U01QHBXEMK4", "U01T3T2LSTH", "U01TDJ03WHW", "U020CT12DQF", "U023KEU40SZ", "U027KV4NYP3", "U0282227N83", "U02BAD3K1GV", "U02F0NXCB3M", "U02FT5U6RRP", "U02JDB3USF4", "U02S4EAL82Z", "U02TAHUHMR6", "U031NA2GCAC", "U039GG2J75L", "U03ACU27XL0", "U03AQU936HG", "U03GSQHTRC4", "U03JCM0SG0G", "U03KBQ0PP5Z", "U03P9RN0878", "U03S1UBDFGX", "U03SLP52804", "U0421MMLHJ9", "U045BCF8WCX", "U045UCU2XV2", "U046960HEJU", "U046FKYJXTJ", "U04BFE9NZ6H", "U04D6KQ9ADN", "U04E0EYDG93", "U04HPASMUKF", "U04J9DQ2U9F", "U04LEV1U8AK", "U04M61G19N2"], "info": {"id": "CGPDQH0MA", "name": "foodies", "created": 1551792834, "member_count": 59, "is_general": false, "is_private": false, "is_im": false, "is_mpim": false, "is_file": false, "is_deleted": false, "is_archived": false, "creator": "U9G6MC36V", "is_moved": false, "is_shared": false, "name_normalized": "foodies", "is_global_shared": false, "is_org_shared": false, "is_org_mandatory": false, "is_org_default": false, "previous_names": [], "purpose": {"text": "to discuss and organise trips to epic food joints in London", "set_by": "U9G6MC36V", "date_set": 1551792835}, "topic": {"text": "", "set_by": "", "date_set": 0}, "retention": {"type": "default", "duration": 0}, "shared": {"shared_team_ids": [], "connected_team_ids": [], "internal_team_ids": [], "connected_limited_team_ids": []}, "has_guests": false}, "edits": []}
{"messages": [{"client_msg_id": "0def2041-64a3-4a2b-b538-21bd2d4a444b", "type": "message", "text": "I have updated the position spec as well", "user": "U02JDB3USF4", "ts": "1676646587.746679", "blocks": [{"type": "rich_text", "block_id": "5nu", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I have updated the position spec as well"}]}]}], "team": "T3P0PGX18"}, {"client_msg_id": "52de11ad-08cb-45f1-ae4a-6cd80766724c", "type": "message", "text": "Hello <@U02BKE4MRSN>, I got the answer:\n1. if the ISIN is not 12 characters then reject/skip the record,\n2. No, let's not make any verification on the currency,\n3. Please remove `direction` from ID Props but keep it as mandatory.", "user": "U02JDB3USF4", "ts": "1676646576.544119", "blocks": [{"type": "rich_text", "block_id": "=Jcra", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Hello "}, {"type": "user", "user_id": "U02BKE4MRSN"}, {"type": "text", "text": ", I got the answer:\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "if the ISIN is not 12 characters then reject/skip the record,"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "No, let's not make any verification on the currency,"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Please remove "}, {"type": "text", "text": "direction", "style": {"code": true}}, {"type": "text", "text": " from ID Props but keep it as mandatory."}]}], "style": "ordered", "indent": 0, "border": 0}]}], "team": "T3P0PGX18", "edited": {"user": "U02JDB3USF4", "ts": "1676647229.000000"}}, {"client_msg_id": "d6a6de3d-1bcd-463f-9c27-70636a5c5b37", "type": "message", "text": "<@U4PA3EVJT> <@U02JDB3USF4>\nfrom our discussion please let me know if we need these changes in the specs, based on the response I can make the changes first thing Monday morning\n\n1. Should we use instrument fallback or skip rows where no instrument was found on srp?\n2. Should we verify that `instrument.notionalCurrency1` must match with `amount.nativeCurrency` , else drop the record?\n3. Should we remove `direction` from ID props?\n_Amount, Quanity should be always position - this change is done and deployed to dev._", "user": "U02BKE4MRSN", "ts": "1676644745.198489", "blocks": [{"type": "rich_text", "block_id": "gEMuz", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U4PA3EVJT"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U02JDB3USF4"}, {"type": "text", "text": "\nfrom our discussion please let me know if we need these changes in the specs, based on the response I can make the changes first thing Monday morning\n\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Should we use instrument fallback or skip rows where no instrument was found on srp?"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Should we verify that "}, {"type": "text", "text": "instrument.notionalCurrency1", "style": {"code": true}}, {"type": "text", "text": " must match with "}, {"type": "text", "text": "amount.nativeCurrency", "style": {"code": true}}, {"type": "text", "text": " , else drop the record?"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Should we remove "}, {"type": "text", "text": "direction", "style": {"code": true}}, {"type": "text", "text": " from ID props?"}]}], "style": "ordered", "indent": 0, "border": 0}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "\n"}, {"type": "text", "text": "Amount, Quanity should be always position - this change is done and deployed to dev.", "style": {"italic": true}}]}]}], "team": "T3P0PGX18", "edited": {"user": "U02BKE4MRSN", "ts": "1676644812.000000"}}, {"client_msg_id": "149ee541-b843-4b2c-a5cf-e46c80ca3098", "type": "message", "text": "thanks", "user": "U02JDB3USF4", "ts": "1676558038.204389", "blocks": [{"type": "rich_text", "block_id": "76AUs", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "thanks"}]}]}], "team": "T3P0PGX18"}, {"type": "message", "text": "<@U02JDB3USF4> <@U4PA3EVJT> Here's the output from elastic post ingestion. I'm also attaching the input source file to ensure we've not missed ingesting any record", "files": [{"id": "F04QMRCULRW", "created": 1676556827, "timestamp": 1676556827, "name": "response_updated.csv", "title": "response_updated.csv", "mimetype": "text/csv", "filetype": "csv", "pretty_type": "CSV", "user": "U02BKE4MRSN", "user_team": "T3P0PGX18", "editable": true, "size": 37380, "mode": "snippet", "is_external": false, "external_type": "", "is_public": true, "public_url_shared": false, "display_as_bot": false, "username": "", "url_private": "https://files.slack.com/files-pri/T3P0PGX18-F04QMRCULRW/response_updated.csv", "url_private_download": "https://files.slack.com/files-pri/T3P0PGX18-F04QMRCULRW/download/response_updated.csv", "permalink": "https://steeleye.slack.com/files/U02BKE4MRSN/F04QMRCULRW/response_updated.csv", "permalink_public": "https://slack-files.com/T3P0PGX18-F04QMRCULRW-9395baa4d5", "edit_link": "https://steeleye.slack.com/files/U02BKE4MRSN/F04QMRCULRW/response_updated.csv/edit", "has_rich_preview": false, "file_access": "visible"}], "upload": false, "user": "U02BKE4MRSN", "display_as_bot": false, "ts": "1676556886.279729", "blocks": [{"type": "rich_text", "block_id": "W=D7", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U02JDB3USF4"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U4PA3EVJT"}, {"type": "text", "text": " Here's the output from elastic post ingestion. I'm also attaching the input source file to ensure we've not missed ingesting any record"}]}]}], "client_msg_id": "1aafdd4a-5dfe-4558-9c29-7974726ffc7a", "file_uploads": [{"id": "F04QMRCULRW", "name": "name", "title": "title", "mimetype": "mimetype", "user": "user", "s3_path": "s3://test.dev.steeleye.co/aries/ingress/depository/slack_chat_poll/random.txt", "filetype": "filetype", "pretty_type": "pretty_type"}]}], "members": ["U3NA51EUR", "U4PA3EVJT", "U4XDQTVFA", "UGH78B1PG", "UR71HD9UZ", "U011XA81ZSA", "U012K27RE9E", "U013W0HT6KW", "U01AWB8GG1F", "U01DFSQNYNB", "U025DCU40CA", "U02BKE4MRSN", "U02JDB3USF4", "U04BFE9NZ6H", "U04CDMJJUDB"], "info": {"id": "C04EDMXT9BJ", "name": "initiative-position-model", "created": 1670222831, "member_count": 15, "is_general": false, "is_private": false, "is_im": false, "is_mpim": false, "is_file": false, "is_deleted": false, "is_archived": false, "creator": "U02BKE4MRSN", "is_moved": false, "is_shared": false, "name_normalized": "initiative-position-model", "is_global_shared": false, "is_org_shared": false, "is_org_mandatory": false, "is_org_default": false, "previous_names": ["position-model"], "purpose": {"text": "", "set_by": "", "date_set": 0}, "topic": {"text": "", "set_by": "", "date_set": 0}, "retention": {"type": "default", "duration": 0}, "shared": {"shared_team_ids": [], "connected_team_ids": [], "internal_team_ids": [], "connected_limited_team_ids": []}, "has_guests": false}, "edits": [{"type": "message", "user": "U02JDB3USF4", "upload": false, "ts": "1676647229.000000", "text": "Hello <@U02BKE4MRSN>, I got the answer:\n1. if the ISIN is not 12 characters then reject/skip the record,\n2. No, let's not make any verification on the currency,\n3. Please remove `direction` from ID Props but keep it as mandatory.", "previous": {"text": "Hello <@U02BKE4MRSN>, I got the answer:\n1. if the ISIN is not found then reject/skip the record,\n2. No, let's not make any verification on the currency,\n3. Please remove `direction` from ID Props but keep it as mandatory.", "blocks": [{"type": "rich_text", "block_id": "8HN", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Hello "}, {"type": "user", "user_id": "U02BKE4MRSN"}, {"type": "text", "text": ", I got the answer:\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "if the ISIN is not found then reject/skip the record,"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "No, let's not make any verification on the currency,"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Please remove "}, {"type": "text", "text": "direction", "style": {"code": true}}, {"type": "text", "text": " from ID Props but keep it as mandatory."}]}], "style": "ordered", "indent": 0, "border": 0}]}]}, "original_ts": "1676646576.544119", "subtype": "message_changed", "editor_id": "U02JDB3USF4", "blocks": [{"type": "rich_text", "block_id": "=Jcra", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Hello "}, {"type": "user", "user_id": "U02BKE4MRSN"}, {"type": "text", "text": ", I got the answer:\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "if the ISIN is not 12 characters then reject/skip the record,"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "No, let's not make any verification on the currency,"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Please remove "}, {"type": "text", "text": "direction", "style": {"code": true}}, {"type": "text", "text": " from ID Props but keep it as mandatory."}]}], "style": "ordered", "indent": 0, "border": 0}]}]}, {"type": "message", "user": "U02BKE4MRSN", "upload": false, "ts": "1676644812.000000", "text": "<@U4PA3EVJT> <@U02JDB3USF4>\nfrom our discussion please let me know if we need these changes in the specs, based on the response I can make the changes first thing Monday morning\n\n1. Should we use instrument fallback or skip rows where no instrument was found on srp?\n2. Should we verify that `instrument.notionalCurrency1` must match with `amount.nativeCurrency` , else drop the record?\n3. Should we remove `direction` from ID props?\n_Amount, Quanity should be always position - this change is done and deployed to dev._", "previous": {"text": "<@U4PA3EVJT> <@U02JDB3USF4>\nfrom our discussion please let me know if we need these changes in the specs, based on the response I can make the changes first thing Monday morning\n\n1. Should we use instrument fallback or skip rows where no instrument was found on srp?\n2. Should we verify that `instrument.notionalCurrency1` must match with `amount.nativeCurrency` , else drop the record?\n3. Should we remove `direction` from ID props?\n_Amount, Quanityt should be always position - this change is done and deployed to dev._", "blocks": [{"type": "rich_text", "block_id": "ri4N", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U4PA3EVJT"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U02JDB3USF4"}, {"type": "text", "text": "\nfrom our discussion please let me know if we need these changes in the specs, based on the response I can make the changes first thing Monday morning\n\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Should we use instrument fallback or skip rows where no instrument was found on srp?"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Should we verify that "}, {"type": "text", "text": "instrument.notionalCurrency1", "style": {"code": true}}, {"type": "text", "text": " must match with "}, {"type": "text", "text": "amount.nativeCurrency", "style": {"code": true}}, {"type": "text", "text": " , else drop the record?"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Should we remove "}, {"type": "text", "text": "direction", "style": {"code": true}}, {"type": "text", "text": " from ID props?"}]}], "style": "ordered", "indent": 0, "border": 0}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "\n"}, {"type": "text", "text": "Amount, Quanityt should be always position - this change is done and deployed to dev.", "style": {"italic": true}}]}]}]}, "original_ts": "1676644745.198489", "subtype": "message_changed", "editor_id": "U02BKE4MRSN", "blocks": [{"type": "rich_text", "block_id": "gEMuz", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U4PA3EVJT"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U02JDB3USF4"}, {"type": "text", "text": "\nfrom our discussion please let me know if we need these changes in the specs, based on the response I can make the changes first thing Monday morning\n\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Should we use instrument fallback or skip rows where no instrument was found on srp?"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Should we verify that "}, {"type": "text", "text": "instrument.notionalCurrency1", "style": {"code": true}}, {"type": "text", "text": " must match with "}, {"type": "text", "text": "amount.nativeCurrency", "style": {"code": true}}, {"type": "text", "text": " , else drop the record?"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Should we remove "}, {"type": "text", "text": "direction", "style": {"code": true}}, {"type": "text", "text": " from ID props?"}]}], "style": "ordered", "indent": 0, "border": 0}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "\n"}, {"type": "text", "text": "Amount, Quanity should be always position - this change is done and deployed to dev.", "style": {"italic": true}}]}]}]}, {"type": "message", "user": "U02BKE4MRSN", "upload": false, "ts": "1676644806.000000", "text": "<@U4PA3EVJT> <@U02JDB3USF4>\nfrom our discussion please let me know if we need these changes in the specs, based on the response I can make the changes first thing Monday morning\n\n1. Should we use instrument fallback or skip rows where no instrument was found on srp?\n2. Should we verify that `instrument.notionalCurrency1` must match with `amount.nativeCurrency` , else drop the record?\n3. Should we remove `direction` from ID props?\n_Amount, Quanityt should be always position - this change is done and deployed to dev._", "previous": {"text": "<@U4PA3EVJT> <@U02JDB3USF4>\nfrom our discussion please let me know if we need these changes in the specs, based on the response I can make the changes first thing Monday morning\n\n1. Should we use instrument fallback or skip rows where no instrument was found on srp?\n2. Should we verify that `instrument.notionalCurrency1` must match with `amount.nativeCurrency` , else drop the record?\n3. Should we remove `direction` from ID props?\n_Amount should be always position - this change is done and deployed to dev._", "blocks": [{"type": "rich_text", "block_id": "aGPA", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U4PA3EVJT"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U02JDB3USF4"}, {"type": "text", "text": "\nfrom our discussion please let me know if we need these changes in the specs, based on the response I can make the changes first thing Monday morning\n\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Should we use instrument fallback or skip rows where no instrument was found on srp?"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Should we verify that "}, {"type": "text", "text": "instrument.notionalCurrency1", "style": {"code": true}}, {"type": "text", "text": " must match with "}, {"type": "text", "text": "amount.nativeCurrency", "style": {"code": true}}, {"type": "text", "text": " , else drop the record?"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Should we remove "}, {"type": "text", "text": "direction", "style": {"code": true}}, {"type": "text", "text": " from ID props?"}]}], "style": "ordered", "indent": 0, "border": 0}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "\n"}, {"type": "text", "text": "Amount should be always position - this change is done and deployed to dev.", "style": {"italic": true}}]}]}]}, "original_ts": "1676644745.198489", "subtype": "message_changed", "editor_id": "U02BKE4MRSN", "blocks": [{"type": "rich_text", "block_id": "ri4N", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U4PA3EVJT"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U02JDB3USF4"}, {"type": "text", "text": "\nfrom our discussion please let me know if we need these changes in the specs, based on the response I can make the changes first thing Monday morning\n\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Should we use instrument fallback or skip rows where no instrument was found on srp?"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Should we verify that "}, {"type": "text", "text": "instrument.notionalCurrency1", "style": {"code": true}}, {"type": "text", "text": " must match with "}, {"type": "text", "text": "amount.nativeCurrency", "style": {"code": true}}, {"type": "text", "text": " , else drop the record?"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Should we remove "}, {"type": "text", "text": "direction", "style": {"code": true}}, {"type": "text", "text": " from ID props?"}]}], "style": "ordered", "indent": 0, "border": 0}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "\n"}, {"type": "text", "text": "Amount, Quanityt should be always position - this change is done and deployed to dev.", "style": {"italic": true}}]}]}]}, {"type": "message", "user": "U02BKE4MRSN", "upload": false, "ts": "1676644798.000000", "text": "<@U4PA3EVJT> <@U02JDB3USF4>\nfrom our discussion please let me know if we need these changes in the specs, based on the response I can make the changes first thing Monday morning\n\n1. Should we use instrument fallback or skip rows where no instrument was found on srp?\n2. Should we verify that `instrument.notionalCurrency1` must match with `amount.nativeCurrency` , else drop the record?\n3. Should we remove `direction` from ID props?\n_Amount should be always position - this change is done and deployed to dev._", "previous": {"text": "<@U4PA3EVJT> <@U02JDB3USF4>\nfrom our discussion please let me know if we need these changes in the specs, based on the response I can make the changes first thing Monday morning\n\n1. Should we use instrument fallback or skip rows where no instrument was found on srp?\n2. Should we verify that `instrument.notionalCurrency1` must match with `amount.nativeCurrency` , else drop the record?\n3. Should we remove `direction` from ID props?\nAmount should be always position - this change is done and deployed to dev.", "blocks": [{"type": "rich_text", "block_id": "/j/h", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U4PA3EVJT"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U02JDB3USF4"}, {"type": "text", "text": "\nfrom our discussion please let me know if we need these changes in the specs, based on the response I can make the changes first thing Monday morning\n\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Should we use instrument fallback or skip rows where no instrument was found on srp?"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Should we verify that "}, {"type": "text", "text": "instrument.notionalCurrency1", "style": {"code": true}}, {"type": "text", "text": " must match with "}, {"type": "text", "text": "amount.nativeCurrency", "style": {"code": true}}, {"type": "text", "text": " , else drop the record?"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Should we remove "}, {"type": "text", "text": "direction", "style": {"code": true}}, {"type": "text", "text": " from ID props?"}]}], "style": "ordered", "indent": 0, "border": 0}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "\nAmount should be always position - this change is done and deployed to dev."}]}]}]}, "original_ts": "1676644745.198489", "subtype": "message_changed", "editor_id": "U02BKE4MRSN", "blocks": [{"type": "rich_text", "block_id": "aGPA", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U4PA3EVJT"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U02JDB3USF4"}, {"type": "text", "text": "\nfrom our discussion please let me know if we need these changes in the specs, based on the response I can make the changes first thing Monday morning\n\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Should we use instrument fallback or skip rows where no instrument was found on srp?"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Should we verify that "}, {"type": "text", "text": "instrument.notionalCurrency1", "style": {"code": true}}, {"type": "text", "text": " must match with "}, {"type": "text", "text": "amount.nativeCurrency", "style": {"code": true}}, {"type": "text", "text": " , else drop the record?"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Should we remove "}, {"type": "text", "text": "direction", "style": {"code": true}}, {"type": "text", "text": " from ID props?"}]}], "style": "ordered", "indent": 0, "border": 0}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "\n"}, {"type": "text", "text": "Amount should be always position - this change is done and deployed to dev.", "style": {"italic": true}}]}]}]}, {"type": "message", "user": "U02BKE4MRSN", "upload": false, "ts": "1676644793.000000", "text": "<@U4PA3EVJT> <@U02JDB3USF4>\nfrom our discussion please let me know if we need these changes in the specs, based on the response I can make the changes first thing Monday morning\n\n1. Should we use instrument fallback or skip rows where no instrument was found on srp?\n2. Should we verify that `instrument.notionalCurrency1` must match with `amount.nativeCurrency` , else drop the record?\n3. Should we remove `direction` from ID props?\nAmount should be always position - this change is done and deployed to dev.", "previous": {"text": "<@U4PA3EVJT> <@U02JDB3USF4>\nfrom our discussion please let me know if we need these changes in the specs, based on the response I can make the changes first thing Monday morning\n\n1. Should we use instrument fallback or skip rows where no instrument was found on srp?\n2. Should we verify that `instrument.notionalCurrency1` must match with `amount.nativeCurrency` , else drop the record?\n3. Should we remove `direction` from ID props?\n", "blocks": [{"type": "rich_text", "block_id": "pa0c", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U4PA3EVJT"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U02JDB3USF4"}, {"type": "text", "text": "\nfrom our discussion please let me know if we need these changes in the specs, based on the response I can make the changes first thing Monday morning\n\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Should we use instrument fallback or skip rows where no instrument was found on srp?"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Should we verify that "}, {"type": "text", "text": "instrument.notionalCurrency1", "style": {"code": true}}, {"type": "text", "text": " must match with "}, {"type": "text", "text": "amount.nativeCurrency", "style": {"code": true}}, {"type": "text", "text": " , else drop the record?"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Should we remove "}, {"type": "text", "text": "direction", "style": {"code": true}}, {"type": "text", "text": " from ID props?"}]}], "style": "ordered", "indent": 0, "border": 0}, {"type": "rich_text_section", "elements": []}]}]}, "original_ts": "1676644745.198489", "subtype": "message_changed", "editor_id": "U02BKE4MRSN", "blocks": [{"type": "rich_text", "block_id": "/j/h", "elements": [{"type": "rich_text_section", "elements": [{"type": "user", "user_id": "U4PA3EVJT"}, {"type": "text", "text": " "}, {"type": "user", "user_id": "U02JDB3USF4"}, {"type": "text", "text": "\nfrom our discussion please let me know if we need these changes in the specs, based on the response I can make the changes first thing Monday morning\n\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Should we use instrument fallback or skip rows where no instrument was found on srp?"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Should we verify that "}, {"type": "text", "text": "instrument.notionalCurrency1", "style": {"code": true}}, {"type": "text", "text": " must match with "}, {"type": "text", "text": "amount.nativeCurrency", "style": {"code": true}}, {"type": "text", "text": " , else drop the record?"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Should we remove "}, {"type": "text", "text": "direction", "style": {"code": true}}, {"type": "text", "text": " from ID props?"}]}], "style": "ordered", "indent": 0, "border": 0}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "\nAmount should be always position - this change is done and deployed to dev."}]}]}]}]}
{"messages": [{"client_msg_id": "21185a11-7b18-4aad-9889-dcbb4e321766", "type": "message", "text": "Hello Steeleye Team :wave:\n\nI am reaching out to deliver our newest `api` and `engine` container images, there aren't any changes to the `api.toml` and `engine.toml` files but I have included them for completeness:\n\n*Latest Image Versions* (reflected in the `docker-compose.yml` configuration file, below)*:*\n\u2022 `deepgram/onprem-api:*1.78.0*`\n\u2022 `deepgram/onprem-engine:*3.39.2*`\n*New Configuration Files for Updated Images:*\n\u2022 `docker-compose.yml` \u2014 <https://deepgram-onprem.s3.us-east-2.amazonaws.com/76ad866e684cd0c37146dff918aef07f/config-files/docker-compose.yml>\n\u2022 `api.toml` \u2014 <https://deepgram-onprem.s3.us-east-2.amazonaws.com/76ad866e684cd0c37146dff918aef07f/config-files/api.toml>\n\u2022 `engine.toml` \u2014 <https://deepgram-onprem.s3.us-east-2.amazonaws.com/76ad866e684cd0c37146dff918aef07f/config-files/engine.toml>\n*Steps for update:*\n1. Download the new `docker-compose.yml` file and replace the old one with it.\n2. Ensure that the new `docker-compose.yml` file matches the paths in your local environment.\n3. Download the new api and engine `.toml` configuration files and replace your existing `.toml` files with the new ones\n4. Download the new model files and place them within the `/models` directory specified in your `docker-compose.yml` configuration file\n5. Update your containers using the following command: `docker-compose up -d`\n6. Verify the upgrade was successful by making a `GET` request to the `/v1/models` endpoint (which will return all available ASR models in its response)", "user": "U03BQ29P3JL", "ts": "1676652128.919829", "blocks": [{"type": "rich_text", "block_id": "Dot", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Hello Steeleye Team "}, {"type": "emoji", "name": "wave", "unicode": "1f44b"}, {"type": "text", "text": "\n\nI am reaching out to deliver our newest "}, {"type": "text", "text": "api", "style": {"code": true}}, {"type": "text", "text": " and "}, {"type": "text", "text": "engine", "style": {"code": true}}, {"type": "text", "text": " container images, there aren't any changes to the "}, {"type": "text", "text": "api.toml", "style": {"code": true}}, {"type": "text", "text": " and "}, {"type": "text", "text": "engine.toml", "style": {"code": true}}, {"type": "text", "text": " files but I have included them for completeness:\n\n"}, {"type": "text", "text": "Latest Image Versions ", "style": {"bold": true}}, {"type": "text", "text": "(reflected in the "}, {"type": "text", "text": "docker-compose.yml", "style": {"code": true}}, {"type": "text", "text": " configuration file, below)"}, {"type": "text", "text": ":", "style": {"bold": true}}, {"type": "text", "text": "\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "deepgram/onprem-api:", "style": {"code": true}}, {"type": "text", "text": "1.78.0", "style": {"bold": true, "code": true}}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "deepgram/onprem-engine:", "style": {"code": true}}, {"type": "text", "text": "3.39.2", "style": {"bold": true, "code": true}}]}], "style": "bullet", "indent": 0, "border": 0}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "\n"}, {"type": "text", "text": "New Configuration Files for Updated Images:", "style": {"bold": true}}, {"type": "text", "text": "\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "docker-compose.yml", "style": {"code": true}}, {"type": "text", "text": " \u2014 "}, {"type": "link", "url": "https://deepgram-onprem.s3.us-east-2.amazonaws.com/76ad866e684cd0c37146dff918aef07f/config-files/docker-compose.yml"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "api.toml", "style": {"code": true}}, {"type": "text", "text": " \u2014 "}, {"type": "link", "url": "https://deepgram-onprem.s3.us-east-2.amazonaws.com/76ad866e684cd0c37146dff918aef07f/config-files/api.toml"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "engine.toml", "style": {"code": true}}, {"type": "text", "text": " \u2014 "}, {"type": "link", "url": "https://deepgram-onprem.s3.us-east-2.amazonaws.com/76ad866e684cd0c37146dff918aef07f/config-files/engine.toml"}]}], "style": "bullet", "indent": 0, "border": 0}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "\n"}, {"type": "text", "text": "Steps for update:", "style": {"bold": true}}, {"type": "text", "text": "\n"}]}, {"type": "rich_text_list", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Download the new "}, {"type": "text", "text": "docker-compose.yml", "style": {"code": true}}, {"type": "text", "text": " file and replace the old one with it."}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Ensure that the new "}, {"type": "text", "text": "docker-compose.yml", "style": {"code": true}}, {"type": "text", "text": " file matches the paths in your local environment."}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Download the new api and engine "}, {"type": "text", "text": ".toml", "style": {"code": true}}, {"type": "text", "text": " configuration files and replace your existing "}, {"type": "text", "text": ".toml", "style": {"code": true}}, {"type": "text", "text": " files with the new ones"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Download the new model files and place them within the "}, {"type": "text", "text": "/models", "style": {"code": true}}, {"type": "text", "text": " directory specified in your "}, {"type": "text", "text": "docker-compose.yml", "style": {"code": true}}, {"type": "text", "text": " configuration file"}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Update your containers using the following command: "}, {"type": "text", "text": "docker-compose up -d", "style": {"code": true}}]}, {"type": "rich_text_section", "elements": [{"type": "text", "text": "Verify the upgrade was successful by making a "}, {"type": "text", "text": "GET", "style": {"code": true}}, {"type": "text", "text": " request to the "}, {"type": "text", "text": "/v1/models", "style": {"code": true}}, {"type": "text", "text": " endpoint (which will return all available ASR models in its response)"}]}], "style": "ordered", "indent": 0, "border": 0}]}], "team": "T0LL805A9"}], "members": ["U0LL805AR", "U150KGKBR", "U3NA51EUR", "U4PA3EVJT", "U5USFCN1W", "U9MLKJ4VA", "U9S42HJDR", "UGH78B1PG", "UJ21VFGHG", "UMRDJ231P", "UVDCV9LQ0", "U0117J26859", "U011EBV3Z4J", "U015T5ED2N9", "U01654S7KH8", "U01DFSQNYNB", "U01GE7E3X2T", "U01JDLQ1N2F", "U020CT12DQF", "U021LK91D35", "U027VPMB9QE", "U02SX2Q3EDP", "U02T856SAPJ", "U0318FKTVTN", "U039AASQGMS", "U03A1PZEBDX", "U03BQ29P3JL", "U03MAMA9KRC"], "info": {"id": "C0226UH0ZKK", "name": "support-deepgram", "created": 1620919067, "member_count": 28, "is_general": false, "is_private": false, "is_im": false, "is_mpim": false, "is_file": false, "is_deleted": false, "is_archived": false, "creator": "U01QC90T2BS", "is_moved": false, "is_shared": true, "name_normalized": "support-deepgram", "is_global_shared": false, "is_org_shared": false, "is_org_mandatory": false, "is_org_default": false, "previous_names": [], "purpose": {"text": "", "set_by": "", "date_set": 0}, "topic": {"text": "", "set_by": "", "date_set": 0}, "retention": {"type": "default", "duration": 0}, "shared": {"shared_team_ids": ["T0LL805A9"], "connected_team_ids": ["T0LL805A9", "T3P0PGX18"], "internal_team_ids": [], "connected_limited_team_ids": []}, "conversation_host_id": "T0LL805A9", "has_guests": false}, "edits": []}
{"messages": [{"type": "message", "subtype": "bot_message", "text": "Flow Runner Failed", "ts": "1676892210.105169", "username": "Flow Runner Failed", "bot_id": "B013H935ZRP", "app_id": "A01338VM16Z", "blocks": [{"type": "header", "block_id": "Td34f", "text": {"type": "plain_text", "text": "Details", "emoji": true}}, {"type": "divider", "block_id": "oqZ"}, {"type": "section", "block_id": "kbo", "fields": [{"type": "mrkdwn", "text": "*flow_id*\n<http://steve.uat.steeleye.co:comms-voice-natterbox|steve.uat.steeleye.co:comms-voice-natterbox>", "verbatim": false}, {"type": "mrkdwn", "text": "*time_taken*\n2s", "verbatim": false}]}, {"type": "section", "block_id": "oVY5t", "fields": [{"type": "mrkdwn", "text": "*file_url*\n<s3://steve.uat.steeleye.co/flows/comms-voice-natterbox/2023/01/18/1674031887_4190483.csv>", "verbatim": false}, {"type": "mrkdwn", "text": "*environment*\nuat", "verbatim": false}]}, {"type": "divider", "block_id": "p2k"}, {"type": "section", "block_id": "HoRF", "text": {"type": "mrkdwn", "text": "*aws_env_vars*\n```{\"batch_ce_name\": \"uat-blue-batch-compute-compute-oq\", \"batch_job_array_index\": \"\", \"batch_job_attempt\": \"1\", \"batch_job_id\": \"d82d061b-3a79-412e-8eac-574cfc3e6aef\", \"batch_job_main_node_index\": \"\", \"batch_job_main_node_private_ipv4_address\": \"\", \"batch_jon_node_index\": \"\", \"batch_job_num_nodes\": \"\", \"batch_jq_name\": \"uat-blue-batch-compute-queue\"}```", "verbatim": false}}, {"type": "divider", "block_id": "hS4"}, {"type": "section", "block_id": "ML7Y/", "text": {"type": "mrkdwn", "text": "*flow_details*\n```{\"flow_id\": \"<http://steve.uat.steeleye.co:comms-voice-natterbox|steve.uat.steeleye.co:comms-voice-natterbox>\", \"file_url\": \"<s3://steve.uat.steeleye.co/flows/comms-voice-natterbox/2023/01/18/1674031887_4190483.csv>\", \"audit_id\": \"<http://steve.uat.steeleye.co:flows/comms-voice-natterbox/2023/01/18/1674031887_4190483.csv:1676891943103:_nil_|steve.uat.steeleye.co:flows/comms-voice-natterbox/2023/01/18/1674031887_4190483.csv:1676891943103:_nil_>\", \"email_notification\": true, \"use_dask_executor\": true, \"time_taken\": \"2s\"}```", "verbatim": false}}, {"type": "divider", "block_id": "v0xxl"}, {"type": "section", "block_id": "JY7d", "text": {"type": "mrkdwn", "text": "*traceback*\n```Traceback (most recent call last):\n  File \"/home/<USER>/venv/lib64/python3.7/site-packages/swarm/flow/runner.py\", line 302, in main\n    flow_runner.execute()\n  File \"/home/<USER>/venv/lib64/python3.7/site-packages/swarm/flow/runner.py\", line 85, in execute\n    \"Flow failed.\\n\" + json.dumps(audit_result.get_failed_tasks())\nException: Flow failed.\n{\"AssignMeta\": \"Trigger was \\\"all_successful\\\" but some of the upstream tasks failed.\", \"TriggerWaveformFlow\": \"Trigger was \\\"all_successful\\\" but some of the upstream tasks failed.\", \"ElasticBulkWriter\": \"Trigger was \\\"all_successful\\\" but some of the upstream tasks failed.\", \"FrameConcatenator\": \"Unexpected error: ValueError('No objects to concatenate')\", \"ElasticBulkTransformer\": \"Trigger was \\\"all_successful\\\" but some of the upstream tasks failed.\", \"TranscriptionRouter\": \"Trigger was \\\"all_successful\\\" but some of the upstream tasks failed.\"}\n```", "verbatim": false}}]}, {"type": "message", "subtype": "bot_message", "text": "Flow Runner Failed", "ts": "1676892210.017439", "username": "Flow Runner Failed", "bot_id": "B013H935ZRP", "app_id": "A01338VM16Z", "blocks": [{"type": "header", "block_id": "2oBq", "text": {"type": "plain_text", "text": "Details", "emoji": true}}, {"type": "divider", "block_id": "ZCA"}, {"type": "section", "block_id": "1z4am", "fields": [{"type": "mrkdwn", "text": "*flow_id*\n<http://steve.uat.steeleye.co:comms-voice-natterbox|steve.uat.steeleye.co:comms-voice-natterbox>", "verbatim": false}, {"type": "mrkdwn", "text": "*time_taken*\n1s", "verbatim": false}]}, {"type": "section", "block_id": "2ch", "fields": [{"type": "mrkdwn", "text": "*file_url*\n<s3://steve.uat.steeleye.co/flows/comms-voice-natterbox/2023/01/18/1674057906_481185.csv>", "verbatim": false}, {"type": "mrkdwn", "text": "*environment*\nuat", "verbatim": false}]}, {"type": "divider", "block_id": "wcNZ"}, {"type": "section", "block_id": "vqKt", "text": {"type": "mrkdwn", "text": "*aws_env_vars*\n```{\"batch_ce_name\": \"uat-blue-batch-compute-compute-oq\", \"batch_job_array_index\": \"\", \"batch_job_attempt\": \"1\", \"batch_job_id\": \"34522501-8ce7-43fc-ba5c-4bdc53cc480d\", \"batch_job_main_node_index\": \"\", \"batch_job_main_node_private_ipv4_address\": \"\", \"batch_jon_node_index\": \"\", \"batch_job_num_nodes\": \"\", \"batch_jq_name\": \"uat-blue-batch-compute-queue\"}```", "verbatim": false}}, {"type": "divider", "block_id": "fe47h"}, {"type": "section", "block_id": "JgOW", "text": {"type": "mrkdwn", "text": "*flow_details*\n```{\"flow_id\": \"<http://steve.uat.steeleye.co:comms-voice-natterbox|steve.uat.steeleye.co:comms-voice-natterbox>\", \"file_url\": \"<s3://steve.uat.steeleye.co/flows/comms-voice-natterbox/2023/01/18/1674057906_481185.csv>\", \"audit_id\": \"<http://steve.uat.steeleye.co:flows/comms-voice-natterbox/2023/01/18/1674057906_481185.csv:1676891947770:_nil_|steve.uat.steeleye.co:flows/comms-voice-natterbox/2023/01/18/1674057906_481185.csv:1676891947770:_nil_>\", \"email_notification\": true, \"use_dask_executor\": true, \"time_taken\": \"1s\"}```", "verbatim": false}}, {"type": "divider", "block_id": "=QMk"}, {"type": "section", "block_id": "hDiP", "text": {"type": "mrkdwn", "text": "*traceback*\n```Traceback (most recent call last):\n  File \"/home/<USER>/venv/lib64/python3.7/site-packages/swarm/flow/runner.py\", line 302, in main\n    flow_runner.execute()\n  File \"/home/<USER>/venv/lib64/python3.7/site-packages/swarm/flow/runner.py\", line 85, in execute\n    \"Flow failed.\\n\" + json.dumps(audit_result.get_failed_tasks())\nException: Flow failed.\n{\"AssignMeta\": \"Trigger was \\\"all_successful\\\" but some of the upstream tasks failed.\", \"TriggerWaveformFlow\": \"Trigger was \\\"all_successful\\\" but some of the upstream tasks failed.\", \"ElasticBulkWriter\": \"Trigger was \\\"all_successful\\\" but some of the upstream tasks failed.\", \"FrameConcatenator\": \"Trigger was \\\"all_successful\\\" but some of the upstream tasks failed.\", \"ElasticBulkTransformer\": \"Trigger was \\\"all_successful\\\" but some of the upstream tasks failed.\", \"TranscriptionRouter\": \"Trigger was \\\"all_successful\\\" but some of the upstream tasks failed.\"}\n```", "verbatim": false}}]}, {"type": "message", "subtype": "bot_message", "text": "Flow Runner Failed", "ts": "1676892210.011589", "username": "Flow Runner Failed", "bot_id": "B013H935ZRP", "app_id": "A01338VM16Z", "blocks": [{"type": "header", "block_id": "UQc", "text": {"type": "plain_text", "text": "Details", "emoji": true}}, {"type": "divider", "block_id": "+5s"}, {"type": "section", "block_id": "MuHZL", "fields": [{"type": "mrkdwn", "text": "*flow_id*\n<http://steve.uat.steeleye.co:comms-voice-natterbox|steve.uat.steeleye.co:comms-voice-natterbox>", "verbatim": false}, {"type": "mrkdwn", "text": "*time_taken*\n1s", "verbatim": false}]}, {"type": "section", "block_id": "Z4Uxl", "fields": [{"type": "mrkdwn", "text": "*file_url*\n<s3://steve.uat.steeleye.co/flows/comms-voice-natterbox/2023/01/18/1674050631_536005.csv>", "verbatim": false}, {"type": "mrkdwn", "text": "*environment*\nuat", "verbatim": false}]}, {"type": "divider", "block_id": "5zV"}, {"type": "section", "block_id": "p/r", "text": {"type": "mrkdwn", "text": "*aws_env_vars*\n```{\"batch_ce_name\": \"uat-blue-batch-compute-compute-oq\", \"batch_job_array_index\": \"\", \"batch_job_attempt\": \"1\", \"batch_job_id\": \"c3844d66-0d17-47ab-b85a-f3cfa8f84dff\", \"batch_job_main_node_index\": \"\", \"batch_job_main_node_private_ipv4_address\": \"\", \"batch_jon_node_index\": \"\", \"batch_job_num_nodes\": \"\", \"batch_jq_name\": \"uat-blue-batch-compute-queue\"}```", "verbatim": false}}, {"type": "divider", "block_id": "xQ5v5"}, {"type": "section", "block_id": "i73", "text": {"type": "mrkdwn", "text": "*flow_details*\n```{\"flow_id\": \"<http://steve.uat.steeleye.co:comms-voice-natterbox|steve.uat.steeleye.co:comms-voice-natterbox>\", \"file_url\": \"<s3://steve.uat.steeleye.co/flows/comms-voice-natterbox/2023/01/18/1674050631_536005.csv>\", \"audit_id\": \"<http://steve.uat.steeleye.co:flows/comms-voice-natterbox/2023/01/18/1674050631_536005.csv:1676891951837:_nil_|steve.uat.steeleye.co:flows/comms-voice-natterbox/2023/01/18/1674050631_536005.csv:1676891951837:_nil_>\", \"email_notification\": true, \"use_dask_executor\": true, \"time_taken\": \"1s\"}```", "verbatim": false}}, {"type": "divider", "block_id": "CVkle"}, {"type": "section", "block_id": "wxE", "text": {"type": "mrkdwn", "text": "*traceback*\n```Traceback (most recent call last):\n  File \"/home/<USER>/venv/lib64/python3.7/site-packages/swarm/flow/runner.py\", line 302, in main\n    flow_runner.execute()\n  File \"/home/<USER>/venv/lib64/python3.7/site-packages/swarm/flow/runner.py\", line 85, in execute\n    \"Flow failed.\\n\" + json.dumps(audit_result.get_failed_tasks())\nException: Flow failed.\n{\"FrameConcatenator\": \"Unexpected error: ValueError('No objects to concatenate')\", \"ElasticBulkTransformer\": \"Trigger was \\\"all_successful\\\" but some of the upstream tasks failed.\", \"TranscriptionRouter\": \"Trigger was \\\"all_successful\\\" but some of the upstream tasks failed.\", \"TriggerWaveformFlow\": \"Trigger was \\\"all_successful\\\" but some of the upstream tasks failed.\", \"ElasticBulkWriter\": \"Trigger was \\\"all_successful\\\" but some of the upstream tasks failed.\", \"AssignMeta\": \"Trigger was \\\"all_successful\\\" but some of the upstream tasks failed.\"}\n```", "verbatim": false}}]}, {"type": "message", "subtype": "bot_message", "text": "Flow Runner Failed", "ts": "1676892209.996879", "username": "Flow Runner Failed", "bot_id": "B013H935ZRP", "app_id": "A01338VM16Z", "blocks": [{"type": "header", "block_id": "wbJlE", "text": {"type": "plain_text", "text": "Details", "emoji": true}}, {"type": "divider", "block_id": "5Wc"}, {"type": "section", "block_id": "TCO", "fields": [{"type": "mrkdwn", "text": "*flow_id*\n<http://steve.uat.steeleye.co:comms-voice-natterbox|steve.uat.steeleye.co:comms-voice-natterbox>", "verbatim": false}, {"type": "mrkdwn", "text": "*time_taken*\n1s", "verbatim": false}]}, {"type": "section", "block_id": "9lhN", "fields": [{"type": "mrkdwn", "text": "*file_url*\n<s3://steve.uat.steeleye.co/flows/comms-voice-natterbox/2023/01/18/1674032496_9378302.csv>", "verbatim": false}, {"type": "mrkdwn", "text": "*environment*\nuat", "verbatim": false}]}, {"type": "divider", "block_id": "JBEN"}, {"type": "section", "block_id": "eA9Ht", "text": {"type": "mrkdwn", "text": "*aws_env_vars*\n```{\"batch_ce_name\": \"uat-blue-batch-compute-compute-oq\", \"batch_job_array_index\": \"\", \"batch_job_attempt\": \"1\", \"batch_job_id\": \"0a7f9c81-8585-4592-a3bb-efa7c5e7ae92\", \"batch_job_main_node_index\": \"\", \"batch_job_main_node_private_ipv4_address\": \"\", \"batch_jon_node_index\": \"\", \"batch_job_num_nodes\": \"\", \"batch_jq_name\": \"uat-blue-batch-compute-queue\"}```", "verbatim": false}}, {"type": "divider", "block_id": "PsL"}, {"type": "section", "block_id": "85m", "text": {"type": "mrkdwn", "text": "*flow_details*\n```{\"flow_id\": \"<http://steve.uat.steeleye.co:comms-voice-natterbox|steve.uat.steeleye.co:comms-voice-natterbox>\", \"file_url\": \"<s3://steve.uat.steeleye.co/flows/comms-voice-natterbox/2023/01/18/1674032496_9378302.csv>\", \"audit_id\": \"<http://steve.uat.steeleye.co:flows/comms-voice-natterbox/2023/01/18/1674032496_9378302.csv:1676891945379:_nil_|steve.uat.steeleye.co:flows/comms-voice-natterbox/2023/01/18/1674032496_9378302.csv:1676891945379:_nil_>\", \"email_notification\": true, \"use_dask_executor\": true, \"time_taken\": \"1s\"}```", "verbatim": false}}, {"type": "divider", "block_id": "=KU"}, {"type": "section", "block_id": "k3tFJ", "text": {"type": "mrkdwn", "text": "*traceback*\n```Traceback (most recent call last):\n  File \"/home/<USER>/venv/lib64/python3.7/site-packages/swarm/flow/runner.py\", line 302, in main\n    flow_runner.execute()\n  File \"/home/<USER>/venv/lib64/python3.7/site-packages/swarm/flow/runner.py\", line 85, in execute\n    \"Flow failed.\\n\" + json.dumps(audit_result.get_failed_tasks())\nException: Flow failed.\n{\"TriggerWaveformFlow\": \"Trigger was \\\"all_successful\\\" but some of the upstream tasks failed.\", \"AssignMeta\": \"Trigger was \\\"all_successful\\\" but some of the upstream tasks failed.\", \"ElasticBulkWriter\": \"Trigger was \\\"all_successful\\\" but some of the upstream tasks failed.\", \"FrameConcatenator\": \"Unexpected error: ValueError('No objects to concatenate')\", \"ElasticBulkTransformer\": \"Trigger was \\\"all_successful\\\" but some of the upstream tasks failed.\", \"TranscriptionRouter\": \"Trigger was \\\"all_successful\\\" but some of the upstream tasks failed.\"}\n```", "verbatim": false}}]}, {"type": "message", "subtype": "bot_message", "text": "Flow Runner Failed", "ts": "1676891836.219169", "username": "Flow Runner Failed", "bot_id": "B013H935ZRP", "app_id": "A01338VM16Z", "blocks": [{"type": "header", "block_id": "Dkkv", "text": {"type": "plain_text", "text": "Details", "emoji": true}}, {"type": "divider", "block_id": "1JE"}, {"type": "section", "block_id": "BFO5t", "fields": [{"type": "mrkdwn", "text": "*flow_id*\n<http://steve.uat.steeleye.co:comms-voice-natterbox|steve.uat.steeleye.co:comms-voice-natterbox>", "verbatim": false}, {"type": "mrkdwn", "text": "*time_taken*\n1s", "verbatim": false}]}, {"type": "section", "block_id": "a85kS", "fields": [{"type": "mrkdwn", "text": "*file_url*\n<s3://steve.uat.steeleye.co/flows/comms-voice-natterbox/2023/01/18/>", "verbatim": false}, {"type": "mrkdwn", "text": "*environment*\nuat", "verbatim": false}]}, {"type": "divider", "block_id": "vOj"}, {"type": "section", "block_id": "bHMKr", "text": {"type": "mrkdwn", "text": "*aws_env_vars*\n```{\"batch_ce_name\": \"uat-blue-batch-compute-compute-oq\", \"batch_job_array_index\": \"\", \"batch_job_attempt\": \"1\", \"batch_job_id\": \"bc2a50d0-cace-423a-8b72-8c9a73b20a74\", \"batch_job_main_node_index\": \"\", \"batch_job_main_node_private_ipv4_address\": \"\", \"batch_jon_node_index\": \"\", \"batch_job_num_nodes\": \"\", \"batch_jq_name\": \"uat-blue-batch-compute-queue\"}```", "verbatim": false}}, {"type": "divider", "block_id": "yHLN1"}, {"type": "section", "block_id": "oBA", "text": {"type": "mrkdwn", "text": "*flow_details*\n```{\"flow_id\": \"<http://steve.uat.steeleye.co:comms-voice-natterbox|steve.uat.steeleye.co:comms-voice-natterbox>\", \"file_url\": \"<s3://steve.uat.steeleye.co/flows/comms-voice-natterbox/2023/01/18/>\", \"audit_id\": \"<http://steve.uat.steeleye.co:flows/comms-voice-natterbox/2023/01/18/:1676891777012:_nil_|steve.uat.steeleye.co:flows/comms-voice-natterbox/2023/01/18/:1676891777012:_nil_>\", \"email_notification\": true, \"use_dask_executor\": true, \"time_taken\": \"1s\"}```", "verbatim": false}}, {"type": "divider", "block_id": "p2H"}, {"type": "section", "block_id": "BzT", "text": {"type": "mrkdwn", "text": "*traceback*\n```Traceback (most recent call last):\n  File \"/home/<USER>/venv/lib64/python3.7/site-packages/swarm/flow/runner.py\", line 302, in main\n    flow_runner.execute()\n  File \"/home/<USER>/venv/lib64/python3.7/site-packages/swarm/flow/runner.py\", line 85, in execute\n    \"Flow failed.\\n\" + json.dumps(audit_result.get_failed_tasks())\nException: Flow failed.\n{\"FrameConcatenator\": \"Trigger was \\\"all_successful\\\" but some of the upstream tasks failed.\", \"AssignMeta\": \"Trigger was \\\"all_successful\\\" but some of the upstream tasks failed.\", \"ElasticBulkWriter\": \"Trigger was \\\"all_successful\\\" but some of the upstream tasks failed.\", \"ReadBatchCSV\": \"Unexpected error: EmptyDataError('No columns to parse from file')\", \"GetRecordingFiles\": \"Trigger was \\\"all_successful\\\" but some of the upstream tasks failed.\", \"ElasticBulkTransformer\": \"Trigger was \\\"all_successful\\\" but some of the upstream tasks failed.\", \"LinkParticipants\": \"Trigger was \\\"all_successful\\\" but some of the upstream tasks failed.\", \"TranscriptionRouter\": \"Trigger was \\\"all_successful\\\" but some of the upstream tasks failed.\", \"ParticipantsIdentifiers\": \"Trigger was \\\"all_successful\\\" but some of the upstream tasks failed.\", \"MapAttribute\": \"Trigger was \\\"all_successful\\\" but some of the upstream tasks failed.\", \"ConvertDatetime\": \"Trigger was \\\"all_successful\\\" but some of the upstream tasks failed.\", \"MapStatic\": \"Trigger was \\\"all_successful\\\" but some of the upstream tasks failed.\", \"S3MetadataAttachmentBatch\": \"Trigger was \\\"all_successful\\\" but some of the upstream tasks failed.\", \"XmlBatchCsvDownloaderParser\": \"Trigger was \\\"all_successful\\\" but some of the upstream tasks failed.\", \"MapConditional\": \"Trigger was \\\"all_successful\\\" but some of the upstream tasks failed.\", \"TriggerWaveformFlow\": \"Trigger was \\\"all_successful\\\" but some of the upstream tasks failed.\", \"ConvertTimeDelta\": \"Trigger was \\\"all_successful\\\" but some of the upstream tasks failed.\", \"FrameColumnManipulator\": \"Trigger was \\\"all_successful\\\" but some of the upstream tasks failed.\", \"ConcatAttributes\": \"Trigger was \\\"all_successful\\\" but some of the upstream tasks failed.\"}\n```", "verbatim": false}}]}, {"type": "message", "subtype": "bot_message", "text": "Flow Runner Failed", "ts": "1676891791.411579", "username": "Flow Runner Failed", "bot_id": "B013H935ZRP", "app_id": "A01338VM16Z", "blocks": [{"type": "header", "block_id": "qt0lc", "text": {"type": "plain_text", "text": "Details", "emoji": true}}, {"type": "divider", "block_id": "c11Xn"}, {"type": "section", "block_id": "rQVq", "fields": [{"type": "mrkdwn", "text": "*flow_id*\n<http://steve.uat.steeleye.co:comms-voice-natterbox|steve.uat.steeleye.co:comms-voice-natterbox>", "verbatim": false}, {"type": "mrkdwn", "text": "*time_taken*\n1s", "verbatim": false}]}, {"type": "section", "block_id": "KpDK", "fields": [{"type": "mrkdwn", "text": "*file_url*\n<s3://steve.uat.steeleye.co/flows/comms-voice-natterbox/2023/01/>", "verbatim": false}, {"type": "mrkdwn", "text": "*environment*\nuat", "verbatim": false}]}, {"type": "divider", "block_id": "6Qwe8"}, {"type": "section", "block_id": "Vm0Q", "text": {"type": "mrkdwn", "text": "*aws_env_vars*\n```{\"batch_ce_name\": \"uat-blue-batch-compute-compute-oq\", \"batch_job_array_index\": \"\", \"batch_job_attempt\": \"1\", \"batch_job_id\": \"22858549-a0a4-4e3d-81f3-45c81da1a5af\", \"batch_job_main_node_index\": \"\", \"batch_job_main_node_private_ipv4_address\": \"\", \"batch_jon_node_index\": \"\", \"batch_job_num_nodes\": \"\", \"batch_jq_name\": \"uat-blue-batch-compute-queue\"}```", "verbatim": false}}, {"type": "divider", "block_id": "CiS"}, {"type": "section", "block_id": "NzRfB", "text": {"type": "mrkdwn", "text": "*flow_details*\n```{\"flow_id\": \"<http://steve.uat.steeleye.co:comms-voice-natterbox|steve.uat.steeleye.co:comms-voice-natterbox>\", \"file_url\": \"<s3://steve.uat.steeleye.co/flows/comms-voice-natterbox/2023/01/>\", \"audit_id\": \"<http://steve.uat.steeleye.co:flows/comms-voice-natterbox/2023/01/:1676891778937:_nil_|steve.uat.steeleye.co:flows/comms-voice-natterbox/2023/01/:1676891778937:_nil_>\", \"email_notification\": true, \"use_dask_executor\": true, \"time_taken\": \"1s\"}```", "verbatim": false}}, {"type": "divider", "block_id": "kOYlP"}, {"type": "section", "block_id": "rnTV", "text": {"type": "mrkdwn", "text": "*traceback*\n```Traceback (most recent call last):\n  File \"/home/<USER>/venv/lib64/python3.7/site-packages/swarm/flow/runner.py\", line 302, in main\n    flow_runner.execute()\n  File \"/home/<USER>/venv/lib64/python3.7/site-packages/swarm/flow/runner.py\", line 85, in execute\n    \"Flow failed.\\n\" + json.dumps(audit_result.get_failed_tasks())\nException: Flow failed.\n{\"ParticipantsIdentifiers\": \"Trigger was \\\"all_successful\\\" but some of the upstream tasks failed.\", \"AssignMeta\": \"Trigger was \\\"all_successful\\\" but some of the upstream tasks failed.\", \"XmlBatchCsvDownloaderParser\": \"Trigger was \\\"all_successful\\\" but some of the upstream tasks failed.\", \"MapStatic\": \"Trigger was \\\"all_successful\\\" but some of the upstream tasks failed.\", \"ElasticBulkWriter\": \"Trigger was \\\"all_successful\\\" but some of the upstream tasks failed.\", \"S3MetadataAttachmentBatch\": \"Trigger was \\\"all_successful\\\" but some of the upstream tasks failed.\", \"ElasticBulkTransformer\": \"Trigger was \\\"all_successful\\\" but some of the upstream tasks failed.\", \"FrameConcatenator\": \"Trigger was \\\"all_successful\\\" but some of the upstream tasks failed.\", \"MapAttribute\": \"Trigger was \\\"all_successful\\\" but some of the upstream tasks failed.\", \"TriggerWaveformFlow\": \"Trigger was \\\"all_successful\\\" but some of the upstream tasks failed.\", \"ConcatAttributes\": \"Trigger was \\\"all_successful\\\" but some of the upstream tasks failed.\", \"MapConditional\": \"Trigger was \\\"all_successful\\\" but some of the upstream tasks failed.\", \"GetRecordingFiles\": \"Trigger was \\\"all_successful\\\" but some of the upstream tasks failed.\", \"ReadBatchCSV\": \"Unexpected error: EmptyDataError('No columns to parse from file')\", \"ConvertDatetime\": \"Trigger was \\\"all_successful\\\" but some of the upstream tasks failed.\", \"ConvertTimeDelta\": \"Trigger was \\\"all_successful\\\" but some of the upstream tasks failed.\", \"TranscriptionRouter\": \"Trigger was \\\"all_successful\\\" but some of the upstream tasks failed.\", \"FrameColumnManipulator\": \"Trigger was \\\"all_successful\\\" but some of the upstream tasks failed.\", \"LinkParticipants\": \"Trigger was \\\"all_successful\\\" but some of the upstream tasks failed.\"}\n```", "verbatim": false}}]}, {"type": "message", "subtype": "bot_message", "text": "Flow Runner Failed", "ts": "1676891569.142379", "username": "Flow Runner Failed", "bot_id": "B013H935ZRP", "app_id": "A01338VM16Z", "blocks": [{"type": "header", "block_id": "STRU", "text": {"type": "plain_text", "text": "Details", "emoji": true}}, {"type": "divider", "block_id": "bQ+"}, {"type": "section", "block_id": "90yZA", "fields": [{"type": "mrkdwn", "text": "*flow_id*\n<http://steve.uat.steeleye.co:comms-voice-natterbox|steve.uat.steeleye.co:comms-voice-natterbox>", "verbatim": false}, {"type": "mrkdwn", "text": "*time_taken*\n2s", "verbatim": false}]}, {"type": "section", "block_id": "iKs4T", "fields": [{"type": "mrkdwn", "text": "*file_url*\n<s3://steve.uat.steeleye.co/flows/comms-voice-natterbox/2023/02/10/1676029408_7363968.csv>", "verbatim": false}, {"type": "mrkdwn", "text": "*environment*\nuat", "verbatim": false}]}, {"type": "divider", "block_id": "GXC"}, {"type": "section", "block_id": "vJt", "text": {"type": "mrkdwn", "text": "*aws_env_vars*\n```{\"batch_ce_name\": \"uat-blue-batch-compute-compute-oq\", \"batch_job_array_index\": \"\", \"batch_job_attempt\": \"1\", \"batch_job_id\": \"c46fbafe-afe4-483b-9b3f-91d833066777\", \"batch_job_main_node_index\": \"\", \"batch_job_main_node_private_ipv4_address\": \"\", \"batch_jon_node_index\": \"\", \"batch_job_num_nodes\": \"\", \"batch_jq_name\": \"uat-blue-batch-compute-queue\"}```", "verbatim": false}}, {"type": "divider", "block_id": "JKk+"}, {"type": "section", "block_id": "tq+m", "text": {"type": "mrkdwn", "text": "*flow_details*\n```{\"flow_id\": \"<http://steve.uat.steeleye.co:comms-voice-natterbox|steve.uat.steeleye.co:comms-voice-natterbox>\", \"file_url\": \"<s3://steve.uat.steeleye.co/flows/comms-voice-natterbox/2023/02/10/1676029408_7363968.csv>\", \"audit_id\": \"<http://steve.uat.steeleye.co:flows/comms-voice-natterbox/2023/02/10/1676029408_7363968.csv:1676891320582:_nil_|steve.uat.steeleye.co:flows/comms-voice-natterbox/2023/02/10/1676029408_7363968.csv:1676891320582:_nil_>\", \"email_notification\": true, \"use_dask_executor\": true, \"time_taken\": \"2s\"}```", "verbatim": false}}, {"type": "divider", "block_id": "v86UE"}, {"type": "section", "block_id": "9Kq", "text": {"type": "mrkdwn", "text": "*traceback*\n```Traceback (most recent call last):\n  File \"/home/<USER>/venv/lib64/python3.7/site-packages/swarm/flow/runner.py\", line 302, in main\n    flow_runner.execute()\n  File \"/home/<USER>/venv/lib64/python3.7/site-packages/swarm/flow/runner.py\", line 85, in execute\n    \"Flow failed.\\n\" + json.dumps(audit_result.get_failed_tasks())\nException: Flow failed.\n{\"AssignMeta\": \"Trigger was \\\"all_successful\\\" but some of the upstream tasks failed.\", \"TriggerWaveformFlow\": \"Trigger was \\\"all_successful\\\" but some of the upstream tasks failed.\", \"ElasticBulkWriter\": \"Trigger was \\\"all_successful\\\" but some of the upstream tasks failed.\", \"FrameConcatenator\": \"Unexpected error: ValueError('No objects to concatenate')\", \"ElasticBulkTransformer\": \"Trigger was \\\"all_successful\\\" but some of the upstream tasks failed.\", \"TranscriptionRouter\": \"Trigger was \\\"all_successful\\\" but some of the upstream tasks failed.\"}\n```", "verbatim": false}}]}, {"type": "message", "subtype": "bot_message", "text": "Flow Runner Failed", "ts": "1676891551.516999", "username": "Flow Runner Failed", "bot_id": "B013H935ZRP", "app_id": "A01338VM16Z", "blocks": [{"type": "header", "block_id": "wk7JU", "text": {"type": "plain_text", "text": "Details", "emoji": true}}, {"type": "divider", "block_id": "936"}, {"type": "section", "block_id": "th05", "fields": [{"type": "mrkdwn", "text": "*flow_id*\n<http://steve.uat.steeleye.co:comms-voice-natterbox|steve.uat.steeleye.co:comms-voice-natterbox>", "verbatim": false}, {"type": "mrkdwn", "text": "*time_taken*\n2s", "verbatim": false}]}, {"type": "section", "block_id": "NbcU", "fields": [{"type": "mrkdwn", "text": "*file_url*\n<s3://steve.uat.steeleye.co/flows/comms-voice-natterbox/2023/02/10/1676030311_4397154.csv>", "verbatim": false}, {"type": "mrkdwn", "text": "*environment*\nuat", "verbatim": false}]}, {"type": "divider", "block_id": "qfF"}, {"type": "section", "block_id": "DFu", "text": {"type": "mrkdwn", "text": "*aws_env_vars*\n```{\"batch_ce_name\": \"uat-blue-batch-compute-compute-oq\", \"batch_job_array_index\": \"\", \"batch_job_attempt\": \"1\", \"batch_job_id\": \"ffbbb7b3-f0f9-469c-9d70-7429572610d2\", \"batch_job_main_node_index\": \"\", \"batch_job_main_node_private_ipv4_address\": \"\", \"batch_jon_node_index\": \"\", \"batch_job_num_nodes\": \"\", \"batch_jq_name\": \"uat-blue-batch-compute-queue\"}```", "verbatim": false}}, {"type": "divider", "block_id": "PN=Qj"}, {"type": "section", "block_id": "77D", "text": {"type": "mrkdwn", "text": "*flow_details*\n```{\"flow_id\": \"<http://steve.uat.steeleye.co:comms-voice-natterbox|steve.uat.steeleye.co:comms-voice-natterbox>\", \"file_url\": \"<s3://steve.uat.steeleye.co/flows/comms-voice-natterbox/2023/02/10/1676030311_4397154.csv>\", \"audit_id\": \"<http://steve.uat.steeleye.co:flows/comms-voice-natterbox/2023/02/10/1676030311_4397154.csv:1676891314375:_nil_|steve.uat.steeleye.co:flows/comms-voice-natterbox/2023/02/10/1676030311_4397154.csv:1676891314375:_nil_>\", \"email_notification\": true, \"use_dask_executor\": true, \"time_taken\": \"2s\"}```", "verbatim": false}}, {"type": "divider", "block_id": "H5/s"}, {"type": "section", "block_id": "ZfR8", "text": {"type": "mrkdwn", "text": "*traceback*\n```Traceback (most recent call last):\n  File \"/home/<USER>/venv/lib64/python3.7/site-packages/swarm/flow/runner.py\", line 302, in main\n    flow_runner.execute()\n  File \"/home/<USER>/venv/lib64/python3.7/site-packages/swarm/flow/runner.py\", line 85, in execute\n    \"Flow failed.\\n\" + json.dumps(audit_result.get_failed_tasks())\nException: Flow failed.\n{\"AssignMeta\": \"Trigger was \\\"all_successful\\\" but some of the upstream tasks failed.\", \"ElasticBulkWriter\": \"Trigger was \\\"all_successful\\\" but some of the upstream tasks failed.\", \"FrameConcatenator\": \"Trigger was \\\"all_successful\\\" but some of the upstream tasks failed.\", \"TriggerWaveformFlow\": \"Trigger was \\\"all_successful\\\" but some of the upstream tasks failed.\", \"ElasticBulkTransformer\": \"Trigger was \\\"all_successful\\\" but some of the upstream tasks failed.\", \"TranscriptionRouter\": \"Trigger was \\\"all_successful\\\" but some of the upstream tasks failed.\"}\n```", "verbatim": false}}]}, {"type": "message", "subtype": "bot_message", "text": "Flow Runner Failed", "ts": "1676891551.412419", "username": "Flow Runner Failed", "bot_id": "B013H935ZRP", "app_id": "A01338VM16Z", "blocks": [{"type": "header", "block_id": "PMG", "text": {"type": "plain_text", "text": "Details", "emoji": true}}, {"type": "divider", "block_id": "rq1wO"}, {"type": "section", "block_id": "boMeL", "fields": [{"type": "mrkdwn", "text": "*flow_id*\n<http://steve.uat.steeleye.co:comms-voice-natterbox|steve.uat.steeleye.co:comms-voice-natterbox>", "verbatim": false}, {"type": "mrkdwn", "text": "*time_taken*\n2s", "verbatim": false}]}, {"type": "section", "block_id": "TMy", "fields": [{"type": "mrkdwn", "text": "*file_url*\n<s3://steve.uat.steeleye.co/flows/comms-voice-natterbox/2023/02/10/1676023918_3158185.csv>", "verbatim": false}, {"type": "mrkdwn", "text": "*environment*\nuat", "verbatim": false}]}, {"type": "divider", "block_id": "2Vme"}, {"type": "section", "block_id": "3lLb", "text": {"type": "mrkdwn", "text": "*aws_env_vars*\n```{\"batch_ce_name\": \"uat-blue-batch-compute-compute-oq\", \"batch_job_array_index\": \"\", \"batch_job_attempt\": \"1\", \"batch_job_id\": \"96d6a788-0760-4fb5-bb97-b800c7e11062\", \"batch_job_main_node_index\": \"\", \"batch_job_main_node_private_ipv4_address\": \"\", \"batch_jon_node_index\": \"\", \"batch_job_num_nodes\": \"\", \"batch_jq_name\": \"uat-blue-batch-compute-queue\"}```", "verbatim": false}}, {"type": "divider", "block_id": "jGw"}, {"type": "section", "block_id": "vJSr6", "text": {"type": "mrkdwn", "text": "*flow_details*\n```{\"flow_id\": \"<http://steve.uat.steeleye.co:comms-voice-natterbox|steve.uat.steeleye.co:comms-voice-natterbox>\", \"file_url\": \"<s3://steve.uat.steeleye.co/flows/comms-voice-natterbox/2023/02/10/1676023918_3158185.csv>\", \"audit_id\": \"<http://steve.uat.steeleye.co:flows/comms-voice-natterbox/2023/02/10/1676023918_3158185.csv:1676891342900:_nil_|steve.uat.steeleye.co:flows/comms-voice-natterbox/2023/02/10/1676023918_3158185.csv:1676891342900:_nil_>\", \"email_notification\": true, \"use_dask_executor\": true, \"time_taken\": \"2s\"}```", "verbatim": false}}, {"type": "divider", "block_id": "R5Lt8"}, {"type": "section", "block_id": "W+I", "text": {"type": "mrkdwn", "text": "*traceback*\n```Traceback (most recent call last):\n  File \"/home/<USER>/venv/lib64/python3.7/site-packages/swarm/flow/runner.py\", line 302, in main\n    flow_runner.execute()\n  File \"/home/<USER>/venv/lib64/python3.7/site-packages/swarm/flow/runner.py\", line 85, in execute\n    \"Flow failed.\\n\" + json.dumps(audit_result.get_failed_tasks())\nException: Flow failed.\n{\"TriggerWaveformFlow\": \"Trigger was \\\"all_successful\\\" but some of the upstream tasks failed.\", \"TranscriptionRouter\": \"Trigger was \\\"all_successful\\\" but some of the upstream tasks failed.\", \"ElasticBulkWriter\": \"Trigger was \\\"all_successful\\\" but some of the upstream tasks failed.\", \"AssignMeta\": \"Trigger was \\\"all_successful\\\" but some of the upstream tasks failed.\", \"FrameConcatenator\": \"Unexpected error: ValueError('No objects to concatenate')\", \"ElasticBulkTransformer\": \"Trigger was \\\"all_successful\\\" but some of the upstream tasks failed.\"}\n```", "verbatim": false}}]}, {"type": "message", "subtype": "bot_message", "text": "Flow Runner Failed", "ts": "1676891551.294249", "username": "Flow Runner Failed", "bot_id": "B013H935ZRP", "app_id": "A01338VM16Z", "blocks": [{"type": "header", "block_id": "zh7", "text": {"type": "plain_text", "text": "Details", "emoji": true}}, {"type": "divider", "block_id": "UbV"}, {"type": "section", "block_id": "SBuYD", "fields": [{"type": "mrkdwn", "text": "*flow_id*\n<http://steve.uat.steeleye.co:comms-voice-natterbox|steve.uat.steeleye.co:comms-voice-natterbox>", "verbatim": false}, {"type": "mrkdwn", "text": "*time_taken*\n1s", "verbatim": false}]}, {"type": "section", "block_id": "=EYx", "fields": [{"type": "mrkdwn", "text": "*file_url*\n<s3://steve.uat.steeleye.co/flows/comms-voice-natterbox/2023/02/10/1676027605_841586.csv>", "verbatim": false}, {"type": "mrkdwn", "text": "*environment*\nuat", "verbatim": false}]}, {"type": "divider", "block_id": "mlqS9"}, {"type": "section", "block_id": "ADLs4", "text": {"type": "mrkdwn", "text": "*aws_env_vars*\n```{\"batch_ce_name\": \"uat-blue-batch-compute-compute-oq\", \"batch_job_array_index\": \"\", \"batch_job_attempt\": \"1\", \"batch_job_id\": \"e30cd986-6b6e-4d1c-8d32-b042d2a6d4c3\", \"batch_job_main_node_index\": \"\", \"batch_job_main_node_private_ipv4_address\": \"\", \"batch_jon_node_index\": \"\", \"batch_job_num_nodes\": \"\", \"batch_jq_name\": \"uat-blue-batch-compute-queue\"}```", "verbatim": false}}, {"type": "divider", "block_id": "/1="}, {"type": "section", "block_id": "=gi", "text": {"type": "mrkdwn", "text": "*flow_details*\n```{\"flow_id\": \"<http://steve.uat.steeleye.co:comms-voice-natterbox|steve.uat.steeleye.co:comms-voice-natterbox>\", \"file_url\": \"<s3://steve.uat.steeleye.co/flows/comms-voice-natterbox/2023/02/10/1676027605_841586.csv>\", \"audit_id\": \"<http://steve.uat.steeleye.co:flows/comms-voice-natterbox/2023/02/10/1676027605_841586.csv:1676891314529:_nil_|steve.uat.steeleye.co:flows/comms-voice-natterbox/2023/02/10/1676027605_841586.csv:1676891314529:_nil_>\", \"email_notification\": true, \"use_dask_executor\": true, \"time_taken\": \"1s\"}```", "verbatim": false}}, {"type": "divider", "block_id": "BcADX"}, {"type": "section", "block_id": "9VAFC", "text": {"type": "mrkdwn", "text": "*traceback*\n```Traceback (most recent call last):\n  File \"/home/<USER>/venv/lib64/python3.7/site-packages/swarm/flow/runner.py\", line 302, in main\n    flow_runner.execute()\n  File \"/home/<USER>/venv/lib64/python3.7/site-packages/swarm/flow/runner.py\", line 85, in execute\n    \"Flow failed.\\n\" + json.dumps(audit_result.get_failed_tasks())\nException: Flow failed.\n{\"AssignMeta\": \"Trigger was \\\"all_successful\\\" but some of the upstream tasks failed.\", \"TriggerWaveformFlow\": \"Trigger was \\\"all_successful\\\" but some of the upstream tasks failed.\", \"ElasticBulkWriter\": \"Trigger was \\\"all_successful\\\" but some of the upstream tasks failed.\", \"FrameConcatenator\": \"Unexpected error: ValueError('No objects to concatenate')\", \"ElasticBulkTransformer\": \"Trigger was \\\"all_successful\\\" but some of the upstream tasks failed.\", \"TranscriptionRouter\": \"Trigger was \\\"all_successful\\\" but some of the upstream tasks failed.\"}\n```", "verbatim": false}}]}, {"type": "message", "subtype": "bot_message", "text": "Flow Runner Failed", "ts": "1676891022.371189", "username": "Flow Runner Failed", "bot_id": "B013H935ZRP", "app_id": "A01338VM16Z", "blocks": [{"type": "header", "block_id": "qOu", "text": {"type": "plain_text", "text": "Details", "emoji": true}}, {"type": "divider", "block_id": "oJRW"}, {"type": "section", "block_id": "Y36r7", "fields": [{"type": "mrkdwn", "text": "*flow_id*\n<http://steve.uat.steeleye.co:comms-voice-natterbox|steve.uat.steeleye.co:comms-voice-natterbox>", "verbatim": false}, {"type": "mrkdwn", "text": "*time_taken*\n1s", "verbatim": false}]}, {"type": "section", "block_id": "0XC", "fields": [{"type": "mrkdwn", "text": "*file_url*\n<s3://steve.uat.steeleye.co/flows/comms-voice-natterbox/2023/02/10/1676029408_7363968.csv>", "verbatim": false}, {"type": "mrkdwn", "text": "*environment*\nuat", "verbatim": false}]}, {"type": "divider", "block_id": "Qq0k"}, {"type": "section", "block_id": "vbbwF", "text": {"type": "mrkdwn", "text": "*aws_env_vars*\n```{\"batch_ce_name\": \"uat-blue-batch-compute-compute-oq\", \"batch_job_array_index\": \"\", \"batch_job_attempt\": \"1\", \"batch_job_id\": \"39422fde-6a27-4f91-a3e9-d3fee3cb5bc0\", \"batch_job_main_node_index\": \"\", \"batch_job_main_node_private_ipv4_address\": \"\", \"batch_jon_node_index\": \"\", \"batch_job_num_nodes\": \"\", \"batch_jq_name\": \"uat-blue-batch-compute-queue\"}```", "verbatim": false}}, {"type": "divider", "block_id": "B/Axh"}, {"type": "section", "block_id": "lab9x", "text": {"type": "mrkdwn", "text": "*flow_details*\n```{\"flow_id\": \"<http://steve.uat.steeleye.co:comms-voice-natterbox|steve.uat.steeleye.co:comms-voice-natterbox>\", \"file_url\": \"<s3://steve.uat.steeleye.co/flows/comms-voice-natterbox/2023/02/10/1676029408_7363968.csv>\", \"audit_id\": \"<http://steve.uat.steeleye.co:flows/comms-voice-natterbox/2023/02/10/1676029408_7363968.csv:1676890792615:_nil_|steve.uat.steeleye.co:flows/comms-voice-natterbox/2023/02/10/1676029408_7363968.csv:1676890792615:_nil_>\", \"email_notification\": true, \"use_dask_executor\": true, \"time_taken\": \"1s\"}```", "verbatim": false}}, {"type": "divider", "block_id": "eYE"}, {"type": "section", "block_id": "4bHE=", "text": {"type": "mrkdwn", "text": "*traceback*\n```Traceback (most recent call last):\n  File \"/home/<USER>/venv/lib64/python3.7/site-packages/swarm/flow/runner.py\", line 302, in main\n    flow_runner.execute()\n  File \"/home/<USER>/venv/lib64/python3.7/site-packages/swarm/flow/runner.py\", line 85, in execute\n    \"Flow failed.\\n\" + json.dumps(audit_result.get_failed_tasks())\nException: Flow failed.\n{\"AssignMeta\": \"Trigger was \\\"all_successful\\\" but some of the upstream tasks failed.\", \"ElasticBulkWriter\": \"Trigger was \\\"all_successful\\\" but some of the upstream tasks failed.\", \"TriggerWaveformFlow\": \"Trigger was \\\"all_successful\\\" but some of the upstream tasks failed.\", \"FrameConcatenator\": \"Unexpected error: ValueError('No objects to concatenate')\", \"ElasticBulkTransformer\": \"Trigger was \\\"all_successful\\\" but some of the upstream tasks failed.\", \"TranscriptionRouter\": \"Trigger was \\\"all_successful\\\" but some of the upstream tasks failed.\"}\n```", "verbatim": false}}]}, {"type": "message", "subtype": "bot_message", "text": "Flow Runner Failed", "ts": "1676891022.261839", "username": "Flow Runner Failed", "bot_id": "B013H935ZRP", "app_id": "A01338VM16Z", "blocks": [{"type": "header", "block_id": "32yV", "text": {"type": "plain_text", "text": "Details", "emoji": true}}, {"type": "divider", "block_id": "5aN"}, {"type": "section", "block_id": "CZv4p", "fields": [{"type": "mrkdwn", "text": "*flow_id*\n<http://steve.uat.steeleye.co:comms-voice-natterbox|steve.uat.steeleye.co:comms-voice-natterbox>", "verbatim": false}, {"type": "mrkdwn", "text": "*time_taken*\n2s", "verbatim": false}]}, {"type": "section", "block_id": "EkXr", "fields": [{"type": "mrkdwn", "text": "*file_url*\n<s3://steve.uat.steeleye.co/flows/comms-voice-natterbox/2023/02/10/1676023918_3158185.csv>", "verbatim": false}, {"type": "mrkdwn", "text": "*environment*\nuat", "verbatim": false}]}, {"type": "divider", "block_id": "oqt+0"}, {"type": "section", "block_id": "VN9", "text": {"type": "mrkdwn", "text": "*aws_env_vars*\n```{\"batch_ce_name\": \"uat-blue-batch-compute-compute-oq\", \"batch_job_array_index\": \"\", \"batch_job_attempt\": \"1\", \"batch_job_id\": \"fc834ed9-47f3-4df7-a938-19cafa921aae\", \"batch_job_main_node_index\": \"\", \"batch_job_main_node_private_ipv4_address\": \"\", \"batch_jon_node_index\": \"\", \"batch_job_num_nodes\": \"\", \"batch_jq_name\": \"uat-blue-batch-compute-queue\"}```", "verbatim": false}}, {"type": "divider", "block_id": "ijV"}, {"type": "section", "block_id": "paY", "text": {"type": "mrkdwn", "text": "*flow_details*\n```{\"flow_id\": \"<http://steve.uat.steeleye.co:comms-voice-natterbox|steve.uat.steeleye.co:comms-voice-natterbox>\", \"file_url\": \"<s3://steve.uat.steeleye.co/flows/comms-voice-natterbox/2023/02/10/1676023918_3158185.csv>\", \"audit_id\": \"<http://steve.uat.steeleye.co:flows/comms-voice-natterbox/2023/02/10/1676023918_3158185.csv:1676890786668:_nil_|steve.uat.steeleye.co:flows/comms-voice-natterbox/2023/02/10/1676023918_3158185.csv:1676890786668:_nil_>\", \"email_notification\": true, \"use_dask_executor\": true, \"time_taken\": \"2s\"}```", "verbatim": false}}, {"type": "divider", "block_id": "SFm"}, {"type": "section", "block_id": "x7I", "text": {"type": "mrkdwn", "text": "*traceback*\n```Traceback (most recent call last):\n  File \"/home/<USER>/venv/lib64/python3.7/site-packages/swarm/flow/runner.py\", line 302, in main\n    flow_runner.execute()\n  File \"/home/<USER>/venv/lib64/python3.7/site-packages/swarm/flow/runner.py\", line 85, in execute\n    \"Flow failed.\\n\" + json.dumps(audit_result.get_failed_tasks())\nException: Flow failed.\n{\"TriggerWaveformFlow\": \"Trigger was \\\"all_successful\\\" but some of the upstream tasks failed.\", \"FrameConcatenator\": \"Unexpected error: ValueError('No objects to concatenate')\", \"TranscriptionRouter\": \"Trigger was \\\"all_successful\\\" but some of the upstream tasks failed.\", \"AssignMeta\": \"Trigger was \\\"all_successful\\\" but some of the upstream tasks failed.\", \"ElasticBulkTransformer\": \"Trigger was \\\"all_successful\\\" but some of the upstream tasks failed.\", \"ElasticBulkWriter\": \"Trigger was \\\"all_successful\\\" but some of the upstream tasks failed.\"}\n```", "verbatim": false}}]}, {"type": "message", "subtype": "bot_message", "text": "Flow Runner Failed", "ts": "1676891022.200959", "username": "Flow Runner Failed", "bot_id": "B013H935ZRP", "app_id": "A01338VM16Z", "blocks": [{"type": "header", "block_id": "Js+", "text": {"type": "plain_text", "text": "Details", "emoji": true}}, {"type": "divider", "block_id": "1=kwL"}, {"type": "section", "block_id": "I+MXt", "fields": [{"type": "mrkdwn", "text": "*flow_id*\n<http://steve.uat.steeleye.co:comms-voice-natterbox|steve.uat.steeleye.co:comms-voice-natterbox>", "verbatim": false}, {"type": "mrkdwn", "text": "*time_taken*\n1s", "verbatim": false}]}, {"type": "section", "block_id": "YFIo", "fields": [{"type": "mrkdwn", "text": "*file_url*\n<s3://steve.uat.steeleye.co/flows/comms-voice-natterbox/2023/02/10/1676030311_4397154.csv>", "verbatim": false}, {"type": "mrkdwn", "text": "*environment*\nuat", "verbatim": false}]}, {"type": "divider", "block_id": "62fRC"}, {"type": "section", "block_id": "Xo/e0", "text": {"type": "mrkdwn", "text": "*aws_env_vars*\n```{\"batch_ce_name\": \"uat-blue-batch-compute-compute-oq\", \"batch_job_array_index\": \"\", \"batch_job_attempt\": \"1\", \"batch_job_id\": \"97e0dc49-70ef-451a-a579-2d213abf942d\", \"batch_job_main_node_index\": \"\", \"batch_job_main_node_private_ipv4_address\": \"\", \"batch_jon_node_index\": \"\", \"batch_job_num_nodes\": \"\", \"batch_jq_name\": \"uat-blue-batch-compute-queue\"}```", "verbatim": false}}, {"type": "divider", "block_id": "MPFT"}, {"type": "section", "block_id": "=W6Ih", "text": {"type": "mrkdwn", "text": "*flow_details*\n```{\"flow_id\": \"<http://steve.uat.steeleye.co:comms-voice-natterbox|steve.uat.steeleye.co:comms-voice-natterbox>\", \"file_url\": \"<s3://steve.uat.steeleye.co/flows/comms-voice-natterbox/2023/02/10/1676030311_4397154.csv>\", \"audit_id\": \"<http://steve.uat.steeleye.co:flows/comms-voice-natterbox/2023/02/10/1676030311_4397154.csv:1676890781204:_nil_|steve.uat.steeleye.co:flows/comms-voice-natterbox/2023/02/10/1676030311_4397154.csv:1676890781204:_nil_>\", \"email_notification\": true, \"use_dask_executor\": true, \"time_taken\": \"1s\"}```", "verbatim": false}}, {"type": "divider", "block_id": "oiT8"}, {"type": "section", "block_id": "lU3XY", "text": {"type": "mrkdwn", "text": "*traceback*\n```Traceback (most recent call last):\n  File \"/home/<USER>/venv/lib64/python3.7/site-packages/swarm/flow/runner.py\", line 302, in main\n    flow_runner.execute()\n  File \"/home/<USER>/venv/lib64/python3.7/site-packages/swarm/flow/runner.py\", line 85, in execute\n    \"Flow failed.\\n\" + json.dumps(audit_result.get_failed_tasks())\nException: Flow failed.\n{\"AssignMeta\": \"Trigger was \\\"all_successful\\\" but some of the upstream tasks failed.\", \"TriggerWaveformFlow\": \"Trigger was \\\"all_successful\\\" but some of the upstream tasks failed.\", \"ElasticBulkWriter\": \"Trigger was \\\"all_successful\\\" but some of the upstream tasks failed.\", \"FrameConcatenator\": \"Unexpected error: ValueError('No objects to concatenate')\", \"ElasticBulkTransformer\": \"Trigger was \\\"all_successful\\\" but some of the upstream tasks failed.\", \"TranscriptionRouter\": \"Trigger was \\\"all_successful\\\" but some of the upstream tasks failed.\"}\n```", "verbatim": false}}]}, {"type": "message", "subtype": "bot_message", "text": "Flow Runner Failed", "ts": "1676891022.195819", "username": "Flow Runner Failed", "bot_id": "B013H935ZRP", "app_id": "A01338VM16Z", "blocks": [{"type": "header", "block_id": "bnV", "text": {"type": "plain_text", "text": "Details", "emoji": true}}, {"type": "divider", "block_id": "rbNM"}, {"type": "section", "block_id": "zdw", "fields": [{"type": "mrkdwn", "text": "*flow_id*\n<http://steve.uat.steeleye.co:comms-voice-natterbox|steve.uat.steeleye.co:comms-voice-natterbox>", "verbatim": false}, {"type": "mrkdwn", "text": "*time_taken*\n1s", "verbatim": false}]}, {"type": "section", "block_id": "W/jns", "fields": [{"type": "mrkdwn", "text": "*file_url*\n<s3://steve.uat.steeleye.co/flows/comms-voice-natterbox/2023/02/10/1676027605_841586.csv>", "verbatim": false}, {"type": "mrkdwn", "text": "*environment*\nuat", "verbatim": false}]}, {"type": "divider", "block_id": "pa2"}, {"type": "section", "block_id": "49yA", "text": {"type": "mrkdwn", "text": "*aws_env_vars*\n```{\"batch_ce_name\": \"uat-blue-batch-compute-compute-oq\", \"batch_job_array_index\": \"\", \"batch_job_attempt\": \"1\", \"batch_job_id\": \"05e93092-d6c1-4bad-aff9-06083d7e11c9\", \"batch_job_main_node_index\": \"\", \"batch_job_main_node_private_ipv4_address\": \"\", \"batch_jon_node_index\": \"\", \"batch_job_num_nodes\": \"\", \"batch_jq_name\": \"uat-blue-batch-compute-queue\"}```", "verbatim": false}}, {"type": "divider", "block_id": "PqPW"}, {"type": "section", "block_id": "H2G9", "text": {"type": "mrkdwn", "text": "*flow_details*\n```{\"flow_id\": \"<http://steve.uat.steeleye.co:comms-voice-natterbox|steve.uat.steeleye.co:comms-voice-natterbox>\", \"file_url\": \"<s3://steve.uat.steeleye.co/flows/comms-voice-natterbox/2023/02/10/1676027605_841586.csv>\", \"audit_id\": \"<http://steve.uat.steeleye.co:flows/comms-voice-natterbox/2023/02/10/1676027605_841586.csv:1676890788652:_nil_|steve.uat.steeleye.co:flows/comms-voice-natterbox/2023/02/10/1676027605_841586.csv:1676890788652:_nil_>\", \"email_notification\": true, \"use_dask_executor\": true, \"time_taken\": \"1s\"}```", "verbatim": false}}, {"type": "divider", "block_id": "1zCt"}, {"type": "section", "block_id": "TE/v", "text": {"type": "mrkdwn", "text": "*traceback*\n```Traceback (most recent call last):\n  File \"/home/<USER>/venv/lib64/python3.7/site-packages/swarm/flow/runner.py\", line 302, in main\n    flow_runner.execute()\n  File \"/home/<USER>/venv/lib64/python3.7/site-packages/swarm/flow/runner.py\", line 85, in execute\n    \"Flow failed.\\n\" + json.dumps(audit_result.get_failed_tasks())\nException: Flow failed.\n{\"FrameConcatenator\": \"Unexpected error: ValueError('No objects to concatenate')\", \"ElasticBulkTransformer\": \"Trigger was \\\"all_successful\\\" but some of the upstream tasks failed.\", \"TranscriptionRouter\": \"Trigger was \\\"all_successful\\\" but some of the upstream tasks failed.\", \"AssignMeta\": \"Trigger was \\\"all_successful\\\" but some of the upstream tasks failed.\", \"TriggerWaveformFlow\": \"Trigger was \\\"all_successful\\\" but some of the upstream tasks failed.\", \"ElasticBulkWriter\": \"Trigger was \\\"all_successful\\\" but some of the upstream tasks failed.\"}\n```", "verbatim": false}}]}, {"type": "message", "subtype": "bot_message", "text": "Flow Runner Failed", "ts": "1676888059.083089", "username": "Flow Runner Failed", "bot_id": "B013H935ZRP", "app_id": "A01338VM16Z", "blocks": [{"type": "header", "block_id": "VhOZ", "text": {"type": "plain_text", "text": "Details", "emoji": true}}, {"type": "divider", "block_id": "yKC"}, {"type": "section", "block_id": "rnM", "fields": [{"type": "mrkdwn", "text": "*flow_id*\n<http://jason.uat.steeleye.co:order-feed-samco-bbg-audt-processor|jason.uat.steeleye.co:order-feed-samco-bbg-audt-processor>", "verbatim": false}, {"type": "mrkdwn", "text": "*time_taken*\n4s", "verbatim": false}]}, {"type": "section", "block_id": "lNr", "fields": [{"type": "mrkdwn", "text": "*file_url*\n<s3://jason.uat.steeleye.co/flows/order-feed-samco-bbg-audt-processor/SteelEyeDemoEQFIFXFUtest.csv>", "verbatim": false}, {"type": "mrkdwn", "text": "*environment*\nuat", "verbatim": false}]}, {"type": "divider", "block_id": "5EKdV"}, {"type": "section", "block_id": "I=+8z", "text": {"type": "mrkdwn", "text": "*aws_env_vars*\n```{\"batch_ce_name\": \"uat-blue-batch-compute-compute-oq\", \"batch_job_array_index\": \"\", \"batch_job_attempt\": \"1\", \"batch_job_id\": \"f4372dde-3c29-4cbc-a32c-202260cf56b6\", \"batch_job_main_node_index\": \"\", \"batch_job_main_node_private_ipv4_address\": \"\", \"batch_jon_node_index\": \"\", \"batch_job_num_nodes\": \"\", \"batch_jq_name\": \"uat-blue-batch-compute-queue\"}```", "verbatim": false}}, {"type": "divider", "block_id": "rVHlh"}, {"type": "section", "block_id": "Znc", "text": {"type": "mrkdwn", "text": "*flow_details*\n```{\"flow_id\": \"<http://jason.uat.steeleye.co:order-feed-samco-bbg-audt-processor|jason.uat.steeleye.co:order-feed-samco-bbg-audt-processor>\", \"file_url\": \"<s3://jason.uat.steeleye.co/flows/order-feed-samco-bbg-audt-processor/SteelEyeDemoEQFIFXFUtest.csv>\", \"audit_id\": \"<http://jason.uat.steeleye.co:flows/order-feed-samco-bbg-audt-processor/steeleyedemoeqfifxfutest.csv:1676887848623:_nil_|jason.uat.steeleye.co:flows/order-feed-samco-bbg-audt-processor/steeleyedemoeqfifxfutest.csv:1676887848623:_nil_>\", \"email_notification\": true, \"use_dask_executor\": true, \"time_taken\": \"4s\"}```", "verbatim": false}}, {"type": "divider", "block_id": "tDH"}, {"type": "section", "block_id": "GmpL", "text": {"type": "mrkdwn", "text": "*traceback*\n```Traceback (most recent call last):\n  File \"/home/<USER>/venv/lib64/python3.7/site-packages/swarm/flow/runner.py\", line 311, in main\n    flow_runner.execute()\n  File \"/home/<USER>/venv/lib64/python3.7/site-packages/swarm/flow/runner.py\", line 94, in execute\n    \"Flow failed.\\n\" + json.dumps(audit_result.get_failed_tasks())\nException: Flow failed.\n{}\n```", "verbatim": false}}]}, {"type": "message", "subtype": "bot_message", "text": "Flow Runner Failed", "ts": "1676886035.327469", "username": "Flow Runner Failed", "bot_id": "B013H935ZRP", "app_id": "A01338VM16Z", "blocks": [{"type": "header", "block_id": "SslgJ", "text": {"type": "plain_text", "text": "Details", "emoji": true}}, {"type": "divider", "block_id": "AoO"}, {"type": "section", "block_id": "3EXgK", "fields": [{"type": "mrkdwn", "text": "*flow_id*\n<http://ben.uat.steeleye.co:order-feed-samco-bbg-audt-processor|ben.uat.steeleye.co:order-feed-samco-bbg-audt-processor>", "verbatim": false}, {"type": "mrkdwn", "text": "*time_taken*\n4s", "verbatim": false}]}, {"type": "section", "block_id": "yy+", "fields": [{"type": "mrkdwn", "text": "*file_url*\n<s3://ben.uat.steeleye.co/flows/order-feed-samco-bbg-audt-processor/SteelEyeDemoEQFIFXFU+%281%29.csv>", "verbatim": false}, {"type": "mrkdwn", "text": "*environment*\nuat", "verbatim": false}]}, {"type": "divider", "block_id": "unfZW"}, {"type": "section", "block_id": "MGFP", "text": {"type": "mrkdwn", "text": "*aws_env_vars*\n```{\"batch_ce_name\": \"uat-blue-batch-compute-compute-oq\", \"batch_job_array_index\": \"\", \"batch_job_attempt\": \"1\", \"batch_job_id\": \"81a67f3e-f4d5-44ab-a4b9-5c7a9007e14c\", \"batch_job_main_node_index\": \"\", \"batch_job_main_node_private_ipv4_address\": \"\", \"batch_jon_node_index\": \"\", \"batch_job_num_nodes\": \"\", \"batch_jq_name\": \"uat-blue-batch-compute-queue\"}```", "verbatim": false}}, {"type": "divider", "block_id": "ox2"}, {"type": "section", "block_id": "7Cyh", "text": {"type": "mrkdwn", "text": "*flow_details*\n```{\"flow_id\": \"<http://ben.uat.steeleye.co:order-feed-samco-bbg-audt-processor|ben.uat.steeleye.co:order-feed-samco-bbg-audt-processor>\", \"file_url\": \"<s3://ben.uat.steeleye.co/flows/order-feed-samco-bbg-audt-processor/SteelEyeDemoEQFIFXFU+%281%29.csv>\", \"audit_id\": \"<http://ben.uat.steeleye.co:flows/order-feed-samco-bbg-audt-processor/steeleyedemoeqfifxfu+%281%29.csv:1676885773979:_nil_|ben.uat.steeleye.co:flows/order-feed-samco-bbg-audt-processor/steeleyedemoeqfifxfu+%281%29.csv:1676885773979:_nil_>\", \"email_notification\": true, \"use_dask_executor\": true, \"time_taken\": \"4s\"}```", "verbatim": false}}, {"type": "divider", "block_id": "NG20"}, {"type": "section", "block_id": "F2f", "text": {"type": "mrkdwn", "text": "*traceback*\n```Traceback (most recent call last):\n  File \"/home/<USER>/venv/lib64/python3.7/site-packages/swarm/flow/runner.py\", line 311, in main\n    flow_runner.execute()\n  File \"/home/<USER>/venv/lib64/python3.7/site-packages/swarm/flow/runner.py\", line 94, in execute\n    \"Flow failed.\\n\" + json.dumps(audit_result.get_failed_tasks())\nException: Flow failed.\n{}\n```", "verbatim": false}}]}, {"type": "message", "subtype": "bot_message", "text": "Flow Runner Failed", "ts": "1676884444.647709", "username": "Flow Runner Failed", "bot_id": "B013H935ZRP", "app_id": "A01338VM16Z", "blocks": [{"type": "header", "block_id": "LWT5", "text": {"type": "plain_text", "text": "Details", "emoji": true}}, {"type": "divider", "block_id": "bUMcs"}, {"type": "section", "block_id": "k+951", "fields": [{"type": "mrkdwn", "text": "*flow_id*\n<http://ben.uat.steeleye.co:order-feed-samco-bbg-audt-processor|ben.uat.steeleye.co:order-feed-samco-bbg-audt-processor>", "verbatim": false}, {"type": "mrkdwn", "text": "*time_taken*\n3s", "verbatim": false}]}, {"type": "section", "block_id": "5Hk", "fields": [{"type": "mrkdwn", "text": "*file_url*\n<s3://ben.uat.steeleye.co/flows/order-feed-samco-bbg-audt-processor/FTN1238884.csv>", "verbatim": false}, {"type": "mrkdwn", "text": "*environment*\nuat", "verbatim": false}]}, {"type": "divider", "block_id": "ngL"}, {"type": "section", "block_id": "IXf", "text": {"type": "mrkdwn", "text": "*aws_env_vars*\n```{\"batch_ce_name\": \"uat-blue-batch-compute-compute-oq\", \"batch_job_array_index\": \"\", \"batch_job_attempt\": \"1\", \"batch_job_id\": \"623694b4-a70a-4c79-bba4-e961e42549fe\", \"batch_job_main_node_index\": \"\", \"batch_job_main_node_private_ipv4_address\": \"\", \"batch_jon_node_index\": \"\", \"batch_job_num_nodes\": \"\", \"batch_jq_name\": \"uat-blue-batch-compute-queue\"}```", "verbatim": false}}, {"type": "divider", "block_id": "OOyVt"}, {"type": "section", "block_id": "=yn", "text": {"type": "mrkdwn", "text": "*flow_details*\n```{\"flow_id\": \"<http://ben.uat.steeleye.co:order-feed-samco-bbg-audt-processor|ben.uat.steeleye.co:order-feed-samco-bbg-audt-processor>\", \"file_url\": \"<s3://ben.uat.steeleye.co/flows/order-feed-samco-bbg-audt-processor/FTN1238884.csv>\", \"audit_id\": \"<http://ben.uat.steeleye.co:flows/order-feed-samco-bbg-audt-processor/ftn1238884.csv:1676884301147:_nil_|ben.uat.steeleye.co:flows/order-feed-samco-bbg-audt-processor/ftn1238884.csv:1676884301147:_nil_>\", \"email_notification\": true, \"use_dask_executor\": true, \"time_taken\": \"3s\"}```", "verbatim": false}}, {"type": "divider", "block_id": "tUj"}, {"type": "section", "block_id": "nuaS3", "text": {"type": "mrkdwn", "text": "*traceback*\n```Traceback (most recent call last):\n  File \"/home/<USER>/venv/lib64/python3.7/site-packages/swarm/flow/runner.py\", line 311, in main\n    flow_runner.execute()\n  File \"/home/<USER>/venv/lib64/python3.7/site-packages/swarm/flow/runner.py\", line 94, in execute\n    \"Flow failed.\\n\" + json.dumps(audit_result.get_failed_tasks())\nException: Flow failed.\n{}\n```", "verbatim": false}}]}, {"type": "message", "subtype": "bot_message", "text": "Flow Runner Failed", "ts": "1676884444.621339", "username": "Flow Runner Failed", "bot_id": "B013H935ZRP", "app_id": "A01338VM16Z", "blocks": [{"type": "header", "block_id": "mIlU", "text": {"type": "plain_text", "text": "Details", "emoji": true}}, {"type": "divider", "block_id": "GcE"}, {"type": "section", "block_id": "n1IJc", "fields": [{"type": "mrkdwn", "text": "*flow_id*\n<http://ben.uat.steeleye.co:order-feed-samco-bbg-audt-processor|ben.uat.steeleye.co:order-feed-samco-bbg-audt-processor>", "verbatim": false}, {"type": "mrkdwn", "text": "*time_taken*\n3s", "verbatim": false}]}, {"type": "section", "block_id": "M94A8", "fields": [{"type": "mrkdwn", "text": "*file_url*\n<s3://ben.uat.steeleye.co/flows/order-feed-samco-bbg-audt-processor/FTN1238957.csv>", "verbatim": false}, {"type": "mrkdwn", "text": "*environment*\nuat", "verbatim": false}]}, {"type": "divider", "block_id": "UIb"}, {"type": "section", "block_id": "sktzR", "text": {"type": "mrkdwn", "text": "*aws_env_vars*\n```{\"batch_ce_name\": \"uat-blue-batch-compute-compute-oq\", \"batch_job_array_index\": \"\", \"batch_job_attempt\": \"1\", \"batch_job_id\": \"0ddd7f5c-549f-42ae-a9a4-d01cef26fe26\", \"batch_job_main_node_index\": \"\", \"batch_job_main_node_private_ipv4_address\": \"\", \"batch_jon_node_index\": \"\", \"batch_job_num_nodes\": \"\", \"batch_jq_name\": \"uat-blue-batch-compute-queue\"}```", "verbatim": false}}, {"type": "divider", "block_id": "nLBZ"}, {"type": "section", "block_id": "/t/", "text": {"type": "mrkdwn", "text": "*flow_details*\n```{\"flow_id\": \"<http://ben.uat.steeleye.co:order-feed-samco-bbg-audt-processor|ben.uat.steeleye.co:order-feed-samco-bbg-audt-processor>\", \"file_url\": \"<s3://ben.uat.steeleye.co/flows/order-feed-samco-bbg-audt-processor/FTN1238957.csv>\", \"audit_id\": \"<http://ben.uat.steeleye.co:flows/order-feed-samco-bbg-audt-processor/ftn1238957.csv:1676884307555:_nil_|ben.uat.steeleye.co:flows/order-feed-samco-bbg-audt-processor/ftn1238957.csv:1676884307555:_nil_>\", \"email_notification\": true, \"use_dask_executor\": true, \"time_taken\": \"3s\"}```", "verbatim": false}}, {"type": "divider", "block_id": "QH6L"}, {"type": "section", "block_id": "ka4x", "text": {"type": "mrkdwn", "text": "*traceback*\n```Traceback (most recent call last):\n  File \"/home/<USER>/venv/lib64/python3.7/site-packages/swarm/flow/runner.py\", line 311, in main\n    flow_runner.execute()\n  File \"/home/<USER>/venv/lib64/python3.7/site-packages/swarm/flow/runner.py\", line 94, in execute\n    \"Flow failed.\\n\" + json.dumps(audit_result.get_failed_tasks())\nException: Flow failed.\n{}\n```", "verbatim": false}}]}, {"type": "message", "subtype": "bot_message", "text": "Flow Runner Failed", "ts": "1676884408.653209", "username": "Flow Runner Failed", "bot_id": "B013H935ZRP", "app_id": "A01338VM16Z", "blocks": [{"type": "header", "block_id": "qUta", "text": {"type": "plain_text", "text": "Details", "emoji": true}}, {"type": "divider", "block_id": "cnDTo"}, {"type": "section", "block_id": "6sfQ", "fields": [{"type": "mrkdwn", "text": "*flow_id*\n<http://ben.uat.steeleye.co:order-feed-samco-bbg-audt-processor|ben.uat.steeleye.co:order-feed-samco-bbg-audt-processor>", "verbatim": false}, {"type": "mrkdwn", "text": "*time_taken*\n3s", "verbatim": false}]}, {"type": "section", "block_id": "sdtr", "fields": [{"type": "mrkdwn", "text": "*file_url*\n<s3://ben.uat.steeleye.co/flows/order-feed-samco-bbg-audt-processor/1244447.csv>", "verbatim": false}, {"type": "mrkdwn", "text": "*environment*\nuat", "verbatim": false}]}, {"type": "divider", "block_id": "sOjYI"}, {"type": "section", "block_id": "Tcu+=", "text": {"type": "mrkdwn", "text": "*aws_env_vars*\n```{\"batch_ce_name\": \"uat-blue-batch-compute-compute-oq\", \"batch_job_array_index\": \"\", \"batch_job_attempt\": \"1\", \"batch_job_id\": \"407188e7-7ec5-4b3e-be90-c56d06c0d5b7\", \"batch_job_main_node_index\": \"\", \"batch_job_main_node_private_ipv4_address\": \"\", \"batch_jon_node_index\": \"\", \"batch_job_num_nodes\": \"\", \"batch_jq_name\": \"uat-blue-batch-compute-queue\"}```", "verbatim": false}}, {"type": "divider", "block_id": "Rq3"}, {"type": "section", "block_id": "/5U3", "text": {"type": "mrkdwn", "text": "*flow_details*\n```{\"flow_id\": \"<http://ben.uat.steeleye.co:order-feed-samco-bbg-audt-processor|ben.uat.steeleye.co:order-feed-samco-bbg-audt-processor>\", \"file_url\": \"<s3://ben.uat.steeleye.co/flows/order-feed-samco-bbg-audt-processor/1244447.csv>\", \"audit_id\": \"<http://ben.uat.steeleye.co:flows/order-feed-samco-bbg-audt-processor/1244447.csv:1676884278071:_nil_|ben.uat.steeleye.co:flows/order-feed-samco-bbg-audt-processor/1244447.csv:1676884278071:_nil_>\", \"email_notification\": true, \"use_dask_executor\": true, \"time_taken\": \"3s\"}```", "verbatim": false}}, {"type": "divider", "block_id": "0ahjR"}, {"type": "section", "block_id": "JfFFA", "text": {"type": "mrkdwn", "text": "*traceback*\n```Traceback (most recent call last):\n  File \"/home/<USER>/venv/lib64/python3.7/site-packages/swarm/flow/runner.py\", line 311, in main\n    flow_runner.execute()\n  File \"/home/<USER>/venv/lib64/python3.7/site-packages/swarm/flow/runner.py\", line 94, in execute\n    \"Flow failed.\\n\" + json.dumps(audit_result.get_failed_tasks())\nException: Flow failed.\n{}\n```", "verbatim": false}}]}, {"type": "message", "subtype": "bot_message", "text": "Flow Runner Failed", "ts": "1676884392.109089", "username": "Flow Runner Failed", "bot_id": "B013H935ZRP", "app_id": "A01338VM16Z", "blocks": [{"type": "header", "block_id": "yEQS", "text": {"type": "plain_text", "text": "Details", "emoji": true}}, {"type": "divider", "block_id": "VRZ"}, {"type": "section", "block_id": "oskMH", "fields": [{"type": "mrkdwn", "text": "*flow_id*\n<http://ben.uat.steeleye.co:order-feed-samco-bbg-audt-processor|ben.uat.steeleye.co:order-feed-samco-bbg-audt-processor>", "verbatim": false}, {"type": "mrkdwn", "text": "*time_taken*\n3s", "verbatim": false}]}, {"type": "section", "block_id": "Jxg", "fields": [{"type": "mrkdwn", "text": "*file_url*\n<s3://ben.uat.steeleye.co/flows/order-feed-samco-bbg-audt-processor/1245728.csv>", "verbatim": false}, {"type": "mrkdwn", "text": "*environment*\nuat", "verbatim": false}]}, {"type": "divider", "block_id": "0/d"}, {"type": "section", "block_id": "YImD", "text": {"type": "mrkdwn", "text": "*aws_env_vars*\n```{\"batch_ce_name\": \"uat-blue-batch-compute-compute-oq\", \"batch_job_array_index\": \"\", \"batch_job_attempt\": \"1\", \"batch_job_id\": \"d270d44c-3a9c-4097-ae79-c61be3e21e6e\", \"batch_job_main_node_index\": \"\", \"batch_job_main_node_private_ipv4_address\": \"\", \"batch_jon_node_index\": \"\", \"batch_job_num_nodes\": \"\", \"batch_jq_name\": \"uat-blue-batch-compute-queue\"}```", "verbatim": false}}, {"type": "divider", "block_id": "t77/O"}, {"type": "section", "block_id": "1FYS", "text": {"type": "mrkdwn", "text": "*flow_details*\n```{\"flow_id\": \"<http://ben.uat.steeleye.co:order-feed-samco-bbg-audt-processor|ben.uat.steeleye.co:order-feed-samco-bbg-audt-processor>\", \"file_url\": \"<s3://ben.uat.steeleye.co/flows/order-feed-samco-bbg-audt-processor/1245728.csv>\", \"audit_id\": \"<http://ben.uat.steeleye.co:flows/order-feed-samco-bbg-audt-processor/1245728.csv:1676884253394:_nil_|ben.uat.steeleye.co:flows/order-feed-samco-bbg-audt-processor/1245728.csv:1676884253394:_nil_>\", \"email_notification\": true, \"use_dask_executor\": true, \"time_taken\": \"3s\"}```", "verbatim": false}}, {"type": "divider", "block_id": "ihR7+"}, {"type": "section", "block_id": "1/4j", "text": {"type": "mrkdwn", "text": "*traceback*\n```Traceback (most recent call last):\n  File \"/home/<USER>/venv/lib64/python3.7/site-packages/swarm/flow/runner.py\", line 311, in main\n    flow_runner.execute()\n  File \"/home/<USER>/venv/lib64/python3.7/site-packages/swarm/flow/runner.py\", line 94, in execute\n    \"Flow failed.\\n\" + json.dumps(audit_result.get_failed_tasks())\nException: Flow failed.\n{}\n```", "verbatim": false}}]}, {"type": "message", "subtype": "bot_message", "text": "Flow Runner Failed", "ts": "1676593190.157229", "username": "Flow Runner Failed", "bot_id": "B013H935ZRP", "app_id": "A01338VM16Z", "blocks": [{"type": "header", "block_id": "BYM", "text": {"type": "plain_text", "text": "Details", "emoji": true}}, {"type": "divider", "block_id": "/YaRy"}, {"type": "section", "block_id": "5r6lc", "fields": [{"type": "mrkdwn", "text": "*flow_id*\n<http://victor.uat.steeleye.co:csv-to-record|victor.uat.steeleye.co:csv-to-record>", "verbatim": false}, {"type": "mrkdwn", "text": "*time_taken*\n01h01m46s", "verbatim": false}]}, {"type": "section", "block_id": "tcRu", "fields": [{"type": "mrkdwn", "text": "*file_url*\n<s3://victor.uat.steeleye.co/flows/csv-to-record/Order/steeleyeBlotter.base.schema.250000.multiDay/steeleyeBlotter.base.schema.250000.csv.multiDay.16.csv>", "verbatim": false}, {"type": "mrkdwn", "text": "*environment*\nuat", "verbatim": false}]}, {"type": "divider", "block_id": "GKb=U"}, {"type": "section", "block_id": "Kt+", "text": {"type": "mrkdwn", "text": "*aws_env_vars*\n```{\"batch_ce_name\": \"uat-benchmark-batch-compute-compute-ph\", \"batch_job_array_index\": \"\", \"batch_job_attempt\": \"1\", \"batch_job_id\": \"991443e5-167f-4f20-a4dd-c799b7d19631\", \"batch_job_main_node_index\": \"\", \"batch_job_main_node_private_ipv4_address\": \"\", \"batch_jon_node_index\": \"\", \"batch_job_num_nodes\": \"\", \"batch_jq_name\": \"uat-benchmark-batch-compute-queue\"}```", "verbatim": false}}, {"type": "divider", "block_id": "+CK4"}, {"type": "section", "block_id": "NdGVs", "text": {"type": "mrkdwn", "text": "*flow_details*\n```{\"flow_id\": \"<http://victor.uat.steeleye.co:csv-to-record|victor.uat.steeleye.co:csv-to-record>\", \"file_url\": \"<s3://victor.uat.steeleye.co/flows/csv-to-record/Order/steeleyeBlotter.base.schema.250000.multiDay/steeleyeBlotter.base.schema.250000.csv.multiDay.16.csv>\", \"audit_id\": \"<http://victor.uat.steeleye.co:flows/csv-to-record/order/steeleyeblotter.base.schema.250000.multiday/steeleyeblotter.base.schema.250000.csv.multiday.16.csv:1676585277270:_nil_|victor.uat.steeleye.co:flows/csv-to-record/order/steeleyeblotter.base.schema.250000.multiday/steeleyeblotter.base.schema.250000.csv.multiday.16.csv:1676585277270:_nil_>\", \"email_notification\": true, \"use_dask_executor\": true, \"time_taken\": \"01h01m46s\"}```", "verbatim": false}}, {"type": "divider", "block_id": "dy="}, {"type": "section", "block_id": "FhrY", "text": {"type": "mrkdwn", "text": "*traceback*\n```Traceback (most recent call last):\n  File \"/home/<USER>/venv/lib64/python3.7/site-packages/swarm/flow/runner.py\", line 302, in main\n    flow_runner.execute()\n  File \"/home/<USER>/venv/lib64/python3.7/site-packages/swarm/flow/runner.py\", line 85, in execute\n    \"Flow failed.\\n\" + json.dumps(audit_result.get_failed_tasks())\nException: Flow failed.\n{}\n```", "verbatim": false}}]}, {"type": "message", "subtype": "bot_message", "text": "Flow Runner Failed", "ts": "1676589436.848959", "username": "Flow Runner Failed", "bot_id": "B013H935ZRP", "app_id": "A01338VM16Z", "blocks": [{"type": "header", "block_id": "S1OnT", "text": {"type": "plain_text", "text": "Details", "emoji": true}}, {"type": "divider", "block_id": "K8g"}, {"type": "section", "block_id": "sAg/S", "fields": [{"type": "mrkdwn", "text": "*flow_id*\n<http://victor.uat.steeleye.co:csv-to-record|victor.uat.steeleye.co:csv-to-record>", "verbatim": false}, {"type": "mrkdwn", "text": "*time_taken*\n01h05m29s", "verbatim": false}]}, {"type": "section", "block_id": "e1fF", "fields": [{"type": "mrkdwn", "text": "*file_url*\n<s3://victor.uat.steeleye.co/flows/csv-to-record/Order/steeleyeBlotter.base.schema.250000.multiDay/steeleyeBlotter.base.schema.250000.csv.multiDay.27.csv>", "verbatim": false}, {"type": "mrkdwn", "text": "*environment*\nuat", "verbatim": false}]}, {"type": "divider", "block_id": "4tVV"}, {"type": "section", "block_id": "wE2Y", "text": {"type": "mrkdwn", "text": "*aws_env_vars*\n```{\"batch_ce_name\": \"uat-benchmark-batch-compute-compute-ph\", \"batch_job_array_index\": \"\", \"batch_job_attempt\": \"1\", \"batch_job_id\": \"4fae12be-c013-4673-9611-a7ccc6931284\", \"batch_job_main_node_index\": \"\", \"batch_job_main_node_private_ipv4_address\": \"\", \"batch_jon_node_index\": \"\", \"batch_job_num_nodes\": \"\", \"batch_jq_name\": \"uat-benchmark-batch-compute-queue\"}```", "verbatim": false}}, {"type": "divider", "block_id": "LRgD/"}, {"type": "section", "block_id": "P=6", "text": {"type": "mrkdwn", "text": "*flow_details*\n```{\"flow_id\": \"<http://victor.uat.steeleye.co:csv-to-record|victor.uat.steeleye.co:csv-to-record>\", \"file_url\": \"<s3://victor.uat.steeleye.co/flows/csv-to-record/Order/steeleyeBlotter.base.schema.250000.multiDay/steeleyeBlotter.base.schema.250000.csv.multiDay.27.csv>\", \"audit_id\": \"<http://victor.uat.steeleye.co:flows/csv-to-record/order/steeleyeblotter.base.schema.250000.multiday/steeleyeblotter.base.schema.250000.csv.multiday.27.csv:1676585251323:_nil_|victor.uat.steeleye.co:flows/csv-to-record/order/steeleyeblotter.base.schema.250000.multiday/steeleyeblotter.base.schema.250000.csv.multiday.27.csv:1676585251323:_nil_>\", \"email_notification\": true, \"use_dask_executor\": true, \"time_taken\": \"01h05m29s\"}```", "verbatim": false}}, {"type": "divider", "block_id": "7cz"}, {"type": "section", "block_id": "BRJEq", "text": {"type": "mrkdwn", "text": "*traceback*\n```Traceback (most recent call last):\n  File \"/home/<USER>/venv/lib64/python3.7/site-packages/swarm/flow/runner.py\", line 302, in main\n    flow_runner.execute()\n  File \"/home/<USER>/venv/lib64/python3.7/site-packages/swarm/flow/runner.py\", line 85, in execute\n    \"Flow failed.\\n\" + json.dumps(audit_result.get_failed_tasks())\nException: Flow failed.\n{}\n```", "verbatim": false}}]}], "members": ["U012PU2ACPR", "U01DFSQNYNB", "U01PGFLH47Q", "U01TDJ03WHW", "U03G5R5PJRM", "U03SLP52804", "U04LEV1U8AK"], "info": {"id": "C04NAQPRV6H", "name": "swarm-alerts-uat", "created": 1675764629, "member_count": 7, "is_general": false, "is_private": false, "is_im": false, "is_mpim": false, "is_file": false, "is_deleted": false, "is_archived": false, "creator": "U04LEV1U8AK", "is_moved": false, "is_shared": false, "name_normalized": "swarm-alerts-uat", "is_global_shared": false, "is_org_shared": false, "is_org_mandatory": false, "is_org_default": false, "previous_names": [], "purpose": {"text": "Swarm Alerts for Flows in UAT", "set_by": "U01TDJ03WHW", "date_set": 1675855228}, "topic": {"text": "", "set_by": "", "date_set": 0}, "retention": {"type": "default", "duration": 0}, "shared": {"shared_team_ids": [], "connected_team_ids": [], "internal_team_ids": [], "connected_limited_team_ids": []}, "has_guests": false}, "edits": []}
{"messages": [{"client_msg_id": "87bd6d73-f0bc-453c-b49d-fa12c99f3df8", "type": "message", "text": "cool thanks, i will work on what ever is agreed, anything not agreed in writing i will not work on", "user": "U01AMPACW4Q", "ts": "1676909573.034549", "blocks": [{"type": "rich_text", "block_id": "Zw9", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "cool thanks, i will work on what ever is agreed, anything not agreed in writing i will not work on"}]}]}], "team": "T3P0PGX18"}, {"client_msg_id": "58CC3769-12A9-4542-AC1D-1882A4F45E10", "type": "message", "text": "Ok. This makes sense to me. I\u2019m off this week but will arrange for the meet with James and Adrien to discuss ", "user": "U039GG2J75L", "ts": "1676908952.124039", "blocks": [{"type": "rich_text", "block_id": "s6bus", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Ok. This makes sense to me. I\u2019m off this week but will arrange for the meet with James and Adrien to discuss "}]}]}], "team": "T3P0PGX18"}, {"client_msg_id": "2ce3aaa8-221c-45ca-bf10-805c97f96527", "type": "message", "text": "please could you co ordinate so that we are all on the same page", "user": "U01AMPACW4Q", "ts": "1676893789.589929", "blocks": [{"type": "rich_text", "block_id": "A9B", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "please could you co ordinate so that we are all on the same page"}]}]}], "team": "T3P0PGX18"}, {"client_msg_id": "cd4593fe-6266-499f-b079-824fa8b3560f", "type": "message", "text": "I think a agreement is needed between us and them to realign expectations", "user": "U01AMPACW4Q", "ts": "1676893771.685019", "blocks": [{"type": "rich_text", "block_id": "WJZ1", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "I think a agreement is needed between us and them to realign expectations"}]}]}], "team": "T3P0PGX18"}, {"client_msg_id": "0c561a06-1713-4f69-876b-f575c0fd0e05", "type": "message", "text": "they also are not sure exactly what they need/want/should have as part of the POC", "user": "U01AMPACW4Q", "ts": "1676893748.446559", "blocks": [{"type": "rich_text", "block_id": "ZVc1", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "they also are not sure exactly what they need/want/should have as part of the POC"}]}]}], "team": "T3P0PGX18"}, {"client_msg_id": "8706191a-4234-482c-89fe-be6f745f8727", "type": "message", "text": "did", "user": "U01AMPACW4Q", "ts": "1676893713.330239", "blocks": [{"type": "rich_text", "block_id": "qyZK", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "did"}]}]}], "team": "T3P0PGX18"}, {"client_msg_id": "4e69e1f7-5f58-4d41-87c0-6a7e6a0c4781", "type": "message", "text": "i think they believe they didt", "user": "U01AMPACW4Q", "ts": "1676893712.104219", "blocks": [{"type": "rich_text", "block_id": "XeujD", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "i think they believe they didt"}]}]}], "team": "T3P0PGX18"}, {"client_msg_id": "72c554e3-de45-434c-9fa7-c779432245d2", "type": "message", "text": "Looking at Caxtons order form it doesn't look like they have transcription", "user": "U01AMPACW4Q", "ts": "1676893702.093039", "blocks": [{"type": "rich_text", "block_id": "5RhB7", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "Looking at Caxtons order form it doesn't look like they have transcription"}]}]}], "team": "T3P0PGX18"}, {"client_msg_id": "0827c87c-0ed3-4656-9d89-ae17e61b9e62", "type": "message", "text": "hi <@U039GG2J75L>", "user": "U01AMPACW4Q", "ts": "1676893662.092189", "blocks": [{"type": "rich_text", "block_id": "Lc5L", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "hi "}, {"type": "user", "user_id": "U039GG2J75L"}]}]}], "team": "T3P0PGX18"}], "members": ["U4PA3EVJT", "UGH78B1PG", "UMRDJ231P", "U01AMPACW4Q", "U039GG2J75L"], "info": {"id": "C04CDST8657", "name": "client-caxton", "created": 1669126394, "member_count": 5, "is_general": false, "is_private": false, "is_im": false, "is_mpim": false, "is_file": false, "is_deleted": false, "is_archived": false, "creator": "UGH78B1PG", "is_moved": false, "is_shared": false, "name_normalized": "client-caxton", "is_global_shared": false, "is_org_shared": false, "is_org_mandatory": false, "is_org_default": false, "previous_names": [], "purpose": {"text": "", "set_by": "", "date_set": 0}, "topic": {"text": "", "set_by": "", "date_set": 0}, "retention": {"type": "default", "duration": 0}, "shared": {"shared_team_ids": [], "connected_team_ids": [], "internal_team_ids": [], "connected_limited_team_ids": []}, "has_guests": false}, "edits": []}
