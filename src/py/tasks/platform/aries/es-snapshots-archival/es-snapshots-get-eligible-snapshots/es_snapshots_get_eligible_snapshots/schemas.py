"""Schemas useful to validate whether input parameters are appropriate."""

from pydantic import BaseModel, Field, validator


def check_if_empty(value):
    if not value:
        raise ValueError("value cannot be none or empty string")
    return value


class TransitionRule(BaseModel):
    name: str
    aws_storage_class: str
    retention_months: int

    @validator(
        "name",
        "aws_storage_class",
        "retention_months",
    )
    def is_empty(cls, field_value):
        return check_if_empty(field_value)


class AriesInputParams(BaseModel):
    """Data model for Aries Input Params."""

    es_snapshots_bucket_name: str | None = Field(default=None)
    es_snapshots_s3_path: str | None = Field(default=None)
    es_snapshots_transition_rules: list[TransitionRule] | None = Field(
        default=None,
        description=(
            "Transition rules, should be in valid format list[dict(str, str | int)]. Ex: "
            '[{"name": "<str-value>", "aws_storage_class": "<str-value>", '
            '"retention_months": int-value}]'
        ),
    )
    es_allowed_stats_diff_percentage: int | float | None = Field(default=None)
    es_snapshots_metadata_file_path: str | None = Field(default=None)
    skip_metadata_file_check: bool = Field(default=False)

    @validator(
        "es_snapshots_bucket_name",
        "es_snapshots_s3_path",
        "es_snapshots_transition_rules",
        "es_allowed_stats_diff_percentage",
        "es_snapshots_metadata_file_path",
    )
    def is_empty(cls, field_value):
        return check_if_empty(field_value)
