import addict
import botocore.exceptions
import logging
import os
import time
from es_snapshots_archival_utils import archival_utils
from es_snapshots_process_snapshots.utils import post_error_slack_message
from se_s3_utils import s3_utils

logger = logging.getLogger("es-snapshot-process-snapshots-meta-files")


def process_metadata_files(
    stack: str,
    bucket_name: str,
    snapshot_repository: str,
    s3_snapshot_path: str,
    s3_tar_file_path: str,
    slack_webhook_url: str,
    aries_task_name: str,
    resources: addict.Dict,
    skip_files: list[str] = [],
):
    """Processes index meta files tars them and uploads them to S3, if not
    already exists in S3.

    Params:
        stack (str): Stack name
        bucket_name (str): S3 bucket name
        snapshot_repository (str): Snapshot repository name
        s3_snapshot_path (str): S3 ES snapshot path
        s3_tar_file_path (str): S3 tar file path where the tar files are uploaded
        slack_webhook_url (str): Slack webhook url
        aries_task_name (str): Task name, used in producing slack messages
        resources (addict.Dict): addict Dict with s3_client, config, download paths etc
        skip_files (list[str]): Used for skipping index meta files

    Returns:
    Raises:
        botocore.exceptions.ClientError
    """
    _start_time = time.time()
    # getting download paths
    meta_files_download_folder = resources.download_paths.meta_files_download_folder
    tarred_files_folder = resources.download_paths.tarred_files_folder

    total_metadata_files = 0
    logger.info("Started processing metadata files")
    try:
        # Checking if tar file already exists in S3, we can skip the process if already exists
        tar_file_path = archival_utils.get_tar_file_path(
            source_folder=meta_files_download_folder,
            tarred_folder=tarred_files_folder,
        )
        tar_file_name = os.path.basename(tar_file_path)
        s3_tarfile_upload_path = os.path.join(s3_tar_file_path, tar_file_name)
        _exists = s3_utils.file_exists(
            s3_client=resources.s3_client,
            bucket_name=bucket_name,
            s3_key=s3_tarfile_upload_path,
        )
        if _exists:
            # We don't need to proceed further as tar file already exists in S3
            logger.info(
                f"Metadata files tar already exists in S3, S3Path: {s3_tarfile_upload_path}. "
                "Skipping process."
            )
            archival_utils.clean_up(files=[meta_files_download_folder, tarred_files_folder])
            return

        # Using aws list_objects_v2 api instead of aws cli, because cli doesn't
        # return just metadata files using delimiter
        for results in s3_utils.list_objects(
            s3_client=resources.s3_client,
            bucket_name=bucket_name,
            prefix=s3_snapshot_path,
            delimiter="/",
        ):
            files = [i["Key"] for i in results["Contents"]]
            total_metadata_files += len(files)
            # Skipping index files from the list which are already uploaded.
            files_process = [i for i in files if i not in skip_files]
            if files_process:
                # Using aws download_file api instead of aws sync cli, because we are downloading
                # multiple files without any common prefix. cli will be useful if we have prefix
                archival_utils.download_files_from_s3_parallel(
                    s3_client=resources.s3_client,
                    bucket_name=bucket_name,
                    prefix=s3_snapshot_path,
                    files=files,
                    download_folder=meta_files_download_folder,
                    object_type="metadata",
                )
            else:
                logger.info("All metadata files in the subset are already downloaded")

        # Creating tar file with all the downloaded metadata files
        tar_file_path = archival_utils.create_tar_file_cli(
            source_folder=meta_files_download_folder,
            destination_folder=tarred_files_folder,
        )

        # Using aws cli cp command to upload meta files tar
        archival_utils.upload_with_aws_cp(
            bucket_name=bucket_name,
            upload_path=s3_tarfile_upload_path,
            local_file_path=tar_file_path,
            storage_class=archival_utils.S3_DEEP_ARCHIVE_STORAGE,
        )
    except (
        botocore.exceptions.ClientError,
        archival_utils.CalledSubProcessError,
    ) as e:
        error_message = f"Failed to process Index Meta Data Files, Error: {e}"
        post_error_slack_message(
            stack=stack,
            bucket_name=bucket_name,
            snapshot_repository=snapshot_repository,
            s3_snapshot_path=s3_snapshot_path,
            slack_webhook_url=slack_webhook_url,
            aries_task_name=aries_task_name,
            error_message=error_message,
        )
        raise e
    finally:
        # This clause satisfies in all cases either success or error.
        archival_utils.clean_up(files=[meta_files_download_folder, tarred_files_folder])

    _time_taken = round(time.time() - _start_time)
    logger.info(
        f"Completed processing metadata files. TotalMetadataFiles: {total_metadata_files}, "
        f"TimeTaken: {_time_taken}"
    )
    resources.process_metrics["process_meta_files_count"] = total_metadata_files
    resources.process_metrics["process_meta_files_time_s"] = _time_taken
    resources.process_metrics["process_meta_files_time_hum"] = archival_utils.human_readable_time(
        seconds=_time_taken
    )
