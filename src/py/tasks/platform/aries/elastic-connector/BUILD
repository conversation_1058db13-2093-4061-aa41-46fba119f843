resource(name="config", source="config.yml")
python_sources(
    name="task",
    sources=["elastic_connector/**/*.py"],
    dependencies=[
        "//:3rdparty#s3fs",
        "//:3rdparty#adlfs",
        ":config",
    ],
)

resource(name="test_config", source="tests_elastic_connector/elastic_connector_test_config.yml")
python_tests(
    name="tests", sources=["tests_elastic_connector/**/test_*.py"], dependencies=[":test_config"]
)

python_sources(
    name="sample",
    sources=["*.py"],
)

se_image(
    image_name="aries-elastic-connector",
    pex_entry_point="elastic_connector/elastic_connector_task.py:run",
)
