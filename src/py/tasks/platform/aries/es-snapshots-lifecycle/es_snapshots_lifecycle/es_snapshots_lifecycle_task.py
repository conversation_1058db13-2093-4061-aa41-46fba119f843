import json
import logging
from aries_io_event.app_metric import AppMetricFieldSet
from aries_task_link.models import AriesTaskInput, AriesTaskResult
from aries_task_link.task import aries_task
from collections import defaultdict
from datetime import datetime
from dateutil.relativedelta import relativedelta
from es_snapshots_lifecycle import utils
from es_snapshots_lifecycle.schemas import AriesInputPara<PERSON>, TransitionRule
from omegaconf import DictConfig, ListConfig, OmegaConf
from pathlib import Path
from se_elasticsearch.repository import (
    AnyElasticsearchRepositoryType,
    ResourceConfig,
    get_repository_by_cluster_version,
)
from se_s3_utils import s3_utils
from typing import Any, Tuple, Union

logger = logging.getLogger("es-snapshot-s3-lifecycle-task")

DEFAULT_ALLOWED_COUNTS_DIFF_PERCENTAGE: int | float = 0.5
DEFAULT_ES_SNAPSHOTS_TRANSITION_RULES: list[dict[str, Any]] = [
    {
        "name": "standard to deep archive",
        "aws_storage_class": "DEEP_ARCHIVE",
        "retention_months": 1,
    },
]
DEFAULT_ES_SNAPSHOTS_BUCKET_NAME = "{stack}.elastic.{aws_region}.steeleye.co"
DEFAULT_ES_SNAPSHOTS_S3_PATH = "{stack}/es-backups/"
DEFAULT_ES_SNAPSHOTS_METADATA_FILE_PATH = "metadata/"
VALID_INPUT_PARAMS = {
    "es_snapshots_bucket_name",
    "es_snapshots_s3_path",
    "es_snapshots_transition_rules",
    "es_allowed_stats_diff_percentage",
}


class Resources:  # pragma: no cover
    _es_repo = None
    _config: Union[DictConfig, ListConfig] | None = None
    _s3_client = None

    @classmethod
    def config(cls):
        if not cls._config:
            cls._config = OmegaConf.load(Path(__file__).parent.parent.joinpath("config.yml"))
        return cls._config

    @classmethod
    def es_repo(cls) -> AnyElasticsearchRepositoryType:
        if not cls._es_repo:
            cls._es_repo = get_repository_by_cluster_version(
                resource_config=ResourceConfig(
                    host=cls.config().elastic.host,
                    port=cls.config().elastic.port,
                    scheme=cls.config().elastic.scheme,
                )
            )
        return cls._es_repo

    @classmethod
    def s3_client(cls):
        if not cls._s3_client:
            cls._s3_client = s3_utils.get_s3_client()
        return cls._s3_client


def update_s3_bucket_lifecycle_configuration(
    stack: str,
    bucket_name: str,
    backup_folder_path: str,
    snapshot_repositories: list[str],
    transition_rule: dict[str, str],
    policy_rules: list[dict[Any, Any]],
    slack_webhook_url: str,
    service_name: str,
) -> bool:
    """Updates s3 buckets life cycle policy, if policy not already set.

    Args:
        stack (str): Stack name
        bucket_name (str): S3 bucket name
        backup_folder_path (str): S3 ES backup folder (folder name)
        snapshot_repositories list[str]: S3 ES snapshot repositories
        transition_rule (dict[str, str]): Transition rule that should be applied to the repository
        policy_rules (list[dict[Any, Any]]): Policy rules that needs to be updated
        slack_webhook_url (str): slack webhook url to post the message
        service_name (str): Service name
    Returns:
        is_policy_updated (bool)
    Raises:
    """
    is_policy_updated = False
    status_code = s3_utils.put_bucket_lifecycle_configuration(
        s3_client=Resources.s3_client(),
        bucket_name=bucket_name,
        rules=policy_rules,
    )
    if status_code == 200:
        is_policy_updated = True
        logger.info(
            "[Transition Policy] Successfully created/updated bucket lifecycle "
            f"configuration for Bucket: {bucket_name}, "
            f"BackupFolder: {backup_folder_path}, "
            f"Prefixes: {snapshot_repositories}, Rule: {transition_rule}"
        )
    else:
        error_message = (
            "[Transition Policy] Failed to updated bucket lifecycle configuration "
            f"for Bucket: {bucket_name}, BackupFolder: {backup_folder_path} "
            f"Rule: {transition_rule}."
        )
        logger.error(error_message + f" Prefixes: {snapshot_repositories}")
        utils.send_slack_error_message(
            stack=stack,
            webhook_url=slack_webhook_url,
            bucket_name=bucket_name,
            prefixes=snapshot_repositories,
            error_message=error_message,
            service_name=service_name,
        )
    return is_policy_updated


def get_updated_s3_bucket_lifecycle_configuration_rules(
    bucket_name: str,
    backup_folder_path: str,
    snapshot_repositories: list[str],
    transition_rule: dict[str, str],
) -> list[dict[Any, Any]]:
    """Returns updated policy list (including existing rules if any), if no
    rule are updated returns empty list.

    Args:
        bucket_name (str): S3 bucket name
        backup_folder_path (str): S3 ES backup folder (folder name)
        snapshot_repositories list[str]: S3 ES snapshot repositories
        transition_rule (dict[str, str]): Transition rule that should be applied to the repository
    Returns:
        policy_rules (list[dict[Any, Any]]]): Updated policy rules list,
                                    will be empty if policy is not updated
    Raises:
    """

    # Existing policy rules for a S3 bucket
    # Returns empty list if no rules exist.
    existing_policy_rules = s3_utils.get_bucket_lifecycle_configuration_rules(
        s3_client=Resources.s3_client(), bucket_name=bucket_name
    )

    # We are first processing all the snapshot repositories that match
    # the transaction rule and updating S3 policy only once with all the rules
    # that needs to be added/updated, to avoid multiple requests to S3
    rules_updated = False
    for snapshot_repository in snapshot_repositories:
        # Getting prefix for which the transition rule should be applied
        # All prefixes should end with `/``
        s3_snapshot_repo_prefix = (
            Path(backup_folder_path).joinpath(snapshot_repository).as_posix() + "/"
        )

        logger.info(
            f"[Transition Policy] Processing - S3Bucket: {bucket_name}, "
            f"S3Prefix: {s3_snapshot_repo_prefix}, "
            f"Rule: {transition_rule}"
        )
        # S3 ES back up folder name is always same as the ES Snapshot repository name
        # Setting Days as 0, so that storage class will be changed as soon as the rule is added.
        prefix_transition_rule = {
            "Days": 0,
            "StorageClass": transition_rule["aws_storage_class"],
        }
        prefix_noncurrent_transition_rule = {
            "NoncurrentDays": 0,
            "StorageClass": transition_rule["aws_storage_class"],
        }

        prefix_rule_already_exist = False
        prefix_rule_updated = False
        for existing_rule in existing_policy_rules:
            if existing_rule.get("Filter", {}).get("Prefix") == s3_snapshot_repo_prefix:
                # Setting default value as `[]`, so `in` lookup in next lines
                # doesn't fail with exception.
                aws_existing_rules = existing_rule.get("Transitions", [])
                aws_existing_noncurrent_rules = existing_rule.get(
                    "NoncurrentVersionTransitions", []
                )
                logger.info(
                    "[Transition Policy] found existing rule "
                    f"in AWS for prefix `{s3_snapshot_repo_prefix}`. "
                    f"Existing Rules: {aws_existing_rules}, "
                    f"Existing Noncurrent Rules: {aws_existing_noncurrent_rules}"
                )

                transition_rule_exists = False
                noncurrent_transition_rule_exists = False
                # Order of keys doesn't matter when doing `in` lookup with dict
                # ie if l = [{'a': 1, 'b': 2, 'c': 3}]
                # d = {"c": 3, "a": 1, "b": 2}
                # d in l will always be True, even if order of keys are different.
                # Checking if the new rule already exists in existing aws rules
                # We need to update the policy only if rules doesn't exist.
                if prefix_transition_rule in aws_existing_rules:
                    transition_rule_exists = True
                else:
                    # If there are any existing rules, we need to replace the current
                    # Transitions rules with new rules
                    existing_rule["Transitions"] = [prefix_transition_rule]
                    prefix_rule_updated = True
                    logger.info(
                        "[Transition Policy] Replacing existing transition rules "
                        f"for prefix `{s3_snapshot_repo_prefix}`. "
                        f"AwsExistingRule: {aws_existing_rules}, NewRule: {prefix_transition_rule}"
                    )
                if prefix_noncurrent_transition_rule in aws_existing_noncurrent_rules:
                    noncurrent_transition_rule_exists = True
                else:
                    # If there are any existing rules, we need to replace the current
                    # NoncurrentVersionTransitions rules with new rules
                    existing_rule["NoncurrentVersionTransitions"] = [
                        prefix_noncurrent_transition_rule
                    ]
                    prefix_rule_updated = True
                    logger.info(
                        "[Transition Policy] Replacing existing Noncurrent transition rules "
                        f"for prefix `{s3_snapshot_repo_prefix}`. "
                        f"AwsExistingRule: {aws_existing_noncurrent_rules}, "
                        f"NewRule: {prefix_noncurrent_transition_rule}"
                    )

                if all((transition_rule_exists, noncurrent_transition_rule_exists)):
                    # if both transition_rule_exists and noncurrent_transition_rule_exists are True
                    # We don't need to update the policy again in aws
                    prefix_rule_already_exist = True
                    logger.info(
                        f"[Transition Policy] Skipping bucket lifecycle configuration update. "
                        f"New rule for prefix `{s3_snapshot_repo_prefix}` already exist in AWS. "
                        f"TransitionRule: {prefix_transition_rule}, "
                        f"Noncurrent TransitionRule: {prefix_noncurrent_transition_rule}"
                    )
                else:
                    # transition-rule/noncurrent-transition-rule updated.
                    # We need to update S3 policy, so setting skip flag as False
                    prefix_rule_already_exist = False

                # We can break the loop after existing prefix rule is processed
                # No need to iterate over other rules, which are not related to the prefix.
                break

        if not prefix_rule_updated and not prefix_rule_already_exist:
            # Process comes here only if there is no existing rule for the prefix in S3.
            # Need to append a new rule for the prefix to the existing rules list
            logger.info(
                f"[Transition Policy] Creating new rules for prefix `{s3_snapshot_repo_prefix}`. "
                f"TransitionRule: {prefix_transition_rule}, "
                f"Noncurrent TransitionRule: {prefix_noncurrent_transition_rule}"
            )
            existing_policy_rules.append(
                {
                    "ID": f"es-backups-{snapshot_repository}",
                    "Filter": {"Prefix": s3_snapshot_repo_prefix},
                    "Status": "Enabled",
                    "Transitions": [prefix_transition_rule],
                    "NoncurrentVersionTransitions": [prefix_noncurrent_transition_rule],
                }
            )
            prefix_rule_updated = True

        # Even if prefix rule of a single snapshot is updated we need to update the rules in AWS
        # Irrespective of other snapshots state which are processed in next iteration.
        if prefix_rule_updated:
            rules_updated = prefix_rule_updated

    # We need to call AWS API only if the rules are updated
    # So returning empty list in case if rules are not updated.
    if rules_updated:
        return existing_policy_rules
    else:
        return []


def filter_snapshot_repositories_with_meta_data(
    bucket_name: str,
    snapshot_repositories: list[str],
    metadata_file_path: str,
) -> Tuple[list[str], list[str]]:
    """Filters snapshot repositories with meta data file in S3.

    Params:
        bucket_name (str): S3 bucket name
        snapshot_repositories (list[str]): List of snapshot repository names
    Returns:
        valid_repositories (list[str]): List of valid repositories with mete data file
        invalid_repositories (list[str]): List of repositories that are missing mete data file
    """
    valid_repositories = []
    invalid_repositories = []
    for snapshot_repo in snapshot_repositories:
        _meta_data_file_path = Path(metadata_file_path).joinpath(f"{snapshot_repo}.json").as_posix()
        file_exists = s3_utils.file_exists(
            s3_client=Resources.s3_client(),
            bucket_name=bucket_name,
            s3_key=_meta_data_file_path,
        )
        if file_exists:
            valid_repositories.append(snapshot_repo)
        else:
            invalid_repositories.append(snapshot_repo)
    return valid_repositories, invalid_repositories


def check_status_of_current_month_snapshot(
    snapshot_repositories: list[str],
    allowed_diff_percentage: int | float = DEFAULT_ALLOWED_COUNTS_DIFF_PERCENTAGE,
) -> Tuple[bool, str | None]:
    """Checks if current month snapshot repository has successful snapshot.

    Params:
        snapshot_repositories (list[str]): List of snapshot repository names
    Returns:
        has_successful_snapshot (bool): True if successful snapshot else False
        error_message (str | None): error message if has no successful snapshot
    """
    has_successful_snapshot = False
    error_message = None
    current_month_snapshot_repository = f"stack-{datetime.utcnow().strftime('%Y-%m')}"
    if current_month_snapshot_repository not in snapshot_repositories:
        # We can't proceed if the current month snapshot repository doesn't exist
        error_message = (
            f"Current month snapshot repository {current_month_snapshot_repository}, "
            "doesn't exist yet. Skipping apply transitions."
        )
        return has_successful_snapshot, error_message

    cluster_stats = utils.get_es_cluster_stats(es_client=Resources.es_repo())
    cluster_indices_count = cluster_stats.get("indices", {}).get("count")
    cluster_shards_count = cluster_stats.get("indices", {}).get("shards", {}).get("primaries")
    if not cluster_indices_count or not cluster_shards_count:
        error_message = "Failed to get cluster stats response. Skipping apply transitions."
        return has_successful_snapshot, error_message

    current_month_snapshots = utils.get_es_snapshots_by_repository(
        es_client=Resources.es_repo(), repository=current_month_snapshot_repository
    )

    # Filtering success snapshots
    success_snapshots = [
        i for i in current_month_snapshots.get("snapshots", []) if i.get("state") == "SUCCESS"
    ]
    if not success_snapshots:
        error_message = (
            f"Failed to get any `SUCCESS` snapshots in {current_month_snapshot_repository} "
            " repository. Skipping apply transitions."
        )
        return has_successful_snapshot, error_message

    # Reversing the list and iterating over it
    # as snapshots response is always sorted in ascending order of start time
    # ie all the newer snapshots will be in the last.
    # If we reverse the list and find a successful latest snapshot we can break the loop
    for snapshot_info in reversed(success_snapshots):
        snapshot_indices_count = len(snapshot_info.get("indices", []))
        snapshot_indices_count_diff = utils.get_difference_in_percentage(
            cluster_indices_count,
            snapshot_indices_count,
        )
        snapshot_success_shards_count = snapshot_info.get("shards", {}).get("successful")
        snapshot_success_shards_count_diff = utils.get_difference_in_percentage(
            cluster_shards_count,
            snapshot_success_shards_count,
        )
        if (
            snapshot_indices_count_diff <= allowed_diff_percentage
            and snapshot_success_shards_count_diff <= allowed_diff_percentage
        ):
            # If the counts of snapshot match with the cluster counts or fall with in the
            # allowed difference percentage range.
            # This is a valid successful snapshot and we can break the loop
            has_successful_snapshot = True
            logger.info(
                f"Found successful snapshot. SnapShot: {snapshot_info.get('snapshot')}, "
                f"SnapshotStatus: {snapshot_info.get('state')}, "
                f"ClusterIndicesCount: {cluster_indices_count}, "
                f"SnapshotIndicesCount: {snapshot_indices_count}, "
                f"ClusterShardsCount: {cluster_shards_count}, "
                f"SnapshotSuccessShardsCount: {snapshot_success_shards_count}"
            )
            break

    if not has_successful_snapshot:
        error_message = (
            "Failed to get successful snapshot from current repository "
            f"`{current_month_snapshot_repository} that matches the cluster counts. "
            "Skipping apply transitions."
        )

    return has_successful_snapshot, error_message


def group_eligible_repositories_by_transition_rules(
    snapshot_repositories: list[str],
    transition_rules: list[TransitionRule],
) -> dict[str, list[str]]:
    """Group eligible repositories for transition by there transition_rule.

    Params:
        snapshot_repositories (list[str]): List of snapshot repositories
        transition_rules (list[dict[str, str]]): List if transition rules
    Returns:
        grouped_snapshot_repositories (dict[str, list[str]]): Grouped dict with transition rule as
        key and value is list of snapshot repositories that matched the rule.
    """
    grouped_snapshot_repositories: dict[str, list[str]] = defaultdict(list[str])
    snapshot_repositories_matched_transition_rule = set()
    # Sorting the rules in desc order of retention months.
    # So rule with higher retention months will be processed first.
    # If we don't do this there is high chance that older repos
    # might not be moved to the proper storage class as expected.
    for transition_rule in sorted(transition_rules, key=lambda x: x.retention_months, reverse=True):
        # We need to group the repos that fall before the retention month
        # from the transition rule. These are eligible repos to apply the transition rule
        retention_months = transition_rule.retention_months
        # Getting retention date based on the months set in transition rule
        retention_date = (datetime.utcnow() - relativedelta(months=retention_months)).strftime(
            "%Y-%m"
        )
        # This will be like stack-%Y-%m (stack-year-month)
        max_retention_repo = f"stack-{retention_date}"
        transition_rule_str = json.dumps(transition_rule.dict())
        for _snapshot_repository in snapshot_repositories:
            if _snapshot_repository in snapshot_repositories_matched_transition_rule:
                # we shouldn't process the repos that already matched a transition rule
                # So skipping these.
                continue

            if _snapshot_repository < max_retention_repo:
                # All the snapshot repositories that fall before the max_retention_repo are
                # eligible repos to apply the transition rule
                grouped_snapshot_repositories[transition_rule_str].append(_snapshot_repository)
                snapshot_repositories_matched_transition_rule.add(_snapshot_repository)
    return grouped_snapshot_repositories


@aries_task
def run(aries_task_input: AriesTaskInput) -> AriesTaskResult:
    """Run method to check and update the lifecycle policy of Es snapshots
    backup folders in S3."""
    aries_task_name = aries_task_input.task.name
    base_task_result = AriesTaskResult()
    app_metric = AppMetricFieldSet(
        metrics={
            "generic": {
                "output_count": 0,
                "errored_count": 0,
            }
        }
    )
    base_task_result.app_metric = app_metric

    # This throws an error if the env variables are not set
    # and run fails fast.
    stack = Resources.config().stack
    aws_region = Resources.config().aws_region
    slack_webhook_url = Resources.config().slack.webhook_url

    # Validating input params - this raises error if validation fails
    input_params = AriesInputParams.validate(aries_task_input.input_param.params)

    # If `es_snapshots_transition_rules` input param is not set,
    # replacing it with default DEFAULT_ES_SNAPSHOTS_TRANSITION_RULES
    if not input_params.es_snapshots_transition_rules:
        input_params.es_snapshots_transition_rules = [
            TransitionRule.validate(i) for i in DEFAULT_ES_SNAPSHOTS_TRANSITION_RULES
        ]
    # Just used for logging
    transition_rules_list = [i.dict() for i in input_params.es_snapshots_transition_rules]

    # If `es_snapshots_bucket_name` input param is not set,
    # replacing with default DEFAULT_ES_SNAPSHOTS_BUCKET_NAME
    if not input_params.es_snapshots_bucket_name:
        input_params.es_snapshots_bucket_name = DEFAULT_ES_SNAPSHOTS_BUCKET_NAME.format(
            stack=stack, aws_region=aws_region
        )

    # If `es_snapshots_s3_path` input param is not set,
    # replacing with default DEFAULT_ES_SNAPSHOTS_S3_PATH
    if not input_params.es_snapshots_s3_path:
        input_params.es_snapshots_s3_path = DEFAULT_ES_SNAPSHOTS_S3_PATH.format(stack=stack)

    # If `es_allowed_stats_diff_percentage` input param is not set,
    # replacing default DEFAULT_ALLOWED_COUNTS_DIFF_PERCENTAGE
    if not input_params.es_allowed_stats_diff_percentage:
        input_params.es_allowed_stats_diff_percentage = DEFAULT_ALLOWED_COUNTS_DIFF_PERCENTAGE

    # If `es_snapshots_metadata_file_path` input param is not set,
    # replacing default DEFAULT_ES_SNAPSHOTS_METADATA_FILE_PATH
    if not input_params.es_snapshots_metadata_file_path:
        input_params.es_snapshots_metadata_file_path = DEFAULT_ES_SNAPSHOTS_METADATA_FILE_PATH

    logger.info(
        f"[Started Process] with S3 bucket: {input_params.es_snapshots_bucket_name}, "
        f"S3Path: {input_params.es_snapshots_s3_path}, "
        f"Transition Rules: {transition_rules_list}, "
        f"Skip MetaData File Check: {input_params.skip_metadata_file_check}"
    )

    snapshot_repositories = utils.get_es_repositories(es_client=Resources.es_repo())
    logger.info(f"Snapshot Repositories count in ES cluster: {len(snapshot_repositories)}")

    grouped_eligible_snapshot_repositories = group_eligible_repositories_by_transition_rules(
        snapshot_repositories=snapshot_repositories,
        transition_rules=input_params.es_snapshots_transition_rules,
    )
    if not grouped_eligible_snapshot_repositories:
        # If its empty, it means no snapshot repository fall below
        # the retention months specified in the transition rule.
        # We don't need to proceed further in this case,
        # and no need to publish slack message.
        error_message = (
            "No snapshot repository fall below the retention months specified"
            f" in transition rules. Transition Rules: {transition_rules_list}"
        )
        logger.error(error_message)
        base_task_result.app_metric.metrics["generic"]["errored_count"] += 1
        return base_task_result

    has_successful_snapshot, err_message = check_status_of_current_month_snapshot(
        snapshot_repositories=snapshot_repositories,
        allowed_diff_percentage=input_params.es_allowed_stats_diff_percentage,
    )
    if not has_successful_snapshot:
        # We shouldn't process if there is no successful snapshot
        # in current months repository.
        logger.error(err_message)
        base_task_result.app_metric.metrics["generic"]["errored_count"] += 1
        utils.send_slack_error_message(
            stack=stack,
            webhook_url=slack_webhook_url,
            bucket_name=input_params.es_snapshots_bucket_name,
            prefixes=[input_params.es_snapshots_s3_path],
            error_message=err_message if err_message else "",
            service_name=aries_task_name,
        )
        return base_task_result

    for (
        transition_rule_str,
        _snapshot_repositories,
    ) in grouped_eligible_snapshot_repositories.items():
        transition_rule: dict[str, str] = json.loads(transition_rule_str)
        logger.info(
            f"List of snapshot repositories which matched transition rule: {transition_rule} - "
            f"Repos: {_snapshot_repositories}"
        )

        if input_params.skip_metadata_file_check:
            logger.info("Skipping MetaData file check, as skip flag is set to True in input params")
        else:
            # Checking if snapshot metadata file exits for all the eligible snapshot repositories
            # We shouldn't apply policy for snapshot repositories
            # that doesn't have the metadata file.
            (
                _filtered_snapshot_repositories,
                _snapshot_repositories_missing_meta_data,
            ) = filter_snapshot_repositories_with_meta_data(
                bucket_name=input_params.es_snapshots_bucket_name,
                snapshot_repositories=_snapshot_repositories,
                metadata_file_path=input_params.es_snapshots_metadata_file_path,
            )
            if not _filtered_snapshot_repositories:
                # In this case all the eligible snapshot repositories are missing
                # meta data files, we can skip processing further
                error_message = (
                    "Metadata file is missing for all the eligible "
                    f"repositories: {_snapshot_repositories}"
                )
                logger.info(
                    f"[Transition Policy] Skipping bucket lifecycle configuration update. "
                    "As Metadata file is missing. "
                    f"Bucket: {input_params.es_snapshots_bucket_name}, "
                    f"BackupFolder: {input_params.es_snapshots_s3_path}, "
                    f"Prefixes: {_snapshot_repositories}, Rule: {transition_rule}"
                )
                utils.send_slack_error_message(
                    stack=stack,
                    webhook_url=slack_webhook_url,
                    bucket_name=input_params.es_snapshots_bucket_name,
                    prefixes=[input_params.es_snapshots_metadata_file_path],
                    error_message=error_message,
                    service_name=aries_task_name,
                )
                continue
            elif _snapshot_repositories_missing_meta_data:
                # In this case only few snapshots are missing meta data files.
                # We just need to post slack message for failed repos and continue.
                error_message = (
                    "Metadata file is missing for "
                    f"repositories: {_snapshot_repositories_missing_meta_data}"
                )
                logger.info(
                    f"[Transition Policy] [Partial] Skipping bucket lifecycle configuration "
                    "update. As Metadata file is missing. "
                    f"Bucket: {input_params.es_snapshots_bucket_name}, "
                    f"BackupFolder: {input_params.es_snapshots_s3_path}, "
                    f"Prefixes: {_snapshot_repositories_missing_meta_data}, Rule: {transition_rule}"
                )
                utils.send_slack_error_message(
                    stack=stack,
                    webhook_url=slack_webhook_url,
                    bucket_name=input_params.es_snapshots_bucket_name,
                    prefixes=[input_params.es_snapshots_metadata_file_path],
                    error_message=error_message,
                    service_name=aries_task_name,
                )

            # Overriding _snapshot_repositories with only repositories that have metadata files
            _snapshot_repositories = _filtered_snapshot_repositories

        # S3 policy is updated for all the prefixes (snapshot repos) that match the transition_rule
        # at once, to avoid multiple calls to S3 API.
        updated_policy_rules = get_updated_s3_bucket_lifecycle_configuration_rules(
            bucket_name=input_params.es_snapshots_bucket_name,
            backup_folder_path=input_params.es_snapshots_s3_path,
            snapshot_repositories=_snapshot_repositories,
            transition_rule=transition_rule,
        )

        is_policy_updated = True
        if updated_policy_rules:
            is_policy_updated = update_s3_bucket_lifecycle_configuration(
                stack=stack,
                bucket_name=input_params.es_snapshots_bucket_name,
                backup_folder_path=input_params.es_snapshots_s3_path,
                snapshot_repositories=_snapshot_repositories,
                transition_rule=transition_rule,
                policy_rules=updated_policy_rules,
                slack_webhook_url=slack_webhook_url,
                service_name=aries_task_name,
            )
        else:
            logger.info(
                f"[Transition Policy] Skipping bucket lifecycle configuration update. "
                "New lifecycle configuration rule already exist in AWS. "
                f"Bucket: {input_params.es_snapshots_bucket_name}, "
                f"BackupFolder: {input_params.es_snapshots_s3_path}, "
                f"Prefixes: {snapshot_repositories}, Rule: {transition_rule}"
            )

        if is_policy_updated:
            # Snapshot API doesn't support bulk delete so processing
            # Each delete operation sequentially
            for snapshot_repository in _snapshot_repositories:
                try:
                    s3_repo_prefix = (
                        Path(input_params.es_snapshots_s3_path)
                        .joinpath(snapshot_repository)
                        .as_posix()
                        + "/"
                    )
                    response: dict[str, bool] = utils.delete_es_snapshot_repository(
                        es_client=Resources.es_repo(), repository=snapshot_repository
                    )
                    if response.get("acknowledged") is True:
                        logger.info(
                            f"Successfully deleted ES snapshot repository "
                            f"{snapshot_repository}, "
                            "as AWS bucket lifecycle configuration is created/updated successfully."
                        )
                        base_task_result.app_metric.metrics["generic"]["output_count"] += 1
                        # Sending success message only if ES repository is removed
                        utils.send_slack_success_message(
                            stack=stack,
                            webhook_url=slack_webhook_url,
                            bucket_name=input_params.es_snapshots_bucket_name,
                            prefixes=[s3_repo_prefix],
                            rule=transition_rule,
                            service_name=aries_task_name,
                        )
                    else:
                        error_message = (
                            f"Failed to delete ES snapshot repository {snapshot_repository}, "
                            f"invalid response. Response: {response}"
                        )
                        logger.error(error_message)
                        base_task_result.app_metric.metrics["generic"]["errored_count"] += 1
                        utils.send_slack_error_message(
                            stack=stack,
                            webhook_url=slack_webhook_url,
                            bucket_name=input_params.es_snapshots_bucket_name,
                            prefixes=[input_params.es_snapshots_s3_path],
                            error_message=error_message,
                            service_name=aries_task_name,
                        )
                except Resources.es_repo().elasticsearch_exceptions.NotFoundError:
                    # Can be just error, if snapshot repository doesn't exist instead of exception
                    error_message = (
                        f"Failed to delete ES snapshot repository {snapshot_repository}, "
                        "repository doesn't exist."
                    )
                    logger.error(error_message)
                    base_task_result.app_metric.metrics["generic"]["errored_count"] += 1
                    utils.send_slack_error_message(
                        stack=stack,
                        webhook_url=slack_webhook_url,
                        bucket_name=input_params.es_snapshots_bucket_name,
                        prefixes=[input_params.es_snapshots_s3_path],
                        error_message=error_message,
                        service_name=aries_task_name,
                    )

    return base_task_result
