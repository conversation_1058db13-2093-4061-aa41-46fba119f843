import humanize
import json
import logging
import sys
from aries_io_event.io_param import IOParamFieldSet
from aries_task_link.models import AriesTaskInput, AriesTaskResult
from aries_task_logger import AriesTaskLogInfo, configure_logging
from cascade_utils import cascade_es_utils, cascade_schema, cascade_utils
from collections import defaultdict
from elasticsearch8 import Elasticsearch
from platform_core_tasks.cascade.cascade_process_rts22_transactions.config import configuration
from se_elastic_schema.models.tenant.market.market_counterparty import (
    MarketCounterparty,
)
from typing import Any

_log_info = AriesTaskLogInfo(
    stack=configuration.stack,
    task_name=configuration.task_name,
    task_version=configuration.task_version,
)
log_level = "DEBUG" if int(configuration.debug) else "INFO"
logger = logging.getLogger(configuration.task_name)
configure_logging(_log_info, logger=logger, level=log_level)


class Resources:  # pragma: no cover
    _es_client = None

    @classmethod
    def es_client(cls):
        if not cls._es_client:
            cls._es_client = Elasticsearch(
                configuration.elastic_url, api_key=configuration.elastic_api_key
            )
        return cls._es_client


class CascadeProcessRTS22Transactions:
    def __init__(self, aries_task_input: AriesTaskInput):
        self._tenant_name = aries_task_input.workflow.tenant
        self._aries_task_input = aries_task_input
        # Getting data lake output path prefix
        self._output_path_prefix = cascade_utils.get_output_path_prefix(
            workflow=self._aries_task_input.workflow,
            task=self._aries_task_input.task,
            input_param=self._aries_task_input.input_param,
        )

    def compose_events_query(
        self,
        input_params: cascade_schema.CascadeInputParams,
        source_record: cascade_utils.FIRM_MODELS,
    ) -> dict[str, Any]:
        """Composes Events Query.

        Args:
            tenant (str): Tenant name
            input_params (InputParams): cascade input params
            source_record (cascade_utils.RTS22_SOURCE_MODELS): Source record model object
        Returns:
            query (dict[str, Any]): Events query dict
        Raises:
        """
        uniq_ids = source_record.uniqueIds if source_record.uniqueIds is not None else []
        src_id = source_record.id__
        query: dict[str, Any] = {
            "sort": {"&timestamp": "desc", "&id": "asc"},
            "query": {
                "bool": {
                    "filter": [
                        {"term": {"&model": "RTS22Transaction"}},
                        {"range": {"&timestamp": {"lte": input_params.sourceTimestamp}}},
                        {
                            "bool": {
                                "should": [
                                    {
                                        "nested": {
                                            "path": "parties.buyer",
                                            "query": {"term": {"parties.buyer.&id": src_id}},
                                            "ignore_unmapped": True,
                                        }
                                    },
                                    {
                                        "nested": {
                                            "path": "parties.buyerDecisionMaker",
                                            "query": {
                                                "term": {"parties.buyerDecisionMaker.&id": src_id}
                                            },
                                            "ignore_unmapped": True,
                                        }
                                    },
                                    {
                                        "nested": {
                                            "path": "parties.seller",
                                            "query": {"term": {"parties.seller.&id": src_id}},
                                            "ignore_unmapped": True,
                                        }
                                    },
                                    {
                                        "nested": {
                                            "path": "parties.sellerDecisionMaker",
                                            "query": {
                                                "term": {"parties.sellerDecisionMaker.&id": src_id}
                                            },
                                            "ignore_unmapped": True,
                                        }
                                    },
                                    {
                                        "nested": {
                                            "path": "parties.trader",
                                            "query": {"term": {"parties.trader.&id": src_id}},
                                            "ignore_unmapped": True,
                                        }
                                    },
                                    {"term": {"parties.buyerTransmittingFirm.&id": src_id}},
                                    {"term": {"parties.executionWithinFirm.&id": src_id}},
                                    {"term": {"parties.investmentDecisionWithinFirm.&id": src_id}},
                                    {"term": {"parties.sellerTransmittingFirm.&id": src_id}},
                                ],
                                "minimum_should_match": 1,
                            }
                        },
                    ]
                }
            },
        }
        if (
            input_params.sourceModel in cascade_utils.PERSON_MODEL_MAP
            and input_params.action == cascade_schema.ActionEnum.DELETED
        ):
            pass
        elif (
            input_params.sourceModel == MarketCounterparty.__name__
            and input_params.action == cascade_schema.ActionEnum.DELETED
        ):
            should_filters: list[dict[str, Any]] = [
                {"term": {"parties.counterparty.&id": src_id}},
                {"term": {"parties.executingEntity.&id": src_id}},
            ]
            query["query"]["bool"]["filter"][2]["bool"]["should"].extend(should_filters)
        else:
            expiry_filter = {"bool": {"must_not": {"exists": {"field": "&expiry"}}}}
            should_filters = [
                {"term": {"parties.counterparty.&id": src_id}},
                {"term": {"parties.executingEntity.&id": src_id}},
            ]
            # if uniqueIds are present add to the filter clause
            if uniq_ids:
                should_filters.append(
                    {
                        "nested": {
                            "path": "marketIdentifiers",
                            "query": {"terms": {"marketIdentifiers.labelId": uniq_ids}},
                            "ignore_unmapped": True,
                        }
                    }
                )

            query["query"]["bool"]["filter"].append(expiry_filter)
            query["query"]["bool"]["filter"][2]["bool"]["should"].extend(should_filters)
        return query

    def run(
        self,
    ) -> AriesTaskResult:
        """Gets all target records and generates batch files to process them
        parallely.

        Args:
        Returns:
            AriesTaskResult
        Raises:
        """

        base_task_result = AriesTaskResult(
            output_param=IOParamFieldSet(
                params={"dynamicTasks": [], "dynamicTaskInputs": {}},
            )
        )

        params = cascade_schema.CascadeDriverOutParams.validate(
            self._aries_task_input.input_param.params
        )

        # Reads meta data file from object store and returns meta data: cascade_schema.MetaDataFile
        # Any error here should be fatal
        meta_data: cascade_schema.MetaDataFile = cascade_utils.get_meta_data(
            file_uri=params.meta_data_file_uri
        )

        # In rts22 transactions cascade we know that source record will
        # be of only FIRM_MODELS types.
        # Overriding type to fix type hints.
        source_record: cascade_utils.FIRM_MODELS
        source_record = meta_data.source_record
        events_query = self.compose_events_query(
            input_params=meta_data.workflow_io_params,
            source_record=source_record,
        )

        # Getting aliases of all the target models and querying for events
        # on all of these, we don't need have filter on `&model` as we are restricting
        # the search only to target model aliases
        target_model_aliases = ",".join(
            [
                _model.get_elastic_index_alias(
                    tenant=self._aries_task_input.workflow.tenant,
                )
                for _, _model in cascade_utils.RTS22_TARGET_MODEL_OBJ_MAP.items()
            ]
        )
        logger.info(
            "[Process RTS22 Transactions] List of target model aliases used to get events, "
            f"Aliases: {target_model_aliases}"
        )

        batch_files_by_model: defaultdict[str, list[str]] = defaultdict(list[str])
        total_records_by_model: defaultdict[str, int] = defaultdict(int)
        # Iterating over each target model alias and getting events
        # To avoid large number of records in memory when we are batching the records.
        # We are batching the records by model, so if we query by all aliases at once
        # we also be storing all model records in memory until the
        # max_batch_count or max_batch_size_b is reached.
        for target_model_alias in target_model_aliases.split(","):
            batch_idx_by_model: defaultdict[str, int] = defaultdict(int)
            batch_count_by_model: defaultdict[str, int] = defaultdict(int)
            batch_size_b_by_model: defaultdict[str, int] = defaultdict(int)
            batch_records_by_model: defaultdict[str, list[dict[str, Any]]] = defaultdict(
                list[dict[str, Any]]
            )
            for events in cascade_es_utils.search_after(
                es_client=Resources.es_client(),
                alias=target_model_alias,
                query=events_query,
                page_size=configuration.search_query_page_size,
            ):
                for target_es_record in events:
                    # We don't need to validate here this will be validated in downstream task
                    target_model_name = target_es_record["_source"]["&model"]
                    total_records_by_model[target_model_name] += 1
                    batch_count_by_model[target_model_name] += 1
                    batch_size_b_by_model[target_model_name] += sys.getsizeof(
                        json.dumps(target_es_record)
                    )
                    batch_records_by_model[target_model_name].append(target_es_record)
                    # Writing the file to object store if the batch count or batch size
                    # exceeds the max limit
                    if (
                        batch_count_by_model[target_model_name] >= configuration.max_batch_count
                        or batch_size_b_by_model[target_model_name]
                        >= configuration.max_batch_size_b
                    ):
                        batch_idx_by_model[target_model_name] += 1
                        batch_file_path = cascade_utils.write_records_batch_to_object_store(
                            target_model_name=target_model_name,
                            batch_id=batch_idx_by_model[target_model_name],
                            output_path_prefix=self._output_path_prefix,
                            records_bulk=batch_records_by_model[target_model_name],
                        )
                        _size_readable = humanize.naturalsize(
                            batch_size_b_by_model[target_model_name],
                            binary=True,
                        )
                        logger.info(
                            "[Process RTS22 Transactions] uploaded bulk records file "
                            f"to object store. Model: {target_model_name}, "
                            f"RecordsCount: {batch_count_by_model[target_model_name]}, "
                            f"BatchSize: {_size_readable}, "
                            f"FilePath: {batch_file_path}"
                        )
                        batch_files_by_model[target_model_name].append(batch_file_path)
                        batch_count_by_model[target_model_name] = 0
                        batch_size_b_by_model[target_model_name] = 0
                        batch_records_by_model[target_model_name] = []

            # Handling final batch of files if any records are left
            # This happens when the last batch of records are less than
            # max_batch_count or max_batch_size_b
            for target_model_name, batch_records in batch_records_by_model.items():
                if not batch_records:
                    continue

                total_records_by_model[target_model_name] += 1
                batch_idx_by_model[target_model_name] += 1
                batch_file_path = cascade_utils.write_records_batch_to_object_store(
                    target_model_name=target_model_name,
                    batch_id=batch_idx_by_model[target_model_name],
                    output_path_prefix=self._output_path_prefix,
                    records_bulk=batch_records,
                )
                _size_readable = humanize.naturalsize(
                    batch_size_b_by_model[target_model_name],
                    binary=True,
                )
                logger.info(
                    "[Process RTS22 Transactions] uploaded bulk records file "
                    f"to object store. Model: {target_model_name}, "
                    f"RecordsCount: {batch_count_by_model[target_model_name]}, "
                    f"BatchSize: {_size_readable}, "
                    f"FilePath: {batch_file_path}"
                )
                batch_files_by_model[target_model_name].append(batch_file_path)

        logger.info(
            "[Process RTS22 Transactions] Completed process RTS22 transactions task. "
            f"Total Record Count by model: {dict(total_records_by_model)}. "
            f"Total no of batch files uploaded to object store {len(batch_files_by_model)}."
        )

        dynamic_tasks = cascade_utils.create_dynamic_update_tasks_list(
            stack=configuration.stack,
            files_to_process=batch_files_by_model,
            meta_data_file_uri=params.meta_data_file_uri,
            sub_workflow_name=configuration.cascade_rts22_transactions_sub_workflow_name,
            sub_workflow_task_name=configuration.cascade_update_rts22_transactions_task_name,
            aries_task_input=self._aries_task_input,
        )
        base_task_result.output_param = IOParamFieldSet(params=dynamic_tasks)
        return base_task_result


def cascade_process_rts22_transactions_task_run(
    aries_task_input: AriesTaskInput,
) -> AriesTaskResult:
    fetch = CascadeProcessRTS22Transactions(
        aries_task_input=aries_task_input,
    )

    return fetch.run()
