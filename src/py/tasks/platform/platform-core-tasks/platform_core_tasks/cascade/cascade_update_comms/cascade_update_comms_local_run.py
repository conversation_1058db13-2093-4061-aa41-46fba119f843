# type: ignore
import datetime
import logging
from aries_io_event.io_param import IOParamFieldSet
from aries_io_event.task import TaskFieldSet
from aries_io_event.workflow import WorkflowFieldSet
from aries_task_link.models import AriesTaskInput
from platform_core_tasks.main import platform_core_task_run

logger = logging.getLogger("cascade_update_comms")

if __name__ == "__main__":
    logger.info("Starting execution...")
    workflow = WorkflowFieldSet(
        trace_id="test_cascade_update_comms",
        name="cascade",
        stack="stack",
        tenant="test",
        start_timestamp=datetime.datetime.utcnow(),
    )
    input_param = IOParamFieldSet(
        params=dict(
            file_uri="<object-store-file-uri>",
            meta_data_file_uri="<object-store-file-uri>",
            data_model="se_elastic_schema.models.tenant.communication.message:Message",
            orchestrator_metadata=dict(
                workflow_execution_id="8827a695-1934-4ed0-ba92-ba8d6af60348",
                task_reference_name="cascade_update_comms",
                task_execution_id="bc7f1daf-79cd-4867-88ea-a7eb8737f29e",
            ),
        )
    )
    task = TaskFieldSet(name="cascade_update_comms", version="latest", success=False)
    input = AriesTaskInput(workflow=workflow, input_param=input_param, task=task)
    output = platform_core_task_run(aries_task_input=input)
    logger.info(f"Finished executing with output {output}")
