import datetime
import fsspec
import json
import logging
import se_schema_meta
from elasticsearch8.helpers import scan
from platform_core_tasks.ops_runbook.utils.elastic_client import ElasticResources
from pydantic import BaseModel, Extra
from se_elastic_schema.models.tenant.surveillance.surveillance_watch import SurveillanceWatch

log = logging.getLogger(__name__)


class CellWatchConfigReport(BaseModel, extra=Extra.ignore):
    account_s3_bucket: str
    cell_name: str


def run_cell_watch_config_report(**kwargs):
    params = CellWatchConfigReport(**kwargs)
    es_client = ElasticResources.es_client()

    try:
        watch_config_query = {
            # We are doing the scroll, however, it will barely exceed 10k,
            # even if it does, the scroll will keep us handy with the data.
            "size": 10000,
            "query": {"bool": {"must_not": [{"exists": {"field": se_schema_meta.EXPIRY}}]}},
        }

        watches = []
        for result in scan(
            es_client,
            index=SurveillanceWatch.get_elastic_index_alias(tenant="*"),
            query=watch_config_query,
        ):
            watches.append(result)

        log.info(f"Total watches found: {len(watches)}")

        for export_path in [
            f"{params.account_s3_bucket}/reports/watchConfigurations/{params.cell_name}.json",
            f"{params.account_s3_bucket}/reports/watchConfigurations/dailyFiles/{params.cell_name}_{datetime.date.today().isoformat()}.json",
        ]:
            with fsspec.open(f"s3://{export_path}", "w") as f:
                f.write(json.dumps(watches))

        log.info("Cell Watch Configurations report exported successfully.")
    except Exception as e:
        log.error(f"Error while fetching users: {e}")
        return
