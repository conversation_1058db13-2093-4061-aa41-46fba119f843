import backoff
import concurrent.futures
import datetime
import httpx
import json
import logging
import nanoid
import pyarrow as pa
import pyarrow.compute as pc
import pyarrow.parquet as pq
import shutil
import time
import uuid
from aries_io_event.app_metric import AppMetricFieldSet
from aries_se_api_client.client import AriesApiClient
from aries_task_link.models import AriesTaskInput, AriesTaskResult
from azure.core.exceptions import ResourceNotFoundError
from azure.storage.blob import ContainerClient
from data_platform_config_api_client.tenant import TenantAPI
from elasticsearch8 import Elasticsearch, exceptions
from elasticsearch8.helpers import scan
from omegaconf import OmegaConf
from pathlib import Path
from pydantic import BaseModel, Field
from se_azure_blob_utils.azure_blob_utils import (
    delete_blob,
    get_blob_service_client,
    upload_blob,
)
from se_data_lake.lake_path import get_ingest_lake_dir_for_task
from se_elastic_schema.elastic_schema.core.steeleye_schema_model import (
    SteelEyeSchemaBaseModelES8,
)
from se_elastic_schema.models import (
    AccountFirm,
    AccountPerson,
    Call,
    Case,
    CaseCall,
    CaseEmail,
    CaseMeeting,
    CaseMessage,
    CaseText,
    ChatEvent,
    CommunicationAlert,
    Email,
    MarketCounterparty,
    MarketPerson,
    Meeting,
    Message,
    SurveillanceWatch,
    SurveillanceWatchExecution,
    SurveillanceWatchPendingChange,
    TenantDataSource,
    Text,
    Transcript,
    UserAudit,
    UserComment,
)
from se_enums.cloud import CloudProviderEnum
from se_es_utils.utils import es_api_retriable_call
from typing import Any, Set, Tuple
from urllib.parse import urlparse

_config = OmegaConf.load(Path(__file__).parent.joinpath("custom-data-purge-config.yml"))
DEBUG = bool(int(_config.debug))
logging.basicConfig(
    level="INFO" if not DEBUG else "DEBUG",
    format="{asctime} | {levelname} | [{name}:{funcName}:{lineno}] -- {message}",
    style="{",
    datefmt="%Y-%m-%dT%H:%M:%S",
)
logger = logging.getLogger(__name__)


MODELS_TO_PURGE = (Call, ChatEvent, Email, Meeting, Message, Text, Transcript)
MODELS_TO_KEEP = (
    AccountFirm,
    AccountPerson,
    Case,
    CaseCall,
    CaseEmail,
    CaseMeeting,
    CaseMessage,
    CaseText,
    CommunicationAlert,
    MarketCounterparty,
    MarketPerson,
    SurveillanceWatch,
    SurveillanceWatchExecution,
    SurveillanceWatchPendingChange,
    TenantDataSource,
    UserAudit,
    UserComment,
)

_FILE_FIELDS_IN_ES = [
    "attachments.fileInfo.location.key",
    "auditKey",
    "body.translations.model.sourceKey",
    "counterparty.sourceKey",
    "comments.attachment.fileInfo.location.key",
    "comments.attachments.fileInfo.location.key",
    "dataSource.s3Path",
    "key",
    "meetingDetails.participants_timeline.location.key",
    "metadata.source.fileInfo.location.key",
    "recordings.sourceKey",
    "recordingSourceKey",
    "sourceKey",
    "transcripts.recordingSourceKey",
    "transcripts.transcriptSourceKey",
    "transcriptSourceKey",
    "translations.model.sourceKey",
    "voiceFile.fileInfo.location.key",
    "voiceTranscript.fileInfo.location.key",
    "waveform.location.key",
]

# raw data prefixes that are eligible for purging
PURGE_ELIGIBLE_PREFIXES = [
    "aries/ingress/",
    "aries/ingest_batching/",
    "attachments/",
    "feeds/eml",
]


class CustomDataPurgeParams(BaseModel):
    dry_run: bool = Field(
        default=True,  # set to True for safeguard
        description="if true, it doen't delete any files",
    )
    inventory_report_container: str | None = Field(
        None, description="name of the azure blob storage inventory report"
    )
    inventory_report_name: str = Field(
        ..., description="name of the azure blob storage inventory report"
    )
    retention_days: int = Field(default=30, description="retention days for the files", gt=8)


class CustomDataPurge:
    """This task deletes the files from the elastic search and azure blob
    storage based on the retention days."""

    def __init__(self, aries_task_input: AriesTaskInput):
        if not PURGE_ELIGIBLE_PREFIXES:
            raise Exception("PURGE_ELIGIBLE_PREFIXES is empty")
        self._validate_azure_env_vars()
        self._aries_task_input = aries_task_input
        self._io_params = CustomDataPurgeParams.validate(aries_task_input.input_param.params)
        self._es_client = Elasticsearch(
            _config.elastic.url,
            api_key=_config.elastic.api_key,
            request_timeout=120,
            ca_certs=_config.elastic.ssl_cert_file,
            verify_certs=bool(int(_config.elastic.verify_certs)),
            ssl_show_warn=bool(int(_config.elastic.verify_certs)),
        )
        logger.info("ES INFO -> %s", self._es_client.info())
        self._start_of_today = datetime.datetime.now(tz=datetime.timezone.utc).replace(
            hour=0, minute=0, second=0, microsecond=0
        )
        self._purge_date_time = self._start_of_today - datetime.timedelta(
            days=self._io_params.retention_days
        )
        self.local_temp_dir = Path(uuid.uuid4().__str__())
        self.local_temp_dir.mkdir()

        self._config_api_client = self._get_aries_api_client()
        self._tenant_api_client = TenantAPI(self._config_api_client)
        self._tenant_container = urlparse(
            self._tenant_api_client.get(
                stack_name=_config.stack,
                tenant_name=self._aries_task_input.workflow.tenant,
            ).content.lake_prefix
        ).netloc
        self._inventory_container = (
            self._io_params.inventory_report_container or self._tenant_container
        )
        blob_service_client = get_blob_service_client(
            _config.azure_storage_account_name,
            _config.use_default_azure_credentials,
            _config.azure_storage_connection_string,
        )
        self._tenant_container_client = blob_service_client.get_container_client(
            self._tenant_container
        )
        self._inventory_container_client = blob_service_client.get_container_client(
            self._inventory_container
        )
        self._output_path_prefix = get_ingest_lake_dir_for_task(
            workflow_name=aries_task_input.workflow.name,
            task_name=aries_task_input.task.name,
            workflow_start_timestamp=aries_task_input.workflow.start_timestamp,
            workflow_trace_id=aries_task_input.workflow.trace_id,
        )

    @staticmethod
    @backoff.on_exception(backoff.expo, httpx.RequestError, max_tries=3, logger=logger)
    def _get_aries_api_client() -> AriesApiClient:
        return AriesApiClient(host=_config.data_platform_config_api_url)

    @staticmethod
    def _validate_azure_env_vars():
        # Validate only Azure env vars as they're null in the OmegaConf
        if _config.cloud == CloudProviderEnum.AZURE:
            if _config.use_default_azure_credentials is None:
                if _config.azure_storage_connection_string is None:
                    raise Exception(
                        "AZURE_STORAGE_CONNECTION_STRING env is required when"
                        "DEFAULT_AZURE_CREDENTIALS is False"
                    )
            elif _config.azure_storage_account_name is None:
                raise Exception(
                    "AZURE_STORAGE_ACCOUNT_NAME env is required when "
                    "using default azure credentials"
                )

    def _parse_values(self, data: Any):
        """Recursively traverse nested dicts/lists and yield any string
        value."""
        if isinstance(data, dict):
            for value in data.values():
                yield from self._parse_values(value)
        elif isinstance(data, list):
            for elem in data:
                yield from self._parse_values(elem)
        elif isinstance(data, str):
            yield data

    @staticmethod
    def _get_object_key(object_path: str) -> str:
        """get object_key from object uri."""
        return urlparse(object_path).path.lstrip("/")

    def _get_files(self, query: dict, models: Tuple[SteelEyeSchemaBaseModelES8]) -> Set[str]:
        """Get the files from the elastic search based on the query and models.

        parse and return set of object paths
        """
        logger.debug(f"start of {self._get_files.__qualname__}")
        logger.debug("files query: `%s`", json.dumps(query))
        logger.info(
            "getting file references from models `%s`", [model.__name__ for model in models]
        )
        files = set()
        for model in models:
            alias = model.get_elastic_index_alias(tenant=self._aries_task_input.workflow.tenant)
            logger.info(f"getting file references for model `{model.__name__}` from `{alias}`")
            try:
                for result in es_api_retriable_call(
                    scan, client=self._es_client, index=alias, query=query, size=10000
                ):
                    object_uris = [i for i in self._parse_values(result["_source"])]
                    object_paths = [self._get_object_key(i.lstrip("/")) for i in object_uris]
                    files.update(object_paths)
            except exceptions.NotFoundError as e:
                # Check if the NotFoundError is due to a missing index.
                error_type = e.info.get("error", {}).get("type")
                if error_type == "index_not_found_exception":
                    logger.warning("alias '%s' was not found. Error message: `%s`", alias, e)
                else:
                    # If it's not due to a missing index, re-raise the exception.
                    raise
        logger.info(f"total files `{len(files)}`")
        logger.debug(f"end of {self._get_files.__qualname__}")
        return files

    def _delete_elastic_records(self):
        logger.debug(f"start of {self._get_files.__qualname__}")
        query = {"query": {"range": {"&timestamp": {"lt": self._purge_date_time.isoformat()}}}}
        logger.debug("query to delete records `%s`", json.dumps(query))
        model_wise_deleted_record_count = dict()
        for model in MODELS_TO_PURGE:
            alias = model.get_elastic_index_alias(tenant=self._aries_task_input.workflow.tenant)
            logger.info(f"deleting records from model `{model.__name__}` and alias `{alias}`")
            records_to_delete = self._es_client.count(index=alias, body=query)["count"]
            model_wise_deleted_record_count[model.__name__.lower()] = records_to_delete
            logger.info(f"total records to delete `{records_to_delete}`")

            if self._io_params.dry_run:
                logger.info(
                    f"running in dry run mode, not deleting records from model `{model.__name__}`"
                )
                continue
            if not records_to_delete:
                logger.info(f"skipping `{model.__name__}` as no records to delete")
                continue
            task_id = es_api_retriable_call(
                self._es_client.delete_by_query,
                index=alias,
                body=query,
                wait_for_completion=False,
                refresh=True,
            )["task"]
            while True:
                task_response = es_api_retriable_call(self._es_client.tasks.get, task_id=task_id)
                deleted_count = task_response["task"]["status"]["deleted"]
                if task_response["completed"]:
                    logger.info(f"deleted `{deleted_count}` records from `{alias}`")
                    break
                logger.info(f"deletion in progress.`{deleted_count}` docs deleted so far.")
                time.sleep(5)
        logger.debug(f"end of {self._get_files.__qualname__}")
        return model_wise_deleted_record_count

    def _get_files_queries(self) -> dict:
        """Get the queries to get the files from elastic search."""
        logger.debug(f"start of {self._get_files.__qualname__}")
        query = {
            MODELS_TO_PURGE: {
                "_source": _FILE_FIELDS_IN_ES,
                "query": {"range": {"&timestamp": {"gte": self._purge_date_time.isoformat()}}},
            },
            MODELS_TO_KEEP: {
                "_source": _FILE_FIELDS_IN_ES + ["hit." + i for i in _FILE_FIELDS_IN_ES],
                "query": {"match_all": {}},
            },
        }

        logger.debug(f"end of {self._get_files.__qualname__}")
        return query

    def _delete_objects(
        self,
        object_and_version_tuples: set[tuple],
        container_client: ContainerClient,
        max_workers: int = int(_config.max_delete_workers),
    ) -> set[tuple]:
        """Deletes a set of object (each tuple is (object_path, version_id)) in
        parallel."""
        logger.debug(f"start of {self._get_files.__qualname__}")
        logger.info("deleting files from object store")
        failed_files = set()
        with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
            # Map each future to its corresponding (object_path, version_id)
            deleted_futures = {
                executor.submit(
                    delete_blob,
                    blob_path=object_path,
                    container_client=container_client,
                    version_id=version_id,
                    retriable=False,
                ): (
                    object_path,
                    version_id,
                )
                for object_path, version_id in object_and_version_tuples
            }
            # Process futures as they complete
            for future in concurrent.futures.as_completed(deleted_futures):
                object_path, version_id = deleted_futures[future]
                try:
                    future.result()
                except ResourceNotFoundError:
                    # Ignore the error if the blob is not found
                    logger.debug("Blob not found: %s (version %s)", object_path, version_id)
                    pass
                except Exception as e:
                    # Append the tuple of the failed blob details
                    failed_files.add((object_path, version_id))
                    logger.error(f"Deletion error for {object_path} (version {version_id}): {e}")
        logger.debug(f"end of {self._get_files.__qualname__}")
        return failed_files

    def _download_inventory_report(self) -> list[Path]:
        """download the inventory reports from the azure blob storage."""
        logger.debug(f"start of {self._get_files.__qualname__}")
        logger.info("downloading inventory reports")

        parquet_files = []
        # find inventory reports created in last 8 days
        # since we are having weekly inventory reports
        after_datetime = self._start_of_today - datetime.timedelta(days=8)
        logger.info("looking for inventory reports created after `%s`", after_datetime)
        path_prefix = after_datetime.strftime("%Y/%m")
        blobs_list = self._inventory_container_client.list_blobs(
            name_starts_with=path_prefix, results_per_page=5000
        )
        inventory_folder_prefix = ""
        for blob in blobs_list:
            if blob.last_modified > after_datetime:
                # example report path:
                # 2025/02/27/18-27-43/{report_name}}/{report_name}...
                if blob.name.split("/")[4] == self._io_params.inventory_report_name:
                    inventory_folder_prefix = blob["name"].rsplit("/", 1)[0] + "/"
                    break
        if not inventory_folder_prefix:
            raise Exception(
                f"Inventory report `{self._io_params.inventory_report_name}` "
                f"not found in last 8 days"
            )
        logger.info(f"found inventory report folder `{inventory_folder_prefix}`")
        inventory_list = self._inventory_container_client.list_blobs(
            name_starts_with=inventory_folder_prefix, results_per_page=5000
        )
        for blob in inventory_list:
            if blob.name.endswith(".parquet"):
                blob_client = self._inventory_container_client.get_blob_client(blob.name)
                local_path = self.local_temp_dir.joinpath(blob.name.rsplit("/", 1)[1])
                with open(local_path, "wb") as f:
                    f.write(blob_client.download_blob().readall())
                parquet_files.append(local_path)
        logger.info("downloaded inventory reports `%s`", parquet_files)
        logger.debug(f"end of {self._get_files.__qualname__}")
        return parquet_files

    def _upload_to_object_store(self, local_file_path: Path) -> str:
        """Upload the file to cloud object store."""
        logger.debug(f"start of {self._get_files.__qualname__}")
        file_name = local_file_path.name
        cloud_object_path: str = self._output_path_prefix + file_name
        logger.info(
            f"uploading file `{local_file_path}` to tenant container "
            f"`{self._tenant_container}` at `{cloud_object_path}`"
        )
        upload_blob(
            container_client=self._tenant_container_client,
            local_file_path=local_file_path.as_posix(),
            blob_path=cloud_object_path,
        )
        logger.info(f"uploaded file `{local_file_path}` to tenant container")
        logger.debug(f"end of {self._get_files.__qualname__}")
        return cloud_object_path

    def run(self) -> AriesTaskResult:
        logger.debug(f"start of {self._get_files.__qualname__}")
        logger.info("io_params: `%s`", self._io_params)
        logger.info(f"purging data created before `{self._purge_date_time}`")
        total_files_deleted = 0
        total_files_failed = 0
        files_to_retain = set()
        logger.info("getting files to retain from elasticseach")
        for models, query in self._get_files_queries().items():
            files_to_retain.update(self._get_files(query=query, models=models))
        logger.debug("files to retain `%s`", files_to_retain)
        logger.info(f"total files to retain from all models `{len(files_to_retain)}`")

        inventory_files = self._download_inventory_report()

        logger.info(
            f"purging files created before `{self._purge_date_time}` and "
            f"don't exist in the retention list"
        )
        threshold_ms = int(
            self._purge_date_time.timestamp() * 1000
        )  # convert to epoch milliseconds
        latest_creation_time = pa.scalar(threshold_ms, type=pa.int64())

        files_to_retain_file_path = self.local_temp_dir.joinpath(
            f"files_to_retain_{nanoid.generate(size=5)}.txt"
        )
        deleted_objects__csv_local_path = self.local_temp_dir.joinpath(
            f"deleted_objects_{nanoid.generate(size=5)}_.csv"
        )
        failed_objects_local_csv_path = self.local_temp_dir.joinpath(
            f"failed_objects_{nanoid.generate(size=5)}.csv"
        )

        deleted_objects_csv_handler = open(deleted_objects__csv_local_path, "w")
        failed_objects_csv_handler = open(failed_objects_local_csv_path, "w")

        deleted_objects_csv_handler.write("object,version\n")
        failed_objects_csv_handler.write("object,version\n")

        with open(files_to_retain_file_path, "w") as f:
            for entry_ in files_to_retain:
                f.write(f"{entry_}\n")

        for file_path in inventory_files:
            logger.info(f"processing inventory report `{file_path}`")
            parquet_file = pq.ParquetFile(file_path)
            for batch in parquet_file.iter_batches():
                failed_files = []
                logger.debug("processing batch")
                mask_time = pc.less(batch.column("Creation-Time"), latest_creation_time)
                time_filtered = pc.filter(batch, mask_time)
                if time_filtered.num_rows == 0:
                    logger.info("no files to delete in this batch after datetime filter")
                    continue
                # Extract the object path (strip off the container part)
                # Assumes the "Name" column is in the format "container/object_path"
                extracted_struct = pc.extract_regex(
                    time_filtered.column("Name"), r"^[^/]+/+(?P<object_path>.*)$"
                )
                # Extract the "object_path" field from the struct.
                extracted = extracted_struct.field("object_path")

                mask_include = None
                for prefix in PURGE_ELIGIBLE_PREFIXES:
                    # Use utf8_slice_codeunits to extract the first len(prefix)
                    # characters from each string.
                    substr = pc.utf8_slice_codeunits(extracted, 0, len(prefix))
                    prefix_mask = pc.equal(substr, prefix)
                    mask_include = (
                        prefix_mask if mask_include is None else pc.or_(mask_include, prefix_mask)
                    )

                final_batch = pc.filter(time_filtered, mask_include)
                if final_batch.num_rows == 0:
                    logger.debug(
                        "no files to delete in this batch after datetime and exclude prefix filters"
                    )
                    continue

                # Extract object_path again from final_batch.
                final_extracted_struct = pc.extract_regex(
                    final_batch.column("Name"), r"^[^/]+/(?P<object_path>.*)$"
                )
                final_object_path_array = final_extracted_struct.field("object_path")

                # Extract VersionId column.
                version_array = final_batch.column("VersionId")

                # Convert both arrays to Python lists.
                object_path_list = final_object_path_array.to_pylist()
                version_list = version_array.to_pylist()

                objects_to_delete = set(
                    (object_path_, version_)
                    for object_path_, version_ in zip(object_path_list, version_list)
                    if object_path_ not in files_to_retain
                )
                logger.debug("files to delete `%s`", objects_to_delete)
                if self._io_params.dry_run:
                    logger.info("running in dry run mode, not deleting files")
                else:
                    failed_files = self._delete_objects(
                        objects_to_delete, self._tenant_container_client
                    )
                    logger.debug("failed to delete following files: `%s`", failed_files)
                    logger.info(
                        f"deleted `{len(objects_to_delete) - len(failed_files)}` "
                        f"files and failed to delete `{len(failed_files)}` files"
                    )
                    total_files_failed += len(failed_files)
                    if failed_files:
                        for object_path_, version_ in failed_files:
                            failed_objects_csv_handler.write(f"{object_path_},{version_}\n")
                        failed_objects_csv_handler.write("\n")
                        failed_objects_csv_handler.flush()
                total_files_deleted += len(objects_to_delete) - len(failed_files)

                for entry in objects_to_delete:
                    if entry in failed_files:
                        continue
                    deleted_objects_csv_handler.write(f"{entry[0]},{entry[1]}\n")
                deleted_objects_csv_handler.write("\n")
                deleted_objects_csv_handler.flush()
                logger.debug("processed batch")

        deleted_objects_csv_handler.close()
        failed_objects_csv_handler.close()

        deleted_objects_csv_cloud_path = self._upload_to_object_store(
            deleted_objects__csv_local_path
        )

        if total_files_failed > 0:
            failed_objects_csv_cloud_path = self._upload_to_object_store(
                failed_objects_local_csv_path
            )
            logger.info(
                f"Failed to delete `{total_files_failed}` files and "
                f"list is available in `{failed_objects_csv_cloud_path}`"
            )
            raise Exception(
                f"Failed to delete `{total_files_failed}` files and "
                f"list is available in `{failed_objects_csv_cloud_path}`"
            )

        logger.info(
            f"purging elasticseach data older than "
            f"`{self._purge_date_time}` from `{MODELS_TO_PURGE}`"
        )
        model_wise_deleted_record_count = self._delete_elastic_records()
        app_metric = AppMetricFieldSet(
            metrics={
                "custom": {
                    "total_number_of_objects_deleted": total_files_deleted,
                    "deleted_objects_file_path": deleted_objects_csv_cloud_path,
                    "elastic_records_deleted": model_wise_deleted_record_count,
                    "dry_run": self._io_params.dry_run,
                }
            }
        )
        logger.debug(f"end of {self._get_files.__qualname__}")
        return AriesTaskResult(app_metric=app_metric)

    def __del__(self):
        """Cleanup the local temp dir."""
        try:
            logger.info(f"deleting temp directory `{self.local_temp_dir}`")
            shutil.rmtree(self.local_temp_dir)
        except Exception as e:
            logger.error(f"failed to delete temp dir `{self.local_temp_dir}`: {e}")


def custom_data_purge_task_run(aries_task_input: AriesTaskInput) -> AriesTaskResult:
    return CustomDataPurge(aries_task_input=aries_task_input).run()
