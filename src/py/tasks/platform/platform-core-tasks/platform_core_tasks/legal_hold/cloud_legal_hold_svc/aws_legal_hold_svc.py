import boto3
import logging
from aries_se_core_tasks.utilities.s3_utils import get_bucket_and_key_from_file_url
from platform_core_tasks.legal_hold.cloud_legal_hold_svc.abstract_legal_hold_svc import (
    AbstractLegalHoldService,
)
from platform_core_tasks.legal_hold.schemas import (
    ObjectLockStatus,
    RetentionInfo,
    RetentionRecord,
    RetentionStatus,
)
from platform_core_tasks.legal_hold.static import RETENTION_FILE_DESTINATION_PATH
from se_s3_utils import s3_utils
from typing import Any, Tuple

BULK_DELETE_CHUNK_SIZE = 1000
log = logging.getLogger(__name__)


class AWSLegalHoldService(AbstractLegalHoldService):
    def __init__(self, **_kwargs):
        self._s3_client = boto3.client("s3")

    def is_storage_immutable(self, storage_name: str) -> bool:
        bucket_object_lock_status = s3_utils.get_bucket_object_lock_status(
            s3_client=self._s3_client, bucket_name=storage_name
        )
        if bucket_object_lock_status != ObjectLockStatus.ENABLED.value:
            return False
        return True

    def process_legal_hold_record(
        self, storage_name: str, record: RetentionRecord
    ) -> RetentionInfo:
        """Processes record dict and applies lock on raw files.

        Args:
            storage_name (str): Object Store name
            record (dict): retention record dict

        Returns:
            RetentionInfo (dataclass): dataclass with predefined fields
        """
        # Getting raw file S3 key and using the same for destination path
        _, source_s3_key = get_bucket_and_key_from_file_url(file_url=record.objectUri)
        base_dest_s3_key = RETENTION_FILE_DESTINATION_PATH.format(case_id=record.caseId)
        dest_s3_key = base_dest_s3_key + source_s3_key
        dest_file_uri = f"s3://{storage_name}/{dest_s3_key}"
        log.info(f"[LEGAL HOLD] processing raw file {record.objectUri}")

        # Setting legal hold status using legalHold if true should set to ON else OFF
        # values form the retention_record
        # Using Literal, to avoid mypy errors
        # when using this as value for RetentionInfo.retention_status
        set_legal_hold_status = (
            RetentionStatus.ON if record.legalHold is True else RetentionStatus.OFF
        )

        # We need to copy files only if legal hold status is ON
        # Assumption is that when status is "OFF" file should already exist.
        # This will be cross checked in next steps.
        if set_legal_hold_status == RetentionStatus.ON:
            return self._apply_lock(
                retention_record=record,
                bucket_name=storage_name,
                source_s3_key=source_s3_key,
                dest_s3_key=dest_s3_key,
                dest_file_uri=dest_file_uri,
                set_legal_hold_status=set_legal_hold_status,
            )

        return self._release_lock(
            retention_record=record,
            bucket_name=storage_name,
            dest_s3_key=dest_s3_key,
            dest_file_uri=dest_file_uri,
            set_legal_hold_status=set_legal_hold_status,
        )

    def _apply_lock(
        self,
        retention_record: RetentionRecord,
        bucket_name: str,
        source_s3_key: str,
        dest_s3_key: str,
        dest_file_uri: str,
        set_legal_hold_status: RetentionStatus,
    ) -> RetentionInfo:
        # Checking if source file exists, if doesn't exist raises error
        source_s3_file_exists = s3_utils.file_exists(
            s3_client=self._s3_client, bucket_name=bucket_name, s3_key=source_s3_key
        )
        if not source_s3_file_exists:
            err_msg = "Raw file path is invalid, file doesn't exist"
            log.error(f"[LEGAL HOLD] {err_msg}, Path: {retention_record.objectUri}")
            return RetentionInfo(
                retentionStatus=RetentionStatus.FAILED,
                retentionErrorMessage=err_msg,
                id=retention_record.id,
            )

        # Checking if destination file exists already
        _file_exists = s3_utils.file_exists(
            s3_client=self._s3_client,
            bucket_name=bucket_name,
            s3_key=dest_s3_key,
        )
        if _file_exists:
            # If file exists already we need to cross check if the file has
            # legal hold status set if not we need to apply
            # No need to copy in this case.
            return self._check_and_apply_legal_hold_status(
                retention_record=retention_record,
                bucket_name=bucket_name,
                dest_s3_key=dest_s3_key,
                dest_file_uri=dest_file_uri,
                set_legal_hold_status=set_legal_hold_status,
            )

        # Copying raw s3 file and applying legal hold status as well
        # if the file doesn't exists already.
        log.info(f"[LEGAL HOLD] [COPYING] raw file {retention_record.objectUri} to {dest_file_uri}")
        status_code, error = s3_utils.copy_object(
            s3_client=self._s3_client,
            bucket_name=bucket_name,
            source_s3_key=source_s3_key,
            dest_s3_key=dest_s3_key,
            set_legal_hold_status=set_legal_hold_status.value,
        )

        if status_code != 200 or error:
            err_msg = (
                f"Failed to copy S3 file `{source_s3_key}` to `{dest_file_uri}` "
                f"ResponseStatusCode: {status_code}, Error: {error}"
            )
            log.error(f"[LEGAL HOLD] {err_msg}")
            return RetentionInfo(
                retentionStatus=RetentionStatus.FAILED,
                retentionErrorMessage=err_msg,
                id=retention_record.id,
            )

        return RetentionInfo(
            retentionStatus=set_legal_hold_status,
            retentionPath=dest_file_uri,
            id=retention_record.id,
        )

    def _release_lock(
        self,
        retention_record: RetentionRecord,
        bucket_name: str,
        dest_s3_key: str,
        dest_file_uri: str,
        set_legal_hold_status: RetentionStatus,
    ) -> RetentionInfo:  # Checking if dest_s3_key exits, we can return if doesn't exists.
        # This also helps in avoiding `client.exceptions.NoSuchKey` when trying to
        # get/apply legal hold status.
        dest_s3_file_exists = s3_utils.file_exists(
            s3_client=self._s3_client, bucket_name=bucket_name, s3_key=dest_s3_key
        )
        if not dest_s3_file_exists:
            # We don't need to mark it as failed if the destination file doesn't exist.
            # The action of _release_lock is to release the lock and delete the file.
            # If the file doesn't exist we can just set the status as OFF (set_legal_hold_status)
            msg = (
                "Destination raw file doesn't exist, "
                f"so setting the status as {set_legal_hold_status}"
            )
            log.info(f"[LEGAL HOLD] {msg}. File {dest_file_uri}")
            return RetentionInfo(
                retentionStatus=set_legal_hold_status,
                id=retention_record.id,
            )

        retention_info = self._check_and_apply_legal_hold_status(
            retention_record=retention_record,
            bucket_name=bucket_name,
            dest_s3_key=dest_s3_key,
            dest_file_uri=dest_file_uri,
            set_legal_hold_status=set_legal_hold_status,
        )
        if retention_info.retentionStatus == RetentionStatus.FAILED:
            return retention_info

        log.info(f"[LEGAL HOLD] deleting copied file {dest_file_uri}")
        # Deleting copied raw file incase if legal hold is released
        status_code, errors = self._delete_object_versions(
            bucket_name=bucket_name, s3_key=dest_s3_key
        )
        if status_code != 200 or errors:
            err_msg = (
                f"Failed to delete S3 file `{dest_file_uri}`, "
                f"ResponseStatusCode: {status_code}, Errors: {errors}"
            )
            log.error(f"[LEGAL HOLD] {err_msg}")
            return RetentionInfo(
                retentionStatus=RetentionStatus.FAILED,
                retentionErrorMessage=err_msg,
                id=retention_record.id,
            )

        # We don't need to set retentionPath in case of release lock is success
        # As lock is released, even in db this field should be set to null
        return RetentionInfo(
            retentionStatus=set_legal_hold_status,
            id=retention_record.id,
        )

    def _check_and_apply_legal_hold_status(
        self,
        retention_record: RetentionRecord,
        bucket_name: str,
        dest_s3_key: str,
        dest_file_uri: str,
        set_legal_hold_status: RetentionStatus,
    ) -> RetentionInfo:
        # Getting legal hold status
        # Possible options ON, OFF, NA (if failed with NoSuchObjectLockConfiguration error)
        # This helps in avoiding duplicate file processing
        legal_hold_status = s3_utils.get_legal_hold_status(
            s3_client=self._s3_client, bucket_name=bucket_name, s3_key=dest_s3_key
        )
        if not legal_hold_status:
            err_msg = "Failed to get legal hold status, invalid response"
            log.error(f"[LEGAL HOLD] {err_msg} for file {dest_file_uri}")
            return RetentionInfo(
                retentionStatus=RetentionStatus.FAILED,
                retentionErrorMessage=err_msg,
                id=retention_record.id,
            )

        if legal_hold_status == set_legal_hold_status.value:
            # No action needed if legal hold status is already applied
            log.info(
                f"[LEGAL HOLD] status already set to `{set_legal_hold_status.value}` "
                f"for {dest_file_uri}"
            )
        else:
            log.info(
                f"[LEGAL HOLD] applying status `{set_legal_hold_status.value}` on {dest_file_uri}"
            )
            status_code = s3_utils.apply_legal_hold_status(
                s3_client=self._s3_client,
                bucket_name=bucket_name,
                s3_key=dest_s3_key,
                status=set_legal_hold_status.value,
            )
            if status_code != 200:
                err_msg = (
                    f"Failed to apply legal hold status `{set_legal_hold_status.value}`, "
                    f"ResponseStatusCode: {status_code}"
                )
                log.error(f"[LEGAL HOLD] {err_msg}, File: {dest_file_uri}")
                return RetentionInfo(
                    retentionStatus=RetentionStatus.FAILED,
                    retentionErrorMessage=err_msg,
                    id=retention_record.id,
                )

        # Setting retention_path only in success cases
        return RetentionInfo(
            retentionStatus=set_legal_hold_status,
            retentionPath=dest_file_uri,
            id=retention_record.id,
        )

    def _delete_object_versions(
        self, bucket_name: str, s3_key: str
    ) -> Tuple[int | None, list[Any]]:
        """Deletes S3 Objects and all its versions.

        Returns error if directory is passed instead of s3 object.
        To avoid risk of deleting directories.

        Args:
            s3_client (boto3.client.S3): S3 client
            bucket_name (str): S3 bucket name
            s3_key (str): S3 object key path

        Returns:
            delete_status (optional(int)): HTTP Status code, 200 if success
            errors (optional(list)): Errors if any
        Raises:
            client.exceptions.NoSuchKey: if s3 key path doesn't exist
            botocore.exceptions.ClientError(AccessDenied): if role doesn't have DeleteObject
            permission
        """
        # If versioning is not enabled for the object, VersionId will be `null`
        # This shouldn't effect delete_objects call
        errors: list[Any] = []
        delete_status: int = -1

        # Checking if s3_key is a directory,
        # If directory returns error, deletion of directories is not supported
        s3_obj = s3_utils.get_object(
            s3_client=self._s3_client, bucket_name=bucket_name, s3_key=s3_key
        )
        if "application/x-directory" in s3_obj["ContentType"]:
            errors.append(
                f"Passed S3 Key {s3_key} is a directory, Deleting directories is not supported"
            )
            return delete_status, errors

        for versions in s3_utils.list_object_versions_by_prefix(
            s3_client=self._s3_client, bucket_name=bucket_name, prefix=s3_key, delete_markers=True
        ):
            # Processing versions in chunks
            for i in range(0, len(versions), BULK_DELETE_CHUNK_SIZE):
                delete_resp = s3_utils.s3_retriable_call(
                    self._s3_client.delete_objects,
                    Bucket=bucket_name,
                    Delete={
                        "Objects": versions[i : i + BULK_DELETE_CHUNK_SIZE],
                        "Quiet": True,
                    },
                )
                _status_code = delete_resp.get("ResponseMetadata", {}).get("HTTPStatusCode", -1)
                if _status_code > delete_status:
                    # Any status code which are > 200 are actually failed cases
                    # Returning the max error code, instead of list of error codes
                    delete_status = _status_code
                if delete_resp.get("Errors"):
                    errors.append(delete_resp.get("Errors"))

        return delete_status, errors
