# type:ignore

import logging
import pandas as pd
from aws_inspector_report_analysis.constants import (
    AWSInspectorReportColumn,
    AWSInspectorReportValue,
    IACTag,
    Tag,
)

log = logging.getLogger(__name__)


class Constants:
    EC2_INSPECTOR_REPORT_FILENAME = "EC2-inspector-report-{date}.csv"
    NON_PRODUCTION_ASSETS = "Non-Production Assets"
    PRODUCTION_SHARED_ASSETS = "Production Shared Assets"
    PRODUCTION_CLIENT_ASSETS = "Production Client Assets"
    TAG_SCOPE = "Scope"
    DATE_COLUMN = "date"
    NEW_COLUMN = "NEW"
    NEW_UNIQUE_COLUMN = "NEW_UNIQUE"
    RESOURCE_COLUMN = "RESOURCES"


PLA = {
    Constants.PRODUCTION_CLIENT_ASSETS: {
        AWSInspectorReportValue.SEVERITY_CRITICAL: 10,
        A<PERSON>InspectorReportValue.SEVERITY_HIGH: 30,
        AWSInspectorReportValue.SEVERITY_MEDIUM: 90,
        AWSInspectorReportValue.SEVERITY_LOW: 270,
    },
    Constants.PRODUCTION_SHARED_ASSETS: {
        AWSInspectorReportValue.SEVERITY_CRITICAL: 30,
        AWSInspectorReportValue.SEVERITY_HIGH: 90,
        AWSInspectorReportValue.SEVERITY_MEDIUM: 270,
        AWSInspectorReportValue.SEVERITY_LOW: 810,
    },
    Constants.NON_PRODUCTION_ASSETS: {
        AWSInspectorReportValue.SEVERITY_CRITICAL: 30,
        AWSInspectorReportValue.SEVERITY_HIGH: 90,
        AWSInspectorReportValue.SEVERITY_MEDIUM: 270,
        AWSInspectorReportValue.SEVERITY_LOW: 810,
    },
}


def generate_kpis(df_report: pd.DataFrame, report_date: str, scope: dict):
    # assess kpi's for each report date
    df_report_metrics = filter_data(df=df_report, scope=scope)
    metrics = aggregate_metrics(df_report_metrics, report_date)
    return metrics


def filter_data(df: pd.DataFrame, scope: dict):
    # filter out informational and untriaged findings and those without a fix available
    mask = (
        (df[Tag.STACK].notnull())
        & (df[AWSInspectorReportColumn.FIX_AVAILABLE] == AWSInspectorReportValue.FIX_AVAILABLE_YES)
        & (~df[AWSInspectorReportColumn.SEVERITY].str.contains("INFORMATIONAL|UNTRIAGED"))
    )

    if scope and scope.get("constrained"):
        suffix = scope.get("stackSuffix")
        mask &= df[Tag.STACK].str.endswith(suffix)

    filtered = df.loc[mask].copy()

    # filter out on-demand instances
    mask_remove_on_demand = (filtered[IACTag.NAME].notnull()) & (
        ~filtered[IACTag.NAME].str.contains("-ondemand")
    )

    static_df = filtered.loc[mask_remove_on_demand].copy()

    # tag scope
    static_df.loc[:, Constants.TAG_SCOPE] = static_df.loc[:, Tag.STACK].apply(tag_scope)
    return static_df


def tag_scope(stack: str):
    if stack.startswith(("dev-", "poc-", "sit-", "uat-")):
        return Constants.NON_PRODUCTION_ASSETS

    if stack.startswith("prod-") and not stack.endswith("-io"):
        return Constants.PRODUCTION_CLIENT_ASSETS

    return Constants.PRODUCTION_SHARED_ASSETS


def tag_ondemand(tag_name: str, stack: str):
    if stack.startswith(("dev-", "poc-", "sit-", "uat-")):
        return Constants.NON_PRODUCTION_ASSETS

    if stack.startswith("prod-") and not stack.endswith("-io"):
        return Constants.PRODUCTION_CLIENT_ASSETS

    return Constants.PRODUCTION_SHARED_ASSETS


def aggregate_metrics(df: pd.DataFrame, date: str):
    # aggregate by scope and severity
    metrics = (
        df.groupby([Constants.TAG_SCOPE, AWSInspectorReportColumn.SEVERITY])[
            AWSInspectorReportColumn.SEVERITY
        ]
        .count()
        .reset_index(name=date)
    )
    # aggregate unique vulnerabilities by scope and severity
    metrics_unique = (
        df.loc[:]
        .drop_duplicates(
            subset=[
                Constants.TAG_SCOPE,
                AWSInspectorReportColumn.SEVERITY,
                AWSInspectorReportColumn.VULNERABILITY_ID,
            ],
            keep="first",
        )
        .groupby([Constants.TAG_SCOPE, AWSInspectorReportColumn.SEVERITY])[
            AWSInspectorReportColumn.SEVERITY
        ]
        .count()
        .reset_index(name=date)
    )
    metrics_unique[AWSInspectorReportColumn.SEVERITY] = (
        metrics_unique[AWSInspectorReportColumn.SEVERITY] + "_UNIQUE"
    )
    # aggregate by scope and age less or equal to 30 days
    mask_30_days = df[AWSInspectorReportColumn.AGE_DAYS] <= 30
    v30_df = (
        df.loc[mask_30_days]
        .groupby([Constants.TAG_SCOPE])[AWSInspectorReportColumn.SEVERITY]
        .count()
        .reset_index(name=date)
    )
    v30_df.insert(1, AWSInspectorReportColumn.SEVERITY, Constants.NEW_COLUMN)
    # aggregate unique vulnerabilities by scope and age less or equal to 30 days
    v30_distinct_df = (
        df.loc[mask_30_days]
        .drop_duplicates(subset=[AWSInspectorReportColumn.VULNERABILITY_ID], keep="first")
        .groupby([Constants.TAG_SCOPE])[AWSInspectorReportColumn.SEVERITY]
        .count()
        .reset_index(name=date)
    )
    v30_distinct_df.insert(1, AWSInspectorReportColumn.SEVERITY, Constants.NEW_UNIQUE_COLUMN)
    # aggregate unique resources by scope
    resource_distinct_df = (
        df.loc[:]
        .drop_duplicates(subset=[AWSInspectorReportColumn.RESOURCE_ID], keep="first")
        .groupby([Constants.TAG_SCOPE])[AWSInspectorReportColumn.SEVERITY]
        .count()
        .reset_index(name=date)
    )
    resource_distinct_df.insert(1, AWSInspectorReportColumn.SEVERITY, Constants.RESOURCE_COLUMN)

    frames = [metrics, metrics_unique, v30_df, v30_distinct_df, resource_distinct_df]

    for scope in PLA.keys():
        scope_pla = PLA.get(scope)
        for severity in scope_pla.keys():
            pla_days = scope_pla.get(severity)
            pla_df = assess_pla(df=df, scope=scope, severity=severity, pla_days=pla_days, date=date)
            frames.append(pla_df)

    metrics = pd.concat(frames).sort_values(
        by=[Constants.TAG_SCOPE, AWSInspectorReportColumn.SEVERITY]
    )

    return metrics


def assess_pla(df: pd.DataFrame, scope: str, severity: str, pla_days: int, date: str):
    mask_pla = (
        (df[AWSInspectorReportColumn.SEVERITY] == severity)
        & (df[Constants.TAG_SCOPE] == scope)
        & (df[AWSInspectorReportColumn.AGE_DAYS] >= pla_days)
    )
    pla_df = (
        df.loc[mask_pla]
        .groupby([Constants.TAG_SCOPE])[AWSInspectorReportColumn.SEVERITY]
        .count()
        .reset_index(name=date)
    )
    pla_df.insert(1, AWSInspectorReportColumn.SEVERITY, f"PLA_{severity}")
    pla_distinct = df.loc[mask_pla].drop_duplicates(
        subset=[AWSInspectorReportColumn.VULNERABILITY_ID], keep="first"
    )
    pla_distinct_df = (
        pla_distinct.groupby([Constants.TAG_SCOPE])[AWSInspectorReportColumn.SEVERITY]
        .count()
        .reset_index(name=date)
    )
    pla_distinct_df.insert(1, AWSInspectorReportColumn.SEVERITY, f"PLA_{severity}_UNIQUE")
    pla_df = pd.concat([pla_df, pla_distinct_df])
    return pla_df
