{"exportBucket": "se-inspector-exports", "reportUri": "~/Desktop/Macbook/SecOps/AWS Inspector/2024/09/30/2d7c19d3-56bf-4e0e-b70f-41450ac50455.csv", "analysisOutputPath": "~/Desktop/Macbook/SecOps/AWS Inspector/Reports", "ec2": {"enabled": true, "jiraBypass": true, "kpi": {"enabled": true, "reports": [{"enabled": true, "name": "vm-kpi-report", "uri": "s3://se-security-operations/kpi/vm-kpi-report.csv"}, {"enabled": true, "name": "shell-vm-kpi-report", "uri": "s3://se-security-operations/kpi/shell-vm-kpi-report.csv", "scope": {"constrained": false, "stackSuffix": "-shell", "name": "shell"}}]}}, "ecr": {"enabled": false, "jiraBypass": true, "scopes": [{"enabled": true, "scopeName": "UBS", "scopeImages": {"audit-api-svc": "0.1.25", "data-provenance-api-svc": "0.3.35", "iris-ui": "1.29.00-URC1", "kafka-services": "v0.30.28", "order-api-svc": "1.0.19", "order-integration-flows": "1.4.40", "security-api-svc": "0.2.72", "surveillance-api-svc": "0.4.33", "tenant-api-svc": "0.2.24", "trade-surveillance-flows": "0.6.37", "ubs-tools": "0.1.9", "tenant-data-schema": "1.2.2", "data-lake-flows": "0.3.5"}}, {"enabled": false, "scopeName": "ARIES", "scopePrefix": "aries-"}]}}