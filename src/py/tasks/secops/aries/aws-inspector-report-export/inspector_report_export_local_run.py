import datetime
import json
from aries_io_event.io_param import IOParamFieldSet
from aries_io_event.task import TaskFieldSet
from aries_io_event.workflow import WorkflowFieldSet
from aries_task_link.models import AriesTaskInput
from aws_inspector_report_export.main import aws_inspector_report_export


def main():
    fp = open("io_params.json")
    io_params = json.load(fp)
    fp.close()

    workflow = WorkflowFieldSet(
        trace_id="run_aws_inspector_report_export",
        name="aws_inspector_report_export",
        stack="enterprise",
        start_timestamp=datetime.datetime.utcnow(),
        tenant="security",
    )
    input_param = IOParamFieldSet(params=io_params)
    task = TaskFieldSet(name="aws_inspector_report_export", version="latest", success=False)

    task_input = AriesTaskInput(workflow=workflow, input_param=input_param, task=task)

    aws_inspector_report_export(aries_task_input=task_input)


if __name__ == "__main__":
    main()
