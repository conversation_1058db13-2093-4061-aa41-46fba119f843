import io
from paramiko import RSAKey
import fsspec
import paramiko
paramiko.common.logging.basicConfig(level=paramiko.common.DEBUG)


# add details
# d = {
#     "host": "sftp.bloomberg.com",
#     "port": 22,
#     "username": "mc1262065488",
#     "private_key_ascii": """-----BEGIN RSA PRIVATE KEY-----
# MIIJKQIBAAKCAgEA3DR+Iep0vpadBrw1WCLsMhqyLoUBQc82Ff/Q8sm+jWWjN1Y5
# UOlWUCUBSwATcflDMsYM0L4bbcVtcBvPxmK2D4G+TGmDuOV7jPsG5scA1iWDNkJf
# A8CTKcRK0D6f/e/E5EDvoiWAi0klE07L5hghbhCxG0PiKQQ3cxBeZfLqjiWkdExN
# Nch4ppi7/85Uqrc3EslqKou+kV+AD2IY5ZW/0mJ/UsAiv70wEz4Rys9vDq2uaEy2
# yEId16jCa6/MJxZ8eK6B01B0glYTyWgW4pG9MWfTRwqNLhEb5PBN6pn2LSUotBgV
# 3gNMFDV6IxMgETdaOHIHMc6xxFB1kJ+9D56OqI3WjeFrJ1+tBgfLNcInuu2YlPO3
# mscAi4GRYRsgpWChRxpqto+XiY6MqGiRW7EmC2Y+kkYMRjpsulSkKHury5rp8DUW
# 5UVoBgYw4j5+JOqtebN2CWuId4sc1+nFN0jCuVms9j9F8d10kG12Pg90XrvXlaGE
# bTrWJOU9kXlNGquBwtAHZJ4dWyph1vbqQsbJl5bjrRLHIYN9HWf7iQpxhANPC0qi
# zES2NhjqRad7WmIBzOCrw3uI77JuzvKsaKWWEpn3HSmck/Vj8l172fgSELPhfG/r
# 1/k02FN6yXJnxEHX8uxUn9llOidOfTQRq2yzQZnbdoR/LWxQuPifHqDmzn8CAwEA
# AQKCAgBpw1iVh5t88rAGzRy98UAKrKHKEB3TcueiV1UPV3CLTza+XE55GylWov9k
# +2tbgYIR/+el1rkr1vhCMUlxGnYhKoqw/rjCdI4L34GoFArF0AyeYfBqx/Kmy922
# +W00tRWXH804ztRXkdEZ3e223P4pdCDsXIVid+D+Be2g/7RCg7fiF8e5vkick50w
# NvSZbhgokM7KEoZ8IidKvC4DDxKrxJ0srJXEC+TsSRKYm1rW7Ppoo/fiZUcaDrca
# q7vdRkKQYBiA2xKqixdD1BexyJfbA+gJJfs7FgKUygpSVJJVipRrM2pFUMYYqrq3
# 3/a17iUKGVJ1TLY6+P4OcXqH5vrS+/TDdRPpC6UNiy1XiQvSsNcgwzXdJ3aBnX/E
# JHUS2imCaKN8yYYDJHScmxw/RLY8+jp4ejdUVL6VAcU/Xf5dNTaZ/QYwLwnJeMZK
# dqPBfgjwuYrFvEJaj3ldcuk5qAiS1/jvcmODZ7p/NY6cXXfpQi94O6dNvIbJYzUM
# qVH3vffi7jaRxNjSnmRRC90qMf2pEoM6vjTUbukcT0ChBBjV4m62ELfHUOUw6tQ4
# MQk2x/Dp2BTsFMcMRib4yFRpUSwabnwjM3ClTXulXb7Ai5UDK2Xn3U16Yg1cA4CU
# Mx8CmOPOuexBq4fFlyXhdOPHB5bdbLuTKcTASbSPF17Zyi5RsQKCAQEA8oTB34WK
# 7kpqw2HpiqYWFwF8qoiaAzPNWfg32+OdvUV9gTpJkcPQ5Utcw6/AXEgBa5jkt7Kj
# 8jTyEAbd3oGTyHt4oXYMIYbo1F3CNYKKOypPorWpK9DrgluDveIoffF31TzvrQ9d
# 4R94iKtlXhT9RPNYDvjhvX+rd8y0nd2uvSDHXG3qEGEE+zvOfr+zPH9NBuPJWZ9m
# 6sp1P73lkAKTwVioLq0EtN6lio9uk06Pva8Kjh7TYSG5Tk45+JDnkahUmwWZD60A
# wDhq5q6ftDszjMayjMW5JcRoZcuS0G0x+cuCw+28lFm7dHNziFAQZSUCTdZp/8S7
# 66DrHUCJBEBz5wKCAQEA6HIx8l5A/eevGf/CVhM92pGVf2Jqlng5jjM770YUSoDg
# ikhIxoht8LBCtU+cDkDQ25ePxIiB9ACP0IXkVROuGj8sKcK4WsCebiovpp8ZbDFR
# CNNyBvb4ELGLHX6L6DMlEF3HtYr9yO7hF/lsoUy3x0FTmGu73ZhFNtU3RqeSH7dW
# oxd7c/lSV/wH9rRloUZnOLTaZj5XYpO1lOnTnodJ42/Q1cpdoAW+s72jg5WmnJ2h
# 68KruVXwDlTBUsd0DWLqmaSrq+HjsKcRyR9FKQ+qjfP1vQLG/G5lM8b68IRHxLcB
# QFhqG27rPcUuAKdo/BKkmpmbD7h7nceJo3bX99n9qQKCAQAmxCnpfQNUS/j8+m3G
# h/4H92jiKavUzzcIJrFnKN9SHgWN3q9b6Z0+yekuLq3lEm2HsYz+AERMt0HqZe6Z
# XsUsyu/hf2k5AsO9Dq+4tgE6q+zG6Zr9MgwzbkWE2N34I90v7OmzFD3BerJdxmgR
# vfEuUGfHHOmYzQuvvr0wbvs7qD21/KvpqliNgGXlxOb1yLObYlYRSXW+GlbbtZM4
# jfDoey+HJokWEHEN0Rev3GMFwzFAvpSzdhyU89Q4c5FQq0O69A4ff1wbXelYrplA
# yiXXXvuznPeXnenCO+RgBNR0RZ/Fif8XB7D7UGkrZQD56gyQPLH8/Im9hJ+QsXw1
# fcgJAoIBAQCzByEI9yc/Z64pbB8VhIRjTzXBgt9tPPO3XiaPR35hU5mdZ8cFghaL
# BnaahtI++BTkzHrid/D/KV+az0TpcdrqRDWThHHRFAHx/3Pr9SXzephbH9/3FUhC
# MzxeXxUKpjmpvVEPthl5JOMhP+423Yu+X8Yu4usy5RCKBvYn6PBWlTKZMoDW9fRB
# 58+KZ67O5BakSkbb8LAFMJbuNpnSw4hZL8m8n7w0f7g9X5YMxroobhBEMQ9qE5Js
# t0KeqxfrJ4BhDb9bBwQDDupX6UmvMm2+H2PpS7ZOvggcNOkZTgiorfq8wFwY/PkZ
# /kBMKKRnYUR9/ki7du9eukx8JhB4yODhAoIBAQDJFTIzeekyhT0/7wz65kmEn3LH
# MSKbFWJmOBvBR6RlV2LTQLV2mnBOwGwxE+DqBonii2QBEk+JdrNsg226junPJQpl
# ZQ3TAJdF3q+y3k9V2P0bc1LIw2vDagJncu9OEQ53r2QI3hZd7zVtMIaYkf/UE95I
# MQ3B/R04EzcCOI5Nnp3zlUE33MHbxLBjQ10S/kopU63eZTkJXwYVh10sW6ZFEx1K
# WhJq4PxKXku7gNOPpMWM8RWzin/pUrMkuXkrjIEPLSQG86Qj0L9duxDqQQb/KOM1
# IE8vAJ/wD6aYIyxMWBnc8mbxsKXPEJgRi8oEBEeV7i+8eSj7cmLzIQikCv/z
# -----END RSA PRIVATE KEY-----""",
# }

# add details
d = {
    "host": "sftp.bloomberg.com",
    "port": 22,
    "username": "mc1313494161",
    "private_key_ascii": """**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************""",
}

def with_proxy():
    pkey = RSAKey.from_private_key(io.StringIO(d['private_key_ascii']))
    proxy = paramiko.proxy.ProxyCommand('corkscrew proxy.enterprise.steeleye.co 8080 %s %d' % (d['host'], d['port']))

    authentication_kwargs = dict()
    authentication_kwargs["pkey"] = pkey
    authentication_kwargs["sock"] = proxy

    fs = fsspec.filesystem(
        "sftp", host=d['host'], port=d['port'], username=d['username'], **authentication_kwargs
    )
    if fs:
        print("fs found:")
        files = fs.ls("/", detail=True)
        files = [f for f in files if '240920' in f['name'] and 'gpg' in f['name']]
        print("Files in the remote directory:")
        k = []
        for file in files:
            k.append(file['name'])
#             Uncomment the following if you want to download the file
#             print(f"Downloading {file}")
            # fs.get(file["name"], f"/Users/<USER>/JIRAs/BloombergRobeco/20/{file['name']}")
        print(sorted(k))


if __name__ == '__main__':
    with_proxy()