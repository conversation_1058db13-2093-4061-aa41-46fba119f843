{"description": "Manual Annotation for our anonymized alerts dataset", "label_config": "<View>\n  \n  <Collapse name=\"metaCollapse\" collapsed=\"true\">\n    <Panel name=\"metaPanel\" header=\"Document Metadata\" value=\"Document Metadata\" style=\"border: 1px solid #e8e8e8; border-radius: 4px; margin-bottom: 16px;\">\n      <View name=\"metaContent\" style=\"padding: 16px; background-color: #fff;\">\n        <View name=\"metaGrid\" style=\"display: grid; grid-template-columns: repeat(2, 1fr); gap: 12px;\">\n          \n          <View name=\"leftCol\">\n            <Header name=\"genericHeader\" value=\"General Information\" style=\"font-weight: bold !important; font-size: 18px; color: #666; margin-bottom: 8px;\"/>\n            <Text name=\"alertModel\" value=\"Alert Model: $hitModel\" style=\"font-weight: bold !important; color: #1890ff;\"/>\n            <Text name=\"semanticCluster\" value=\"Semantic Cluster: $cluster\" style=\"font-size: 13px;\"/>\n            <Text name=\"topWords\" value=\"Cluster Top 10 Words: $top_10_words\" style=\"font-size: 13px;\"/>\n            <Text name=\"docSummary\" value=\"Document Summarization: $doc_summarization\" style=\"font-size: 13px;\"/>\n            <View name=\"lexicaDetails\" style=\"margin-top: 16px;\">\n              <Text name=\"lexicaTerm\" value=\"Lexica Term: $matchedLexica\" style=\"font-size: 13px;\"/>\n              <Text name=\"lexicaCategory\" value=\"Lexica Category: $matchedLexicaCategories\" style=\"font-size: 13px;\"/>\n              <Text name=\"lexicaSource\" value=\"Lexica Source: $matchedLexicaSource\" style=\"font-size: 13px;\"/>\n              <Text name=\"workflowStatus\" value=\"Workflow Status: $workflow.status\" style=\"font-size: 13px;\"/>\n              <Text name=\"workflowresolutionCategory\" value=\"Workflow Resolution Category: $workflow.resolutionCategory\" style=\"font-size: 13px;\"/>\n              <Text name=\"workflowresolutionSubCategories\" value=\"Workflow Resolution Sub Category: $workflow.resolutionSubCategories\" style=\"font-size: 13px;\"/>\n            </View>\n            <Text name=\"watchKind\" value=\"Watch Kind: $watchKind\" style=\"font-size: 13px;\"/>\n            <Text name=\"neuralClass\" value=\"Neural Classification: $classification\" style=\"font-weight: bold !important; color: #1890ff;\"/>\n            \n            <View name=\"o1Assessment\" style=\"margin-top: 16px;\">\n              <Text name=\"4ominillmAssessment\" value=\"GPT 4o Mini Assessment: $llm_assessment_during_summarization\" style=\"font-size: 13px;\"/>\n              <Text name=\"o1gptExplanation\" value=\"GPT o1 Explanation If Not Alert: $explanation_if_not_alert\" style=\"font-size: 13px;\"/>\n              <Text name=\"o1gptComment\" value=\"GPT o1 Comment: $comments\" style=\"font-size: 13px;\"/>\n            </View>\n          </View>\n          \n          <View name=\"rightCol\">\n            \n            <View name=\"l1Assessment\" style=\"margin-top: 16px;\">\n              <Header name=\"l1Header\" value=\"L1 Assessment\" style=\"font-weight: bold !important; font-size: 18px; color: #666; margin-bottom: 8px;\"/>\n              <Text name=\"l1Assess\" value=\"Assessment: $assessments.l1.assessment\" style=\"font-size: 13px;\"/>\n              <Text name=\"l1Behavior\" value=\"Behaviour: $assessments.l1.behaviour\" style=\"font-size: 13px;\"/>\n              <Text name=\"l1Recommend\" value=\"Recommendations: $assessments.l1.recommendations\" style=\"font-size: 13px;\"/>\n              <Text name=\"l1Severity\" value=\"Severity Score: $assessments.l1.severityScoreWeighted\" style=\"font-size: 13px;\"/>\n              <Text name=\"l1Triggers\" value=\"Triggers: $assessments.l1.triggers\" style=\"font-size: 13px;\"/>\n            </View>\n            \n            <View name=\"l2Assessment\" style=\"margin-top: 16px;\">\n              <Header name=\"l2Header\" value=\"L2 Assessment\" style=\"font-weight: bold !important; font-size: 18px; color: #666; margin-bottom: 8px;\"/>\n              <Text name=\"l2Assess\" value=\"Assessment: $assessments.l2.assessment\" style=\"font-size: 13px;\"/>\n              <Text name=\"l2Behavior\" value=\"Behaviour: $assessments.l2.behaviour\" style=\"font-size: 13px;\"/>\n              <Text name=\"l2Recommend\" value=\"Recommendations: $assessments.l2.recommendations\" style=\"font-size: 13px;\"/>\n              <Text name=\"l2Severity\" value=\"Severity Score: $assessments.l2.severityScoreWeighted\" style=\"font-size: 13px;\"/>\n              <Text name=\"l2Triggers\" value=\"Triggers: $assessments.l2.triggers\" style=\"font-size: 13px;\"/>\n            </View>\n            \n            <View name=\"l3Assessment\" style=\"margin-top: 16px;\">\n              <Header name=\"l3Header\" value=\"L3 Assessment\" style=\"font-weight: bold !important; font-size: 18px; color: #666; margin-bottom: 8px;\"/>\n              <Text name=\"l3Assess\" value=\"Assessment: $assessments.l3.assessment\" style=\"font-size: 13px;\"/>\n              <Text name=\"l3Behavior\" value=\"Behaviour: $assessments.l3.behaviour\" style=\"font-size: 13px;\"/>\n              <Text name=\"l3Recommend\" value=\"Recommendations: $assessments.l3.recommendations\" style=\"font-size: 13px;\"/>\n              <Text name=\"l3Severity\" value=\"Severity Score: $assessments.l3.severityScoreWeighted\" style=\"font-size: 13px;\"/>\n              <Text name=\"l3Triggers\" value=\"Triggers: $assessments.l3.triggers\" style=\"font-size: 13px;\"/>\n            </View>\n          </View>\n        </View>\n      </View>\n    </Panel>\n  </Collapse>\n  \n  <Header name=\"subjectHeader\" value=\"$subject\" style=\"font-weight: bold !important; font-size: 18px; color: #1a1a1a; margin: 16px 0;\"/>\n  <Text name=\"bodyText\" value=\"$body.text\" style=\"line-height: 1.6; font-size: 16px; white-space: pre-wrap;\"/>\n  \n  <View name=\"annotationSection\" style=\"margin-top: 24px; padding: 16px; background: #f8f9fa; border-radius: 4px;\">\n    \n    <Header value=\"Classfication Labels\" style=\"font-size: 14px; margin: 16px 0 8px 0; font-weight: 500;\"/>\n    <Choices name=\"human_classification\" \n             toName=\"bodyText\" \n             choice=\"single\" \n             showInline=\"true\"\n             required=\"true\"\n             requiredMessage=\"Please select a classification\">\n      <Choice name=\"alertChoice\" value=\"Alert\" style=\"color: #d4380d; font-weight: bold !important;\"/>\n      <Choice name=\"notAlertChoice\" value=\"Not Alert\" style=\"color: #389e0d; font-weight: bold !important;\"/>\n      <Choice name=\"notSureChoice\" value=\"Not Sure\" style=\"color: #d4880d; font-weight: bold !important;\"/>\n    </Choices>\n\n    \n    <View visibleWhen=\"choice-selected\"\n          whenTagName=\"human_classification\"\n          whenChoiceValue=\"Alert\">\n      <Header value=\"Alert Type:\" style=\"font-size: 14px; margin: 16px 0 8px 0; font-weight: 500;\"/>\n      <Choices name=\"alert_type\" \n               toName=\"bodyText\" \n               choice=\"multiple\" \n               showInline=\"true\"\n               required=\"true\"\n               requiredMessage=\"Please select at least one alert type\">\n        <Choice name=\"BreachAwareness\" value=\"Breach Awareness\" hint=\"This policy monitors communications for indications that employees are aware of potential or actual breaches of company rules and regulations. The policy focuses on language demonstrating knowledge of breaches, the possibility of breaches, relevant laws, consequences of discovery, and company reporting procedures. Additionally, the policy detects communications revealing employee awareness of oversight by supervisory or compliance functions.\"/>\n        <Choice name=\"CollusionInducement\" value=\"Collusion / Inducement\" hint=\"This policy targets language that suggests employees attempt to work together to break company rules. It detects efforts to conceal information, switch to unmonitored communication channels, or express gratitude for a willingness to collude. The policy also focuses on communications demonstrating awareness of potential consequences if the collusion is discovered.\"/>\n        <Choice name=\"ConductRisk\" value=\"Conduct Risk\" hint=\"This policy monitors communications for behaviours that could risk the companys reputation or operations. It focuses on detecting instances of blackmail, bribery, conflict, favouritism, information leaks, transmitting sensitive information, and using unapproved research. The policy also aims to identify language indicating an awareness of potential risks or the fallout from risky behaviour.\"/>\n        <Choice name=\"ConflictandComplaints\" value=\"Conflict and Complaints\" hint=\"This policy focuses on detecting different types of conflicts within the organization. It monitors for general interpersonal conflict, disputes related to company processes, and conflicts centred around specific individuals. Additionally, the policy tracks the progression of complaints, including the initial complaint, the companys response, requests for action, and any adverse consequences resulting from unresolved complaints.\"/>\n        <Choice name=\"GiftsandEntertainment\" value=\"Gifts and Entertainment\" hint=\"This policy monitors communications about offering, accepting, and acknowledging gifts and entertainment events. It focuses on gratitude, references to other included parties (colleagues or family), and any mention of payments or payments-in-kind. The policy ensures compliance with company guidelines and regulations regarding gifts and entertainment.\"/>\n        <Choice name=\"HarassingOffensiveCommunication\" value=\"Harassing / Offensive Communication\" hint=\"This policy targets offensive language used in company communications. It detects language that is insulting or abusive towards specific individuals, whether they are part of the conversation. The policy also monitors for generally offensive language, even if it doesnt target a particular person directly.\"/>\n        <Choice name=\"InappropriateBehaviour\" value=\"Inappropriate Behaviour\" hint=\"This policy identifies communications demonstrating inappropriate conduct, whether as a single instance or a repeated pattern of behaviour. It flags language that suggests inappropriate workplace relationships characterised by emotional expressions, longing, or references to activities outside the professional setting. The policy aims to maintain a respectful and professional workplace environment.\"/>\n        <Choice name=\"InsideInformation\" value=\"Inside Information\" hint=\"This policy targets communications that reveal the transmission of inside information. It focuses on identifying the source of the information (whether abstract or a specific individual) and the nature of the information itself (impending events, personnel changes, or trading activity). Additionally, the policy monitors for any language, suggesting awareness that the information is not yet publicly known.\"/>\n        <Choice name=\"MarketAbuse\" value=\"Market Abuse\" hint=\"This policy targets communications that suggest various forms of market abuse. It focuses on detecting references to algorithm manipulation, circular trading, inappropriate client order handling, coordinated trading, price manipulation, and influencing reference prices. The policy carefully distinguishes between planning, awareness, and the past, present, or future occurrence of such activities.\"/>\n        <Choice name=\"Mistakes\" value=\"Mistakes\" hint=\"This policy focuses on identifying communications where employees express awareness of mistakes being made. It also tracks the various stages of handling mistakes, including apologies, expressions of responsibility, operational errors, and the presence or absence of remediation efforts. The policy likely aims to foster a culture of accountability and continuous improvement within the organisation.\"/>\n        <Choice name=\"Performance\" value=\"Performance\" hint=\"This policy monitors communications for references to order placement and execution performance. It focuses on detecting language that indicates awareness of issues such as poor order handling, problems in achieving best execution, or disagreements with brokers/counterparts about execution quality. The policy aims to ensure optimal trade execution and maintain positive relationships with external parties.\"/>\n        <Choice name=\"other_alert\" value=\"Other\" hint=\"Select this option if you want to classify it as another class. Ensure you put that in the Reason for Classification\"/>\n      </Choices>\n    </View>\n    <View visibleWhen=\"choice-selected\"\n          whenTagName=\"human_classification\"\n          whenChoiceValue=\"Not Alert\">\n      <Header value=\"Not Alert Type:\" style=\"font-size: 14px; margin: 16px 0 8px 0; font-weight: 500;\"/>\n      <Choices name=\"non_alert_type\" \n               toName=\"bodyText\" \n               choice=\"multiple\" \n               showInline=\"true\"\n               required=\"true\"\n               requiredMessage=\"Please select at least one non alert type\">\n        <Choice name=\"FalsePositive\" value=\"False Positive\"/>\n        <Choice name=\"NearMiss\" value=\"Near Miss\"/>\n      </Choices>\n    </View>\n    <Header value=\"Reason for Classification:\" style=\"font-size: 14px; margin: 16px 0 8px 0; font-weight: 500;\"/>\n    <TextArea name=\"human_classification_reason\" \n              toName=\"bodyText\"\n              rows=\"4\" \n              style=\"width: 100%; \n                     padding: 12px; \n                     border: 1px solid #ddd; \n                     border-radius: 4px; \n                     background: #fff;\"\n              placeholder=\"Explain your classification decision...\"\n              maxSubmissions=\"1\"/>\n  </View>\n  \n  <Style>\n    /* Force bold styling */\n    [style*=\"font-weight: bold\"], \n    [style*=\"font-weight: bold i\"] {\n      font-weight: bold !important;\n    }\n    \n    /* Collapse header override */\n    .ant-collapse > .ant-collapse-item > .ant-collapse-header {\n      font-weight: bold !important;\n      background: #fafafa !important;\n    }\n    \n    /* Base text styling */\n    .htx-text {\n      font-family: system-ui, -apple-system, sans-serif;\n      font-size: 14px;\n    }\n    \n    \n    /* Custom tooltip styling */\n    .ant-tooltip-inner {\n      white-space: normal !important;\n      max-width: 300px !important;\n      word-wrap: break-word !important;\n      text-align: left !important;\n      padding: 12px !important;\n      border-radius: 4px !important;\n      background: #2a2a2a !important;\n      box-shadow: 0 3px 6px rgba(0,0,0,0.16) !important;\n    }\n\n    /* Tooltip arrow color */\n    .ant-tooltip-arrow::before {\n      background: #2a2a2a !important;\n    }\n\n    /* Delay tooltip appearance */\n    .ant-tooltip {\n      animation-duration: 300ms !important;\n    }\n  </Style>\n</View>", "expert_instruction": "<p>Please refer <a href=\"https://steeleyed-my.sharepoint.com/:w:/r/personal/ai-vee_lim_steel-eye_com/Documents/03.%20Initiatives/2025%20H1/TrueAlert%20CIP/Data%20Labelling/cSurv/Labeller%20Playbook%20v1.0.docx?d=w379e7f5f8a0b4f21b0f7841307c383a2&amp;csf=1&amp;web=1&amp;e=Xdhzxy\" rel=\"noopener noreferrer\">here</a> for labeling manual.</p>", "show_instruction": true, "show_skip_button": true, "enable_empty_annotation": true, "show_annotation_history": false, "organization": 1, "color": "#FFFFFF", "maximum_annotations": 1, "is_published": false, "model_version": "", "is_draft": false, "control_weights": {"alert_type": {"type": "Choices", "labels": {"Other": 1.0, "Mistakes": 1.0, "Performance": 1.0, "Conduct Risk": 1.0, "Market Abuse": 1.0, "Breach Awareness": 1.0, "Inside Information": 1.0, "Collusion / Inducement": 1.0, "Conflict and Complaints": 1.0, "Gifts and Entertainment": 1.0, "Inappropriate Behaviour": 1.0, "Harassing / Offensive Communication": 1.0}, "overall": 1.0}, "non_alert_type": {"type": "Choices", "labels": {"Near Miss": 1.0, "False Positive": 1.0}, "overall": 1.0}, "human_classification": {"type": "Choices", "labels": {"Alert": 1.0, "Not Sure": 1.0, "Not Alert": 1.0}, "overall": 1.0}, "human_classification_reason": {"type": "TextArea", "labels": {}, "overall": 1.0}}, "parsed_label_config": {"alert_type": {"type": "Choices", "inputs": [{"type": "Text", "value": "body.text", "valueType": null}], "labels": ["Breach Awareness", "Collusion / Inducement", "Conduct Risk", "Conflict and Complaints", "Gifts and Entertainment", "Harassing / Offensive Communication", "Inappropriate Behaviour", "Inside Information", "Market Abuse", "Mistakes", "Performance", "Other"], "to_name": ["bodyText"], "labels_attrs": {"Other": {"hint": "Select this option if you want to classify it as another class. Ensure you put that in the Reason for Classification", "name": "other_alert", "value": "Other"}, "Mistakes": {"hint": "This policy focuses on identifying communications where employees express awareness of mistakes being made. It also tracks the various stages of handling mistakes, including apologies, expressions of responsibility, operational errors, and the presence or absence of remediation efforts. The policy likely aims to foster a culture of accountability and continuous improvement within the organisation.", "name": "Mistakes", "value": "Mistakes"}, "Performance": {"hint": "This policy monitors communications for references to order placement and execution performance. It focuses on detecting language that indicates awareness of issues such as poor order handling, problems in achieving best execution, or disagreements with brokers/counterparts about execution quality. The policy aims to ensure optimal trade execution and maintain positive relationships with external parties.", "name": "Performance", "value": "Performance"}, "Conduct Risk": {"hint": "This policy monitors communications for behaviours that could risk the company's reputation or operations. It focuses on detecting instances of blackmail, bribery, conflict, favouritism, information leaks, transmitting sensitive information, and using unapproved research. The policy also aims to identify language indicating an awareness of potential risks or the fallout from risky behaviour.", "name": "ConductRisk", "value": "Conduct Risk"}, "Market Abuse": {"hint": "This policy targets communications that suggest various forms of market abuse. It focuses on detecting references to algorithm manipulation, circular trading, inappropriate client order handling, coordinated trading, price manipulation, and influencing reference prices. The policy carefully distinguishes between planning, awareness, and the past, present, or future occurrence of such activities.", "name": "MarketAbuse", "value": "Market Abuse"}, "Breach Awareness": {"hint": "This policy monitors communications for indications that employees are aware of potential or actual breaches of company rules and regulations. The policy focuses on language demonstrating knowledge of breaches, the possibility of breaches, relevant laws, consequences of discovery, and company reporting procedures. Additionally, the policy detects communications revealing employee awareness of oversight by supervisory or compliance functions.", "name": "BreachAwareness", "value": "Breach Awareness"}, "Inside Information": {"hint": "This policy targets communications that reveal the transmission of inside information. It focuses on identifying the source of the information (whether abstract or a specific individual) and the nature of the information itself (impending events, personnel changes, or trading activity). Additionally, the policy monitors for any language, suggesting awareness that the information is not yet publicly known.", "name": "InsideInformation", "value": "Inside Information"}, "Collusion / Inducement": {"hint": "This policy targets language that suggests employees attempt to work together to break company rules. It detects efforts to conceal information, switch to unmonitored communication channels, or express gratitude for a willingness to collude. The policy also focuses on communications demonstrating awareness of potential consequences if the collusion is discovered.", "name": "CollusionInducement", "value": "Collusion / Inducement"}, "Conflict and Complaints": {"hint": "This policy focuses on detecting different types of conflicts within the organization. It monitors for general interpersonal conflict, disputes related to company processes, and conflicts centred around specific individuals. Additionally, the policy tracks the progression of complaints, including the initial complaint, the company's response, requests for action, and any adverse consequences resulting from unresolved complaints.", "name": "ConflictandComplaints", "value": "Conflict and Complaints"}, "Gifts and Entertainment": {"hint": "This policy monitors communications about offering, accepting, and acknowledging gifts and entertainment events. It focuses on gratitude, references to other included parties (colleagues or family), and any mention of payments or payments-in-kind. The policy ensures compliance with company guidelines and regulations regarding gifts and entertainment.", "name": "GiftsandEntertainment", "value": "Gifts and Entertainment"}, "Inappropriate Behaviour": {"hint": "This policy identifies communications demonstrating inappropriate conduct, whether as a single instance or a repeated pattern of behaviour. It flags language that suggests inappropriate workplace relationships characterised by emotional expressions, longing, or references to activities outside the professional setting. The policy aims to maintain a respectful and professional workplace environment.", "name": "InappropriateBehaviour", "value": "Inappropriate Behaviour"}, "Harassing / Offensive Communication": {"hint": "This policy targets offensive language used in company communications. It detects language that is insulting or abusive towards specific individuals, whether they are part of the conversation. The policy also monitors for generally offensive language, even if it doesn't target a particular person directly.", "name": "HarassingOffensiveCommunication", "value": "Harassing / Offensive Communication"}}}, "non_alert_type": {"type": "Choices", "inputs": [{"type": "Text", "value": "body.text", "valueType": null}], "labels": ["False Positive", "Near Miss"], "to_name": ["bodyText"], "labels_attrs": {"Near Miss": {"name": "NearMiss", "value": "Near Miss"}, "False Positive": {"name": "FalsePositive", "value": "False Positive"}}}, "human_classification": {"type": "Choices", "inputs": [{"type": "Text", "value": "body.text", "valueType": null}], "labels": ["<PERSON><PERSON>", "Not Alert", "Not Sure"], "to_name": ["bodyText"], "labels_attrs": {"Alert": {"name": "alertChoice", "style": "color: #d4380d; font-weight: bold !important;", "value": "<PERSON><PERSON>"}, "Not Sure": {"name": "notSureChoice", "style": "color: #d4880d; font-weight: bold !important;", "value": "Not Sure"}, "Not Alert": {"name": "notAlertChoice", "style": "color: #389e0d; font-weight: bold !important;", "value": "Not Alert"}}}, "human_classification_reason": {"type": "TextArea", "inputs": [{"type": "Text", "value": "body.text", "valueType": null}], "labels": [], "to_name": ["bodyText"], "labels_attrs": {}}}, "evaluate_predictions_automatically": false, "config_has_control_tags": true, "skip_queue": "REQUEUE_FOR_OTHERS", "reveal_preannotations_interactively": false, "pinned_at": null}