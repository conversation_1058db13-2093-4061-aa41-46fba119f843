import time
import requests
import logging
import os
import copy
import json

log = logging.getLogger(__name__)
# Constants
EXPORT_QUERY_PARAMS = ""
EXPORT_COMMS_API_PATH = f"api/v4.0/comms/export-all?{EXPORT_QUERY_PARAMS}"
DOWNLOAD_STATUS_API_PATH = "api/v3.0/download/{download_id}"
PRE_SIGNED_URL_API_PATH = "api/v3.0/download/{download_id}/pre-signed"
RUN_WATCH_EXECUTION_API_PATH = "api/v4.0/comms-surveillance/watch-execution"
WATCH_EXECUTION_STATUS_API_PATH = "api/v4.0/comms-surveillance/watch-execution/{watch_execution_id}"

# TO be set from environment variables
API_HOST = None
API_TOKEN = None
DEFAULT_HEADERS = None


def get_comms():
    url = f"{API_HOST}/{EXPORT_COMMS_API_PATH}"

    log.info(f"Requesting comms from {url}")

    response = requests.post(url, headers=DEFAULT_HEADERS)
    if response.status_code != 201:
        raise Exception(f"Failed to get comms: {response.status_code} {response.text}")

    download_id = response.json().get("download_id")
    if not download_id:
        raise Exception("No download_id found in response.")

    # Wait for the download to be ready
    download_status_url = f"{API_HOST}/{DOWNLOAD_STATUS_API_PATH.format(download_id=download_id)}"
    log.info(f"Waiting for download to be ready at {download_status_url}")
    while True:

        response = requests.get(download_status_url, headers=DEFAULT_HEADERS)
        if response.status_code != 200:
            raise Exception(f"Failed to get download status: {response.status_code} {response.text}")

        download_status = response.json().get("status")
        if download_status == "READY":
            log.info("Download is ready.")
            break
        elif download_status == "FAILED":
            raise Exception(f"Download Comms failed. - {response.json()}")

        time.sleep(5)

    # Get the pre-signed URL
    response = requests.get(f"{API_HOST}/{PRE_SIGNED_URL_API_PATH.format(download_id=download_id)}",
                            headers=DEFAULT_HEADERS)

    pre_signed_url = response.json().get("pre_signed_url")

    if not pre_signed_url:
        raise Exception("No pre_signed_url found in response.")

    log.info(f"Downloading file from {pre_signed_url}")
    response = requests.get(pre_signed_url)

    content = response.content

    lines = content.decode("utf-8").splitlines()

    # Parse each line into a dictionary
    records = [json.loads(line) for line in lines]

    return records


def process(records):
    # Process the records

    """

    THIS FUNCTION IS A PLACEHOLDER FOR THE ACTUAL PROCESSING LOGIC.

    """

    processed_records = []
    unwanted_meta_fields = ["&fqn", "&version", "&uniqueProps", "&validationErrors"]
    for record in records:

        # CUSTOM MODEL GOES HERE

        hit = copy.deepcopy(record)

        # Remove unwanted fields
        for key in record.keys():
            if key in unwanted_meta_fields:
                hit.pop(key, None)

        # Csurv Alert model Dict
        processed_records.append({
            "hit": hit,
            "hitModel": hit.get("&model"),
            # Add any other fields you want to include in the processed record
        })

    log.info(f"Processed {len(processed_records)} records.")
    return processed_records


def run_execution(hits):
    url = f"{API_HOST}/{RUN_WATCH_EXECUTION_API_PATH}"

    response = requests.post(url, json={"records": hits}, headers=DEFAULT_HEADERS)

    if response.status_code != 200:
        raise Exception(f"Failed to run watch execution: {response.status_code} {response.text}")

    watch_execution_id = response.json().get("&id")

    if not watch_execution_id:
        raise Exception("No watch_execution_id found in response.")
    log.info(f"Watch execution started with ID: {watch_execution_id}")

    # wait for the watch execution to complete
    watch_execution_status_url = f"{API_HOST}/{WATCH_EXECUTION_STATUS_API_PATH.format(watch_execution_id=watch_execution_id)}"
    log.info(f"Waiting for watch execution to complete at {watch_execution_status_url}")

    while True:

        response = requests.get(watch_execution_status_url, headers=DEFAULT_HEADERS)
        if response.status_code != 200:
            raise Exception(f"Failed to get watch execution status: {response.status_code} {response.text}")
        watch_execution_status = response.json().get("status")

        if watch_execution_status == "COMPLETED":
            break
        elif watch_execution_status == "ERROR":
            raise Exception(f"Watch execution failed: {response.json()}")

        time.sleep(5)
    log.info("Watch execution completed successfully.")


def run():
    global API_HOST, API_TOKEN, DEFAULT_HEADERS

    API_HOST = os.getenv("API_HOST")
    API_TOKEN = os.getenv("API_TOKEN")

    if not API_HOST or not API_TOKEN:
        raise ValueError("API_HOST and API_TOKEN must be set as environment variables.")

    DEFAULT_HEADERS = {
        "Authorization": f"TOKEN {API_TOKEN}",
    }

    comms = get_comms()

    hits = process(comms)

    run_execution(hits)


if __name__ == "__main__":
    # Set up logging
    logging.basicConfig(level=logging.INFO)

    run()
