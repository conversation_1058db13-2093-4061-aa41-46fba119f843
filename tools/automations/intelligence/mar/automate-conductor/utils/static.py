PREVIOUS_RUN_ORDER_QUERY = {
    "track_total_hits": True,
    "size": 0,
    "query": {}
}

ORDERS_BASE_QUERY = {
    "track_total_hits": True,
    "size": 0,
    "aggs": {
        "sorted": {
            "terms": {
                "field": "&timestamp",
                "order": {
                    "_key": "asc"
                },
                "size": 200_000
            }
        }
    }
}

SURVEILLANCE_WATCH_QUERY = {
            "query": {
                "bool": {
                    "must_not": [
                        {
                            "exists": {
                                "field": "&expiry"
                            }
                        }
                    ],
                    "filter": [
                        {
                           "term": {
                               "&id": ""
                           }
                        }
                    ]
                }
            },
            "size": 1,
            "sort": [
                {
                    "&timestamp": {
                        "order": "desc"
                    }
                }
            ]
        }


class Status:
    RUNNING = "RUNNING"
    COMPLETED = "COMPLETED"
    TERMINATED = "TERMINATED"
    TIMED_OUT = "TIMED_OUT"
    FAILED = "FAILED"
