import os
import logging
import typer
import click
from addict import addict
from se_elastic_schema.static.transcript import TranslationVendorEnum
from aries_se_api_client.base import ParsedResponse
from aries_se_api_client.client import AriesApiClient
from aries_se_comms_tasks.transcription.static import LANGUAGE_CODE_TO_AMAZON_LANGUAGE_CODE, LANGUAGE_CODE_TO_DEEPGRAM_LANGUAGE_CODE
from data_platform_config_api_client.translation_config import TranslationConfigAPI
import httpx

log = logging.getLogger(__name__)

def translation_deployment_run(
        tenant_name: str = typer.Option("irises8", prompt="Tenant Name"),
        workflow_name: str = typer.Option("asc_voice", prompt="Workflow Name"),
        cell_name: str = typer.Option("dev-shared-2", prompt="Cell Name"),
        fleet_name: str  = typer.Option("nonprod-eu-ie-1", prompt="Fleet Name"),
        action: str = typer.Option(..., prompt="Action (GET, PUT, DELETE) ",
                                   case_sensitive=False,
                                   show_choices=True,
                                   help="Allowed values: GET, PUT, DELETE")
):
    """
    CLI entrypoint to manage translation configuration for a given workflow.

    Depending on the selected action, this function will either retrieve, create/update,
    or delete a translation configuration for a workflow in a specified realm, tenant, cell, and fleet.

    Parameters:
        tenant_name (str): The tenant name. Default is "irises8".
        workflow_name (str): The name of the workflow. Default is "asc_voice".
        cell_name (str): The cell name. Default is "dev-shared-2".
        fleet_name (str): The fleet name. Default is "nonprod-eu-ie-1".
        action (str): The action to perform. Must be one of: GET, PUT, DELETE.

    Raises:
        typer.BadParameter: If input validation fails or an invalid action is provided.

    Side Effects:
        - Prompts user input for translation details (PUT).
        - Prints results or errors to the console.
        - May raise errors if API operations fail.

    Actions:
        - GET: Fetches and displays existing translation configuration.
        - PUT: Prompts user for configuration parameters and applies them.
        - DELETE: Removes existing translation configuration.
    """
    action = action.upper()

    data_platform_url = f"https://config-api.{fleet_name}.steeleye.co"
    realm_suffix = cell_name.split("-")[0]
    realm_name = tenant_name + ("." +realm_suffix if realm_suffix != "prod" else "")

    if not crosscheck_cell_fleet(
            realm_name= realm_name,
            cell_name= cell_name,
            fleet_name= fleet_name
    ):
        raise typer.BadParameter("Cell or Fleet are incorrect for the given realm")

    typer.secho(f"SUMMARY: action: {action} | cell: {cell_name} | workflow: {workflow_name} | tenant: {tenant_name}", fg=typer.colors.YELLOW)
    if action == "GET":
        try:
            result = get_translation_config(
                cell_name=cell_name,
                tenant_name=tenant_name,
                workflow_name=workflow_name,
                data_platform_url=data_platform_url
            )
            typer.secho(f"Result for GET:{result}", fg=typer.colors.CYAN)
        except Exception as e:
            print("Error:", e)
    elif action == "PUT":
        try:
            acceptable_translation_provider = [key.value for key in list(TranslationVendorEnum)]
            translation_provider: str = typer.prompt(
                "Translation Provider",
                type=click.Choice(acceptable_translation_provider, case_sensitive=True)
            )

            acceptable_deepgram_source_languages = [key.value for key in LANGUAGE_CODE_TO_DEEPGRAM_LANGUAGE_CODE.keys()]
            translation_source_language: str = typer.prompt(
                "Translation Source Language --- press Enter for autodetect ---",
                default="",
                type=click.Choice(acceptable_deepgram_source_languages + [""], case_sensitive=True)
            )

            acceptable_amazon_target_languages = [key.value for key in LANGUAGE_CODE_TO_AMAZON_LANGUAGE_CODE.keys()]
            translation_target_language: str = typer.prompt(
                "Translation Target Language --- press Enter for ENGLISH ---",
                default="ENGLISH",
                type=click.Choice(acceptable_amazon_target_languages, case_sensitive=True)
            )

            json_body = {
                "translationEnabled": True,
                "translationProvider": translation_provider,
                "translationSourceLanguage": translation_source_language,
                "translationTargetLanguage": translation_target_language
            }

            if translation_source_language != "":
                typer.confirm("Warning!!! When selecting a source language, "
                              "transcription and translation will be set to it."
                              " ALL calls will be transcribed with the selected source language."
                              " DeepGram's language detection will be DISABLED."
                              " Proceed?", abort=True)
            else:
                json_body.pop("translationSourceLanguage")

            put_translation_config(
                json_body=json_body,
                cell_name=cell_name,
                tenant_name=tenant_name,
                workflow_name=workflow_name,
                data_platform_url=data_platform_url
            )
        except Exception as e:
            print("Error:", e)
    elif action == "DELETE":
        try:
            delete_translation_config(
                cell_name=cell_name,
                tenant_name=tenant_name,
                workflow_name=workflow_name,
                data_platform_url=data_platform_url
            )
        except Exception as e:
            print("Error:", e)
    else:
        raise typer.BadParameter("Action must be one of: GET, PUT, DELETE")

def create_translation_client(data_platform_url: str):
    typer.secho(f"Connecting to: {data_platform_url}")
    client = TranslationConfigAPI(
        AriesApiClient(
            host=os.getenv(
                "DATA_PLATFORM_CONFIG_API_URL",
                data_platform_url,
            ),
        )
    )
    return client

def get_translation_config(
    cell_name: str,
    tenant_name: str,
    workflow_name: str,
    data_platform_url: str
) -> addict.Dict:
    client = create_translation_client(data_platform_url = data_platform_url)
    parsed_response: ParsedResponse = client.get_config(
        tenant_name=tenant_name, workflow_name=workflow_name, stack_name=cell_name
    )
    translation_dict: addict.Dict = parsed_response.content
    return translation_dict

def put_translation_config(
        json_body,
        cell_name: str,
        tenant_name: str,
        workflow_name: str,
        data_platform_url:str
):
    client = create_translation_client(data_platform_url=data_platform_url)
    response = client.upsert_config_for_tenant(
        json_body=json_body,
        stack_name=cell_name,
        tenant_name=tenant_name,
        workflow_name=workflow_name
    )
    return response

def delete_translation_config(
        cell_name: str,
        tenant_name: str,
        workflow_name: str,
        data_platform_url:str
):
    client = create_translation_client(data_platform_url=data_platform_url)
    response = client.delete_config_for_tenant(
        stack_name=cell_name,
        tenant_name=tenant_name,
        workflow_name=workflow_name
    )
    return response

def crosscheck_cell_fleet(realm_name, cell_name, fleet_name):
    response = httpx.get(f"https://{realm_name}.steeleye.co/api/v2.1/version").json()
    return cell_name == response["cell"] and fleet_name == response["fleet"]

if __name__ == "__main__":
    typer.run(translation_deployment_run)
