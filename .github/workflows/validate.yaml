name: Validate

on:
  pull_request:
  merge_group:

defaults:
  run:
    shell: bash

# This allows a subsequently queued workflow run to interrupt previous runs
# does not interrupt currently running workflows on main branch
concurrency:
  group: ${{ github.workflow }}-${{ github.event_name == 'pull_request' && github.event.pull_request.number || github.event.merge_group.head_sha }}
  cancel-in-progress: true

env:
  GH_SE_BOT_TOKEN: ${{ secrets.GH_SE_BOT_TOKEN }}
  JFROG_PYPI_USERNAME: ${{ secrets.JFROG_PYPI_USERNAME }}
  JFROG_PYPI_PASSWORD: ${{ secrets.JFROG_PYPI_PASSWORD }}
  GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}
  # registry definitions for pantsbuild via env vars
  PANTS_DOCKER_REGISTRIES: '{"ecr_prod":{"address":"698897937809.dkr.ecr.eu-west-1.amazonaws.com","default":"true"},"acr_prod":{"address":"acrsteeleyehub.azurecr.io","default":"true"}}'

jobs:
  pre-checks:
    runs-on: ubuntu-latest
    outputs:
      run-validate: ${{ steps.check.outputs.run-validate }}
      docker-checks: ${{ steps.check.outputs.docker-checks }}
      target-sha: ${{ steps.check.outputs.target-sha }}
    steps:
      - id: check
        run: |
          if [[ "${{ github.event_name }}" == "pull_request" ]]; then
            # Run validate only if commit message matches the required pattern
            HEAD_COMMIT_MESSAGE=$(gh api /repos/${{ github.repository }}/commits/${{ github.event.pull_request.head.sha }} --jq '.commit.message')
            echo "Message: $HEAD_COMMIT_MESSAGE"
            if [[ ("$HEAD_COMMIT_MESSAGE" =~ ^validate(,mypy)?( .+)?$) ]]; then
              echo "run-validate=fast" >> $GITHUB_OUTPUT
            elif [[ "$HEAD_COMMIT_MESSAGE" =~ ^validate-full( .+)?$ ]]; then
              echo "run-validate=full" >> $GITHUB_OUTPUT
            else
              echo "run-validate=false" >> $GITHUB_OUTPUT
            fi
            echo "docker-checks=true" >> $GITHUB_OUTPUT
            echo "target-sha=${{ github.event.pull_request.base.sha }}" >> $GITHUB_OUTPUT
          else
            # always validate commit message on merge_group events
            echo "run-validate=full" >> $GITHUB_OUTPUT
            echo "docker-checks=false" >> $GITHUB_OUTPUT
            echo "target-sha=${{ github.event.merge_group.base_sha }}" >> $GITHUB_OUTPUT
          fi

  python-validation-amd:
    uses: ./.github/workflows/python-validation.yaml
    needs: pre-checks
    if: needs.pre-checks.outputs.run-validate != 'false'
    with:
      runner-label: se-mono-amd-runners
      additional-pants-options: "--tag=amd_only,build_amd"
      full-checks: ${{ needs.pre-checks.outputs.run-validate == 'full' }}
      docker-checks: ${{ needs.pre-checks.outputs.docker-checks == 'true' }}
      target-sha: ${{ needs.pre-checks.outputs.target-sha }}
    secrets:
      JFROG_PYPI_USERNAME: ${{ secrets.JFROG_PYPI_USERNAME }}
      JFROG_PYPI_PASSWORD: ${{ secrets.JFROG_PYPI_PASSWORD }}
      GH_SE_BOT_TOKEN: ${{ secrets.GH_SE_BOT_TOKEN }}

  python-validation-arm:
    uses: ./.github/workflows/python-validation.yaml
    needs: pre-checks
    if: needs.pre-checks.outputs.run-validate != 'false'
    with:
      runner-label: se-mono-arm-runners
      additional-pants-options: "--tag=-amd_only"
      full-checks: ${{ needs.pre-checks.outputs.run-validate == 'full' }}
      docker-checks: ${{ needs.pre-checks.outputs.docker-checks == 'true' }}
      target-sha: ${{ needs.pre-checks.outputs.target-sha }}
    secrets:
      JFROG_PYPI_USERNAME: ${{ secrets.JFROG_PYPI_USERNAME }}
      JFROG_PYPI_PASSWORD: ${{ secrets.JFROG_PYPI_PASSWORD }}
      GH_SE_BOT_TOKEN: ${{ secrets.GH_SE_BOT_TOKEN }}
