name: Documentation

on:
  push:
    paths:
      - mkdocs.yml
      - docs/**
      - .github/workflows/documentation.yaml

defaults:
  run:
    shell: bash

jobs:
  ecr-creds:
    uses: ./.github/workflows/generate-ecr-creds.yaml
    secrets:
      AWS_ACCESS_KEY_ID: ${{ secrets.AWS_PROD_ECR_ACCESS_KEY_ID }}
      AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_PROD_ECR_SECRET_ACCESS_KEY }}

  build-and-publish:
    runs-on: ubuntu-latest
    needs: ecr-creds
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Disable git repository validation
        # this is done to avoid pants failure which fails to recognize the git repo
        run: |
          git config --global --add safe.directory '*'

      - name: Install python requirements
        run: |
          pip install mkdocs~=1.3.1 mkdocs-material~=8.4.1 mkdocstrings~=0.19.0 mkdocs-mermaid2-plugin~=0.6.0 mkdocs-section-index~=0.3.4 mkdocs-git-revision-date-localized-plugin~=1.1.0 

      - name: Build docs
        run: |
          mkdocs build -s

      - name: Publish docs
        if: github.ref == 'refs/heads/main'
        run: mkdocs gh-deploy --force
